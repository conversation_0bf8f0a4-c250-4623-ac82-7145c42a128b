# ITC Agent Frontend - Report Generation Integration

This document provides setup instructions for the integrated report generation functionality.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
cd itc-agent-frontend
npm install
```

### 2. Configure Environment

Copy the environment template and configure your API endpoint:

```bash
cp .env.local.example .env.local
```

Edit `.env.local` to point to your backend:
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
```

### 3. Start Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## 📋 Features Implemented

### ✅ Report Generation Integration
- **Streaming Reports**: Real-time report generation with live progress updates
- **Batch Reports**: Traditional report generation with final result delivery
- **Progress Tracking**: Visual progress indicators and status messages

### ✅ Enhanced UI Components
- **ReportChart**: Supports multiple chart types (area, bar, line, pie)
- **ReportTable**: Paginated, sortable tables with client-side filtering
- **ReportGenerator**: Complete report generation interface

### ✅ Chart Type Support
- `area_chart` - Area charts with gradient fills
- `bar_chart` - Bar charts with rounded corners
- `line_chart` - Line charts with data points
- `pie_chart` - Pie charts with percentage labels
- `table` - Sortable, paginated data tables
- `number` - Large number displays

### ✅ Real-time Streaming
- **EventSource Integration**: Live streaming of report generation progress
- **Section-by-Section Updates**: Sections appear as they're completed
- **Figure Processing**: Real-time figure embedding and numbering
- **Error Handling**: Graceful error handling with user feedback

## 🎯 Usage

### Chat Interface
1. Navigate to `/chat`
2. Use the tab switcher to choose between "Chat" and "Reports"
3. In the Reports tab, enter your research question
4. Choose between real-time processing or batch processing
5. Click "Generate Report" to start

### API Integration
The frontend integrates with these backend endpoints:
- `POST /report/generate-streaming` - Real-time report generation
- `POST /report/generate` - Batch report generation  
- `GET /report/stream/{task_id}` - Streaming progress updates

## 🔧 Configuration

### Theme Support
The components automatically adapt to the existing theme system:
- Light theme with blue accents
- Dark theme support
- Consistent color variables

### Chart Customization
Charts automatically format data based on column names:
- Monetary values (amount, fee, cost) → `$1,234`
- Percentages (percent, rate) → `45%`
- Numbers → `1,234`

### Table Features
- **Sorting**: Click column headers to sort
- **Pagination**: Configurable page sizes (5, 10, 25, 50)
- **Responsive**: Horizontal scrolling on mobile
- **Formatting**: Automatic data type formatting

## 📊 Data Format Support

### Chart Data Format
```typescript
interface ChartData {
  type: 'area_chart' | 'bar_chart' | 'pie_chart' | 'line_chart' | 'table' | 'number';
  data: Record<string, any>[] | any;
  axes?: {
    x_axis_key: string;
    y_axis_keys: string[];
  };
  title?: string;
  description?: string;
}
```

### Streaming Event Format
```typescript
interface StreamingEvent {
  status: 'generating_outline' | 'refining_outline' | 'writing_sections' | 'section_complete' | 'complete' | 'error';
  section_index?: number;
  section_title?: string;
  section_content?: string;
  figures_in_section?: FigureMetadata[];
  total_figures_so_far?: number;
  result?: ReportResult;
  error?: string;
}
```

## 🛠 Development

### Project Structure
```
src/
├── components/
│   ├── ReportChart.tsx      # Chart visualization component
│   ├── ReportTable.tsx      # Table component with pagination
│   ├── ReportGenerator.tsx  # Main report generation interface
│   └── ...
├── services/
│   └── reportApi.ts         # API integration and streaming hooks
├── app/
│   └── chat/
│       ├── page.tsx         # Enhanced chat page with tabs
│       └── message.tsx      # Enhanced message component
└── ...
```

### Key Components

#### ReportGenerator
Main interface for report generation with:
- Question input
- Configuration options (data gaps, streaming mode)
- Progress tracking
- Real-time section display

#### ReportChart
Flexible chart component supporting:
- Multiple chart types
- Theme-aware color palettes
- Automatic data formatting
- Custom tooltips

#### ReportTable
Advanced table component with:
- Client-side sorting
- Pagination controls
- Responsive design
- Data type formatting

## 🔄 Integration Flow

1. **User Input**: User enters research question in ReportGenerator
2. **API Call**: Frontend calls `/report/generate-streaming` endpoint
3. **Streaming**: EventSource connection established for real-time updates
4. **Progress Updates**: UI updates as sections are completed
5. **Data Visualization**: Charts and tables rendered in real-time
6. **Completion**: Final report summary displayed

## 🎨 Customization

### Adding New Chart Types
1. Add new type to `ChartData` interface
2. Implement rendering logic in `ReportChart.tsx`
3. Add color palette support
4. Update message component to handle new type

### Styling
Components use the existing theme system:
- CSS variables for colors
- Tailwind classes for layout
- Consistent spacing and typography

## 🐛 Troubleshooting

### Common Issues

**API Connection Errors**
- Check `NEXT_PUBLIC_API_BASE_URL` in `.env.local`
- Ensure backend is running on specified port
- Verify CORS settings on backend

**Streaming Not Working**
- Check browser console for EventSource errors
- Verify `/report/stream/{task_id}` endpoint accessibility
- Check network tab for SSE connection

**Charts Not Rendering**
- Verify data format matches expected structure
- Check console for Recharts errors
- Ensure `axes` configuration is correct

### Debug Mode
Enable debug logging by setting:
```javascript
localStorage.setItem('debug', 'reportApi:*');
```

## 📈 Performance

### Optimizations Implemented
- **Lazy Loading**: Charts only render when data is available
- **Memoization**: Table sorting and pagination are memoized
- **Streaming**: Real-time updates without full page refreshes
- **Error Boundaries**: Graceful error handling

### Recommended Settings
- Use streaming mode for better user experience
- Set appropriate page sizes for large datasets
- Consider data gaps inclusion based on use case

## 🔒 Security

### Best Practices
- API endpoints should validate input
- Implement rate limiting on backend
- Sanitize user input before display
- Use HTTPS in production

## 📝 Next Steps

### Potential Enhancements
1. **Export Functionality**: PDF/Excel export of reports
2. **Report History**: Save and retrieve previous reports
3. **Advanced Filtering**: Client-side data filtering
4. **Collaborative Features**: Share reports with team members
5. **Custom Themes**: User-configurable color schemes

// Simple test script to verify API connectivity
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8000';

async function testAPI() {
  console.log('🧪 Testing API connectivity...');
  
  try {
    // Test basic connectivity
    console.log('1. Testing basic connectivity...');
    const healthResponse = await axios.get(`${API_BASE_URL}/health`, { timeout: 5000 });
    console.log('✅ Health check passed:', healthResponse.status);
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
  }
  
  try {
    // Test report generation endpoint
    console.log('2. Testing report generation endpoint...');
    const reportResponse = await axios.post(`${API_BASE_URL}/report/generate-streaming`, {
      original_question: "Test question",
      include_data_gaps: false
    }, { timeout: 10000 });
    
    console.log('✅ Report generation endpoint works:', reportResponse.data);
    
    // Test streaming endpoint
    console.log('3. Testing streaming endpoint...');
    const streamResponse = await axios.get(`${API_BASE_URL}/report/stream/${reportResponse.data.task_id}`, {
      timeout: 5000,
      responseType: 'stream'
    });
    
    console.log('✅ Streaming endpoint accessible:', streamResponse.status);
    
  } catch (error) {
    console.error('❌ Report API test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testAPI();

import axios from 'axios';

// Configure base URL - adjust this to match your backend
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false,
  timeout: 120000, // 2 minute timeout for TTS requests
});

// Types for API requests and responses
export interface TTSRequest {
  text: string;
  voice_id?: string;
  voice_settings?: Record<string, any>;
  output_format?: string;
  model_id?: string;
}

export interface STTResponse {
  text: string;
  confidence?: number;
  language?: string;
  metadata?: Record<string, any>;
}

export interface Voice {
  voice_id: string;
  name: string;
  category: string;
  description: string;
  preview_url: string;
  available_for_tiers: string[];
  settings?: Record<string, any>;
}

export interface VoicesResponse {
  voices: Voice[];
}

export interface ProviderInfo {
  provider_type: string;
  tts_available: boolean;
  stt_available: boolean;
  supported_formats: string[];
  voices_count: number;
}

export interface SpeechHealthResponse {
  status: string;
  provider: string;
  tts_available: boolean;
  stt_available: boolean;
  message: string;
  error?: string;
}

// API Functions
export const speechApi = {
  // Test speech service connection
  async testConnection(): Promise<boolean> {
    try {
      const response = await apiClient.get('/speech/health');
      return response.status === 200 && response.data.status === 'healthy';
    } catch (error) {
      console.error('Speech service connection test failed:', error);
      return false;
    }
  },

  // Text-to-Speech conversion
  async textToSpeech(request: TTSRequest): Promise<Blob> {
    try {
      // Check text length and potentially chunk it
      const maxLength = 5000; // ElevenLabs recommended max length

      if (request.text.length > maxLength) {
        console.log(`📝 Text is ${request.text.length} characters, chunking for better performance...`);
        return await this.textToSpeechChunked(request, maxLength);
      }

      const response = await apiClient.post('/speech/tts', request, {
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 120000, // 2 minute timeout for individual requests
      });

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          throw new Error('Request timeout: The text might be too long. Try with a shorter text or check your connection.');
        }
        if (error.code === 'ERR_NETWORK') {
          throw new Error('Network error: Cannot connect to speech service. Please check if the backend is running on ' + API_BASE_URL);
        }
        if (error.response?.status === 400) {
          throw new Error(`TTS error: ${error.response.data.detail || 'Invalid request'}`);
        }
      }
      throw error;
    }
  },

  // Helper method to chunk long text and combine audio
  async textToSpeechChunked(request: TTSRequest, maxLength: number): Promise<Blob> {
    const text = request.text;
    const chunks = this.chunkText(text, maxLength);

    console.log(`🔄 Processing ${chunks.length} text chunks...`);

    const audioBlobs: Blob[] = [];

    for (let i = 0; i < chunks.length; i++) {
      console.log(`🎤 Processing chunk ${i + 1}/${chunks.length} (${chunks[i].length} chars)...`);

      try {
        const chunkRequest = { ...request, text: chunks[i] };
        const response = await apiClient.post('/speech/tts', chunkRequest, {
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 60000, // 1 minute per chunk
        });

        audioBlobs.push(response.data);
      } catch (error) {
        console.error(`❌ Failed to process chunk ${i + 1}:`, error);
        throw new Error(`Failed to process text chunk ${i + 1}/${chunks.length}`);
      }
    }

    // Combine all audio blobs
    console.log(`🔗 Combining ${audioBlobs.length} audio chunks...`);
    return new Blob(audioBlobs, { type: 'audio/mp3' });
  },

  // Helper method to intelligently chunk text
  chunkText(text: string, maxLength: number): string[] {
    if (text.length <= maxLength) {
      return [text];
    }

    const chunks: string[] = [];
    let currentChunk = '';

    // Split by sentences first
    const sentences = text.split(/(?<=[.!?])\s+/);

    for (const sentence of sentences) {
      // If adding this sentence would exceed the limit
      if (currentChunk.length + sentence.length > maxLength) {
        if (currentChunk.length > 0) {
          chunks.push(currentChunk.trim());
          currentChunk = sentence;
        } else {
          // Single sentence is too long, split by words
          const words = sentence.split(' ');
          let wordChunk = '';

          for (const word of words) {
            if (wordChunk.length + word.length + 1 > maxLength) {
              if (wordChunk.length > 0) {
                chunks.push(wordChunk.trim());
                wordChunk = word;
              } else {
                // Single word is too long, just add it
                chunks.push(word);
              }
            } else {
              wordChunk += (wordChunk.length > 0 ? ' ' : '') + word;
            }
          }

          if (wordChunk.length > 0) {
            currentChunk = wordChunk;
          }
        }
      } else {
        currentChunk += (currentChunk.length > 0 ? ' ' : '') + sentence;
      }
    }

    if (currentChunk.length > 0) {
      chunks.push(currentChunk.trim());
    }

    return chunks.filter(chunk => chunk.length > 0);
  },

  // Speech-to-Text conversion
  async speechToText(
    audioFile: File,
    options: {
      language_code?: string;
      model_id?: string;
      tag_audio_events?: boolean;
      diarize?: boolean;
    } = {}
  ): Promise<STTResponse> {
    try {
      const formData = new FormData();
      formData.append('audio_file', audioFile);
      
      if (options.language_code) {
        formData.append('language_code', options.language_code);
      }
      if (options.model_id) {
        formData.append('model_id', options.model_id);
      }
      formData.append('tag_audio_events', String(options.tag_audio_events ?? true));
      formData.append('diarize', String(options.diarize ?? false));

      const response = await apiClient.post('/speech/stt', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ERR_NETWORK') {
          throw new Error('Network error: Cannot connect to speech service. Please check if the backend is running on ' + API_BASE_URL);
        }
        if (error.response?.status === 400) {
          throw new Error(`STT error: ${error.response.data.detail || 'Invalid request'}`);
        }
      }
      throw error;
    }
  },

  // Get available voices
  async getVoices(): Promise<VoicesResponse> {
    try {
      const response = await apiClient.get('/speech/voices');
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ERR_NETWORK') {
          throw new Error('Network error: Cannot connect to speech service.');
        }
      }
      throw error;
    }
  },

  // Get supported formats
  async getSupportedFormats(): Promise<{ supported_formats: string[]; description: string }> {
    try {
      const response = await apiClient.get('/speech/formats');
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ERR_NETWORK') {
          throw new Error('Network error: Cannot connect to speech service.');
        }
      }
      throw error;
    }
  },

  // Get provider information
  async getProviderInfo(): Promise<ProviderInfo> {
    try {
      const response = await apiClient.get('/speech/provider-info');
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ERR_NETWORK') {
          throw new Error('Network error: Cannot connect to speech service.');
        }
      }
      throw error;
    }
  },

  // Get speech service health
  async getHealth(): Promise<SpeechHealthResponse> {
    try {
      const response = await apiClient.get('/speech/health');
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ERR_NETWORK') {
          throw new Error('Network error: Cannot connect to speech service.');
        }
      }
      throw error;
    }
  }
};

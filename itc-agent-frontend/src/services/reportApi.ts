import axios from 'axios';

// Configure base URL - adjust this to match your backend
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false, // Set to true if you need cookies/auth
  timeout: 30000, // 30 second timeout
});

// Types for API requests and responses
export interface ReportGenerationRequest {
  original_question: string;
  include_data_gaps: boolean;
}

export interface ReportGenerationResponse {
  task_id: string;
}

export interface StreamingEvent {
  status: 'generating_uninformed_outline' | 'expanding_questions' | 'filtering_questions' | 'interviewing' |
          'generating_informed_outline' | 'refining_outline' | 'writing_sections' | 'section_complete' |
          'complete' | 'error' | 'generating_outline';
  section_index?: number;
  section_title?: string;
  section_content?: string;
  figures_in_section?: FigureMetadata[];
  total_figures_so_far?: number;
  result?: ReportResult;
  error?: string;
}

export interface FigureMetadata {
  figure_number: number;
  title: string;
  type: string;
}

export interface ReportResult {
  outline: string;
  sections: string[];
  data: Record<string, unknown>;
  total_figures?: number;
  figures_list?: string[];
}

// API Functions
export const reportApi = {
  // Test backend connection
  async testConnection(): Promise<boolean> {
    try {
      const response = await apiClient.get('/health');
      return response.status === 200;
    } catch (error) {
      console.error('Backend connection test failed:', error);
      return false;
    }
  },

  // Start batch report generation (original endpoint)
  async generateReport(request: ReportGenerationRequest): Promise<ReportGenerationResponse> {
    try {
      const response = await apiClient.post('/report/generate', request);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ERR_NETWORK') {
          throw new Error('Network error: Cannot connect to backend. Please check if the backend is running on ' + API_BASE_URL);
        }
        if (error.response?.status === 0) {
          throw new Error('CORS error: Backend is not allowing requests from this origin. Please check CORS configuration.');
        }
      }
      throw error;
    }
  },

  // Start streaming report generation (new endpoint)
  async generateStreamingReport(request: ReportGenerationRequest): Promise<ReportGenerationResponse> {
    console.log('🚀 Making API call to generate streaming report:', request);
    console.log('🌐 API Base URL:', API_BASE_URL);

    try {
      const response = await apiClient.post('/report/generate-streaming', request);
      console.log('✅ API call successful:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API call failed:', error);

      if (axios.isAxiosError(error)) {
        console.error('📊 Axios error details:', {
          code: error.code,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data
        });

        if (error.code === 'ERR_NETWORK') {
          throw new Error('Network error: Cannot connect to backend. Please check if the backend is running on ' + API_BASE_URL);
        }
        if (error.response?.status === 0) {
          throw new Error('CORS error: Backend is not allowing requests from this origin. Please check CORS configuration.');
        }
      }
      throw error;
    }
  },

  // Create EventSource for streaming progress
  createStreamingConnection(taskId: string): EventSource {
    const streamUrl = `${API_BASE_URL}/report/stream/${taskId}`;
    console.log('🔗 Creating EventSource connection to:', streamUrl);

    const eventSource = new EventSource(streamUrl);

    eventSource.onopen = () => {
      console.log('✅ EventSource connection opened');
    };

    eventSource.onerror = (error) => {
      console.error('❌ EventSource error:', error);
    };

    return eventSource;
  },

  // Get report status (fallback for non-streaming)
  async getReportStatus(taskId: string): Promise<unknown> {
    const response = await apiClient.get(`/report/status/${taskId}`);
    return response.data;
  }
};

// Utility functions for handling streaming data
export const streamingUtils = {
  // Parse streaming event data
  parseStreamingEvent(eventData: string): StreamingEvent | null {
    try {
      return JSON.parse(eventData);
    } catch (error) {
      console.error('Failed to parse streaming event:', error);
      return null;
    }
  },

  // Extract data objects from section content
  extractDataObjects(sectionContent: string): Record<string, unknown> {
    const dataObjects: Record<string, unknown> = {};
    
    // Look for data objects in the format {chart_data} or similar
    const dataPattern = /\{([^}]+)\}/g;
    let match;
    
    while ((match = dataPattern.exec(sectionContent)) !== null) {
      const dataKey = match[1];
      // This would need to be enhanced based on actual data format
      dataObjects[dataKey] = match[0];
    }
    
    return dataObjects;
  },

  // Check if section contains figures
  hasFigures(sectionContent: string): boolean {
    return /Figure \d+/.test(sectionContent);
  },

  // Extract figure references from content
  extractFigureReferences(sectionContent: string): Array<{number: number, title: string}> {
    const figurePattern = /Figure (\d+) – (.+?)(?=\n|$)/g;
    const figures: Array<{number: number, title: string}> = [];
    let match;
    
    while ((match = figurePattern.exec(sectionContent)) !== null) {
      figures.push({
        number: parseInt(match[1]),
        title: match[2].trim()
      });
    }
    
    return figures;
  }
};

// Import React for the hook
import React from 'react';

// Hook for managing streaming report generation
export const useStreamingReport = () => {
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [progress, setProgress] = React.useState<StreamingEvent[]>([]);
  const [currentSection, setCurrentSection] = React.useState<number>(0);
  const [sections, setSections] = React.useState<string[]>([]);
  const [figures, setFigures] = React.useState<FigureMetadata[]>([]);
  const [error, setError] = React.useState<string | null>(null);
  const [finalResult, setFinalResult] = React.useState<ReportResult | null>(null);

  const startStreamingReport = async (request: ReportGenerationRequest, useStreaming: boolean = true) => {
    setIsGenerating(true);
    setError(null);
    setProgress([]);
    setSections([]);
    setFigures([]);
    setFinalResult(null);

    try {
      // Choose endpoint based on preference
      const response = useStreaming 
        ? await reportApi.generateStreamingReport(request)
        : await reportApi.generateReport(request);

      const eventSource = reportApi.createStreamingConnection(response.task_id);

      eventSource.onmessage = (event) => {
        console.debug('[useStreamingReport] raw streaming event:', event.data);

        const streamingEvent = streamingUtils.parseStreamingEvent(event.data);
        if (!streamingEvent) {
          console.warn('⚠️ Failed to parse streaming event:', event.data);
          return;
        }

        console.debug('[useStreamingReport] parsed event:', streamingEvent.status, streamingEvent);

        // Add progress event with additional validation
        setProgress(prev => {
          const newProgress = [...prev, streamingEvent];
          console.debug('[useStreamingReport] progress updated:', newProgress.map(p => p.status));
          return newProgress;
        });

        switch (streamingEvent.status) {
          case 'section_complete':
            if (streamingEvent.section_content) {
              setSections(prev => {
                const newSections = [...prev];
                newSections[streamingEvent.section_index!] = streamingEvent.section_content!;
                return newSections;
              });
              setCurrentSection(streamingEvent.section_index! + 1);
              
              if (streamingEvent.figures_in_section) {
                setFigures(prev => [...prev, ...streamingEvent.figures_in_section!]);
              }
            }
            break;
          
          case 'complete':
            setIsGenerating(false);
            if (streamingEvent.result) {
              setFinalResult(streamingEvent.result);
            }
            eventSource.close();
            break;
          
          case 'error':
            setIsGenerating(false);
            setError(streamingEvent.error || 'Unknown error occurred');
            eventSource.close();
            break;
        }
      };

      eventSource.onerror = () => {
        setIsGenerating(false);
        setError('Connection error occurred');
        eventSource.close();
      };

    } catch (error) {
      setIsGenerating(false);
      setError(error instanceof Error ? error.message : 'Failed to start report generation');
    }
  };

  return {
    isGenerating,
    progress,
    currentSection,
    sections,
    figures,
    error,
    finalResult,
    startStreamingReport
  };
};

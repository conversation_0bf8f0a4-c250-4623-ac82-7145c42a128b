import SideNav from "./side-nav";
import { ThemeSwitcher } from "@/components/theme-switcher";

export default function Layout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <div className="flex h-screen overflow-hidden p-5 bg-background">
            {/* Theme switcher in top-right corner */}

            {/* <div className="rounded-l-3xl p-4 flex flex-col shadow-md border-2 border-gray-900 bg-linear-to-t from-gray-900 to-gray-950 min-w-64 h-full"> */}
            <SideNav />
            <div className="flex-1 flex flex-col px-7">
                {children}
            </div>
        </div>
    );
}

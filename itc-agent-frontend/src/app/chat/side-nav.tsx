"use client";

import {
    EditIcon,
    EllipsisVerticalIcon,
    PanelLeft,
    PencilIcon,
    SearchIcon,
    SettingsIcon,
    TrashIcon,
} from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { useTheme } from "@/contexts/theme-context";

export default function SideNav() {
    const { theme } = useTheme();
    const [isExpanded, setIsExpanded] = useState(false);
    const [openMenuIndex, setOpenMenuIndex] = useState<number | null>(null);
    const menuRefs = useRef<(HTMLDivElement | null)[]>([]);
    const ellipsisRefs = useRef<(HTMLDivElement | null)[]>([]);

    const navItems = [
        { icon: EditIcon, text: "New chat" },
        { icon: SearchIcon, text: "Search chats" },
    ];

    const recentChats = [
        { title: "AI Driven Research for Ghanaian Universities" },
        { title: "Education Reform Strategy 2025" },
        { title: "Draft Presentation Notes" },
    ];

    // Close menu on outside click
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as Node;
            const clickedInsideMenu = menuRefs.current.some(ref => ref?.contains(target));
            const clickedOnEllipsis = ellipsisRefs.current.some(ref => ref?.contains(target));

            if (!clickedInsideMenu && !clickedOnEllipsis) {
                setOpenMenuIndex(null);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);

    return (
        <div className={`${isExpanded ? "rounded-l-3xl w-64" : "rounded-3xl w-16"} duration-300 px-2 py-4 flex flex-col shadow-md ${theme === 'light-blue' ? 'bg-accent text-accent-foreground border-sidebar-border' : 'bg-card border border-border'} h-full`}>
            <div className={`flex justify-between items-center ${!isExpanded ? "group" : ""}`}>
                <div className={`size-[3rem] ${isExpanded ? "flex" : "flex group-hover:hidden"} items-center justify-center`}>
                    <img className="w-full" src={"itc-logo-outer-transparent.png"} />
                </div>
                <div className={`size-[3rem] ${isExpanded ? "flex" : "hidden group-hover:flex"} items-center justify-center`}>
                    <PanelLeft
                        onClick={() => setIsExpanded((prev) => !prev)}
                        size={"1.3rem"}
                        className={`cursor-pointer transition-colors ${theme === 'light-blue' ? 'text-sidebar-accent-foreground hover:text-sidebar-primary-foreground' : 'text-muted-foreground hover:text-foreground'}`}
                    />
                </div>
            </div>

            <div className={`mt-7`}>
                {navItems.map((item, idx) => {
                    const Icon = item.icon;
                    return (
                        <div key={idx} className={`flex px-3.5 py-2 rounded-lg gap-4 items-center cursor-pointer transition-colors ${theme === 'light-blue' ? 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}>
                            <Icon size={"1.1rem"} className={theme === 'light-blue' ? 'text-sidebar-accent-foreground' : 'text-muted-foreground'} />
                            <div className={`text-[0.92rem] overflow-hidden whitespace-nowrap font-medium ${theme === 'light-blue' ? 'text-sidebar-accent-foreground' : 'text-muted-foreground'} ${isExpanded ? "" : "hidden"}`}>
                                {item.text}
                            </div>
                        </div>
                    );
                })}
            </div>

            <div className={`px-3.5 ${isExpanded ? "" : "hidden"} overflow-hidden whitespace-nowrap mt-6 text-[0.92rem] font-medium ${theme === 'light-blue' ? 'text-sidebar-accent-foreground' : 'text-muted-foreground'}`}>
                Recent
            </div>

            <div className="flex-1 mt-3 overflow-y-auto">
                {recentChats.map((chat, idx) => (
                    <div key={idx} className={`relative group flex cursor-pointer px-3.5 py-2 rounded-lg gap-4 items-center transition-colors ${theme === 'light-blue' ? 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}>
                        <div className={`text-[0.92rem] max-w-45 overflow-hidden whitespace-nowrap font-medium truncate ${isExpanded ? "" : "hidden"} ${theme === 'light-blue' ? 'text-sidebar-accent-foreground group-hover:text-sidebar-accent-foreground' : 'text-muted-foreground group-hover:text-accent-foreground'}`}>
                            {chat.title}
                        </div>

                        <div
                            className="absolute right-2 hidden group-hover:flex"
                            ref={(el) => { ellipsisRefs.current[idx] = el }}
                        >
                            <EllipsisVerticalIcon
                                size={"1.1rem"}
                                className={`transition-colors ${theme === 'light-blue' ? 'text-sidebar-accent-foreground hover:text-sidebar-primary-foreground' : 'text-muted-foreground hover:text-foreground'}`}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setOpenMenuIndex((prev) => (prev === idx ? null : idx));
                                }}
                            />
                        </div>

                        {openMenuIndex === idx && (
                            <div
                                ref={(el) => { menuRefs.current[idx] = el }}
                                className="absolute border border-border top-full right-2 mt-1 bg-popover text-sm text-popover-foreground rounded-md shadow-md z-20 w-32"
                                onClick={(e) => e.stopPropagation()}
                            >
                                <div
                                    className="px-4 py-2 flex items-center gap-4 text-popover-foreground hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors"
                                    onClick={() => {
                                        console.log("Rename", chat.title);
                                        setOpenMenuIndex(null);
                                    }}
                                >
                                    <PencilIcon size={"1.1rem"} />
                                    <div className="text-[0.92rem]">Rename</div>
                                </div>
                                <div
                                    className="px-4 py-2 flex items-center gap-4 text-destructive hover:bg-destructive hover:text-destructive-foreground cursor-pointer transition-colors"
                                    onClick={() => {
                                        console.log("Delete", chat.title);
                                        setOpenMenuIndex(null);
                                    }}
                                >
                                    <TrashIcon size={"1.1rem"} />
                                    <div className="text-[0.92rem]">Delete</div>
                                </div>
                            </div>
                        )}
                    </div>
                ))}
            </div>

            <div className={`flex px-3.5 py-2 rounded-lg gap-4 items-center cursor-pointer transition-colors ${theme === 'light-blue' ? 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground' : 'hover:bg-accent hover:text-accent-foreground'}`}>
                <SettingsIcon size={"1.1rem"} className={theme === 'light-blue' ? 'text-sidebar-accent-foreground' : 'text-muted-foreground'} />
                <div className={`text-[0.92rem] overflow-hidden whitespace-nowrap font-medium ${theme === 'light-blue' ? 'text-sidebar-accent-foreground' : 'text-muted-foreground'} ${isExpanded ? "" : "hidden"}`}>
                    Settings
                </div>
            </div>
        </div>
    );
}

@import "tailwindcss";

:root {
  /* Base theme variables */
  --radius: 0.625rem;

  /* Light theme (default) - based on transflow-light */
  --background: oklch(0.98 0.01 240); /* Very light blue-tinted background */
  --foreground: oklch(0.15 0.08 240); /* Dark blue text */
  --card: oklch(1 0 0); /* Pure white cards for contrast */
  --card-foreground: oklch(0.15 0.08 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.08 240);
  --primary: oklch(0.35 0.15 240); /* #08518A - main transflow blue */
  --primary-foreground: oklch(0.98 0.01 240);
  --secondary: oklch(0.92 0.02 240); /* Light blue secondary */
  --secondary-foreground: oklch(0.35 0.15 240);
  --muted: oklch(0.95 0.01 240); /* Very light blue muted */
  --muted-foreground: oklch(0.4 0.1 240); /* Darker blue for better contrast */
  --accent: oklch(0.92 0.02 240);
  --accent-foreground: oklch(0.35 0.15 240);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.98 0.01 240);
  --border: oklch(0.88 0.02 240); /* Light blue borders */
  --input: oklch(0.95 0.01 240); /* Light blue inputs */
  --ring: oklch(0.35 0.15 240);
}

.light-blue {
  /* Light theme with blue sidebar (transflow-light inspired) */
  --background: oklch(0.98 0.01 240); /* Very light blue-tinted background */
  --foreground: oklch(0.15 0.08 240); /* Dark blue text */
  --card: oklch(1 0 0); /* Pure white cards for contrast */
  --card-foreground: oklch(0.15 0.08 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.08 240);
  --primary: oklch(0.35 0.15 240); /* #08518A - main transflow blue */
  --primary-foreground: oklch(0.98 0.01 240);
  --secondary: oklch(0.92 0.02 240); /* Light blue secondary */
  --secondary-foreground: oklch(0.35 0.15 240);
  --muted: oklch(0.95 0.01 240); /* Very light blue muted */
  --muted-foreground: oklch(0.4 0.1 240); /* Darker blue for better contrast */
  --accent: oklch(0.35 0.15 240); /* Blue accent for sidebar */
  --accent-foreground: oklch(0.98 0.01 240); /* Light text on blue sidebar */
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.98 0.01 240);
  --border: oklch(0.88 0.02 240); /* Light blue borders */
  --input: oklch(0.95 0.01 240); /* Light blue inputs */
  --ring: oklch(0.35 0.15 240);

  /* Sidebar specific colors for blue sidebar */
  --sidebar: var(--accent); /* Blue sidebar */
  --sidebar-foreground: var(--accent-foreground); /* Light text on blue */
  --sidebar-primary: oklch(0.25 0.18 240); /* Darker blue for primary elements */
  --sidebar-primary-foreground: oklch(0.98 0.01 240);
  --sidebar-accent: oklch(0.4 0.12 240); /* Slightly lighter blue for hover states */
  --sidebar-accent-foreground: oklch(0.98 0.01 240);
  --sidebar-border: oklch(0.3 0.12 240); /* Darker blue borders in sidebar */
  --sidebar-ring: oklch(0.25 0.18 240);
}

.dark {
  /* Dark theme variables - improved contrast and hierarchy */
  --background: oklch(0.04 0.015 240); /* Very dark background */
  --foreground: oklch(0.92 0.01 240); /* Light text */
  --card: oklch(0.09 0.02 240); /* Elevated card background */
  --card-foreground: oklch(0.92 0.01 240);
  --popover: oklch(0.11 0.025 240); /* Slightly lighter for popovers */
  --popover-foreground: oklch(0.92 0.01 240);
  --primary: oklch(0.55 0.15 240); /* Bright blue primary */
  --primary-foreground: oklch(0.98 0.005 240);
  --secondary: oklch(0.15 0.03 240); /* More distinct secondary */
  --secondary-foreground: oklch(0.85 0.02 240);
  --muted: oklch(0.07 0.02 240); /* Subtle muted background */
  --muted-foreground: oklch(0.65 0.05 240); /* Muted text with better contrast */
  --accent: oklch(0.18 0.04 240); /* More prominent accent */
  --accent-foreground: oklch(0.95 0.01 240);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.98 0.01 240);
  --border: oklch(0.2 0.03 240); /* More visible borders */
  --input: oklch(0.08 0.02 240); /* Input background close to card but distinct */
  --ring: oklch(0.55 0.15 240);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-sidebar: var(--sidebar, var(--card));
  --color-sidebar-foreground: var(--sidebar-foreground, var(--card-foreground));
  --color-sidebar-primary: var(--sidebar-primary, var(--primary));
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground, var(--primary-foreground));
  --color-sidebar-accent: var(--sidebar-accent, var(--accent));
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground, var(--accent-foreground));
  --color-sidebar-border: var(--sidebar-border, var(--border));
  --color-sidebar-ring: var(--sidebar-ring, var(--ring));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius: var(--radius);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Ensure proper background colors for theme switching */
.light {
  color-scheme: light;
}

.dark {
  color-scheme: dark;
}


/*
 * ADD YOUR CUSTOM ANIMATION CODE HERE
 */

@keyframes shimmer {
  0% {
    /* Starting position (off-screen top-right) */
    background-position: 150% 150%;
  }
  
  67% {
    /* Ending position (off-screen bottom-left) */
    background-position: -150% -150%;
  }

  100% {
    /* Hold the ending position for the pause */
    background-position: -150% -150%;
  }
}

.animate-shimmer {
  /* Total cycle duration is now 6 seconds */
  animation: shimmer 8s linear infinite;
}

/* Conversational Report Styling - Custom Typography */
.prose {
  color: var(--foreground);
  max-width: none;
  line-height: 1.6;
}

.prose h1 {
  color: var(--foreground);
  font-size: 1.875rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h2 {
  color: var(--foreground);
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.75rem;
  margin-bottom: 0.75rem;
}

.prose h3 {
  color: var(--foreground);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

.prose h4, .prose h5, .prose h6 {
  color: var(--foreground);
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.prose p {
  color: var(--foreground);
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

.prose strong {
  color: var(--foreground);
  font-weight: 600;
}

.prose em {
  color: var(--foreground);
  font-style: italic;
}

.prose ul, .prose ol {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
}

.prose ul {
  list-style-type: disc;
}

.prose ol {
  list-style-type: decimal;
}

.prose li {
  color: var(--foreground);
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  line-height: 1.6;
}

.prose blockquote {
  color: var(--muted-foreground);
  border-left: 4px solid var(--border);
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
}

.prose code {
  color: var(--foreground);
  background-color: var(--muted);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.prose pre {
  background-color: var(--muted);
  color: var(--foreground);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.prose pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.prose a {
  color: var(--primary);
  text-decoration: underline;
  text-underline-offset: 2px;
}

.prose a:hover {
  text-decoration: none;
}

.prose hr {
  border: none;
  border-top: 1px solid var(--border);
  margin: 2rem 0;
}

/* First paragraph should not have top margin */
.prose > p:first-child {
  margin-top: 0;
}

/* Last paragraph should not have bottom margin */
.prose > p:last-child {
  margin-bottom: 0;
}

/* Generation Steps Animations */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(var(--primary), 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(var(--primary), 0);
  }
}

.generation-step-current {
  animation: pulse-glow 2s infinite;
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.generation-step-enter {
  animation: slide-in-up 0.3s ease-out;
}

@keyframes progress-fill {
  from {
    width: 0%;
  }
}

.progress-bar-fill {
  animation: progress-fill 0.5s ease-out;
}


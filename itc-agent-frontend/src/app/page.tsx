import { ThemeSwitcher } from "@/components/theme-switcher";

export default function Home() {
  return (
    <div className="font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20">
      {/* Theme switcher in top-right corner */}
      <div className="fixed top-4 right-4 z-50">
        <ThemeSwitcher />
      </div>

      {/* Main content area */}
      <main className="flex flex-col items-center justify-center gap-8">
        <h1 className="text-4xl font-bold text-foreground">
          Welcome to ITC Agent Frontend
        </h1>
        <p className="text-lg text-muted-foreground text-center max-w-2xl">
          This is a Next.js application with theme switching functionality.
          Use the theme toggle in the top-right corner to switch between light and dark themes.
        </p>

        {/* Theme demonstration cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-4xl">
          <div className="p-6 bg-card border border-border rounded-lg shadow-sm">
            <h2 className="text-xl font-semibold text-card-foreground mb-3">Light Theme</h2>
            <p className="text-muted-foreground mb-4">Clean and bright interface based on the transflow-light theme</p>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-primary rounded-full"></div>
              <span className="text-sm text-primary">Primary Color</span>
            </div>
          </div>

          <div className="p-6 bg-secondary border border-border rounded-lg">
            <h2 className="text-xl font-semibold text-secondary-foreground mb-3">Theme Support</h2>
            <p className="text-muted-foreground mb-4">Automatic theme persistence and system preference detection</p>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-accent rounded-full"></div>
              <span className="text-sm text-accent-foreground">Accent Color</span>
            </div>
          </div>

          <div className="p-6 bg-muted border border-border rounded-lg">
            <h2 className="text-xl font-semibold text-foreground mb-3">Responsive Design</h2>
            <p className="text-muted-foreground mb-4">Consistent styling across all components and themes</p>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-ring rounded-full"></div>
              <span className="text-sm text-muted-foreground">Ring Color</span>
            </div>
          </div>
        </div>

        {/* Feature highlights */}
        <div className="mt-8 p-6 bg-card border border-border rounded-lg w-full max-w-2xl">
          <h3 className="text-lg font-semibold text-card-foreground mb-4">Theme Features</h3>
          <ul className="space-y-2 text-muted-foreground">
            <li>• Light and dark theme support</li>
            <li>• Based on transflow-light color scheme</li>
            <li>• Automatic persistence in localStorage</li>
            <li>• System preference detection</li>
            <li>• Smooth transitions between themes</li>
            <li>• Consistent color variables across components</li>
          </ul>
        </div>
      </main>
    </div>
  );
}

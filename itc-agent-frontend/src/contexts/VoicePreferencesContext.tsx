"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';

export interface VoicePreferences {
  autoSendVoiceMessages: boolean;
  autoReadAIResponses: boolean;
}

interface VoicePreferencesContextType {
  preferences: VoicePreferences;
  updatePreference: <K extends keyof VoicePreferences>(
    key: K,
    value: VoicePreferences[K]
  ) => void;
  resetPreferences: () => void;
}

const defaultPreferences: VoicePreferences = {
  autoSendVoiceMessages: true,  // Default: auto-send enabled
  autoReadAIResponses: false,   // Default: manual reading
};

const VoicePreferencesContext = createContext<VoicePreferencesContextType | undefined>(undefined);

const STORAGE_KEY = 'voice-preferences';

export function VoicePreferencesProvider({ children }: { children: React.ReactNode }) {
  const [preferences, setPreferences] = useState<VoicePreferences>(defaultPreferences);

  // Load preferences from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsedPreferences = JSON.parse(stored);
        setPreferences({ ...defaultPreferences, ...parsedPreferences });
      }
    } catch (error) {
      console.warn('Failed to load voice preferences from localStorage:', error);
    }
  }, []);

  // Save preferences to localStorage whenever they change
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(preferences));
    } catch (error) {
      console.warn('Failed to save voice preferences to localStorage:', error);
    }
  }, [preferences]);

  const updatePreference = <K extends keyof VoicePreferences>(
    key: K,
    value: VoicePreferences[K]
  ) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const resetPreferences = () => {
    setPreferences(defaultPreferences);
  };

  return (
    <VoicePreferencesContext.Provider
      value={{
        preferences,
        updatePreference,
        resetPreferences,
      }}
    >
      {children}
    </VoicePreferencesContext.Provider>
  );
}

export function useVoicePreferences() {
  const context = useContext(VoicePreferencesContext);
  if (context === undefined) {
    throw new Error('useVoicePreferences must be used within a VoicePreferencesProvider');
  }
  return context;
}

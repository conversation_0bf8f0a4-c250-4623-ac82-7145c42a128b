import { useState, useRef, useCallback } from 'react';
import { speechApi, TTSRequest } from '@/services/speechApi';

export interface TextToSpeechState {
  isPlaying: boolean;
  isLoading: boolean;
  error: string | null;
  currentAudio: HTMLAudioElement | null;
}

export interface TextToSpeechControls {
  speak: (text: string, options?: Partial<TTSRequest>) => Promise<void>;
  stop: () => void;
  pause: () => void;
  resume: () => void;
}

export const useTextToSpeech = (): TextToSpeechState & TextToSpeechControls => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);

  const audioRef = useRef<HTMLAudioElement | null>(null);

  const cleanup = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.removeEventListener('ended', handleAudioEnd);
      audioRef.current.removeEventListener('error', handleAudioError);
      audioRef.current = null;
      setCurrentAudio(null);
    }
    setIsPlaying(false);
  }, []);

  const handleAudioEnd = useCallback(() => {
    setIsPlaying(false);
    cleanup();
  }, [cleanup]);

  const handleAudioError = useCallback((event: Event) => {
    console.error('Audio playback error:', event);
    setError('Audio playback failed');
    setIsPlaying(false);
    cleanup();
  }, [cleanup]);

  const speak = useCallback(async (text: string, options: Partial<TTSRequest> = {}) => {
    try {
      setError(null);
      setIsLoading(true);

      // Stop any currently playing audio
      if (audioRef.current) {
        cleanup();
      }

      // Enhanced debugging for auto-read
      console.log(`🎤 Starting TTS for ${text.length} characters...`);
      console.log('🔧 TTS Request details:', {
        textLength: text.length,
        textPreview: text.substring(0, 100) + '...',
        options
      });

      // Prepare TTS request
      const request: TTSRequest = {
        text,
        output_format: 'mp3_22050_32',
        ...options,
      };

      console.log('📡 Making TTS API request...');
      // Get audio blob from API
      const audioBlob = await speechApi.textToSpeech(request);

      console.log(`✅ TTS completed, audio blob size: ${audioBlob.size} bytes`);

      // Create audio URL
      const audioUrl = URL.createObjectURL(audioBlob);

      // Create and configure audio element
      const audio = new Audio(audioUrl);
      audio.addEventListener('ended', handleAudioEnd);
      audio.addEventListener('error', handleAudioError);

      audioRef.current = audio;
      setCurrentAudio(audio);

      console.log('🔊 Starting audio playback...');
      // Play audio
      await audio.play();
      setIsPlaying(true);
      setIsLoading(false);
      console.log('✅ Audio playback started successfully');

    } catch (err) {
      console.error('❌ Text-to-speech error:', err);

      // Provide more specific error messages
      let errorMessage = 'Text-to-speech failed';
      if (err instanceof Error) {
        if (err.message.includes('timeout')) {
          errorMessage = 'Request timed out. The text might be too long.';
        } else if (err.message.includes('Network error')) {
          errorMessage = 'Cannot connect to speech service. Please check your connection.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
      setIsLoading(false);
      setIsPlaying(false);
    }
  }, [cleanup, handleAudioEnd, handleAudioError]);

  const stop = useCallback(() => {
    cleanup();
  }, [cleanup]);

  const pause = useCallback(() => {
    if (audioRef.current && isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  }, [isPlaying]);

  const resume = useCallback(() => {
    if (audioRef.current && !isPlaying) {
      audioRef.current.play().then(() => {
        setIsPlaying(true);
      }).catch((err) => {
        console.error('Resume playback error:', err);
        setError('Failed to resume playback');
      });
    }
  }, [isPlaying]);

  return {
    // State
    isPlaying,
    isLoading,
    error,
    currentAudio,
    
    // Controls
    speak,
    stop,
    pause,
    resume,
  };
};

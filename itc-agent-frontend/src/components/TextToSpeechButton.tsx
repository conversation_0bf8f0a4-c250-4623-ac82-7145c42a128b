import React from 'react';
import { Volume2, VolumeX, Loader2, Pause, Play } from 'lucide-react';
import { useTextToSpeech } from '@/hooks/useTextToSpeech';

interface TextToSpeechButtonProps {
  text: string;
  className?: string;
  disabled?: boolean;
  voiceId?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'icon' | 'button';
  children?: React.ReactNode;
}

export const TextToSpeechButton: React.FC<TextToSpeechButtonProps> = ({
  text,
  className = '',
  disabled = false,
  voiceId,
  size = 'md',
  variant = 'icon',
  children
}) => {
  const { isPlaying, isLoading, error, speak, stop, pause, resume } = useTextToSpeech();

  const handleClick = async () => {
    if (isLoading) return;

    if (isPlaying) {
      pause();
    } else if (error) {
      // Retry on error
      await speak(text, { voice_id: voiceId });
    } else {
      // Show warning for very long text
      if (text.length > 5000) {
        const proceed = window.confirm(
          `This text is quite long (${text.length} characters). ` +
          'It may take a while to generate the audio. Continue?'
        );
        if (!proceed) return;
      }

      await speak(text, { voice_id: voiceId });
    }
  };

  const handleStop = (e: React.MouseEvent) => {
    e.stopPropagation();
    stop();
  };

  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10'
  };

  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 20
  };

  const getIcon = () => {
    if (isLoading) {
      return <Loader2 size={iconSizes[size]} className="animate-spin" />;
    }
    
    if (error) {
      return <VolumeX size={iconSizes[size]} className="text-red-500" />;
    }
    
    if (isPlaying) {
      return <Pause size={iconSizes[size]} />;
    }
    
    return <Volume2 size={iconSizes[size]} />;
  };

  const getButtonClass = () => {
    const baseClass = `
      flex items-center justify-center transition-colors rounded-full
      ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-accent hover:text-accent-foreground'}
      ${error ? 'text-red-500' : 'text-muted-foreground'}
      ${isPlaying ? 'text-blue-500' : ''}
    `;

    if (variant === 'icon') {
      return `${baseClass} ${sizeClasses[size]} ${className}`;
    }

    return `${baseClass} px-3 py-2 gap-2 ${className}`;
  };

  if (!text.trim()) {
    return null;
  }

  return (
    <div className="flex items-center gap-1">
      <button
        onClick={handleClick}
        disabled={disabled}
        className={getButtonClass()}
        title={
          isLoading ? (text.length > 5000 ? 'Generating audio (this may take a while)...' : 'Loading...') :
          error ? 'Error - Click to retry' :
          isPlaying ? 'Pause speech' :
          'Play text as speech'
        }
      >
        {getIcon()}
        {variant === 'button' && (
          <span className="text-sm">
            {isLoading ? (text.length > 5000 ? 'Processing...' : 'Loading...') :
             error ? 'Retry' :
             isPlaying ? 'Pause' :
             children || 'Play'}
          </span>
        )}
      </button>

      {isPlaying && (
        <button
          onClick={handleStop}
          className={`
            flex items-center justify-center transition-colors rounded-full
            text-muted-foreground hover:bg-accent hover:text-accent-foreground
            ${sizeClasses.sm}
          `}
          title="Stop speech"
        >
          <VolumeX size={iconSizes.sm} />
        </button>
      )}

      {error && (
        <div className="text-xs text-red-500 max-w-xs truncate" title={error}>
          {error}
        </div>
      )}
    </div>
  );
};

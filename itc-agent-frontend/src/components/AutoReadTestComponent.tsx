"use client";

import React, { useState } from 'react';
import { ConversationalReport } from './ConversationalReport';
import { VoicePreferencesToggle } from './VoicePreferencesToggle';
import { useVoicePreferences } from '@/contexts/VoicePreferencesContext';
import { ReportResult } from '@/services/reportApi';

export const AutoReadTestComponent: React.FC = () => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [showReport, setShowReport] = useState(false);
  const { preferences } = useVoicePreferences();

  // Mock report data for testing
  const mockReportData: ReportResult = {
    sections: [
      "# Executive Summary\n\nThis is a test report to verify the auto-read functionality works correctly when reports finish generating.",
      "# Key Findings\n\n1. Auto-read should trigger when streaming stops\n2. Loading indicator should appear during TTS generation\n3. Audio should play automatically when ready",
      "# Conclusion\n\nThe auto-read feature enhances accessibility and user experience by automatically reading completed reports aloud."
    ],
    data: {},
    outline: "Test Report Outline",
    total_figures: 0
  };

  const simulateReportGeneration = () => {
    setShowReport(true);
    setIsStreaming(true);
    
    // Simulate report completion after 3 seconds
    setTimeout(() => {
      setIsStreaming(false);
      console.log('🎯 Report generation complete - auto-read should trigger now');
    }, 3000);
  };

  const resetTest = () => {
    setShowReport(false);
    setIsStreaming(false);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="border border-border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">Auto-Read Report Test</h2>
        
        {/* Voice Preferences */}
        <div className="mb-6">
          <h3 className="text-md font-medium mb-2">Voice Settings</h3>
          <VoicePreferencesToggle />
          <div className="mt-2 text-sm text-muted-foreground">
            Current auto-read setting: {preferences.autoReadAIResponses ? '✅ Enabled' : '❌ Disabled'}
          </div>
        </div>

        {/* Test Controls */}
        <div className="mb-6 space-x-4">
          <button
            onClick={simulateReportGeneration}
            disabled={showReport}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
          >
            {isStreaming ? 'Generating Report...' : 'Start Report Generation Test'}
          </button>
          
          <button
            onClick={resetTest}
            className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90"
          >
            Reset Test
          </button>
        </div>

        {/* Test Instructions */}
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="font-medium text-blue-900 mb-2">Test Instructions:</h4>
          <ol className="text-sm text-blue-800 space-y-1">
            <li>1. Enable "Auto-read AI responses" toggle above</li>
            <li>2. Click "Start Report Generation Test"</li>
            <li>3. Wait for report to finish "generating" (3 seconds)</li>
            <li>4. Observe: Loading indicator should appear, then audio should play automatically</li>
            <li>5. If auto-read is disabled, only manual "Listen to Report" button should be available</li>
          </ol>
        </div>

        {/* Status Display */}
        <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div className="text-sm">
            <div>Report Status: {showReport ? (isStreaming ? '🔄 Generating...' : '✅ Complete') : '⏸️ Not Started'}</div>
            <div>Auto-read Enabled: {preferences.autoReadAIResponses ? '✅ Yes' : '❌ No'}</div>
            <div>Expected Behavior: {
              showReport && !isStreaming && preferences.autoReadAIResponses 
                ? '🎤 Should auto-play audio' 
                : showReport && !isStreaming 
                ? '🔘 Manual play only' 
                : '⏳ Waiting for report completion'
            }</div>
          </div>
        </div>

        {/* Report Display */}
        {showReport && (
          <div className="border border-border rounded-lg p-4">
            <ConversationalReport
              reportData={mockReportData}
              content="Test report for auto-read functionality"
              isStreaming={isStreaming}
              currentStatus={isStreaming ? 'writing_sections' : 'complete'}
              streamingSections={[]}
              currentSectionIndex={0}
              autoRead={true}
            />
          </div>
        )}
      </div>
    </div>
  );
};

import React, { useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import { ReportChart, ChartData } from '@/components/ReportChart';
import { ReportTable } from '@/components/ReportTable';
import { StreamingSections } from './StreamingSections';
import { TextToSpeechButton } from '@/components/TextToSpeechButton';
import { useTextToSpeech } from '@/hooks/useTextToSpeech';
import { useVoicePreferences } from '@/contexts/VoicePreferencesContext';
import { ReportResult } from '@/services/reportApi';
import { Loader2 } from 'lucide-react';

interface ConversationalReportProps {
  reportData: ReportResult;
  content?: string;
  isStreaming?: boolean;
  currentStatus?: string;
  streamingSections?: string[];
  currentSectionIndex?: number;
  autoRead?: boolean; // Whether to auto-read when report completes
}

export function ConversationalReport({
  reportData,
  content,
  isStreaming = false,
  currentStatus = '',
  streamingSections = [],
  currentSectionIndex = 0,
  autoRead = false
}: ConversationalReportProps) {
  const { preferences } = useVoicePreferences();
  const { speak, isLoading: isTTSLoading, isPlaying } = useTextToSpeech();
  const hasAutoReadRef = useRef(false); // Track if we've already auto-read this report
  const parseDataFromSection = (sectionContent: string, extendedData?: Record<string, unknown>): {
    charts: ChartData[],
    tables: { data: Record<string, unknown>[] }[]
  } => {
    const charts: ChartData[] = [];
    const tables: { data: Record<string, unknown>[] }[] = [];

    if (!extendedData) {
      return { charts, tables };
    }

    // Find all placeholders in the content
    const placeholderMatches = sectionContent.match(/\[\[([^\]]+)\]\]/g) || [];

    placeholderMatches.forEach(placeholderMatch => {
      const tag = placeholderMatch.replace(/\[\[|\]\]/g, '');
      const dataObj = extendedData[tag];
      
      if (dataObj) {
        try {
          const parsedData = typeof dataObj === 'string' ? JSON.parse(dataObj) : dataObj;
          
          if (parsedData && typeof parsedData === 'object' && parsedData.presentation_type && parsedData.data) {
            const title = tag.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            
            if (parsedData.presentation_type === 'table') {
              const tableData = {
                data: Array.isArray(parsedData.data) ? parsedData.data : [parsedData.data]
              };
              tables.push(tableData);
            } else if (['chart', 'graph', 'bar_chart', 'area_chart', 'line_chart', 'pie_chart'].includes(parsedData.presentation_type)) {
              const chartData: ChartData = {
                type: parsedData.presentation_type === 'graph' ? 'bar_chart' : parsedData.presentation_type as any,
                data: Array.isArray(parsedData.data) ? parsedData.data : [parsedData.data],
                title: title,
                description: 'Generated from report data'
              };

              // Set up axes
              if (parsedData.x_axis_key) {
                if (Array.isArray(parsedData.data) && parsedData.data.length > 0) {
                  const firstItem = parsedData.data[0];
                  const keys = Object.keys(firstItem);
                  const yAxisKeys = keys.filter(key => key !== parsedData.x_axis_key);
                  chartData.axes = {
                    x_axis_key: parsedData.x_axis_key,
                    y_axis_keys: yAxisKeys
                  };
                }
              } else if (Array.isArray(parsedData.data) && parsedData.data.length > 0) {
                const firstItem = parsedData.data[0];
                const keys = Object.keys(firstItem);
                if (keys.length >= 2) {
                  chartData.axes = {
                    x_axis_key: keys[0],
                    y_axis_keys: keys.slice(1)
                  };
                }
              }

              charts.push(chartData);
            }
          }
        } catch (error) {
          console.error(`Failed to parse data for ${tag}:`, error);
        }
      }
    });

    return { charts, tables };
  };

  // Process all sections and create content with embedded visualizations
  const processAllSections = () => {
    const contentParts: Array<{
      type: 'text' | 'chart' | 'table';
      content?: string;
      chart?: ChartData;
      table?: { data: Record<string, unknown>[] };
    }> = [];

    // Add initial content if provided
    if (content) {
      contentParts.push({ type: 'text', content: content });
    }

    // Process each section
    reportData.sections?.forEach((section, index) => {
      if (!section) return;

      const { charts, tables } = parseDataFromSection(section, reportData.data);

      // Remove placeholders from section content
      let cleanedSection = section.replace(/\[\[([^\]]+)\]\]/g, '');

      // Add section content
      if (cleanedSection.trim()) {
        contentParts.push({ type: 'text', content: cleanedSection });
      }

      // Add charts
      charts.forEach(chart => {
        contentParts.push({ type: 'chart', chart });
      });

      // Add tables
      tables.forEach(table => {
        contentParts.push({ type: 'table', table });
      });
    });

    return contentParts;
  };

  // Process all sections and get text content (always do this for hooks consistency)
  const contentParts = processAllSections();

  // Extract all text content for TTS
  const getAllTextContent = (): string => {
    const textParts = contentParts
      .filter(part => part.type === 'text' && part.content)
      .map(part => part.content)
      .join('\n\n');

    return content ? `${content}\n\n${textParts}` : textParts;
  };

  const fullTextContent = getAllTextContent();

  // Simple auto-read: when the report is complete and auto-read is enabled, speak it
  useEffect(() => {
    if (autoRead &&
        preferences.autoReadAIResponses &&
        fullTextContent &&
        !hasAutoReadRef.current &&
        !isTTSLoading &&
        !isPlaying &&
        !isStreaming) { // Only auto-read when not streaming

      console.log('🎤 Auto-reading complete report (simplified approach)...');
      hasAutoReadRef.current = true;

      // Small delay to ensure everything is rendered
      const timer = setTimeout(() => {
        speak(fullTextContent);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [autoRead, preferences.autoReadAIResponses, fullTextContent, isTTSLoading, isPlaying, speak, isStreaming]);

  // Reset auto-read flag when content changes (for new reports)
  useEffect(() => {
    hasAutoReadRef.current = false;
  }, [fullTextContent]);

  // Show streaming sections during writing phase
  if (isStreaming && (currentStatus === 'writing_sections' || currentStatus === 'section_complete') && streamingSections.length > 0) {
    return (
      <div className="space-y-6">
        {/* Initial content if provided */}
        {content && (
          <div className="prose max-w-none mb-8">
            <ReactMarkdown>{content}</ReactMarkdown>
          </div>
        )}

        {/* Streaming sections with typewriter effect */}
        <StreamingSections
          sections={streamingSections}
          currentSectionIndex={currentSectionIndex}
          isWriting={currentStatus === 'writing_sections'}
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Report header with TTS button */}
      {fullTextContent && (
        <div className="flex items-center justify-between mb-4 pb-2 border-b border-border">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-muted-foreground">Research Report</span>

            {/* Show loading indicator when auto-reading is preparing */}
            {isTTSLoading && autoRead && preferences.autoReadAIResponses ? (
              <div className="flex items-center gap-2 text-xs text-blue-600">
                <Loader2 size={12} className="animate-spin" />
                <span>Preparing audio...</span>
              </div>
            ) : (
              <TextToSpeechButton
                text={fullTextContent}
                size="sm"
                variant="button"
                className="text-xs"
              >
                Listen to Report
              </TextToSpeechButton>
            )}
          </div>
        </div>
      )}

      {contentParts.map((part, index) => {
        if (part.type === 'text' && part.content) {
          return (
            <div key={`text-${index}`} className="prose max-w-none">
              <ReactMarkdown>{part.content}</ReactMarkdown>
            </div>
          );
        } else if (part.type === 'chart' && part.chart) {
          return (
            <div key={`chart-${index}`} className="my-8">
              <ReportChart chartData={part.chart} />
            </div>
          );
        } else if (part.type === 'table' && part.table) {
          return (
            <div key={`table-${index}`} className="my-8">
              <ReportTable data={part.table.data || []} />
            </div>
          );
        }
        return null;
      })}
    </div>
  );
}

import { ChevronDown } from "lucide-react";
import React, { useState, useRef, useEffect } from "react";

interface Option {
  label: string;
  value: string;
}

interface CustomDropdownProps {
  value: string;
  setValue: (val: string) => void;
  options: Option[];
  placeholder?: string;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  value,
  setValue,
  options,
  placeholder = "Select an option",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  const selectedLabel = options.find((opt) => opt.value === value)?.label;

  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleSelect = (val: string) => {
    setValue(val);
    setIsOpen(false);
  };

  return (
    <div className="relative w-48" ref={ref}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex gap-4 items-center rounded-lg px-4 py-2 text-left shadow-sm outline-none"
      >
        {selectedLabel || <span className="text-gray-800">{placeholder}</span>}
        <span className=""><ChevronDown size={"1.2rem"} /></span>
      </button>

      {isOpen && (
        <ul className="absolute z-10 mt-1 w-full border border-gray-800 rounded-md shadow-lg max-h-60 overflow-auto">
          {options.map((opt) => (
            <li
              key={opt.value}
              onClick={() => handleSelect(opt.value)}
              className={`px-4 py-2 cursor-pointer hover:bg-gray-900 ${
                value === opt.value ? "bg-gray-900 font-medium" : ""
              }`}
            >
              {opt.label}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default CustomDropdown;

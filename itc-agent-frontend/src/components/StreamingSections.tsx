import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import { TypewriterText } from './TypewriterText';
import { ReportChart, ChartData } from './ReportChart';
import { ReportTable } from './ReportTable';

interface StreamingSectionsProps {
  sections: string[];
  currentSectionIndex: number;
  isWriting: boolean;
  className?: string;
}

interface ParsedSection {
  title: string;
  content: string;
  charts: ChartData[];
  tables: { data: Record<string, unknown>[] }[];
}

export function StreamingSections({ 
  sections, 
  currentSectionIndex, 
  isWriting, 
  className = '' 
}: StreamingSectionsProps) {
  const [completedSections, setCompletedSections] = useState<number[]>([]);
  const [currentlyTyping, setCurrentlyTyping] = useState<number | null>(null);

  // Parse section content to extract charts and tables
  const parseSection = (sectionContent: string): ParsedSection => {
    const charts: ChartData[] = [];
    const tables: { data: Record<string, unknown>[] }[] = [];
    
    // Extract title (first line that starts with #)
    const lines = sectionContent.split('\n');
    const titleLine = lines.find(line => line.trim().startsWith('#'));
    const title = titleLine ? titleLine.replace(/^#+\s*/, '') : `Section ${currentSectionIndex + 1}`;
    
    // For now, return the content as-is
    // In a real implementation, you'd parse JSON data blocks and convert them to charts/tables
    const content = sectionContent;
    
    return { title, content, charts, tables };
  };

  // Handle section completion
  const handleSectionComplete = (sectionIndex: number) => {
    setCompletedSections(prev => [...prev, sectionIndex]);
    setCurrentlyTyping(null);
    
    // Start typing the next section if it exists
    if (sectionIndex + 1 < sections.length && sections[sectionIndex + 1]) {
      setTimeout(() => {
        setCurrentlyTyping(sectionIndex + 1);
      }, 500);
    }
  };

  // Start typing when a new section arrives
  useEffect(() => {
    if (sections.length > 0) {
      const latestSectionIndex = sections.length - 1;
      
      // If this is a new section and we're not already typing it
      if (!completedSections.includes(latestSectionIndex) && 
          currentlyTyping !== latestSectionIndex &&
          sections[latestSectionIndex]) {
        setCurrentlyTyping(latestSectionIndex);
      }
    }
  }, [sections, completedSections, currentlyTyping]);

  if (sections.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-8 ${className}`}>
      <AnimatePresence>
        {sections.map((section, index) => {
          if (!section) return null;
          
          const parsedSection = parseSection(section);
          const isCompleted = completedSections.includes(index);
          const isCurrentlyTyping = currentlyTyping === index;
          const shouldShow = isCompleted || isCurrentlyTyping;

          if (!shouldShow) return null;

          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="border-l-2 border-primary/30 pl-6 relative"
            >
              {/* Section indicator */}
              <div className="absolute -left-2 top-0 w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-primary-foreground rounded-full" />
              </div>

              {/* Section title */}
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Section {index + 1}</span>
                  {isCurrentlyTyping && (
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity }}
                      className="w-2 h-2 bg-primary rounded-full"
                    />
                  )}
                </h3>
                {parsedSection.title && (
                  <h4 className="text-xl font-bold text-foreground mt-1">
                    {isCurrentlyTyping ? (
                      <TypewriterText 
                        text={parsedSection.title}
                        speed={50}
                        className="text-foreground"
                      />
                    ) : (
                      parsedSection.title
                    )}
                  </h4>
                )}
              </div>

              {/* Section content */}
              <div className="prose max-w-none">
                {isCurrentlyTyping ? (
                  <TypewriterText
                    text={parsedSection.content}
                    speed={20}
                    onComplete={() => handleSectionComplete(index)}
                    className="text-foreground"
                    startDelay={parsedSection.title ? 1000 : 0}
                  />
                ) : (
                  <ReactMarkdown>{parsedSection.content}</ReactMarkdown>
                )}
              </div>

              {/* Charts and tables (shown after content is complete) */}
              {isCompleted && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                  className="mt-6 space-y-6"
                >
                  {parsedSection.charts.map((chart, chartIndex) => (
                    <div key={`chart-${index}-${chartIndex}`} className="my-8">
                      <ReportChart chartData={chart} />
                    </div>
                  ))}
                  
                  {parsedSection.tables.map((table, tableIndex) => (
                    <div key={`table-${index}-${tableIndex}`} className="my-8">
                      <ReportTable data={table.data || []} />
                    </div>
                  ))}
                </motion.div>
              )}
            </motion.div>
          );
        })}
      </AnimatePresence>

      {/* Writing indicator */}
      {isWriting && currentlyTyping === null && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex items-center gap-3 text-muted-foreground"
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full"
          />
          <span>Preparing next section...</span>
        </motion.div>
      )}
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import { speechApi } from '@/services/speechApi';
import { TextToSpeechButton } from './TextToSpeechButton';
import { AudioRecorder } from './AudioRecorder';

export const SpeechTest: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');
  const [providerInfo, setProviderInfo] = useState<any>(null);
  const [testText, setTestText] = useState('Hello! This is a test of the text-to-speech functionality.');
  const [transcribedText, setTranscribedText] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkConnection();
  }, []);

  const checkConnection = async () => {
    try {
      setConnectionStatus('checking');
      const isConnected = await speechApi.testConnection();
      
      if (isConnected) {
        setConnectionStatus('connected');
        const info = await speechApi.getProviderInfo();
        setProviderInfo(info);
      } else {
        setConnectionStatus('disconnected');
      }
    } catch (err) {
      console.error('Connection test failed:', err);
      setConnectionStatus('disconnected');
      setError(err instanceof Error ? err.message : 'Connection failed');
    }
  };

  const handleTranscription = (text: string) => {
    setTranscribedText(text);
    setError(null);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-600';
      case 'disconnected': return 'text-red-600';
      default: return 'text-yellow-600';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Connected';
      case 'disconnected': return 'Disconnected';
      default: return 'Checking...';
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="border border-border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">Speech Services Test</h2>
        
        {/* Connection Status */}
        <div className="mb-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Status:</span>
            <span className={`text-sm font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </span>
            <button
              onClick={checkConnection}
              className="text-xs text-blue-600 hover:text-blue-800 underline"
            >
              Refresh
            </button>
          </div>
          
          {providerInfo && (
            <div className="mt-2 text-xs text-muted-foreground">
              Provider: {providerInfo.provider_type} | 
              TTS: {providerInfo.tts_available ? '✅' : '❌'} | 
              STT: {providerInfo.stt_available ? '✅' : '❌'} | 
              Voices: {providerInfo.voices_count}
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {connectionStatus === 'connected' && (
          <>
            {/* Text-to-Speech Test */}
            <div className="mb-6">
              <h3 className="text-md font-medium mb-2">Text-to-Speech Test</h3>
              <div className="space-y-2">
                <textarea
                  value={testText}
                  onChange={(e) => setTestText(e.target.value)}
                  className="w-full p-2 border border-border rounded-md resize-none"
                  rows={3}
                  placeholder="Enter text to convert to speech..."
                />
                <div className="flex items-center gap-2">
                  <TextToSpeechButton 
                    text={testText}
                    variant="button"
                    size="md"
                  >
                    Play Text
                  </TextToSpeechButton>
                </div>
              </div>
            </div>

            {/* Speech-to-Text Test */}
            <div className="mb-6">
              <h3 className="text-md font-medium mb-2">Speech-to-Text Test</h3>
              <div className="space-y-3">
                <AudioRecorder
                  onTranscription={handleTranscription}
                  onError={handleError}
                />
                
                {transcribedText && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                    <p className="text-sm text-green-800">
                      <strong>Transcribed:</strong> {transcribedText}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {connectionStatus === 'disconnected' && (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-2">
              Speech services are not available.
            </p>
            <p className="text-sm text-muted-foreground">
              Make sure the backend server is running and the speech endpoints are accessible.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

"use client";

import React from 'react';
import { Settings, Mic, Volume2 } from 'lucide-react';
import { useVoicePreferences } from '@/contexts/VoicePreferencesContext';

interface VoicePreferencesToggleProps {
  className?: string;
  compact?: boolean;
}

export const VoicePreferencesToggle: React.FC<VoicePreferencesToggleProps> = ({
  className = '',
  compact = false
}) => {
  const { preferences, updatePreference } = useVoicePreferences();

  const ToggleSwitch = ({ 
    checked, 
    onChange, 
    disabled = false 
  }: { 
    checked: boolean; 
    onChange: (checked: boolean) => void;
    disabled?: boolean;
  }) => (
    <button
      onClick={() => onChange(!checked)}
      disabled={disabled}
      className={`
        relative inline-flex h-5 w-9 items-center rounded-full transition-colors
        ${checked ? 'bg-blue-600' : 'bg-gray-300'}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
      `}
      role="switch"
      aria-checked={checked}
    >
      <span
        className={`
          inline-block h-3 w-3 transform rounded-full bg-white transition-transform
          ${checked ? 'translate-x-5' : 'translate-x-1'}
        `}
      />
    </button>
  );

  if (compact) {
    return (
      <div className={`flex items-center gap-3 ${className}`}>
        {/* Auto-send toggle */}
        <div className="flex items-center gap-2">
          <Mic size={14} className="text-muted-foreground" />
          <ToggleSwitch
            checked={preferences.autoSendVoiceMessages}
            onChange={(checked) => updatePreference('autoSendVoiceMessages', checked)}
          />
        </div>

        {/* Auto-read toggle */}
        <div className="flex items-center gap-2">
          <Volume2 size={14} className="text-muted-foreground" />
          <ToggleSwitch
            checked={preferences.autoReadAIResponses}
            onChange={(checked) => updatePreference('autoReadAIResponses', checked)}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-2 text-sm font-medium text-foreground">
        <Settings size={16} />
        Voice Settings
      </div>

      {/* Auto-send voice messages */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Mic size={16} className="text-muted-foreground" />
          <div>
            <div className="text-sm font-medium text-foreground">
              Auto-send voice messages
            </div>
            <div className="text-xs text-muted-foreground">
              Automatically transcribe and send recordings
            </div>
          </div>
        </div>
        <ToggleSwitch
          checked={preferences.autoSendVoiceMessages}
          onChange={(checked) => updatePreference('autoSendVoiceMessages', checked)}
        />
      </div>

      {/* Auto-read AI responses */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Volume2 size={16} className="text-muted-foreground" />
          <div>
            <div className="text-sm font-medium text-foreground">
              Auto-read AI responses
            </div>
            <div className="text-xs text-muted-foreground">
              Automatically play AI responses aloud
            </div>
          </div>
        </div>
        <ToggleSwitch
          checked={preferences.autoReadAIResponses}
          onChange={(checked) => updatePreference('autoReadAIResponses', checked)}
        />
      </div>
    </div>
  );
};

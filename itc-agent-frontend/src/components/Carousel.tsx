import React, { useState, useEffect } from 'react';

// Define the type for a single item in the carousel
interface CarouselItem {
    src: string;
    duration: number;
    transition: string; // Currently only 'fade' is supported, can be expanded
}

// Define the props for the Carousel component
interface CarouselProps {
    items: CarouselItem[];
}

/**
 * A component that displays a series of items (images) with customizable durations 
 * and fade transitions.
 * @param {CarouselProps} props - The props for the component.
 * @param {CarouselItem[]} props.items - An array of objects, each representing a slide.
 */
const Carousel: React.FC<CarouselProps> = ({ items }) => {
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        // Ensure there are items to display to prevent errors.
        if (!items || items.length === 0) {
            return;
        }

        // The core logic for the carousel:
        // 1. Get the duration for the *current* item from its properties.
        // 2. Set a timeout to switch to the next item after that duration.
        // 3. The modulo operator (`% items.length`) ensures the carousel loops back to the beginning.
        const timer = setTimeout(() => {
            setCurrentIndex((prevIndex) => (prevIndex + 1) % items.length);
        }, items[currentIndex].duration);

        // Cleanup function: This is crucial. It clears the timeout when the component
        // unmounts or when its dependencies (currentIndex, items) change. This prevents
        // memory leaks and bugs from multiple timers running simultaneously.
        return () => clearTimeout(timer);
    }, [currentIndex, items]);

    // Don't render anything if there are no items to display.
    if (!items || items.length === 0) {
        return null;
    }

    return (
        <div className="relative w-[12rem] h-[12rem] flex items-center justify-center">
            {/* We map over all items to render them in the same spot.
              CSS classes for opacity and transition will control which one is visible.
            */}
            {items.map((item, index) => {
                const isActive = index === currentIndex;
                
                // Define the transition classes for the fade-in/fade-out effect.
                // The `duration-1000` class makes the fade animation last for 1 second.
                // You can adjust this value for faster or slower transitions.
                const transitionClasses = `
                    absolute transition-opacity duration-1000 ease-in-out
                    ${isActive ? 'opacity-100' : 'opacity-0'}
                `;
                
                return (
                    <img
                        key={index}
                        src={item.src}
                        className={`${transitionClasses} w-full h-full object-contain rounded-xl`}
                        alt={`Carousel item ${index + 1}`}
                        // Provides a fallback image in case the original src fails to load.
                        onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => { 
                            const target = e.target as HTMLImageElement;
                            target.onerror = null; 
                            target.src="https://placehold.co/192x192/1f2937/4b5563?text=Error"; 
                        }}
                    />
                );
            })}
        </div>
    );
};

export default Carousel;
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FileTextIcon, 
  SearchIcon, 
  FilterIcon, 
  MessageCircleIcon, 
  EditIcon, 
  PenToolIcon, 
  CheckCircleIcon,
  LoaderIcon
} from 'lucide-react';

interface GenerationStepsProps {
  currentStatus: string;
  isGenerating: boolean;
  className?: string;
}

const generationSteps = [
  {
    id: 'generating_uninformed_outline',
    title: 'Creating Initial Outline',
    description: 'Analyzing your question and creating a preliminary structure',
    icon: FileTextIcon,
    color: 'text-blue-500'
  },
  {
    id: 'expanding_questions',
    title: 'Expanding Research Questions',
    description: 'Identifying key areas that need investigation',
    icon: SearchIcon,
    color: 'text-purple-500'
  },
  {
    id: 'filtering_questions',
    title: 'Filtering Questions',
    description: 'Prioritizing the most relevant research questions',
    icon: FilterIcon,
    color: 'text-green-500'
  },
  {
    id: 'interviewing',
    title: 'Gathering Information',
    description: 'Collecting data and insights from available sources',
    icon: MessageCircleIcon,
    color: 'text-orange-500'
  },
  {
    id: 'generating_informed_outline',
    title: 'Creating Informed Outline',
    description: 'Building a comprehensive outline based on gathered data',
    icon: FileTextIcon,
    color: 'text-indigo-500'
  },
  {
    id: 'refining_outline',
    title: 'Refining Structure',
    description: 'Optimizing the report structure for clarity and flow',
    icon: EditIcon,
    color: 'text-pink-500'
  },
  {
    id: 'writing_sections',
    title: 'Writing Report',
    description: 'Generating detailed content for each section',
    icon: PenToolIcon,
    color: 'text-teal-500'
  },
  {
    id: 'complete',
    title: 'Report Complete',
    description: 'Your report has been successfully generated',
    icon: CheckCircleIcon,
    color: 'text-emerald-500'
  }
];

export function GenerationSteps({ currentStatus, isGenerating, className = '' }: GenerationStepsProps) {
  // Map actual streaming statuses to our step IDs
  const getStepIndex = (status: string): number => {
    switch (status) {
      case 'generating_uninformed_outline':
      case 'generating_outline': // fallback generic
        return 0;
      case 'expanding_questions':
        return 1;
      case 'filtering_questions':
        return 2;
      case 'interviewing':
        return 3;
      case 'generating_informed_outline':
        return 4;
      case 'refining_outline':
        return 5;
      case 'writing_sections':
      case 'section_complete':
        return 6;
      case 'complete':
        return 7;
      default:
        return -1;
    }
  };

  const currentStepIndex = getStepIndex(currentStatus);
  const currentStep = currentStepIndex >= 0 ? generationSteps[currentStepIndex] : null;

  // Debug logs
  console.debug('[GenerationSteps] currentStatus:', currentStatus);
  console.debug('[GenerationSteps] mapped step index:', currentStepIndex, 'of', generationSteps.length);
  console.debug('[GenerationSteps] isGenerating:', isGenerating);

  // Log when status changes
  React.useEffect(() => {
    if (currentStatus) {
      console.log(`🔄 [GenerationSteps] Status changed to: ${currentStatus} (step ${currentStepIndex + 1}/${generationSteps.length})`);
    }
  }, [currentStatus, currentStepIndex]);

  if (!isGenerating && currentStatus !== 'complete') {
    return null;
  }

  return (
    <div className={`bg-muted/30 rounded-lg p-6 border border-border/50 ${className}`}>
      <div className="flex items-center gap-4 mb-4">
        <div className="flex items-center gap-2">
          <motion.div
            animate={{ rotate: isGenerating && currentStatus !== 'complete' ? 360 : 0 }}
            transition={{ duration: 2, repeat: isGenerating && currentStatus !== 'complete' ? Infinity : 0, ease: "linear" }}
          >
            {currentStatus === 'complete' ? (
              <CheckCircleIcon className="h-5 w-5 text-emerald-500" />
            ) : (
              <LoaderIcon className="h-5 w-5 text-primary" />
            )}
          </motion.div>
          <h3 className="text-lg font-semibold text-foreground">
            {currentStatus === 'complete' ? 'Generation Complete' : 'Generating Report'}
          </h3>
        </div>
      </div>

      {/* Current Step Display */}
      <AnimatePresence mode="popLayout">
        {currentStep && (
          <motion.div
            key={currentStep.id}
            initial={{ opacity: 0, y: 8 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -8 }}
            transition={{ duration: 0.2 }}
            className="flex items-center gap-4 mb-6"
          >
            <div className={`p-3 rounded-full bg-background border border-border/50 ${currentStep.color}`}>
              <currentStep.icon className="h-5 w-5" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-foreground">{currentStep.title}</h4>
              <p className="text-sm text-muted-foreground">{currentStep.description}</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Progress Bar */}
      <div className="space-y-3">
        <div className="flex justify-between text-sm text-muted-foreground">
          <span>Progress</span>
          <span>{Math.max(0, currentStepIndex + 1)} of {generationSteps.length}</span>
        </div>

        <div className="w-full bg-muted rounded-full h-2 overflow-hidden">
          <motion.div
            key={`progress-${currentStepIndex}`}
            className="h-full bg-gradient-to-r from-primary to-primary/80 rounded-full"
            initial={{ width: 0 }}
            animate={{
              width: `${Math.max(0, ((currentStepIndex + 1) / generationSteps.length) * 100)}%`
            }}
            transition={{ duration: 0.4, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* Step Timeline */}
      <div className="mt-6 space-y-2">
        <h5 className="text-sm font-medium text-foreground mb-3">Generation Steps</h5>
        <div className="space-y-2">
          {generationSteps.map((step, index) => {
            const isCompleted = index < currentStepIndex;
            const isCurrent = index === currentStepIndex;
            const isUpcoming = index > currentStepIndex;

            return (
              <motion.div
                key={step.id}
                initial={{ opacity: 0.5 }}
                animate={{ 
                  opacity: isCompleted ? 1 : isCurrent ? 1 : 0.4,
                  scale: isCurrent ? 1.02 : 1
                }}
                transition={{ duration: 0.3 }}
                className={`flex items-center gap-3 p-2 rounded-md transition-colors ${
                  isCurrent ? 'bg-primary/10 border border-primary/20' : ''
                }`}
              >
                <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center ${
                  isCompleted 
                    ? 'bg-emerald-500 text-white' 
                    : isCurrent 
                      ? 'bg-primary text-primary-foreground' 
                      : 'bg-muted border border-border'
                }`}>
                  {isCompleted ? (
                    <CheckCircleIcon className="h-3 w-3" />
                  ) : isCurrent ? (
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                      className="w-2 h-2 bg-current rounded-full"
                    />
                  ) : (
                    <div className="w-2 h-2 bg-current rounded-full opacity-50" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <p className={`text-sm font-medium truncate ${
                    isCompleted ? 'text-foreground' : 
                    isCurrent ? 'text-foreground' : 
                    'text-muted-foreground'
                  }`}>
                    {step.title}
                  </p>
                </div>

                {isCurrent && (
                  <motion.div
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="flex-shrink-0"
                  >
                    <LoaderIcon className="h-4 w-4 text-primary animate-spin" />
                  </motion.div>
                )}
              </motion.div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

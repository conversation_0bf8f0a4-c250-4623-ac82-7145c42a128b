"use client";

import React, { useState, useEffect } from 'react';
import { MicIcon, Square, Play, Pause, Trash2, Send, Loader2 } from 'lucide-react';
import { useAudioRecorder } from '@/hooks/useAudioRecorder';
import { speechApi } from '@/services/speechApi';
import { useVoicePreferences } from '@/contexts/VoicePreferencesContext';

interface InlineAudioRecorderProps {
  onTranscription?: (text: string) => void;
  onError?: (error: string) => void;
  onCancel?: () => void;
  disabled?: boolean;
  autoStart?: boolean; // Auto-start recording when component mounts
}

export const InlineAudioRecorder: React.FC<InlineAudioRecorderProps> = ({
  onTranscription,
  onError,
  onCancel,
  disabled = false,
  autoStart = false
}) => {
  const [isTranscribing, setIsTranscribing] = useState(false);
  const { preferences } = useVoicePreferences();
  
  const {
    isRecording,
    isPaused,
    recordingTime,
    audioBlob,
    audioUrl,
    error: recordingError,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    clearRecording,
  } = useAudioRecorder();

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartRecording = async () => {
    try {
      await startRecording();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start recording';
      onError?.(errorMessage);
    }
  };

  const handleStopAndSend = async () => {
    // Stop recording first
    stopRecording();

    // The transcription will happen in the useEffect below when audioBlob is available
  };

  const handleTranscribe = async () => {
    if (!audioBlob) return;

    try {
      setIsTranscribing(true);

      // Convert blob to file
      const audioFile = new File([audioBlob], 'recording.webm', { type: 'audio/webm' });

      // Send to speech-to-text API (omit language to let API auto-detect)
      const response = await speechApi.speechToText(audioFile, {
        tag_audio_events: true,
        diarize: false,
      });

      onTranscription?.(response.text);

      // Auto-clear recording after transcription
      clearRecording();

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Transcription failed';
      console.error('Transcription error:', err);

      // Clear the recording and reset state on error
      clearRecording();

      // Call onCancel to reset the parent component state
      onCancel?.();

      // Show error to user
      onError?.(errorMessage);
    } finally {
      setIsTranscribing(false);
    }
  };

  // Auto-start recording when component mounts if autoStart is true
  useEffect(() => {
    if (autoStart && !isRecording && !audioBlob) {
      handleStartRecording();
    }
  }, [autoStart]);

  // Auto-transcribe when recording stops (audioBlob becomes available)
  useEffect(() => {
    if (audioBlob && !isTranscribing) {
      handleTranscribe();
    }
  }, [audioBlob]);

  const error = recordingError;

  // Recording state indicator
  const getRecordingIndicator = () => {
    if (isRecording && !isPaused) {
      return (
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
          <span className="text-sm font-mono text-red-600">
            REC {formatTime(recordingTime)}
          </span>
        </div>
      );
    }
    
    if (isPaused) {
      return (
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-yellow-500 rounded-full" />
          <span className="text-sm font-mono text-yellow-600">
            PAUSED {formatTime(recordingTime)}
          </span>
        </div>
      );
    }

    if (audioBlob) {
      return (
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full" />
          <span className="text-sm text-green-600">
            Recording ready
          </span>
        </div>
      );
    }

    return null;
  };

  // Recording controls
  const getRecordingControls = () => {
    if (isRecording) {
      // Recording state - show send (stop) and cancel buttons
      return (
        <div className="flex items-center gap-2">
          <button
            onClick={handleStopAndSend}
            disabled={isTranscribing}
            className="flex items-center justify-center w-8 h-8 bg-primary hover:bg-primary/90 disabled:bg-primary/50 text-primary-foreground rounded-full transition-colors"
            title="Stop and Send"
          >
            {isTranscribing ? (
              <Loader2 size={14} className="animate-spin" />
            ) : (
              <Send size={14} />
            )}
          </button>

          {!isPaused ? (
            <button
              onClick={pauseRecording}
              className="flex items-center justify-center w-6 h-6 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full transition-colors"
              title="Pause Recording"
            >
              <Pause size={12} />
            </button>
          ) : (
            <button
              onClick={resumeRecording}
              className="flex items-center justify-center w-6 h-6 bg-green-500 hover:bg-green-600 text-white rounded-full transition-colors"
              title="Resume Recording"
            >
              <Play size={12} />
            </button>
          )}
        </div>
      );
    }

    // If we have an audio blob but auto-send is disabled, show manual controls
    if (audioBlob && !preferences.autoSendVoiceMessages) {
      return (
        <div className="flex items-center gap-2">
          <audio controls src={audioUrl || undefined} className="h-6 text-xs">
            Your browser does not support the audio element.
          </audio>

          <button
            onClick={handleTranscribe}
            disabled={isTranscribing}
            className="flex items-center justify-center w-6 h-6 bg-primary hover:bg-primary/90 disabled:bg-primary/50 text-primary-foreground rounded-full transition-colors"
            title="Send Message"
          >
            {isTranscribing ? (
              <Loader2 size={12} className="animate-spin" />
            ) : (
              <Send size={12} />
            )}
          </button>

          <button
            onClick={clearRecording}
            className="flex items-center justify-center w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors"
            title="Clear Recording"
          >
            <Trash2 size={12} />
          </button>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="flex items-center justify-between w-full">
      {/* Left side - Recording indicator */}
      <div className="flex items-center gap-2 min-w-0 flex-1">
        {getRecordingIndicator()}
        
        {/* Error display */}
        {error && (
          <span className="text-xs text-red-600 truncate">
            {error}
          </span>
        )}
        
        {/* Transcribing indicator */}
        {isTranscribing && (
          <div className="flex items-center gap-2">
            <Loader2 size={12} className="animate-spin text-blue-600" />
            <span className="text-xs text-blue-600">
              Transcribing and sending...
            </span>
          </div>
        )}
      </div>

      {/* Right side - Controls */}
      <div className="flex items-center gap-2">
        {getRecordingControls()}

        {/* Cancel button - only show when recording or when we have audio but auto-send is disabled */}
        {(isRecording || (audioBlob && !preferences.autoSendVoiceMessages)) && (
          <button
            onClick={onCancel}
            className="text-xs text-muted-foreground hover:text-foreground px-2 py-1 rounded transition-colors"
            title="Cancel"
          >
            Cancel
          </button>
        )}
      </div>
    </div>
  );
};

import { ChevronDownIcon, ChevronsDownIcon } from "lucide-react";
import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface Option {
  label: string;
  value: string;
}

interface TransparentDropdownProps {
  value: string;
  setValue: (val: string) => void;
  options: Option[];
  placeholder?: string;
}

const TransparentDropdown: React.FC<TransparentDropdownProps> = ({
  value,
  setValue,
  options,
  placeholder = "Select an option",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  const selectedLabel = options.find((opt) => opt.value === value)?.label;

  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleSelect = (val: string) => {
    setValue(val);
    setIsOpen(false);
  };

  return (
    <div className="relative text-[0.8rem] " ref={ref}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex gap-1 items-center cursor-pointer rounded-full bg-secondary text-secondary-foreground px-4 py-2 text-left shadow-sm outline-none hover:bg-accent hover:text-accent-foreground transition-colors"
      >
        {selectedLabel || <span className="text-muted-foreground">{placeholder}</span>}
        <span className={`${isOpen ? 'rotate-180' : ''} duration-300`}><ChevronsDownIcon size={"1rem"} /></span>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.ul
            className="absolute z-10 mt-2 w-full border border-border bg-popover text-popover-foreground rounded-md shadow-lg max-h-60 overflow-auto"
            initial={{ opacity: 0, y: -8 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -8 }}
            transition={{ duration: 0.15 }}
          >
            <div className="px-4 py-2 text-sm text-muted-foreground">
              Select an agent type
            </div>
            {options.map((opt) => (
              <li
                key={opt.value}
                onClick={() => handleSelect(opt.value)}
                className={`px-4 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground transition-colors ${value === opt.value ? "bg-accent text-accent-foreground font-medium" : ""
                  }`}
              >
                {opt.label}
              </li>
            ))}
          </motion.ul>
        )}
      </AnimatePresence>
    </div>
  );
};

export default TransparentDropdown;

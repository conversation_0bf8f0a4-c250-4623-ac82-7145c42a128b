"use client";

import React, { useState } from 'react';
import { PlayIcon, Square, FileTextIcon, BarChart3Icon, TableIcon } from 'lucide-react';
import { useStreamingReport, ReportGenerationRequest } from '@/services/reportApi';
import { ReportChart, ChartData } from './ReportChart';
import { ReportTable } from './ReportTable';

interface ReportGeneratorProps {
  className?: string;
}

export function ReportGenerator({ className = '' }: ReportGeneratorProps) {
  const [question, setQuestion] = useState('');
  const [includeDataGaps, setIncludeDataGaps] = useState(false);
  const [useStreamingMode, setUseStreamingMode] = useState(true);



  const {
    isGenerating,
    progress,
    currentSection,
    sections,
    figures,
    error,
    finalResult,
    startStreamingReport
  } = useStreamingReport();

  const handleGenerateReport = async () => {
    if (!question.trim()) return;

    console.log('🚀 Starting report generation:', question.trim());

    const request: ReportGenerationRequest = {
      original_question: question.trim(),
      include_data_gaps: includeDataGaps
    };

    console.log('📋 Report request:', request);
    console.log('🔄 Using streaming mode:', useStreamingMode);

    try {
      await startStreamingReport(request, useStreamingMode);
      console.log('✅ Report generation started successfully');
    } catch (error) {
      console.error('❌ Error starting report generation:', error);
    }
  };



  const parseDataFromSection = (sectionContent: string, extendedData?: Record<string, unknown>): {
    charts: ChartData[],
    tables: { data: Record<string, unknown>[] }[]
  } => {
    const charts: ChartData[] = [];
    const tables: { data: Record<string, unknown>[] }[] = [];

    console.log('🔍 Parsing section content:', sectionContent.substring(0, 200) + '...');
    console.log('📊 Extended data available:', extendedData ? Object.keys(extendedData) : 'None');

    if (!extendedData) {
      console.log('⚠️ No extended data available');
      return { charts, tables };
    }

    // Find all placeholders in the content
    const placeholderMatches = sectionContent.match(/\[\[([^\]]+)\]\]/g) || [];
    console.log('🏷️ Found placeholders:', placeholderMatches);

    placeholderMatches.forEach(placeholderMatch => {
      const tag = placeholderMatch.replace(/\[\[|\]\]/g, '');
      const dataObj = extendedData[tag];

      if (dataObj) {
        console.log(`✅ Processing data for tag: ${tag}`, dataObj);

        try {
          const parsedData = typeof dataObj === 'string' ? JSON.parse(dataObj) : dataObj;

          if (parsedData && typeof parsedData === 'object' && parsedData.presentation_type && parsedData.data) {
            const title = tag.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

            if (parsedData.presentation_type === 'table') {
              const tableData = {
                data: Array.isArray(parsedData.data) ? parsedData.data : [parsedData.data]
              };
              tables.push(tableData);
              console.log(`📊 Added table for ${tag}`);
            } else if (['chart', 'graph', 'bar_chart', 'area_chart', 'line_chart', 'pie_chart'].includes(parsedData.presentation_type)) {
              const chartData: ChartData = {
                type: parsedData.presentation_type === 'graph' ? 'bar_chart' : parsedData.presentation_type as any,
                data: Array.isArray(parsedData.data) ? parsedData.data : [parsedData.data],
                title: title,
                description: 'Generated from report data'
              };

              // Set up axes
              if (parsedData.x_axis_key) {
                if (Array.isArray(parsedData.data) && parsedData.data.length > 0) {
                  const firstItem = parsedData.data[0];
                  const keys = Object.keys(firstItem);
                  const yAxisKeys = keys.filter(key => key !== parsedData.x_axis_key);
                  chartData.axes = {
                    x_axis_key: parsedData.x_axis_key,
                    y_axis_keys: yAxisKeys
                  };
                }
              } else if (Array.isArray(parsedData.data) && parsedData.data.length > 0) {
                const firstItem = parsedData.data[0];
                const keys = Object.keys(firstItem);
                if (keys.length >= 2) {
                  chartData.axes = {
                    x_axis_key: keys[0],
                    y_axis_keys: keys.slice(1)
                  };
                }
              }

              charts.push(chartData);
              console.log(`📈 Added chart for ${tag}:`, chartData);
            }
          }
        } catch (error) {
          console.error(`❌ Failed to parse data for ${tag}:`, error);
        }
      } else {
        console.warn(`❌ No data found for tag: ${tag}`);
      }
    });

    console.log(`📈 Section parsing complete: ${charts.length} charts, ${tables.length} tables`);
    return { charts, tables };
  };





  const getProgressMessage = () => {
    if (!progress.length) return '';

    const lastEvent = progress[progress.length - 1];
    switch (lastEvent.status) {
      case 'generating_uninformed_outline':
        return '📝 Generating initial report outline...';
      case 'expanding_questions':
        return '🔍 Expanding research questions...';
      case 'filtering_questions':
        return '🎯 Filtering answerable questions...';
      case 'interviewing':
        return '💬 Conducting data interviews...';
      case 'generating_informed_outline':
        return '📋 Creating informed outline...';
      case 'refining_outline':
        return '✨ Refining outline structure...';
      case 'writing_sections':
        return '✍️ Writing report sections...';
      case 'section_complete':
        const sectionNum = (lastEvent.section_index || 0) + 1;
        const figuresText = lastEvent.figures_in_section?.length
          ? ` (${lastEvent.figures_in_section.length} figure${lastEvent.figures_in_section.length !== 1 ? 's' : ''})`
          : '';
        return `✅ Completed section ${sectionNum}${figuresText}`;
      case 'complete':
        return '🎉 Report generation complete!';
      case 'error':
        return `❌ Error: ${lastEvent.error}`;
      default:
        return '⚙️ Processing...';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Connection Status */}

      {/* Input Section */}
      <div className="rounded-lg border bg-card p-6">
        <h2 className="text-xl font-semibold text-card-foreground mb-4">Generate Report</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-card-foreground mb-2">
              Research Question
            </label>
            <textarea
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              placeholder="Enter your research question (e.g., 'How many students are there?')"
              className="w-full px-3 py-2 border rounded-md bg-background text-foreground placeholder-muted-foreground resize-none"
              rows={3}
              disabled={isGenerating}
            />
          </div>

          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={includeDataGaps}
                onChange={(e) => setIncludeDataGaps(e.target.checked)}
                disabled={isGenerating}
                className="rounded"
              />
              <span className="text-sm text-card-foreground">Include data gaps section</span>
            </label>

            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={useStreamingMode}
                onChange={(e) => setUseStreamingMode(e.target.checked)}
                disabled={isGenerating}
                className="rounded"
              />
              <span className="text-sm text-card-foreground">Real-time processing</span>
            </label>
          </div>

          <button
            onClick={handleGenerateReport}
            disabled={isGenerating || !question.trim()}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isGenerating ? (
              <>
                <Square className="h-4 w-4" />
                Generating...
              </>
            ) : (
              <>
                <PlayIcon className="h-4 w-4" />
                Generate Report
              </>
            )}
          </button>
        </div>
      </div>

      {/* Progress Section */}
      {(isGenerating || progress.length > 0) && (
        <div className="rounded-lg border bg-card p-6">
          <h3 className="text-lg font-semibold text-card-foreground mb-4">Progress</h3>
          
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground">
              {getProgressMessage()}
            </div>
            
            {isGenerating && (
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${Math.min(100, (currentSection / Math.max(1, sections.length || 5)) * 100)}%` 
                  }}
                />
              </div>
            )}

            {figures.length > 0 && (
              <div className="text-sm text-muted-foreground">
                Generated {figures.length} figure{figures.length !== 1 ? 's' : ''}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="rounded-lg border border-destructive bg-destructive/10 p-4">
          <div className="text-destructive font-medium">Error</div>
          <div className="text-sm text-destructive/80 mt-1">{error}</div>
        </div>
      )}

      {/* Sections Display */}
      {sections.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-lg font-semibold text-foreground">Report Sections ({sections.length})</h3>

          {sections.map((section, index) => {
            if (!section) return null;

            console.log(`📄 Processing section ${index + 1}:`, section.substring(0, 200) + '...');

            if (finalResult?.data && index === 0) {
              console.log('🎯 Final result data keys:', Object.keys(finalResult.data));
              console.log('🎯 Final result data sample:', Object.entries(finalResult.data).slice(0, 2));
            }

            const { charts, tables } = parseDataFromSection(section, finalResult?.data);

            console.log(`📊 Section ${index + 1} parsed:`, {
              charts: charts.length,
              tables: tables.length
            });

            // Simple content processing - remove all placeholders since we render charts/tables separately
            let processedContent = section;

            // Remove all placeholders - we'll render charts/tables separately below
            processedContent = processedContent.replace(/\[\[([^\]]+)\]\]/g, (match) => {
              console.log(`🗑️ Removing placeholder: ${match}`);
              return ''; // Remove all placeholders
            });

            return (
              <div key={index} className="rounded-lg border bg-card p-6">
                <div className="flex items-center gap-2 mb-4">
                  <FileTextIcon className="h-5 w-5 text-primary" />
                  <h4 className="text-lg font-medium text-card-foreground">
                    Section {index + 1}
                  </h4>
                  {useStreamingMode && (
                    <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded">
                      Real-time
                    </span>
                  )}
                </div>

                {/* Section Content */}
                <div className="prose prose-sm max-w-none text-card-foreground mb-6">
                  <div className="whitespace-pre-wrap">
                    {processedContent}
                  </div>
                </div>

                {/* Render charts and tables */}
                {charts.map((chart, chartIndex) => (
                  <div key={`chart-${chartIndex}`} className="scroll-mt-4 my-6">
                    <div className="flex items-center gap-2 mb-4">
                      <BarChart3Icon className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium text-card-foreground">
                        {chart.title || `Chart ${chartIndex + 1}`}
                      </span>
                    </div>
                    <ReportChart chartData={chart} />
                  </div>
                ))}

                {tables.map((table, tableIndex) => (
                  <div key={`table-${tableIndex}`} className="scroll-mt-4 my-6">
                    <div className="flex items-center gap-2 mb-4">
                      <TableIcon className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium text-card-foreground">
                        Table {tableIndex + 1}
                      </span>
                    </div>
                    <ReportTable data={table.data || []} />
                  </div>
                ))}
              </div>
            );
          })}
        </div>
      )}

      {/* Final Result Summary */}
      {finalResult && (
        <div className="rounded-lg border bg-primary/5 p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Report Complete</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <div className="text-muted-foreground">Sections</div>
              <div className="font-medium">{finalResult.sections?.length || 0}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Figures</div>
              <div className="font-medium">{finalResult.total_figures || 0}</div>
            </div>
            <div>
              <div className="text-muted-foreground">Data Objects</div>
              <div className="font-medium">{Object.keys(finalResult.data || {}).length}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

import React, { useState } from 'react';
import { MicIcon, Square, Play, Pause, Trash2, Download, Send } from 'lucide-react';
import { useAudioRecorder } from '@/hooks/useAudioRecorder';
import { speechApi } from '@/services/speechApi';

interface AudioRecorderProps {
  onTranscription?: (text: string) => void;
  onError?: (error: string) => void;
  className?: string;
  disabled?: boolean;
}

export const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onTranscription,
  onError,
  className = '',
  disabled = false
}) => {
  const [isTranscribing, setIsTranscribing] = useState(false);
  
  const {
    isRecording,
    isPaused,
    recordingTime,
    audioBlob,
    audioUrl,
    error: recordingError,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    clearRecording,
    downloadRecording,
  } = useAudioRecorder();

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartRecording = async () => {
    try {
      await startRecording();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start recording';
      onError?.(errorMessage);
    }
  };

  const handleTranscribe = async () => {
    if (!audioBlob) return;

    try {
      setIsTranscribing(true);
      
      // Convert blob to file
      const audioFile = new File([audioBlob], 'recording.webm', { type: 'audio/webm' });
      
      // Send to speech-to-text API
      const response = await speechApi.speechToText(audioFile, {
        language_code: 'eng',
        tag_audio_events: true,
        diarize: false,
      });
      
      onTranscription?.(response.text);
      clearRecording(); // Clear after successful transcription
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Transcription failed';
      onError?.(errorMessage);
    } finally {
      setIsTranscribing(false);
    }
  };

  const error = recordingError;

  return (
    <div className={`flex flex-col gap-3 ${className}`}>
      {/* Recording Controls */}
      <div className="flex items-center gap-2">
        {!isRecording && !audioBlob && (
          <button
            onClick={handleStartRecording}
            disabled={disabled}
            className="flex items-center justify-center w-10 h-10 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white rounded-full transition-colors"
            title="Start Recording"
          >
            <MicIcon size={20} />
          </button>
        )}

        {isRecording && (
          <>
            <button
              onClick={stopRecording}
              className="flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-700 text-white rounded-full transition-colors"
              title="Stop Recording"
            >
              <Square size={16} />
            </button>
            
            {!isPaused ? (
              <button
                onClick={pauseRecording}
                className="flex items-center justify-center w-8 h-8 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full transition-colors"
                title="Pause Recording"
              >
                <Pause size={14} />
              </button>
            ) : (
              <button
                onClick={resumeRecording}
                className="flex items-center justify-center w-8 h-8 bg-green-500 hover:bg-green-600 text-white rounded-full transition-colors"
                title="Resume Recording"
              >
                <Play size={14} />
              </button>
            )}
            
            <span className="text-sm font-mono text-gray-600">
              {formatTime(recordingTime)}
            </span>
            
            {isPaused && (
              <span className="text-xs text-yellow-600 font-medium">PAUSED</span>
            )}
          </>
        )}

        {audioBlob && !isRecording && (
          <>
            <div className="flex items-center gap-2">
              <audio controls src={audioUrl || undefined} className="h-8">
                Your browser does not support the audio element.
              </audio>
              
              <button
                onClick={handleTranscribe}
                disabled={isTranscribing}
                className="flex items-center justify-center w-8 h-8 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-full transition-colors"
                title="Transcribe to Text"
              >
                {isTranscribing ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Send size={14} />
                )}
              </button>
              
              <button
                onClick={downloadRecording}
                className="flex items-center justify-center w-8 h-8 bg-green-500 hover:bg-green-600 text-white rounded-full transition-colors"
                title="Download Recording"
              >
                <Download size={14} />
              </button>
              
              <button
                onClick={clearRecording}
                className="flex items-center justify-center w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors"
                title="Clear Recording"
              >
                <Trash2 size={14} />
              </button>
            </div>
          </>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
          {error}
        </div>
      )}

      {/* Status Display */}
      {isTranscribing && (
        <div className="text-sm text-blue-600 bg-blue-50 border border-blue-200 rounded-md p-2">
          Transcribing audio...
        </div>
      )}
    </div>
  );
};

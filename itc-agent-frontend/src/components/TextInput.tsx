"use client";

import { GlobeIcon, MicIcon, PlusIcon, SendIcon, Settings } from 'lucide-react'
import React, { useState, KeyboardEvent } from 'react'
import { InlineAudioRecorder } from './InlineAudioRecorder'
import { VoicePreferencesToggle } from './VoicePreferencesToggle'
import { useVoicePreferences } from '@/contexts/VoicePreferencesContext'

interface Props {
    onSubmit?: (message: string) => void;
    placeholder?: string;
    disabled?: boolean;
}

export default function TextInput({ onSubmit, placeholder = "Enter message", disabled = false }: Props) {
    const [value, setValue] = useState('');
    const [isRecording, setIsRecording] = useState(false);
    const [showSettings, setShowSettings] = useState(false);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const { preferences } = useVoicePreferences();

    const handleSubmit = () => {
        if (value.trim() && onSubmit && !disabled) {
            onSubmit(value.trim());
            setValue('');
        }
    };

    const handleTranscription = (text: string) => {
        setValue(text);
        setIsRecording(false);

        // Auto-send if enabled
        if (preferences.autoSendVoiceMessages && text.trim()) {
            setTimeout(() => {
                if (onSubmit && !disabled) {
                    onSubmit(text.trim());
                    setValue('');
                }
            }, 100); // Small delay to show the transcribed text briefly
        }
    };

    const handleAudioError = (error: string) => {
        console.error('Audio error:', error);
        setErrorMessage(error);

        // Clear error after 5 seconds
        setTimeout(() => {
            setErrorMessage(null);
        }, 5000);
    };

    const handleStartRecording = () => {
        setIsRecording(true);
        setShowSettings(false); // Hide settings when recording
    };

    const handleCancelRecording = () => {
        setIsRecording(false);
        setErrorMessage(null); // Clear any error when canceling
    };

    const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit();
        }
    };

    return (
        <div className='flex flex-col w-full max-w-[50rem] mx-auto'>
            {/* Error Notification */}
            {errorMessage && (
                <div className='mb-3 p-3 border border-red-200 bg-red-50 text-red-700 rounded-lg flex items-center justify-between'>
                    <span className='text-sm'>{errorMessage}</span>
                    <button
                        onClick={() => setErrorMessage(null)}
                        className='text-red-500 hover:text-red-700 ml-2'
                        title="Dismiss"
                    >
                        ×
                    </button>
                </div>
            )}

            {/* Voice Settings Panel */}
            {showSettings && (
                <div className='mb-3 p-4 border-2 border-border bg-card rounded-lg'>
                    <VoicePreferencesToggle />
                </div>
            )}

            {/* Text Input Container */}
            <div className='border-2 border-border bg-card rounded-lg'>
                {/* Main Input Area */}
                {!isRecording ? (
                    <>
                        <input
                            value={value}
                            onChange={(e) => setValue(e.target.value)}
                            onKeyPress={handleKeyPress}
                            placeholder={placeholder}
                            disabled={disabled}
                            className='placeholder-muted-foreground text-foreground bg-transparent w-full outline-none px-4 py-3 font-medium disabled:opacity-50'
                        />
                        <div className='flex justify-between px-4 py-3'>
                            <div className='flex gap-4'>
                                <div className='text-muted-foreground flex justify-center items-center size-[2rem] cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-full transition-colors'>
                                    <PlusIcon className='' size={"1.3rem"} />
                                </div>
                                <div className='flex cursor-pointer hover:bg-accent hover:text-accent-foreground h-[2rem] px-2 gap-2 rounded-full items-center transition-colors'>
                                    <GlobeIcon className='text-muted-foreground' size={"1rem"} />
                                    <div className='text-muted-foreground text-[0.92rem]'>Web Search</div>
                                </div>
                            </div>
                            <div className='flex gap-2'>
                                {/* Voice Settings Toggle */}
                                <button
                                    onClick={() => setShowSettings(!showSettings)}
                                    disabled={disabled}
                                    className={`text-muted-foreground flex justify-center items-center size-[2rem] cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${showSettings ? 'bg-accent text-accent-foreground' : ''}`}
                                    title="Voice settings"
                                >
                                    <Settings className='' size={"1.1rem"} />
                                </button>

                                {/* Voice Input Button */}
                                <button
                                    onClick={handleStartRecording}
                                    disabled={disabled}
                                    className='text-muted-foreground flex justify-center items-center size-[2rem] cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                                    title="Voice input"
                                >
                                    <MicIcon className='' size={"1.3rem"} />
                                </button>

                                {/* Send Button */}
                                {value.trim() && (
                                    <button
                                        onClick={handleSubmit}
                                        disabled={disabled}
                                        className='text-primary flex justify-center items-center size-[2rem] cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                                    >
                                        <SendIcon className='' size={"1.3rem"} />
                                    </button>
                                )}
                            </div>
                        </div>
                    </>
                ) : (
                    /* Recording Mode - In-place transformation */
                    <div className='px-4 py-3'>
                        <InlineAudioRecorder
                            onTranscription={handleTranscription}
                            onError={handleAudioError}
                            onCancel={handleCancelRecording}
                            disabled={disabled}
                            autoStart={true}
                        />
                    </div>
                )}
            </div>
        </div>
    )
}

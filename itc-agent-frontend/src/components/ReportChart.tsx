import React from 'react';
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { useTheme } from '@/contexts/theme-context';

export interface ChartData {
  type: 'area_chart' | 'bar_chart' | 'pie_chart' | 'line_chart' | 'table' | 'number';
  data: Record<string, unknown>[] | unknown;
  axes?: {
    x_axis_key: string;
    y_axis_keys: string[];
  };
  title?: string;
  description?: string;
}

interface ReportChartProps {
  chartData: ChartData;
  className?: string;
}

export function ReportChart({ chartData, className = '' }: ReportChartProps) {
  console.log('🎨 ReportChart rendering with data:', chartData);
  const { theme } = useTheme();

  // Color palette based on theme
  const getColorPalette = () => {
    switch (theme) {
      case 'dark':
        return ['#60a5fa', '#f472b6', '#34d399', '#facc15', '#a78bfa', '#fb7185', '#fbbf24'];
      case 'light-blue':
      default:
        return ['#08518A', '#ef4444', '#f59e0b', '#10b981', '#8b5cf6', '#06b6d4', '#84cc16'];
    }
  };

  const palette = getColorPalette();

  // Format values based on type
  const getFormatter = (key: string) => {
    if (key.toLowerCase().includes('amount') || key.toLowerCase().includes('fee') || key.toLowerCase().includes('cost')) {
      return (v: number) => `$${v.toLocaleString()}`;
    }
    if (key.toLowerCase().includes('percent') || key.toLowerCase().includes('rate')) {
      return (v: number) => `${v}%`;
    }
    return (v: number) => v.toLocaleString();
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: {
    active?: boolean;
    payload?: Array<{
      color?: string;
      name?: string | number;
      dataKey?: string | number;
      value?: number | string;
    }>;
    label?: any;
  }) => {
    if (!active || !payload?.length) return null;

    return (
      <div className="rounded-lg border bg-card p-3 shadow-lg">
        <div className="mb-1 text-xs uppercase text-muted-foreground">
          {chartData.axes?.x_axis_key?.replace(/_/g, ' ') || 'Category'}
        </div>
        <div className="mb-2 font-semibold text-card-foreground">{label}</div>

        {payload.map((item, i: number) => (
          <div key={i} className="flex items-center justify-between gap-3 text-sm">
            <span className="flex items-center gap-2 capitalize text-muted-foreground">
              <span
                className="inline-block h-2 w-2 rounded-full"
                style={{ backgroundColor: item.color || '#000' }}
              />
              {(item.name || item.dataKey || '').toString().replace(/_/g, ' ')}
            </span>
            <span className="font-medium text-card-foreground">
              {getFormatter(item.dataKey?.toString() || '')(Number(item.value) || 0)}
            </span>
          </div>
        ))}
      </div>
    );
  };

  // Handle different chart types
  const renderChart = () => {
    if (!chartData.data || (Array.isArray(chartData.data) && chartData.data.length === 0)) {
      return (
        <div className="flex h-[300px] items-center justify-center">
          <div className="text-muted-foreground">No data available</div>
        </div>
      );
    }

    // Handle simple number display
    if (chartData.type === 'number') {
      return (
        <div className="flex h-[200px] items-center justify-center">
          <div className="text-center">
            <div className="text-4xl font-bold text-primary">
              {typeof chartData.data === 'number' || typeof chartData.data === 'string'
                ? chartData.data
                : String(chartData.data)}
            </div>
            {chartData.title && (
              <div className="mt-2 text-lg text-muted-foreground">{chartData.title}</div>
            )}
          </div>
        </div>
      );
    }

    // Handle table display (will be handled by separate component)
    if (chartData.type === 'table') {
      return null; // This will be handled by ReportTable component
    }

    const data = Array.isArray(chartData.data) ? chartData.data : [chartData.data];
    const axes = chartData.axes;

    if (!axes?.x_axis_key || !axes?.y_axis_keys?.length) {
      return (
        <div className="flex h-[300px] items-center justify-center">
          <div className="text-muted-foreground">Invalid chart configuration</div>
        </div>
      );
    }

    switch (chartData.type) {
      case 'area_chart':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data}>
              <defs>
                {axes.y_axis_keys.map((_, i) => (
                  <linearGradient key={i} id={`fill-${i}`} x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={palette[i % palette.length]} stopOpacity={0.8} />
                    <stop offset="95%" stopColor={palette[i % palette.length]} stopOpacity={0.1} />
                  </linearGradient>
                ))}
              </defs>
              <XAxis
                dataKey={axes.x_axis_key}
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={getFormatter(axes.y_axis_keys[0])}
              />
              <Tooltip content={CustomTooltip} />
              {axes.y_axis_keys.map((key, i) => (
                <Area
                  key={key}
                  dataKey={key}
                  type="monotone"
                  stroke={palette[i % palette.length]}
                  fill={`url(#fill-${i})`}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'bar_chart':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data}>
              <XAxis
                dataKey={axes.x_axis_key}
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={getFormatter(axes.y_axis_keys[0])}
              />
              <Tooltip content={CustomTooltip} />
              {axes.y_axis_keys.map((key, i) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={palette[i % palette.length]}
                  radius={[4, 4, 0, 0]}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line_chart':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={data}>
              <XAxis
                dataKey={axes.x_axis_key}
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={getFormatter(axes.y_axis_keys[0])}
              />
              <Tooltip content={CustomTooltip} />
              {axes.y_axis_keys.map((key, i) => (
                <Line
                  key={key}
                  dataKey={key}
                  type="monotone"
                  stroke={palette[i % palette.length]}
                  strokeWidth={2}
                  dot={{ fill: palette[i % palette.length], strokeWidth: 2, r: 4 }}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );

      case 'pie_chart':
        // For pie charts, we'll use the first y_axis_key as the value
        const pieData = data.map(item => ({
          name: item[axes.x_axis_key],
          value: item[axes.y_axis_keys[0]]
        }));

        return (
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {pieData.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={palette[index % palette.length]} />
                ))}
              </Pie>
              <Tooltip content={CustomTooltip} />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return (
          <div className="flex h-[300px] items-center justify-center">
            <div className="text-muted-foreground">Unsupported chart type: {chartData.type}</div>
          </div>
        );
    }
  };

  return (
    <div className={className}>
      {chartData.title && (
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-foreground">{chartData.title}</h3>
          {chartData.description && (
            <p className="text-sm text-muted-foreground">{chartData.description}</p>
          )}
        </div>
      )}
      {renderChart()}
    </div>
  );
}

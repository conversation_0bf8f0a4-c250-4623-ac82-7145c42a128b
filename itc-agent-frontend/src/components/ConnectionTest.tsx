"use client";

import React, { useState, useEffect } from 'react';
import { CheckCircleIcon, XCircleIcon, AlertCircleIcon } from 'lucide-react';
import { reportApi } from '@/services/reportApi';

interface ConnectionTestProps {
  className?: string;
}

export function ConnectionTest({ className = '' }: ConnectionTestProps) {
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'connected' | 'failed' | 'idle'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const testConnection = async () => {
    setConnectionStatus('testing');
    setErrorMessage('');

    try {
      const isConnected = await reportApi.testConnection();
      if (isConnected) {
        setConnectionStatus('connected');
      } else {
        setConnectionStatus('failed');
        setErrorMessage('Backend responded but health check failed');
      }
    } catch (error) {
      setConnectionStatus('failed');
      setErrorMessage(error instanceof Error ? error.message : 'Unknown connection error');
    }
  };

  // Test connection on component mount
  useEffect(() => {
    testConnection();
  }, []);

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'testing':
        return <AlertCircleIcon className="h-5 w-5 text-yellow-500 animate-pulse" />;
      case 'connected':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircleIcon className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'testing':
        return 'Testing connection...';
      case 'connected':
        return 'Backend connected';
      case 'failed':
        return 'Connection failed';
      default:
        return 'Connection status unknown';
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'testing':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-muted-foreground bg-muted border-border';
    }
  };

  return (
    <div className={`rounded-lg border p-4 ${getStatusColor()} ${className}`}>
      <div className="flex items-center gap-3">
        {getStatusIcon()}
        <div className="flex-1">
          <div className="font-medium">{getStatusText()}</div>
          {connectionStatus === 'connected' && (
            <div className="text-sm opacity-75">
              Backend is running at {process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'}
            </div>
          )}
          {connectionStatus === 'failed' && errorMessage && (
            <div className="text-sm mt-1">
              <div className="font-medium">Error:</div>
              <div className="opacity-75">{errorMessage}</div>
              <div className="mt-2 text-xs">
                <div>Troubleshooting steps:</div>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Check if backend is running on {process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'}</li>
                  <li>Verify CORS is configured in the backend</li>
                  <li>Check browser console for detailed error messages</li>
                </ul>
              </div>
            </div>
          )}
        </div>
        <button
          onClick={testConnection}
          disabled={connectionStatus === 'testing'}
          className="px-3 py-1 text-sm border rounded hover:bg-background/50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {connectionStatus === 'testing' ? 'Testing...' : 'Test'}
        </button>
      </div>
    </div>
  );
}

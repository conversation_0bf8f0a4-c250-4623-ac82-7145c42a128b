#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.db_api import run_query_silent
import json

def test_db_connection():
    """Test the database connection with a simple query"""
    
    print("🔍 Testing database connection...")
    
    # Test with a simple query that should return data
    test_queries = [
        "SELECT COUNT(*) as total_students FROM students;",
        "SELECT name FROM institutions LIMIT 5;",
        "SELECT COUNT(*) as total_institutions FROM institutions;",
        "SELECT c.name AS campus_name, COUNT(s.id) AS student_count FROM campuses c INNER JOIN students s ON c.institution_id = s.institution_id WHERE s.status = 'active' GROUP BY c.name LIMIT 5;"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Test Query {i}: {query}")
        try:
            result = run_query_silent(query)
            print(f"✅ Success! Result: {json.dumps(result, indent=2)}")
            
            # Check if we got actual data
            if result.get("data"):
                print(f"📊 Data found: {len(result['data'])} rows")
                if result["data"]:
                    print(f"📋 First row: {result['data'][0]}")
            else:
                print("❌ No data in result")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            print(f"❌ Error type: {type(e)}")

if __name__ == "__main__":
    test_db_connection()

#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chains.composite.interview import interview_chain
from app.chains.sql_runner import sql_runner_chain
from app.chains.answerer import sql_answerer_chain
import json

def test_interview_flow():
    """Test the interview flow with a simple question that should return data"""
    
    print("🔍 Testing interview flow...")
    
    # Test with a simple question that should return data
    test_question = "How many students are enrolled in each campus of the institution?"
    
    print(f"📝 Test Question: {test_question}")
    
    try:
        # Test the full interview chain
        print("\n🎯 Testing full interview chain...")
        result = interview_chain.invoke(test_question)
        
        print(f"✅ Interview completed!")
        print(f"📊 Result keys: {list(result.keys())}")
        
        if "convo_with_data" in result:
            convo_with_data = result["convo_with_data"]
            print(f"💬 Conversation turns: {len(convo_with_data)}")
            
            for i, turn in enumerate(convo_with_data):
                print(f"\n🔄 Turn {i+1}:")
                print(f"  ❓ Question: {turn.get('question', 'N/A')[:100]}...")
                print(f"  💬 Answer: {turn.get('answer', 'N/A')[:100]}...")
                print(f"  📊 Data type: {type(turn.get('data', 'N/A'))}")
                print(f"  📈 Data preview: {str(turn.get('data', 'N/A'))[:200]}...")
                print(f"  🏷️ Data tag: {turn.get('data_tag', 'N/A')}")
                print(f"  📋 Presentation type: {turn.get('presentation_type', 'N/A')}")
                
                # Check if data is actually there
                data = turn.get('data')
                if data and data != "No results":
                    print(f"  ✅ Has real data!")
                else:
                    print(f"  ❌ No real data found")
        else:
            print("❌ No convo_with_data found in result")
            
    except Exception as e:
        print(f"❌ Error in interview: {str(e)}")
        import traceback
        traceback.print_exc()

def test_individual_components():
    """Test individual components of the interview flow"""
    
    print("\n🔧 Testing individual components...")
    
    # Test SQL runner directly
    test_sql = "SELECT c.name AS campus_name, COUNT(s.id) AS student_count FROM campuses c INNER JOIN students s ON c.institution_id = s.institution_id WHERE s.status = 'active' GROUP BY c.name LIMIT 5;"
    
    print(f"\n📝 Testing SQL Runner with: {test_sql}")
    
    try:
        sql_input = {
            "question": "Test question",
            "sql": test_sql
        }
        
        sql_result = sql_runner_chain.invoke(sql_input)
        print(f"✅ SQL Runner result keys: {list(sql_result.keys())}")
        print(f"📊 Result type: {type(sql_result.get('result'))}")
        print(f"📈 Result preview: {str(sql_result.get('result'))[:200]}...")
        
        # Test SQL answerer
        if sql_result.get("result") and sql_result["result"] != "No results":
            print(f"\n🤖 Testing SQL Answerer...")
            answerer_input = {
                "question": "How many students are enrolled in each campus?",
                "result": sql_result["result"]
            }
            
            answer_result = sql_answerer_chain.invoke(answerer_input)
            print(f"✅ Answerer result keys: {list(answer_result.keys())}")
            print(f"💬 Answer: {answer_result.get('answer', 'N/A')[:200]}...")
            print(f"🏷️ Data tag: {answer_result.get('data_tag', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Error in component testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_individual_components()
    test_interview_flow()

{"cells": [{"cell_type": "code", "execution_count": 1, "id": "87a2be3c-fd56-4de7-b3a3-835e29586fc0", "metadata": {"scrolled": true}, "outputs": [], "source": ["# !pip install langchain\n", "# !pip install -qU \"langchain[openai]\"\n", "# !pip install pydantic\n", "# !pip install langchain-community tiktoken\n", "# !pip install -qU langchain-elasticsearch\n", "# !pip install langgraph"]}, {"cell_type": "markdown", "id": "3162b155-e3cf-472b-b70d-3b738628d9a8", "metadata": {}, "source": ["### CONSTANT DECLARATIONS"]}, {"cell_type": "code", "execution_count": 3, "id": "bee3b06d-a013-444a-b00a-9336153688e4", "metadata": {}, "outputs": [], "source": ["# API_KEY = \"********************************************************************************************************************************************************************\"\n", "API_KEY = \"********************************************************************************************************************************************************************\"\n", "DB_QUERY_API_URL = \"https://m4s3l32ltf.execute-api.eu-west-1.amazonaws.com/Prod/execute-query\""]}, {"cell_type": "markdown", "id": "2df6a7fd-3627-4e3a-ab5f-c45a42a6baa6", "metadata": {}, "source": ["### DB STRUCTURE REPRESENTATIONS (TXT FILE) RETRIEVAL"]}, {"cell_type": "code", "execution_count": 5, "id": "a53dd974-ebf6-4ccc-8e78-b15e81321d3b", "metadata": {"scrolled": true}, "outputs": [], "source": ["from pathlib import Path\n", "import json\n", "\n", "compact_db_structure = Path('compact_db_structure.txt').read_text(encoding='utf-8')\n", "with open(\"db_categorical_summary.json\", encoding=\"utf-8\") as f:\n", "    db_categorical_summary = json.load(f)\n", "\n", "full_db_structure = Path('full_db_structure.txt').read_text(encoding='utf-8')"]}, {"cell_type": "code", "execution_count": 6, "id": "e609655b-4d1f-4c96-af24-4c6973dccab7", "metadata": {}, "outputs": [], "source": ["# db_categorical_summary"]}, {"cell_type": "markdown", "id": "fd541bdc-3e46-42be-9f1e-81f82f2b1c00", "metadata": {}, "source": ["### RUN QUERY FUNCTION"]}, {"cell_type": "code", "execution_count": 8, "id": "08e9c2a0-9f0d-4ec3-ae18-28890dc14cfe", "metadata": {}, "outputs": [], "source": ["import requests\n", "import logging\n", "import sys\n", "\n", "# Configure logging at the beginning so you can see debug logs in the console.\n", "# Level can be DEBUG or INFO depending on verbosity needs.\n", "logging.basicConfig(stream=sys.stdout, level=logging.DEBUG)\n", "\n", "def run_query(query: str):\n", "    \"\"\"\n", "    Sends a single SQL query to the API endpoint and returns the JSON response.\n", "    \"\"\"\n", "    # Log the query to see exactly what you're sending to the server\n", "    logging.debug(\"Sending query:\\n%s\", query)\n", "\n", "    payload = {\"query\": query}\n", "    \n", "    # Log the entire payload for clarity\n", "    logging.debug(\"Payload to be sent: %s\", json.dumps(payload, indent=2))\n", "\n", "    try:\n", "        response = requests.post(DB_QUERY_API_URL, json=payload)\n", "        logging.debug(\"Response status code: %d\", response.status_code)\n", "        logging.debug(\"Raw response text: %s\", response.text)\n", "\n", "        # This will raise an HTTPError if the response was 4xx or 5xx\n", "        response.raise_for_status()\n", "\n", "        # Attempt to parse JSON from the response\n", "        # If the server doesn't return valid JSON, this could raise a ValueError\n", "        json_data = response.json()\n", "        logging.debug(\"Parsed JSON from response: %s\", json.dumps(json_data, indent=2))\n", "        \n", "        return json_data\n", "\n", "    except requests.exceptions.HTTPError as e:\n", "        logging.error(\"HTTP error occurred: %s\", e)\n", "        raise\n", "    except ValueError as ve:\n", "        logging.error(\"Could not parse JSON from the response. Error: %s\", ve)\n", "        logging.error(\"Response content was: %s\", response.text)\n", "        raise\n", "    except Exception as ex:\n", "        logging.error(\"An unexpected error occurred: %s\", ex)\n", "        raise\n", "\n", "def run_query_silent(query: str):\n", "    \"\"\"\n", "    Sends a single SQL query to the API endpoint and returns the JSON response.\n", "    \"\"\"\n", "    # Log the query to see exactly what you're sending to the server\n", "    payload = {\"query\": query}\n", "    \n", "    # try:\n", "    response = requests.post(DB_QUERY_API_URL, json=payload)\n", "\n", "    # This will raise an HTTPError if the response was 4xx or 5xx\n", "    response.raise_for_status()\n", "\n", "    # Attempt to parse JSON from the response\n", "    # If the server doesn't return valid JSON, this could raise a ValueError\n", "    json_data = response.json()\n", "    # logging.debug(\"Parsed JSON from response: %s\", json.dumps(json_data, indent=2))\n", "    \n", "    return json_data"]}, {"cell_type": "markdown", "id": "2fc71e17-3f12-447d-8066-3934da741d7e", "metadata": {}, "source": ["### GET DB SCHEMA FUNCTION"]}, {"cell_type": "code", "execution_count": 10, "id": "92e49914-519c-4dd5-bac1-c4be471aa645", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "def get_db_schema():\n", "    # A single large query that uses UNION ALL to gather tables, columns, and foreign-key info.\n", "    # info_type tells us which portion of the union the row belongs to.\n", "    # We filter out system databases: information_schema, mysql, performance_schema, sys.\n", "    big_query = \"\"\"\n", "    SELECT\n", "      'TABLE' AS info_type,\n", "      table_schema,\n", "      table_name,\n", "      NULL AS column_name,\n", "      NULL AS data_type,\n", "      NULL AS is_nullable,\n", "      NULL AS char_length,\n", "      NULL AS column_default,\n", "      NULL AS extra,\n", "      NULL AS constraint_name,\n", "      NULL AS referenced_schema,\n", "      NULL AS referenced_table,\n", "      NULL AS referenced_column\n", "    FROM information_schema.tables\n", "    WHERE table_type = 'BASE TABLE'\n", "      AND table_schema NOT IN ('information_schema','mysql','performance_schema','sys')\n", "\n", "    UNION ALL\n", "\n", "    SELECT\n", "      'COLUMN' AS info_type,\n", "      table_schema,\n", "      table_name,\n", "      column_name,\n", "      data_type,\n", "      is_nullable,\n", "      character_maximum_length,\n", "      column_default,\n", "      extra,\n", "      NULL AS constraint_name,\n", "      NULL AS referenced_schema,\n", "      NULL AS referenced_table,\n", "      NULL AS referenced_column\n", "    FROM information_schema.columns\n", "    WHERE table_schema NOT IN ('information_schema','mysql','performance_schema','sys')\n", "\n", "    UNION ALL\n", "\n", "    SELECT\n", "      'FOREIGN_KEY' AS info_type,\n", "      table_schema,\n", "      table_name,\n", "      column_name,\n", "      NULL AS data_type,\n", "      NULL AS is_nullable,\n", "      NULL AS char_length,\n", "      NULL AS column_default,\n", "      NULL AS extra,\n", "      constraint_name,\n", "      referenced_table_schema,\n", "      referenced_table_name,\n", "      referenced_column_name\n", "    FROM information_schema.key_column_usage\n", "    WHERE referenced_table_name IS NOT NULL\n", "      AND table_schema NOT IN ('information_schema','mysql','performance_schema','sys')\n", "\n", "    ORDER BY table_schema, table_name\n", "    \"\"\"\n", "\n", "    # 1) Send our single large query\n", "    results = run_query(big_query)\n", "\n", "    # Check if \"rows\" is actually the key that contains the returned data\n", "    # It's possible your API might use a different key (like \"data\" or \"Records\").\n", "    rows = results.get(\"data\", [])\n", "\n", "    # Log the length of rows to see if we actually got data\n", "    logging.debug(\"Number of rows returned: %d\", len(rows))\n", "\n", "    # If you see zero rows, investigate whether \"rows\" is the correct key or if the DB actually returned nothing.\n", "    if not rows:\n", "        logging.warning(\"No rows were returned from the query. Check your query or API.\")\n", "    \n", "    all_table_info = {}\n", "\n", "    for row in rows:\n", "        # Because each row is a dict, we access the fields by name\n", "        info_type = row[\"info_type\"]              # 'TABLE', 'COLUMN', or 'FOREIGN_KEY'\n", "        schema_name = row[\"TABLE_SCHEMA\"]\n", "        table_name = row[\"TABLE_NAME\"]\n", "\n", "        key = (schema_name, table_name)\n", "        if key not in all_table_info:\n", "            all_table_info[key] = {\n", "                \"columns\": [],\n", "                \"foreign_keys\": []\n", "            }\n", "\n", "        if info_type == \"TABLE\":\n", "            # This indicates it's just a table row; no extra columns to store right now\n", "            pass\n", "        elif info_type == \"COLUMN\":\n", "            # Pull out the relevant fields\n", "            col_info = {\n", "                \"column_name\": row[\"column_name\"],\n", "                \"data_type\": row[\"data_type\"],\n", "                \"is_nullable\": row[\"is_nullable\"],\n", "                \"char_length\": row[\"char_length\"],\n", "                \"column_default\": row[\"column_default\"],\n", "                \"extra\": row[\"extra\"]\n", "            }\n", "            all_table_info[key][\"columns\"].append(col_info)\n", "        elif info_type == \"FOREIGN_KEY\":\n", "            fk_info = {\n", "                \"constraint_name\": row[\"constraint_name\"],\n", "                \"column_name\": row[\"column_name\"],\n", "                \"referenced_schema\": row[\"referenced_schema\"],\n", "                \"referenced_table\": row[\"referenced_table\"],\n", "                \"referenced_column\": row[\"referenced_column\"]\n", "            }\n", "            all_table_info[key][\"foreign_keys\"].append(fk_info)\n", "\n", "    # Convert (schema_name, table_name) to a simpler string key\n", "    final_data = {}\n", "    for (schema_name, table_name), info in all_table_info.items():\n", "        key_str = f\"{schema_name}.{table_name}\"\n", "        final_data[key_str] = info\n", "\n", "    logging.info(\"Final schema info:\")\n", "    logging.info(json.dumps(final_data, indent=2))\n", "    return final_data\n", "\n", "def process_schema_to_JSON(schema_dict):\n", "    \"\"\"\n", "    Takes the raw schema dictionary (from get_db_schema) and returns a more\n", "    concise, text-based representation:\n", "    {\n", "      \"schema.table\": {\n", "         \"columns\": [\n", "            \"column_name (data_type, default=..., nullable=..., extra=...)\", ...\n", "         ],\n", "         \"foreign_keys\": [\n", "            \"column_name -> referenced_schema.referenced_table(referenced_column) [constraint=...]\"\n", "         ]\n", "      }, ...\n", "    }\n", "    \"\"\"\n", "    processed = {}\n", "\n", "    for table_key, info in schema_dict.items():\n", "        # Make a short string for each column\n", "        compact_columns = []\n", "        for col in info[\"columns\"]:\n", "            col_name = col[\"column_name\"]\n", "            data_type = col[\"data_type\"]\n", "            default_val = col[\"column_default\"]\n", "            is_nullable = col[\"is_nullable\"]\n", "            char_len = col[\"char_length\"]\n", "            extra = col[\"extra\"]\n", "\n", "            # Build a short text summary\n", "            # Example: \"status (SMALLINT, default=0, nullable=NO, extra=auto_increment)\"\n", "            # Skip items if they are None or empty, to keep it concise\n", "            parts = [f\"{col_name} ({data_type}\"]\n", "            if char_len is not None:\n", "                parts[-1] += f\"({char_len})\"\n", "            # Close the parenthesis\n", "            parts[-1] += \")\"\n", "\n", "            # Add default if present\n", "            if default_val is not None:\n", "                parts.append(f\"default={default_val}\")\n", "            # Mark if it's nullable or not\n", "            parts.append(f\"nullable={is_nullable}\")\n", "            if extra:\n", "                parts.append(f\"extra={extra}\")\n", "\n", "            compact_columns.append(\", \".join(parts))\n", "\n", "        # Make a short string for each foreign key\n", "        compact_fks = []\n", "        for fk in info[\"foreign_keys\"]:\n", "            col_name = fk[\"column_name\"]\n", "            c_name = fk[\"constraint_name\"]\n", "            r_schema = fk[\"referenced_schema\"]\n", "            r_table = fk[\"referenced_table\"]\n", "            r_column = fk[\"referenced_column\"]\n", "\n", "            # Example: \"col_name -> r_schema.r_table(r_column) [constraint=XYZ]\"\n", "            fk_str = f\"{col_name} -> {r_schema}.{r_table}({r_column})\"\n", "            if c_name:\n", "                fk_str += f\" [constraint={c_name}]\"\n", "            compact_fks.append(fk_str)\n", "\n", "        processed[table_key] = {\n", "            \"columns\": compact_columns,\n", "            \"foreign_keys\": compact_fks\n", "        }\n", "    return processed\n", "\n", "\n", "def process_schema_to_graph(schema_dict):\n", "    \"\"\"\n", "    Takes the raw schema dictionary (from get_db_schema) and returns a graph-like structure.\n", "    \n", "    The output format is:\n", "    \n", "    {\n", "      \"nodes\": [\n", "         {\n", "            \"id\": \"schema.table\",         # Unique identifier (schema and table name)\n", "            \"columns\": [\n", "                {\n", "                   \"name\": \"column_name\",\n", "                   \"data_type\": \"data_type\",\n", "                   \"default\": \"column_default\",\n", "                   \"nullable\": True or False,\n", "                   \"char_length\": <number or None>,\n", "                   \"extra\": \"extra\"\n", "                },\n", "                ...\n", "            ]\n", "         },\n", "         ...\n", "      ],\n", "      \"edges\": [\n", "         {\n", "            \"source\": \"schema.table\",      # Table that holds the foreign key\n", "            \"target\": \"ref_schema.ref_table\", # Referenced table\n", "            \"column\": \"local_column\",        # Column in the source table\n", "            \"referenced_column\": \"ref_column\", # Column in the referenced table\n", "            \"constraint\": \"constraint_name\"  # Optional constraint name\n", "         },\n", "         ...\n", "      ]\n", "    }\n", "    \"\"\"\n", "    nodes = []\n", "    edges = []\n", "    \n", "    for table_key, info in schema_dict.items():\n", "        # Create node for each table\n", "        node = {\"id\": table_key, \"columns\": []}\n", "        for col in info[\"columns\"]:\n", "            # Determine nullable: our schema dictionary uses \"YES\" or \"NO\"\n", "            nullable = (col.get(\"is_nullable\", \"NO\").upper() == \"YES\")\n", "            node[\"columns\"].append({\n", "                \"name\": col.get(\"column_name\"),\n", "                \"data_type\": col.get(\"data_type\"),\n", "                \"default\": col.get(\"column_default\"),\n", "                \"nullable\": nullable,\n", "                \"char_length\": col.get(\"char_length\"),\n", "                \"extra\": col.get(\"extra\")\n", "            })\n", "        nodes.append(node)\n", "        \n", "        # Create edges for foreign keys\n", "        for fk in info[\"foreign_keys\"]:\n", "            # Build target table key from referenced schema and table name.\n", "            target_table = f\"{fk.get('referenced_schema')}.{fk.get('referenced_table')}\"\n", "            edge = {\n", "                \"source\": table_key,\n", "                \"target\": target_table,\n", "                \"column\": fk.get(\"column_name\"),\n", "                \"referenced_column\": fk.get(\"referenced_column\"),\n", "                \"constraint\": fk.get(\"constraint_name\")\n", "            }\n", "            edges.append(edge)\n", "    \n", "    return {\"nodes\": nodes, \"edges\": edges}\n", "\n", "def convert_processed_json_to_compact_format(processed_json):\n", "    \"\"\"\n", "    Converts processed schema JSON (from process_schema_to_JSON) into a LangChain-friendly schema string.\n", "    Format:\n", "        users(id, name, email)\n", "        posts(id, user_id -> users.id, title)\n", "    \"\"\"\n", "    lines = []\n", "\n", "    for full_table_name, info in processed_json.items():\n", "        table_name = full_table_name.split(\".\")[1]  # Drop schema prefix if needed\n", "\n", "        column_names = []\n", "        fk_columns = set()\n", "\n", "        # Parse foreign key references\n", "        for fk in info.get(\"foreign_keys\", []):\n", "            parts = fk.split(\" -> \")\n", "            if len(parts) == 2:\n", "                col_name = parts[0].strip()\n", "                target = parts[1].split(\"(\")[0].strip()  # e.g. users.id\n", "                target = target.replace(\".\", \".\")        # keep schema.table\n", "                column_names.append(f\"{col_name} -> {target}\")\n", "                fk_columns.add(col_name)\n", "\n", "        # Add other columns (excluding foreign key columns already added)\n", "        for col in info.get(\"columns\", []):\n", "            col_name = col.split(\" \")[0].strip()  # Take the name before the opening parenthesis\n", "            if col_name not in fk_columns:\n", "                column_names.append(col_name)\n", "\n", "        line = f\"{table_name}({', '.join(column_names)})\"\n", "        lines.append(line)\n", "\n", "    return \"\\n\".join(lines)\n", "\n", "def convert_schema_to_compact_format(schema_dict):\n", "    \"\"\"\n", "    Converts a detailed schema dictionary (from get_db_schema) into a compact string format.\n", "    Example output:\n", "        users(id, name, email, created_at)\n", "        posts(id, user_id -> users.id, title, content, published)\n", "    \"\"\"\n", "    lines = []\n", "\n", "    for full_table_name, info in schema_dict.items():\n", "        table_name = full_table_name # Drop schema prefix (e.g., 'public.users' -> 'users')\n", "\n", "        column_defs = []\n", "        fk_map = {\n", "            fk[\"column_name\"]: f'{fk[\"column_name\"]} -> {fk[\"referenced_table\"]}.{fk[\"referenced_column\"]}'\n", "            for fk in info.get(\"foreign_keys\", [])\n", "        }\n", "\n", "        for col in info.get(\"columns\", []):\n", "            col_name = col[\"column_name\"]\n", "            if col_name in fk_map:\n", "                column_defs.append(fk_map[col_name])\n", "            else:\n", "                column_defs.append(col_name)\n", "\n", "        line = f\"{table_name}({', '.join(column_defs)})\"\n", "        lines.append(line)\n", "\n", "    return \"\\n\".join(lines)"]}, {"cell_type": "code", "execution_count": 11, "id": "5b5e156b-b315-41b2-9fc7-acd913eeefbb", "metadata": {}, "outputs": [], "source": ["with open(\"categorical_cache_backup.json\", encoding=\"utf-8\") as f:\n", "    temp_cache = json.load(f)\n", "# temp_cache = {}"]}, {"cell_type": "code", "execution_count": 12, "id": "8b598bec-a86c-443d-8748-b9e0ee58ce01", "metadata": {}, "outputs": [], "source": ["import logging\n", "import json\n", "\n", "temp_cache = {}\n", "\n", "def get_categorical_values():\n", "    column_query = \"\"\"\n", "    SELECT\n", "      table_schema,\n", "      table_name,\n", "      column_name,\n", "      data_type\n", "    FROM information_schema.columns\n", "    WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')\n", "      AND (\n", "          data_type LIKE 'char%' OR\n", "          data_type LIKE 'varchar%' OR\n", "          data_type LIKE 'text%' OR\n", "          data_type LIKE 'nvarchar%' OR\n", "          data_type LIKE 'nchar%' OR\n", "          data_type LIKE 'enum%'\n", "        )\n", "      AND column_name NOT IN ('id')\n", "      AND column_name NOT LIKE '%_id' ESCAPE '\\'\n", "    \"\"\"\n", "\n", "    column_results = run_query_silent(column_query)\n", "\n", "    columns = [\n", "        {k.lower(): v for k, v in row.items()}\n", "        for row in column_results.get(\"data\", [])\n", "    ]\n", "\n", "    if not columns:\n", "        logging.warning(\"No string columns found.\")\n", "        return {}\n", "\n", "    logging.info(\"Found %d categorical columns\", len(columns))\n", "\n", "    all_categorical_values = {}\n", "\n", "    for col in columns:\n", "        schema = col[\"table_schema\"]\n", "        table = col[\"table_name\"]\n", "        column = col[\"column_name\"]\n", "        key = f\"{schema}.{table}\"\n", "\n", "        if key not in all_categorical_values:\n", "            all_categorical_values[key] = {}\n", "        if key not in temp_cache:\n", "            temp_cache[key] = {}\n", "\n", "        if column in temp_cache[key]:\n", "            logging.info(f\"Cache hit for {key}.{column}\")\n", "            all_categorical_values[key][column] = temp_cache[key][column]\n", "            continue\n", "\n", "        query = f'SELECT DISTINCT {column} FROM {schema}.{table} LIMIT 50'\n", "\n", "        try:\n", "            result = run_query_silent(query)\n", "            values = [row[column] for row in result.get(\"data\", [])]\n", "            all_categorical_values[key][column] = values\n", "            temp_cache[key][column] = values\n", "        except requests.exceptions.HTTPError as e:\n", "            if e.response.status_code == 502:\n", "                logging.warning(f\"502 Bad Gateway for {key}.{column}. Skipping...\")\n", "                continue  # Skip this column, continue loop\n", "            else:\n", "                logging.error(f\"HTTP error for {key}.{column}: {e}\")\n", "                break  # Stop execution for other HTTP errors\n", "        except Exception as e:\n", "            logging.error(f\"Unhandled error for {key}.{column}: {e}\")\n", "            break  # Stop execution for all other errors\n", "        finally:\n", "            try:\n", "                with open(\"categorical_cache_backup.json\", \"w\") as f:\n", "                    json.dump(temp_cache, f, indent=2)\n", "                logging.info(\"Saved temp_cache to 'categorical_cache_backup.json'\")\n", "            except Exception as write_error:\n", "                logging.error(f\"Could not write temp_cache to file: {write_error}\")\n", "\n", "    logging.info(\"Finished collecting categorical values.\")\n", "    logging.debug(json.dumps(all_categorical_values, indent=2))\n", "    return all_categorical_values\n", "    \n", "\n", "def format_categorical_summary(categorical_data: dict) -> str:\n", "    \"\"\"\n", "    Converts categorical column value dictionary into compact human-readable text format.\n", "\n", "    Args:\n", "        categorical_data (dict): The output from get_categorical_values().\n", "\n", "    Returns:\n", "        str: Formatted string representation.\n", "    \"\"\"\n", "    lines = []\n", "    for table_name, columns in categorical_data.items():\n", "        lines.append(table_name)\n", "        for column_name, values in columns.items():\n", "            formatted_values = ', '.join(str(v) for v in values)\n", "            lines.append(f\"  - {column_name}: {formatted_values}\")\n", "        lines.append(\"\")  # empty line between tables\n", "\n", "    return \"\\n\".join(lines)\n"]}, {"cell_type": "markdown", "id": "21f5dfc2-5c17-4985-947f-c30c4e3de837", "metadata": {}, "source": ["### GENERATE SCHEMAS AND DESCRIPTIONS"]}, {"cell_type": "code", "execution_count": 14, "id": "bab25fbd-d415-46f9-8b2f-cab600241475", "metadata": {"scrolled": true}, "outputs": [], "source": ["# # db_schema = get_db_schema()\n", "\n", "# with open(\"categorical_cache_backup.json\", encoding=\"utf-8\") as f:\n", "#     temp_cache = json.load(f)\n", "# db_column_uniques = get_categorical_values()\n", "# Path(\"db_categorical_summary.json\").write_text(json.dumps(db_column_uniques, indent=2), encoding=\"utf-8\")\n", "# db_column_uniques = format_categorical_summary(db_column_uniques)\n", "# Path(\"compact_db_categorical_summary.txt\").write_text(db_column_uniques)\n", "# # schema_compact = convert_schema_to_compact_format(db_schema)\n", "# # Path(\"compact_db_structure.txt\").write_text(schema_compact)\n", "# # schema_JSON = process_schema_to_JSON(db_schema)\n", "# # Path(\"full_db_structure.txt\").write_text(json.dumps(schema_JSON, indent=2))\n", "# # schema_graph = process_schema_to_graph(db_schema)\n", "# # Path(\"full_db_graph.txt\").write_text(json.dumps(schema_graph, indent=2))"]}, {"cell_type": "code", "execution_count": 15, "id": "1268b64f-3fd7-4ed2-be34-99f118984106", "metadata": {}, "outputs": [], "source": ["# run_query_silent(\"SELECT * FROM core.student_email_format\")"]}, {"cell_type": "markdown", "id": "f2f79e35-e244-4402-84cc-42f45e6e2513", "metadata": {}, "source": ["### STRUCTURED OUTPUT DEFINITIONS"]}, {"cell_type": "code", "execution_count": 17, "id": "5a6b1396-012c-499f-8006-c365c909ec2c", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "from typing_extensions import Annotated, TypedDict, Literal\n", "\n", "from typing import List, Annotated, TypedDict\n", "\n", "class RelevantTables(TypedDict):\n", "    \"\"\"\n", "    TypedDict representing a question and its relevant table names.\n", "\n", "    Attributes:\n", "        question: The original natural language question.\n", "        tables: A list of strings representing the names of relevant tables.\n", "    \"\"\"\n", "    question: Annotated[str, ..., \"The natural language question\"]\n", "    tables: Annotated[List[str], ..., \"List of table names relevant to the query\"]\n"]}, {"cell_type": "code", "execution_count": 18, "id": "162d140b-02a9-4b0b-81f2-7bc9553f3d2b", "metadata": {}, "outputs": [], "source": ["logging.getLogger(\"openai\").setLevel(logging.WARNING)\n", "logging.getLogger(\"httpcore\").setLevel(logging.WARNING)\n", "logging.getLogger(\"httpx\").setLevel(logging.WARNING)"]}, {"cell_type": "markdown", "id": "3f16e6e4-8539-4fe1-bedb-3a933ec7fec5", "metadata": {}, "source": ["### MODEL INITIALIZATION AND QUESTION DEFINITION"]}, {"cell_type": "markdown", "id": "4d38b09e-4232-4e83-9b9d-54b580980b08", "metadata": {}, "source": ["#### Wrapper to augment model for calculating costs"]}, {"cell_type": "code", "execution_count": 21, "id": "b662da1c-a6bc-423f-b8a7-8a8688c0a4f0", "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import Runnable\n", "from langchain_core.messages import BaseMessage\n", "from typing import List, Dict, Any, Union\n", "import tiktoken\n", "from IPython.display import Markdown, display\n", "\n", "\n", "class ChatModelWrapper(Runnable):\n", "    _total_cost = 0.0  # Shared across all instances\n", "    _total_tokens = 0\n", "    _cost_by_model = {}\n", "    _input_tokens_by_model = {}\n", "    _output_tokens_by_model = {}\n", "\n", "    def __init__(self, model, model_name: str, model_costs: Dict[str, Dict[str, float]]):\n", "        self.model = model\n", "        self.model_name = model_name\n", "        cost_info = model_costs.get(model_name)\n", "\n", "        if cost_info is None or not all(k in cost_info for k in [\"input\", \"output\"]):\n", "            raise ValueError(f\"Model cost info must include 'input' and 'output' for '{model_name}'.\")\n", "\n", "        self.input_cost_per_1k = cost_info[\"input\"]\n", "        self.output_cost_per_1k = cost_info[\"output\"]\n", "\n", "        try:\n", "            self.token_encoder = tiktoken.encoding_for_model(model_name)\n", "        except KeyError:\n", "            self.token_encoder = tiktoken.get_encoding(\"cl100k_base\")\n", "\n", "    def _count_tokens(self, messages: Union[List[BaseMessage], Any], output: BaseMessage) -> Dict[str, int]:\n", "        def encode(text):\n", "            return len(self.token_encoder.encode(text))\n", "\n", "        input_tokens = 0\n", "        output_tokens = 0\n", "\n", "        if hasattr(messages, \"messages\"):\n", "            messages = messages.messages\n", "\n", "        if isinstance(messages, list):\n", "            for msg in messages:\n", "                if hasattr(msg, \"content\") and isinstance(msg.content, str):\n", "                    input_tokens += encode(msg.content)\n", "        <PERSON><PERSON> has<PERSON>r(messages, \"content\") and isinstance(messages.content, str):\n", "            input_tokens += encode(messages.content)\n", "\n", "        if hasattr(output, \"content\") and isinstance(output.content, str):\n", "            output_tokens += encode(output.content)\n", "\n", "        return {\"input\": input_tokens, \"output\": output_tokens}\n", "\n", "    def invoke(self, input: Any, config: Any = None, **kwargs) -> BaseMessage:\n", "        result = self.model.invoke(input, config=config, **kwargs)\n", "        token_counts = self._count_tokens(input, result)\n", "        self._update_cost(token_counts)\n", "        return result\n", "\n", "    def batch(self, inputs: List[Any], config: Any = None, **kwargs) -> List[BaseMessage]:\n", "        results = self.model.batch(inputs, config=config, **kwargs)\n", "        for inp, out in zip(inputs, results):\n", "            token_counts = self._count_tokens(inp, out)\n", "            self._update_cost(token_counts)\n", "        return results\n", "\n", "    def _update_cost(self, token_counts: Dict[str, int]):\n", "        input_tokens = token_counts[\"input\"]\n", "        output_tokens = token_counts[\"output\"]\n", "\n", "        cost = (\n", "            (input_tokens / 1000) * self.input_cost_per_1k +\n", "            (output_tokens / 1000) * self.output_cost_per_1k\n", "        )\n", "\n", "        ChatModelWrapper._total_cost += cost\n", "        ChatModelWrapper._total_tokens += input_tokens + output_tokens\n", "\n", "        # Track by model\n", "        ChatModelWrapper._cost_by_model.setdefault(self.model_name, 0)\n", "        ChatModelWrapper._cost_by_model[self.model_name] += cost\n", "\n", "        ChatModelWrapper._input_tokens_by_model.setdefault(self.model_name, 0)\n", "        ChatModelWrapper._input_tokens_by_model[self.model_name] += input_tokens\n", "\n", "        ChatModelWrapper._output_tokens_by_model.setdefault(self.model_name, 0)\n", "        ChatModelWrapper._output_tokens_by_model[self.model_name] += output_tokens\n", "\n", "    def with_structured_output(self, schema):\n", "        wrapped = self.model.with_structured_output(schema)\n", "        return ChatModelWrapper(\n", "            wrapped,\n", "            self.model_name,\n", "            {\n", "                self.model_name: {\n", "                    \"input\": self.input_cost_per_1k,\n", "                    \"output\": self.output_cost_per_1k\n", "                }\n", "            }\n", "        )\n", "\n", "    @classmethod\n", "    def get_total_cost(cls) -> float:\n", "        return cls._total_cost\n", "\n", "    @classmethod\n", "    def get_total_tokens(cls) -> int:\n", "        return cls._total_tokens\n", "\n", "    @classmethod\n", "    def get_cost_by_model(cls) -> Dict[str, float]:\n", "        return cls._cost_by_model\n", "\n", "    @classmethod\n", "    def get_input_tokens_by_model(cls) -> Dict[str, int]:\n", "        return cls._input_tokens_by_model\n", "\n", "    @classmethod\n", "    def get_output_tokens_by_model(cls) -> Dict[str, int]:\n", "        return cls._output_tokens_by_model\n"]}, {"cell_type": "markdown", "id": "04d4cc66-2d6d-41ef-800c-8a8af65ae50e", "metadata": {}, "source": ["#### Definitions"]}, {"cell_type": "code", "execution_count": 194, "id": "3a22856b-ec43-4d94-a503-73cf160ef8c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DEBUG:urllib3.connectionpool:Starting new HTTP connection (1): *************:9200\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"HEAD /deep_research HTTP/1.1\" 404 0\n", "INFO:elastic_transport.transport:HEAD http://*************:9200/deep_research [status:404 duration:0.375s]\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"PUT /deep_research HTTP/1.1\" 200 0\n", "INFO:elastic_transport.transport:PUT http://*************:9200/deep_research [status:200 duration:0.465s]\n", "DEBUG:urllib3.connectionpool:Starting new HTTP connection (1): *************:9200\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"GET / HTTP/1.1\" 200 0\n", "INFO:elastic_transport.transport:GET http://*************:9200/ [status:200 duration:0.291s]\n"]}, {"ename": "KeyboardInterrupt", "evalue": "Interrupted by user", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[194], line 69\u001b[0m\n\u001b[1;32m     59\u001b[0m elastic_vector_search \u001b[38;5;241m=\u001b[39m ElasticsearchStore(\n\u001b[1;32m     60\u001b[0m     es_url\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttp://*************:9200\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     61\u001b[0m     index_name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdeep_research\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     64\u001b[0m     es_password\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpassword\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     65\u001b[0m )\n\u001b[1;32m     67\u001b[0m \u001b[38;5;66;03m# question = \"Which institution has the most students?\"\u001b[39;00m\n\u001b[0;32m---> 69\u001b[0m question \u001b[38;5;241m=\u001b[39m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mEnter your question: \u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     71\u001b[0m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[1;32m     72\u001b[0m     include_data_gaps \u001b[38;5;241m=\u001b[39m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mShould we include data gaps in the final report? (y/n): \u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/ipykernel/kernelbase.py:1262\u001b[0m, in \u001b[0;36mKernel.raw_input\u001b[0;34m(self, prompt)\u001b[0m\n\u001b[1;32m   1260\u001b[0m     msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mraw_input was called, but this frontend does not support input requests.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1261\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m StdinNotImplementedError(msg)\n\u001b[0;32m-> 1262\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_input_request(\n\u001b[1;32m   1263\u001b[0m     \u001b[38;5;28mstr\u001b[39m(prompt),\n\u001b[1;32m   1264\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_parent_ident[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mshell\u001b[39m\u001b[38;5;124m\"\u001b[39m],\n\u001b[1;32m   1265\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_parent(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mshell\u001b[39m\u001b[38;5;124m\"\u001b[39m),\n\u001b[1;32m   1266\u001b[0m     password\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m   1267\u001b[0m )\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.12/site-packages/ipykernel/kernelbase.py:1305\u001b[0m, in \u001b[0;36mKernel._input_request\u001b[0;34m(self, prompt, ident, parent, password)\u001b[0m\n\u001b[1;32m   1302\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m:\n\u001b[1;32m   1303\u001b[0m     \u001b[38;5;66;03m# re-raise KeyboardInterrupt, to truncate traceback\u001b[39;00m\n\u001b[1;32m   1304\u001b[0m     msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mInterrupted by user\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m-> 1305\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m(msg) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[1;32m   1306\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m   1307\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlog\u001b[38;5;241m.\u001b[39mwarning(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mInvalid Message:\u001b[39m\u001b[38;5;124m\"\u001b[39m, exc_info\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: Interrupted by user"]}], "source": ["from langchain_openai import ChatOpenAI\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_elasticsearch import ElasticsearchStore\n", "from langchain_core.documents import Document\n", "from langchain.chat_models import init_chat_model\n", "from langchain_core.messages import HumanMessage, SystemMessage\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnableLambda\n", "from typing import List\n", "from langchain_core.runnables import Runnable\n", "from typing_extensions import Annotated, TypedDict\n", "from elasticsearch import Elasticsearch\n", "\n", "\n", "model_costs = {\n", "    \"gpt-4o\": {\"input\": 0.0025, \"output\": 0.01},\n", "    \"gpt-4o-mini\": {\"input\": 0.00015, \"output\": 0.0006},\n", "    \"gpt-4.1\": {\"input\": 0.002, \"output\": 0.008},\n", "    \"gpt-4.1-mini\": {\"input\": 0.0004, \"output\": 0.0016},\n", "    \"gpt-4.1-nano\": {\"input\": 0.0001, \"output\": 0.0004},\n", "}\n", "\n", "model_name = \"gpt-4o-mini\"\n", "\n", "openai_chat = ChatOpenAI(model_name=model_name, temperature=0, api_key=API_KEY)\n", "\n", "# Wrap it\n", "model = ChatModelWrapper(openai_chat, model_name, model_costs)\n", "# model = init_chat_model(\"gpt-4o-mini\", model_provider=\"openai\", temperature=0, api_key=API_KEY)\n", "\n", "\n", "es = Elasticsearch(\"http://*************:9200\", basic_auth=(\"elastic\", \"password\"))\n", "\n", "mapping = {\n", "    \"mappings\": {\n", "        \"properties\": {\n", "            \"page_content\": {\"type\": \"text\"},\n", "            \"metadata\": {\n", "                \"properties\": {\n", "                    \"conversation_id\": {\n", "                        \"type\": \"text\",\n", "                        \"fields\": {\"keyword\": {\"type\": \"keyword\"}}\n", "                    },\n", "                    \"partition\": {\"type\": \"keyword\"},\n", "                    \"turn_index\": {\"type\": \"integer\"},\n", "                    \"timestamp\": {\"type\": \"date\"},\n", "                    \"data_returned\": {\"type\": \"boolean\"},\n", "                    \"data_tag\": {\"type\": \"text\"}\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "if not es.indices.exists(index=\"deep_research\"):\n", "    es.indices.create(index=\"deep_research\", body=mapping)\n", "\n", "embeddings = OpenAIEmbeddings(model=\"text-embedding-3-small\", api_key=API_KEY)\n", "elastic_vector_search = ElasticsearchStore(\n", "    es_url=\"http://*************:9200\",\n", "    index_name=\"deep_research\",\n", "    embedding=embeddings,\n", "    es_user=\"elastic\",\n", "    es_password=\"password\",\n", ")\n", "\n", "# question = \"Which institution has the most students?\"\n", "\n", "question = input(\"Enter your question: \")\n", "\n", "while True:\n", "    include_data_gaps = input(\"Should we include data gaps in the final report? (y/n): \")\n", "    if include_data_gaps.lower() == \"y\":\n", "        include_data_gaps = True\n", "        break\n", "    elif include_data_gaps.lower() == \"n\":\n", "        include_data_gaps = False\n", "        break\n", "    else:\n", "        print(\"Invalid input. Please try again.\")\n"]}, {"cell_type": "markdown", "id": "d38b41f5-afce-4cfc-9a69-fbaa18d9ed72", "metadata": {}, "source": ["### SCHEMA DESCRIPTIONS GENERATOR"]}, {"cell_type": "code", "execution_count": 25, "id": "f9ba038b-a131-460d-92f0-7503b96970d9", "metadata": {}, "outputs": [], "source": ["# import os\n", "# import json\n", "# from langchain_openai import ChatOpenAI\n", "# from langchain_core.documents import Document\n", "# from langchain_core.output_parsers import StrOutputParser\n", "# from langchain_core.prompts import ChatPromptTemplate\n", "\n", "# # === CONFIG ===\n", "# SCHEMA_FILE = \"compact_db_structure.txt\"\n", "# OUTPUT_FILE = \"schema_docs.json\"\n", "\n", "# prompt = ChatPromptTemplate.from_template(\n", "#     \"Describe the purpose of the following database table and what kind of information it stores:\\n\\n{schema_line}\"\n", "# )\n", "\n", "# chain = prompt | model | StrOutputParser()\n", "\n", "# # === PARSE FUNCTION ===\n", "# def parse_schema_line(line: str):\n", "#     line = line.strip()\n", "#     if not line or \"(\" not in line or \")\" not in line:\n", "#         return None\n", "#     table_part, columns_part = line.split(\"(\", 1)\n", "#     table_name = table_part.strip()\n", "#     columns = [col.strip() for col in columns_part.rstrip(\")\").split(\",\")]\n", "#     return table_name, columns\n", "\n", "# documents = []\n", "\n", "# with open(SCHEMA_FILE, \"r\") as f:\n", "#     lines = f.readlines()\n", "    \n", "# line_num = 0\n", "# total_lines_num = len(lines)\n", "# for line in lines:\n", "#     parsed = parse_schema_line(line)\n", "#     print()\n", "#     if not parsed:\n", "#         continue\n", "\n", "#     table_name, columns = parsed\n", "\n", "#     # Call LLM to generate description\n", "#     try:\n", "#         description = chain.invoke({\"schema_line\": line.strip()})\n", "#     except Exception as e:\n", "#         print(f\"[!] Failed to generate description for {table_name}: {e}\")\n", "#         continue\n", "\n", "#     doc = Document(\n", "#         page_content=description,\n", "#         metadata={\n", "#             \"table_name\": table_name,\n", "#             \"columns\": columns,\n", "#             \"partition\": \"table_descriptions\"\n", "#         }\n", "#     )\n", "    \n", "#     line_num+=1\n", "\n", "#     print(f\"Line {line_num}/{total_lines_num}\")\n", "#     documents.append(doc)\n", "\n", "# # Serialize to JSON\n", "# with open(OUTPUT_FILE, \"w\") as f:\n", "#     json.dump([{\"page_content\": d.page_content, \"metadata\": d.metadata} for d in documents], f, indent=2)\n", "\n", "# print(f\"✅ Saved {len(documents)} documents to {OUTPUT_FILE}\\nCost: {model.get_total_cost()} Tokens: {model.get_total_tokens()}\")"]}, {"cell_type": "code", "execution_count": 26, "id": "b58232c8-a710-4960-ae1c-000758f2c50e", "metadata": {"scrolled": true}, "outputs": [], "source": ["# elastic_vector_search.add_documents([Document(\n", "#     page_content=d.page_content, \n", "#     metadata={**d.metadata, \"partition\": \"table_descriptions\"}) \n", "#                              for d in documents], ids=[d.metadata[\"table_name\"] \n", "#                                                    for d in documents])"]}, {"cell_type": "markdown", "id": "1f14435b-c954-4c05-8926-98e2de1d1417", "metadata": {}, "source": ["### QUESTION EXPANDER"]}, {"cell_type": "code", "execution_count": 28, "id": "a01b7860-4707-4209-8a6f-5541ecfa8a2b", "metadata": {}, "outputs": [], "source": ["# 1) Define a new TypedDict for the output (list of deeper questions).\n", "class ExpandedQuestions(TypedDict):\n", "    \"\"\"\n", "    TypedDict representing the deeper, related questions.\n", "    \n", "    Attributes:\n", "        questions: A list of strings containing follow-up or exploratory questions.\n", "    \"\"\"\n", "    questions: Annotated[List[str], ..., \"A list of deeper questions that expand on the main query\"]\n", "\n", "# 3) Create the system and user templates.\n", "#    They keep the structure of \"system + user\" but direct the AI to return *questions* only.\n", "system_template = \"\"\"\n", "You are an expert at brainstorming deeper, related questions for a given user inquiry.\n", "Below is the database schema (which may or may not be used), but your primary task is\n", "to expand upon the user's question in order to generate in-depth follow-up questions.\n", "\n", "{schema_text}\n", "\"\"\"\n", "\n", "user_template = \"\"\"\n", "User question: \"{question}\"\n", "\n", "Generate a list of follow-up questions that go deeper into the specific topic raised in the original question.\n", "\n", "Guidelines:\n", "- All follow-up questions must stay within the **logical scope** of the original question. Do **not make assumptions** that go beyond the data implied.\n", "- For example, if the question is \"Which institution do students owe the most fees?\", do **not assume** this means the institution has the highest tuition or charges the most per student.\n", "- Refer back to the subject of the original question **without using vague phrases** like \"that institution\" or \"this institution\". Instead, say \"the institution where students owe the most fees\" (or use the same phrasing from the original question).\n", "- Avoid generalizations, assumptions, or reinterpretations of the original question’s meaning.\n", "- Output only the list of follow-up questions. No explanations, no headers.\n", "\n", "Your goal is to generate precise, relevant, and logically consistent follow-up questions.\n", "\"\"\"\n", "\n", "# 4) Create a ChatPromptTemplate that orchestrates these messages\n", "prompt_template = ChatPromptTemplate.from_messages([\n", "    (\"system\", system_template),\n", "    (\"user\", user_template),\n", "])\n", "\n", "# 6) Build a single chain that returns the \"ExpandedQuestions\" typed dict\n", "expanded_questions_chain = (prompt_template | model.with_structured_output(ExpandedQuestions))"]}, {"cell_type": "markdown", "id": "f89d613d-fdb4-41a6-a8c5-1b7976ad9819", "metadata": {}, "source": ["### QUESTION VERIFIER"]}, {"cell_type": "code", "execution_count": 30, "id": "12cc4c9c-83e0-4806-89af-f544df4cfc1d", "metadata": {"scrolled": true}, "outputs": [], "source": ["class QuestionCheck(TypedDict):\n", "    \"\"\"\n", "    TypedDict for determining if a single question is answerable from the DB schema.\n", "\n", "    Attributes:\n", "        answerable: A boolean indicating whether the question is answerable.\n", "        reasoning: A short explanation of why/why not it's answerable.\n", "    \"\"\"\n", "    answerable: Annotated[bool, ..., \"True if the question can be answered from the schema, false otherwise\"]\n", "    reasoning: Annotated[str, ..., \"Detailed explanation of how or why the question is answerable\"]\n", "\n", "\n", "system_template_1 = \"\"\"\n", "You are an SQL expert with access to a database schema and a natural language question.\n", "\n", "Your job is to determine if the question is answerable from the schema, be it directly or indirectly by having to create joins.\n", "\n", "You will determine if the question can be answered from the schema, possibly after receiving feedback from a failed SQL attempt.\n", "\n", "You may also receive optional feedback from a previous attempt to answer the question — consider it while assessing answerability.\n", "\n", "\n", "Return your answer as JSON with two fields:\n", "- \"answerable\": a boolean\n", "- \"reasoning\": a detailed explanation\n", "\"\"\"\n", "\n", "user_template_1 = \"\"\"\n", "Database schema: {schema_text}\n", "\n", "Question: \"{question}\"\n", "\n", "{feedback}\n", "\n", "Can this question be correctly and completely answered from the schema?\n", "\"\"\"\n", "\n", "prompt_template_1 = ChatPromptTemplate.from_messages([\n", "    (\"system\", system_template_1),\n", "    (\"user\", user_template_1),\n", "])\n", "\n", "question_check_chain = (prompt_template_1 | model.with_structured_output(QuestionCheck))\n", "\n", "# # --- Step 1: Generate deeper or follow-up questions ---\n", "# expanded_questions_response = expanded_questions_chain.invoke({\n", "#     \"schema_text\": compact_db_structure,\n", "#     \"question\": question\n", "# })"]}, {"cell_type": "code", "execution_count": 31, "id": "f9933fbc-aa0d-490a-90a7-bbf66fe35f94", "metadata": {}, "outputs": [], "source": ["def generate_question_markdown(question_data: dict, check_results: list) -> str:\n", "    output_lines = []\n", "\n", "    for question, result in zip(question_data['questions'], check_results):\n", "        status = \"✅ **Answerable**\" if result['answerable'] else \"❌ **Not Answerable**\"\n", "        output_lines.append(f\"**{question}**  \\n{status}  \\n- {result['reasoning']}\\n\")\n", "\n", "    return '\\n'.join(output_lines)\n", "\n", "def extract_answerable_questions(question_data: dict, check_results: list) -> dict:\n", "    answerable = [\n", "        question\n", "        for question, result in zip(question_data['questions'], check_results)\n", "        if result['answerable']\n", "    ]\n", "    return answerable\n", "\n", "extract_answerable_questions_chain = RunnableLambda(extract_answerable_questions)"]}, {"cell_type": "code", "execution_count": 32, "id": "4fb321f5-1b3a-4fe3-87a7-67510abffa8d", "metadata": {}, "outputs": [], "source": ["# from IPython.display import Markdown, display\n", "\n", "# display(Markdown(generate_question_markdown(expanded_questions_response,question_check_response)))"]}, {"cell_type": "markdown", "id": "3674137c-00f3-40eb-80bf-d05eb7d6795c", "metadata": {}, "source": ["### PARALLEL BATCH QUESTION VERIFICATION"]}, {"cell_type": "code", "execution_count": 34, "id": "6666ac63-acad-458e-8226-ba65202b4d39", "metadata": {}, "outputs": [], "source": ["def batch_question_verification(questions_dict):\n", "    question_check_response = question_check_chain.batch([ {\"question\": q, \"schema_text\": compact_db_structure, \"feedback\": \"\"}\n", "                                                          for q in questions_dict[\"questions\"]])\n", "    return extract_answerable_questions(questions_dict,question_check_response)\n", "# sql_query_gen_chain\n", "\n", "batch_question_verification_chain = RunnableLambda(batch_question_verification)"]}, {"cell_type": "markdown", "id": "b2cd8f16-dbca-4703-abcc-62c4111c9f7d", "metadata": {}, "source": ["### TABLES FINDER CHAIN"]}, {"cell_type": "code", "execution_count": 36, "id": "f3c28056-bc51-4324-85d2-46bee1f441b8", "metadata": {}, "outputs": [], "source": ["system_template = \"\"\"\n", "Here is the database schema:\n", "\n", "{schema_text}\n", "\"\"\"\n", "\n", "user_template = \"\"\"\n", "Which tables would you query to answer the question: \"{question}\"?\n", "\n", "{feedback}\n", "\n", "Output only the full table names, with no extra text. eg. core.some_table.\n", "\"\"\"\n", "\n", "prompt_template = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\"system\", system_template),\n", "        (\"user\", user_template)\n", "    ]\n", ")\n", "\n", "tables_finder_chain = (prompt_template | model.with_structured_output(RelevantTables))"]}, {"cell_type": "markdown", "id": "b5555d75-6031-4a2c-8040-7307bff42db1", "metadata": {}, "source": ["### PARALLEL BATCH TABLES FINDER CHAIN"]}, {"cell_type": "code", "execution_count": 38, "id": "be8b6586-2c79-4d47-a11d-6268d325d06d", "metadata": {}, "outputs": [], "source": ["def batch_tables_finder(answerable_questions_dict):\n", "    table_finder_response = tables_finder_chain.batch([ \n", "        {\"question\": q, \"schema_text\": compact_db_structure} for q in answerable_questions_dict[\"answerable_questions\"]\n", "    ])\n", "    return {\"questions_tables\": table_finder_response}\n", "\n", "batch_tables_finder_chain = RunnableLambda(batch_tables_finder)"]}, {"cell_type": "markdown", "id": "07a4be86-2709-4af0-a780-17dab4899657", "metadata": {}, "source": ["### RELEVANT TABLE SCHEMAS EXTRACTOR"]}, {"cell_type": "code", "execution_count": 40, "id": "778fc9bd-8f85-4ae0-9f21-4ae1ad531099", "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnableLambda\n", "\n", "def get_relevant_tables_schema(tables):\n", "    db_json = json.loads(full_db_structure)\n", "    relevant_tables_json = {}\n", "    for table in tables:\n", "        relevant_tables_json[table] = db_json[table]\n", "    \n", "    return convert_processed_json_to_compact_format(relevant_tables_json)\n", "    \n", "def get_relevant_tables_categorical_summary(tables):\n", "    db_json = db_categorical_summary\n", "    relevant_tables_json = {}\n", "    for table in tables:\n", "        try:\n", "            relevant_tables_json[table] = db_json[table]\n", "        except:\n", "            pass #If the table has no categorical values\n", "    \n", "    return format_categorical_summary(relevant_tables_json)\n", "    \n", "relevant_tables_schemas_extractor_chain = RunnableLambda(lambda output_from_first: {\n", "            # Pass along the user’s original question or anything else you need\n", "            \"question\": output_from_first[\"question\"],\n", "    \n", "            # output_from_first is the dict returned by .with_structured_output()\n", "            # e.g.  {\"tables\": [\"StudentFees\", \"Institution\"], ...}\n", "            \"tables_schema\": get_relevant_tables_schema(output_from_first[\"tables\"]),\n", "            \"tables_categorical_summary\": get_relevant_tables_categorical_summary(output_from_first[\"tables\"])\n", "        })\n", "\n", "# get_relevant_tables_schema(response[\"tables\"])"]}, {"cell_type": "code", "execution_count": 41, "id": "c2e017f4-2dd7-4d75-9e1d-e54c9365cfe2", "metadata": {}, "outputs": [{"data": {"text/plain": ["'academic_years(created_at, end_year, id, institution_id, start_year, status, updated_at)'"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["get_relevant_tables_schema([\"core.academic_years\"])"]}, {"cell_type": "code", "execution_count": 42, "id": "767cf9e7-c6d9-49f6-90bf-03a774d890e1", "metadata": {}, "outputs": [{"data": {"text/plain": ["'core.academic_years\\n  - status: Active\\n'"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["get_relevant_tables_categorical_summary([\"core.academic_years\"])"]}, {"cell_type": "markdown", "id": "ded9344a-2e40-4750-b4fe-2848dc864d23", "metadata": {}, "source": ["### BATCH RELEVANT TABLE SCHEMAS EXTRACTOR"]}, {"cell_type": "code", "execution_count": 44, "id": "0aa2e5d3-d6f8-4a87-aa55-4f9d34dde658", "metadata": {}, "outputs": [], "source": ["def batch_relevant_tables_schemas_extractor(questions_tables_dict):\n", "    relevant_tables_schemas_extractor_response = relevant_tables_schemas_extractor_chain.batch(questions_tables_dict[\"questions_tables\"])\n", "    return {\"questions_schemas\": relevant_tables_schemas_extractor_response}\n", "\n", "batch_relevant_tables_schemas_extractor_chain = RunnableLambda(batch_relevant_tables_schemas_extractor)"]}, {"cell_type": "markdown", "id": "c024c773-6ca8-48fa-951b-da70b9a65306", "metadata": {}, "source": ["### SQL GENERATOR CHAIN"]}, {"cell_type": "code", "execution_count": 46, "id": "f4ea3152-6d8c-4806-bec1-634cd6f565ff", "metadata": {}, "outputs": [], "source": ["from typing import TypedDict, Annotated\n", "from langchain.prompts import ChatPromptTemplate\n", "\n", "class SQLAndReasoning(TypedDict):\n", "    \"\"\"\n", "    TypedDict representing the output of a query generation process.\n", "\n", "    Attributes:\n", "        question: The original natural language question.\n", "        sql: The generated SQL query.\n", "        reasoning: Any accompanying text explaining the logical reasoning behind how the SQL was formed.\n", "    \"\"\"\n", "    question: Annotated[str, ..., \"The natural language question\"]\n", "    sql: Annotated[str, ..., \"The generated SQL query\"]\n", "    reasoning: Annotated[str, ..., \"Explains the logic or steps used to form the SQL query\"]\n", "\n", "system_template_2 = \"\"\"\n", "You are a helpful SQL query generator. You will receive:\n", "1. A list of relevant tables and their schema from the database.\n", "2. A summary of categorical columns and their known unique values.\n", "3. The user's original question.\n", "\n", "You may receive feedback from a previous failed attempt. Use it to improve your query generation.\n", "\n", "Your job is to generate a SQL query that answers the question using these tables.\n", "Return your answer as JSON with three fields: \n", "`sql`, `reasoning`, and `text`.\n", "\n", "Use the categorical summary to better understand what values to filter on (e.g., sex = 'M' vs 'male').\n", "\"\"\"\n", "\n", "user_template_2 = \"\"\"\n", "Relevant tables schema: {tables_schema}\n", "\n", "Known categorical values summary:\n", "{tables_categorical_summary}\n", "\n", "User question: \"{question}\"\n", "\n", "Feedback from prior attempt (if any): {feedback}\n", "\n", "Generate a SQL query that will answer the user's question. \n", "\n", "- Default to showing results for all relevant entities unless the question specifies a filter.\n", "- Return only the specific columns that directly answer the user's question. Avoid SELECT * unless all fields are explicitly requested.\n", "- If the question is about types, categories, summaries, or totals, prefer DISTINCT, COUNT, SUM, or other aggregations — not raw data.\n", "- Focus on the user's intent and optimize the result for clarity and relevance, not for completeness.\n", "\n", "Include some reasoning about how you arrived at this query in the `reasoning` field, and any extra explanation in the `text` field.\n", "\"\"\"\n", "\n", "prompt_template_2 = ChatPromptTemplate.from_messages([\n", "    (\"system\", system_template_2),\n", "    (\"user\", user_template_2),\n", "])\n", "\n", "sql_and_reasoning_chain = (\n", "    prompt_template_2 |\n", "    model.with_structured_output(SQLAndReasoning)\n", ")\n"]}, {"cell_type": "markdown", "id": "8a5a0962-fa3d-4c98-9dc9-f7b7d5b8caa9", "metadata": {}, "source": ["### PARALLEL BATCH SQL GENERATOR CHAIN"]}, {"cell_type": "code", "execution_count": 48, "id": "c159eec5-de22-4bcd-bcf9-824bbdc8ad67", "metadata": {}, "outputs": [], "source": ["def batch_sql_and_reasoning(questions_schemas_dict):\n", "    sql_and_reasoning_response = sql_and_reasoning_chain.batch(questions_schemas_dict[\"questions_schemas\"])\n", "    return {\"sql_reasoning\": sql_and_reasoning_response}\n", "\n", "batch_sql_and_reasoning_chain = RunnableLambda(batch_sql_and_reasoning)"]}, {"cell_type": "markdown", "id": "4151b512-b48c-4e4a-a7ab-1ef73686d59e", "metadata": {}, "source": ["### SQL VERIFIER CHAIN"]}, {"cell_type": "code", "execution_count": 50, "id": "23b2ff8b-7368-459e-b865-b526652fe522", "metadata": {}, "outputs": [], "source": ["class SQLVerification(TypedDict):\n", "    \"\"\"\n", "    TypedDict representing the output of a query verification process.\n", "\n", "    Attributes:\n", "        question: The original natural language question.\n", "        sql: The generated SQL query.\n", "        correct: A boolean indicating whether the query answers the question precisely.\n", "        reasoning: Any accompanying text explaining the logical reasoning behind how the SQL was formed.\n", "        feedback: Suggest how the question could be clarified or how the SQL might be improved.\n", "    \"\"\"\n", "    question: Annotated[str, ..., \"The natural language question\"]\n", "    sql: Annotated[str, ..., \"The generated SQL query\"]\n", "    correct: Annotated[bool, ..., \"True if the query truly fetches the relevant information that answers the question precisely based on the schema, false otherwise\"]\n", "    reasoning: Annotated[str, ..., \"Explains the logic or steps used to verify the SQL query and arrive at the decision on whether it is correct\"]\n", "    feedback: Annotated[str, ..., \"Suggested improvement or critical note for debugging or retrying\"]\n", "\n", "# --- SQL Verification Prompt Templates ---\n", "\n", "sql_verification_system_template = \"\"\"\n", "You are an SQL expert verifying a SQL query against a given question and database schema.\n", "\n", "You will receive:\n", "1. A database schema\n", "2. A natural language question\n", "3. A generated SQL query\n", "\n", "Your job is to assess if the SQL query correctly answers the question based on the schema.\n", "\n", "Respond in JSON format with:\n", "- \"question\": The original natural language question\n", "- \"sql\": The SQL query being evaluated\n", "- \"correct\": A boolean indicating if the SQL accurately answers the question\n", "- \"reasoning\": A detailed explanation for your judgment\n", "- \"feedback\": Suggest how the question could be clarified or how the SQL might be improved.\n", "\n", "\"\"\"\n", "\n", "sql_verification_user_template = \"\"\"\n", "Database schema:\n", "{tables_schema}\n", "\n", "Question:\n", "\"{question}\"\n", "\n", "Generated SQL:\n", "{sql}\n", "\n", "Does the SQL query correctly and completely answer the question?\n", "\"\"\"\n", "\n", "# Updated prompt template for verification\n", "sql_verification_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", sql_verification_system_template),\n", "    (\"user\", sql_verification_user_template),\n", "])\n", "\n", "sql_verification_chain = sql_verification_prompt | model.with_structured_output(SQLVerification)"]}, {"cell_type": "markdown", "id": "6dc699a3-3ae0-482a-bc5e-b65f1a4ecde8", "metadata": {}, "source": ["### SQL GENERATION - VERIFICATION LOOP CHAIN"]}, {"cell_type": "code", "execution_count": 52, "id": "5ba03df0-42a2-4442-a81d-d1cc88c9c3e1", "metadata": {}, "outputs": [], "source": ["logs = {}\n", "\n", "def with_optional_feedback(base_input: dict, feedback: str | None = None):\n", "    return {\n", "        **base_input,\n", "        \"feedback\": f'Feedback from previous step: \"{feedback}\"' if feedback else \"\"\n", "    }\n", "\n", "def sql_gen_veri(input_question):\n", "    global logs\n", "    feedback = \"\"\n", "    retry_count = 0\n", "    MAX_RETRIES = 5\n", "    log_entries = []  # Temporary list to collect logs for this run\n", "\n", "    def add_log(entry):\n", "        # print(entry)\n", "        log_entries.append(entry)\n", "\n", "    add_log(\"\\n🔍 Starting SQL generation and verification process\")\n", "    add_log(f\"🔎 User Question: \\\"{input_question}\\\"\")\n", "\n", "    while retry_count < MAX_RETRIES:\n", "        retry_count += 1\n", "        add_log(f\"\\n🔁 Retry Attempt #{retry_count} — Feedback: \\\"{feedback}\\\"\")\n", "\n", "        # Step 1: Verify answerability\n", "        answerable_count = 0\n", "        answerable_loop_count = 0\n", "        while True:\n", "            answerable_loop_count += 1\n", "            add_log(f\"  🔄 Checking answerability — Loop {answerable_loop_count}\")\n", "\n", "            question_check_input = with_optional_feedback({\n", "                \"schema_text\": compact_db_structure,\n", "                \"question\": input_question\n", "            }, feedback)\n", "\n", "            answerable_reasoning_dict = question_check_chain.invoke(question_check_input)\n", "            add_log(f\"    ✅ Answerability Check Result: {answerable_reasoning_dict}\")\n", "\n", "            if answerable_reasoning_dict[\"answerable\"]:\n", "                answerable_count += 1\n", "            else:\n", "                answerable_count -= 1\n", "\n", "            if answerable_loop_count == 2:\n", "                if answerable_count == -2:\n", "                    add_log(\"❌ Determined to be unanswerable after 2 consistent failures.\")\n", "                    logs[input_question] = \"\\n\".join(log_entries)\n", "                    return {\n", "                        \"question\": input_question,\n", "                        \"answerable\": <PERSON><PERSON><PERSON>,\n", "                        \"reasoning\": answerable_reasoning_dict[\"reasoning\"],\n", "                        \"feedback\": feedback\n", "                    }\n", "                if answerable_count == 2:\n", "                    add_log(\"✅ Consistently answerable — proceeding to table extraction and SQL generation.\")\n", "                    break\n", "            elif answerable_loop_count == 3:\n", "                add_log(\"❌ Could not consistently determine answerability in 3 attempts.\")\n", "                logs[input_question] = \"\\n\".join(log_entries)\n", "                return {\n", "                    \"question\": input_question,\n", "                    \"answerable\": <PERSON><PERSON><PERSON>,\n", "                    \"reasoning\": \"Too much uncertainty as to whether or not it can be answered\",\n", "                    \"feedback\": feedback\n", "                }\n", "\n", "        # Step 2: Find relevant tables\n", "        add_log(\"📊 Finding relevant tables...\")\n", "        tables_finder_input = with_optional_feedback({\n", "            \"schema_text\": compact_db_structure,\n", "            \"question\": input_question\n", "        }, feedback)\n", "        question_tables_dict = tables_finder_chain.invoke(tables_finder_input)\n", "        add_log(f\"    📌 Relevant Tables: {question_tables_dict['tables']}\")\n", "\n", "        # Step 3: Extract relevant schema\n", "        add_log(\"📂 Extracting relevant schema...\")\n", "        question_schema_dict = relevant_tables_schemas_extractor_chain.invoke(question_tables_dict)\n", "\n", "        # Step 4: Generate SQL\n", "        add_log(\"🛠️ Generating SQL query...\")\n", "        sql_input = with_optional_feedback({\n", "            \"tables_schema\": question_schema_dict[\"tables_schema\"],\n", "            \"tables_categorical_summary\": question_schema_dict[\"tables_categorical_summary\"],\n", "            \"question\": input_question\n", "        }, feedback)\n", "        sql_and_reasoning_results = sql_and_reasoning_chain.invoke(sql_input)\n", "        add_log(f\"    🧠 SQL Reasoning: {sql_and_reasoning_results['reasoning']}\")\n", "        add_log(f\"    📝 Generated SQL: {sql_and_reasoning_results['sql']}\")\n", "\n", "        # Step 5: Verify SQL\n", "        add_log(\"🧪 Verifying SQL correctness...\")\n", "        sql_verification_results = sql_verification_chain.invoke({\n", "            **question_schema_dict,\n", "            \"question\": input_question,\n", "            \"sql\": sql_and_reasoning_results[\"sql\"]\n", "        })\n", "        add_log(f\"    🔍 SQL Verification Result: {sql_verification_results}\")\n", " \n", "        if sql_verification_results[\"correct\"]:\n", "            add_log(\"✅ SQL verified successfully.\")\n", "            logs[input_question] = \"\\n\".join(log_entries)\n", "            return sql_verification_results\n", "\n", "        # Step 6: <PERSON><PERSON> with feedback\n", "        add_log(\"❌ SQL verification failed. Looping with new feedback...\")\n", "        feedback = sql_verification_results.get(\"feedback\", sql_verification_results.get(\"reasoning\", \"\"))\n", "\n", "    # Step 7: Max retries hit\n", "    add_log(f\"🚫 Reached maximum retries ({MAX_RETRIES}). Returning last attempt with feedback.\")\n", "    logs[input_question] = \"\\n\".join(log_entries)\n", "    return {\n", "        \"question\": input_question,\n", "        \"sql\": sql_and_reasoning_results[\"sql\"],\n", "        \"correct\": <PERSON><PERSON><PERSON>,\n", "        \"reasoning\": sql_verification_results[\"reasoning\"],\n", "        \"feedback\": feedback,\n", "        \"note\": f\"Reached max retries ({MAX_RETRIES}) without finding a valid SQL.\"\n", "    }\n", "\n", "\n", "sql_gen_veri_chain = RunnableLambda(sql_gen_veri)"]}, {"cell_type": "code", "execution_count": 53, "id": "90463c04-b049-41b0-b026-39936d95aab8", "metadata": {}, "outputs": [], "source": ["def sql_runner(sql_gen_dict):\n", "    \"\"\"\n", "    Runs a verified SQL query and wraps the result with the original context for traceability.\n", "    \"\"\"\n", "    \n", "    question = sql_gen_dict[\"question\"]\n", "    try:\n", "        result = run_query_silent(sql_gen_dict[\"sql\"])\n", "        logs[question] = logs.get(question, \"\") + \"\\n\\n📥 **SQL Execution Result:**\\n```\\n\" + str(result) + \"\\n```\"\n", "        return {\n", "            **sql_gen_dict,\n", "            \"result\": result[\"data\"]\n", "        }\n", "    except Exception as e:\n", "        logs[question] = logs.get(question, \"\") + \"\\n\\n❌ **SQL Execution Error:**\\n```\\n\" + str(e) + \"\\n```\"\n", "        return {\n", "            **sql_gen_dict,\n", "            \"result\": \"No results\",\n", "            \"error\": str(e)\n", "        }\n", "\n", "sql_runner_chain = RunnableLambda(sql_runner)\n", "\n", "\n", "# def batch_sql_runner(sql_queries_dict):\n", "#     sql_runner_response = sql_runner_chain.batch(sql_queries_dict[\"sql_queries\"])\n", "#     return {\"sql_outputs\":sql_runner_response}\n", "\n", "# batch_sql_runner_chain = RunnableLambda(batch_sql_runner)\n", "\n", "# def filter_and_run_sql_outputs(results_dict: dict) -> dict:\n", "#     \"\"\"\n", "#     Filters and returns only the entries from 'sql_queries' where 'correct' is True.\n", "\n", "#     Args:\n", "#         results_dict (dict): A dictionary with the key 'sql_queries', \n", "#                              containing a list of SQL generation results.\n", "\n", "#     Returns:\n", "#         dict: A dictionary with the same key, but only including entries with correct == True.\n", "#     \"\"\"\n", "#     correct_outputs = [\n", "#         entry for entry in results_dict[\"sql_queries\"]\n", "#         if entry.get(\"correct\") is True\n", "#     ]\n", "\n", "#     outputs = batch_sql_runner({\"sql_queries\": correct_outputs})\n", "    \n", "#     return outputs\n", "\n", "# filter_and_run_sql_outputs_chain = RunnableLambda(filter_and_run_sql_outputs)"]}, {"cell_type": "markdown", "id": "ad7292cf-dbf7-4aee-a854-5841bea8e32b", "metadata": {}, "source": ["### ANSWERER"]}, {"cell_type": "code", "execution_count": 55, "id": "417917bb-ccf1-41d0-b60d-51bf8f80ba6b", "metadata": {}, "outputs": [], "source": ["# 1. Structured Output Schema\n", "class SQLAnswer(TypedDict):\n", "    answer: Annotated[\n", "        str,\n", "        \"A concise, natural language explanation of the SQL result that directly answers the user's question.\"\n", "    ]\n", "    data_tag: Annotated[\n", "        str,\n", "        \"A short, descriptive tag summarizing the SQL result in the context of the question. Must be a valid Python snake_case variable name.\"\n", "    ]\n", "\n", "# 2. System Prompt\n", "sql_answerer_system = \"\"\"\n", "You are an expert data analyst and communicator.\n", "\n", "You will be given:\n", "- A user's natural language question\n", "- The results of an SQL query that was executed to answer that question\n", "\n", "Your task is to:\n", "1. Write a clear, insightful, and accurate explanation that directly answers the user's question using the provided data.\n", "2. Suggest a data tag name — a short, descriptive variable name that clearly summarizes what the SQL result represents.\n", "\n", "Instructions for the data tag:\n", "- It must follow Python variable naming conventions (snake_case).\n", "- It should reflect the relationship between the data and the question.\n", "- Avoid generic names like 'data', 'result', or 'info'.\n", "- Examples of good tags: 'student_enrollments_by_department', 'average_gpa_by_faculty', 'monthly_library_visits'\n", "\"\"\"\n", "\n", "# 3. User Prompt Template\n", "sql_answerer_user = \"\"\"\n", "User question:\n", "{question}\n", "\n", "SQL results:\n", "{result}\n", "\n", "Write a clear natural language answer to the question using the results, and suggest an appropriate snake_case data tag.\n", "\"\"\"\n", "\n", "# 4. Prompt Template\n", "sql_answerer_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", sql_answerer_system),\n", "    (\"user\", sql_answerer_user)\n", "])\n", "\n", "# 5. Final Chain (replace `model` with your actual model instance)\n", "sql_answerer_chain = sql_answerer_prompt | model.with_structured_output(SQLAnswer)"]}, {"cell_type": "markdown", "id": "abc4c722-6362-4570-b23a-bba59992d6d9", "metadata": {}, "source": ["### INITIAL OUTLINE GENERATOR"]}, {"cell_type": "code", "execution_count": 57, "id": "0db96813-dc3a-4195-8030-5c42d9aa65c5", "metadata": {}, "outputs": [], "source": ["# 1. Output TypedDict\n", "class ReportOutline(TypedDict):\n", "    outline: Annotated[str, ..., \"Markdown-style outline summarizing the queries and insights.\"]\n", "\n", "# 2. Prompt Templates\n", "report_outline_system = \"\"\"\n", "You are a technical writer generating a report outline.\n", "\n", "Each entry below represents:\n", "- A user question\n", "\n", "Use this information to generate a clean, structured markdown-formatted outline for a report.\n", "\n", "Only return the outline — do not write the full report.\n", "\"\"\"\n", "\n", "report_outline_user = \"\"\"\n", "Original question: \"{original_question}\"\n", "\n", "Now write a structured outline for a report that investigates the original question.\n", "\"\"\"\n", "\n", "# 3. Prompt Template\n", "report_outline_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", report_outline_system),\n", "    (\"user\", report_outline_user)\n", "])\n", "\n", "# 4. <PERSON>\n", "report_outline_chain = report_outline_prompt | model.with_structured_output(ReportOutline)"]}, {"cell_type": "code", "execution_count": 58, "id": "989236fd-8b02-4006-a410-6a0017ffa4b4", "metadata": {}, "outputs": [], "source": ["# result = report_outline_chain.invoke({\n", "#     \"original_question\": question\n", "# })"]}, {"cell_type": "code", "execution_count": 59, "id": "5bcbab4a-cd05-47b9-ab13-305cc3591090", "metadata": {"scrolled": true}, "outputs": [], "source": ["# result"]}, {"cell_type": "code", "execution_count": 60, "id": "ad457623-04a4-485a-afa7-d3f2a041f149", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.0"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["model.get_total_cost()"]}, {"cell_type": "markdown", "id": "2a5d9452-1eab-4ca7-bc46-be2f36e1e12a", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["### REPORT GENERATOR"]}, {"cell_type": "code", "execution_count": 62, "id": "04c24ef6-26fa-4edb-8f37-4e2a1bfd9c64", "metadata": {}, "outputs": [], "source": ["class ReportMarkdown(TypedDict):\n", "    markdown: Annotated[str, ..., \"The full report written in Markdown format.\"]\n", "\n", "report_markdown_system = \"\"\"\n", "You are a data analyst and technical writer. You are given an outline for a report, along with SQL-based summaries that contain:\n", "\n", "- The original question\n", "- Each follow-up question\n", "- Reasoning about how each was answered\n", "- Data results from the database\n", "\n", "Your job is to write a complete, readable, well-organized report in Markdown format based on the outline and supporting data.\n", "\n", "Write clearly and concisely. Do not include raw SQL unless relevant.\n", "Include tables, bullet points, or paragraphs as needed.\n", "\"\"\"\n", "\n", "report_markdown_user = \"\"\"\n", "Original question:\n", "{original_question}\n", "\n", "Report outline:\n", "{outline}\n", "\n", "Question summaries and data:\n", "{question_summaries}\n", "\n", "Write the full report in Markdown below.\n", "\"\"\"\n", "\n", "report_markdown_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", report_markdown_system),\n", "    (\"user\", report_markdown_user)\n", "])\n", "\n", "report_markdown_chain = report_markdown_prompt | model.with_structured_output(ReportMarkdown)"]}, {"cell_type": "markdown", "id": "9b264696-1bdd-441f-b735-02298d060baa", "metadata": {}, "source": ["### QUESTIONER"]}, {"cell_type": "code", "execution_count": 64, "id": "c9e21a6a-ef91-4ab9-8071-bb4ac36f4397", "metadata": {}, "outputs": [], "source": ["class FollowUpQuestion(TypedDict):\n", "    follow_up_question: Annotated[str, \"The next best question to ask, or 'No further questions' if the topic has been fully explored.\"]\n", "\n", "interview_questioner_system = \"\"\"\n", "You are an intelligent and curious interviewer, referred to as 'Questioner' conducting a structured conversation with an expert, referred to as \"Expert\".\n", "You are trying to gather insightful, in-depth information about a topic.\n", "\n", "You will be given:\n", "- The main topic of interest\n", "- A history of prior questions you (Questioner) have asked, and the expert's answers\n", "\n", "Your job is to ask the next best question that builds on the discussion so far, uncovers deeper insights, or explores unexplained angles.\n", "\n", "Avoid repeating what's already been covered.\n", "Ask only one thoughtful, specific question at a time.\n", "Be concise, but make sure the question adds value to the dialogue.\n", "IMPORTANT: If you have no further questions, say these exact words \"No further questions\"\n", "\"\"\"\n", "\n", "interview_questioner_user = \"\"\"\n", "Main topic:\n", "{topic}\n", "\n", "Conversation so far:\n", "{history}\n", "\n", "Ask the next best follow-up question to deepen the interview. \n", "\"\"\"\n", "\n", "interview_questioner_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", interview_questioner_system),\n", "    (\"user\", interview_questioner_user)\n", "])\n", "\n", "interview_questioner = (interview_questioner_prompt | model.with_structured_output(FollowUpQuestion))"]}, {"cell_type": "markdown", "id": "0974dc57-558a-49c1-b334-4aa16937d00e", "metadata": {}, "source": ["### INFORMED REPORT OUTLINE GENERATOR"]}, {"cell_type": "code", "execution_count": 66, "id": "41b86ded-323e-4d12-8e80-174832ed5649", "metadata": {}, "outputs": [], "source": ["# 1. Output TypedDict\n", "class InformedReportOutline(TypedDict):\n", "    outline: Annotated[str, ..., \"Markdown-style outline synthesizing key insights from the interviews, structured into a coherent and logically flowing report.\"]\n", "\n", "# 2. Prompt Templates\n", "report_outline_system = \"\"\"\n", "You are an expert technical writer creating a clear, logical, and structured outline for a professional report.\n", "\n", "The report is based on:\n", "- An original guiding question\n", "- Multiple interview transcripts or summaries, all addressing the same question\n", "\n", "Your task is to extract and organize key themes and insights from the content, crafting a markdown-style outline that would form the structure of a well-written report.\n", "\n", "Structure it so that it flows logically, with clearly defined sections and subsections. Highlight connections, contrasts, and trends across the content where applicable.\n", "\n", "Do not write the full report — just the outline. Do not include any sections or items that there isn't information for.\n", "\"\"\"\n", "\n", "report_outline_user = \"\"\"\n", "Original Question:\n", "{original_question}\n", "\n", "Interview Histories:\n", "{interview_histories}\n", "\n", "Now write a coherent markdown-style outline for a report investigating the original question using the interviews above.\n", "\"\"\"\n", "\n", "# 3. Prompt Template\n", "informed_report_outline_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", report_outline_system),\n", "    (\"user\", report_outline_user)\n", "])\n", "\n", "# 4. <PERSON>\n", "informed_report_outline_chain = informed_report_outline_prompt | model.with_structured_output(InformedReportOutline)\n"]}, {"cell_type": "markdown", "id": "5e0bf08b-ff3f-490f-8475-8f676865a0e4", "metadata": {}, "source": ["### REFINED REPORT OUTLINE GENERATOR"]}, {"cell_type": "code", "execution_count": 68, "id": "226df3ae-4a35-4d2e-ad8d-da7f1a3d2c8c", "metadata": {}, "outputs": [], "source": ["# 1. Output TypedDict\n", "class RefinedReportOutline(TypedDict):\n", "    outline: Annotated[\n", "        str,\n", "        ...,\n", "        \"A refined markdown-style outline that merges the uninformed and informed outlines, structured to best reflect the interview history while improving flow and completeness without introducing new information.\"\n", "    ]\n", "\n", "# 2. Prompt Templates\n", "refine_outline_system = \"\"\"\n", "You are a technical-writing assistant. You are given:\n", "\n", "- **Uninformed outline** – drafted before interview insights  \n", "- **Informed outline**   – drafted after interview insights\n", "\n", "**Goal**\n", "\n", "Combine the two into one refined, markdown-style outline that\n", "\n", "- Uses factual content ONLY from the informed outline and the interview insights\n", "- May borrow ordering or phrasing from the uninformed outline if it improves flow\n", "- Removes redundancies and ensures a clear, logical progression\n", "- Preserves a precise, technical tone\n", "\n", "---\n", "\n", "### Special rules\n", "\n", "#### 1.  Introduction  \n", "After the heading `## Introduction`, write **one concise paragraph** (no bullets) that  \n", "1) states the purpose / scope of the report, and  \n", "2) presents the key answer to the guiding question in plain language.\n", "\n", "*No* references. Do not talk about references.\n", "\n", "---\n", "\n", "### Formatting rules for all other sections\n", "\n", "- Top-level sections use `##`.\n", "- Subsections use `###` if needed.\n", "- Except for the Introduction paragraph, items inside sections use bullet points (`-`).\n", "- Separate each section with **two blank lines** (`\\\\n\\\\n`).\n", "- Do **not** include any commentary outside the outline itself.\n", "\n", "Return **only** the final refined markdown outline, nothing else.\n", "\"\"\"\n", "\n", "\n", "\n", "refine_outline_user = \"\"\"\n", "Original Question:\n", "{original_question}\n", "\n", "Uninformed Outline:\n", "{uninformed_outline}\n", "\n", "Informed Outline:\n", "{informed_outline}\n", "\n", "Now generate a refined outline that best fits the interview insights and combines the best structure of both outlines.\n", "\"\"\"\n", "\n", "# 3. Prompt Template\n", "refine_outline_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", refine_outline_system),\n", "    (\"user\", refine_outline_user)\n", "])\n", "\n", "# 4. <PERSON>\n", "# Replace `model` with your actual LLM instance (e.g. ChatOpenAI, ChatAnthropic, etc.)\n", "refine_report_outline_chain = refine_outline_prompt | model.with_structured_output(RefinedReportOutline)"]}, {"cell_type": "markdown", "id": "d642cb30-0175-4d96-9462-a805ae61af4f", "metadata": {}, "source": ["### DATA GAP SECTION OUTLINE GENERATOR"]}, {"cell_type": "code", "execution_count": 70, "id": "7a0daa6b-8358-4e1f-912f-55fc7d99b786", "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "\n", "# 2. Prompt Templates\n", "missing_data_system = \"\"\"\n", "You are a technical report-writing assistant.\n", "\n", "You are given:\n", "- The original guiding question for the report\n", "- A list of Q-A pairs generated from an interview with a system\n", "\n", "Your task is to produce a markdown section titled:\n", "\n", "    ## Data Gaps and Limitations\n", "\n", "List all explicitly stated **missing data or unanswered questions** in the Q-A pairs.\n", "These may appear in answers as:\n", "- \"There is no data available...\"\n", "- \"No results were found...\"\n", "- \"Data was missing for...\"\n", "- \"It was not possible to...\"\n", "\n", "IMPORTANT:\n", "- Do **not** mention how the data was retrieved (e.g., \"SQL query\", \"system\", \"process\", \"model\", \"interview\")\n", "- Do **not** refer to failures or errors (\"returned no results\", \"query failed\")\n", "- Do **not** include implementation terms or debugging context\n", "- Just state plainly what data is missing\n", "\n", "Formatting Rules:\n", "- Start with exactly: `## Data Gaps and Limitations`\n", "- Use a line break, then plain bullets (`-`) for each gap\n", "- Each bullet should begin with the missing topic, e.g.:\n", "    - No data available on [topic]\n", "    - No results regarding [topic]\n", "- No additional explanation, commentary, or framing\n", "- No references or section numbers\n", "\"\"\"\n", "\n", "\n", "missing_data_user = \"\"\"\n", "Original Question:\n", "{original_question}\n", "\n", "Interview Q-A Pairs:\n", "{interview_history}\n", "\n", "Write only the `## Data Gaps and Limitations` section.\n", "\"\"\"\n", "\n", "# 3. Prompt Template\n", "missing_data_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", missing_data_system),\n", "    (\"user\", missing_data_user)\n", "])\n", "\n", "# 4. <PERSON>\n", "missing_data_section_chain = (\n", "    missing_data_prompt\n", "    | model | StrOutputParser()\n", ")\n"]}, {"cell_type": "markdown", "id": "f8528ee1-605d-4f95-a9fd-00f6a8a3f948", "metadata": {}, "source": ["### PRESENTATION TYPE SUGGESTOR"]}, {"cell_type": "code", "execution_count": 72, "id": "7ce77004-b594-40d4-bc6d-d1572eab2be8", "metadata": {}, "outputs": [], "source": ["class PresentationSuggestion(TypedDict):\n", "    presentation_type: Annotated[\n", "        Literal[\"graph\", \"table\", \"text\"],\n", "        \"The best presentation format for the answer: 'graph', 'table', or 'text'\"\n", "    ]\n", "\n", "presentation_decision_system = \"\"\"\n", "You are a helpful data communication assistant.\n", "\n", "You will be given:\n", "- A user's question\n", "- A set of structured data (e.g., rows from a database)\n", "- A proposed natural language answer to the question\n", "\n", "Your task is to decide the best way to visually (or not visually) present the answer:\n", "- Choose \"graph\" if a chart or graph would significantly help illustrate trends, comparisons, or patterns.\n", "- Choose \"table\" if the user would benefit from seeing the raw or tabular data directly.\n", "- Choose \"text\" if the natural language answer is sufficient on its own.\n", "\n", "Only return the best single choice as \"presentation_type\".\n", "\"\"\"\n", "\n", "\n", "presentation_decision_user = \"\"\"\n", "Question:\n", "{question}\n", "\n", "Data:\n", "{data}\n", "\n", "Answer: {answer}\n", "\n", "Based on the above, what is the best way to present this information?\n", "\"\"\"\n", "\n", "\n", "presentation_decision_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", presentation_decision_system),\n", "    (\"user\", presentation_decision_user)\n", "])\n", "\n", "presentation_decision_chain = presentation_decision_prompt | model.with_structured_output(PresentationSuggestion)"]}, {"cell_type": "markdown", "id": "b1b0e63a-9f61-4612-b6e3-6b3cb430b895", "metadata": {}, "source": ["### X_AXIS SELECTOR"]}, {"cell_type": "code", "execution_count": 74, "id": "7225d009-ccb1-4bbd-8e5a-5f98bd30d61c", "metadata": {}, "outputs": [], "source": ["class AxisSuggestion(TypedDict):\n", "    x_axis: str\n", "    \"The best key from the data_item to be used on the x-axis of a graph\"\n", "\n", "\n", "axis_decision_system = \"\"\"\n", "You are a helpful data visualization assistant.\n", "\n", "You will be given:\n", "\n", "* A user's question\n", "* A set of structured data (e.g., a data item from a database result)\n", "\n", "Your task is to decide the best key from the provided data item to be used as the x-axis in case the data were to be visualized in a graph:\n", "\n", "* Choose a key from the data item that best represents an independent variable (something that can be used for the x-axis).\n", "* Make sure the selected key is relevant to the question and would make sense for the x-axis in a graph.\n", "\n", "Only return the best single key that should be used for the x-axis as \"x_axis\".\n", "\"\"\"\n", "\n", "axis_decision_user = \"\"\"\n", "Question:\n", "{question}\n", "\n", "Data Item:\n", "{data_item}\n", "\n", "Based on the above, what is the best key to be used on the x-axis for a graph?\n", "\"\"\"\n", "\n", "axis_decision_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", axis_decision_system),\n", "    (\"user\", axis_decision_user)\n", "])\n", "\n", "axis_decision_chain = axis_decision_prompt | model.with_structured_output(AxisSuggestion)"]}, {"cell_type": "markdown", "id": "51183d92-a166-4b14-b703-32f55a93bfa5", "metadata": {}, "source": ["### SECTION WRITER CHAIN"]}, {"cell_type": "code", "execution_count": 76, "id": "71452b9f-d860-40cb-a274-42bdd2006f0f", "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "\n", "# ─── 1. Retriever (unchanged) ───\n", "retriever = elastic_vector_search.as_retriever(search_kwargs={\"k\": 5})\n", "\n", "section_io: list[dict] = []\n", "\n", "def stash_io(io_dict):\n", "    \"\"\"\n", "    io_dict has keys \"context\" and \"outline\".\n", "    Save them, then pass the dict unchanged.\n", "    \"\"\"\n", "    section_io.append({\"outline\": io_dict[\"outline\"], \"context\": io_dict[\"context\"]})\n", "    return io_dict\n", "    \n", "# ─── 2. Outline → concise query (unchanged) ───\n", "query_prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\",\n", "     \"Rewrite the outline heading below into the shortest possible \"\n", "     \"search query that captures its main topic. \"\n", "     \"Only the phrase—no extra words or punctuation.\"),\n", "    (\"user\", \"{outline_segment}\")\n", "])\n", "\n", "query_builder = (\n", "    query_prompt\n", "    | model                                   # your zero-temp gpt-4o-mini\n", "    | RunnableLambda(lambda msg: msg.content.strip().replace(\"\\n\", \" \"))\n", ")\n", "\n", "# FOR NOT SHOWING FIGURES IN INTRO AND CONCLUSION\n", "section_prompt = ChatPromptTemplate.from_messages([\n", "    (\n", "        \"system\",\n", "        \"You are writing a section of a formal analytical report for institutional stakeholders. \"\n", "        \"Be concise and straight to the point.\\n\"\n", "        \"Inputs you receive:\\n\"\n", "        \"• **Outline segment** – the exact items this section must address.\\n\"\n", "        \"• **Source context** – prior Q-A findings.  Some blocks end with a line:\\n\"\n", "        \"      Data Tag: <tag_name>\\n\"\n", "        \"  where <tag_name> marks a table or graphic that will be inserted later.\\n\\n\"\n", "        \"Guidelines:\\n\"\n", "        \"1. Use only the information provided; do not invent facts.\\n\"\n", "        \"2. You may cite any numeric values already present in the narrative text of an Answer.\\n\"\n", "        \"3. **If the outline segment is an Introduction or a Conclusion, ignore every Data-Tag line and \"\n", "        \"write plain narrative with NO placeholders.**\\n\"\n", "        \"4. Otherwise, when a **Data Tag** line *is* present, finish the sentence and then, on the next line, \"\n", "        \"insert a placeholder exactly like:\\n\"\n", "        \"      [[tag_name]]\\n\"\n", "        \"   Keep quoting any relevant figures from the narrative text; the placeholder is *in addition* to the prose.\\n\"\n", "        \"5. If no tag line is present, simply write the narrative with no placeholder.\\n\"\n", "        \"6. Do not mention tags, placeholders, or processing steps—write as if delivering a finished report.\"\n", "    ),\n", "    (\n", "        \"user\",\n", "        \"### Outline segment\\n{outline}\\n\\n\"\n", "        \"### Source context\\n{context}\\n\\n\"\n", "        \"Draft the complete prose for this section, following the outline and the guidelines above.\"\n", "    ),\n", "])\n", "\n", "\n", "\n", "# ─── 4. join_docs with quote-safe f-string ───\n", "join_docs = RunnableLambda(\n", "    lambda docs: {\n", "        \"context\": \"\\n\\n\".join(\n", "            f\"{d.page_content}\\nData Tag: {d.metadata['data_tag']}\"\n", "            if \"data_tag\" in d.metadata\n", "            else d.page_content\n", "            for d in docs\n", "        )\n", "    }\n", ")\n", "\n", "# ─── 5. FINAL PIPELINE ───\n", "section_writer_chain = (\n", "    {\n", "        \"context\": (\n", "            {\n", "                \"query\":         RunnableLambda(lambda inp: inp[\"outline_segment\"]) | query_builder,\n", "                \"conversation_id\": RunnableLambda(lambda inp: inp[\"conversation_id\"]),\n", "            }\n", "            | RunnableLambda(\n", "                lambda d: elastic_vector_search.as_retriever(search_kwargs={\"k\": 5,\n", "                                                  \"filter\": [\n", "                                                        {\"term\": {\"metadata.data_returned\": True}}\n", "                                                        ,\n", "                                                        {\"term\": {\"metadata.conversation_id.keyword\": question}}\n", "                                                    ]\n", "                                                 }).invoke(d[\"query\"])\n", "\n", "            )\n", "            | join_docs\n", "        ),\n", "        \"outline\": RunnableLambda(lambda inp: inp[\"outline_segment\"]),\n", "    }\n", "    | RunnableLambda(stash_io)\n", "    | section_prompt\n", "    | model | StrOutputParser()                             \n", ")\n"]}, {"cell_type": "code", "execution_count": 77, "id": "f8477af5-742f-4ac8-9e19-6574fae2b570", "metadata": {}, "outputs": [], "source": ["from IPython.display import Markdown, display\n", "\n", "def render_log_markdown(question: str):\n", "    \"\"\"\n", "    Render the logs for a given question as <PERSON><PERSON>.\n", "    \"\"\"\n", "    log_text = logs.get(question)\n", "    if not log_text:\n", "        print(f\"❌ No logs found for question: \\\"{question}\\\"\")\n", "        return\n", "\n", "    # Format each log entry as a bullet point, skipping lines with SQL blocks\n", "    markdown_output = \"\"\n", "    for line in log_text.splitlines():\n", "        if line.strip().startswith(\"```sql\"):\n", "            markdown_output += line + \"\\n\"\n", "        elif \"```\" in line:\n", "            markdown_output += line + \"\\n\"\n", "        elif line.strip().startswith(\"    \"):  # Indented line (e.g., JSON/dict preview)\n", "            markdown_output += f\"\\n```\\n{line.strip()}\\n```\\n\"\n", "        elif line.strip():\n", "            markdown_output += f\"- {line.strip()}\\n\"\n", "\n", "    display(Markdown(markdown_output))\n"]}, {"cell_type": "code", "execution_count": 78, "id": "a8d21384-c675-472a-8c95-31f22fece70f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["(list(logs.keys()))"]}, {"cell_type": "code", "execution_count": 79, "id": "7f6ee71a-a3d1-423c-b0f0-340bc480a881", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❌ No logs found for question: \"How has the fee debt at the institution where students owe the most fees changed over the past few years?\"\n"]}], "source": ["render_log_markdown('How has the fee debt at the institution where students owe the most fees changed over the past few years?')"]}, {"cell_type": "markdown", "id": "e61caef8-d804-43a0-adea-1cfa7585104d", "metadata": {}, "source": ["### DEEP RESEARCH V1"]}, {"cell_type": "markdown", "id": "7aece647-28af-4177-be3c-e6217f661290", "metadata": {}, "source": ["#### INTERVIEW LOOP CHAIN"]}, {"cell_type": "code", "execution_count": 82, "id": "5598a4b3-f207-4627-8e90-2b2814eeb1d8", "metadata": {}, "outputs": [], "source": ["def db_expert(question):\n", "    sql_results = (sql_gen_veri_chain | sql_runner_chain).invoke(question)\n", "    answer = sql_answerer_chain.invoke({\n", "        \"question\": question, \"result\": sql_results[\"result\"]\n", "    })\n", "    \n", "    return {\n", "        \"question\": question,\n", "        \"answer\": answer[\"answer\"],\n", "        \"sql\": sql_results.get(\"sql\", None),\n", "        \"data\": sql_results[\"result\"],\n", "        \"data_tag\": answer[\"data_tag\"]\n", "    }\n", "\n", "db_expert_chain = RunnableLambda(db_expert)"]}, {"cell_type": "markdown", "id": "2dc190df-6814-4304-8d5a-067867ef30e2", "metadata": {}, "source": ["##### Data tags and report generation\n", "\n", "For the report generator prompt, it would have to understand that the data tags are a place holder for a graph or something so that it includes them in the generation with an appropriate marking or something like  {{--data-tag-name--}} so that it can be swapped in, or something like that. Would probably not be swapped in as a string but the entire thing would probably be an array of dicts or strings. Still brainstorming.."]}, {"cell_type": "code", "execution_count": 84, "id": "65e2d5b5-7fc4-413a-9a83-c93b3ff6f221", "metadata": {"scrolled": true}, "outputs": [], "source": ["# db_expert.invoke(question)"]}, {"cell_type": "code", "execution_count": 85, "id": "9a3ab109-6f07-4966-9104-c35e9c0007f6", "metadata": {}, "outputs": [], "source": ["from langchain_core.runnables import RunnableLambda\n", "from datetime import datetime, UTC\n", "\n", "def interview(initial_question: str):\n", "    \"\"\"\n", "    Conducts up to 5 Q-A turns.\n", "    Returns:\n", "        {\n", "          \"convo\":               [ {\"question\": str, \"answer\": str}, ... ],\n", "          \"convo_with_data\":     [ {\"question\": str, \"answer\": dict}, ... ],\n", "          \"data\":                { tag: any, ... }\n", "        }\n", "    \"\"\"\n", "    turns: list[dict] = []                 # plain answer text\n", "    turns_with_data: list[dict] = []       # full answer object\n", "    data_dict: dict = {}\n", "\n", "    follow_up = initial_question\n", "\n", "    for i in range(5):\n", "        # print(f\"\\n\\nQUESTION {i+1}\\n\")\n", "\n", "        # ----- Expert answers -----\n", "        answer_obj = db_expert_chain.invoke(follow_up)\n", "        # print(\"=\" * 49)\n", "        # print(answer_obj)\n", "        # print(\"=\" * 49)\n", "\n", "        presentation_suggestion = presentation_decision_chain.invoke(answer_obj)\n", "        presentation_type = presentation_suggestion[\"presentation_type\"]\n", "\n", "        # Handle “no results”\n", "        # if answer_obj.get(\"data\") == \"No results\":\n", "        #     print(\"Skipping turn because no data returned.\")\n", "        # else:\n", "        #     # Store the successful turn\n", "        #     turns.append({\n", "        #         \"question\": follow_up,\n", "        #         \"answer\":   answer_obj[\"answer\"]\n", "        #     })\n", "        #     turns_with_data.append({\n", "        #         \"question\": follow_up,\n", "        #         \"answer\":   answer_obj           # full object\n", "        #     })\n", "        #     # Collect structured data\n", "        #     data_dict[answer_obj[\"data_tag\"]] = answer_obj[\"data\"]\n", "\n", "        # Store the successful turn\n", "        turns.append({\n", "            \"question\": follow_up,\n", "            \"answer\":   answer_obj[\"answer\"]\n", "        })\n", "        turns_with_data.append({\n", "            **answer_obj,\n", "            \"presentation_type\": presentation_type,\n", "            \"question\": follow_up,\n", "        })\n", "\n", "        # Collect structured data\n", "        data_dict[answer_obj[\"data_tag\"]] = {\n", "            \"presentation_type\": presentation_type,\n", "            \"data\": answer_obj[\"data\"]\n", "        } \n", "\n", "        print(f\"\\n\\nAnswer OBJ: {answer_obj}\\n\\n\")\n", "\n", "        if presentation_type.lower() == \"graph\" and isinstance(answer_obj[\"data\"], list) and len(answer_obj[\"data\"]) > 0:\n", "            axis_decision = axis_decision_chain.invoke({\n", "                \"question\": follow_up,\n", "                \"data_item\": answer_obj[\"data\"][0]\n", "            })\n", "            \n", "            data_dict[answer_obj[\"data_tag\"]][\"x_axis_key\"] = axis_decision[\"x_axis\"]    \n", "        \n", "\n", "        # ----- Ask the questioner model for the next follow-up -----\n", "        try:\n", "            questioner_output = interview_questioner.invoke({\n", "                \"topic\":   initial_question,\n", "                \"history\": turns                 # pass the concise history\n", "            })\n", "        except Exception as e:\n", "            print(\"Error from interview_questioner:\")\n", "            print(e)\n", "            break                                # stop the interview loop\n", "\n", "        follow_up = questioner_output.get(\"follow_up_question\", \"No further questions\")\n", "        # print(\"=\" * 49)\n", "        # print(follow_up)\n", "        # print(\"=\" * 49)\n", "\n", "        if follow_up == \"No further questions\":\n", "            break\n", "\n", "    # print(turns_with_data)\n", "    # print(turns)\n", "    # ---------- return ----------\n", "    return {\n", "        \"convo_id\": initial_question,\n", "        \"convo\": turns,\n", "        \"convo_with_data\": turns_with_data,\n", "        \"data\": data_dict,\n", "        \"timestamp\": datetime.now(UTC).isoformat()\n", "    }\n", "\n", "# Wrap it in a RunnableLambda so you can pipe it in a LangChain flow\n", "interview_chain = RunnableLambda(interview)"]}, {"cell_type": "code", "execution_count": 86, "id": "1dfa7989-5e00-4a80-9fdf-1facb85235ff", "metadata": {"scrolled": true}, "outputs": [], "source": ["# interview = interview_chain.invoke(\"Which institution has the most students?\")"]}, {"cell_type": "code", "execution_count": 87, "id": "02d1efd0-b77d-4282-b6e7-f726a12b3f00", "metadata": {}, "outputs": [], "source": ["# convo"]}, {"cell_type": "code", "execution_count": 88, "id": "dbde0b52-e4f9-4e0c-83be-37ddc8ba3b11", "metadata": {}, "outputs": [], "source": ["def batch_interview(questions_list):\n", "    interview_responses = interview_chain.batch(questions_list)\n", "    return {'interviews': interview_responses}\n", "\n", "batch_interview_chain = RunnableLambda(batch_interview)"]}, {"cell_type": "code", "execution_count": 89, "id": "efe73b17-aaea-4982-a637-6a2a3e0a8295", "metadata": {}, "outputs": [], "source": ["from typing import List, Dict\n", "\n", "def interview_to_docs(\n", "    result: Dict,  original_question: str                    # payload returned by interview_chain\n", ") -> List[Document]:\n", "    \"\"\"\n", "    Turns the interview output into a list of Document objects ready for upsert.\n", "    Each turn → one vector (Question + Answer text) + metadata.\n", "    \"\"\"\n", "    docs: List[Document] = []\n", "\n", "    print(\"CONVERTING TO VECTOR STORE DOCS\\n\")\n", "    for idx, turn in enumerate(result[\"convo_with_data\"]):\n", "        # q, a = turn[\"question\"], turn[\"answer\"]\n", "        q, a, pt, d, dt = turn[\"question\"], turn[\"answer\"], turn[\"presentation_type\"], turn[\"data\"], turn[\"data_tag\"]\n", "\n", "        page_content = f\"Question: {q}\\nAnswer: {a}\"\n", "\n", "        metadata = {\n", "            \"conversation_id\": original_question,\n", "            \"turn_index\": idx,\n", "            \"partition\": \"interview\",\n", "            # \"question\": q,\n", "            # \"answer\": a,\n", "            \"timestamp\": result[\"timestamp\"],\n", "        }\n", "\n", "        \n", "        if d != \"No results\":\n", "            print(\"\\n Checking supposed non empty data\")\n", "            print(d)\n", "            print(\"================================= \\n\")\n", "            metadata[\"data_returned\"] = True\n", "            \n", "            if pt != \"text\":\n", "                metadata[\"data_tag\"] = dt\n", "                # print(f\"\\n{idx}. {d}\")\n", "                # print(f\"{pt}: {dt}\\n\")\n", "        else:\n", "            metadata[\"data_returned\"] = False\n", "\n", "        docs.append(Document(page_content=page_content, metadata=metadata))\n", "\n", "    return docs\n", "\n", "\n", "def upsert_docs(docs: list[Document], store: ElasticsearchStore):\n", "    \"\"\"\n", "    If a doc with the same ID already exists, it is overwritten.\n", "    \"\"\"\n", "    ids = [\n", "        f\"{doc.metadata['conversation_id']}_{doc.metadata['timestamp']}_{doc.metadata['turn_index']}\"\n", "        for doc in docs\n", "    ]\n", "    store.add_documents(docs, ids=ids)       # bulk upsert"]}, {"cell_type": "code", "execution_count": 90, "id": "40e5f7ee-6219-4154-8f0a-1d813688e20b", "metadata": {}, "outputs": [], "source": ["from typing import List, Dict, Any\n", "\n", "def extract_q_a_with_data(interviews_obj: Dict[str, Any]) -> List[Dict[str, str]]:\n", "    \"\"\"\n", "    From the batch-interview output, return a flat list of\n", "    {\"question\": ..., \"answer\": ...} where turn[\"data\"] != \"No results\".\n", "    \"\"\"\n", "    keep: List[Dict[str, str]] = []\n", "\n", "    # interviews_obj[\"interviews\"] is the list produced by batch_interview\n", "    for interview in interviews_obj.get(\"interviews\", []):\n", "        for turn in interview.get(\"convo_with_data\", []):\n", "            if turn.get(\"data\") != \"No results\":\n", "                keep.append({\n", "                    \"question\": turn[\"question\"],\n", "                    \"answer\":   turn[\"answer\"],\n", "                })\n", "\n", "    return keep\n", "\n", "def extract_q_a_without_data(interviews_obj: Dict[str, Any]) -> List[Dict[str, str]]:\n", "    \"\"\"\n", "    From the batch-interview output, return a flat list of\n", "    {\"question\": ..., \"answer\": ...} where turn[\"data\"] != \"No results\".\n", "    \"\"\"\n", "    keep: List[Dict[str, str]] = []\n", "\n", "    # interviews_obj[\"interviews\"] is the list produced by batch_interview\n", "    for interview in interviews_obj.get(\"interviews\", []):\n", "        for turn in interview.get(\"convo_with_data\", []):\n", "            if turn.get(\"data\") == \"No results\":\n", "                keep.append({\n", "                    \"question\": turn[\"question\"],\n", "                    \"answer\":   turn[\"answer\"],\n", "                })\n", "\n", "    return keep"]}, {"cell_type": "code", "execution_count": 91, "id": "bca8ce45-f9a8-4c45-98d1-2578e0a1a7c1", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "def split_markdown_sections(markdown_text):\n", "    # Find all level 2 sections (## headings) and split the content based on them\n", "    # This regex captures the heading as well as the content that follows it\n", "    pattern = r\"(##\\s.*?)(?=\\n##\\s|\\Z)\"  # Match '## ' to next '## ' or end of string\n", "    sections = re.findall(pattern, markdown_text, re.DOTALL)\n", "    return [section.strip() for section in sections]\n", "\n", "def interviews_to_report(question_schema_dict):\n", "\n", "    original_question = question_schema_dict[\"question\"]\n", "    uninformed_outline = report_outline_chain.invoke({\n", "        \"original_question\": original_question\n", "    })\n", "        \n", "    output = expanded_questions_chain.invoke({\n", "        \"schema_text\":  question_schema_dict[\"schema_text\"],\n", "        \"question\": original_question,\n", "        \"feedback\": \"\"\n", "    })\n", "\n", "    questions_list = batch_question_verification_chain.invoke(output)\n", "    \n", "    interviews = batch_interview(questions_list)\n", "    data_backed_convos = extract_q_a_with_data(interviews)\n", "    data_lacking_convos = extract_q_a_without_data(interviews)\n", "    \n", "    informed_outline = informed_report_outline_chain.invoke({\n", "        'original_question': original_question,\n", "        'interview_histories': data_backed_convos\n", "    })\n", "\n", "    refined_outline = refine_report_outline_chain.invoke({\n", "        \"informed_outline\": informed_outline, \"uninformed_outline\": uninformed_outline, \"original_question\": original_question\n", "    })\n", "\n", "    interviews = [ interview for interview in interviews[\"interviews\"] if len(interview[\"convo\"]) > 0 ]\n", "    data_dict_list = [interview[\"data\"] for interview in interviews]\n", "    \n", "    extended_data = {}\n", "    for data_dict in data_dict_list:\n", "        extended_data.update(data_dict)\n", "\n", "    all_docs: List[Document] = []\n", "    for interview in interviews:\n", "        all_docs.extend(interview_to_docs(interview, original_question))\n", "\n", "    # print(\"All Documents to be inserted\")\n", "    # print(all_docs)\n", "    \n", "    upsert_docs(all_docs, elastic_vector_search) \n", "\n", "    outline_sections = split_markdown_sections(refined_outline[\"outline\"])\n", "\n", "    report_sections = section_writer_chain.batch([{\"outline_segment\": outline_section, \"conversation_id\": original_question} \n", "                                                  for outline_section in outline_sections])\n", "\n", "    if include_data_gaps:\n", "        missing_data_section = missing_data_section_chain.invoke({\n", "            \"original_question\": original_question,\n", "            \"interview_history\": data_lacking_convos\n", "        })\n", "    \n", "        report_sections.append(missing_data_section)\n", "    \n", "    return report_sections, extended_data, data_backed_convos, informed_outline, refined_outline"]}, {"cell_type": "code", "execution_count": 92, "id": "993a1fdb-5c3c-4c6a-9e66-8c6cd959d864", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443\n", "DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443\n", "DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443\n", "DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443\n", "DEBUG:urllib3.connectionpool:https://m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443 \"POST /Prod/execute-query HTTP/1.1\" 200 116\n", "DEBUG:urllib3.connectionpool:https://m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443 \"POST /Prod/execute-query HTTP/1.1\" 200 274\n", "\n", "\n", "Answer OBJ: {'question': 'What is the total number of students enrolled at the institution with the most students?', 'answer': 'The total number of students enrolled at the institution with the most students is 192,627.', 'sql': 'SELECT institution_id, COUNT(*) AS total_students\\nFROM core.students\\nGROUP BY institution_id\\nORDER BY total_students DESC\\nLIMIT 1;', 'data': [{'institution_id': 24, 'total_students': 192627}], 'data_tag': 'total_students_at_largest_institution'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What are the admission criteria for the institution with the most students?', 'answer': \"The admission criteria for the institution with the most students include the following application forms: 1) the 'UEW UNDERGRADUATE APPLICATION FORM 2022/2023', which is currently active and available since September 2022 until July 2026, and 2) the 'UEW Postgraduate forms', also active from September 2022 to July 2026. These forms outline the necessary steps and requirements for prospective undergraduate and postgraduate students to apply for admission.\", 'sql': 'SELECT af.description, ab.doa, ab.doc, ab.status \\nFROM admission_forms af \\nJOIN admission_batches ab ON af.admission_batch_id = ab.id \\nJOIN (SELECT institution_id, COUNT(*) as student_count \\n      FROM applicants \\n      GROUP BY institution_id \\n      ORDER BY student_count DESC \\n      LIMIT 1) as most_students \\nON af.institution_id = most_students.institution_id;', 'data': [{'description': 'UEW UNDERGRADUATE APPLICATION FORM 2022/2023', 'doa': 'SEP2022', 'doc': 'JUL2026', 'status': 'Active'}, {'description': 'UEW Postgraduate forms', 'doa': 'SEP2022', 'doc': 'JUL2026', 'status': 'Active'}], 'data_tag': 'admission_criteria_for_most_students_institution'}\n", "\n", "\n", "DEBUG:urllib3.connectionpool:https://m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443 \"POST /Prod/execute-query HTTP/1.1\" 200 170\n", "\n", "\n", "Answer OBJ: {'question': 'What is the demographic breakdown of students at the institution with the most students?', 'answer': 'The institution with the most students has a total of 164,622 students, broken down by gender as follows: 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total). There are also 8,205 students whose gender is not specified.', 'sql': 'SELECT s.sex, COUNT(*) AS student_count\\nFROM core.students s\\nJOIN core.institutions i ON s.institution_id = i.id\\nWHERE i.id = (SELECT institution_id FROM core.students GROUP BY institution_id ORDER BY COUNT(*) DESC LIMIT 1)\\nGROUP BY s.sex;', 'data': [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}], 'data_tag': 'student_demographics_by_gender'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answer': 'The SQL query did not return any results, which suggests that there may not be specific data available to analyze the factors contributing to high enrollment numbers at this institution. This could be due to a lack of relevant data in the database or the need for a different query to capture the necessary information. To better understand the factors influencing enrollment, it may be helpful to consider aspects such as marketing efforts, academic programs offered, campus facilities, student support services, and community engagement initiatives.', 'sql': None, 'data': 'No results', 'data_tag': 'enrollment_factors_analysis'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'Can you elaborate on the specific eligibility requirements or qualifications that applicants must meet for both undergraduate and postgraduate programs at this institution?', 'answer': \"Unfortunately, there are no specific eligibility requirements or qualifications available for undergraduate and postgraduate programs at this institution in the current dataset. It may be beneficial to check the institution's official website or contact their admissions office for the most accurate and detailed information regarding program eligibility.\", 'sql': None, 'data': 'No results', 'data_tag': 'program_eligibility_requirements'}\n", "\n", "\n", "DEBUG:urllib3.connectionpool:https://m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443 \"POST /Prod/execute-query HTTP/1.1\" 200 95\n", "\n", "\n", "Answer OBJ: {'question': 'What is the retention rate of students at the institution with the most students?', 'answer': 'The retention rate of students at the institution with the most students is 99.49%. This indicates a very high level of student satisfaction and success in continuing their education at this institution.', 'sql': 'WITH student_counts AS (SELECT institution_id, COUNT(*) AS total_students FROM core.students GROUP BY institution_id), max_institution AS (SELECT institution_id FROM student_counts ORDER BY total_students DESC LIMIT 1) SELECT COUNT(DISTINCT s.id) * 100.0 / COUNT(s.id) AS retention_rate FROM core.students s JOIN core.student_programs sp ON s.id = sp.student_id WHERE s.institution_id = (SELECT institution_id FROM max_institution) AND sp.current = TRUE;', 'data': [{'retention_rate': 99.49}], 'data_tag': 'retention_rate_highest_enrollment_institution'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'How does the demographic breakdown of this institution compare to national averages or trends in higher education?', 'answer': 'Unfortunately, there are no available results to provide a demographic breakdown of this institution. Without this data, we cannot compare it to national averages or trends in higher education. It may be beneficial to gather demographic data from relevant sources to facilitate such a comparison in the future.', 'sql': None, 'data': 'No results', 'data_tag': 'institution_demographic_data'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'Given the lack of specific data on factors contributing to high enrollment, what general trends or strategies have you observed in other large institutions that might explain their success in attracting students?', 'answer': 'Unfortunately, there are no specific data results available to analyze trends or strategies that contribute to high enrollment in large institutions. However, based on general observations in the field, successful institutions often employ a combination of strategies such as enhancing their marketing efforts, improving student support services, offering competitive financial aid packages, and developing strong community partnerships. Additionally, focusing on academic excellence, diverse program offerings, and creating a vibrant campus culture can also attract more students. Without specific data, these general trends can serve as a guideline for understanding factors that may influence enrollment success.', 'sql': None, 'data': 'No results', 'data_tag': 'enrollment_strategies_observed_in_large_institutions'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What are some common challenges that applicants face during the admission process at this institution, and how can they effectively overcome them?', 'answer': 'Unfortunately, there are no specific data results available regarding the common challenges that applicants face during the admission process at this institution. However, generally, applicants often encounter challenges such as understanding the application requirements, meeting deadlines, preparing for interviews, and gathering necessary documentation. To effectively overcome these challenges, applicants can take the following steps: thoroughly research the admission process, create a checklist of requirements, reach out to admissions counselors for clarification, and seek advice from current students or alumni. Additionally, time management and early preparation can significantly alleviate stress during the application period.', 'sql': None, 'data': 'No results', 'data_tag': 'applicant_challenges_admission_process'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What specific programs or initiatives does this institution implement to achieve such a high retention rate?', 'answer': 'Unfortunately, there are no specific programs or initiatives listed in the data that explain how this institution achieves its high retention rate. It may be beneficial to consult additional resources or institutional reports for more detailed information on their retention strategies.', 'sql': None, 'data': 'No results', 'data_tag': 'retention_initiatives_info'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What specific factors do you think contribute to the gender distribution observed at this institution, and how might these factors differ from those at other institutions?', 'answer': 'The SQL query did not return any results, which suggests that there may not be sufficient data available to analyze the specific factors contributing to the gender distribution at this institution. Without data, it is challenging to identify or compare these factors with those at other institutions. To gain insights, it may be necessary to gather more comprehensive data on enrollment, retention, and other demographic factors that could influence gender distribution.', 'sql': None, 'data': 'No results', 'data_tag': 'gender_distribution_factors_analysis'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What role do online education and technology play in influencing enrollment numbers at large institutions?', 'answer': 'The SQL query did not return any results, which suggests that there may not be sufficient data available to analyze the influence of online education and technology on enrollment numbers at large institutions. This could indicate that either the relevant data has not been collected or that the specific parameters of the query did not match any existing records. Therefore, we cannot draw any conclusions regarding the role of online education and technology in this context.', 'sql': None, 'data': 'No results', 'data_tag': 'enrollment_data_availability'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What resources or support systems does the institution provide to assist applicants in navigating the admission process?', 'answer': \"The SQL query returned no results, indicating that there are currently no documented resources or support systems provided by the institution to assist applicants in navigating the admission process. It may be beneficial to check directly with the admissions office or the institution's website for any available support options.\", 'sql': None, 'data': 'No results', 'data_tag': 'admission_support_resources'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What factors do you think contribute to the overall student satisfaction at this institution, given the high retention rate?', 'answer': 'The SQL query did not return any results, which suggests that there may not be specific data available to directly analyze the factors contributing to overall student satisfaction at this institution. However, high retention rates typically indicate that students are satisfied with their experience, which could be influenced by various factors such as quality of teaching, campus facilities, student support services, and social opportunities. To gain a better understanding, it may be beneficial to conduct surveys or gather qualitative data from students regarding their experiences.', 'sql': None, 'data': 'No results', 'data_tag': 'student_satisfaction_factors'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What additional demographic factors, such as ethnicity or socioeconomic status, would be important to consider when analyzing the student population at this institution, and why?', 'answer': 'The SQL query did not return any results, indicating that there may not be available data on demographic factors such as ethnicity or socioeconomic status for the student population at this institution. However, these factors are crucial for a comprehensive analysis of the student body. Ethnicity can provide insights into diversity and representation, while socioeconomic status can highlight access to resources and support systems. Understanding these demographics can help in tailoring programs and initiatives to better serve the student population and address any disparities that may exist.', 'sql': None, 'data': 'No results', 'data_tag': 'missing_demographic_data_analysis'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': \"In the absence of specific data on online education's impact, what anecdotal evidence or case studies can you share that illustrate how large institutions have successfully integrated technology to boost enrollment?\", 'answer': 'Unfortunately, there are no specific data results available to provide anecdotal evidence or case studies regarding how large institutions have successfully integrated technology to boost enrollment. However, it is widely recognized that many universities have adopted online learning platforms, enhanced their digital marketing strategies, and utilized data analytics to better understand student needs and preferences. These initiatives often lead to increased enrollment by making education more accessible and appealing to a broader audience.', 'sql': None, 'data': 'No results', 'data_tag': 'anecdotal_evidence_online_education_integration'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What strategies do successful applicants typically employ to enhance their chances of admission at this institution, given the lack of documented resources?', 'answer': 'Unfortunately, there are no documented resources or data available regarding the strategies employed by successful applicants at this institution. This lack of information makes it difficult to provide specific insights into the practices that may enhance admission chances. It may be beneficial to seek advice from current students or alumni, or to explore general best practices for college admissions.', 'sql': None, 'data': 'No results', 'data_tag': 'applicant_strategies_for_admission'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What methods or approaches could be used to gather qualitative data from students to better understand their experiences and satisfaction at this institution?', 'answer': 'It appears that there are currently no specific methods or approaches documented in the database for gathering qualitative data from students regarding their experiences and satisfaction at this institution. However, common methods to consider include conducting focus groups, one-on-one interviews, distributing open-ended surveys, and utilizing feedback forms. These approaches can provide valuable insights into student perspectives and help improve their overall experience.', 'sql': None, 'data': 'No results', 'data_tag': 'qualitative_data_collection_methods'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'Given the lack of available data on ethnicity and socioeconomic status, what strategies could the institution implement to collect this information effectively and ensure it is representative of the student population?', 'answer': \"The SQL query did not return any results, indicating that there may be no existing data on ethnicity and socioeconomic status within the institution's database. To effectively collect this information and ensure it is representative of the student population, the institution could consider the following strategies: 1. **Surveys and Questionnaires**: Implement anonymous surveys during the enrollment process or at the beginning of each academic year to gather demographic information. 2. **Partnerships with Community Organizations**: Collaborate with local community organizations that serve diverse populations to reach underrepresented groups. 3. **Incentives for Participation**: Offer incentives for students to complete demographic surveys, such as entry into a raffle or small rewards. 4. **Focus Groups**: Conduct focus groups with students to discuss the importance of providing this information and how it can benefit the student body. 5. **Data Privacy Assurance**: Clearly communicate how the data will be used and ensure that it will be kept confidential to encourage participation. By implementing these strategies, the institution can work towards collecting comprehensive and representative data on ethnicity and socioeconomic status.\", 'sql': None, 'data': 'No results', 'data_tag': 'strategies_for_collecting_demographic_data'}\n", "\n", "\n", "DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443\n", "DEBUG:urllib3.connectionpool:https://m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443 \"POST /Prod/execute-query HTTP/1.1\" 502 36\n", "\n", "\n", "Answer OBJ: {'question': 'How does the institution with the most students compare to others in terms of student-to-faculty ratio?', 'answer': 'It appears that there are no results available for the student-to-faculty ratio of the institution with the most students. This could mean that the data is either not present or not accessible at this time. Therefore, I cannot provide a comparison of the student-to-faculty ratio for this institution against others.', 'sql': 'WITH StudentCounts AS (\\n    SELECT institution_id, COUNT(*) AS student_count\\n    FROM core.students\\n    GROUP BY institution_id\\n), FacultyCounts AS (\\n    SELECT institution_id, COUNT(*) AS faculty_count\\n    FROM core.staff\\n    WHERE designation_id IN (SELECT id FROM core.staff_designations WHERE is_faculty = TRUE)\\n    GROUP BY institution_id\\n), Ratios AS (\\n    SELECT sc.institution_id,\\n           sc.student_count,\\n           COALESCE(fc.faculty_count, 0) AS faculty_count,\\n           CASE WHEN COALESCE(fc.faculty_count, 0) = 0 THEN NULL\\n                ELSE CAST(sc.student_count AS FLOAT) / fc.faculty_count END AS student_to_faculty_ratio\\n    FROM StudentCounts sc\\n    LEFT JOIN FacultyCounts fc ON sc.institution_id = fc.institution_id\\n), MaxInstitution AS (\\n    SELECT institution_id\\n    FROM Ratios\\n    ORDER BY student_count DESC\\n    LIMIT 1\\n)\\nSELECT r.institution_id, r.student_count, r.faculty_count, r.student_to_faculty_ratio\\nFROM Ratios r\\nJOIN MaxInstitution mi ON r.institution_id = mi.institution_id\\nUNION ALL\\nSELECT r.institution_id, r.student_count, r.faculty_count, r.student_to_faculty_ratio\\nFROM Ratios r\\nWHERE r.institution_id != (SELECT institution_id FROM MaxInstitution);', 'data': 'No results', 'data_tag': 'student_faculty_ratio_data_availability'}\n", "\n", "\n", "DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443\n", "\n", "\n", "Answer OBJ: {'question': 'What challenges might the institution face in implementing these qualitative data collection methods, and how could they address them?', 'answer': 'The SQL query returned no results, indicating that there may not be any specific data available regarding the challenges the institution might face in implementing qualitative data collection methods. However, generally, institutions may encounter challenges such as resistance to change from staff, lack of training on qualitative methods, and difficulties in ensuring participant engagement. To address these challenges, the institution could provide comprehensive training sessions, involve staff in the planning process to gain their buy-in, and develop strategies to encourage participant involvement, such as offering incentives or ensuring that the data collection process is user-friendly.', 'sql': None, 'data': 'No results', 'data_tag': 'qualitative_data_collection_challenges'}\n", "\n", "\n", "DEBUG:urllib3.connectionpool:https://m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443 \"POST /Prod/execute-query HTTP/1.1\" 200 61\n", "\n", "\n", "Answer OBJ: {'question': 'What factors do you think contribute to the variation in student-to-faculty ratios among different institutions?', 'answer': \"The SQL query did not return any results, which suggests that there may not be sufficient data available to analyze the factors contributing to the variation in student-to-faculty ratios among different institutions. Without specific data points, it's challenging to identify the exact factors. However, generally, factors that could influence these ratios include the size of the institution, funding levels, academic programs offered, and institutional policies regarding faculty hiring. Further data collection and analysis would be necessary to draw any concrete conclusions.\", 'sql': None, 'data': 'No results', 'data_tag': 'student_faculty_ratio_factors'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'How has the student enrollment at the institution with the most students changed over the past few years?', 'answer': 'It appears that there are no available results regarding the changes in student enrollment at the institution with the most students over the past few years. This could indicate that either the data is not recorded or there have been no significant changes to report during that time period.', 'sql': 'WITH InstitutionStudentCount AS (  SELECT institution_id, COUNT(*) AS student_count  FROM core.students  GROUP BY institution_id), MaxInstitution AS (  SELECT institution_id  FROM InstitutionStudentCount  ORDER BY student_count DESC  LIMIT 1) SELECT academic_years.start_year, COUNT(student_programs.student_id) AS enrollment_count FROM core.student_programs JOIN core.academic_years ON core.student_programs.created_at BETWEEN academic_years.start_year AND academic_years.end_year WHERE core.student_programs.institution_id = (SELECT institution_id FROM MaxInstitution) GROUP BY academic_years.start_year ORDER BY academic_years.start_year;', 'data': 'No results', 'data_tag': 'student_enrollment_trends_most_students'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answer': \"While we don't have specific data to analyze student enrollment trends at large institutions, several factors could influence these trends. These may include economic conditions, changes in tuition fees, the availability of financial aid, demographic shifts, the reputation of the institution, program offerings, and job market demands. Additionally, external factors such as global events, technological advancements, and social trends can also play a significant role in shaping enrollment patterns.\", 'sql': None, 'data': 'No results', 'data_tag': 'potential_enrollment_influencers'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'Given the challenges in obtaining data on student-to-faculty ratios, what methodologies or approaches do you think could be employed to gather more reliable information on this topic?', 'answer': 'Unfortunately, there are no results available regarding student-to-faculty ratios, which indicates that data on this topic may not be readily accessible or collected in a standardized manner. To gather more reliable information on student-to-faculty ratios, several methodologies could be employed: 1. **Surveys and Questionnaires**: Institutions could conduct regular surveys to collect data directly from students and faculty about their experiences and perceptions of the ratio. 2. **Data Standardization**: Establishing a standardized framework for data collection across institutions can help ensure consistency and comparability. 3. **Collaboration with Educational Bodies**: Partnering with educational organizations or government bodies can facilitate access to aggregated data. 4. **Utilizing Technology**: Implementing data management systems that track enrollment and faculty numbers in real-time can improve data accuracy. 5. **Longitudinal Studies**: Conducting studies over time can help identify trends and provide a more comprehensive view of the student-to-faculty ratio dynamics. By employing these approaches, institutions can work towards obtaining more reliable and actionable data on student-to-faculty ratios.', 'sql': None, 'data': 'No results', 'data_tag': 'student_to_faculty_ratio_methodologies'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'Given the various factors that can influence student enrollment trends, how do you think institutions can proactively adapt their strategies to attract and retain students in a changing environment?', 'answer': 'The SQL query did not return any results, which suggests that there may not be specific data available at this time to analyze student enrollment trends. However, institutions can proactively adapt their strategies to attract and retain students by focusing on several key areas: enhancing the quality of academic programs, improving student support services, leveraging technology for online learning, and actively engaging with prospective students through targeted marketing campaigns. Additionally, institutions should consider conducting surveys or gathering data on student preferences and market trends to better understand the factors influencing enrollment. This proactive approach can help institutions remain competitive and responsive to the changing educational landscape.', 'sql': None, 'data': 'No results', 'data_tag': 'student_enrollment_trends_analysis'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What role do you think technology plays in influencing the student-to-faculty ratio, particularly in large institutions, and how might it change in the future?', 'answer': 'The SQL query did not return any results, which suggests that there may not be specific data available regarding the influence of technology on the student-to-faculty ratio in large institutions. However, we can infer that technology plays a significant role in shaping educational environments. For instance, advancements in online learning platforms and educational technologies can allow institutions to accommodate larger student populations without a proportional increase in faculty. This could lead to a higher student-to-faculty ratio. In the future, as technology continues to evolve, we might see even more innovative solutions that could either mitigate or exacerbate this ratio, depending on how institutions choose to implement these technologies. Therefore, while the current data does not provide specific insights, the potential impact of technology on this ratio is an important area for further exploration.', 'sql': None, 'data': 'No results', 'data_tag': 'student_faculty_ratio_influence_by_technology'}\n", "\n", "\n", "DEBUG:urllib3.connectionpool:Starting new HTTPS connection (1): m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443\n", "\n", "\n", "Answer OBJ: {'question': 'What specific strategies or initiatives have been most effective for institutions in enhancing student support services and how can these be measured for success?', 'answer': 'Unfortunately, there are no specific results available regarding the strategies or initiatives that institutions have implemented to enhance student support services. This could indicate that either the data has not been collected or analyzed yet, or that the existing data does not highlight effective strategies. To measure the success of such initiatives, institutions typically look at metrics such as student satisfaction surveys, retention rates, and academic performance indicators. Without specific data, it is challenging to identify which strategies have been most effective.', 'sql': None, 'data': 'No results', 'data_tag': 'student_support_service_strategies'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'How do you think the increasing reliance on technology in education might affect the quality of student-faculty interactions and overall learning outcomes?', 'answer': 'The SQL query did not return any results, which suggests that there may not be specific data available to directly analyze the impact of increasing reliance on technology in education on student-faculty interactions and overall learning outcomes. However, it is widely recognized that technology can enhance communication and accessibility, potentially improving interactions. On the other hand, excessive reliance on technology might lead to reduced personal engagement, which could negatively affect learning outcomes. Therefore, while the relationship is complex and context-dependent, the absence of data limits our ability to draw definitive conclusions.', 'sql': None, 'data': 'No results', 'data_tag': 'impact_of_technology_on_student_faculty_interactions'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What role do you think technology plays in shaping student enrollment trends, particularly in terms of online learning and digital engagement?', 'answer': 'The SQL query did not return any results, which suggests that there may not be available data on student enrollment trends related to technology, online learning, or digital engagement at this time. This lack of data makes it difficult to draw specific conclusions about the role of technology in shaping these trends. However, it is widely recognized that technology has a significant impact on student enrollment, as online learning platforms and digital engagement tools can enhance accessibility and flexibility for students, potentially influencing their decisions to enroll in various programs.', 'sql': None, 'data': 'No results', 'data_tag': 'student_enrollment_trends_technology'}\n", "\n", "\n", "DEBUG:urllib3.connectionpool:https://m4s3l32ltf.execute-api.eu-west-1.amazonaws.com:443 \"POST /Prod/execute-query HTTP/1.1\" 504 41\n", "\n", "\n", "Answer OBJ: {'question': 'What programs or courses are most popular at the institution with the most students?', 'answer': 'It appears that there are no results available for the programs or courses at the institution with the most students. This could mean that either the data is not present or that the institution does not have any recorded programs or courses. Therefore, we cannot determine which programs or courses are the most popular at that institution.', 'sql': \"WITH StudentCounts AS (  SELECT institution_id, COUNT(*) AS student_count  FROM core.students  WHERE status = 'active'  GROUP BY institution_id), MostPopularInstitution AS (  SELECT institution_id  FROM StudentCounts  ORDER BY student_count DESC  LIMIT 1) SELECT 'Program' AS type, p.long_name AS name, COUNT(*) AS popularity FROM core.programs p JOIN core.students s ON p.institution_id = s.institution_id WHERE s.institution_id = (SELECT institution_id FROM MostPopularInstitution) GROUP BY p.long_name UNION ALL SELECT 'Course' AS type, c.title AS name, COUNT(*) AS popularity FROM core.courses c JOIN core.students s ON c.institution_id = s.institution_id WHERE s.institution_id = (SELECT institution_id FROM MostPopularInstitution) GROUP BY c.title ORDER BY popularity DESC;\", 'data': 'No results', 'data_tag': 'popular_courses_at_largest_institution'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answer': 'While specific data is not available to pinpoint the exact factors contributing to the popularity of certain programs or courses at large institutions, several general factors can be considered. These may include the reputation of the faculty, the relevance of the curriculum to current job markets, the availability of resources such as labs and libraries, the success of alumni, and the marketing efforts of the institution. Additionally, social factors such as peer influence and trends in education can also play a significant role in attracting students to specific programs.', 'sql': None, 'data': 'No results', 'data_tag': 'factors_affecting_program_popularity'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'How do you think the changing job market and technological advancements are influencing the development and popularity of new programs or courses at large institutions?', 'answer': 'The SQL query did not return any results, which suggests that there may not be specific data available regarding the influence of the changing job market and technological advancements on the development and popularity of new programs or courses at large institutions. However, it is widely recognized that these factors are driving educational institutions to adapt their curricula to meet the evolving demands of the workforce. Institutions are increasingly offering programs in areas such as data science, artificial intelligence, and cybersecurity to prepare students for emerging job roles. Additionally, the integration of technology in education, such as online learning platforms, is making these programs more accessible and popular among students.', 'sql': None, 'data': 'No results', 'data_tag': 'programs_influenced_by_job_market'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'What role do you think student feedback and industry partnerships play in shaping the curriculum of popular programs at large institutions?', 'answer': 'The SQL query did not return any results, which suggests that there may not be specific data available regarding the role of student feedback and industry partnerships in shaping the curriculum of popular programs at large institutions. However, generally speaking, student feedback is crucial as it provides insights into the effectiveness of the curriculum and areas for improvement. Industry partnerships can also play a significant role by ensuring that the curriculum aligns with current industry standards and job market demands, thus enhancing the employability of graduates. Without specific data, we cannot quantify these influences, but they are widely recognized as important factors in curriculum development.', 'sql': None, 'data': 'No results', 'data_tag': 'curriculum_influence_factors'}\n", "\n", "\n", "\n", "\n", "Answer OBJ: {'question': 'In what ways do you think institutions can effectively gather and implement student feedback to enhance their programs and ensure they remain relevant in a rapidly changing job market?', 'answer': 'The SQL query did not return any results, which suggests that there may not be specific data available on how institutions gather and implement student feedback. However, generally, institutions can effectively gather student feedback through various methods such as surveys, focus groups, suggestion boxes, and regular meetings with student representatives. To implement this feedback, they can analyze the data collected to identify trends and areas for improvement, engage in open discussions with faculty and administration, and prioritize changes that align with the evolving demands of the job market. Continuous assessment and adaptation of programs based on student input can help ensure that educational offerings remain relevant and effective.', 'sql': None, 'data': 'No results', 'data_tag': 'student_feedback_implementation_methods'}\n", "\n", "\n", "CONVERTING TO VECTOR STORE DOCS\n", "\n", "\n", " Checking supposed non empty data\n", "[{'institution_id': 24, 'total_students': 192627}]\n", "================================= \n", "\n", "CONVERTING TO VECTOR STORE DOCS\n", "\n", "CONVERTING TO VECTOR STORE DOCS\n", "\n", "\n", " Checking supposed non empty data\n", "[{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]\n", "================================= \n", "\n", "CONVERTING TO VECTOR STORE DOCS\n", "\n", "CONVERTING TO VECTOR STORE DOCS\n", "\n", "\n", " Checking supposed non empty data\n", "[{'retention_rate': 99.49}]\n", "================================= \n", "\n", "CONVERTING TO VECTOR STORE DOCS\n", "\n", "CONVERTING TO VECTOR STORE DOCS\n", "\n", "\n", " Checking supposed non empty data\n", "[{'description': 'UEW UNDERGRADUATE APPLICATION FORM 2022/2023', 'doa': 'SEP2022', 'doc': 'JUL2026', 'status': 'Active'}, {'description': 'UEW Postgraduate forms', 'doa': 'SEP2022', 'doc': 'JUL2026', 'status': 'Active'}]\n", "================================= \n", "\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"HEAD /deep_research HTTP/1.1\" 200 0\n", "INFO:elastic_transport.transport:HEAD http://*************:9200/deep_research [status:200 duration:0.134s]\n", "DEBUG:elasticsearch.helpers.vectorstore._sync.vectorstore:Index deep_research already exists. Skipping creation.\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"PUT /_bulk?refresh=true HTTP/1.1\" 200 None\n", "INFO:elastic_transport.transport:PUT http://*************:9200/_bulk?refresh=true [status:200 duration:1.207s]\n", "DEBUG:elasticsearch.helpers.vectorstore._sync.vectorstore:added texts ['What is the institution with the most students?_2025-08-07T11:35:11.914100+00:00_0', 'What is the institution with the most students?_2025-08-07T11:35:11.914100+00:00_1', 'What is the institution with the most students?_2025-08-07T11:35:11.914100+00:00_2', 'What is the institution with the most students?_2025-08-07T11:35:11.914100+00:00_3', 'What is the institution with the most students?_2025-08-07T11:35:11.914100+00:00_4', 'What is the institution with the most students?_2025-08-07T11:37:14.943524+00:00_0', 'What is the institution with the most students?_2025-08-07T11:37:14.943524+00:00_1', 'What is the institution with the most students?_2025-08-07T11:37:14.943524+00:00_2', 'What is the institution with the most students?_2025-08-07T11:37:14.943524+00:00_3', 'What is the institution with the most students?_2025-08-07T11:37:14.943524+00:00_4', 'What is the institution with the most students?_2025-08-07T11:35:20.430413+00:00_0', 'What is the institution with the most students?_2025-08-07T11:35:20.430413+00:00_1', 'What is the institution with the most students?_2025-08-07T11:35:20.430413+00:00_2', 'What is the institution with the most students?_2025-08-07T11:35:20.430413+00:00_3', 'What is the institution with the most students?_2025-08-07T11:35:20.430413+00:00_4', 'What is the institution with the most students?_2025-08-07T11:36:15.072910+00:00_0', 'What is the institution with the most students?_2025-08-07T11:36:15.072910+00:00_1', 'What is the institution with the most students?_2025-08-07T11:36:15.072910+00:00_2', 'What is the institution with the most students?_2025-08-07T11:36:15.072910+00:00_3', 'What is the institution with the most students?_2025-08-07T11:36:15.072910+00:00_4', 'What is the institution with the most students?_2025-08-07T11:35:31.144633+00:00_0', 'What is the institution with the most students?_2025-08-07T11:35:31.144633+00:00_1', 'What is the institution with the most students?_2025-08-07T11:35:31.144633+00:00_2', 'What is the institution with the most students?_2025-08-07T11:35:31.144633+00:00_3', 'What is the institution with the most students?_2025-08-07T11:35:31.144633+00:00_4', 'What is the institution with the most students?_2025-08-07T11:36:07.477452+00:00_0', 'What is the institution with the most students?_2025-08-07T11:36:07.477452+00:00_1', 'What is the institution with the most students?_2025-08-07T11:36:07.477452+00:00_2', 'What is the institution with the most students?_2025-08-07T11:36:07.477452+00:00_3', 'What is the institution with the most students?_2025-08-07T11:36:07.477452+00:00_4', 'What is the institution with the most students?_2025-08-07T11:35:15.163161+00:00_0', 'What is the institution with the most students?_2025-08-07T11:35:15.163161+00:00_1', 'What is the institution with the most students?_2025-08-07T11:35:15.163161+00:00_2', 'What is the institution with the most students?_2025-08-07T11:35:15.163161+00:00_3', 'What is the institution with the most students?_2025-08-07T11:35:15.163161+00:00_4'] to index\n", "DEBUG:urllib3.connectionpool:Starting new HTTP connection (2): *************:9200\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"POST /deep_research/_search?_source_includes=metadata,text HTTP/1.1\" 200 None\n", "INFO:elastic_transport.transport:POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.279s]\n", "DEBUG:urllib3.connectionpool:Starting new HTTP connection (3): *************:9200\n", "DEBUG:urllib3.connectionpool:Starting new HTTP connection (4): *************:9200\n", "DEBUG:urllib3.connectionpool:Starting new HTTP connection (5): *************:9200\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"POST /deep_research/_search?_source_includes=metadata,text HTTP/1.1\" 200 None\n", "INFO:elastic_transport.transport:POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.184s]\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"POST /deep_research/_search?_source_includes=metadata,text HTTP/1.1\" 200 None\n", "INFO:elastic_transport.transport:POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.411s]\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"POST /deep_research/_search?_source_includes=metadata,text HTTP/1.1\" 200 None\n", "INFO:elastic_transport.transport:POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.467s]\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"POST /deep_research/_search?_source_includes=metadata,text HTTP/1.1\" 200 None\n", "INFO:elastic_transport.transport:POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.166s]\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"POST /deep_research/_search?_source_includes=metadata,text HTTP/1.1\" 200 None\n", "INFO:elastic_transport.transport:POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.446s]\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"POST /deep_research/_search?_source_includes=metadata,text HTTP/1.1\" 200 None\n", "INFO:elastic_transport.transport:POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.400s]\n"]}], "source": ["report_sections, extended_data, data_backed_convos, informed_outline, refined_outline = interviews_to_report({\n", "    \"schema_text\": compact_db_structure,\n", "    \"question\": question\n", "})"]}, {"cell_type": "code", "execution_count": 93, "id": "f95cb778-fcad-473e-8bd0-3ad9d4924454", "metadata": {}, "outputs": [], "source": ["# run_query_silent(\"SELECT * FROM student_programs LIMIT 10\")"]}, {"cell_type": "code", "execution_count": 94, "id": "ed966623-8692-4a52-9726-4d7d4ae72464", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'question': 'What is the total number of students enrolled at the institution with the most students?',\n", "  'answer': 'The total number of students enrolled at the institution with the most students is 192,627.'},\n", " {'question': 'What is the demographic breakdown of students at the institution with the most students?',\n", "  'answer': 'The institution with the most students has a total of 164,622 students, broken down by gender as follows: 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total). There are also 8,205 students whose gender is not specified.'},\n", " {'question': 'What is the retention rate of students at the institution with the most students?',\n", "  'answer': 'The retention rate of students at the institution with the most students is 99.49%. This indicates a very high level of student satisfaction and success in continuing their education at this institution.'},\n", " {'question': 'What are the admission criteria for the institution with the most students?',\n", "  'answer': \"The admission criteria for the institution with the most students include the following application forms: 1) the 'UEW UNDERGRADUATE APPLICATION FORM 2022/2023', which is currently active and available since September 2022 until July 2026, and 2) the 'UEW Postgraduate forms', also active from September 2022 to July 2026. These forms outline the necessary steps and requirements for prospective undergraduate and postgraduate students to apply for admission.\"}]"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["data_backed_convos"]}, {"cell_type": "code", "execution_count": 95, "id": "a4948292-5008-4cd3-a602-20e3804a0972", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["{'outline': '# Report on the Institution with the Most Students\\n\\n## I. Introduction  \\n   A. Purpose of the Report  \\n   B. Original Guiding Question  \\n      1. What is the institution with the most students?  \\n\\n## II. Overview of the Institution  \\n   A. Total Enrollment  \\n      1. Current total number of students: 192,627  \\n   B. Demographic Breakdown  \\n      1. Gender Distribution  \\n         - Male Students: 96,457 (58.5%)  \\n         - Female Students: 87,965 (53.5%)  \\n         - Non-specified Gender: 8,205  \\n\\n## III. Student Retention  \\n   A. Retention Rate  \\n      1. Current retention rate: 99.49%  \\n      2. Implications of high retention rate  \\n         - Indicates student satisfaction and success  \\n\\n## IV. Admission Criteria  \\n   A. Application Forms  \\n      1. UEW Undergraduate Application Form (2022/2023)  \\n         - Availability: September 2022 - July 2026  \\n      2. UEW Postgraduate Application Forms  \\n         - Availability: September 2022 - July 2026  \\n   B. Requirements for Prospective Students  \\n      1. Steps and requirements outlined in application forms  \\n\\n## V. Conclusion  \\n   A. Summary of Key Findings  \\n   B. Implications for Future Research or Inquiry  \\n\\n## VI. References  \\n   A. Interview Transcripts/Summaries  \\n   B. Additional Sources (if applicable)  \\n'}"]}, "execution_count": 95, "metadata": {}, "output_type": "execute_result"}], "source": ["informed_outline"]}, {"cell_type": "code", "execution_count": 96, "id": "b4b6bc60-3722-4834-aedf-13988248daeb", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["{'outline': '# Report on the Institution with the Most Students\\n\\n## Introduction  \\nThe purpose of this report is to investigate and present findings on the institution with the highest student enrollment. The key answer to the guiding question is that the institution with the most students currently has a total enrollment of 192,627 students.\\n\\n## I. Overview of the Institution  \\n- A. Total Enrollment  \\n   - Current total number of students: 192,627  \\n- B. Demographic Breakdown  \\n   - 1. Gender Distribution  \\n      - Male Students: 96,457 (58.5%)  \\n      - Female Students: 87,965 (53.5%)  \\n      - Non-specified Gender: 8,205  \\n\\n## II. Student Retention  \\n- A. Retention Rate  \\n   - 1. Current retention rate: 99.49%  \\n   - 2. Implications of high retention rate  \\n      - Indicates student satisfaction and success  \\n\\n## III. Admission Criteria  \\n- A. Application Forms  \\n   - 1. UEW Undergraduate Application Form (2022/2023)  \\n      - Availability: September 2022 - July 2026  \\n   - 2. UEW Postgraduate Application Forms  \\n      - Availability: September 2022 - July 2026  \\n- B. Requirements for Prospective Students  \\n   - 1. Steps and requirements outlined in application forms  \\n\\n## IV. Implications of Findings  \\n- A. Impact on Education Policy  \\n- B. Influence on Future Enrollment Trends  \\n\\n## V. Conclusion  \\n- A. Summary of Key Findings  \\n- B. Implications for Future Research or Inquiry  \\n\\n## VI. References  \\n- A. Interview Transcripts/Summaries  \\n- B. Additional Sources (if applicable)  \\n'}"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["refined_outline"]}, {"cell_type": "code", "execution_count": 97, "id": "b5be9fc6-bfff-4c58-ac82-5185471fd06a", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DEBUG:urllib3.connectionpool:http://*************:9200 \"POST /deep_research/_search?_source_includes=metadata,text HTTP/1.1\" 200 None\n", "INFO:elastic_transport.transport:POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.167s]\n"]}, {"data": {"text/plain": ["[Document(metadata={'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-07T11:35:11.914100+00:00', 'data_returned': True}, page_content='Question: What is the total number of students enrolled at the institution with the most students?\\nAnswer: The total number of students enrolled at the institution with the most students is 192,627.'),\n", " Document(metadata={'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-07T11:35:20.430413+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}, page_content='Question: What is the demographic breakdown of students at the institution with the most students?\\nAnswer: The institution with the most students has a total of 164,622 students, broken down by gender as follows: 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total). There are also 8,205 students whose gender is not specified.'),\n", " Document(metadata={'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-07T11:35:31.144633+00:00', 'data_returned': True}, page_content='Question: What is the retention rate of students at the institution with the most students?\\nAnswer: The retention rate of students at the institution with the most students is 99.49%. This indicates a very high level of student satisfaction and success in continuing their education at this institution.'),\n", " Document(metadata={'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-07T11:35:15.163161+00:00', 'data_returned': True}, page_content=\"Question: What are the admission criteria for the institution with the most students?\\nAnswer: The admission criteria for the institution with the most students include the following application forms: 1) the 'UEW UNDERGRADUATE APPLICATION FORM 2022/2023', which is currently active and available since September 2022 until July 2026, and 2) the 'UEW Postgraduate forms', also active from September 2022 to July 2026. These forms outline the necessary steps and requirements for prospective undergraduate and postgraduate students to apply for admission.\")]"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["elastic_vector_search.as_retriever(search_kwargs={\"k\": 10,\n", "                                                  \"filter\": [\n", "                                                        {\"term\": {\"metadata.data_returned\": True}}\n", "                                                        ,\n", "                                                        {\"term\": {\"metadata.conversation_id.keyword\": question}}\n", "                                                    ]\n", "                                                 }).invoke(\n", "                                                        \"Give me a report on the topic: Which institution has the most students?\"\n", "                                                    )"]}, {"cell_type": "code", "execution_count": 98, "id": "84c45f55-8a51-4432-917b-0bda00e6b028", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["['## Introduction  \\nThe purpose of this report is to investigate and present findings on the institution with the highest student enrollment. The key answer to the guiding question is that the institution with the most students currently has a total enrollment of 192,627 students.',\n", " '## I. Overview of the Institution  \\n\\n### A. Total Enrollment  \\nThe current total number of students enrolled at the institution is 192,627.  \\n\\n### B. Demographic Breakdown  \\n1. Gender Distribution  \\nThe gender distribution among the students is as follows: there are 96,457 male students, accounting for 58.5% of the total enrollment, and 87,965 female students, representing 53.5%. Additionally, there are 8,205 students whose gender is not specified.  \\n[[student_demographics_by_gender]]',\n", " \"## II. Student Retention  \\n- A. Retention Rate  \\n   - 1. Current retention rate: 99.49%  \\n   - 2. Implications of high retention rate  \\n      The current retention rate of 99.49% indicates a very high level of student satisfaction and success in continuing their education at this institution. This high retention rate reflects positively on the institution's ability to meet the needs of its students, fostering an environment conducive to academic achievement and personal growth.\",\n", " '## III. Admission Criteria  \\n\\n### A. Application Forms  \\n1. **UEW Undergraduate Application Form (2022/2023)**  \\n   - Availability: September 2022 - July 2026  \\n   \\n2. **UEW Postgraduate Application Forms**  \\n   - Availability: September 2022 - July 2026  \\n\\n### B. Requirements for Prospective Students  \\n1. The steps and requirements for prospective undergraduate and postgraduate students are outlined in the application forms.',\n", " \"## IV. Implications of Findings  \\n\\n### A. Impact on Education Policy  \\nThe findings from the analysis of the institution with the most students highlight significant implications for education policy. With a total enrollment of 192,627 students, the institution demonstrates a robust demand for higher education. The high retention rate of 99.49% suggests that current educational policies are effectively supporting student satisfaction and success. This data can inform policymakers to continue or enhance existing support systems, ensuring that they meet the needs of a diverse student body, which includes 96,457 male students and 87,965 female students, along with 8,205 students whose gender is not specified. Such demographic insights are crucial for developing targeted initiatives that promote inclusivity and address the unique challenges faced by different student groups. [[student_demographics_by_gender]]\\n\\n### B. Influence on Future Enrollment Trends  \\nThe current enrollment figures and retention rates indicate a positive trend that may influence future enrollment strategies. The institution's ability to maintain a high retention rate suggests that prospective students are likely to view it as a desirable option for their education. As the admission criteria remain accessible through the active application forms for both undergraduate and postgraduate programs, it is anticipated that enrollment will continue to grow. The ongoing availability of these forms until July 2026 provides a stable framework for attracting new students, which could further enhance the institution's reputation and capacity.\",\n", " '## V. Conclusion  \\n\\n### A<PERSON> Summary of Key Findings  \\nThe analysis reveals that the institution with the most students has a remarkable retention rate of 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total enrollment at this institution stands at 192,627 students. The demographic breakdown shows that there are 96,457 male students, accounting for approximately 58.5% of the total, and 87,965 female students, representing about 53.5%. Additionally, there are 8,205 students whose gender is not specified. \\n\\n### B. Implications for Future Research or Inquiry  \\nThese findings suggest a need for further research into the factors contributing to the high retention rate and student satisfaction. Understanding the demographics and their impact on educational outcomes could provide valuable insights for institutional strategies. Additionally, examining the effectiveness of the current admission criteria may yield opportunities for enhancing enrollment processes and student diversity. [[student_demographics_by_gender]]',\n", " '## VI. References  \\n\\n### A. Interview Transcripts/Summaries  \\nThe interview transcripts and summaries provide detailed insights into the institutional practices and student experiences. They highlight key themes such as student satisfaction, retention strategies, and the effectiveness of admission processes.\\n\\n### B. Additional Sources (if applicable)  \\nAdditional sources include quantitative data regarding student demographics and retention rates. For instance, the retention rate of students at the institution with the most students is 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total number of students enrolled at this institution is 192,627, with a demographic breakdown showing 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total), along with 8,205 students whose gender is not specified. [[student_demographics_by_gender]]',\n", " '## Data Gaps and Limitations\\n\\n- No data available on factors contributing to high enrollment numbers at this institution\\n- No results regarding trends or strategies that contribute to high enrollment in large institutions\\n- No data available to analyze the influence of online education and technology on enrollment numbers at large institutions\\n- No specific data results available to provide anecdotal evidence or case studies regarding technology integration to boost enrollment\\n- No results available for the programs or courses at the institution with the most students\\n- No specific data available to pinpoint the exact factors contributing to the popularity of certain programs or courses at large institutions\\n- No specific data available regarding the influence of the changing job market and technological advancements on new programs or courses\\n- No specific data available regarding the role of student feedback and industry partnerships in shaping the curriculum of popular programs\\n- No specific data available on how institutions gather and implement student feedback\\n- No available results to provide a demographic breakdown of this institution\\n- No data available to analyze the specific factors contributing to the gender distribution at this institution\\n- No available data on demographic factors such as ethnicity or socioeconomic status for the student population at this institution\\n- No available results regarding the changes in student enrollment at the institution with the most students over the past few years\\n- No specific data available to analyze student enrollment trends at large institutions\\n- No specific results available regarding the strategies or initiatives that institutions have implemented to enhance student support services\\n- No results available on student enrollment trends related to technology, online learning, or digital engagement\\n- No specific programs or initiatives listed that explain how this institution achieves its high retention rate\\n- No specific data available to directly analyze the factors contributing to overall student satisfaction at this institution\\n- No specific methods or approaches documented for gathering qualitative data from students regarding their experiences and satisfaction\\n- No specific data available regarding the challenges the institution might face in implementing qualitative data collection methods\\n- No results available for the student-to-faculty ratio of the institution with the most students\\n- No specific data available to analyze the factors contributing to the variation in student-to-faculty ratios among different institutions\\n- No results available regarding student-to-faculty ratios, indicating that data may not be readily accessible\\n- No specific data available regarding the influence of technology on the student-to-faculty ratio in large institutions\\n- No specific data available to directly analyze the impact of increasing reliance on technology on student-faculty interactions and overall learning outcomes\\n- No specific eligibility requirements or qualifications available for undergraduate and postgraduate programs at this institution\\n- No specific data results available regarding the common challenges that applicants face during the admission process at this institution\\n- No documented resources or support systems provided by the institution to assist applicants in navigating the admission process\\n- No documented resources or data available regarding the strategies employed by successful applicants at this institution']"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["# report_sections =  [report_section for report_section in report_sections]\n", "report_sections"]}, {"cell_type": "code", "execution_count": 99, "id": "d8749aa6-a457-488c-8865-4e758cfb1a77", "metadata": {}, "outputs": [{"data": {"text/markdown": ["## Introduction  \n", "The purpose of this report is to investigate and present findings on the institution with the highest student enrollment. The key answer to the guiding question is that the institution with the most students currently has a total enrollment of 192,627 students.\n", "## I. Overview of the Institution  \n", "\n", "### A. Total Enrollment  \n", "The current total number of students enrolled at the institution is 192,627.  \n", "\n", "### B. Demographic Breakdown  \n", "1. Gender Distribution  \n", "The gender distribution among the students is as follows: there are 96,457 male students, accounting for 58.5% of the total enrollment, and 87,965 female students, representing 53.5%. Additionally, there are 8,205 students whose gender is not specified.  \n", "[[student_demographics_by_gender]]\n", "## II. Student Retention  \n", "- A. Retention Rate  \n", "   - 1. Current retention rate: 99.49%  \n", "   - 2. Implications of high retention rate  \n", "      The current retention rate of 99.49% indicates a very high level of student satisfaction and success in continuing their education at this institution. This high retention rate reflects positively on the institution's ability to meet the needs of its students, fostering an environment conducive to academic achievement and personal growth.\n", "## III. Admission Criteria  \n", "\n", "### A. Application Forms  \n", "1. **UEW Undergraduate Application Form (2022/2023)**  \n", "   - Availability: September 2022 - July 2026  \n", "   \n", "2. **UEW Postgraduate Application Forms**  \n", "   - Availability: September 2022 - July 2026  \n", "\n", "### B. Requirements for Prospective Students  \n", "1. The steps and requirements for prospective undergraduate and postgraduate students are outlined in the application forms.\n", "## IV. Implications of Findings  \n", "\n", "### A. Impact on Education Policy  \n", "The findings from the analysis of the institution with the most students highlight significant implications for education policy. With a total enrollment of 192,627 students, the institution demonstrates a robust demand for higher education. The high retention rate of 99.49% suggests that current educational policies are effectively supporting student satisfaction and success. This data can inform policymakers to continue or enhance existing support systems, ensuring that they meet the needs of a diverse student body, which includes 96,457 male students and 87,965 female students, along with 8,205 students whose gender is not specified. Such demographic insights are crucial for developing targeted initiatives that promote inclusivity and address the unique challenges faced by different student groups. [[student_demographics_by_gender]]\n", "\n", "### B. Influence on Future Enrollment Trends  \n", "The current enrollment figures and retention rates indicate a positive trend that may influence future enrollment strategies. The institution's ability to maintain a high retention rate suggests that prospective students are likely to view it as a desirable option for their education. As the admission criteria remain accessible through the active application forms for both undergraduate and postgraduate programs, it is anticipated that enrollment will continue to grow. The ongoing availability of these forms until July 2026 provides a stable framework for attracting new students, which could further enhance the institution's reputation and capacity.\n", "## <PERSON>. Conclusion  \n", "\n", "### <PERSON><PERSON> of Key Findings  \n", "The analysis reveals that the institution with the most students has a remarkable retention rate of 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total enrollment at this institution stands at 192,627 students. The demographic breakdown shows that there are 96,457 male students, accounting for approximately 58.5% of the total, and 87,965 female students, representing about 53.5%. Additionally, there are 8,205 students whose gender is not specified. \n", "\n", "### B. Implications for Future Research or Inquiry  \n", "These findings suggest a need for further research into the factors contributing to the high retention rate and student satisfaction. Understanding the demographics and their impact on educational outcomes could provide valuable insights for institutional strategies. Additionally, examining the effectiveness of the current admission criteria may yield opportunities for enhancing enrollment processes and student diversity. [[student_demographics_by_gender]]\n", "## VI. References  \n", "\n", "### A. Interview Transcripts/Summaries  \n", "The interview transcripts and summaries provide detailed insights into the institutional practices and student experiences. They highlight key themes such as student satisfaction, retention strategies, and the effectiveness of admission processes.\n", "\n", "### B. Additional Sources (if applicable)  \n", "Additional sources include quantitative data regarding student demographics and retention rates. For instance, the retention rate of students at the institution with the most students is 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total number of students enrolled at this institution is 192,627, with a demographic breakdown showing 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total), along with 8,205 students whose gender is not specified. [[student_demographics_by_gender]]\n", "## Data Gaps and Limitations\n", "\n", "- No data available on factors contributing to high enrollment numbers at this institution\n", "- No results regarding trends or strategies that contribute to high enrollment in large institutions\n", "- No data available to analyze the influence of online education and technology on enrollment numbers at large institutions\n", "- No specific data results available to provide anecdotal evidence or case studies regarding technology integration to boost enrollment\n", "- No results available for the programs or courses at the institution with the most students\n", "- No specific data available to pinpoint the exact factors contributing to the popularity of certain programs or courses at large institutions\n", "- No specific data available regarding the influence of the changing job market and technological advancements on new programs or courses\n", "- No specific data available regarding the role of student feedback and industry partnerships in shaping the curriculum of popular programs\n", "- No specific data available on how institutions gather and implement student feedback\n", "- No available results to provide a demographic breakdown of this institution\n", "- No data available to analyze the specific factors contributing to the gender distribution at this institution\n", "- No available data on demographic factors such as ethnicity or socioeconomic status for the student population at this institution\n", "- No available results regarding the changes in student enrollment at the institution with the most students over the past few years\n", "- No specific data available to analyze student enrollment trends at large institutions\n", "- No specific results available regarding the strategies or initiatives that institutions have implemented to enhance student support services\n", "- No results available on student enrollment trends related to technology, online learning, or digital engagement\n", "- No specific programs or initiatives listed that explain how this institution achieves its high retention rate\n", "- No specific data available to directly analyze the factors contributing to overall student satisfaction at this institution\n", "- No specific methods or approaches documented for gathering qualitative data from students regarding their experiences and satisfaction\n", "- No specific data available regarding the challenges the institution might face in implementing qualitative data collection methods\n", "- No results available for the student-to-faculty ratio of the institution with the most students\n", "- No specific data available to analyze the factors contributing to the variation in student-to-faculty ratios among different institutions\n", "- No results available regarding student-to-faculty ratios, indicating that data may not be readily accessible\n", "- No specific data available regarding the influence of technology on the student-to-faculty ratio in large institutions\n", "- No specific data available to directly analyze the impact of increasing reliance on technology on student-faculty interactions and overall learning outcomes\n", "- No specific eligibility requirements or qualifications available for undergraduate and postgraduate programs at this institution\n", "- No specific data results available regarding the common challenges that applicants face during the admission process at this institution\n", "- No documented resources or support systems provided by the institution to assist applicants in navigating the admission process\n", "- No documented resources or data available regarding the strategies employed by successful applicants at this institution"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Markdown(\"\\n\".join(report_sections)))"]}, {"cell_type": "code", "execution_count": 100, "id": "a902a834-8fa1-4f0d-af01-c23de11cbfba", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["['## Introduction  \\nThe purpose of this report is to investigate and present findings on the institution with the highest student enrollment. The key answer to the guiding question is that the institution with the most students currently has a total enrollment of 192,627 students.',\n", " '## I. Overview of the Institution  \\n\\n### A. Total Enrollment  \\nThe current total number of students enrolled at the institution is 192,627.  \\n\\n### B. Demographic Breakdown  \\n1. Gender Distribution  \\nThe gender distribution among the students is as follows: there are 96,457 male students, accounting for 58.5% of the total enrollment, and 87,965 female students, representing 53.5%. Additionally, there are 8,205 students whose gender is not specified.  \\n',\n", " '[[student_demographics_by_gender]]',\n", " '',\n", " \"## II. Student Retention  \\n- A. Retention Rate  \\n   - 1. Current retention rate: 99.49%  \\n   - 2. Implications of high retention rate  \\n      The current retention rate of 99.49% indicates a very high level of student satisfaction and success in continuing their education at this institution. This high retention rate reflects positively on the institution's ability to meet the needs of its students, fostering an environment conducive to academic achievement and personal growth.\",\n", " '## III. Admission Criteria  \\n\\n### A. Application Forms  \\n1. **UEW Undergraduate Application Form (2022/2023)**  \\n   - Availability: September 2022 - July 2026  \\n   \\n2. **UEW Postgraduate Application Forms**  \\n   - Availability: September 2022 - July 2026  \\n\\n### B. Requirements for Prospective Students  \\n1. The steps and requirements for prospective undergraduate and postgraduate students are outlined in the application forms.',\n", " '## IV. Implications of Findings  \\n\\n### A. Impact on Education Policy  \\nThe findings from the analysis of the institution with the most students highlight significant implications for education policy. With a total enrollment of 192,627 students, the institution demonstrates a robust demand for higher education. The high retention rate of 99.49% suggests that current educational policies are effectively supporting student satisfaction and success. This data can inform policymakers to continue or enhance existing support systems, ensuring that they meet the needs of a diverse student body, which includes 96,457 male students and 87,965 female students, along with 8,205 students whose gender is not specified. Such demographic insights are crucial for developing targeted initiatives that promote inclusivity and address the unique challenges faced by different student groups. ',\n", " '[[student_demographics_by_gender]]',\n", " \"\\n\\n### B. Influence on Future Enrollment Trends  \\nThe current enrollment figures and retention rates indicate a positive trend that may influence future enrollment strategies. The institution's ability to maintain a high retention rate suggests that prospective students are likely to view it as a desirable option for their education. As the admission criteria remain accessible through the active application forms for both undergraduate and postgraduate programs, it is anticipated that enrollment will continue to grow. The ongoing availability of these forms until July 2026 provides a stable framework for attracting new students, which could further enhance the institution's reputation and capacity.\",\n", " '## V. Conclusion  \\n\\n### A<PERSON> Summary of Key Findings  \\nThe analysis reveals that the institution with the most students has a remarkable retention rate of 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total enrollment at this institution stands at 192,627 students. The demographic breakdown shows that there are 96,457 male students, accounting for approximately 58.5% of the total, and 87,965 female students, representing about 53.5%. Additionally, there are 8,205 students whose gender is not specified. \\n\\n### B. Implications for Future Research or Inquiry  \\nThese findings suggest a need for further research into the factors contributing to the high retention rate and student satisfaction. Understanding the demographics and their impact on educational outcomes could provide valuable insights for institutional strategies. Additionally, examining the effectiveness of the current admission criteria may yield opportunities for enhancing enrollment processes and student diversity. ',\n", " '[[student_demographics_by_gender]]',\n", " '',\n", " '## VI. References  \\n\\n### A. Interview Transcripts/Summaries  \\nThe interview transcripts and summaries provide detailed insights into the institutional practices and student experiences. They highlight key themes such as student satisfaction, retention strategies, and the effectiveness of admission processes.\\n\\n### B. Additional Sources (if applicable)  \\nAdditional sources include quantitative data regarding student demographics and retention rates. For instance, the retention rate of students at the institution with the most students is 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total number of students enrolled at this institution is 192,627, with a demographic breakdown showing 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total), along with 8,205 students whose gender is not specified. ',\n", " '[[student_demographics_by_gender]]',\n", " '',\n", " '## Data Gaps and Limitations\\n\\n- No data available on factors contributing to high enrollment numbers at this institution\\n- No results regarding trends or strategies that contribute to high enrollment in large institutions\\n- No data available to analyze the influence of online education and technology on enrollment numbers at large institutions\\n- No specific data results available to provide anecdotal evidence or case studies regarding technology integration to boost enrollment\\n- No results available for the programs or courses at the institution with the most students\\n- No specific data available to pinpoint the exact factors contributing to the popularity of certain programs or courses at large institutions\\n- No specific data available regarding the influence of the changing job market and technological advancements on new programs or courses\\n- No specific data available regarding the role of student feedback and industry partnerships in shaping the curriculum of popular programs\\n- No specific data available on how institutions gather and implement student feedback\\n- No available results to provide a demographic breakdown of this institution\\n- No data available to analyze the specific factors contributing to the gender distribution at this institution\\n- No available data on demographic factors such as ethnicity or socioeconomic status for the student population at this institution\\n- No available results regarding the changes in student enrollment at the institution with the most students over the past few years\\n- No specific data available to analyze student enrollment trends at large institutions\\n- No specific results available regarding the strategies or initiatives that institutions have implemented to enhance student support services\\n- No results available on student enrollment trends related to technology, online learning, or digital engagement\\n- No specific programs or initiatives listed that explain how this institution achieves its high retention rate\\n- No specific data available to directly analyze the factors contributing to overall student satisfaction at this institution\\n- No specific methods or approaches documented for gathering qualitative data from students regarding their experiences and satisfaction\\n- No specific data available regarding the challenges the institution might face in implementing qualitative data collection methods\\n- No results available for the student-to-faculty ratio of the institution with the most students\\n- No specific data available to analyze the factors contributing to the variation in student-to-faculty ratios among different institutions\\n- No results available regarding student-to-faculty ratios, indicating that data may not be readily accessible\\n- No specific data available regarding the influence of technology on the student-to-faculty ratio in large institutions\\n- No specific data available to directly analyze the impact of increasing reliance on technology on student-faculty interactions and overall learning outcomes\\n- No specific eligibility requirements or qualifications available for undergraduate and postgraduate programs at this institution\\n- No specific data results available regarding the common challenges that applicants face during the admission process at this institution\\n- No documented resources or support systems provided by the institution to assist applicants in navigating the admission process\\n- No documented resources or data available regarding the strategies employed by successful applicants at this institution']"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["report_array = []\n", "for report_section in report_sections:\n", "    report_array.extend(re.split(r'(\\[\\[.*?\\]\\])', report_section))\n", "\n", "report_array"]}, {"cell_type": "code", "execution_count": 101, "id": "0709f889-1551-419b-93b2-92e84ea6096a", "metadata": {"scrolled": true}, "outputs": [], "source": ["import re\n", "from collections import OrderedDict\n", "from typing import List, Dict, Any\n", "\n", "# -----------------------------------------------------------\n", "# utility\n", "# -----------------------------------------------------------\n", "_PLACEHOLDER = re.compile(r\"\\[\\[(.*?)]]\")\n", "def _title(tag: str) -> str:\n", "    \"\"\"Convert snake_case or kebab-case tag → Title Case.\"\"\"\n", "    return tag.replace(\"_\", \" \").replace(\"-\", \" \").title()\n", "\n", "# -----------------------------------------------------------\n", "# main helper\n", "# -----------------------------------------------------------\n", "def process_report_figures(blocks: List[str]) -> str:\n", "    \"\"\"\n", "    Transform a list of markdown section strings that contain [[[tag]]] placeholders into\n", "    a finished report that\n", "\n", "      • inserts a reference line `[Figure n – Title]` immediately *before* the first block\n", "        that contains each unique tag,\n", "      • keeps the first placeholder unchanged (so later production code can swap it for a\n", "        graphic or table),\n", "      • replaces subsequent occurrences of the same tag with `(See Figure n)`,\n", "      • appends a 'List of Figures' section at the end.\n", "\n", "    Parameters\n", "    ----------\n", "    blocks : List[str]\n", "        The draft report, one string per section/paragraph.\n", "\n", "    Returns\n", "    -------\n", "    str\n", "        A single markdown string ready for post-processing or viewing.\n", "    \"\"\"\n", "    fig_map: OrderedDict[str, int] = OrderedDict()   # tag → figure number\n", "    fig_counter = 1\n", "    processed: List[str] = []\n", "\n", "    for part in blocks:\n", "        # If no placeholder in this part, just copy it over.\n", "        if not _PLACEHOLDER.search(part):\n", "            processed.append(part)\n", "            continue\n", "\n", "        # Walk all placeholders in this part so we can treat first vs. later correctly.\n", "        def replace(match):\n", "            nonlocal fig_counter\n", "            tag = match.group(1)\n", "\n", "            if extended_data.get(tag, False):\n", "                # ── First time we've seen this tag ───────────────────────────\n", "                if tag not in fig_map:\n", "                    fig_map[tag] = fig_counter\n", "                    n = fig_counter\n", "                    fig_counter += 1\n", "                    # Insert reference line *before* this block exactly once\n", "                    ref_line = f\"\\nFigure {n} – {_title(tag)}\\n\"\n", "                    if ref_line not in processed[-1:]:   # avoid duplicate if block starts with same tag\n", "                        processed.append(ref_line)\n", "\n", "                    # KEEP the first placeholder unchanged so later routine can swap it.\n", "                    return match.group(0)   # original [[[tag]]]\n", "\n", "                # ── Subsequent occurrence ─────────────────────────────────────\n", "                n = fig_map[tag]\n", "                return f\"(See Figure {n})\"\n", "\n", "        # Apply replacement to the current block\n", "        part = _PLACEHOLDER.sub(replace, part)\n", "        processed.append(part)\n", "\n", "    # Append List of Figures\n", "    if fig_map:\n", "        lof_lines = [\"\\n\\n## List of Figures\"]\n", "        for tag, num in fig_map.items():\n", "            lof_lines.append(f\"* **Figure {num}** – {_title(tag)}\")\n", "        processed.append(\"\\n\".join(lof_lines))\n", "\n", "    return processed\n", "\n", "report = process_report_figures(report_array)"]}, {"cell_type": "code", "execution_count": 102, "id": "006981b9-5d23-464d-ba6c-7f5c71fd254a", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["['## Introduction  \\nThe purpose of this report is to investigate and present findings on the institution with the highest student enrollment. The key answer to the guiding question is that the institution with the most students currently has a total enrollment of 192,627 students.',\n", " '## I. Overview of the Institution  \\n\\n### A. Total Enrollment  \\nThe current total number of students enrolled at the institution is 192,627.  \\n\\n### B. Demographic Breakdown  \\n1. Gender Distribution  \\nThe gender distribution among the students is as follows: there are 96,457 male students, accounting for 58.5% of the total enrollment, and 87,965 female students, representing 53.5%. Additionally, there are 8,205 students whose gender is not specified.  \\n',\n", " '\\nFigure 1 – Student Demographics By Gender\\n',\n", " '[[student_demographics_by_gender]]',\n", " '',\n", " \"## II. Student Retention  \\n- A. Retention Rate  \\n   - 1. Current retention rate: 99.49%  \\n   - 2. Implications of high retention rate  \\n      The current retention rate of 99.49% indicates a very high level of student satisfaction and success in continuing their education at this institution. This high retention rate reflects positively on the institution's ability to meet the needs of its students, fostering an environment conducive to academic achievement and personal growth.\",\n", " '## III. Admission Criteria  \\n\\n### A. Application Forms  \\n1. **UEW Undergraduate Application Form (2022/2023)**  \\n   - Availability: September 2022 - July 2026  \\n   \\n2. **UEW Postgraduate Application Forms**  \\n   - Availability: September 2022 - July 2026  \\n\\n### B. Requirements for Prospective Students  \\n1. The steps and requirements for prospective undergraduate and postgraduate students are outlined in the application forms.',\n", " '## IV. Implications of Findings  \\n\\n### A. Impact on Education Policy  \\nThe findings from the analysis of the institution with the most students highlight significant implications for education policy. With a total enrollment of 192,627 students, the institution demonstrates a robust demand for higher education. The high retention rate of 99.49% suggests that current educational policies are effectively supporting student satisfaction and success. This data can inform policymakers to continue or enhance existing support systems, ensuring that they meet the needs of a diverse student body, which includes 96,457 male students and 87,965 female students, along with 8,205 students whose gender is not specified. Such demographic insights are crucial for developing targeted initiatives that promote inclusivity and address the unique challenges faced by different student groups. ',\n", " '(See Figure 1)',\n", " \"\\n\\n### B. Influence on Future Enrollment Trends  \\nThe current enrollment figures and retention rates indicate a positive trend that may influence future enrollment strategies. The institution's ability to maintain a high retention rate suggests that prospective students are likely to view it as a desirable option for their education. As the admission criteria remain accessible through the active application forms for both undergraduate and postgraduate programs, it is anticipated that enrollment will continue to grow. The ongoing availability of these forms until July 2026 provides a stable framework for attracting new students, which could further enhance the institution's reputation and capacity.\",\n", " '## V. Conclusion  \\n\\n### A<PERSON> Summary of Key Findings  \\nThe analysis reveals that the institution with the most students has a remarkable retention rate of 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total enrollment at this institution stands at 192,627 students. The demographic breakdown shows that there are 96,457 male students, accounting for approximately 58.5% of the total, and 87,965 female students, representing about 53.5%. Additionally, there are 8,205 students whose gender is not specified. \\n\\n### B. Implications for Future Research or Inquiry  \\nThese findings suggest a need for further research into the factors contributing to the high retention rate and student satisfaction. Understanding the demographics and their impact on educational outcomes could provide valuable insights for institutional strategies. Additionally, examining the effectiveness of the current admission criteria may yield opportunities for enhancing enrollment processes and student diversity. ',\n", " '(See Figure 1)',\n", " '',\n", " '## VI. References  \\n\\n### A. Interview Transcripts/Summaries  \\nThe interview transcripts and summaries provide detailed insights into the institutional practices and student experiences. They highlight key themes such as student satisfaction, retention strategies, and the effectiveness of admission processes.\\n\\n### B. Additional Sources (if applicable)  \\nAdditional sources include quantitative data regarding student demographics and retention rates. For instance, the retention rate of students at the institution with the most students is 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total number of students enrolled at this institution is 192,627, with a demographic breakdown showing 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total), along with 8,205 students whose gender is not specified. ',\n", " '(See Figure 1)',\n", " '',\n", " '## Data Gaps and Limitations\\n\\n- No data available on factors contributing to high enrollment numbers at this institution\\n- No results regarding trends or strategies that contribute to high enrollment in large institutions\\n- No data available to analyze the influence of online education and technology on enrollment numbers at large institutions\\n- No specific data results available to provide anecdotal evidence or case studies regarding technology integration to boost enrollment\\n- No results available for the programs or courses at the institution with the most students\\n- No specific data available to pinpoint the exact factors contributing to the popularity of certain programs or courses at large institutions\\n- No specific data available regarding the influence of the changing job market and technological advancements on new programs or courses\\n- No specific data available regarding the role of student feedback and industry partnerships in shaping the curriculum of popular programs\\n- No specific data available on how institutions gather and implement student feedback\\n- No available results to provide a demographic breakdown of this institution\\n- No data available to analyze the specific factors contributing to the gender distribution at this institution\\n- No available data on demographic factors such as ethnicity or socioeconomic status for the student population at this institution\\n- No available results regarding the changes in student enrollment at the institution with the most students over the past few years\\n- No specific data available to analyze student enrollment trends at large institutions\\n- No specific results available regarding the strategies or initiatives that institutions have implemented to enhance student support services\\n- No results available on student enrollment trends related to technology, online learning, or digital engagement\\n- No specific programs or initiatives listed that explain how this institution achieves its high retention rate\\n- No specific data available to directly analyze the factors contributing to overall student satisfaction at this institution\\n- No specific methods or approaches documented for gathering qualitative data from students regarding their experiences and satisfaction\\n- No specific data available regarding the challenges the institution might face in implementing qualitative data collection methods\\n- No results available for the student-to-faculty ratio of the institution with the most students\\n- No specific data available to analyze the factors contributing to the variation in student-to-faculty ratios among different institutions\\n- No results available regarding student-to-faculty ratios, indicating that data may not be readily accessible\\n- No specific data available regarding the influence of technology on the student-to-faculty ratio in large institutions\\n- No specific data available to directly analyze the impact of increasing reliance on technology on student-faculty interactions and overall learning outcomes\\n- No specific eligibility requirements or qualifications available for undergraduate and postgraduate programs at this institution\\n- No specific data results available regarding the common challenges that applicants face during the admission process at this institution\\n- No documented resources or support systems provided by the institution to assist applicants in navigating the admission process\\n- No documented resources or data available regarding the strategies employed by successful applicants at this institution',\n", " '\\n\\n## List of Figures\\n* **Figure 1** – Student Demographics By Gender']"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["report"]}, {"cell_type": "code", "execution_count": 103, "id": "448d26d0-8e8f-40d8-90ae-bd84e8d5dec8", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/markdown": ["## Introduction  \n", "The purpose of this report is to investigate and present findings on the institution with the highest student enrollment. The key answer to the guiding question is that the institution with the most students currently has a total enrollment of 192,627 students.\n", "## I. Overview of the Institution  \n", "\n", "### A. Total Enrollment  \n", "The current total number of students enrolled at the institution is 192,627.  \n", "\n", "### B. Demographic Breakdown  \n", "1. Gender Distribution  \n", "The gender distribution among the students is as follows: there are 96,457 male students, accounting for 58.5% of the total enrollment, and 87,965 female students, representing 53.5%. Additionally, there are 8,205 students whose gender is not specified.  \n", "\n", "\n", "Figure 1 – Student Demographics By Gender\n", "\n", "[[student_demographics_by_gender]]\n", "\n", "## II. Student Retention  \n", "- A. Retention Rate  \n", "   - 1. Current retention rate: 99.49%  \n", "   - 2. Implications of high retention rate  \n", "      The current retention rate of 99.49% indicates a very high level of student satisfaction and success in continuing their education at this institution. This high retention rate reflects positively on the institution's ability to meet the needs of its students, fostering an environment conducive to academic achievement and personal growth.\n", "## III. Admission Criteria  \n", "\n", "### A. Application Forms  \n", "1. **UEW Undergraduate Application Form (2022/2023)**  \n", "   - Availability: September 2022 - July 2026  \n", "   \n", "2. **UEW Postgraduate Application Forms**  \n", "   - Availability: September 2022 - July 2026  \n", "\n", "### B. Requirements for Prospective Students  \n", "1. The steps and requirements for prospective undergraduate and postgraduate students are outlined in the application forms.\n", "## IV. Implications of Findings  \n", "\n", "### A. Impact on Education Policy  \n", "The findings from the analysis of the institution with the most students highlight significant implications for education policy. With a total enrollment of 192,627 students, the institution demonstrates a robust demand for higher education. The high retention rate of 99.49% suggests that current educational policies are effectively supporting student satisfaction and success. This data can inform policymakers to continue or enhance existing support systems, ensuring that they meet the needs of a diverse student body, which includes 96,457 male students and 87,965 female students, along with 8,205 students whose gender is not specified. Such demographic insights are crucial for developing targeted initiatives that promote inclusivity and address the unique challenges faced by different student groups. \n", "(See Figure 1)\n", "\n", "\n", "### B. Influence on Future Enrollment Trends  \n", "The current enrollment figures and retention rates indicate a positive trend that may influence future enrollment strategies. The institution's ability to maintain a high retention rate suggests that prospective students are likely to view it as a desirable option for their education. As the admission criteria remain accessible through the active application forms for both undergraduate and postgraduate programs, it is anticipated that enrollment will continue to grow. The ongoing availability of these forms until July 2026 provides a stable framework for attracting new students, which could further enhance the institution's reputation and capacity.\n", "## <PERSON>. Conclusion  \n", "\n", "### <PERSON><PERSON> of Key Findings  \n", "The analysis reveals that the institution with the most students has a remarkable retention rate of 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total enrollment at this institution stands at 192,627 students. The demographic breakdown shows that there are 96,457 male students, accounting for approximately 58.5% of the total, and 87,965 female students, representing about 53.5%. Additionally, there are 8,205 students whose gender is not specified. \n", "\n", "### B. Implications for Future Research or Inquiry  \n", "These findings suggest a need for further research into the factors contributing to the high retention rate and student satisfaction. Understanding the demographics and their impact on educational outcomes could provide valuable insights for institutional strategies. Additionally, examining the effectiveness of the current admission criteria may yield opportunities for enhancing enrollment processes and student diversity. \n", "(See Figure 1)\n", "\n", "## VI. References  \n", "\n", "### A. Interview Transcripts/Summaries  \n", "The interview transcripts and summaries provide detailed insights into the institutional practices and student experiences. They highlight key themes such as student satisfaction, retention strategies, and the effectiveness of admission processes.\n", "\n", "### B. Additional Sources (if applicable)  \n", "Additional sources include quantitative data regarding student demographics and retention rates. For instance, the retention rate of students at the institution with the most students is 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total number of students enrolled at this institution is 192,627, with a demographic breakdown showing 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total), along with 8,205 students whose gender is not specified. \n", "(See Figure 1)\n", "\n", "## Data Gaps and Limitations\n", "\n", "- No data available on factors contributing to high enrollment numbers at this institution\n", "- No results regarding trends or strategies that contribute to high enrollment in large institutions\n", "- No data available to analyze the influence of online education and technology on enrollment numbers at large institutions\n", "- No specific data results available to provide anecdotal evidence or case studies regarding technology integration to boost enrollment\n", "- No results available for the programs or courses at the institution with the most students\n", "- No specific data available to pinpoint the exact factors contributing to the popularity of certain programs or courses at large institutions\n", "- No specific data available regarding the influence of the changing job market and technological advancements on new programs or courses\n", "- No specific data available regarding the role of student feedback and industry partnerships in shaping the curriculum of popular programs\n", "- No specific data available on how institutions gather and implement student feedback\n", "- No available results to provide a demographic breakdown of this institution\n", "- No data available to analyze the specific factors contributing to the gender distribution at this institution\n", "- No available data on demographic factors such as ethnicity or socioeconomic status for the student population at this institution\n", "- No available results regarding the changes in student enrollment at the institution with the most students over the past few years\n", "- No specific data available to analyze student enrollment trends at large institutions\n", "- No specific results available regarding the strategies or initiatives that institutions have implemented to enhance student support services\n", "- No results available on student enrollment trends related to technology, online learning, or digital engagement\n", "- No specific programs or initiatives listed that explain how this institution achieves its high retention rate\n", "- No specific data available to directly analyze the factors contributing to overall student satisfaction at this institution\n", "- No specific methods or approaches documented for gathering qualitative data from students regarding their experiences and satisfaction\n", "- No specific data available regarding the challenges the institution might face in implementing qualitative data collection methods\n", "- No results available for the student-to-faculty ratio of the institution with the most students\n", "- No specific data available to analyze the factors contributing to the variation in student-to-faculty ratios among different institutions\n", "- No results available regarding student-to-faculty ratios, indicating that data may not be readily accessible\n", "- No specific data available regarding the influence of technology on the student-to-faculty ratio in large institutions\n", "- No specific data available to directly analyze the impact of increasing reliance on technology on student-faculty interactions and overall learning outcomes\n", "- No specific eligibility requirements or qualifications available for undergraduate and postgraduate programs at this institution\n", "- No specific data results available regarding the common challenges that applicants face during the admission process at this institution\n", "- No documented resources or support systems provided by the institution to assist applicants in navigating the admission process\n", "- No documented resources or data available regarding the strategies employed by successful applicants at this institution\n", "\n", "\n", "## List of Figures\n", "* **Figure 1** – Student Demographics By Gender"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Markdown(\"\\n\".join(report)))"]}, {"cell_type": "code", "execution_count": 104, "id": "1ab23e84-37d4-4346-83a0-7a041e94ba5c", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nothing\n", "Nothing\n", "Nothing\n", "{'presentation_type': 'table', 'data': [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]}\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n", "Nothing\n"]}], "source": ["for i in range(len(report)):\n", "    item = report[i]\n", "    \n", "    # Check if the item matches the tag pattern [[...]]\n", "    match = re.match(r'\\[\\[(.*?)\\]\\]', item)\n", "    if match:\n", "        # If it's a tag, process it\n", "        tag = match.group(1)\n", "        data = extended_data.get(tag, \"\")  # Replace with processed result\n", "        if data == \"\":\n", "            print(tag)\n", "        else:\n", "            print(data)\n", "        report[i] = data\n", "    else:\n", "        print(\"Nothing\")\n", "        "]}, {"cell_type": "code", "execution_count": 105, "id": "2ea88a54-f3ea-4945-81c0-d855768d9ae4", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["['## Introduction  \\nThe purpose of this report is to investigate and present findings on the institution with the highest student enrollment. The key answer to the guiding question is that the institution with the most students currently has a total enrollment of 192,627 students.',\n", " '## I. Overview of the Institution  \\n\\n### A. Total Enrollment  \\nThe current total number of students enrolled at the institution is 192,627.  \\n\\n### B. Demographic Breakdown  \\n1. Gender Distribution  \\nThe gender distribution among the students is as follows: there are 96,457 male students, accounting for 58.5% of the total enrollment, and 87,965 female students, representing 53.5%. Additionally, there are 8,205 students whose gender is not specified.  \\n',\n", " '\\nFigure 1 – Student Demographics By Gender\\n',\n", " {'presentation_type': 'table',\n", "  'data': [{'sex': '', 'student_count': 8205},\n", "   {'sex': 'M', 'student_count': 96457},\n", "   {'sex': 'F', 'student_count': 87965}]},\n", " '',\n", " \"## II. Student Retention  \\n- A. Retention Rate  \\n   - 1. Current retention rate: 99.49%  \\n   - 2. Implications of high retention rate  \\n      The current retention rate of 99.49% indicates a very high level of student satisfaction and success in continuing their education at this institution. This high retention rate reflects positively on the institution's ability to meet the needs of its students, fostering an environment conducive to academic achievement and personal growth.\",\n", " '## III. Admission Criteria  \\n\\n### A. Application Forms  \\n1. **UEW Undergraduate Application Form (2022/2023)**  \\n   - Availability: September 2022 - July 2026  \\n   \\n2. **UEW Postgraduate Application Forms**  \\n   - Availability: September 2022 - July 2026  \\n\\n### B. Requirements for Prospective Students  \\n1. The steps and requirements for prospective undergraduate and postgraduate students are outlined in the application forms.',\n", " '## IV. Implications of Findings  \\n\\n### A. Impact on Education Policy  \\nThe findings from the analysis of the institution with the most students highlight significant implications for education policy. With a total enrollment of 192,627 students, the institution demonstrates a robust demand for higher education. The high retention rate of 99.49% suggests that current educational policies are effectively supporting student satisfaction and success. This data can inform policymakers to continue or enhance existing support systems, ensuring that they meet the needs of a diverse student body, which includes 96,457 male students and 87,965 female students, along with 8,205 students whose gender is not specified. Such demographic insights are crucial for developing targeted initiatives that promote inclusivity and address the unique challenges faced by different student groups. ',\n", " '(See Figure 1)',\n", " \"\\n\\n### B. Influence on Future Enrollment Trends  \\nThe current enrollment figures and retention rates indicate a positive trend that may influence future enrollment strategies. The institution's ability to maintain a high retention rate suggests that prospective students are likely to view it as a desirable option for their education. As the admission criteria remain accessible through the active application forms for both undergraduate and postgraduate programs, it is anticipated that enrollment will continue to grow. The ongoing availability of these forms until July 2026 provides a stable framework for attracting new students, which could further enhance the institution's reputation and capacity.\",\n", " '## V. Conclusion  \\n\\n### A<PERSON> Summary of Key Findings  \\nThe analysis reveals that the institution with the most students has a remarkable retention rate of 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total enrollment at this institution stands at 192,627 students. The demographic breakdown shows that there are 96,457 male students, accounting for approximately 58.5% of the total, and 87,965 female students, representing about 53.5%. Additionally, there are 8,205 students whose gender is not specified. \\n\\n### B. Implications for Future Research or Inquiry  \\nThese findings suggest a need for further research into the factors contributing to the high retention rate and student satisfaction. Understanding the demographics and their impact on educational outcomes could provide valuable insights for institutional strategies. Additionally, examining the effectiveness of the current admission criteria may yield opportunities for enhancing enrollment processes and student diversity. ',\n", " '(See Figure 1)',\n", " '',\n", " '## VI. References  \\n\\n### A. Interview Transcripts/Summaries  \\nThe interview transcripts and summaries provide detailed insights into the institutional practices and student experiences. They highlight key themes such as student satisfaction, retention strategies, and the effectiveness of admission processes.\\n\\n### B. Additional Sources (if applicable)  \\nAdditional sources include quantitative data regarding student demographics and retention rates. For instance, the retention rate of students at the institution with the most students is 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total number of students enrolled at this institution is 192,627, with a demographic breakdown showing 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total), along with 8,205 students whose gender is not specified. ',\n", " '(See Figure 1)',\n", " '',\n", " '## Data Gaps and Limitations\\n\\n- No data available on factors contributing to high enrollment numbers at this institution\\n- No results regarding trends or strategies that contribute to high enrollment in large institutions\\n- No data available to analyze the influence of online education and technology on enrollment numbers at large institutions\\n- No specific data results available to provide anecdotal evidence or case studies regarding technology integration to boost enrollment\\n- No results available for the programs or courses at the institution with the most students\\n- No specific data available to pinpoint the exact factors contributing to the popularity of certain programs or courses at large institutions\\n- No specific data available regarding the influence of the changing job market and technological advancements on new programs or courses\\n- No specific data available regarding the role of student feedback and industry partnerships in shaping the curriculum of popular programs\\n- No specific data available on how institutions gather and implement student feedback\\n- No available results to provide a demographic breakdown of this institution\\n- No data available to analyze the specific factors contributing to the gender distribution at this institution\\n- No available data on demographic factors such as ethnicity or socioeconomic status for the student population at this institution\\n- No available results regarding the changes in student enrollment at the institution with the most students over the past few years\\n- No specific data available to analyze student enrollment trends at large institutions\\n- No specific results available regarding the strategies or initiatives that institutions have implemented to enhance student support services\\n- No results available on student enrollment trends related to technology, online learning, or digital engagement\\n- No specific programs or initiatives listed that explain how this institution achieves its high retention rate\\n- No specific data available to directly analyze the factors contributing to overall student satisfaction at this institution\\n- No specific methods or approaches documented for gathering qualitative data from students regarding their experiences and satisfaction\\n- No specific data available regarding the challenges the institution might face in implementing qualitative data collection methods\\n- No results available for the student-to-faculty ratio of the institution with the most students\\n- No specific data available to analyze the factors contributing to the variation in student-to-faculty ratios among different institutions\\n- No results available regarding student-to-faculty ratios, indicating that data may not be readily accessible\\n- No specific data available regarding the influence of technology on the student-to-faculty ratio in large institutions\\n- No specific data available to directly analyze the impact of increasing reliance on technology on student-faculty interactions and overall learning outcomes\\n- No specific eligibility requirements or qualifications available for undergraduate and postgraduate programs at this institution\\n- No specific data results available regarding the common challenges that applicants face during the admission process at this institution\\n- No documented resources or support systems provided by the institution to assist applicants in navigating the admission process\\n- No documented resources or data available regarding the strategies employed by successful applicants at this institution',\n", " '\\n\\n## List of Figures\\n* **Figure 1** – Student Demographics By Gender']"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["report"]}, {"cell_type": "markdown", "id": "4df66ed2-e1cc-4011-a99e-29ab659245e7", "metadata": {}, "source": ["### OUTPUT"]}, {"cell_type": "code", "execution_count": 107, "id": "8fc785e0-5728-43b5-9452-96f7eb37fbab", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/markdown": ["## Introduction  \n", "The purpose of this report is to investigate and present findings on the institution with the highest student enrollment. The key answer to the guiding question is that the institution with the most students currently has a total enrollment of 192,627 students.\n", "## I. Overview of the Institution  \n", "\n", "### A. Total Enrollment  \n", "The current total number of students enrolled at the institution is 192,627.  \n", "\n", "### B. Demographic Breakdown  \n", "1. Gender Distribution  \n", "The gender distribution among the students is as follows: there are 96,457 male students, accounting for 58.5% of the total enrollment, and 87,965 female students, representing 53.5%. Additionally, there are 8,205 students whose gender is not specified.  \n", "\n", "\n", "Figure 1 – Student Demographics By Gender\n", "\n", "{'presentation_type': 'table', 'data': [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]}\n", "\n", "## II. Student Retention  \n", "- A. Retention Rate  \n", "   - 1. Current retention rate: 99.49%  \n", "   - 2. Implications of high retention rate  \n", "      The current retention rate of 99.49% indicates a very high level of student satisfaction and success in continuing their education at this institution. This high retention rate reflects positively on the institution's ability to meet the needs of its students, fostering an environment conducive to academic achievement and personal growth.\n", "## III. Admission Criteria  \n", "\n", "### A. Application Forms  \n", "1. **UEW Undergraduate Application Form (2022/2023)**  \n", "   - Availability: September 2022 - July 2026  \n", "   \n", "2. **UEW Postgraduate Application Forms**  \n", "   - Availability: September 2022 - July 2026  \n", "\n", "### B. Requirements for Prospective Students  \n", "1. The steps and requirements for prospective undergraduate and postgraduate students are outlined in the application forms.\n", "## IV. Implications of Findings  \n", "\n", "### A. Impact on Education Policy  \n", "The findings from the analysis of the institution with the most students highlight significant implications for education policy. With a total enrollment of 192,627 students, the institution demonstrates a robust demand for higher education. The high retention rate of 99.49% suggests that current educational policies are effectively supporting student satisfaction and success. This data can inform policymakers to continue or enhance existing support systems, ensuring that they meet the needs of a diverse student body, which includes 96,457 male students and 87,965 female students, along with 8,205 students whose gender is not specified. Such demographic insights are crucial for developing targeted initiatives that promote inclusivity and address the unique challenges faced by different student groups. \n", "(See Figure 1)\n", "\n", "\n", "### B. Influence on Future Enrollment Trends  \n", "The current enrollment figures and retention rates indicate a positive trend that may influence future enrollment strategies. The institution's ability to maintain a high retention rate suggests that prospective students are likely to view it as a desirable option for their education. As the admission criteria remain accessible through the active application forms for both undergraduate and postgraduate programs, it is anticipated that enrollment will continue to grow. The ongoing availability of these forms until July 2026 provides a stable framework for attracting new students, which could further enhance the institution's reputation and capacity.\n", "## <PERSON>. Conclusion  \n", "\n", "### <PERSON><PERSON> of Key Findings  \n", "The analysis reveals that the institution with the most students has a remarkable retention rate of 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total enrollment at this institution stands at 192,627 students. The demographic breakdown shows that there are 96,457 male students, accounting for approximately 58.5% of the total, and 87,965 female students, representing about 53.5%. Additionally, there are 8,205 students whose gender is not specified. \n", "\n", "### B. Implications for Future Research or Inquiry  \n", "These findings suggest a need for further research into the factors contributing to the high retention rate and student satisfaction. Understanding the demographics and their impact on educational outcomes could provide valuable insights for institutional strategies. Additionally, examining the effectiveness of the current admission criteria may yield opportunities for enhancing enrollment processes and student diversity. \n", "(See Figure 1)\n", "\n", "## VI. References  \n", "\n", "### A. Interview Transcripts/Summaries  \n", "The interview transcripts and summaries provide detailed insights into the institutional practices and student experiences. They highlight key themes such as student satisfaction, retention strategies, and the effectiveness of admission processes.\n", "\n", "### B. Additional Sources (if applicable)  \n", "Additional sources include quantitative data regarding student demographics and retention rates. For instance, the retention rate of students at the institution with the most students is 99.49%, indicating a very high level of student satisfaction and success in continuing their education. The total number of students enrolled at this institution is 192,627, with a demographic breakdown showing 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total), along with 8,205 students whose gender is not specified. \n", "(See Figure 1)\n", "\n", "## Data Gaps and Limitations\n", "\n", "- No data available on factors contributing to high enrollment numbers at this institution\n", "- No results regarding trends or strategies that contribute to high enrollment in large institutions\n", "- No data available to analyze the influence of online education and technology on enrollment numbers at large institutions\n", "- No specific data results available to provide anecdotal evidence or case studies regarding technology integration to boost enrollment\n", "- No results available for the programs or courses at the institution with the most students\n", "- No specific data available to pinpoint the exact factors contributing to the popularity of certain programs or courses at large institutions\n", "- No specific data available regarding the influence of the changing job market and technological advancements on new programs or courses\n", "- No specific data available regarding the role of student feedback and industry partnerships in shaping the curriculum of popular programs\n", "- No specific data available on how institutions gather and implement student feedback\n", "- No available results to provide a demographic breakdown of this institution\n", "- No data available to analyze the specific factors contributing to the gender distribution at this institution\n", "- No available data on demographic factors such as ethnicity or socioeconomic status for the student population at this institution\n", "- No available results regarding the changes in student enrollment at the institution with the most students over the past few years\n", "- No specific data available to analyze student enrollment trends at large institutions\n", "- No specific results available regarding the strategies or initiatives that institutions have implemented to enhance student support services\n", "- No results available on student enrollment trends related to technology, online learning, or digital engagement\n", "- No specific programs or initiatives listed that explain how this institution achieves its high retention rate\n", "- No specific data available to directly analyze the factors contributing to overall student satisfaction at this institution\n", "- No specific methods or approaches documented for gathering qualitative data from students regarding their experiences and satisfaction\n", "- No specific data available regarding the challenges the institution might face in implementing qualitative data collection methods\n", "- No results available for the student-to-faculty ratio of the institution with the most students\n", "- No specific data available to analyze the factors contributing to the variation in student-to-faculty ratios among different institutions\n", "- No results available regarding student-to-faculty ratios, indicating that data may not be readily accessible\n", "- No specific data available regarding the influence of technology on the student-to-faculty ratio in large institutions\n", "- No specific data available to directly analyze the impact of increasing reliance on technology on student-faculty interactions and overall learning outcomes\n", "- No specific eligibility requirements or qualifications available for undergraduate and postgraduate programs at this institution\n", "- No specific data results available regarding the common challenges that applicants face during the admission process at this institution\n", "- No documented resources or support systems provided by the institution to assist applicants in navigating the admission process\n", "- No documented resources or data available regarding the strategies employed by successful applicants at this institution\n", "\n", "\n", "## List of Figures\n", "* **Figure 1** – Student Demographics By Gender"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(Markdown(\"\\n\".join([str(item) for item in report])))"]}, {"cell_type": "code", "execution_count": 108, "id": "ab913536-9437-44ea-b2ca-7efbfa4d7df2", "metadata": {}, "outputs": [{"data": {"text/plain": ["(0.16107600000000005, 1068986)"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["model.get_total_cost(), model.get_total_tokens()"]}, {"cell_type": "code", "execution_count": 109, "id": "931c5cd0-9453-4656-bf74-7feef5a90df4", "metadata": {}, "outputs": [], "source": ["# run_query(\"SELECT * FROM core.academic_years\")"]}, {"cell_type": "code", "execution_count": 110, "id": "6f125d29-378a-4e8d-b28f-2a5489b08c52", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["{'outline': '## Introduction  \\nThe purpose of this report is to investigate and present findings on the institution with the highest student enrollment. The key answer to the guiding question is that the institution with the most students currently has a total enrollment of 192,627 students.',\n", " 'context': {'context': \"Question: What is the total number of students enrolled at the institution with the most students?\\nAnswer: The total number of students enrolled at the institution with the most students is 192,627.\\n\\nQuestion: What is the demographic breakdown of students at the institution with the most students?\\nAnswer: The institution with the most students has a total of 164,622 students, broken down by gender as follows: 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total). There are also 8,205 students whose gender is not specified.\\nData Tag: student_demographics_by_gender\\n\\nQuestion: What is the retention rate of students at the institution with the most students?\\nAnswer: The retention rate of students at the institution with the most students is 99.49%. This indicates a very high level of student satisfaction and success in continuing their education at this institution.\\n\\nQuestion: What are the admission criteria for the institution with the most students?\\nAnswer: The admission criteria for the institution with the most students include the following application forms: 1) the 'UEW UNDERGRADUATE APPLICATION FORM 2022/2023', which is currently active and available since September 2022 until July 2026, and 2) the 'UEW Postgraduate forms', also active from September 2022 to July 2026. These forms outline the necessary steps and requirements for prospective undergraduate and postgraduate students to apply for admission.\"}}"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["section_io[0]"]}, {"cell_type": "code", "execution_count": 111, "id": "8bb17385-176f-4f5c-af31-9995834ef909", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DEBUG:urllib3.connectionpool:http://*************:9200 \"POST /deep_research/_search?_source_includes=metadata,text HTTP/1.1\" 200 None\n", "INFO:elastic_transport.transport:POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.142s]\n"]}, {"data": {"text/plain": ["[Document(metadata={'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-07T11:35:11.914100+00:00', 'data_returned': True}, page_content='Question: What is the total number of students enrolled at the institution with the most students?\\nAnswer: The total number of students enrolled at the institution with the most students is 192,627.'),\n", " Document(metadata={'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-07T11:35:20.430413+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}, page_content='Question: What is the demographic breakdown of students at the institution with the most students?\\nAnswer: The institution with the most students has a total of 164,622 students, broken down by gender as follows: 96,457 male students (approximately 58.5% of the total) and 87,965 female students (approximately 53.5% of the total). There are also 8,205 students whose gender is not specified.'),\n", " Document(metadata={'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-07T11:35:31.144633+00:00', 'data_returned': True}, page_content='Question: What is the retention rate of students at the institution with the most students?\\nAnswer: The retention rate of students at the institution with the most students is 99.49%. This indicates a very high level of student satisfaction and success in continuing their education at this institution.'),\n", " Document(metadata={'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-07T11:35:15.163161+00:00', 'data_returned': True}, page_content=\"Question: What are the admission criteria for the institution with the most students?\\nAnswer: The admission criteria for the institution with the most students include the following application forms: 1) the 'UEW UNDERGRADUATE APPLICATION FORM 2022/2023', which is currently active and available since September 2022 until July 2026, and 2) the 'UEW Postgraduate forms', also active from September 2022 to July 2026. These forms outline the necessary steps and requirements for prospective undergraduate and postgraduate students to apply for admission.\")]"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["elastic_vector_search.as_retriever(search_kwargs={\"k\": 10,\n", "                                                  \"filter\": [\n", "                                                        {\"term\": {\"metadata.data_returned\": True}}\n", "                                                        ,\n", "                                                        {\"term\": {\"metadata.conversation_id.keyword\": question}}\n", "                                                    ]\n", "                                                 }).invoke(\n", "                                                        \"Give me a report on the topic: Which institution has the most students?\"\n", "                                                    )"]}, {"cell_type": "markdown", "id": "19a57f8b-0fec-45b4-add6-d0f63baf9814", "metadata": {}, "source": ["#### DELETE AN INDEX"]}, {"cell_type": "code", "execution_count": 192, "id": "75c8e889-7e49-49c2-a184-05265857dab0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DEBUG:urllib3.connectionpool:Starting new HTTP connection (1): *************:9200\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"HEAD /deep_research HTTP/1.1\" 200 0\n", "INFO:elastic_transport.transport:HEAD http://*************:9200/deep_research [status:200 duration:0.273s]\n", "DEBUG:urllib3.connectionpool:http://*************:9200 \"DELETE /deep_research HTTP/1.1\" 200 0\n", "INFO:elastic_transport.transport:DELETE http://*************:9200/deep_research [status:200 duration:0.220s]\n", "Index 'deep_research' deleted.\n"]}], "source": ["from elasticsearch import Elasticsearch\n", "\n", "es = Elasticsearch(\n", "    \"http://*************:9200\",\n", "    basic_auth=(\"elastic\", \"password\")\n", ")\n", "\n", "index_name = \"deep_research\"\n", "\n", "# 1) Check it exists (optional)\n", "if es.indices.exists(index=index_name):\n", "    # 2) Delete it\n", "    es.indices.delete(index=index_name)\n", "    print(f\"Index '{index_name}' deleted.\")\n", "else:\n", "    print(f\"Index '{index_name}' does not exist.\")"]}, {"cell_type": "code", "execution_count": null, "id": "f36fe942-24a1-44dc-a2c2-3631eea8151c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}
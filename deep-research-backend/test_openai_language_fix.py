"""
Test script to verify the OpenAI language attribute fix.

This script tests that the OpenAI provider properly handles the case where
the transcription response doesn't have a 'language' attribute.
"""

import asyncio
import io
import wave
import numpy as np
from app.speech.service import get_speech_service
from app.speech.factory import SpeechProviderType


def create_test_audio_file() -> io.BytesIO:
    """Create a simple test audio file (sine wave)."""
    # Generate a 1-second sine wave at 440 Hz
    sample_rate = 44100
    duration = 1.0
    frequency = 440.0
    
    # Generate samples
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    samples = np.sin(2 * np.pi * frequency * t)
    
    # Convert to 16-bit integers
    samples = (samples * 32767).astype(np.int16)
    
    # Create WAV file in memory
    audio_buffer = io.BytesIO()
    with wave.open(audio_buffer, 'wb') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 2 bytes per sample
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(samples.tobytes())
    
    audio_buffer.seek(0)
    return audio_buffer


async def test_openai_stt_no_language():
    """Test OpenAI STT without language parameter."""
    print("🎤 Testing OpenAI STT without language parameter...")
    
    try:
        service = get_speech_service(SpeechProviderType.OPENAI)
        
        # Create test audio
        audio_file = create_test_audio_file()
        
        # Test STT without language
        print("  Testing STT without language parameter...")
        result = await service.speech_to_text(
            audio_file=audio_file,
            language_code=None,  # No language provided
            tag_audio_events=False,
            diarize=False
        )
        
        print(f"  ✅ STT completed successfully")
        print(f"  Text: '{result.text}'")
        print(f"  Language: {result.language}")
        print(f"  Metadata: {result.metadata}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ STT test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_openai_stt_with_language():
    """Test OpenAI STT with language parameter."""
    print("\n🌐 Testing OpenAI STT with language parameter...")
    
    try:
        service = get_speech_service(SpeechProviderType.OPENAI)
        
        # Create test audio
        audio_file = create_test_audio_file()
        
        # Test STT with language
        print("  Testing STT with 'en' language parameter...")
        result = await service.speech_to_text(
            audio_file=audio_file,
            language_code="en",  # English language
            tag_audio_events=False,
            diarize=False
        )
        
        print(f"  ✅ STT completed successfully")
        print(f"  Text: '{result.text}'")
        print(f"  Language: {result.language}")
        print(f"  Metadata: {result.metadata}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ STT test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_provider_info():
    """Test provider information."""
    print("\n📋 Testing Provider Info...")
    
    try:
        service = get_speech_service(SpeechProviderType.OPENAI)
        
        provider_info = service.get_provider_info()
        print(f"  ✅ Provider: {provider_info['provider_type']}")
        print(f"  ✅ STT Available: {provider_info['stt_available']}")
        print(f"  ✅ TTS Available: {provider_info['tts_available']}")
        
        formats = service.get_supported_formats()
        print(f"  ✅ Supported formats: {formats}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Provider info test failed: {e}")
        return False


async def main():
    """Run all OpenAI language fix tests."""
    print("🚀 Starting OpenAI Language Fix Tests...\n")
    
    tests = [
        ("Provider Info", test_provider_info),
        ("STT without Language", test_openai_stt_no_language),
        ("STT with Language", test_openai_stt_with_language),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📊 OpenAI Language Fix Test Summary:")
    print("="*50)
    
    passed = 0
    total = len(tests)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All OpenAI language fix tests passed!")
        print("💡 The 'language' attribute error should now be resolved.")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

from pydantic import BaseModel, Field, field_validator
from typing import List, Dict, Any, Optional, Union

class ReportGenerationRequest(BaseModel):
    original_question: str = Field(..., min_length=1, max_length=1000, description="The original question to generate a report for")
    include_data_gaps: Optional[bool] = Field(True, description="Whether to include data gaps section in the report")

    @field_validator('original_question')
    @classmethod
    def validate_original_question(cls, v):
        if not v or not v.strip():
            raise ValueError('Original question cannot be empty or just whitespace')
        return v.strip()


class DataObject(BaseModel):
    """Represents a data object that can be embedded in the report"""
    presentation_type: str = Field(..., description="Type of data presentation (e.g., 'table', 'chart', 'graph')")
    data: Any = Field(..., description="The actual data content")


class ReportGenerationResult(BaseModel):
    outline: str
    sections: List[Union[str, DataObject, Dict[str, Any]]] = Field(
        ...,
        description="Array of report sections containing strings (markdown text) and data objects"
    )
    data: Dict[str, Any] = Field(
        ...,
        description="Extended data dictionary containing all data objects keyed by tag names"
    )


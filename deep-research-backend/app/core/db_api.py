import requests
import logging
import sys
import json
from .config import settings

# Configure logging at the beginning so you can see debug logs in the console.
# Level can be DEBUG or INFO depending on verbosity needs.
logging.basicConfig(stream=sys.stdout, level=logging.DEBUG)

def run_query(query: str):
    """
    Sends a single SQL query to the API endpoint and returns the JSON response.
    """
    # Log the query to see exactly what you're sending to the server
    logging.debug("Sending query:\n%s", query)

    payload = {"query": query}
    
    # Log the entire payload for clarity
    logging.debug("Payload to be sent: %s", json.dumps(payload, indent=2))

    try:
        response = requests.post(settings.db_query_api_url, json=payload)
        logging.debug("Response status code: %d", response.status_code)
        logging.debug("Raw response text: %s", response.text)

        # This will raise an HTTPError if the response was 4xx or 5xx
        response.raise_for_status()

        # Attempt to parse JSON from the response
        # If the server doesn't return valid JSON, this could raise a ValueError
        json_data = response.json()
        logging.debug("Parsed JSON from response: %s", json.dumps(json_data, indent=2))
        
        return json_data

    except requests.exceptions.HTTPError as e:
        logging.error("HTTP error occurred: %s", e)
        raise
    except ValueError as ve:
        logging.error("Could not parse JSON from the response. Error: %s", ve)
        logging.error("Response content was: %s", response.text)
        raise
    except Exception as ex:
        logging.error("An unexpected error occurred: %s", ex)
        raise

def run_query_silent(query: str):
    """
    Sends a single SQL query to the API endpoint and returns the JSON response.
    """
    # Log the query to see exactly what you're sending to the server
    payload = {"query": query}
    
    # try:
    response = requests.post(settings.db_query_api_url, json=payload)

    # This will raise an HTTPError if the response was 4xx or 5xx
    response.raise_for_status()

    # Attempt to parse JSON from the response
    # If the server doesn't return valid JSON, this could raise a ValueError
    json_data = response.json()
    # logging.debug("Parsed JSON from response: %s", json.dumps(json_data, indent=2))
    
    return json_data
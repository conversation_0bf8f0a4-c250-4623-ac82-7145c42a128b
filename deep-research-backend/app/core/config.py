from pydantic_settings import BaseSettings
from pydantic import Field

class Settings(BaseSettings):
    api_key: str
    db_query_api_url: str
    elasticsearch_url: str
    elasticsearch_user: str
    elasticsearch_password: str
    elasticsearch_index: str
    embedding_model: str
    redis_broker_url: str
    redis_backend_url: str
    elevenlabs_api_key: str
    openai_api_key: str

    class Config:
        env_file = ".env"

settings = Settings()

# print(settings.api_key)
# print(settings.db_query_api_url)

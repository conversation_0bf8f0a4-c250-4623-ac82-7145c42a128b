from celery import Celery
from app.core.config import settings  # your config/env loader

celery_app = Celery(
    "report_tasks",
    broker=settings.redis_broker_url,         # e.g. redis://localhost:6379/0
    backend=settings.redis_backend_url,        # optional but useful for result tracking
    include=["app.tasks.report_task"]
)

celery_app.conf.update(
    task_serializer="json",
    result_serializer="json",
    accept_content=["json"],
    timezone="UTC",
    enable_utc=True,
)

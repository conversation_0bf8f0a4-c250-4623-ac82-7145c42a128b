from app.celery_app import celery_app
from app.models.report import ReportGenerationRequest
from app.chains.composite.report_pipeline import generate_full_report, generate_streaming_report
import logging

logger = logging.getLogger(__name__)

@celery_app.task(name="generate_report")
def generate_report_task(payload: dict) -> dict:
    try:
        task_id = payload["task_id"]
        req = ReportGenerationRequest(**payload["request"])
        logger.info("starting...")
        return generate_full_report(req, task_id=task_id)
    except Exception as e:
        error_msg = f"Task execution error: {str(e)}"
        if "task_id" in payload:
            from app.utils.progress_tracker import push_progress
            push_progress(payload["task_id"], "error", extra={
                "error": error_msg
            })
        return {"error": error_msg}


@celery_app.task(name="generate_streaming_report")
def generate_streaming_report_task(payload: dict) -> dict:
    try:
        task_id = payload["task_id"]
        req = ReportGenerationRequest(**payload["request"])
        logger.info("starting streaming report generation...")
        return generate_streaming_report(req, task_id=task_id)
    except Exception as e:
        error_msg = f"Streaming task execution error: {str(e)}"
        if "task_id" in payload:
            from app.utils.progress_tracker import push_progress
            push_progress(payload["task_id"], "error", extra={
                "error": error_msg
            })
        return {"error": error_msg}

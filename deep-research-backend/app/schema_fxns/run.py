# # db_schema = get_db_schema()

# with open("categorical_cache_backup.json", encoding="utf-8") as f:
#     temp_cache = json.load(f)
# db_column_uniques = get_categorical_values()
# Path("db_categorical_summary.json").write_text(json.dumps(db_column_uniques, indent=2), encoding="utf-8")
# db_column_uniques = format_categorical_summary(db_column_uniques)
# Path("compact_db_categorical_summary.txt").write_text(db_column_uniques)
# # schema_compact = convert_schema_to_compact_format(db_schema)
# # Path("compact_db_structure.txt").write_text(schema_compact)
# # schema_JSON = process_schema_to_JSON(db_schema)
# # Path("full_db_structure.txt").write_text(json.dumps(schema_JSON, indent=2))
# # schema_graph = process_schema_to_graph(db_schema)
# # Path("full_db_graph.txt").write_text(json.dumps(schema_graph, indent=2))
import logging
import json

temp_cache = {}

def get_categorical_values():
    column_query = """
    SELECT
      table_schema,
      table_name,
      column_name,
      data_type
    FROM information_schema.columns
    WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
      AND (
          data_type LIKE 'char%' OR
          data_type LIKE 'varchar%' OR
          data_type LIKE 'text%' OR
          data_type LIKE 'nvarchar%' OR
          data_type LIKE 'nchar%' OR
          data_type LIKE 'enum%'
        )
      AND column_name NOT IN ('id')
      AND column_name NOT LIKE '%_id' ESCAPE '\'
    """

    column_results = run_query_silent(column_query)

    columns = [
        {k.lower(): v for k, v in row.items()}
        for row in column_results.get("data", [])
    ]

    if not columns:
        logging.warning("No string columns found.")
        return {}

    logging.info("Found %d categorical columns", len(columns))

    all_categorical_values = {}

    for col in columns:
        schema = col["table_schema"]
        table = col["table_name"]
        column = col["column_name"]
        key = f"{schema}.{table}"

        if key not in all_categorical_values:
            all_categorical_values[key] = {}
        if key not in temp_cache:
            temp_cache[key] = {}

        if column in temp_cache[key]:
            logging.info(f"Cache hit for {key}.{column}")
            all_categorical_values[key][column] = temp_cache[key][column]
            continue

        query = f'SELECT DISTINCT {column} FROM {schema}.{table} LIMIT 50'

        try:
            result = run_query_silent(query)
            values = [row[column] for row in result.get("data", [])]
            all_categorical_values[key][column] = values
            temp_cache[key][column] = values
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 502:
                logging.warning(f"502 Bad Gateway for {key}.{column}. Skipping...")
                continue  # Skip this column, continue loop
            else:
                logging.error(f"HTTP error for {key}.{column}: {e}")
                break  # Stop execution for other HTTP errors
        except Exception as e:
            logging.error(f"Unhandled error for {key}.{column}: {e}")
            break  # Stop execution for all other errors
        finally:
            try:
                with open("categorical_cache_backup.json", "w") as f:
                    json.dump(temp_cache, f, indent=2)
                logging.info("Saved temp_cache to 'categorical_cache_backup.json'")
            except Exception as write_error:
                logging.error(f"Could not write temp_cache to file: {write_error}")

    logging.info("Finished collecting categorical values.")
    logging.debug(json.dumps(all_categorical_values, indent=2))
    return all_categorical_values
    

def format_categorical_summary(categorical_data: dict) -> str:
    """
    Converts categorical column value dictionary into compact human-readable text format.

    Args:
        categorical_data (dict): The output from get_categorical_values().

    Returns:
        str: Formatted string representation.
    """
    lines = []
    for table_name, columns in categorical_data.items():
        lines.append(table_name)
        for column_name, values in columns.items():
            formatted_values = ', '.join(str(v) for v in values)
            lines.append(f"  - {column_name}: {formatted_values}")
        lines.append("")  # empty line between tables

    return "\n".join(lines)

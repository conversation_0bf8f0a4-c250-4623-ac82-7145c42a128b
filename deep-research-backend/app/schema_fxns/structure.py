import json

def get_db_schema():
    # A single large query that uses UNION ALL to gather tables, columns, and foreign-key info.
    # info_type tells us which portion of the union the row belongs to.
    # We filter out system databases: information_schema, mysql, performance_schema, sys.
    big_query = """
    SELECT
      'TABLE' AS info_type,
      table_schema,
      table_name,
      NULL AS column_name,
      NULL AS data_type,
      NULL AS is_nullable,
      NULL AS char_length,
      NULL AS column_default,
      NULL AS extra,
      NULL AS constraint_name,
      NULL AS referenced_schema,
      NULL AS referenced_table,
      NULL AS referenced_column
    FROM information_schema.tables
    WHERE table_type = 'BASE TABLE'
      AND table_schema NOT IN ('information_schema','mysql','performance_schema','sys')

    UNION ALL

    SELECT
      'COLUMN' AS info_type,
      table_schema,
      table_name,
      column_name,
      data_type,
      is_nullable,
      character_maximum_length,
      column_default,
      extra,
      NULL AS constraint_name,
      NULL AS referenced_schema,
      NULL AS referenced_table,
      NULL AS referenced_column
    FROM information_schema.columns
    WHERE table_schema NOT IN ('information_schema','mysql','performance_schema','sys')

    UNION ALL

    SELECT
      'FOREIGN_KEY' AS info_type,
      table_schema,
      table_name,
      column_name,
      NULL AS data_type,
      NULL AS is_nullable,
      NULL AS char_length,
      NULL AS column_default,
      NULL AS extra,
      constraint_name,
      referenced_table_schema,
      referenced_table_name,
      referenced_column_name
    FROM information_schema.key_column_usage
    WHERE referenced_table_name IS NOT NULL
      AND table_schema NOT IN ('information_schema','mysql','performance_schema','sys')

    ORDER BY table_schema, table_name
    """

    # 1) Send our single large query
    results = run_query(big_query)

    # Check if "rows" is actually the key that contains the returned data
    # It's possible your API might use a different key (like "data" or "Records").
    rows = results.get("data", [])

    # Log the length of rows to see if we actually got data
    logging.debug("Number of rows returned: %d", len(rows))

    # If you see zero rows, investigate whether "rows" is the correct key or if the DB actually returned nothing.
    if not rows:
        logging.warning("No rows were returned from the query. Check your query or API.")
    
    all_table_info = {}

    for row in rows:
        # Because each row is a dict, we access the fields by name
        info_type = row["info_type"]              # 'TABLE', 'COLUMN', or 'FOREIGN_KEY'
        schema_name = row["TABLE_SCHEMA"]
        table_name = row["TABLE_NAME"]

        key = (schema_name, table_name)
        if key not in all_table_info:
            all_table_info[key] = {
                "columns": [],
                "foreign_keys": []
            }

        if info_type == "TABLE":
            # This indicates it's just a table row; no extra columns to store right now
            pass
        elif info_type == "COLUMN":
            # Pull out the relevant fields
            col_info = {
                "column_name": row["column_name"],
                "data_type": row["data_type"],
                "is_nullable": row["is_nullable"],
                "char_length": row["char_length"],
                "column_default": row["column_default"],
                "extra": row["extra"]
            }
            all_table_info[key]["columns"].append(col_info)
        elif info_type == "FOREIGN_KEY":
            fk_info = {
                "constraint_name": row["constraint_name"],
                "column_name": row["column_name"],
                "referenced_schema": row["referenced_schema"],
                "referenced_table": row["referenced_table"],
                "referenced_column": row["referenced_column"]
            }
            all_table_info[key]["foreign_keys"].append(fk_info)

    # Convert (schema_name, table_name) to a simpler string key
    final_data = {}
    for (schema_name, table_name), info in all_table_info.items():
        key_str = f"{schema_name}.{table_name}"
        final_data[key_str] = info

    logging.info("Final schema info:")
    logging.info(json.dumps(final_data, indent=2))
    return final_data

def process_schema_to_JSON(schema_dict):
    """
    Takes the raw schema dictionary (from get_db_schema) and returns a more
    concise, text-based representation:
    {
      "schema.table": {
         "columns": [
            "column_name (data_type, default=..., nullable=..., extra=...)", ...
         ],
         "foreign_keys": [
            "column_name -> referenced_schema.referenced_table(referenced_column) [constraint=...]"
         ]
      }, ...
    }
    """
    processed = {}

    for table_key, info in schema_dict.items():
        # Make a short string for each column
        compact_columns = []
        for col in info["columns"]:
            col_name = col["column_name"]
            data_type = col["data_type"]
            default_val = col["column_default"]
            is_nullable = col["is_nullable"]
            char_len = col["char_length"]
            extra = col["extra"]

            # Build a short text summary
            # Example: "status (SMALLINT, default=0, nullable=NO, extra=auto_increment)"
            # Skip items if they are None or empty, to keep it concise
            parts = [f"{col_name} ({data_type}"]
            if char_len is not None:
                parts[-1] += f"({char_len})"
            # Close the parenthesis
            parts[-1] += ")"

            # Add default if present
            if default_val is not None:
                parts.append(f"default={default_val}")
            # Mark if it's nullable or not
            parts.append(f"nullable={is_nullable}")
            if extra:
                parts.append(f"extra={extra}")

            compact_columns.append(", ".join(parts))

        # Make a short string for each foreign key
        compact_fks = []
        for fk in info["foreign_keys"]:
            col_name = fk["column_name"]
            c_name = fk["constraint_name"]
            r_schema = fk["referenced_schema"]
            r_table = fk["referenced_table"]
            r_column = fk["referenced_column"]

            # Example: "col_name -> r_schema.r_table(r_column) [constraint=XYZ]"
            fk_str = f"{col_name} -> {r_schema}.{r_table}({r_column})"
            if c_name:
                fk_str += f" [constraint={c_name}]"
            compact_fks.append(fk_str)

        processed[table_key] = {
            "columns": compact_columns,
            "foreign_keys": compact_fks
        }
    return processed


def process_schema_to_graph(schema_dict):
    """
    Takes the raw schema dictionary (from get_db_schema) and returns a graph-like structure.
    
    The output format is:
    
    {
      "nodes": [
         {
            "id": "schema.table",         # Unique identifier (schema and table name)
            "columns": [
                {
                   "name": "column_name",
                   "data_type": "data_type",
                   "default": "column_default",
                   "nullable": True or False,
                   "char_length": <number or None>,
                   "extra": "extra"
                },
                ...
            ]
         },
         ...
      ],
      "edges": [
         {
            "source": "schema.table",      # Table that holds the foreign key
            "target": "ref_schema.ref_table", # Referenced table
            "column": "local_column",        # Column in the source table
            "referenced_column": "ref_column", # Column in the referenced table
            "constraint": "constraint_name"  # Optional constraint name
         },
         ...
      ]
    }
    """
    nodes = []
    edges = []
    
    for table_key, info in schema_dict.items():
        # Create node for each table
        node = {"id": table_key, "columns": []}
        for col in info["columns"]:
            # Determine nullable: our schema dictionary uses "YES" or "NO"
            nullable = (col.get("is_nullable", "NO").upper() == "YES")
            node["columns"].append({
                "name": col.get("column_name"),
                "data_type": col.get("data_type"),
                "default": col.get("column_default"),
                "nullable": nullable,
                "char_length": col.get("char_length"),
                "extra": col.get("extra")
            })
        nodes.append(node)
        
        # Create edges for foreign keys
        for fk in info["foreign_keys"]:
            # Build target table key from referenced schema and table name.
            target_table = f"{fk.get('referenced_schema')}.{fk.get('referenced_table')}"
            edge = {
                "source": table_key,
                "target": target_table,
                "column": fk.get("column_name"),
                "referenced_column": fk.get("referenced_column"),
                "constraint": fk.get("constraint_name")
            }
            edges.append(edge)
    
    return {"nodes": nodes, "edges": edges}

def convert_processed_json_to_compact_format(processed_json):
    """
    Converts processed schema JSON (from process_schema_to_JSON) into a LangChain-friendly schema string.
    Format:
        users(id, name, email)
        posts(id, user_id -> users.id, title)
    """
    lines = []

    for full_table_name, info in processed_json.items():
        table_name = full_table_name.split(".")[1]  # Drop schema prefix if needed

        column_names = []
        fk_columns = set()

        # Parse foreign key references
        for fk in info.get("foreign_keys", []):
            parts = fk.split(" -> ")
            if len(parts) == 2:
                col_name = parts[0].strip()
                target = parts[1].split("(")[0].strip()  # e.g. users.id
                target = target.replace(".", ".")        # keep schema.table
                column_names.append(f"{col_name} -> {target}")
                fk_columns.add(col_name)

        # Add other columns (excluding foreign key columns already added)
        for col in info.get("columns", []):
            col_name = col.split(" ")[0].strip()  # Take the name before the opening parenthesis
            if col_name not in fk_columns:
                column_names.append(col_name)

        line = f"{table_name}({', '.join(column_names)})"
        lines.append(line)

    return "\n".join(lines)

def convert_schema_to_compact_format(schema_dict):
    """
    Converts a detailed schema dictionary (from get_db_schema) into a compact string format.
    Example output:
        users(id, name, email, created_at)
        posts(id, user_id -> users.id, title, content, published)
    """
    lines = []

    for full_table_name, info in schema_dict.items():
        table_name = full_table_name # Drop schema prefix (e.g., 'public.users' -> 'users')

        column_defs = []
        fk_map = {
            fk["column_name"]: f'{fk["column_name"]} -> {fk["referenced_table"]}.{fk["referenced_column"]}'
            for fk in info.get("foreign_keys", [])
        }

        for col in info.get("columns", []):
            col_name = col["column_name"]
            if col_name in fk_map:
                column_defs.append(fk_map[col_name])
            else:
                column_defs.append(col_name)

        line = f"{table_name}({', '.join(column_defs)})"
        lines.append(line)

    return "\n".join(lines)
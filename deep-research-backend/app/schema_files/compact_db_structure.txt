auth.institution_app_clients(id, institution_id, app_name, client_id, client_secret, revoked, created_at, updated_at)
auth.institutions(id, name, short_name, email, phone, address, website, digital_address, api_url, sip_url, path, host, created_at, updated_at)
core.Untitled(id)
core.academic_years(id, institution_id, start_year, end_year, status, created_at, updated_at)
core.activity_log(id, institution_id, log_name, description, subject_id, subject_type, causer_id, causer_type, student_id, ip_address, user_agent, properties, created_at, updated_at)
core.adhoc_bill_item_types(id, institution_id, name, created_at, updated_at)
core.adhoc_bill_items(id, bill_id -> bills.id, adhoc_bill_item_type_id -> adhoc_bill_item_types.id, amount, currency_id, created_at, updated_at, deleted_at)
core.admission_batches(id, institution_id, academic_year_id, doa, doc, doa_month, doa_year, doc_month, doc_year, is_start_year_inclusive, status, is_child, parent_id, is_merged, merge_level, created_at, upated_at, deleted_at)
core.admission_forms(id, institution_id, admission_batch_id, description, total_referees, total_program_options, show_results, show_education, show_employment, start_date, end_date, status, is_program_configured, is_campus_stream_configured, workflow_form_id, created_at, updated_at)
core.admission_status_tokens(id, institution_id, applicant_id, recipient, token, created_at, updated_at)
core.affiliations(id, institution_id, short_name, long_name, created_at, updated_at)
core.allotments(id, institution_id, student_id -> students.id, billing_period_id -> billing_periods.id, receipt_no, bill_item_type_id -> bill_item_types.id, currency_id -> currencies.id, amount, created_at, updated_at)
core.alt1_bill_categories(id, transflow_alt1_id, bill_category_id)
core.app_clients(id, institution_id, client_id, name, secret, revoked, created_at, updated_at)
core.applicant_answers(id, applicant_id, form_question_id, answer, created_at, updated_at)
core.applicant_attachments(id, applicant_id, form_attachment_type_id, filename, path, created_at, updated_at)
core.applicant_contact_types(id, instittuion_id, type, created_at, updated_at)
core.applicant_contacts(id, applicant_id, applicant_contact_type_id, name, address, mobile, email, occupation, created_at, updated_at)
core.applicant_education(id, applicant_id, school, start_date, end_date, certificate, qualification, class, awarded_at, created_at, updated_at)
core.applicant_employment(id, applicant_id, institution, start_date, end_date, position, created_at, updated_at)
core.applicant_exam_aggregate(id, applicant_id, aggregate, failure_message)
core.applicant_exam_re_entry_tokens(id, applicant_id, token)
core.applicant_exam_results(id, applicant_id, applicant_exam_id, subject_id, grade, created_at, updated_at)
core.applicant_exams(id, applicant_id, exam_type_id, index_number, date, aggregate, created_at, updated_at)
core.applicant_guardian_types(id, type, created_at, updated_at)
core.applicant_guardians(id, applicant_id, applicant_guardian_type_id, name, address, mobile, email, occupation, created_at, updated_at)
core.applicant_halls(id, applicant_id, hall_id, created_at, updated_at)
core.applicant_identities(id, applicant_id, applicant_identity_type_id, number, expiry, url, created_at, updated_at)
core.applicant_identity_types(id, name, created_at, updated_at)
core.applicant_info(prefix, surname, othernames, gender, program_applied_for, program_offered, program_short_name, email, demonym, admission_year, applicant_status, decision_on_applicant, mobile, campus, stream, entry_mode, applicantion_source, dob, regnumber, temp_number, contact_name, contact_address, contact_mobile_number, guardian_name, guardian_address, guardian_mobile_number, form_name, extra_data)
core.applicant_meta_values(id, applicant_id, student_meta_column_id, value, created_at, updated_at)
core.applicant_photos(id, applicant_id, filename, path, created_at, updated_at)
core.applicant_programs(id, applicant_id, program_id, choice, aggregate, message, created_at, updated_at)
core.applicant_referees(id, applicant_id, name, address, mobile, email, occupation, email_status, created_at, updated_at)
core.applicants(id, institution_id, surname, othernames, prefix, suffix, sex, dob, nationality_id, mobile, email, form_id, created_at, updated_at, decision, regnumber, temp_number, entrance_number, is_student, student_id, program_id, entry_level, campus_id, stream_id, entry_mode_id, alumni_regnumber, offer_letter_template_id, letter_last_printed_at, date_of_issue, study_center_id, source, status, aggregate)
core.assessment_approved_results(id, institution_id, semester_id -> semesters.id, course_code, course_title, course_credits, level, assessment_result_id -> assessment_results.id, assessment_sheet_id -> assessment_sheets.id, student_id -> students.id, student_program_id -> student_programs.id, grade, assessment_total, exam_total, finalscore, gp, position, remarks, registration_source, created_at, updated_at)
core.assessment_column_scores(id, institution_id, assessment_sheet_column_id -> assessment_sheet_columns.id, student_id -> students.id, score, raw_score, count_changes, created_at, updated_at)
core.assessment_result_remarks(id, institution_id, assessment_result_id -> assessment_results.id, user_id -> users.id, remarks, created_at, modified_at)
core.assessment_results(id, institution_id, previous_assessment_result_id, re_registered, assessment_sheet_id -> assessment_sheets.id, semester_id, course_id, student_id -> students.id, student_program_id -> student_programs.id, grade, assessment_total, exam_total, finalscore, gp, position, remarks, registration_source, created_at, updated_at)
core.assessment_sheet_columns(id, institution_id, assessment_sheet_id, number, name, value, max_raw_score, description, colour, type, created_at, updated_at)
core.assessment_sheet_group_settings(id, institution_id, semester_id, course_id, method, auto_group_by, quota, max_groups, status, created_at, updated_at)
core.assessment_sheet_groups(id, institution_id, assessment_sheet_group_setting_id, groupname, semester_id, course_id, course_type, student_count, created_at, updated_at)
core.assessment_sheet_stages(id, institution_id, assessment_sheet_id, assessment_sheet_status_id -> assessment_sheet_statuses.id, user_id -> users.id, remarks, created_at, updated_at)
core.assessment_sheet_statuses(id, institution_id, position, label, description, permitted_roles, permitted_actions, created_at, updated_at)
core.assessment_sheets(id, institution_id, semester_id -> semesters.id, course_id -> courses.id, course_type, assessment_sheet_group_id -> assessment_sheet_groups.id, grading_scheme_id -> grading_schemes.id, created_at, updated_at, assessment_sheet_status_id, approved, unit_id, level)
core.bank_integrations(id, institution_id, integration_type, bank_id, status, created_at, updated_at)
core.banks(id, institution_id, name, short_code, swift_code, address, phone_numbers, email, created_at, updated_at, deleted)
core.bill_categories(id, institution_id, program_id -> programs.id, doa, doc, level, campus_id -> campuses.id, stream_id -> streams.id, nationality_type, entry_mode_id, created_at, updated_at)
core.bill_item_ratios(id, institution_id, bill_item_id, ratio)
core.bill_item_types(id, institution_id, name, priority, created_at, updated_at)
core.bill_items(id, bill_id -> bills.id, bill_item_type_id -> bill_item_types.id, amount, currency_id, created_at, updated_at, deleted_at)
core.billing_periods(id, institution_id, description, group_determinant, start_date, end_date, status, created_at, updated_at)
core.bills(id, institution_id, billing_period_id -> billing_periods.id, bill_category_id -> bill_categories.id, total_due, currency_id, created_at, updated_at)
core.campus_types(id, institution_id, name, created_at, updated_at)
core.campus_types_copy1(id, institution_id, name, created_at, updated_at)
core.campuses(id, institution_id, name, location, campus_type_id -> campus_types.id, status, created_at, updated_at)
core.case_attachments(id, case_id -> cases.id, file_path, created_at)
core.case_categories(id, name, description, created_at, updated_at)
core.case_correspondence(id, case_id -> cases.id, contact_email, message, attachments, type, reopen_case, created_at, updated_at)
core.case_severities(name)
core.cases(id, code, case_category_id -> case_categories.id, module_id, institution_id -> institutions.id, title, description, requested_by, severity, status, created_at, updated_at)
core.complementary_scheme_grades(id, institution_id, complementary_scheme_id, grade, created_at, updated_at)
core.complementary_schemes(id, institution_id, complementary_scheme_name, description, created_at, updated_at)
core.course_lecturers(id, course_id -> courses.id, lecturer_staff_id -> staff.id, created_at, updated_at)
core.course_prerequisites(id, course_id -> courses.id, prerequisite_course_id -> courses.id, created_at, updated_at)
core.courses(id, institution_id, code, title, credits, level, unit_id -> units.id, semester, status, parent_id -> courses.id, created_at, updated_at)
core.currencies(id, code, description, created_at, updated_at)
core.currency_exchange(id, institution_id, base_currency_id, foreign_currency_id, exchange_rate, inverse_rate, effective_date, created_at, updated_at)
core.designations(id, name, description, created_at, updated_at)
core.document_requests(id, institution_id, invoice, student_id, student_program_id, student_letter_template_id, copies, description, currency_id, amount, status, created_at, updated_at)
core.email_integration_password_format(id, institution_id, prefix, type, separator, order, alternative, created_at, updated_at)
core.email_validation_tokens(id, institution_id, subject_id, token)
core.entrance_exam_scores(id, institution_id, applicant_id, score, created_at, updated_at)
core.entry_modes(id, institution_id, entry_mode, type, created_at, updated_at)
core.evaluation_form_answers(id, institution_id, user_id, course_id, evaluation_id, student_program_id, student_id, answers, created_at, updated_at)
core.evaluation_form_question_options(id, institution_id, evaluation_section_id -> evaluation_sections.id, evaluation_form_section_question_id -> evaluation_form_section_questions.id, order, question_option)
core.evaluation_form_section_questions(id, institution_id -> evaluation_sections.institution_id, evaluation_section_id -> evaluation_sections.id, question, answer_type, hints, required, created_at, updated_at)
core.evaluation_forms(id, institution_id, name, form_instruction, created_at, updated_at)
core.evaluation_sections(id, institution_id -> evaluation_forms.institution_id, evaluation_form_id -> evaluation_forms.id, section, order, created_at, updated_at)
core.evaluations(id, institution_id, evaluation_form_id, semester_id, start_date, end_date, status, created_at, updated_at)
core.exam_grades(id, exam_type_id, grade, weight, order, created_at, updated_at)
core.exam_subjects(id, exam_type_id, subject, type, created_at, updated_at)
core.exam_types(id, institution_id, total_core_for_aggregate, total_elective_for_aggregate, type, status, created_at, updated_at)
core.failed_jobs(id, institution_id, connection, queue, payload, exception, failed_at)
core.file_download_requests(id, reference_number, institution_id, type, meta_data, status, created_at, updated_at)
core.file_import_logs(id, institution_id, student_import_id -> file_imports.id, status, description, created_at, updated_at)
core.file_imports(id, institution_id, reference_no, path, filename, mime_type, import_type, status, created_at, updated_at)
core.financial_aid_request_students(institution_id, student_program_id -> student_programs.id, financial_aid_request_reference_number, currency_id, amount, student_transaction_id, created_at)
core.financial_aid_requests(institution_id, reference_number, billing_period_id -> billing_periods.id, type, description, currency_id -> currencies.id, amount, requested_by, approved_by, status, type_extra_data, created_at, updated_at)
core.form_attachment_types(id, institution_id, type, created_at, updated_at)
core.form_meta_columns(id, institution_id, form_id, type, label, default_value, option_values, created_at, updated_at)
core.form_program_qualification(id, form_program_id -> form_programs.id, exam_type_id -> exam_types.id, least_grade_id -> exam_grades.id, cutoff_aggregate, created_at, updated_at)
core.form_program_qualification_subjects(id, form_program_qualification_id -> form_program_qualification.id, exam_subject_id -> exam_subjects.id, type, min_grade, created_at, updated_at)
core.form_programs(id, admission_form_id, program_id, created_at, updated_at)
core.form_question_categories(id, institution_id, category, order, created_at, updated_at)
core.form_question_options(id, form_question_id, question_option, created_at, updated_at)
core.form_questions(id, institution_id, admission_form_id, form_question_category_id, order, question, answer_type, options, hints, required, status, created_at, updated_at)
core.grade_changes(id, institution_id, student_id -> students.id, status, user_id -> users.id, assessment_result_id, request_data, action, created_at, updated_at)
core.grade_options(id, value, label, type)
core.grading_scheme_complements(id, grading_scheme_id, complementary_scheme_id, created_at, updated_at)
core.grading_scheme_gpas(id, institution_id, grading_scheme_id, gpa, class, order, created_at, updated_at)
core.grading_scheme_maps(id, scheme_grade_id, complementary_scheme_grade_id, created_at, updated_at)
core.grading_scheme_programs(id, institution_id, grading_scheme_id, program_id, created_at, updated_at)
core.grading_schemes(id, institution_id, scheme_name, assessment_total, exam_total, description, status, created_at, updated_at)
core.graduation_batches(id, institution_id, cohort, status, created_at, updated_at)
core.graduation_bill_item_types(id, institution_id, name, created_at, updated_at)
core.graduation_bill_items(id, institution_id, graduation_bill_id -> graduation_bills.id, graduation_bill_item_type_id, currency_id, amount, created_at, updated_at)
core.graduation_bills(id, institution_id, billing_period_id, graduation_batch_id, currency_id, total_due, created_at, updated_at)
core.graduation_clearance(id, institution_id, student_id, student_program_id, status, created_at, updated_at)
core.graduation_report(id, institution_id, graduation_batch_id, user_id, status, created_at, updated_at)
core.halls(id, institution_id, name, location, address, created_at, updated_at)
core.historical_upload_file_assessment_sheets(id, historical_upload_file_id -> historical_upload_files.id, assessment_sheet_id -> assessment_sheets.id, created_at, updated_at)
core.historical_upload_files(id, institution_id, historical_upload_session_id -> historical_upload_sessions.id, reference_no, filename, path, status, meta_data, created_at, updated_at)
core.historical_upload_sessions(id, institution_id, end_date, status, users_involved, historical_semester_involved, requested_by, approved_by, created_at, updated_at)
core.ibas_device_venues(device_id -> ibas_devices.device_id, venue_id -> venues.id, created_at, updated_at)
core.ibas_devices(device_id, institution_id, created_at)
core.ibas_failed_staff_attendance(id, email, institution_id, device_id, date, time, timetable_id, timetable_placement_id, venue_name, course_code, start_time, end_time, attempts, created_at, updated_at)
core.ibas_staff_attendance(id, email, institution_id, device_id, date, time, timetable_id, timetable_placement_id, venue_name, course_code, start_time, end_time, created_at, updated_at)
core.institution_setting_values(id, institution_id, setting_id, value)
core.institution_third_party_products(id, institution_id, student_transaction_type_id, source, product_id, created_at)
core.institutions(id, NAME, host, merchant_id, workflow_institution_id, path)
core.jobs(id, institution_id, queue, payload, attempts, reserved_at, available_at, created_at)
core.lecturer_assessment_sheet_columns(id, institution_id, lecturer_assessment_sheet_id, assessment_sheet_column_id, user_id, staff_id, column_status, created_at, updated_at)
core.lecturer_assessment_sheets(id, institution_id, assessment_sheet_id, user_id, created_at, updated_at, status)
core.levels(id, institution_id, value, label, next_level)
core.migrations(id, institution_id, migration, batch)
core.model_has_permissions(institution_id, permission_id -> permissions.id, model_type, model_id)
core.model_has_roles(role_id -> roles.id, model_type, model_id)
core.modules(id, name, description, case_category_id, created_at, updated_at)
core.multi_factor_auths(id, institution_id, auth_mode, auth_user, auth_code, message, expire, validated, created_at, updated_at)
core.nationalities(id, code, name, demonym, status)
core.notifications(id, institution_id, type, notifiable_type, notifiable_id, data, read_at, sent, created_at, updated_at)
core.oauth_access_tokens(id, institution_id, user_id, client_id, name, scopes, revoked, created_at, updated_at, expires_at)
core.oauth_auth_codes(id, institution_id, user_id, client_id, scopes, revoked, expires_at)
core.oauth_clients(id, institution_id, user_id, name, secret, redirect, personal_access_client, password_client, revoked, created_at, updated_at)
core.oauth_personal_access_clients(id, institution_id, client_id, created_at, updated_at)
core.oauth_refresh_tokens(id, institution_id, access_token_id, revoked, expires_at)
core.offer_letter_template_cc(id, institution_id, offer_letter_template_id, email, created_at, updated_at)
core.offer_letter_template_search_keys(id, institution_id, offer_letter_template_id, program_id, stream_id, campus_id, nationality, entry_mode_id, study_center_id, created_at, updated_at)
core.offer_letter_template_tags(id, institution_id, tag, description, field, created_at, updated_at)
core.offer_letter_templates(id, institution_id, name, decision, print_content, email_content, sms_content, created_at, updated_at)
core.one_time_pins(id, institution_id, type, channel, channel_id, pin, verified_at, created_at, updated_at)
core.operation_exceptions(id, institution_id, type, value, created_at, updated_at)
core.options(id, institution_id, subject_name, created_at, updated_at)
core.password_resets(id, institution_id, email, token, created_at, updated_at)
core.permissions(id, name, guard_name, description, mutually_exclusive_permissions, created_at, updated_at)
core.program_affiliations(id, institution_id, program_id, affiliation_id, created_at, updated_at)
core.program_options(id, institution_id, program_id -> programs.id, major_option_id, minor_option_id, created_at, updated_at)
core.program_plan_additions(id, institution_id, program_plan_semester_id -> program_plan_semesters.id, program_plan_id -> program_plans.id, course_id -> courses.id, course_type, created_at, updated_at)
core.program_plan_courses(id, institution_id, program_plan_semester_id -> program_plan_semesters.id, program_plan_id -> program_plans.id, program_option_id -> program_options.id, course_id -> courses.id, course_type, created_at, updated_at)
core.program_plan_semesters(id, program_plan_id -> program_plans.id, level, period, minimum_credits, maximum_credits, created_at, updated_at)
core.program_plans(id, institution_id, name, program_id, minimum_credit, created_at, updated_at)
core.program_plans_info(plan_name, program_name, major, level, period, code, title, credits, course_type)
core.programs(id, institution_id, unit_id, short_name, long_name, programme_type, duration, duration_unit, entry_level, exit_level, status, created_at, updated_at)
core.provisional_results(surname, othernames, program, program_short_name, regnumber, level, semester, code, title, credits, assessment_total, exam_total, finalscore, grade, gp, campus, stream, doa, doc)
core.published_results(surname, othernames, regnumber, level, semester, program, program_short_name, course_code, course_title, course_credits, assessment_total, exam_total, finalscore, grade, gp, campus, stream, doa, doc)
core.query_keywords(id, keyword, comments)
core.recommendation_answers(id, institution_id, applicant_id, applicant_referee_id, recommendation_form_id, file_path, answers, created_at, updated_at)
core.recommendation_forms(id, institution_id, name, form_instruction, created_at, updated_at)
core.recommendation_question_options(id, institution_id, recommendation_question_id -> recommendation_questions.id, order, question_option)
core.recommendation_questions(id, institution_id, recommendation_form_id, order, question, answer_type, hints, required, created_at, updated_at)
core.referee_letter_templates(id, institution_id, admission_form_id, recommendation_form_id, name, email_content, created_at, updated_at)
core.regions(id, nationality_id, name, created_at, updated_at)
core.registration_quota_exemptions(id, institution_id, student_id, semester_id, registration_schedule_id, created_at, updated_at)
core.registration_schedule_programs(id, registration_schedule_id -> registration_schedules.id, program_id -> programs.id, active, created_at, updated_at)
core.registration_schedules(id, institution_id, doc, stream_id -> streams.id, semester_id -> semesters.id, penalty, resits, active, start_date, end_date, program_count, fresher_payment_quota, continuing_payment_quota, registration_mode, created_at, updated_at)
core.regnumber_codes(id, institution_id, type, type_id, code, created_at, updated_at)
core.regnumber_preferences(id, institution_id, regnumber_type, generation_time, generation_status, serial_length, created_at, updated_at)
core.regnumber_prefix_categories(id, institution_id, name, program_type, created_at, updated_at)
core.regnumber_prefixes(id, regnumber_prefix_category_id, prefix, type, separator, order, alternative, created_at, updated_at)
core.regnumber_serial_ranges(id, institution_id, program_id, start_limit, end_limit, created_at, updated_at)
core.regnumber_suffixes(id, regnumber_prefix_category_id, suffix, type, separator, order, alternative, created_at, updated_at)
core.resit_transactions(id, institution_id, receipt_number, student_id -> students.id, semester_id -> semesters.id, course_id -> courses.id, balance, created_at, updated_at)
core.role_has_permissions(permission_id -> permissions.id, role_id -> roles.id)
core.roles(description, is_mandate, mandate_start_dt, mandate_end_dt, created_at, updated_at, id, institution_id, type, name, guard_name, user_count)
core.scheme_grades(id, institution_id, grading_scheme_id, grade, lower_limit, gp, type, created_at, updated_at)
core.school_list(id, name, type)
core.semester_bill_totals(billing_period, start_date, end_date, currency, total_bill)
core.semester_types(id, name, periods, promote_on_period, created_at, updated_at)
core.semesters(id, institution_id, semester_type_id, start_year, end_year, period, start_date, end_date, status, next_semester_id, academic_year_id, created_at, updated_at)
core.setting_categories(id, name, position, belongs_to, created_at, updated_at)
core.settings(id, setting_category_id, parent_id, _lft, _rgt, type, name, description, default_value, option_data, position, editable, created_at, updated_at)
core.sheet_assignment(code, title, credits, groupname, semester, lecturer_assigned, campus, student_roll)
core.sip_mobile_app_versions(id, name, version, status, created_at)
core.sip_mobile_push_notifications(institution_id, topic, title, message, receiver_type, request_data, created_at)
core.sip_password_resets(id, institution_id, regnumber, email, token, created_at, updated_at)
core.sip_transcript_layout_programs(id, institution_id, layout, program_id, campus_id, created_at, updated_at)
core.sip_user_tokens(id, institution_id, sip_user_id, token, expiry, created_at, updated_at)
core.sip_users(id, institution_id, student_id -> students.id, regnumber, email, email_verified_at, password, remember_token, two_factor, status, login_count, is_logged_in, last_activity, expiry, created_at, updated_at)
core.sms_notifications(id, institution_id, recipient_type, recipient_type_id, recipient, message, created_at)
core.staff(id, prefix, suffix, surname, othernames, email, mobile, designation_id, path, filename, mime_type, created_at, updated_at)
core.staff_institutions(id, staff_id -> staff.id, institution_id)
core.staff_units(id, institution_id, staff_id -> staff.id, unit_id -> units.id, created_at, updated_at)
core.stream_campuses(id, form_program_id, stream_id, campus_id, created_at, updated_at)
core.streams(id, institution_id, abbr, name, created_at, updated_at)
core.student_affiliation_numbers(id, institution_id, student_id, student_program_id, affiliation_number, student_regno_type_id, affiliation_id, created_at, updated_at)
core.student_award_types(id, name, created_at, updated_at)
core.student_awards(id, institution_id, student_id -> students.id, student_award_type_id, name, semester_id, created_at, updated_at)
core.student_balances(id, institution_id, student_id -> students.id, student_program_id -> student_programs.id, student_transaction_type_id, billing_period_id -> billing_periods.id, currency_id -> currencies.id, balance, created_at, updated_at)
core.student_banks(id, institution_id, bank_integration_id, student_id, request_data, status, created_at, updated_at)
core.student_bill_info(transaction_date, transaction_description, billing_period, regnumber, currency, transaction_amount)
core.student_bills(id, institution_id, student_id -> students.id, student_program_id -> student_programs.id, bill_id -> bills.id, created_at, updated_at)
core.student_cgpas(id, institution_id, student_id, student_program_id, total_cumulative_gp, total_cumulative_credit, total_cumulative_courses, cgpa, class, awarded_class, reason_for_awarded_class, status, created_at, updated_at)
core.student_contact_types(id, name, created_at, updated_at)
core.student_contacts(id, institution_id, student_id -> students.id, student_contact_type_id, number, created_at, updated_at)
core.student_current_bal(regnumber, surname, othernames, level, program, student_transaction_type, statement_balance)
core.student_email_format(id, institution_id, prefix, type, separator, order, alternative, created_at, updated_at)
core.student_file_types(id, name, created_at, updated_at)
core.student_files(id, institution_id, student_id -> students.id, student_file_type_id, name, desc, reference_no, path, filename, mime_type, created_at, updated_at)
core.student_graduation_bills(id, institution_id, student_id -> students.id, student_program_id -> student_programs.id, graduation_bill_id -> graduation_bills.id, created_at, updated_at)
core.student_guardian_types(id, name, created_at, updated_at)
core.student_guardians(id, institution_id, student_id -> students.id, student_guardian_type_id, name, address, mobile, email, created_at, updated_at)
core.student_halls(id, institution_id, student_id, hall_id, created_at, updated_at)
core.student_identities(id, institution_id, student_id -> students.id, student_identity_type_id -> student_identity_types.id, number, expiry, url, created_at, updated_at)
core.student_identity_types(id, name, created_at, updated_at)
core.student_info(surname, othernames, prefix, suffix, sex, dob, nationality, mobile, email, student_status, regnumber, affiliation_number, program, entry_level, level, doa, doc, campus, stream, student_program_status)
core.student_letter_template_tags(id, institution_id, tag, description, field, created_at, updated_at)
core.student_letter_templates(id, institution_id, name, print_content, currency_id, amount, created_at, updated_at)
core.student_meta_column_values(id, student_id -> students.id, student_meta_column_id, value, created_at, updated_at)
core.student_meta_columns(id, institution_id, type, label, default_value, option_values, created_at, updated_at)
core.student_payment_wallets(id, student_id, type, details, created_at, updated_at)
core.student_photos(id, institution_id, student_id -> students.id, mime_type, path, filename, created_at, updated_at)
core.student_plans(id, institution_id, student_id -> students.id, plan_id, created, doa, doc)
core.student_positions(id, institution_id, student_id -> students.id, start_date, end_date, name, created_at, updated_at)
core.student_program_histories(id, institution_id, student_program_id -> student_programs.id, student_id -> students.id, program_id, campus_id, semester_id, student_program_status_id, student_regno_type_id, stream_id, regnumber, level, student_residency_id, major_id, minor_id, doa, doc, entry_mode_id, program_plan_id, created_at, updated_at)
core.student_program_info(doa_year, regnumber, surname, othernames, gender, program, program_short_name, level, entry_level, doa, doc, email, student_status, student_program_status, demonym, mobile, campus, stream, entry_mode, dob, major, other_contacts, personal_email_address)
core.student_program_status_changes(id, student_program_id -> student_programs.id, student_program_status_id -> student_program_statuses.id, semester_id -> semesters.id, comment, created_at, updated_at)
core.student_program_statuses(id, name, created_at, updated_at)
core.student_programs(id, institution_id, student_id -> students.id, program_id, semester_id, campus_id, student_program_status_id, student_regno_type_id, stream_id, regnumber, level, entry_level, major_id, minor_id, doa, doc, previous_student_program_id, created_at, updated_at, current, entry_mode_id, program_plan_id)
core.student_registration_courses(id, institution_id, student_registration_id -> student_registrations.id, registration_type, course_id -> courses.id, course_type, assessment_sheet_group_id -> assessment_sheet_groups.id, confirmed, remarks, created_at, updated_at, registration_source)
core.student_registration_data(semester, student_name, program, program_short_name, regnumber, doa, doc, level, entry_level, code, title, credits, confirmed, completed, gender, campus, is_resit)
core.student_registrations(id, institution_id, type, student_id -> students.id, student_program_id -> student_programs.id, registration_schedule_id -> registration_schedules.id, semester_id -> semesters.id, completed, created_at, updated_at)
core.student_regno_types(id, institution_id, name, created_at, updated_at)
core.student_residencies(id, institution_id, student_id, type, location, created_at, updated_at)
core.student_review_types(id, name, created_at, updated_at)
core.student_reviews(id, institution_id, student_id, student_review_type_id -> student_review_types.id, message, created_at, updated_at)
core.student_scholarship_bill_item_types(id, institution_id, student_scholarship_id -> student_scholarships.id, bill_item_type_id)
core.student_scholarship_transactions(id, institution_id, student_scholarship_id, student_transaction_id, created_at, updated_at)
core.student_scholarships(id, institution_id, billing_period_id -> billing_periods.id, student_id -> students.id, student_program_id -> student_programs.id, value, currency_id, type, source, created_at, updated_at)
core.student_semester_gpas(id, institution_id, student_id, student_program_id, semester_id, cumulative_gp, total_credit, total_courses, gpa, status, created_at, updated_at)
core.student_status_types(id, name, created_at, updated_at)
core.student_statuses(id, student_id -> students.id, semester_id, student_status_type_id -> student_status_types.id, comments, created_at, updated_at)
core.student_study_centers(id, institution_id, study_center_program_id, student_id, student_program_id, created_at, updated_at)
core.student_transaction_types(id, name, is_fees, description, transflow_product_id, created_at, updated_at)
core.student_transactions(id, institution_id, student_transaction_type_id -> student_transaction_types.id, receipt_number, transaction_date, transaction_description, transaction_amount, student_program_id, currency_id, currency_exchange_id, converted_amount, student_id, level, billing_period_id, type, reversal, allotment_status, allotment_balance, bank_id, cheque_no, cheque_desc, cheque_date, cheque_value_date, created_at, user_id, updated_at, source, payer_name)
core.students(id, institution_id, surname, othernames, prefix, suffix, sex, dob, nationality_id, mobile, email, status -> student_status_types.name, created_at, updated_at, osisv1_studid)
core.study_center_programs(id, institution_id, study_center_id -> study_centers.id, program_id, created_at, updated_at)
core.study_centers(id, institution_id, name, geolocation, status, created_at, updated_at)
core.template_images(institution_id, filename, type, path, created_at)
core.timetable_notes(id, institution_id, title, note, note_date, note_calloff, created_at, updated_at)
core.timetable_pending_placements(id, institution_id, timetable_id -> timetables.id, assessment_sheet_id, capacity, credits, user_id, created_at, updated_at)
core.timetable_periods(id, institution_id, timetable_type_id, period, start, end, created_at, updated_at)
core.timetable_placements(id, institution_id, timetable_id -> timetables.id, start_period_id -> timetable_periods.id, end_period_id -> timetable_periods.id, venue_id -> venues.id, assessment_sheet_id -> assessment_sheets.id, day, date, capacity, credits, user_id -> users.id, changed_info, created_at, updated_at)
core.timetable_types(id, name, created_at, updated_at)
core.timetables(id, institution_id, timetable_type_id -> timetable_types.id, semester_id -> semesters.id, unit_id -> units.id, campus_id, streams, active, created_at, updated_at)
core.transaction_requests(id, institution_id, student_id, student_program_id, type, data, requested_by, reviewed_by, status, created_at, updated_at)
core.transflow_alt1(id, institution_id, alt1, account_number)
core.transflow_payments(id, institution_id, transaction_id, transaction_date, data, processed, created_at, updated_at, bank_code)
core.unit_types(id, institution_id, name, scope, parent_id, created_at, updated_at)
core.unit_venues(id, institution_id, venue_id -> venues.id, unit_id, created_at, updated_at)
core.units(id, institution_id, unit_type_id -> unit_types.id, parent_id, official_name, abbreviated_name, campus_id -> campuses.id, _lft, _rgt, created_at, updated_at)
core.unredeemed_resit_list(regnumber, surname, othernames, long_name, program_short_name, campus, failed_courses, count_of_courses)
core.usage_deductions(id, institution_id, student_id -> students.id, student_program_id, semester_id, applicant_id, charged_item_name, deducted, extra_info, created_at, updated_at)
core.user_roles(prefix, suffix, surname, othernames, email, mobile, designation, role)
core.users(id, staff_id -> staff.id, email, password, remember_token, two_factor, status, login_count, is_logged_in, last_activity, expiry, preferred_locale, created_at, updated_at)
core.venues(id, institution_id, name, parent_id, campus_id -> campuses.id, class_capacity, exam_capacity, status, type, handicap_acc, created_at, updated_at)
core.waec_institution(id, institution_id, index_number, exam_year, created_at, updated_at)
core.waec_results(id, index_number, exam_year, waec_data, status, created_at, updated_at)
{"auth.institution_app_clients": {"columns": ["app_name (varchar(255)), nullable=YES", "client_id (varchar(100)), nullable=YES", "client_secret (varchar(100)), nullable=YES", "created_at (timestamp), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=YES", "revoked (tinyint), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "auth.institutions": {"columns": ["address (varchar(255)), nullable=YES", "api_url (varchar(255)), nullable=YES", "created_at (datetime), nullable=YES", "digital_address (varchar(100)), nullable=YES", "email (varchar(50)), nullable=YES", "host (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "name (varchar(255)), nullable=YES", "path (varchar(255)), nullable=YES", "phone (varchar(26)), nullable=YES", "short_name (varchar(50)), nullable=YES", "sip_url (varchar(255)), nullable=YES", "updated_at (datetime), nullable=YES", "website (varchar(255)), nullable=YES"], "foreign_keys": []}, "core.Untitled": {"columns": ["id (int), nullable=NO, extra=auto_increment"], "foreign_keys": []}, "core.academic_years": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "end_year (year), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "start_year (year), nullable=NO", "status (enum(8)), default=Active, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.activity_log": {"columns": ["causer_id (char(36)), nullable=YES", "causer_type (varchar(100)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "description (text(65535)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "ip_address (varchar(100)), nullable=YES", "log_name (varchar(200)), nullable=YES", "properties (text(65535)), nullable=YES", "student_id (char(36)), nullable=YES", "subject_id (char(36)), nullable=YES", "subject_type (varchar(100)), nullable=YES", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "user_agent (varchar(255)), nullable=YES"], "foreign_keys": []}, "core.adhoc_bill_item_types": {"columns": ["created_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(50)), nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.adhoc_bill_items": {"columns": ["adhoc_bill_item_type_id (int), nullable=NO", "amount (decimal), nullable=NO", "bill_id (int), nullable=YES", "created_at (datetime), nullable=YES", "currency_id (int), nullable=NO", "deleted_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["adhoc_bill_item_type_id -> core.adhoc_bill_item_types(id) [constraint=adhoc_bill_items_ibfk_4]", "bill_id -> core.bills(id) [constraint=adhoc_bill_items_ibfk_5]"]}, "core.admission_batches": {"columns": ["academic_year_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "deleted_at (datetime), nullable=YES", "doa (var<PERSON><PERSON>(7)), nullable=YES", "doa_month (varchar(3)), nullable=NO", "doa_year (year), nullable=NO", "doc (var<PERSON>r(7)), nullable=YES", "doc_month (varchar(3)), nullable=NO", "doc_year (year), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "is_child (enum(1)), default=0, nullable=YES", "is_merged (tinyint), nullable=YES", "is_start_year_inclusive (enum(1)), default=0, nullable=YES", "merge_level (smallint), nullable=YES", "parent_id (int), nullable=YES", "status (enum(8)), default=Active, nullable=NO", "upated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.admission_forms": {"columns": ["admission_batch_id (int), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "description (varchar(100)), nullable=NO", "end_date (date), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "is_campus_stream_configured (enum(3)), default=No, nullable=YES", "is_program_configured (enum(3)), default=No, nullable=YES", "show_education (enum(3)), default=No, nullable=NO", "show_employment (enum(3)), default=No, nullable=NO", "show_results (enum(3)), default=No, nullable=NO", "start_date (date), nullable=NO", "status (enum(8)), default=Active, nullable=NO", "total_program_options (int), default=0, nullable=NO", "total_referees (int), nullable=NO", "updated_at (timestamp), nullable=YES", "workflow_form_id (varchar(255)), nullable=YES"], "foreign_keys": []}, "core.admission_status_tokens": {"columns": ["applicant_id (int), nullable=NO", "created_at (timestamp), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "recipient (var<PERSON>r(255)), nullable=NO", "token (varchar(255)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.affiliations": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "long_name (varchar(50)), nullable=YES", "short_name (varchar(5)), nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.allotments": {"columns": ["amount (double), nullable=YES", "bill_item_type_id (int), nullable=YES", "billing_period_id (int), nullable=YES", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "currency_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "receipt_no (varchar(20)), nullable=YES", "student_id (var<PERSON>r(36)), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=allotments_ibfk_1]", "billing_period_id -> core.billing_periods(id) [constraint=allotments_ibfk_2]", "currency_id -> core.currencies(id) [constraint=allotments_ibfk_4]", "bill_item_type_id -> core.bill_item_types(id) [constraint=allotments_ibfk_5]"]}, "core.alt1_bill_categories": {"columns": ["bill_category_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "transflow_alt1_id (int), nullable=NO"], "foreign_keys": []}, "core.app_clients": {"columns": ["client_id (varchar(100)), nullable=YES", "created_at (timestamp), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (varchar(255)), nullable=NO", "revoked (tinyint), nullable=NO", "secret (varchar(100)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_answers": {"columns": ["answer (text(65535)), nullable=NO", "applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "form_question_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_attachments": {"columns": ["updated_at (timestamp), nullable=YES", "applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "filename (varchar(255)), nullable=NO", "form_attachment_type_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "path (varchar(200)), nullable=NO"], "foreign_keys": []}, "core.applicant_contact_types": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "instittuion_id (int), nullable=NO", "type (varchar(100)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_contacts": {"columns": ["address (varchar(50)), nullable=YES", "applicant_contact_type_id (int), nullable=NO", "applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "email (varchar(100)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "mobile (varchar(20)), nullable=YES", "name (var<PERSON><PERSON>(50)), nullable=NO", "occupation (var<PERSON>r(30)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_education": {"columns": ["applicant_id (int), nullable=NO", "awarded_at (timestamp), nullable=YES", "certificate (varchar(50)), nullable=NO", "class (varchar(50)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "end_date (date), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "qualification (varchar(100)), nullable=YES", "school (var<PERSON>r(50)), nullable=NO", "start_date (date), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_employment": {"columns": ["applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "end_date (date), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution (varchar(100)), nullable=NO", "position (varchar(100)), nullable=YES", "start_date (date), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_exam_aggregate": {"columns": ["aggregate (int), nullable=YES", "applicant_id (int), nullable=NO", "failure_message (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment"], "foreign_keys": []}, "core.applicant_exam_re_entry_tokens": {"columns": ["applicant_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "token (varchar(64)), nullable=NO"], "foreign_keys": []}, "core.applicant_exam_results": {"columns": ["applicant_exam_id (int), nullable=NO", "applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "grade (varchar(20)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "subject_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_exams": {"columns": ["aggregate (int), nullable=YES", "applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "date (date), nullable=YES", "exam_type_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "index_number (varchar(50)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_guardian_types": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "type (varchar(100)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_guardians": {"columns": ["address (varchar(255)), nullable=YES", "applicant_guardian_type_id (int), nullable=NO", "applicant_id (int), default=0, nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "email (varchar(100)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "mobile (varchar(13)), nullable=YES", "name (var<PERSON><PERSON>(150)), nullable=NO", "occupation (var<PERSON>r(30)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_halls": {"columns": ["applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "hall_id (tinyint), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_identities": {"columns": ["applicant_id (int), nullable=NO", "applicant_identity_type_id (tinyint), nullable=NO", "created_at (timestamp), nullable=YES", "expiry (date), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "number (varchar(20)), nullable=NO", "updated_at (timestamp), nullable=YES", "url (varchar(255)), nullable=YES"], "foreign_keys": []}, "core.applicant_identity_types": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(100)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_info": {"columns": ["admission_year (varchar(9)), nullable=YES", "applicant_status (enum(8)), default=Active, nullable=YES", "applicantion_source (enum(6)), default=online, nullable=YES", "campus (varchar(50)), nullable=YES", "contact_address (varchar(50)), nullable=YES", "contact_mobile_number (varchar(20)), nullable=YES", "contact_name (varchar(50)), nullable=YES", "decision_on_applicant (enum(31)), default=None, nullable=YES", "demonym (var<PERSON><PERSON>(25)), nullable=YES", "dob (date), nullable=YES", "email (varchar(100)), nullable=YES", "entry_mode (varchar(50)), nullable=YES", "extra_data (text(65535)), nullable=YES", "form_name (varchar(100)), nullable=YES", "gender (enum(1)), default=M, nullable=YES", "guardian_address (varchar(255)), nullable=YES", "guardian_mobile_number (varchar(13)), nullable=YES", "guardian_name (var<PERSON><PERSON>(150)), nullable=YES", "mobile (varchar(13)), nullable=YES", "othernames (varchar(100)), nullable=YES", "prefix (varchar(10)), nullable=YES", "program_applied_for (varchar(150)), nullable=YES", "program_offered (varchar(150)), nullable=YES", "program_short_name (varchar(100)), nullable=YES", "regnumber (varchar(50)), nullable=YES", "stream (varchar(20)), nullable=YES", "surname (var<PERSON><PERSON>(50)), nullable=YES", "temp_number (varchar(36)), nullable=YES"], "foreign_keys": []}, "core.applicant_meta_values": {"columns": ["applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "student_meta_column_id (int), nullable=NO", "updated_at (timestamp), nullable=YES", "value (varchar(255)), nullable=NO"], "foreign_keys": []}, "core.applicant_photos": {"columns": ["applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "filename (varchar(255)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "path (varchar(200)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_programs": {"columns": ["aggregate (int), nullable=YES", "applicant_id (int), nullable=NO", "choice (int), default=0, nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "message (varchar(255)), nullable=YES", "program_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicant_referees": {"columns": ["address (varchar(50)), nullable=YES", "applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "email (varchar(100)), nullable=YES", "email_status (enum(1)), default=0, nullable=YES", "id (int), nullable=NO, extra=auto_increment", "mobile (varchar(20)), nullable=YES", "name (var<PERSON><PERSON>(50)), nullable=NO", "occupation (var<PERSON>r(30)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.applicants": {"columns": ["alumni_regnumber (varchar(50)), nullable=YES", "campus_id (int), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "date_of_issue (timestamp), nullable=YES", "decision (enum(31)), default=None, nullable=NO", "dob (date), nullable=NO", "email (varchar(100)), nullable=NO", "entrance_number (varchar(20)), nullable=YES", "entry_level (varchar(20)), nullable=YES", "entry_mode_id (int), nullable=YES", "form_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "is_student (enum(3)), default=No, nullable=NO", "letter_last_printed_at (timestamp), nullable=YES", "mobile (var<PERSON>r(13)), nullable=NO", "nationality_id (smallint), nullable=NO", "offer_letter_template_id (int), nullable=YES", "othernames (varchar(100)), nullable=NO", "prefix (varchar(10)), nullable=YES", "program_id (int), nullable=YES", "regnumber (varchar(50)), nullable=YES", "sex (enum(1)), default=M, nullable=NO", "source (enum(6)), default=online, nullable=YES", "status (enum(8)), default=Active, nullable=NO", "stream_id (int), nullable=YES", "student_id (var<PERSON>r(36)), nullable=YES", "study_center_id (int), nullable=YES", "suffix (varchar(10)), nullable=YES", "surname (var<PERSON><PERSON>(50)), nullable=NO", "temp_number (varchar(36)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.assessment_approved_results": {"columns": ["assessment_result_id (int), nullable=YES", "assessment_sheet_id (bigint), nullable=NO", "assessment_total (decimal), nullable=YES", "course_code (varchar(20)), nullable=YES", "course_credits (tinyint), nullable=YES", "course_title (varchar(255)), nullable=YES", "created_at (datetime), nullable=YES", "exam_total (decimal), nullable=YES", "finalscore (decimal), nullable=YES", "gp (double), default=0.00, nullable=NO", "grade (varchar(5)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "level (varchar(20)), nullable=YES", "position (int), nullable=YES", "registration_source (enum(8)), default=Self, nullable=YES", "remarks (text(65535)), nullable=YES", "semester_id (int), nullable=YES", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": ["assessment_result_id -> core.assessment_results(id) [constraint=assessment_approved_results_ibfk_1]", "assessment_sheet_id -> core.assessment_sheets(id) [constraint=assessment_approved_results_ibfk_2]", "student_id -> core.students(id) [constraint=assessment_approved_results_ibfk_3]", "student_program_id -> core.student_programs(id) [constraint=assessment_approved_results_ibfk_4]", "semester_id -> core.semesters(id) [constraint=assessment_approved_results_ibfk_5]"]}, "core.assessment_column_scores": {"columns": ["assessment_sheet_column_id (int), nullable=NO", "count_changes (tinyint), default=0, nullable=YES", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "score (decimal), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "test_score (decimal), nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=assessment_column_scores_ibfk_1]", "assessment_sheet_column_id -> core.assessment_sheet_columns(id) [constraint=assessment_column_scores_ibfk_2]"]}, "core.assessment_result_remarks": {"columns": ["assessment_result_id (int), nullable=NO", "created_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "modified_at (datetime), nullable=YES", "remarks (text(65535)), nullable=YES", "user_id (int), nullable=NO"], "foreign_keys": ["assessment_result_id -> core.assessment_results(id) [constraint=assessment_result_remarks_ibfk_1]", "user_id -> core.users(id) [constraint=assessment_result_remarks_ibfk_2]"]}, "core.assessment_results": {"columns": ["assessment_sheet_id (bigint), nullable=NO", "assessment_total (decimal), nullable=YES", "course_id (int), nullable=NO", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "exam_total (decimal), nullable=YES", "finalscore (decimal), nullable=YES", "gp (double), default=0.00, nullable=NO", "grade (varchar(5)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "position (int), nullable=YES", "previous_assessment_result_id (int), nullable=YES", "re_registered (tinyint), default=0, nullable=YES", "registration_source (enum(8)), default=Self, nullable=YES", "remarks (text(65535)), nullable=YES", "semester_id (int), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": ["assessment_sheet_id -> core.assessment_sheets(id) [constraint=assessment_results_ibfk_1]", "student_id -> core.students(id) [constraint=assessment_results_ibfk_2]", "student_program_id -> core.student_programs(id) [constraint=assessment_results_ibfk_3]"]}, "core.assessment_sheet_columns": {"columns": ["assessment_sheet_id (bigint), default=1, nullable=NO", "colour (varchar(10)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "description (varchar(60)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(50)), nullable=NO", "number (int), nullable=NO", "test_total_score (int), nullable=NO", "type (enum(10)), nullable=YES", "updated_at (timestamp), nullable=YES", "value (int), nullable=NO"], "foreign_keys": []}, "core.assessment_sheet_group_settings": {"columns": ["auto_group_by (set(61)), default=, nullable=NO", "course_id (int), nullable=NO", "created_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "max_groups (int), nullable=NO", "method (enum(9)), default=automatic, nullable=NO", "quota (int), nullable=NO", "semester_id (int), nullable=NO", "status (enum(1)), default=1, nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.assessment_sheet_groups": {"columns": ["assessment_sheet_group_setting_id (int), default=0, nullable=YES", "course_id (int), nullable=NO", "course_type (enum(8)), default=required, nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "groupname (var<PERSON><PERSON>(200)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "semester_id (int), nullable=NO", "student_count (smallint), default=0, nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.assessment_sheet_stages": {"columns": ["assessment_sheet_id (bigint), nullable=YES", "assessment_sheet_status_id (tinyint), nullable=NO", "created_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "remarks (mediumtext(16777215)), nullable=YES", "updated_at (datetime), nullable=YES", "user_id (int), nullable=YES"], "foreign_keys": ["assessment_sheet_status_id -> core.assessment_sheet_statuses(id) [constraint=assessment_sheet_stages_ibfk_1]", "user_id -> core.users(id) [constraint=assessment_sheet_stages_ibfk_2]"]}, "core.assessment_sheet_statuses": {"columns": ["created_at (datetime), nullable=YES", "description (varchar(255)), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "label (enum(10)), nullable=NO", "permitted_actions (set(59)), default=, nullable=YES", "permitted_roles (varchar(255)), nullable=YES", "position (int), nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.assessment_sheets": {"columns": ["approved (enum(1)), default=0, nullable=YES", "assessment_sheet_group_id (int), nullable=NO", "assessment_sheet_status_id (tinyint), default=0, nullable=YES", "course_id (int), nullable=NO", "course_type (enum(8)), default=required, nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "grading_scheme_id (tinyint), nullable=YES", "id (bigint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "level (varchar(20)), nullable=YES", "semester_id (int), nullable=NO", "unit_id (smallint), nullable=YES", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP"], "foreign_keys": ["semester_id -> core.semesters(id) [constraint=assessment_sheets_ibfk_1]", "course_id -> core.courses(id) [constraint=assessment_sheets_ibfk_2]", "assessment_sheet_group_id -> core.assessment_sheet_groups(id) [constraint=assessment_sheets_ibfk_3]", "grading_scheme_id -> core.grading_schemes(id) [constraint=assessment_sheets_ibfk_4]"]}, "core.bank_integrations": {"columns": ["bank_id (smallint), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "integration_type (enum(12)), nullable=NO", "status (enum(8)), default=active, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.banks": {"columns": ["address (varchar(200)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "deleted (tinyint), default=0, nullable=YES", "email (varchar(50)), nullable=YES", "id (smallint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (varchar(200)), nullable=YES", "phone_numbers (varchar(100)), nullable=YES", "short_code (varchar(10)), nullable=YES", "swift_code (varchar(15)), nullable=YES", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED"], "foreign_keys": []}, "core.bill_categories": {"columns": ["campus_id (tinyint), nullable=YES", "created_at (datetime), nullable=YES", "doa (char(7)), nullable=YES", "doc (char(7)), nullable=YES", "entry_mode_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "level (varchar(20)), nullable=YES", "nationality_type (enum(7)), nullable=YES", "program_id (int), nullable=YES", "stream_id (tinyint), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": ["program_id -> core.programs(id) [constraint=bill_categories_ibfk_1]", "campus_id -> core.campuses(id) [constraint=bill_categories_ibfk_2]", "stream_id -> core.streams(id) [constraint=bill_categories_ibfk_3]"]}, "core.bill_item_ratios": {"columns": ["bill_item_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "ratio (int), nullable=NO"], "foreign_keys": []}, "core.bill_item_types": {"columns": ["created_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(50)), nullable=NO", "priority (tinyint), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.bill_items": {"columns": ["amount (decimal), nullable=NO", "bill_id (int), nullable=YES", "bill_item_type_id (int), nullable=NO", "created_at (datetime), nullable=YES", "currency_id (int), nullable=NO", "deleted_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["bill_item_type_id -> core.bill_item_types(id) [constraint=bill_items_ibfk_4]", "bill_id -> core.bills(id) [constraint=bill_items_ibfk_5]"]}, "core.billing_periods": {"columns": ["created_at (datetime), nullable=YES", "description (varchar(50)), nullable=YES", "end_date (date), nullable=YES", "group_determinant (varchar(3)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "start_date (date), nullable=YES", "status (enum(6)), default=open, nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.bills": {"columns": ["bill_category_id (int), nullable=YES", "billing_period_id (int), nullable=YES", "created_at (datetime), nullable=YES", "currency_id (tinyint), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "total_due (double), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": ["billing_period_id -> core.billing_periods(id) [constraint=bills_ibfk_1]", "bill_category_id -> core.bill_categories(id) [constraint=bills_ibfk_2]"]}, "core.campus_types": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(70)), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.campus_types_copy1": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(70)), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.campuses": {"columns": ["campus_type_id (tinyint), nullable=NO", "created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "location (varchar(255)), nullable=YES", "name (var<PERSON><PERSON>(50)), nullable=NO", "status (enum(1)), default=1, nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["campus_type_id -> core.campus_types(id) [constraint=campuses_ibfk_1]"]}, "core.case_attachments": {"columns": ["case_id (var<PERSON>r(36)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "file_path (varchar(255)), nullable=NO", "id (varchar(36)), nullable=NO"], "foreign_keys": ["case_id -> core.cases(id) [constraint=case_id_fk]"]}, "core.case_categories": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "description (varchar(255)), nullable=NO", "id (smallint), nullable=NO, extra=auto_increment", "name (var<PERSON><PERSON>(50)), nullable=NO", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.case_correspondence": {"columns": ["attachments (text(65535)), nullable=NO", "case_id (var<PERSON>r(36)), nullable=NO", "contact_email (varchar(60)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "message (text(65535)), nullable=NO", "reopen_case (enum(1)), default=N, nullable=NO", "type (enum(4)), nullable=NO", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP"], "foreign_keys": ["case_id -> core.cases(id) [constraint=case_id]"]}, "core.case_severities": {"columns": ["name (var<PERSON>r(10)), nullable=NO"], "foreign_keys": []}, "core.cases": {"columns": ["case_category_id (smallint), nullable=NO", "code (int), nullable=NO, extra=auto_increment", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "description (text(65535)), nullable=NO", "id (varchar(36)), nullable=NO", "institution_id (int), nullable=NO", "module_id (tinyint), nullable=YES", "requested_by (var<PERSON>r(60)), nullable=NO", "severity (varchar(10)), nullable=YES", "status (enum(8)), default=pending, nullable=NO", "title (varchar(100)), nullable=NO", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP"], "foreign_keys": ["case_category_id -> core.case_categories(id) [constraint=cases_ibfk_1]", "institution_id -> core.institutions(id) [constraint=fk_institution_id]"]}, "core.complementary_scheme_grades": {"columns": ["complementary_scheme_id (tinyint), default=0, nullable=NO", "created_at (datetime), nullable=YES", "grade (varchar(20)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.complementary_schemes": {"columns": ["complementary_scheme_name (varchar(30)), nullable=NO", "created_at (datetime), nullable=YES", "description (varchar(50)), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.course_lecturers": {"columns": ["course_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "lecturer_staff_id (int), nullable=NO", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP"], "foreign_keys": ["course_id -> core.courses(id) [constraint=course_fk1]", "lecturer_staff_id -> core.staff(id) [constraint=staff_fk1]"]}, "core.course_prerequisites": {"columns": ["course_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "prerequisite_course_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["course_id -> core.courses(id) [constraint=course_prerequisites_ibfk_1]", "prerequisite_course_id -> core.courses(id) [constraint=course_prerequisites_ibfk_2]"]}, "core.courses": {"columns": ["code (varchar(8)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "credits (tinyint), default=3, nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "level (varchar(20)), nullable=NO", "parent_id (int), nullable=YES", "semester (int), nullable=NO", "status (enum(1)), nullable=NO", "title (var<PERSON>r(200)), nullable=NO", "unit_id (smallint), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["unit_id -> core.units(id) [constraint=courses_ibfk_1]", "parent_id -> core.courses(id) [constraint=course_parent_ibfk_1]"]}, "core.currencies": {"columns": ["code (varchar(3)), nullable=NO", "created_at (timestamp), nullable=YES", "description (varchar(50)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.currency_exchange": {"columns": ["base_currency_id (int), nullable=YES", "created_at (datetime), nullable=YES", "effective_date (date), nullable=YES", "exchange_rate (double), nullable=YES", "foreign_currency_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "inverse_rate (double), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.designations": {"columns": ["created_at (timestamp), nullable=YES", "description (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "name (varchar(255)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.document_requests": {"columns": ["amount (decimal), nullable=YES", "copies (smallint), nullable=NO", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "currency_id (int), nullable=YES", "description (varchar(100)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "invoice (varchar(6)), nullable=NO", "status (enum(7)), default=pending, nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_letter_template_id (tinyint), nullable=YES", "student_program_id (int), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.email_integration_password_format": {"columns": ["alternative (var<PERSON>r(20)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "order (int), nullable=YES", "prefix (varchar(20)), nullable=NO", "separator (varchar(1)), nullable=YES", "type (enum(8)), default=Standard, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.email_validation_tokens": {"columns": ["id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "subject_id (var<PERSON><PERSON>(36)), nullable=NO", "token (varchar(64)), nullable=NO"], "foreign_keys": []}, "core.entrance_exam_scores": {"columns": ["applicant_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "score (decimal), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.entry_modes": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "entry_mode (varchar(50)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "type (enum(13)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.evaluation_form_answers": {"columns": ["answers (json), nullable=NO", "course_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "evaluation_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP", "user_id (int), nullable=NO"], "foreign_keys": []}, "core.evaluation_form_question_options": {"columns": ["evaluation_form_section_question_id (int), nullable=NO", "evaluation_section_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "order (int), nullable=NO", "question_option (text(65535)), nullable=NO"], "foreign_keys": ["evaluation_section_id -> core.evaluation_sections(id) [constraint=fk_evsections]", "evaluation_form_section_question_id -> core.evaluation_form_section_questions(id) [constraint=fk_lefquestions]"]}, "core.evaluation_form_section_questions": {"columns": ["answer_type (enum(15)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "evaluation_section_id (int), nullable=NO", "hints (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "question (text(65535)), nullable=NO", "required (enum(3)), default=Yes, nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["evaluation_section_id -> core.evaluation_sections(id) [constraint=fkey_categories]", "institution_id -> core.evaluation_sections(institution_id) [constraint=fkey_categories]"]}, "core.evaluation_forms": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "form_instruction (text(65535)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (varchar(100)), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.evaluation_sections": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "evaluation_form_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "order (int), nullable=NO", "section (varchar(100)), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["evaluation_form_id -> core.evaluation_forms(id) [constraint=section_FK]", "institution_id -> core.evaluation_forms(institution_id) [constraint=section_FK]"]}, "core.evaluations": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "end_date (date), nullable=NO", "evaluation_form_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "semester_id (int), nullable=NO", "start_date (date), nullable=NO", "status (enum(8)), default=active, nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.exam_grades": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "exam_type_id (int), nullable=NO", "grade (varchar(10)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "order (int), nullable=NO", "updated_at (timestamp), nullable=YES", "weight (int), nullable=NO"], "foreign_keys": []}, "core.exam_subjects": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "exam_type_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "subject (varchar(100)), nullable=NO", "type (enum(8)), default=Elective, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.exam_types": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "status (enum(8)), default=Active, nullable=NO", "total_core_for_aggregate (tinyint), nullable=YES", "total_elective_for_aggregate (tinyint), nullable=YES", "type (varchar(50)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.failed_jobs": {"columns": ["connection (text(65535)), nullable=NO", "exception (longtext(4294967295)), nullable=NO", "failed_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (bigint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "payload (longtext(4294967295)), nullable=NO", "queue (text(65535)), nullable=NO"], "foreign_keys": []}, "core.file_download_requests": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "meta_data (json), nullable=NO", "reference_number (varchar(6)), nullable=NO", "status (enum(10)), nullable=YES", "type (enum(24)), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.file_import_logs": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "description (text(65535)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "status (enum(7)), default=success, nullable=NO", "student_import_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["student_import_id -> core.file_imports(id) [constraint=file_import_logs_ibfk_1]"]}, "core.file_imports": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "filename (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "import_type (enum(13)), default=student, nullable=NO", "institution_id (int), nullable=NO", "mime_type (varchar(255)), nullable=YES", "path (varchar(255)), default=, nullable=YES", "reference_no (varchar(20)), default=, nullable=NO", "status (enum(10)), default=pending, nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.financial_aid_request_students": {"columns": ["amount (double), nullable=NO", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "currency_id (int), nullable=NO", "financial_aid_request_reference_number (varchar(8)), nullable=NO", "institution_id (int), nullable=NO", "student_program_id (int), nullable=NO", "student_transaction_id (int), nullable=YES"], "foreign_keys": ["student_program_id -> core.student_programs(id) [constraint=student_program_fk1]"]}, "core.financial_aid_requests": {"columns": ["amount (double), nullable=NO", "approved_by (int), nullable=YES", "billing_period_id (int), nullable=NO", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "currency_id (int), nullable=NO", "institution_id (int), nullable=NO", "description (varchar(255)), nullable=NO", "reference_number (varchar(8)), nullable=NO", "requested_by (int), nullable=NO", "status (enum(12)), default=pending, nullable=NO", "type (enum(6)), nullable=NO", "type_extra_data (json), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "description": "Essentially the same as financial aid options", "foreign_keys": ["billing_period_id -> core.billing_periods(id) [constraint=billing_fk1]", "currency_id -> core.currencies(id) [constraint=currency_fk1]"]}, "core.form_attachment_types": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "type (varchar(50)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.form_meta_columns": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "default_value (varchar(255)), nullable=YES", "form_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "label (varchar(50)), nullable=NO", "option_values (varchar(255)), nullable=YES", "type (enum(7)), default=string, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.form_program_qualification": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "cutoff_aggregate (tinyint), nullable=YES", "exam_type_id (int), nullable=NO", "form_program_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "least_grade_id (int), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": ["exam_type_id -> core.exam_types(id) [constraint=fk_exam_type_id]", "form_program_id -> core.form_programs(id) [constraint=fk_form_program_id]", "least_grade_id -> core.exam_grades(id) [constraint=fk_least_grade_id]"]}, "core.form_program_qualification_subjects": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "exam_subject_id (int), nullable=NO", "form_program_qualification_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "min_grade (varchar(20)), nullable=YES", "type (enum(12)), default=optional, nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": ["exam_subject_id -> core.exam_subjects(id) [constraint=fk_exam_subject_id]", "form_program_qualification_id -> core.form_program_qualification(id) [constraint=fk_form_program_qualification_id]"]}, "core.form_programs": {"columns": ["admission_form_id (int), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "program_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.form_question_categories": {"columns": ["category (varchar(100)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "order (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.form_question_options": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "form_question_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "question_option (text(65535)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.form_questions": {"columns": ["admission_form_id (int), nullable=NO", "answer_type (enum(14)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "form_question_category_id (int), nullable=YES", "hints (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "options (varchar(300)), nullable=YES", "order (int), nullable=NO", "question (text(65535)), nullable=NO", "required (enum(3)), default=Yes, nullable=NO", "status (enum(8)), default=Active, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.grade_changes": {"columns": ["action (enum(15)), nullable=NO", "assessment_result_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "request_data (json), nullable=NO", "status (enum(8)), default=Pending, nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "updated_at (timestamp), nullable=NO, extra=on update CURRENT_TIMESTAMP", "user_id (int), nullable=NO"], "foreign_keys": ["student_id -> core.students(id) [constraint=grade_changes_ibfk_1]", "user_id -> core.users(id) [constraint=grade_changes_ibfk_4]"]}, "core.grade_options": {"columns": ["id (tinyint), nullable=NO, extra=auto_increment", "label (varchar(30)), nullable=NO", "type (enum(9)), default=regular, nullable=YES", "value (varchar(20)), nullable=NO"], "foreign_keys": []}, "core.grading_scheme_complements": {"columns": ["complementary_scheme_id (tinyint), default=0, nullable=NO", "created_at (datetime), nullable=YES", "grading_scheme_id (tinyint), default=0, nullable=NO", "id (int), nullable=NO, extra=auto_increment", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.grading_scheme_gpas": {"columns": ["class (varchar(50)), default=, nullable=NO", "created_at (datetime), nullable=YES", "gpa (float), default=0.00, nullable=NO", "grading_scheme_id (tinyint), default=0, nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "order (int), default=0, nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.grading_scheme_maps": {"columns": ["complementary_scheme_grade_id (int), nullable=NO", "created_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "scheme_grade_id (int), nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.grading_scheme_programs": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "grading_scheme_id (tinyint), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "program_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.grading_schemes": {"columns": ["assessment_total (int), nullable=YES", "created_at (datetime), nullable=YES", "description (varchar(50)), nullable=YES", "exam_total (int), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "scheme_name (varchar(30)), nullable=NO", "status (enum(1)), default=1, nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.graduation_batches": {"columns": ["cohort (var<PERSON><PERSON>(7)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "status (enum(8)), default=active, nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.graduation_bill_item_types": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(50)), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.graduation_bill_items": {"columns": ["amount (decimal), nullable=NO", "created_at (datetime), nullable=YES", "currency_id (int), nullable=NO", "graduation_bill_id (int), nullable=YES", "graduation_bill_item_type_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["graduation_bill_id -> core.graduation_bills(id) [constraint=graduation_bill_fk]"]}, "core.graduation_bills": {"columns": ["billing_period_id (int), nullable=YES", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "currency_id (tinyint), nullable=YES", "graduation_batch_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "total_due (double), nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.graduation_clearance": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "status (enum(11)), default=not_cleared, nullable=YES", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.graduation_report": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "graduation_batch_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "status (enum(8)), default=pending, nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP", "user_id (int), nullable=NO"], "foreign_keys": []}, "core.halls": {"columns": ["address (varchar(255)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (tinyint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "location (varchar(255)), nullable=YES", "name (varchar(255)), nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.historical_upload_file_assessment_sheets": {"columns": ["assessment_sheet_id (bigint), nullable=NO", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "historical_upload_file_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["assessment_sheet_id -> core.assessment_sheets(id) [constraint=assessment_sheet_fk1]", "historical_upload_file_id -> core.historical_upload_files(id) [constraint=historical_upload_file_fk]"]}, "core.historical_upload_files": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "filename (varchar(100)), nullable=NO", "historical_upload_session_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), default=0, nullable=NO", "meta_data (json), nullable=YES", "path (varchar(255)), nullable=NO", "reference_no (varchar(6)), nullable=NO", "status (enum(18)), default=pending_review, nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["historical_upload_session_id -> core.historical_upload_sessions(id) [constraint=historical_session_fk1]"]}, "core.historical_upload_sessions": {"columns": ["approved_by (int), nullable=YES", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "end_date (date), nullable=NO", "historical_semester_involved (varchar(255)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), default=0, nullable=NO", "requested_by (int), nullable=NO", "status (enum(16)), default=pending_approval, nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP", "users_involved (varchar(255)), nullable=NO"], "foreign_keys": []}, "core.ibas_device_venues": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "device_id (varchar(36)), nullable=NO", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP", "venue_id (int), nullable=NO"], "foreign_keys": ["device_id -> core.ibas_devices(device_id) [constraint=device_id_fk]", "venue_id -> core.venues(id) [constraint=venue_id_fk1]"]}, "core.ibas_devices": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "device_id (varchar(36)), nullable=NO", "institution_id (int), nullable=NO"], "foreign_keys": []}, "core.ibas_failed_staff_attendance": {"columns": ["attempts (int), nullable=NO", "course_code (varchar(45)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "date (date), nullable=NO", "device_id (varchar(45)), nullable=NO", "email (varchar(150)), nullable=NO", "end_time (time), nullable=NO", "id (varchar(36)), nullable=NO", "institution_id (int), nullable=NO", "start_time (time), nullable=NO", "time (time), nullable=NO", "timetable_id (int), nullable=NO", "timetable_placement_id (int), nullable=NO", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP", "venue_name (var<PERSON><PERSON>(45)), nullable=NO"], "foreign_keys": []}, "core.ibas_staff_attendance": {"columns": ["course_code (varchar(45)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "date (date), nullable=NO", "device_id (varchar(45)), nullable=NO", "email (varchar(150)), nullable=NO", "end_time (time), nullable=NO", "id (varchar(36)), nullable=NO", "institution_id (int), nullable=NO", "start_time (time), nullable=NO", "time (time), nullable=NO", "timetable_id (int), nullable=NO", "timetable_placement_id (int), nullable=NO", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP", "venue_name (var<PERSON><PERSON>(45)), nullable=NO"], "foreign_keys": []}, "core.institution_setting_values": {"columns": ["id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "setting_id (int), nullable=NO", "value (text(65535)), nullable=NO"], "foreign_keys": []}, "core.institution_third_party_products": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "product_id (varchar(36)), nullable=NO", "source (enum(18)), default=transflow, nullable=NO", "student_transaction_type_id (int), nullable=NO"], "foreign_keys": []}, "core.institutions": {"columns": ["host (var<PERSON>r(30)), default=, nullable=NO", "id (int), nullable=NO", "merchant_id (varchar(255)), nullable=YES", "NAME (varchar(255)), nullable=NO", "path (varchar(255)), default=, nullable=NO", "workflow_institution_id (int), nullable=YES"], "foreign_keys": []}, "core.jobs": {"columns": ["attempts (tinyint), nullable=NO", "available_at (int), nullable=NO", "created_at (int), nullable=NO", "id (bigint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "payload (longtext(4294967295)), nullable=NO", "queue (varchar(255)), nullable=NO", "reserved_at (int), nullable=YES"], "foreign_keys": []}, "core.lecturer_assessment_sheet_columns": {"columns": ["assessment_sheet_column_id (int), nullable=NO", "column_status (enum(9)), default=assigned, nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "lecturer_assessment_sheet_id (int), nullable=NO", "staff_id (int), nullable=NO", "updated_at (timestamp), nullable=YES", "user_id (int), nullable=YES"], "foreign_keys": []}, "core.lecturer_assessment_sheets": {"columns": ["assessment_sheet_id (bigint), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "status (tinyint), default=1, nullable=YES", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "user_id (int), nullable=NO"], "foreign_keys": []}, "core.levels": {"columns": ["id (tinyint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "label (varchar(30)), nullable=NO", "next_level (varchar(20)), nullable=YES", "value (varchar(20)), nullable=NO"], "foreign_keys": []}, "core.migrations": {"columns": ["batch (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "migration (varchar(255)), nullable=NO"], "foreign_keys": []}, "core.model_has_permissions": {"columns": ["institution_id (int), nullable=NO", "model_id (bigint), nullable=NO", "model_type (varchar(255)), nullable=NO", "permission_id (int), nullable=NO"], "foreign_keys": ["permission_id -> core.permissions(id) [constraint=model_has_permissions_ibfk_1]"]}, "core.model_has_roles": {"columns": ["model_id (bigint), nullable=NO", "model_type (varchar(255)), nullable=NO", "role_id (int), nullable=NO"], "foreign_keys": ["role_id -> core.roles(id) [constraint=model_has_roles_ibfk_1]"]}, "core.modules": {"columns": ["case_category_id (smallint), nullable=NO", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "description (varchar(255)), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(100)), nullable=NO", "updated_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.multi_factor_auths": {"columns": ["auth_code (varchar(255)), nullable=NO", "auth_mode (varchar(50)), nullable=NO", "auth_user (varchar(255)), nullable=NO", "created_at (timestamp), nullable=YES", "expire (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "message (text(65535)), nullable=NO", "updated_at (timestamp), nullable=YES", "validated (tinyint), default=0, nullable=YES"], "foreign_keys": []}, "core.nationalities": {"columns": ["code (varchar(2)), nullable=NO", "demonym (var<PERSON><PERSON>(25)), nullable=YES", "id (smallint), nullable=NO, extra=auto_increment", "name (var<PERSON><PERSON>(60)), nullable=YES", "status (enum(1)), default=0, nullable=YES"], "foreign_keys": []}, "core.notifications": {"columns": ["created_at (timestamp), nullable=YES", "data (text(65535)), nullable=NO", "id (char(36)), nullable=NO", "institution_id (int), nullable=NO", "notifiable_id (bigint), nullable=NO", "notifiable_type (varchar(255)), nullable=NO", "read_at (timestamp), nullable=YES", "sent (tinyint), default=0, nullable=YES", "type (varchar(255)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.oauth_access_tokens": {"columns": ["client_id (int), nullable=NO", "created_at (timestamp), nullable=YES", "expires_at (datetime), nullable=YES", "id (varchar(100)), nullable=NO", "institution_id (int), nullable=NO", "name (varchar(255)), nullable=YES", "revoked (tinyint), nullable=NO", "scopes (text(65535)), nullable=YES", "updated_at (timestamp), nullable=YES", "user_id (int), nullable=YES"], "foreign_keys": []}, "core.oauth_auth_codes": {"columns": ["client_id (int), nullable=NO", "expires_at (datetime), nullable=YES", "id (varchar(100)), nullable=NO", "institution_id (int), nullable=NO", "revoked (tinyint), nullable=NO", "scopes (text(65535)), nullable=YES", "user_id (int), nullable=NO"], "foreign_keys": []}, "core.oauth_clients": {"columns": ["created_at (timestamp), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (varchar(255)), nullable=NO", "password_client (tinyint), nullable=NO", "personal_access_client (tinyint), nullable=NO", "redirect (text(65535)), nullable=NO", "revoked (tinyint), nullable=NO", "secret (varchar(100)), nullable=NO", "updated_at (timestamp), nullable=YES", "user_id (int), nullable=YES"], "foreign_keys": []}, "core.oauth_personal_access_clients": {"columns": ["client_id (int), nullable=NO", "created_at (timestamp), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.oauth_refresh_tokens": {"columns": ["access_token_id (varchar(100)), nullable=NO", "expires_at (datetime), nullable=YES", "id (varchar(100)), nullable=NO", "institution_id (int), nullable=NO", "revoked (tinyint), nullable=NO"], "foreign_keys": []}, "core.offer_letter_template_cc": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "email (varchar(50)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "offer_letter_template_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.offer_letter_template_search_keys": {"columns": ["campus_id (int), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "entry_mode_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "nationality (enum(7)), nullable=YES", "offer_letter_template_id (int), nullable=NO", "program_id (int), nullable=NO", "stream_id (int), nullable=YES", "study_center_id (int), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.offer_letter_template_tags": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "description (varchar(50)), nullable=NO", "field (varchar(50)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "tag (varchar(50)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.offer_letter_templates": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "decision (enum(31)), default=Admit, nullable=YES", "email_content (text(65535)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(50)), nullable=NO", "print_content (text(65535)), nullable=NO", "sms_content (text(65535)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.one_time_pins": {"columns": ["channel (enum(5)), nullable=NO", "channel_id (varchar(50)), nullable=NO", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "pin (varchar(6)), nullable=NO", "type (enum(11)), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP", "verified_at (datetime), nullable=YES"], "foreign_keys": []}, "core.operation_exceptions": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "type (enum(13)), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP", "value (varchar(255)), nullable=NO"], "foreign_keys": []}, "core.options": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "subject_name (varchar(50)), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.password_resets": {"columns": ["created_at (timestamp), nullable=YES", "email (varchar(255)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "token (varchar(255)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.permissions": {"columns": ["created_at (timestamp), nullable=YES", "description (varchar(255)), nullable=YES", "guard_name (varchar(255)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "mutually_exclusive_permissions (varchar(255)), nullable=YES", "name (varchar(255)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.program_affiliations": {"columns": ["affiliation_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "program_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.program_options": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "major_option_id (int), nullable=NO", "minor_option_id (int), nullable=YES", "program_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["program_id -> core.programs(id) [constraint=program_options_ibfk_1]"]}, "core.program_plan_additions": {"columns": ["course_id (int), nullable=NO", "course_type (enum(8)), default=required, nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "program_plan_id (int), nullable=NO", "program_plan_semester_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["program_plan_semester_id -> core.program_plan_semesters(id) [constraint=program_plan_additions_ibfk_1]", "program_plan_id -> core.program_plans(id) [constraint=program_plan_additions_ibfk_2]", "course_id -> core.courses(id) [constraint=program_plan_additions_ibfk_3]"]}, "core.program_plan_courses": {"columns": ["course_id (int), nullable=NO", "course_type (enum(8)), default=required, nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "program_option_id (int), nullable=YES", "program_plan_id (int), nullable=NO", "program_plan_semester_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["program_plan_semester_id -> core.program_plan_semesters(id) [constraint=program_plan_courses_ibfk_1]", "program_plan_id -> core.program_plans(id) [constraint=program_plan_courses_ibfk_2]", "program_option_id -> core.program_options(id) [constraint=program_plan_courses_ibfk_3]", "course_id -> core.courses(id) [constraint=program_plan_courses_ibfk_4]"]}, "core.program_plan_semesters": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "level (varchar(20)), nullable=NO", "maximum_credits (tinyint), nullable=NO", "minimum_credits (tinyint), nullable=NO", "period (tinyint), nullable=YES", "program_plan_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["program_plan_id -> core.program_plans(id) [constraint=program_plan_semesters_ibfk_1]"]}, "core.program_plans": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "minimum_credit (int), nullable=YES", "name (varchar(255)), nullable=YES", "program_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.program_plans_info": {"columns": ["code (varchar(8)), nullable=YES", "course_type (enum(8)), default=required, nullable=YES", "credits (tinyint), default=3, nullable=YES", "level (varchar(20)), nullable=YES", "major (var<PERSON>r(50)), nullable=YES", "period (tinyint), nullable=YES", "plan_name (varchar(255)), nullable=YES", "program_name (varchar(253)), nullable=YES", "title (varchar(200)), nullable=YES"], "foreign_keys": []}, "core.programs": {"columns": ["created_at (timestamp), nullable=YES", "duration (int), nullable=NO", "duration_unit (enum(9)), nullable=YES", "entry_level (varchar(20)), nullable=NO", "exit_level (varchar(20)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "long_name (var<PERSON>r(150)), nullable=NO", "programme_type (varchar(50)), nullable=YES", "short_name (varchar(100)), nullable=NO", "status (enum(1)), default=1, nullable=NO", "unit_id (smallint), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.provisional_results": {"columns": ["assessment_total (decimal), nullable=YES", "campus (varchar(50)), nullable=YES", "code (varchar(8)), nullable=YES", "credits (tinyint), default=3, nullable=YES", "doa (var<PERSON><PERSON>(7)), nullable=YES", "doc (var<PERSON>r(7)), nullable=YES", "exam_total (decimal), nullable=YES", "finalscore (decimal), nullable=YES", "gp (double), default=0.00, nullable=NO", "grade (varchar(5)), nullable=NO", "level (varchar(20)), nullable=YES", "othernames (varchar(100)), default=, nullable=YES", "program (varchar(150)), nullable=YES", "program_short_name (varchar(100)), nullable=YES", "regnumber (varchar(50)), nullable=YES", "semester (varchar(38)), nullable=YES", "stream (varchar(20)), nullable=YES", "surname (var<PERSON><PERSON>(50)), default=, nullable=YES", "title (varchar(200)), nullable=YES"], "foreign_keys": []}, "core.published_results": {"columns": ["assessment_total (decimal), nullable=YES", "campus (varchar(50)), nullable=YES", "course_code (varchar(20)), nullable=YES", "course_credits (tinyint), nullable=YES", "course_title (varchar(255)), nullable=YES", "doa (var<PERSON><PERSON>(7)), nullable=YES", "doc (var<PERSON>r(7)), nullable=YES", "exam_total (decimal), nullable=YES", "finalscore (decimal), nullable=YES", "gp (double), default=0.00, nullable=NO", "grade (varchar(5)), nullable=NO", "level (varchar(20)), nullable=YES", "othernames (varchar(100)), default=, nullable=YES", "program (varchar(150)), nullable=YES", "program_short_name (varchar(100)), nullable=YES", "regnumber (varchar(50)), nullable=YES", "semester (varchar(38)), nullable=YES", "stream (varchar(20)), nullable=YES", "surname (var<PERSON><PERSON>(50)), default=, nullable=YES"], "foreign_keys": []}, "core.query_keywords": {"columns": ["comments (varchar(100)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "keyword (var<PERSON>r(50)), nullable=NO"], "foreign_keys": []}, "core.recommendation_answers": {"columns": ["answers (json), nullable=NO", "applicant_id (int), nullable=NO", "applicant_referee_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "file_path (varchar(200)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "recommendation_form_id (int), nullable=NO", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.recommendation_forms": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "form_instruction (text(65535)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (varchar(100)), nullable=NO", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.recommendation_question_options": {"columns": ["id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "order (int), nullable=NO", "question_option (text(65535)), nullable=NO", "recommendation_question_id (int), nullable=NO"], "foreign_keys": ["recommendation_question_id -> core.recommendation_questions(id) [constraint=recommendation_fk]"]}, "core.recommendation_questions": {"columns": ["answer_type (enum(15)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "hints (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "order (int), nullable=NO", "question (text(65535)), nullable=NO", "recommendation_form_id (int), nullable=NO", "required (enum(3)), default=Yes, nullable=NO", "updated_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.referee_letter_templates": {"columns": ["admission_form_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "email_content (text(65535)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(50)), nullable=NO", "recommendation_form_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.regions": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "name (varchar(255)), nullable=NO", "nationality_id (smallint), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.registration_quota_exemptions": {"columns": ["created_at (timestamp), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "registration_schedule_id (int), nullable=YES", "semester_id (int), nullable=NO", "student_id (var<PERSON>r(36)), nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.registration_schedule_programs": {"columns": ["active (tinyint), default=1, nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "program_id (int), nullable=NO", "registration_schedule_id (int), nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["program_id -> core.programs(id) [constraint=registration_schedule_programs_ibfk_1]", "registration_schedule_id -> core.registration_schedules(id) [constraint=registration_schedule_programs_ibfk_2]"]}, "core.registration_schedules": {"columns": ["active (tinyint), default=1, nullable=NO", "continuing_payment_quota (tinyint), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "doc (char(7)), nullable=NO", "end_date (date), nullable=YES", "fresher_payment_quota (tinyint), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "penalty (tinyint), default=0, nullable=YES", "program_count (int), default=0, nullable=YES", "registration_mode (enum(8)), default=Self, nullable=YES", "resits (enum(5)), default=None, nullable=YES", "semester_id (int), nullable=NO", "start_date (date), nullable=YES", "stream_id (tinyint), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["stream_id -> core.streams(id) [constraint=registration_schedules_ibfk_1]", "semester_id -> core.semesters(id) [constraint=registration_schedules_ibfk_2]"]}, "core.regnumber_codes": {"columns": ["code (varchar(8)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "type (varchar(50)), nullable=NO", "type_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.regnumber_preferences": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "generation_status (enum(36)), nullable=YES", "generation_time (enum(17)), default=After Fee Payment, nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "regnumber_type (enum(12)), nullable=NO", "serial_length (int), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.regnumber_prefix_categories": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (smallint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON>r(25)), nullable=YES", "program_type (varchar(25)), nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.regnumber_prefixes": {"columns": ["alternative (var<PERSON>r(20)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "order (int), nullable=YES", "prefix (varchar(20)), nullable=NO", "regnumber_prefix_category_id (int), nullable=NO", "separator (varchar(1)), nullable=YES", "type (enum(8)), default=Standard, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.regnumber_serial_ranges": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "end_limit (int), nullable=YES", "id (smallint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "program_id (int), nullable=NO", "start_limit (int), nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.regnumber_suffixes": {"columns": ["alternative (var<PERSON>r(20)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "order (int), nullable=YES", "regnumber_prefix_category_id (int), nullable=NO", "separator (varchar(1)), nullable=YES", "suffix (varchar(20)), nullable=NO", "type (enum(8)), default=Standard, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.resit_transactions": {"columns": ["balance (double), nullable=NO", "course_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "receipt_number (varchar(20)), nullable=NO", "semester_id (int), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["student_id -> core.students(id) [constraint=resit_transactions_ibfk_1]", "semester_id -> core.semesters(id) [constraint=resit_transactions_ibfk_2]", "course_id -> core.courses(id) [constraint=resit_transactions_ibfk_3]"]}, "core.role_has_permissions": {"columns": ["permission_id (int), nullable=NO", "role_id (int), nullable=NO"], "foreign_keys": ["permission_id -> core.permissions(id) [constraint=role_has_permissions_ibfk_1]", "role_id -> core.roles(id) [constraint=role_has_permissions_ibfk_2]"]}, "core.roles": {"columns": ["mandate_end_dt (datetime), nullable=YES", "mandate_start_dt (datetime), nullable=YES", "name (varchar(255)), nullable=NO", "type (enum(17)), default=standard user, nullable=YES", "updated_at (timestamp), nullable=YES", "user_count (int), nullable=YES", "created_at (timestamp), nullable=YES", "description (varchar(255)), nullable=YES", "guard_name (varchar(255)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "is_mandate (tinyint), default=0, nullable=YES"], "foreign_keys": []}, "core.scheme_grades": {"columns": ["created_at (datetime), nullable=YES", "gp (float), default=0.00, nullable=NO", "grade (char(6)), default=, nullable=NO", "grading_scheme_id (tinyint), default=0, nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "lower_limit (int), default=0, nullable=NO", "type (enum(17)), default=pass, nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.school_list": {"columns": ["id (int), nullable=NO, extra=auto_increment", "name (varchar(255)), nullable=NO", "type (enum(12)), nullable=YES"], "foreign_keys": []}, "core.semester_bill_totals": {"columns": ["billing_period (varchar(55)), nullable=YES", "currency (varchar(3)), default=, nullable=NO", "end_date (date), nullable=YES", "start_date (date), nullable=YES", "total_bill (double), nullable=YES"], "foreign_keys": []}, "core.semester_types": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(50)), nullable=YES", "periods (tinyint), nullable=YES", "promote_on_period (tinyint), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.semesters": {"columns": ["academic_year_id (int), nullable=NO", "created_at (timestamp), nullable=YES", "end_date (date), nullable=YES", "end_year (year), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "next_semester_id (int), nullable=YES", "period (tinyint), nullable=YES", "semester_type_id (tinyint), nullable=YES", "start_date (date), nullable=YES", "start_year (year), nullable=YES", "status (enum(8)), default=Active, nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.setting_categories": {"columns": ["belongs_to (enum(10)), default=regular, nullable=NO", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(255)), nullable=NO", "position (tinyint), nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.settings": {"columns": ["_lft (int), nullable=YES", "_rgt (int), nullable=YES", "created_at (datetime), nullable=YES", "default_value (varchar(255)), nullable=NO", "description (varchar(255)), nullable=NO", "editable (tinyint), default=1, nullable=YES", "id (int), nullable=NO, extra=auto_increment", "name (varchar(255)), nullable=NO", "option_data (text(65535)), nullable=YES", "parent_id (int), nullable=YES", "position (smallint), default=1, nullable=YES", "setting_category_id (tinyint), nullable=YES", "type (enum(15)), nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.sheet_assignment": {"columns": ["campus (varchar(50)), nullable=NO", "code (varchar(8)), nullable=NO", "credits (tinyint), default=3, nullable=NO", "groupname (var<PERSON><PERSON>(200)), nullable=NO", "lecturer_assigned (var<PERSON><PERSON>(151)), nullable=YES", "semester (varchar(38)), nullable=YES", "student_roll (bigint), default=0, nullable=NO", "title (var<PERSON>r(200)), nullable=NO"], "foreign_keys": []}, "core.sip_mobile_app_versions": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "name (enum(14)), nullable=NO", "status (enum(1)), default=0, nullable=NO", "version (json), nullable=NO"], "foreign_keys": []}, "core.sip_mobile_push_notifications": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "institution_id (int), nullable=NO", "message (varchar(255)), nullable=NO", "receiver_type (enum(7)), nullable=NO", "request_data (json), nullable=NO", "title (varchar(100)), nullable=NO", "topic (varchar(255)), nullable=NO"], "foreign_keys": []}, "core.sip_password_resets": {"columns": ["created_at (timestamp), nullable=YES", "email (varchar(255)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "regnumber (varchar(50)), nullable=YES", "token (varchar(255)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.sip_transcript_layout_programs": {"columns": ["campus_id (int), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "layout (varchar(100)), nullable=NO", "program_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.sip_user_tokens": {"columns": ["created_at (timestamp), nullable=YES", "expiry (datetime), nullable=NO", "id (bigint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "sip_user_id (varchar(255)), nullable=NO", "token (varchar(255)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.sip_users": {"columns": ["created_at (timestamp), nullable=YES", "email (varchar(50)), nullable=NO", "email_verified_at (datetime), nullable=YES", "expiry (timestamp), nullable=YES", "id (char(36)), nullable=NO", "institution_id (int), nullable=NO", "is_logged_in (enum(3)), default=no, nullable=NO", "last_activity (timestamp), nullable=YES", "login_count (int), default=0, nullable=YES", "password (varchar(100)), nullable=NO", "regnumber (varchar(50)), nullable=YES", "remember_token (varchar(100)), nullable=YES", "status (enum(7)), default=active, nullable=NO", "student_id (var<PERSON>r(36)), nullable=YES", "two_factor (enum(3)), default=off, nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["student_id -> core.students(id) [constraint=sip_users_ibfk_1]"]}, "core.sms_notifications": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "message (varchar(160)), nullable=NO", "recipient (var<PERSON><PERSON>(13)), nullable=NO", "recipient_type (enum(9)), nullable=NO", "recipient_type_id (varchar(36)), nullable=NO"], "foreign_keys": []}, "core.staff": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "designation_id (int), nullable=YES", "email (varchar(100)), nullable=NO", "filename (varchar(100)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "mime_type (varchar(100)), nullable=YES", "mobile (varchar(20)), nullable=YES", "othernames (varchar(100)), nullable=NO", "path (varchar(255)), nullable=YES", "prefix (varchar(20)), nullable=NO", "suffix (varchar(20)), nullable=YES", "surname (var<PERSON><PERSON>(50)), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.staff_institutions": {"columns": ["id (bigint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "staff_id (int), nullable=NO"], "foreign_keys": ["staff_id -> core.staff(id) [constraint=staff_fkc]"]}, "core.staff_units": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "staff_id (int), nullable=NO", "unit_id (smallint), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["staff_id -> core.staff(id) [constraint=staff_units_fk1]", "unit_id -> core.units(id) [constraint=staff_units_fk2]"]}, "core.stream_campuses": {"columns": ["campus_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "form_program_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "stream_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.streams": {"columns": ["abbr (char(6)), nullable=NO", "created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(20)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_affiliation_numbers": {"columns": ["affiliation_id (int), nullable=NO", "affiliation_number (varchar(50)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "student_id (var<PERSON>r(36)), nullable=YES", "student_program_id (int), nullable=YES", "student_regno_type_id (tinyint), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.student_award_types": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(100)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_awards": {"columns": ["created_at (timestamp), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(200)), nullable=NO", "semester_id (int), nullable=YES", "student_award_type_id (tinyint), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_awards_ibfk_1]"]}, "core.student_balances": {"columns": ["balance (double), nullable=NO", "billing_period_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "currency_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=YES", "student_transaction_type_id (int), nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["currency_id -> core.currencies(id) [constraint=currency_id_ibfk_1]", "student_id -> core.students(id) [constraint=student_balances_ibfk_1]", "student_program_id -> core.student_programs(id) [constraint=student_balances_ibfk_2]", "billing_period_id -> core.billing_periods(id) [constraint=student_balances_ibfk_3]"]}, "core.student_banks": {"columns": ["bank_integration_id (int), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "request_data (json), nullable=YES", "status (enum(8)), default=opted_in, nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_bill_info": {"columns": ["billing_period (varchar(55)), nullable=YES", "currency (varchar(3)), default=, nullable=NO", "regnumber (varchar(50)), nullable=YES", "transaction_amount (double), nullable=YES", "transaction_date (datetime), nullable=YES", "transaction_description (varchar(100)), nullable=YES"], "foreign_keys": []}, "core.student_bills": {"columns": ["bill_id (int), nullable=YES", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_bills_ibfk_1]", "bill_id -> core.bills(id) [constraint=student_bills_ibfk_2]", "student_program_id -> core.student_programs(id) [constraint=student_bills_ibfk_3]"]}, "core.student_cgpas": {"columns": ["awarded_class (varchar(50)), nullable=YES", "cgpa (double), nullable=YES", "class (varchar(50)), nullable=YES", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "reason_for_awarded_class (varchar(255)), nullable=YES", "status (enum(10)), default=incomplete, nullable=YES", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=NO", "total_cumulative_courses (varchar(10)), nullable=YES", "total_cumulative_credit (varchar(10)), nullable=YES", "total_cumulative_gp (double), nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.student_contact_types": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(100)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_contacts": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "number (varchar(50)), nullable=YES", "student_contact_type_id (tinyint), nullable=YES", "student_id (var<PERSON>r(36)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_contacts_ibfk_1]"]}, "core.student_current_bal": {"columns": ["level (varchar(20)), nullable=YES", "othernames (varchar(100)), default=, nullable=YES", "program (varchar(150)), nullable=YES", "regnumber (varchar(50)), nullable=YES", "statement_balance (double), nullable=YES", "student_transaction_type (varchar(255)), nullable=YES", "surname (var<PERSON><PERSON>(50)), default=, nullable=YES"], "foreign_keys": []}, "core.student_email_format": {"columns": ["alternative (var<PERSON>r(20)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "order (int), nullable=YES", "prefix (varchar(20)), nullable=NO", "separator (varchar(1)), nullable=YES", "type (enum(8)), default=Standard, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_file_types": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(100)), nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.student_files": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "desc (varchar(500)), nullable=NO", "filename (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "mime_type (varchar(255)), nullable=YES", "name (varchar(100)), nullable=NO", "path (varchar(255)), default=, nullable=YES", "reference_no (varchar(20)), default=, nullable=NO", "student_file_type_id (tinyint), nullable=YES", "student_id (varchar(36)), default=0, nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_files_ibfk_1]"]}, "core.student_graduation_bills": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "graduation_bill_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["student_id -> core.students(id) [constraint=stud_grad_fk_1]", "graduation_bill_id -> core.graduation_bills(id) [constraint=stud_grad_fk_2]", "student_program_id -> core.student_programs(id) [constraint=stud_grad_fk_3]"]}, "core.student_guardian_types": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(100)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_guardians": {"columns": ["address (varchar(255)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP", "email (varchar(100)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "mobile (varchar(13)), nullable=YES", "name (var<PERSON>r(150)), nullable=YES", "student_guardian_type_id (tinyint), nullable=NO", "student_id (varchar(36)), default=0, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_guardians_ibfk_1]"]}, "core.student_halls": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "hall_id (tinyint), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "student_id (var<PERSON>r(36)), nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.student_identities": {"columns": ["created_at (timestamp), nullable=YES", "expiry (date), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "number (varchar(20)), nullable=YES", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_identity_type_id (tinyint), nullable=NO", "updated_at (timestamp), nullable=YES", "url (varchar(255)), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_identities_ibfk_1]", "student_identity_type_id -> core.student_identity_types(id) [constraint=student_identities_ibfk_2]"]}, "core.student_identity_types": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(100)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_info": {"columns": ["affiliation_number (varchar(50)), nullable=YES", "campus (varchar(50)), nullable=YES", "doa (var<PERSON><PERSON>(7)), nullable=YES", "dob (date), nullable=NO", "doc (var<PERSON>r(7)), nullable=YES", "email (varchar(100)), nullable=YES", "entry_level (varchar(20)), nullable=YES", "level (varchar(20)), nullable=YES", "mobile (varchar(13)), default=, nullable=NO", "nationality (var<PERSON>r(25)), nullable=YES", "othernames (varchar(100)), default=, nullable=NO", "prefix (varchar(10)), nullable=YES", "program (varchar(150)), nullable=YES", "regnumber (varchar(50)), nullable=YES", "sex (enum(1)), default=M, nullable=NO", "stream (varchar(20)), nullable=YES", "student_program_status (varchar(100)), nullable=YES", "student_status (varchar(30)), default=active, nullable=YES", "suffix (varchar(10)), nullable=YES", "surname (var<PERSON><PERSON>(50)), default=, nullable=NO"], "foreign_keys": []}, "core.student_letter_template_tags": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "description (varchar(50)), nullable=NO", "field (varchar(50)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "tag (varchar(50)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_letter_templates": {"columns": ["amount (decimal), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "currency_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(50)), nullable=NO", "print_content (text(65535)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_meta_column_values": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "student_id (var<PERSON>r(36)), nullable=YES", "student_meta_column_id (char(36)), nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP", "value (varchar(255)), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_meta_column_values_ibfk_1]"]}, "core.student_meta_columns": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "default_value (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "label (varchar(50)), nullable=NO", "option_values (varchar(255)), default=, nullable=YES", "type (enum(7)), default=string, nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.student_payment_wallets": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "details (json), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "type (enum(4)), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.student_photos": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "filename (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "mime_type (varchar(40)), nullable=YES", "path (varchar(200)), nullable=NO", "student_id (var<PERSON>r(36)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_photos_ibfk_1]"]}, "core.student_plans": {"columns": ["created (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "doa (char(7)), nullable=YES", "doc (char(7)), nullable=YES", "id (varchar(36)), nullable=NO", "institution_id (int), nullable=NO", "plan_id (varchar(10)), nullable=NO", "student_id (varchar(10)), nullable=NO"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_plans_ibfk_1]"]}, "core.student_positions": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "end_date (date), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON>r(30)), nullable=YES", "start_date (date), nullable=YES", "student_id (var<PERSON>r(36)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_positions_ibfk_1]"]}, "core.student_program_histories": {"columns": ["campus_id (tinyint), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP", "doa (var<PERSON><PERSON>(7)), nullable=YES", "doc (var<PERSON>r(7)), nullable=YES", "entry_mode_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "level (varchar(20)), nullable=YES", "major_id (int), nullable=YES", "minor_id (int), nullable=YES", "program_id (int), nullable=NO", "program_plan_id (int), nullable=YES", "regnumber (varchar(50)), nullable=NO", "semester_id (int), nullable=YES", "stream_id (tinyint), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=YES", "student_program_status_id (tinyint), nullable=YES", "student_regno_type_id (tinyint), nullable=NO", "student_residency_id (int), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_program_histories_ibfk_1]", "student_program_id -> core.student_programs(id) [constraint=student_program_histories_ibfk_2]"]}, "core.student_program_info": {"columns": ["campus (varchar(50)), nullable=YES", "demonym (var<PERSON><PERSON>(25)), nullable=YES", "doa (var<PERSON><PERSON>(7)), nullable=YES", "doa_year (varchar(4)), nullable=YES", "dob (date), nullable=NO", "doc (var<PERSON>r(7)), nullable=YES", "email (varchar(100)), nullable=YES", "entry_level (varchar(20)), nullable=YES", "entry_mode (varchar(50)), nullable=YES", "gender (enum(1)), default=M, nullable=NO", "level (varchar(20)), nullable=YES", "major (var<PERSON>r(50)), nullable=YES", "mobile (varchar(13)), default=, nullable=NO", "other_contacts (varchar(50)), nullable=YES", "othernames (varchar(100)), default=, nullable=NO", "personal_email_address (varchar(255)), nullable=YES", "program (varchar(150)), nullable=YES", "program_short_name (varchar(100)), nullable=YES", "regnumber (varchar(50)), nullable=YES", "stream (varchar(20)), nullable=YES", "student_program_status (varchar(100)), nullable=YES", "student_status (varchar(30)), default=active, nullable=YES", "surname (var<PERSON><PERSON>(50)), default=, nullable=NO"], "foreign_keys": []}, "core.student_program_status_changes": {"columns": ["comment (text(65535)), nullable=YES", "created_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "semester_id (int), nullable=YES", "student_program_id (int), nullable=YES", "student_program_status_id (tinyint), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": ["student_program_id -> core.student_programs(id) [constraint=student_program_status_changes_ibfk_1]", "student_program_status_id -> core.student_program_statuses(id) [constraint=student_program_status_changes_ibfk_2]", "semester_id -> core.semesters(id) [constraint=student_program_status_changes_ibfk_3]"]}, "core.student_program_statuses": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(100)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_programs": {"columns": ["campus_id (tinyint), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP", "current (tinyint), default=1, nullable=YES", "doa (var<PERSON><PERSON>(7)), nullable=YES", "doc (var<PERSON>r(7)), nullable=YES", "entry_level (varchar(20)), nullable=NO", "entry_mode_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "level (varchar(20)), nullable=YES", "major_id (int), nullable=YES", "minor_id (int), nullable=YES", "previous_student_program_id (int), nullable=YES", "program_id (int), nullable=NO", "program_plan_id (int), nullable=YES", "regnumber (varchar(50)), nullable=NO", "semester_id (int), nullable=YES", "stream_id (tinyint), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_status_id (tinyint), nullable=YES", "student_regno_type_id (tinyint), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_programs_ibfk_1]"]}, "core.student_registration_courses": {"columns": ["assessment_sheet_group_id (int), nullable=YES", "confirmed (tinyint), default=0, nullable=YES", "course_id (int), nullable=YES", "course_type (enum(8)), default=required, nullable=YES", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "registration_source (enum(8)), nullable=NO", "registration_type (enum(7)), default=regular, nullable=YES", "remarks (varchar(255)), nullable=YES", "student_registration_id (int), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": ["course_id -> core.courses(id) [constraint=student_registration_courses_ibfk_1]", "assessment_sheet_group_id -> core.assessment_sheet_groups(id) [constraint=student_registration_courses_ibfk_2]", "student_registration_id -> core.student_registrations(id) [constraint=student_registration_courses_ibfk_3]"]}, "core.student_registration_data": {"columns": ["campus (varchar(50)), nullable=YES", "code (varchar(8)), nullable=YES", "completed (tinyint), default=0, nullable=YES", "confirmed (tinyint), default=0, nullable=YES", "credits (tinyint), default=3, nullable=YES", "doa (var<PERSON><PERSON>(7)), nullable=YES", "doc (var<PERSON>r(7)), nullable=YES", "entry_level (varchar(20)), nullable=YES", "gender (enum(1)), default=M, nullable=YES", "is_resit (varchar(3)), default=, nullable=NO", "level (varchar(20)), nullable=YES", "program (varchar(150)), nullable=YES", "program_short_name (varchar(100)), nullable=YES", "regnumber (varchar(50)), nullable=YES", "semester (varchar(38)), nullable=YES", "student_name (var<PERSON><PERSON>(151)), nullable=YES", "title (varchar(200)), nullable=YES"], "foreign_keys": []}, "core.student_registrations": {"columns": ["completed (tinyint), default=0, nullable=YES", "created_at (datetime), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "registration_schedule_id (int), nullable=YES", "semester_id (int), nullable=YES", "student_id (char(36)), nullable=YES", "student_program_id (int), nullable=YES", "type (enum(7)), default=regular, nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_registrations_ibfk_1]", "student_program_id -> core.student_programs(id) [constraint=student_registrations_ibfk_2]", "registration_schedule_id -> core.registration_schedules(id) [constraint=student_registrations_ibfk_3]", "semester_id -> core.semesters(id) [constraint=student_registrations_ibfk_4]"]}, "core.student_regno_types": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (varchar(50)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_residencies": {"columns": ["created_at (timestamp), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "location (varchar(255)), nullable=YES", "student_id (var<PERSON>r(36)), nullable=YES", "type (enum(12)), default=Non-Resident, nullable=YES", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.student_review_types": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(100)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_reviews": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "message (text(65535)), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_review_type_id (tinyint), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["student_review_type_id -> core.student_review_types(id) [constraint=student_reviews_ibfk_1]"]}, "core.student_scholarship_bill_item_types": {"columns": ["bill_item_type_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "student_scholarship_id (int), nullable=NO"], "foreign_keys": ["student_scholarship_id -> core.student_scholarships(id) [constraint=scholar_bill_item_ibfk_1]"]}, "core.student_scholarship_transactions": {"columns": ["created_at (datetime), nullable=NO", "id (bigint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "student_scholarship_id (bigint), nullable=NO", "student_transaction_id (bigint), nullable=NO", "updated_at (datetime), nullable=NO"], "foreign_keys": []}, "core.student_scholarships": {"columns": ["billing_period_id (int), nullable=NO", "created_at (datetime), nullable=YES", "currency_id (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "source (enum(6)), default=single, nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=YES", "type (enum(10)), default=percentage, nullable=YES", "updated_at (datetime), nullable=YES", "value (decimal), nullable=NO"], "foreign_keys": ["billing_period_id -> core.billing_periods(id) [constraint=student_scholarships_ibfk_1]", "student_id -> core.students(id) [constraint=student_scholarships_ibfk_2]", "student_program_id -> core.student_programs(id) [constraint=student_scholarships_ibfk_3]"]}, "core.student_semester_gpas": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "cumulative_gp (double), nullable=YES", "gpa (double), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "semester_id (int), nullable=NO", "status (enum(10)), default=incomplete, nullable=YES", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=NO", "total_courses (varchar(10)), nullable=YES", "total_credit (varchar(10)), nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.student_status_types": {"columns": ["created_at (timestamp), nullable=YES", "id (tinyint), nullable=NO, extra=auto_increment", "name (varchar(100)), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_statuses": {"columns": ["comments (varchar(255)), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED on update CURRENT_TIMESTAMP", "id (int), nullable=NO, extra=auto_increment", "semester_id (int), nullable=YES", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_status_type_id (tinyint), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=student_statuses_ibfk_1]", "student_status_type_id -> core.student_status_types(id) [constraint=student_statuses_ibfk_2]"]}, "core.student_study_centers": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=NO", "study_center_program_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.student_transaction_types": {"columns": ["created_at (datetime), nullable=YES", "description (varchar(255)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "is_fees (enum(1)), default=N, nullable=NO", "name (varchar(255)), nullable=YES", "transflow_product_id (int), nullable=YES", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.student_transactions": {"columns": ["allotment_balance (double), nullable=YES", "allotment_status (enum(18)), nullable=YES", "bank_id (int), nullable=YES", "billing_period_id (int), nullable=YES", "cheque_date (date), nullable=YES", "cheque_desc (varchar(60)), nullable=YES", "cheque_no (var<PERSON><PERSON>(20)), nullable=YES", "cheque_value_date (date), nullable=YES", "converted_amount (double), nullable=YES", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "currency_exchange_id (int), nullable=YES", "currency_id (int), default=1, nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "level (varchar(20)), nullable=YES", "payer_name (varchar(255)), nullable=YES", "receipt_number (varchar(25)), nullable=YES", "reversal (int), nullable=YES", "source (enum(8)), default=frontend, nullable=YES", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=YES", "student_transaction_type_id (int), nullable=YES", "transaction_amount (double), nullable=YES", "transaction_date (datetime), nullable=YES", "transaction_description (varchar(100)), nullable=YES", "type (enum(6)), default=credit, nullable=NO", "updated_at (datetime), nullable=YES", "user_id (int), nullable=YES"], "foreign_keys": ["student_transaction_type_id -> core.student_transaction_types(id) [constraint=student_transactions_ibfk_1]"]}, "core.students": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "dob (date), nullable=NO", "email (varchar(100)), nullable=YES", "id (varchar(36)), nullable=NO", "institution_id (int), nullable=NO", "mobile (varchar(13)), default=, nullable=NO", "nationality_id (smallint), nullable=YES", "osisv1_studid (int), nullable=YES", "othernames (varchar(100)), default=, nullable=NO", "prefix (varchar(10)), nullable=YES", "sex (enum(1)), default=M, nullable=NO", "status (varchar(30)), default=active, nullable=YES", "suffix (varchar(10)), nullable=YES", "surname (var<PERSON><PERSON>(50)), default=, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["status -> core.student_status_types(name) [constraint=students_ibfk_1]"]}, "core.study_center_programs": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "program_id (int), nullable=NO", "study_center_id (int), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["study_center_id -> core.study_centers(id) [constraint=study_center_ibfk1]"]}, "core.study_centers": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "geolocation (varchar(20)), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (varchar(255)), nullable=NO", "status (enum(8)), default=active, nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.template_images": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "filename (varchar(255)), nullable=NO", "institution_id (int), nullable=NO", "path (varchar(200)), nullable=NO", "type (varchar(20)), nullable=NO"], "foreign_keys": []}, "core.timetable_notes": {"columns": ["created_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "note (tinytext(255)), nullable=NO", "note_calloff (enum(1)), default=0, nullable=NO", "note_date (date), nullable=NO", "title (varchar(100)), default=, nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.timetable_pending_placements": {"columns": ["assessment_sheet_id (bigint), nullable=NO", "capacity (int), nullable=YES", "created_at (datetime), nullable=NO", "credits (int), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "timetable_id (int), nullable=NO", "updated_at (datetime), nullable=NO", "user_id (int), nullable=YES"], "foreign_keys": ["timetable_id -> core.timetables(id) [constraint=timetable_pending_placement_fk]"]}, "core.timetable_periods": {"columns": ["created_at (datetime), nullable=YES", "end (time), default=00:00:00, nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "period (varchar(20)), default=, nullable=NO", "start (time), default=00:00:00, nullable=NO", "timetable_type_id (int), nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.timetable_placements": {"columns": ["assessment_sheet_id (bigint), nullable=NO", "capacity (int), nullable=YES", "changed_info (json), nullable=YES", "created_at (datetime), nullable=NO", "credits (tinyint), nullable=NO", "date (datetime), nullable=YES", "day (enum(9)), nullable=NO", "end_period_id (int), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "start_period_id (int), nullable=NO", "timetable_id (int), nullable=NO", "updated_at (datetime), nullable=NO", "user_id (int), nullable=NO", "venue_id (int), nullable=NO"], "foreign_keys": ["assessment_sheet_id -> core.assessment_sheets(id) [constraint=assessment_sheet_id]", "end_period_id -> core.timetable_periods(id) [constraint=end_period_id]", "start_period_id -> core.timetable_periods(id) [constraint=start_period_id]", "timetable_id -> core.timetables(id) [constraint=timetable_id]", "user_id -> core.users(id) [constraint=user_id]", "venue_id -> core.venues(id) [constraint=venue_id]"]}, "core.timetable_types": {"columns": ["created_at (timestamp), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "name (var<PERSON><PERSON>(50)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.timetables": {"columns": ["active (tinyint), nullable=NO", "campus_id (tinyint), nullable=NO", "created_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "semester_id (int), default=0, nullable=NO", "streams (varchar(255)), nullable=YES", "timetable_type_id (int), nullable=NO", "unit_id (smallint), nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": ["semester_id -> core.semesters(id) [constraint=semester_id]", "timetable_type_id -> core.timetable_types(id) [constraint=timetable_type_id]", "unit_id -> core.units(id) [constraint=unit_id]"]}, "core.transaction_requests": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "data (json), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "requested_by (var<PERSON><PERSON>(36)), nullable=NO", "reviewed_by (int), nullable=YES", "status (enum(18)), default=pending, nullable=NO", "student_id (var<PERSON><PERSON>(36)), nullable=NO", "student_program_id (int), nullable=NO", "type (enum(18)), nullable=NO", "updated_at (timestamp), nullable=YES"], "foreign_keys": []}, "core.transflow_alt1": {"columns": ["account_number (varchar(14)), nullable=YES", "alt1 (varchar(100)), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO"], "foreign_keys": []}, "core.transflow_payments": {"columns": ["bank_code (varchar(10)), nullable=YES", "created_at (datetime), nullable=YES", "data (json), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "processed (tinyint), nullable=YES", "transaction_date (date), nullable=YES", "transaction_id (varchar(50)), nullable=YES", "updated_at (datetime), nullable=YES"], "foreign_keys": []}, "core.unit_types": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (var<PERSON><PERSON>(20)), nullable=NO", "parent_id (int), nullable=YES", "scope (enum(14)), default=academic, nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.unit_venues": {"columns": ["created_at (datetime), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "unit_id (int), nullable=NO", "updated_at (datetime), nullable=YES", "venue_id (int), nullable=NO"], "foreign_keys": ["venue_id -> core.venues(id) [constraint=venue_FK]"]}, "core.units": {"columns": ["_lft (smallint), nullable=YES", "_rgt (smallint), nullable=YES", "abbreviated_name (varchar(25)), nullable=YES", "campus_id (tinyint), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "id (smallint), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "official_name (varchar(100)), nullable=NO", "parent_id (int), nullable=YES", "unit_type_id (int), nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["unit_type_id -> core.unit_types(id) [constraint=units_ibfk_1]", "campus_id -> core.campuses(id) [constraint=units_ibfk_2]"]}, "core.unredeemed_resit_list": {"columns": ["campus (varchar(50)), nullable=YES", "count_of_courses (bigint), default=0, nullable=NO", "failed_courses (text(65535)), nullable=YES", "long_name (varchar(150)), nullable=YES", "othernames (varchar(100)), default=, nullable=YES", "program_short_name (varchar(100)), nullable=YES", "regnumber (varchar(50)), nullable=YES", "surname (var<PERSON><PERSON>(50)), default=, nullable=YES"], "foreign_keys": []}, "core.usage_deductions": {"columns": ["applicant_id (int), nullable=YES", "charged_item_name (varchar(100)), nullable=NO", "created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "deducted (enum(3)), default=no, nullable=NO", "extra_info (json), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "semester_id (int), nullable=YES", "student_id (var<PERSON>r(36)), nullable=YES", "student_program_id (int), nullable=YES", "updated_at (timestamp), nullable=YES"], "foreign_keys": ["student_id -> core.students(id) [constraint=studentfk]"]}, "core.user_roles": {"columns": ["designation (varchar(255)), nullable=YES", "email (varchar(100)), nullable=NO", "mobile (varchar(20)), nullable=YES", "othernames (varchar(100)), nullable=NO", "prefix (varchar(20)), nullable=NO", "role (varchar(255)), nullable=YES", "suffix (varchar(20)), nullable=YES", "surname (var<PERSON><PERSON>(50)), nullable=NO"], "foreign_keys": []}, "core.users": {"columns": ["created_at (timestamp), nullable=YES", "email (varchar(50)), nullable=NO", "expiry (timestamp), nullable=YES", "id (int), nullable=NO, extra=auto_increment", "is_logged_in (enum(3)), default=no, nullable=NO", "last_activity (timestamp), nullable=YES", "login_count (int), default=0, nullable=YES", "password (varchar(100)), nullable=NO", "preferred_locale (enum(2)), nullable=YES", "remember_token (varchar(100)), nullable=YES", "staff_id (int), nullable=YES", "status (enum(7)), default=active, nullable=NO", "two_factor (enum(3)), default=off, nullable=NO", "updated_at (timestamp), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": ["staff_id -> core.staff(id) [constraint=users_ibfk_1]"]}, "core.venues": {"columns": ["campus_id (tinyint), nullable=NO", "class_capacity (int), default=0, nullable=NO", "created_at (datetime), nullable=YES", "exam_capacity (int), default=0, nullable=NO", "handicap_acc (set(37)), default=Visually Impaired, nullable=YES", "id (int), nullable=NO, extra=auto_increment", "institution_id (int), nullable=NO", "name (varchar(200)), default=, nullable=NO", "parent_id (int), nullable=YES", "status (enum(7)), default=active, nullable=NO", "type (enum(10)), default=Lecture, nullable=NO", "updated_at (datetime), nullable=YES"], "foreign_keys": ["campus_id -> core.campuses(id) [constraint=campus_id]"]}, "core.waec_institution": {"columns": ["created_at (datetime), default=CURRENT_TIMESTAMP, nullable=NO, extra=DEFAULT_GENERATED", "exam_year (year), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "index_number (varchar(20)), nullable=NO", "institution_id (int), nullable=NO", "updated_at (datetime), nullable=YES, extra=on update CURRENT_TIMESTAMP"], "foreign_keys": []}, "core.waec_results": {"columns": ["created_at (timestamp), default=CURRENT_TIMESTAMP, nullable=YES, extra=DEFAULT_GENERATED", "exam_year (year), nullable=NO", "id (int), nullable=NO, extra=auto_increment", "index_number (varchar(20)), nullable=NO", "status (smallint), default=0, nullable=NO", "updated_at (timestamp), nullable=YES", "waec_data (json), nullable=NO"], "foreign_keys": []}}
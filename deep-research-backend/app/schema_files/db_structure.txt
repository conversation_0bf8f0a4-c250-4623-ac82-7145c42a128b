activity_log PK : id FK : institution_id -> institutions.id    : log_name    : description    : subject_id    : subject_type    : causer_id    : causer_type    : student_id    : ip_address    : user_agent    : properties    : created_at    : updated_at  
applicant_programs PK : id FK : applicant_id -> applicants.id    : program_id -> programs.id    : choice    : created_at    : updated_at  
applicants PK : id FK : institution_id -> institutions.id    : surname    : othernames    : prefix    : suffix    : sex    : dob    : nationality_id    : mobile    : email    : form_id    : created_at    : updated_at    : decision    : regnumber    : temp_number    : entrance_number    : is_student    : student_id    : program_id    : entry_level    : campus_id    : stream_id    : entry_mode_id    : offer_letter_template_id    : letter_last_printed_at    : date_of_issue    : source    : status  
assessment_approved_results PK : id    : assessment_sheet_id    : student_id FK : assessment_result_id -> assessment_results.id    : assessment_sheet_id -> assessment_sheets.id    : student_id -> students.id    : student_program_id -> student_programs.id    : semester_id -> semesters.id    : institution_id    : grade    : assessment_total    : exam_total    : finalscore    : gp    : position    : remarks    : registration_source    : created_at    : updated_at  
assessment_column_scores PK : id FK : assessment_sheet_column_id -> assessment_sheet_columns.id    : student_id -> students.id    : institution_id    : score    : count_changes    : created_at    : updated_at  
assessment_results PK : id    : assessment_sheet_id    : student_id FK : institution_id    : previous_assessment_result_id -> assessment_results.id    : student_program_id -> student_programs.id    : assessment_sheet_id -> assessment_sheets.id    : student_id -> students.id  
assessment_sheet_columns PK : id FK : assessment_sheet_id -> assessment_sheets.id    : institution_id    : number    : name    : value    : description    : colour    : type    : created_at    : updated_at  
assessment_sheet_group_settings PK : id FK : institution_id    : semester_id    : course_id    : method    : auto_group_by    : quota    : max_groups    : status    : created_at    : updated_at  
assessment_sheet_groups PK : id FK : institution_id    : assessment_sheet_group_setting_id    : groupname    : semester_id    : course_id    : course_type    : student_count    : created_at    : updated_at  
assessment_sheet_stages PK : id FK : institution_id    : assessment_sheet_id    : assessment_sheet_status_id    : user_id    : remarks    : created_at    : updated_at  
assessment_sheet_statuses PK : id FK : institution_id    : position    : label    : description    : permitted_roles    : permitted_actions    : created_at    : updated_at  
assessment_sheets PK : id FK : institution_id    : semester_id -> semesters.id    : course_id -> courses.id    : course_type    : assessment_sheet_group_id -> assessment_sheet_groups.id    : grading_scheme_id -> grading_schemes.id    : created_at    : updated_at    : assessment_sheet_status_id    : approved  
bill_categories PK : id FK : institution_id    : program_id -> programs.id    : doa    : doc    : level    : campus_id    : stream_id    : nationality_type    : entry_mode_id    : created_at    : updated_at  
bill_item_ratios PK : id FK : institution_id    : bill_item_id    : ratio  
bill_item_types PK : id FK : institution_id    : name    : priority    : created_at    : updated_at  
bill_items PK : id FK : bill_id -> bills.id    : bill_item_type_id -> bill_item_types.id    : amount    : currency_id -> currencies.id    : created_at    : updated_at    : deleted_at  
billing_periods PK : id FK : institution_id    : description    : group_determinant    : start_date    : end_date    : status    : created_at    : updated_at  
bills PK : id FK : institution_id    : billing_period_id -> billing_periods.id    : bill_category_id -> bill_categories.id    : total_due    : currency_id    : created_at    : updated_at  
campuses PK : id FK : institution_id    : name    : location    : campus_type_id    : created_at    : updated_at  
case_attachments PK : id FK : case_id -> cases.id    : file_path    : created_at  
case_categories PK : id    : name    : description    : created_at    : updated_at  
case_correspondence PK : id FK : case_id -> cases.id    : contact_email    : message    : attachments    : type    : reopen_case    : created_at    : updated_at  
case_severities PK : name  
cases PK : id FK : case_category_id -> case_categories.id    : module_id    : institution_id    : title    : description    : requested_by    : severity    : status    : created_at    : updated_at  
courses PK : id FK : institution_id    : code    : title    : credits    : level    : unit_id -> units.id    : semester    : status    : created_at    : updated_at  
currencies PK : id    : code    : description    : created_at    : updated_at  
document_requests PK : id FK : institution_id    : invoice    : student_id    : student_program_id    : student_letter_template_id    : copies    : description    : currency_id    : amount    : status    : created_at    : updated_at  
entry_modes PK : id FK : institution_id    : entry_mode    : created_at    : updated_at  
file_imports PK : id FK : institution_id    : reference_no    : path    : filename    : mime_type    : import_type    : status    : created_at    : updated_at  financial_aid_request_students FK : institution_id    : student_program_id    : financial_aid_request_reference_number    : currency_id    : amount    : student_transaction_id    : created_at  
grade_options PK : id    : value    : label    : type  
grading_scheme_complements PK : id    : grading_scheme_id    : complementary_scheme_id    : created_at    : updated_at  
grading_scheme_gpas PK : id FK : institution_id    : grading_scheme_id    : gpa    : class    : order    : created_at    : updated_at  
grading_scheme_maps PK : id    : scheme_grade_id    : complementary_scheme_grade_id    : created_at    : updated_at  
grading_schemes PK : id FK : institution_id    : scheme_name    : assessment_total    : exam_total    : description    : status    : created_at    : updated_at  
halls PK : id FK : institution_id    : name    : location    : address    : created_at    : updated_at  
historical_upload_files PK : id FK : historical_upload_session_id -> historical_upload_sessions.id    : institution_id    : reference_no    : filename    : path    : status    : meta_data    : created_at    : updated_at  
ibas_device_venues PK : device_id FK : venue_id -> venues.id    : created_at    : updated_at  
ibas_devices PK : device_id FK : institution_id    : created_at  
institution_third_party_products PK : id FK : institution_id    : student_transaction_type_id -> student_transaction_types.id    : source    : product_id  
institutions PK : id    : NAME    : host    : path  
institutissssons PK : id    : NAME  
modules PK : id    : name    : description    : case_category_id    : created_at    : updated_at  
nationalities PK : id    : code    : name    : demonym    : status  
program_options PK : id FK : institution_id    : program_id -> programs.id    : major_option_id    : minor_option_id  
program_plan_additions PK : id FK : institution_id    : program_plan_semester_id    : program_plan_id    : course_id  
program_plan_courses PK : id FK : institution_id    : program_plan_semester_id    : program_plan_id    : program_option_id    : course_id  
program_plan_semesters PK : id FK : program_plan_id -> program_plans.id    : level    : semester_id -> semesters.id    : minimum_credits    : maximum_credits  
program_plans PK : id FK : institution_id    : doc    : program_id -> programs.id    : minimum_credit  
programs PK : id FK : institution_id    : unit_id    : short_name    : long_name    : programme_type    : duration    : duration_unit    : entry_level    : exit_level    : status  
registration_quota_exemptions PK : id    : student_id    : semester_id    : registration_schedule_id  
registration_schedule_programs PK : id FK : registration_schedule_id    : program_id  
registration_schedules PK : id FK : stream_id    : semester_id  
scheme_grades PK : id FK : institution_id    : grading_scheme_id    : grade    : lower_limit    : gp    : type    : created_at    : updated_at  
semesters PK : id FK : institution_id    : semester_type_id    : start_year    : end_year    : period    : start_date    : end_date    : status    : next_semester_id  
streams PK : id FK : institution_id    : abbr    : name  
student_affiliation_numbers PK : id FK : institution_id    : student_id    : student_program_id    : affiliation_number    : student_regno_type_id    : affiliation_id  
student_award_types PK : id    : name  
student_awards PK : id FK : institution_id    : student_id    : student_award_type_id    : name    : semester_id  
student_balances PK : id FK : institution_id    : student_id    : student_program_id    : student_transaction_type_id    : billing_period_id    : currency_id  
student_bills PK : id FK : student_id    : student_program_id    : bill_id  
student_contact_types PK : id    : name  
student_contacts PK : id FK : institution_id    : student_id    : student_contact_type_id    : number  
student_files PK : id FK : institution_id    : student_id    : student_file_type_id    : name    : desc    : reference_no    : path    : filename  
student_guardian_types PK : id    : name  
student_guardians PK : id FK : institution_id    : student_id    : student_guardian_type_id    : name    : address    : mobile    : email  
student_halls PK : id FK : institution_id    : student_id    : hall_id  
student_identities PK : id FK : student_id    : student_identity_type_id    : number    : expiry    : url  
student_identity_types PK : id    : name  
student_imports PK : id FK : institution_id    : reference_no    : path    : filename  
student_meta_column_values PK : id FK : student_id    : student_meta_column_id    : value  
student_meta_columns PK : id FK : institution_id    : type    : label    : default_value    : option_values  
student_photos PK : id FK : institution_id    : student_id    : mime_type    : path    : filename  
student_program_histories PK : id FK : institution_id    : student_program_id    : student_id    : program_id    : campus_id    : semester_id    : student_program_status_id    : student_regno_type_id    : stream_id    : regnumber  
student_program_statuses PK : id    : name  
student_programs PK : id FK : institution_id    : student_id    : program_id    : semester_id    : campus_id    : student_program_status_id    : student_regno_type_id    : stream_id    : regnumber  
student_registration_courses PK : id FK : institution_id    : student_registration_id    : course_id    : assessment_sheet_group_id  
student_registrations PK : id FK : institution_id    : student_id    : student_program_id    : registration_schedule_id    : semester_id  
student_residencies PK : id FK : institution_id    : student_id    : type    : location  
student_review_types PK : id    : name  
student_scholarship_bill_item_types PK : id FK : student_scholarship_id -> student_scholarships.id    : bill_item_type_id  
student_scholarship_transactions PK : id FK : institution_id    : student_scholarship_id    : student_transaction_id  
student_scholarships PK : id FK : institution_id    : billing_period_id    : student_id    : student_program_id    : value    : currency_id  
student_statuses PK : id FK : student_id    : semester_id    : student_status_type_id  
student_transaction_types PK : id    : name    : is_fees    : description  
student_transactions PK : id FK : student_transaction_type_id    : receipt_number    : transaction_date    : transaction_description    : transaction_amount    : student_program_id    : currency_id    : currency_exchange_id    : converted_amount    : student_id    : billing_period_id  
students PK : id FK : institution_id    : surname    : othernames    : prefix    : suffix    : sex    : dob    : nationality_id    : mobile    : email    : status  
transflow_alt1 PK : id    : institution_id    : alt1    : account_number  
transflow_payments PK : id    : institution_id    : transaction_id    : transaction_date    : data    : processed    : bank_code  
units PK : id FK : institution_id    : unit_type_id    : parent_id    : official_name    : abbreviated_name    : campus_id  
venues PK : id FK : institution_id    : campus_id    : name    : class_capacity    : exam_capacity    : status    : type    : handicap_acc    : created_at    : updated_at

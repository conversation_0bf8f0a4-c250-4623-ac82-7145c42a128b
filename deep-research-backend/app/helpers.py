from langchain_elasticsearch import ElasticsearchStore
from langchain_core.documents import Document
from app.chains.uninformed_outline_generator import report_outline_chain
from app.chains.informed_outline_generator import informed_report_outline_chain
from app.chains.refined_outline_generator import refine_report_outline_chain
from app.chains.filter_answerable_questions import batch_question_verification_chain
from app.chains.data_gap_section_writer import missing_data_section_chain
from app.chains.section_writer import section_writer_chain
from app.chains.expand_question import expanded_questions_chain
from app.chains.composite.interview import batch_interview
from app.vectorstore.client import elastic_vector_search
import logging
import re


def generate_question_markdown(question_data: dict, check_results: list) -> str:
    output_lines = []

    for question, result in zip(question_data['questions'], check_results):
        status = "✅ **Answerable**" if result['answerable'] else "❌ **Not Answerable**"
        output_lines.append(f"**{question}**  \n{status}  \n- {result['reasoning']}\n")

    return '\n'.join(output_lines)

from typing import List, Dict

def interview_to_docs(
    result: Dict,  original_question: str                    # payload returned by interview_chain
) -> List[Document]:
    """
    Turns the interview output into a list of Document objects ready for upsert.
    Each turn → one vector (Question + Answer text) + metadata.
    """
    from app.utils.debug_logger import debug_logger

    logger = debug_logger.get_logger("INTERVIEW_TO_DOCS")
    debug_logger.log_separator("CONVERTING INTERVIEW TO DOCUMENTS")

    docs: List[Document] = []

    logger.info(f"📝 Original question: '{original_question}'")
    logger.info(f"🗣️ Interview result keys: {list(result.keys())}")

    if "convo_with_data" not in result:
        logger.warning("⚠️ No 'convo_with_data' found in interview result!")
        return docs

    convo_data = result["convo_with_data"]
    logger.info(f"💬 Number of conversation turns: {len(convo_data)}")

    print("CONVERTING TO VECTOR STORE DOCS\n")
    for idx, turn in enumerate(result["convo_with_data"]):
        # q, a = turn["question"], turn["answer"]
        q, a, pt, d = turn["question"], turn["answer"], turn["presentation_type"], turn["data"]
        dt = turn.get("data_tag", "no_data_tag")  # Handle missing data_tag gracefully

        logger.info(f"    🔄 Turn {idx+1}:")
        logger.info(f"      ❓ Question: {q[:100]}...")
        logger.info(f"      💬 Answer: {a[:100]}...")
        logger.info(f"      📊 Presentation type: {pt}")
        logger.info(f"      📈 Data: {str(d)[:100]}...")
        logger.info(f"      🏷️ Data tag: {dt}")

        page_content = f"Question: {q}\nAnswer: {a}"

        metadata = {
            "conversation_id": original_question,
            "turn_index": idx,
            "partition": "interview",
            # "question": q,
            # "answer": a,
            "timestamp": result["timestamp"],
        }


        if d != "No results":
            logger.info(f"      ✅ Data found - setting data_returned=True")
            print("\n Checking supposed non empty data")
            print(d)
            print("================================= \n")
            metadata["data_returned"] = True

            if pt != "text":
                metadata["data_tag"] = dt
                logger.info(f"      🏷️ Added data_tag: {dt}")
                # print(f"\n{idx}. {d}")
                # print(f"{pt}: {dt}\n")
        else:
            logger.info(f"      ❌ No data found - setting data_returned=False")
            metadata["data_returned"] = False

        docs.append(Document(page_content=page_content, metadata=metadata))

    data_returned_docs = sum(1 for doc in docs if doc.metadata.get('data_returned', False))
    logger.info(f"  📊 SUMMARY: Created {len(docs)} documents, {data_returned_docs} with data_returned=True")

    return docs


def upsert_docs(docs: list[Document], store: ElasticsearchStore):
    """
    If a doc with the same ID already exists, it is overwritten.
    """
    from app.utils.debug_logger import debug_logger

    logger = debug_logger.get_logger("UPSERT_DOCS")
    debug_logger.log_separator("UPSERTING DOCUMENTS TO ELASTICSEARCH")

    logger.info(f"📝 Attempting to upsert {len(docs)} documents")

    if not docs:
        logger.warning("⚠️ No documents to upsert!")
        return

    # Log document details
    data_returned_count = 0
    for i, doc in enumerate(docs):
        logger.info(f"  📄 Doc {i+1}:")
        logger.info(f"    Content: {doc.page_content[:100]}...")
        logger.info(f"    Metadata: {doc.metadata}")
        if doc.metadata.get('data_returned', False):
            data_returned_count += 1

    logger.info(f"✅ Documents with data_returned=True: {data_returned_count}/{len(docs)}")

    ids = [
        f"{doc.metadata['conversation_id']}_{doc.metadata['timestamp']}_{doc.metadata['turn_index']}"
        for doc in docs
    ]

    try:
        store.add_documents(docs, ids=ids, bulk_kwargs={"request_timeout": 120})       # bulk upsert with timeout
        logger.info(f"✅ Successfully upserted {len(docs)} documents to Elasticsearch")
    except Exception as e:
        logger.error(f"❌ Error upserting documents: {str(e)}")
        # Fail fast: report generation relies on these vectors for context retrieval
        raise


from typing import List, Dict, Any

def extract_q_a_with_data(interviews_obj: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    From the batch-interview output, return a flat list of
    {"question": ..., "answer": ...} where turn["data"] != "No results".
    """
    keep: List[Dict[str, str]] = []

    # interviews_obj["interviews"] is the list produced by batch_interview
    for interview in interviews_obj.get("interviews", []):
        for turn in interview.get("convo_with_data", []):
            if turn.get("data") != "No results":
                logging.info(repr(turn.get("data")))
                keep.append({
                    "question": turn["question"],
                    "answer":   turn["answer"],
                })

    return keep

def extract_q_a_without_data(interviews_obj: Dict[str, Any]) -> List[Dict[str, str]]:
    """
    From the batch-interview output, return a flat list of
    {"question": ..., "answer": ...} where turn["data"] != "No results".
    """
    keep: List[Dict[str, str]] = []

    # interviews_obj["interviews"] is the list produced by batch_interview
    for interview in interviews_obj.get("interviews", []):
        for turn in interview.get("convo_with_data", []):
            if turn.get("data") == "No results":
                logging.info(repr(turn.get("data")))
                keep.append({
                    "question": turn["question"],
                    "answer":   turn["answer"],
                })

    return keep

def split_markdown_sections(markdown_text):
    # Find all level 2 sections (## headings) and split the content based on them
    # This regex captures the heading as well as the content that follows it
    pattern = r"(##\s.*?)(?=\n##\s|\Z)"  # Match '## ' to next '## ' or end of string
    sections = re.findall(pattern, markdown_text, re.DOTALL)
    return [section.strip() for section in sections]

def interviews_to_report(question_schema_dict, include_data_gaps):

    original_question = question_schema_dict["question"]
    uninformed_outline = report_outline_chain.invoke({
        "original_question": original_question
    })
        
    output = expanded_questions_chain.invoke({
        "schema_text":  question_schema_dict["schema_text"],
        "question": original_question,
        "feedback": ""
    })

    questions_list = batch_question_verification_chain.invoke(output)
    
    interviews = batch_interview(questions_list)
    data_backed_convos = extract_q_a_with_data(interviews)
    data_lacking_convos = extract_q_a_without_data(interviews)
    
    informed_outline = informed_report_outline_chain.invoke({
        'original_question': original_question,
        'interview_histories': data_backed_convos
    })

    refined_outline = refine_report_outline_chain.invoke({
        "informed_outline": informed_outline, "uninformed_outline": uninformed_outline, "original_question": original_question
    })

    interviews = [ interview for interview in interviews["interviews"] if len(interview["convo"]) > 0 ]
    data_dict_list = [interview["data"] for interview in interviews]
    
    extended_data = {}
    for data_dict in data_dict_list:
        extended_data.update(data_dict)

    all_docs: List[Document] = []
    for interview in interviews:
        all_docs.extend(interview_to_docs(interview, original_question))

    # print("All Documents to be inserted")
    # print(all_docs)
    
    upsert_docs(all_docs, elastic_vector_search) 

    outline_sections = split_markdown_sections(refined_outline["outline"])

    report_sections = section_writer_chain.batch([{"outline_segment": outline_section, "conversation_id": original_question} 
                                                  for outline_section in outline_sections])

    if include_data_gaps:
        missing_data_section = missing_data_section_chain.invoke({
            "original_question": original_question,
            "interview_history": data_lacking_convos
        })
    
        report_sections.append(missing_data_section)
    
    return report_sections, extended_data, data_backed_convos, informed_outline, refined_outline
from typing import TypedDict, Annotated
from langchain.prompts import Chat<PERSON>romptTemplate
from app.llm.client import model

class AxisSuggestion(TypedDict):
    x_axis: str
    "The best key from the data_item to be used on the x-axis of a graph"


axis_decision_system = """
You are a helpful data visualization assistant.

You will be given:

* A user's question
* A set of structured data (e.g., a data item from a database result)

Your task is to decide the best key from the provided data item to be used as the x-axis in case the data were to be visualized in a graph:

* Choose a key from the data item that best represents an independent variable (something that can be used for the x-axis).
* Make sure the selected key is relevant to the question and would make sense for the x-axis in a graph.

Only return the best single key that should be used for the x-axis as "x_axis".
"""

axis_decision_user = """
Question:
{question}

Data Item:
{data_item}

Based on the above, what is the best key to be used on the x-axis for a graph?
"""

axis_decision_prompt = ChatPromptTemplate.from_messages([
    ("system", axis_decision_system),
    ("user", axis_decision_user)
])

axis_decision_chain = axis_decision_prompt | model.with_structured_output(AxisSuggestion)
from typing import List
from typing_extensions import Annotated, TypedDict

from typing import List, Annotated, TypedDict

from app.llm.client import model
from langchain.prompts import ChatPromptTemplate

# 1) Define a new TypedDict for the output (list of deeper questions).
class ExpandedQuestions(TypedDict):
    """
    TypedDict representing the deeper, related questions.
    
    Attributes:
        questions: A list of strings containing follow-up or exploratory questions.
    """
    questions: Annotated[List[str], ..., "A list of deeper questions that expand on the main query"]

# 3) Create the system and user templates.
#    They keep the structure of "system + user" but direct the AI to return *questions* only.
system_template = """
You are an expert at brainstorming deeper, related questions for a given user inquiry.
Below is the database schema (which may or may not be used), but your primary task is
to expand upon the user's question in order to generate in-depth follow-up questions.

{schema_text}
"""

user_template = """
User question: "{question}"

Generate a list of follow-up questions that go deeper into the specific topic raised in the original question.

Guidelines:
- All follow-up questions must stay within the **logical scope** of the original question. Do **not make assumptions** that go beyond the data implied.
- For example, if the question is "Which institution do students owe the most fees?", do **not assume** this means the institution has the highest tuition or charges the most per student.
- Refer back to the subject of the original question **without using vague phrases** like "that institution" or "this institution". Instead, say "the institution where students owe the most fees" (or use the same phrasing from the original question).
- Avoid generalizations, assumptions, or reinterpretations of the original question’s meaning.
- Output only the list of follow-up questions. No explanations, no headers.

Your goal is to generate precise, relevant, and logically consistent follow-up questions.
"""

# 4) Create a ChatPromptTemplate that orchestrates these messages
prompt_template = ChatPromptTemplate.from_messages([
    ("system", system_template),
    ("user", user_template),
])

# 6) Build a single chain that returns the "ExpandedQuestions" typed dict
expanded_questions_chain = (prompt_template | model.with_structured_output(ExpandedQuestions))
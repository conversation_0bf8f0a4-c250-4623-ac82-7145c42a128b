from typing import TypedDict, Annotated
from langchain.prompts import Chat<PERSON>romptTemplate
from app.llm.client import model
from langchain_core.runnables import RunnableLambda


class SQLAndReasoning(TypedDict):
    """
    TypedDict representing the output of a query generation process.

    Attributes:
        question: The original natural language question.
        sql: The generated SQL query.
        reasoning: Any accompanying text explaining the logical reasoning behind how the SQL was formed.
    """
    question: Annotated[str, ..., "The natural language question"]
    sql: Annotated[str, ..., "The generated SQL query"]
    reasoning: Annotated[str, ..., "Explains the logic or steps used to form the SQL query"]

system_template_2 = """
You are a helpful SQL query generator. You will receive:
1. A list of relevant tables and their schema from the database.
2. A summary of categorical columns and their known unique values.
3. The user's original question.

You may receive feedback from a previous failed attempt. Use it to improve your query generation.

Your job is to generate a SQL query that answers the question using these tables.
Return your answer as JSON with three fields: 
`sql`, `reasoning`, and `text`.

Use the categorical summary to better understand what values to filter on (e.g., sex = 'M' vs 'male').
"""

user_template_2 = """
Relevant tables schema: {tables_schema}

Known categorical values summary:
{tables_categorical_summary}

User question: "{question}"

Feedback from prior attempt (if any): {feedback}

Generate a SQL query that will answer the user's question. 

- Default to showing results for all relevant entities unless the question specifies a filter.
- Return only the specific columns that directly answer the user's question. Avoid SELECT * unless all fields are explicitly requested.
- If the question is about types, categories, summaries, or totals, prefer DISTINCT, COUNT, SUM, or other aggregations — not raw data.
- Focus on the user's intent and optimize the result for clarity and relevance, not for completeness.
- Limit results to 20 rows max

Include some reasoning about how you arrived at this query in the `reasoning` field, and any extra explanation in the `text` field.
"""

prompt_template_2 = ChatPromptTemplate.from_messages([
    ("system", system_template_2),
    ("user", user_template_2),
])

sql_and_reasoning_chain = (
    prompt_template_2 |
    model.with_structured_output(SQLAndReasoning)
)

def batch_sql_and_reasoning(questions_schemas_dict):
    sql_and_reasoning_response = sql_and_reasoning_chain.batch(questions_schemas_dict["questions_schemas"])
    return {"sql_reasoning": sql_and_reasoning_response}

batch_sql_and_reasoning_chain = RunnableLambda(batch_sql_and_reasoning)
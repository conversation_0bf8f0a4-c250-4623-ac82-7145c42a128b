from typing import TypedDict, Annotated
from langchain.prompts import ChatPromptTemplate
from app.llm.client import model

# 1. Output TypedDict
class ReportOutline(TypedDict):
    outline: Annotated[str, ..., "Markdown-style outline summarizing the queries and insights."]

# 2. Prompt Templates
report_outline_system = """
You are a technical writer generating a report outline.

Each entry below represents:
- A user question

Use this information to generate a clean, structured markdown-formatted outline for a report.

Only return the outline — do not write the full report.
"""

report_outline_user = """
Original question: "{original_question}"

Now write a structured outline for a report that investigates the original question.
"""

# 3. Prompt Template
report_outline_prompt = ChatPromptTemplate.from_messages([
    ("system", report_outline_system),
    ("user", report_outline_user)
])

# 4. Chain
report_outline_chain = report_outline_prompt | model.with_structured_output(ReportOutline)
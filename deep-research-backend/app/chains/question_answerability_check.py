from typing import List
from typing_extensions import Annotated, TypedDict

from typing import List, Annotated, TypedDict

from app.llm.client import model
from langchain.prompts import ChatPromptTemplate


class QuestionCheck(TypedDict):
    """
    TypedDict for determining if a single question is answerable from the DB schema.

    Attributes:
        answerable: A boolean indicating whether the question is answerable.
        reasoning: A short explanation of why/why not it's answerable.
    """
    answerable: Annotated[bool, ..., "True if the question can be answered from the schema, false otherwise"]
    reasoning: Annotated[str, ..., "Detailed explanation of how or why the question is answerable"]


system_template_1 = """
You are an SQL expert with access to a database schema and a natural language question.

Your job is to determine if the question is answerable from the schema, be it directly or indirectly by having to create joins.

You will determine if the question can be answered from the schema, possibly after receiving feedback from a failed SQL attempt.

You may also receive optional feedback from a previous attempt to answer the question — consider it while assessing answerability.


Return your answer as JSON with two fields:
- "answerable": a boolean
- "reasoning": a detailed explanation
"""

user_template_1 = """
Database schema: {schema_text}

Question: "{question}"

{feedback}

Can this question be correctly and completely answered from the schema?
"""

prompt_template_1 = ChatPromptTemplate.from_messages([
    ("system", system_template_1),
    ("user", user_template_1),
])

question_check_chain = (prompt_template_1 | model.with_structured_output(QuestionCheck))

# # --- Step 1: Generate deeper or follow-up questions ---
# expanded_questions_response = expanded_questions_chain.invoke({
#     "schema_text": compact_db_structure,
#     "question": question
# })
from langchain_core.runnables import <PERSON>nableLambda
from app.core.db_api import run_query_silent

def sql_runner(sql_gen_dict):
    """
    Runs a verified SQL query and wraps the result with the original context for traceability.
    """
    
    # question = sql_gen_dict["question"]
    try:
        result = run_query_silent(sql_gen_dict["sql"])
        # logs[question] = logs.get(question, "") + "\n\n📥 **SQL Execution Result:**\n```\n" + str(result) + "\n```"
        return {
            **sql_gen_dict,
            "result": result["data"]
        }
    except Exception as e:
        # logs[question] = logs.get(question, "") + "\n\n❌ **SQL Execution Error:**\n```\n" + str(e) + "\n```"
        return {
            **sql_gen_dict,
            "result": "No results",
            "error": str(e)
        }

sql_runner_chain = RunnableLambda(sql_runner)
from langchain_core.runnables import <PERSON>nableLambda
from .question_answerability_check import question_check_chain


def extract_answerable_questions(question_data: dict, check_results: list) -> dict:
    answerable = [
        question
        for question, result in zip(question_data['questions'], check_results)
        if result['answerable']
    ]
    return answerable

extract_answerable_questions_chain = RunnableLambda(extract_answerable_questions)

def batch_question_verification(questions_dict):
    question_check_response = question_check_chain.batch([ {"question": q, "schema_text": questions_dict["schema_text"], "feedback": ""}
                                                          for q in questions_dict["questions"]])
    return extract_answerable_questions(questions_dict,question_check_response)
# sql_query_gen_chain

batch_question_verification_chain = RunnableLambda(batch_question_verification)
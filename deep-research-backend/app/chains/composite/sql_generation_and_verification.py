from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.chains.question_answerability_check import question_check_chain
from app.chains.relevant_table_finder import tables_finder_chain
from app.chains.sql_and_reasoning_chain import sql_and_reasoning_chain
from app.chains.relevant_schema_info_filter import relevant_tables_schemas_extractor_chain
from app.chains.sql_verification import sql_verification_chain
from app.utils.schema_loader import get_schema_file

compact_db_structure =  get_schema_file("compact_db_structure.txt")

logs = {}

def with_optional_feedback(base_input: dict, feedback: str | None = None):
    return {
        **base_input,
        "feedback": f'Feedback from previous step: "{feedback}"' if feedback else ""
    }

def sql_gen_veri(input_question):
    global logs
    feedback = ""
    retry_count = 0
    MAX_RETRIES = 5
    log_entries = []  # Temporary list to collect logs for this run

    def add_log(entry):
        # print(entry)
        log_entries.append(entry)

    # Handle both string and dict inputs
    if isinstance(input_question, dict):
        if "question" in input_question:
            question_str = input_question["question"]
        else:
            question_str = str(input_question)
    else:
        question_str = str(input_question)

    add_log("\n🔍 Starting SQL generation and verification process")
    add_log(f"📝 Question: {question_str}")
    add_log(f"🔎 User Question: \"{question_str}\"")

    while retry_count < MAX_RETRIES:
        retry_count += 1
        add_log(f"\n🔁 Retry Attempt #{retry_count} — Feedback: \"{feedback}\"")

        # Step 1: Verify answerability
        answerable_count = 0
        answerable_loop_count = 0
        while True:
            answerable_loop_count += 1
            add_log(f"  🔄 Checking answerability — Loop {answerable_loop_count}")

            question_check_input = with_optional_feedback({
                "schema_text": compact_db_structure,
                "question": question_str
            }, feedback)

            answerable_reasoning_dict = question_check_chain.invoke(question_check_input)
            add_log(f"    ✅ Answerability Check Result: {answerable_reasoning_dict}")

            if answerable_reasoning_dict["answerable"]:
                answerable_count += 1
            else:
                answerable_count -= 1

            if answerable_loop_count == 2:
                if answerable_count == -2:
                    add_log("❌ Determined to be unanswerable after 2 consistent failures.")
                    logs[question_str] = "\n".join(log_entries)
                    return {
                        "question": question_str,
                        "answerable": False,
                        "reasoning": answerable_reasoning_dict["reasoning"],
                        "feedback": feedback
                    }
                if answerable_count == 2:
                    add_log("✅ Consistently answerable — proceeding to table extraction and SQL generation.")
                    break
            elif answerable_loop_count == 3:
                add_log("❌ Could not consistently determine answerability in 3 attempts.")
                logs[question_str] = "\n".join(log_entries)
                return {
                    "question": question_str,
                    "answerable": False,
                    "reasoning": "Too much uncertainty as to whether or not it can be answered",
                    "feedback": feedback
                }

        # Step 2: Find relevant tables
        add_log("📊 Finding relevant tables...")
        tables_finder_input = with_optional_feedback({
            "schema_text": compact_db_structure,
            "question": question_str
        }, feedback)
        question_tables_dict = tables_finder_chain.invoke(tables_finder_input)
        add_log(f"    📌 Relevant Tables: {question_tables_dict['tables']}")

        # Step 3: Extract relevant schema
        add_log("📂 Extracting relevant schema...")
        question_schema_dict = relevant_tables_schemas_extractor_chain.invoke(question_tables_dict)

        # Step 4: Generate SQL
        add_log("🛠️ Generating SQL query...")
        sql_input = with_optional_feedback({
            "tables_schema": question_schema_dict["tables_schema"],
            "tables_categorical_summary": question_schema_dict["tables_categorical_summary"],
            "question": question_str
        }, feedback)
        sql_and_reasoning_results = sql_and_reasoning_chain.invoke(sql_input)
        add_log(f"    🧠 SQL Reasoning: {sql_and_reasoning_results['reasoning']}")
        add_log(f"    📝 Generated SQL: {sql_and_reasoning_results['sql']}")

        # Step 5: Verify SQL
        add_log("🧪 Verifying SQL correctness...")
        sql_verification_results = sql_verification_chain.invoke({
            **question_schema_dict,
            "question": question_str,
            "sql": sql_and_reasoning_results["sql"]
        })
        add_log(f"    🔍 SQL Verification Result: {sql_verification_results}")

        if sql_verification_results["correct"]:
            add_log("✅ SQL verified successfully.")
            logs[question_str] = "\n".join(log_entries)
            # Ensure we return the complete structure expected by the interview chain
            return {
                "question": question_str,
                "sql": sql_verification_results["sql"],
                "correct": True,
                "reasoning": sql_verification_results["reasoning"],
                "feedback": sql_verification_results.get("feedback", "")
            }

        # Step 6: Retry with feedback
        add_log("❌ SQL verification failed. Looping with new feedback...")
        feedback = sql_verification_results.get("feedback", sql_verification_results.get("reasoning", ""))

    # Step 7: Max retries hit
    add_log(f"🚫 Reached maximum retries ({MAX_RETRIES}). Returning last attempt with feedback.")
    logs[question_str] = "\n".join(log_entries)
    return {
        "question": question_str,
        "sql": sql_and_reasoning_results["sql"],
        "correct": False,
        "reasoning": sql_verification_results["reasoning"],
        "feedback": feedback,
        "note": f"Reached max retries ({MAX_RETRIES}) without finding a valid SQL."
    }


sql_gen_veri_chain = RunnableLambda(sql_gen_veri)

# def batch_sql_gen_veri(questions_dict):
#     sql_gen_veri_response = sql_gen_veri_chain.batch(questions_dict["questions"])
#     return {"sql_queries": sql_gen_veri_response}

# batch_sql_gen_veri_chain = RunnableLambda(batch_sql_gen_veri)
import re
import json
from typing import List, Dict, Any
from collections import OrderedDict
from app.models.report import ReportGenerationResult, DataObject
from app.chains.uninformed_outline_generator import report_outline_chain
from app.chains.expand_question import expanded_questions_chain
from app.chains.filter_answerable_questions import batch_question_verification_chain
from app.chains.composite.interview import batch_interview
from app.helpers import extract_q_a_with_data, extract_q_a_without_data, interview_to_docs, upsert_docs
from app.chains.informed_outline_generator import informed_report_outline_chain
from app.chains.refined_outline_generator import refine_report_outline_chain
from app.chains.data_gap_section_writer import missing_data_section_chain
from app.chains.section_writer import section_writer_chain
from app.vectorstore.client import elastic_vector_search
from langchain_core.documents import Document
from app.utils.schema_loader import get_schema_file
from app.utils.progress_tracker import push_progress
import logging
import os


logger = logging.getLogger(__name__)


compact_db_structure =  get_schema_file("compact_db_structure.txt")
# logger.info(compact_db_structure)

# Constants and helper functions for figure processing
# Use double-bracket placeholders [[tag]] to match section writer output and frontend parser
_PLACEHOLDER = re.compile(r'\[\[([^\]]+)\]\]')

def _title(tag: str) -> str:
    """Convert a tag like 'student_count_by_gender' to 'Student Count by Gender'."""
    return tag.replace('_', ' ').title()

def split_markdown_sections(markdown_text):
    # Find all level 2 sections (## headings) and split the content based on them
    # This regex captures the heading as well as the content that follows it
    pattern = r"(##\s.*?)(?=\n##\s|\Z)"  # Match '## ' to next '## ' or end of string
    sections = re.findall(pattern, markdown_text, re.DOTALL)
    return [section.strip() for section in sections]

def generate_full_report(request, task_id: str):
    from app.utils.debug_logger import debug_logger

    # Initialize debug logging for this request
    debug_logger.log_request_start(request.original_question, task_id)
    debug_logger.log_elasticsearch_state()

    try:
        original_question = request.original_question

        # Validate input
        if not original_question or not original_question.strip():
            push_progress(task_id, "error", extra={
                "error": "Original question cannot be empty"
            })
            return {"error": "Original question cannot be empty"}
        
        logger.info("generating_uninformed_outline")
        push_progress(task_id, "generating_uninformed_outline")
        uninformed_outline = report_outline_chain.invoke({
            "original_question": original_question
        })

        logger.info("expanding_questions")
        push_progress(task_id, "expanding_questions")
        output = expanded_questions_chain.invoke({
            "schema_text":  compact_db_structure,
            "question": original_question,
            "feedback": ""
        })

        logger.info("filtering_questions")
        push_progress(task_id, "filtering_questions")
        questions_list = batch_question_verification_chain.invoke({
            "schema_text":  compact_db_structure,
            "questions": output["questions"]  # Extract the questions list from the expanded output
        })

        logger.info("interviewing")
        push_progress(task_id, "interviewing")

        debug_log = debug_logger.get_logger("REPORT_PIPELINE")
        debug_logger.log_separator("STARTING INTERVIEW PROCESS")
        debug_log.info(f"📋 Questions to interview: {len(questions_list)}")

        interviews = batch_interview(questions_list)
        debug_log.info(f"🗣️ Completed interviews: {len(interviews)}")

        data_backed_convos = extract_q_a_with_data(interviews)
        data_lacking_convos = extract_q_a_without_data(interviews)

        debug_log.info(f"✅ Conversations with data: {len(data_backed_convos)}")
        debug_log.info(f"❌ Conversations without data: {len(data_lacking_convos)}")

        logger.info("generating_informed_outline")
        push_progress(task_id, "generating_informed_outline")
        informed_outline = informed_report_outline_chain.invoke({
            'original_question': original_question,
            'interview_histories': data_backed_convos
        })

        logger.info("refining_outline")
        push_progress(task_id, "refining_outline")
        refined_outline = refine_report_outline_chain.invoke({
            "informed_outline": informed_outline, "uninformed_outline": uninformed_outline, "original_question": original_question
        })

        interviews = [ interview for interview in interviews["interviews"] if len(interview["convo"]) > 0 ]
        data_dict_list = [interview["data"] for interview in interviews]

        extended_data = {}
        for data_dict in data_dict_list:
            extended_data.update(data_dict)

        debug_logger.log_separator("CONVERTING INTERVIEWS TO DOCUMENTS")
        all_docs: List[Document] = []
        for i, interview in enumerate(interviews):
            debug_log.info(f"🔄 Processing interview {i+1}/{len(interviews)}")
            interview_docs = interview_to_docs(interview, original_question)
            all_docs.extend(interview_docs)
            debug_log.info(f"  📄 Generated {len(interview_docs)} documents from this interview")

        debug_log.info(f"📊 Total documents to upsert: {len(all_docs)}")
        upsert_docs(all_docs, elastic_vector_search)

        # Log Elasticsearch state after upsert
        debug_logger.log_elasticsearch_state()

        outline_sections = split_markdown_sections(refined_outline["outline"])
        debug_log.info(f"📋 Outline sections to write: {len(outline_sections)}")

        logger.info("writing_sections")
        push_progress(task_id, "writing_sections")
        debug_logger.log_separator("WRITING REPORT SECTIONS")

        # Use batch processing to match notebook prototype exactly
        debug_log.info(f"✍️ Writing {len(outline_sections)} sections using batch processing...")
        report_sections = section_writer_chain.batch([{"outline_segment": outline_section, "conversation_id": original_question}
                                                      for outline_section in outline_sections])

        # Log completion and stream sections for progress tracking
        for i, section_content in enumerate(report_sections):
            debug_log.info(f"  ✅ Section {i+1} completed: {len(section_content)} characters")
            push_progress(task_id, "section_complete", extra={
                "section_index": i,
                "section_title": outline_sections[i].split('\n')[0],  # First line as title
                "section_content": section_content
            })

        if request.include_data_gaps:
            missing_data_section = missing_data_section_chain.invoke({
                "original_question": original_question,
                "interview_history": data_lacking_convos
            })

            report_sections.append(missing_data_section)

        # Create the report array by splitting sections on tag patterns
        report_array = []
        for report_section in report_sections:
            report_array.extend(re.split(r'(\[\[.*?\]\])', report_section))

        # Process figures to add figure references and list of figures
        report = process_report_figures(report_array, extended_data)

        # Replace tag placeholders with actual data objects, maintaining the array structure
        for i in range(len(report)):
            item = report[i]

            # Check if the item matches the tag pattern [[...]]
            match = re.match(r'\[\[(.*?)\]\]', item)
            if match:
                # If it's a tag, replace with the actual data object from extended_data
                tag = match.group(1)
                data = extended_data.get(tag, "")
                if data:
                    # Keep the data object as-is (dict format) to match notebook prototype
                    report[i] = data
                    logger.info(f"Replaced tag {tag} with data object")
                else:
                    # If no data found, remove the placeholder
                    report[i] = ""
                    logger.info(f"No data found for tag {tag}")
            else:
                logger.info("No tag pattern found in item")

        # Keep all items including empty strings to match notebook prototype behavior
        # The notebook preserves empty strings in the final report array
    
        result = ReportGenerationResult(
            outline=refined_outline["outline"],
            sections=report,
            data=extended_data
        )
        
        # output_dir = "generated_reports"
        # os.makedirs(output_dir, exist_ok=True)

        # # Create file path using task_id
        # report_path = os.path.join(output_dir, f"{task_id}.txt")

        # # Write to file
        # with open(report_path, "w", encoding="utf-8") as f:
        #     f.write(repr(report))

        debug_logger.log_separator("REPORT GENERATION COMPLETED")
        debug_log.info(f"✅ Report generation successful!")
        debug_log.info(f"📊 Final report sections: {len(result.sections)}")
        debug_log.info(f"📈 Extended data items: {len(result.data)}")
        debug_log.info(f"📁 Debug log file: {debug_logger.log_file_path}")

        push_progress(task_id, "complete", extra={
            "result": result.model_dump()
        })

        return result.model_dump()

    except Exception as e:
        error_msg = f"Error generating report: {str(e)}"

        # Log the error to debug file
        debug_log = debug_logger.get_logger("REPORT_PIPELINE_ERROR")
        debug_logger.log_separator("REPORT GENERATION ERROR")
        debug_log.error(f"❌ Report generation failed: {error_msg}")
        debug_log.error(f"📁 Debug log file: {debug_logger.log_file_path}")

        push_progress(task_id, "error", extra={
            "error": error_msg
        })
        return {"error": error_msg}


def generate_streaming_report(request, task_id: str):
    """
    New streaming implementation that processes figures and replaces placeholders
    in real-time as each section completes.
    """
    from app.utils.debug_logger import debug_logger

    # Initialize debug logging for this request
    debug_logger.log_request_start(request.original_question, task_id)
    debug_logger.log_elasticsearch_state()

    try:
        original_question = request.original_question

        # Validate input
        if not original_question or not original_question.strip():
            push_progress(task_id, "error", extra={
                "error": "Original question cannot be empty"
            })
            return {"error": "Original question cannot be empty"}

        debug_log = debug_logger.get_logger("STREAMING_REPORT_PIPELINE")
        debug_logger.log_separator("STREAMING REPORT GENERATION STARTED")
        debug_log.info(f"🎯 Original question: {original_question}")
        debug_log.info(f"📋 Include data gaps: {request.include_data_gaps}")

        # Step 1: Generate uninformed outline
        logger.info("generating_uninformed_outline")
        push_progress(task_id, "generating_uninformed_outline")
        uninformed_outline = report_outline_chain.invoke({"original_question": original_question})

        # Step 2: Expand questions
        logger.info("expanding_questions")
        push_progress(task_id, "expanding_questions")
        expanded_questions = expanded_questions_chain.invoke({
            "schema_text": compact_db_structure,
            "question": original_question,
            "feedback": ""
        })

        # Step 3: Filter answerable questions
        logger.info("filtering_questions")
        push_progress(task_id, "filtering_questions")
        answerable_questions = batch_question_verification_chain.invoke({
            "questions": expanded_questions["questions"],
            "schema_text": compact_db_structure
        })

        # Step 4: Conduct interviews
        logger.info("interviewing")
        push_progress(task_id, "interviewing")
        interviews = batch_interview(answerable_questions)

        # Step 5: Process interview data
        data_backed_convos = extract_q_a_with_data(interviews)
        data_lacking_convos = extract_q_a_without_data(interviews)

        # Step 6: Generate informed outline
        logger.info("generating_informed_outline")
        push_progress(task_id, "generating_informed_outline")
        informed_outline = informed_report_outline_chain.invoke({
            'original_question': original_question,
            'interview_histories': data_backed_convos
        })

        # Step 7: Refine outline
        logger.info("refining_outline")
        push_progress(task_id, "refining_outline")
        refined_outline = refine_report_outline_chain.invoke({
            "informed_outline": informed_outline,
            "uninformed_outline": uninformed_outline,
            "original_question": original_question
        })

        # Step 8: Prepare data and documents
        interviews = [interview for interview in interviews["interviews"] if len(interview["convo"]) > 0]
        data_dict_list = [interview["data"] for interview in interviews]

        extended_data = {}
        for data_dict in data_dict_list:
            extended_data.update(data_dict)

        # Convert interviews to documents and upsert
        all_docs = []
        for interview in interviews:
            docs = interview_to_docs(interview, original_question)
            all_docs.extend(docs)

        debug_log.info(f"📊 Total documents to upsert: {len(all_docs)}")
        upsert_docs(all_docs, elastic_vector_search)

        # Step 9: Split outline into sections
        outline_sections = split_markdown_sections(refined_outline["outline"])
        debug_log.info(f"📋 Outline sections to write: {len(outline_sections)}")

        # Step 10: Write sections with real-time figure processing
        logger.info("writing_sections")
        push_progress(task_id, "writing_sections")
        debug_logger.log_separator("WRITING REPORT SECTIONS WITH STREAMING")

        # Initialize figure tracking for streaming
        fig_map: OrderedDict[str, int] = OrderedDict()
        fig_counter = 1
        processed_sections = []

        # Process each section individually with real-time figure processing
        for i, outline_section in enumerate(outline_sections):
            debug_log.info(f"✍️ Writing section {i+1}/{len(outline_sections)}...")

            # Generate the section content
            section_content = section_writer_chain.invoke({
                "outline_segment": outline_section,
                "conversation_id": original_question
            })

            # Process figures in this section in real-time
            processed_section, fig_map, fig_counter = process_section_figures_streaming(
                section_content, extended_data, fig_map, fig_counter
            )

            processed_sections.append(processed_section)

            # Stream the processed section immediately
            debug_log.info(f"  ✅ Section {i+1} completed and processed: {len(processed_section)} characters")
            push_progress(task_id, "section_complete", extra={
                "section_index": i,
                "section_title": outline_section.split('\n')[0],  # First line as title
                "section_content": processed_section,
                "figures_in_section": extract_figures_from_section(processed_section),
                "total_figures_so_far": len(fig_map)
            })

        # Step 11: Add data gaps section if requested
        if request.include_data_gaps:
            missing_data_section = missing_data_section_chain.invoke({
                "original_question": original_question,
                "interview_history": data_lacking_convos
            })
            processed_sections.append(missing_data_section)

            # Stream the data gaps section
            push_progress(task_id, "section_complete", extra={
                "section_index": len(processed_sections) - 1,
                "section_title": "Data Gaps and Limitations",
                "section_content": missing_data_section,
                "figures_in_section": [],
                "total_figures_so_far": len(fig_map)
            })

        # Step 12: Generate and stream List of Figures
        if fig_map:
            list_of_figures = generate_list_of_figures(fig_map)
            processed_sections.append(list_of_figures)

            # Stream the List of Figures section
            push_progress(task_id, "section_complete", extra={
                "section_index": len(processed_sections) - 1,
                "section_title": "List of Figures",
                "section_content": list_of_figures,
                "figures_in_section": [],
                "total_figures_so_far": len(fig_map)
            })

        # Step 13: Create final result
        result = ReportGenerationResult(
            outline=refined_outline["outline"],
            sections=processed_sections,
            data=extended_data
        )

        debug_logger.log_separator("STREAMING REPORT GENERATION COMPLETED")
        debug_log.info(f"✅ Streaming report generation successful!")
        debug_log.info(f"📊 Final report sections: {len(result.sections)}")
        debug_log.info(f"📈 Extended data items: {len(result.data)}")
        debug_log.info(f"🎨 Total figures processed: {len(fig_map)}")
        debug_log.info(f"📁 Debug log file: {debug_logger.log_file_path}")

        push_progress(task_id, "complete", extra={
            "result": result.model_dump(),
            "total_figures": len(fig_map),
            "figures_list": list(fig_map.keys())
        })

        return result.model_dump()

    except Exception as e:
        error_msg = f"Error generating streaming report: {str(e)}"

        # Log the error to debug file
        debug_log = debug_logger.get_logger("STREAMING_REPORT_PIPELINE_ERROR")
        debug_logger.log_separator("STREAMING REPORT GENERATION ERROR")
        debug_log.error(f"❌ Streaming report generation failed: {error_msg}")
        debug_log.error(f"📁 Debug log file: {debug_logger.log_file_path}")

        push_progress(task_id, "error", extra={
            "error": error_msg
        })
        return {"error": error_msg}


# -----------------------------------------------------------
# Streaming utility functions
# -----------------------------------------------------------

def process_section_figures_streaming(section_content: str, extended_data: Dict[str, Any],
                                     fig_map: OrderedDict[str, int], fig_counter: int) -> tuple[str, OrderedDict[str, int], int]:
    """
    Process figures in a single section for streaming, replacing placeholders with figure references
    and keeping the original tag for first occurrences so frontend can render the actual chart/table.

    Returns:
        tuple: (processed_section_content, updated_fig_map, updated_fig_counter)
    """
    if not _PLACEHOLDER.search(section_content):
        return section_content, fig_map, fig_counter

    processed_content = section_content

    def replace_placeholder(match):
        nonlocal fig_counter
        tag = match.group(1)

        if extended_data.get(tag, False):
            # First time we've seen this tag
            if tag not in fig_map:
                fig_map[tag] = fig_counter
                n = fig_counter
                fig_counter += 1

                # Insert figure reference and keep the original tag placeholder for frontend to render
                ref_line = f"\nFigure {n}\n"
                return f"{ref_line}[[{tag}]]"
            else:
                # Subsequent occurrence - just reference
                n = fig_map[tag]
                return f"(See Figure {n})"
        else:
            # No data found, remove placeholder
            return ""

    processed_content = _PLACEHOLDER.sub(replace_placeholder, processed_content)
    return processed_content, fig_map, fig_counter


def extract_figures_from_section(section_content: str) -> List[Dict[str, Any]]:
    """
    Extract figure information from a processed section.

    Returns:
        List of figure metadata dictionaries
    """
    figures = []

    # Look for figure references in the format "Figure N – Title" followed by JSON data
    # Use a greedy pattern to capture complete JSON until end of string or double newline
    figure_pattern = re.compile(r'Figure (\d+) – (.+?)\n(\{.*?)(?=\n\n|\Z)', re.DOTALL)
    matches = figure_pattern.findall(section_content)

    for match in matches:
        figure_num, title, json_data = match

        # Try to parse the JSON to get the presentation type
        figure_type = "chart"  # Default fallback
        try:
            data_obj = json.loads(json_data)
            if isinstance(data_obj, dict) and "presentation_type" in data_obj:
                figure_type = data_obj["presentation_type"]
        except (json.JSONDecodeError, TypeError):
            # If JSON parsing fails, keep default type
            pass

        figures.append({
            "figure_number": int(figure_num),
            "title": title.strip(),
            "type": figure_type
        })

    return figures


def generate_list_of_figures(fig_map: OrderedDict[str, int]) -> str:
    """
    Generate the List of Figures section.

    Args:
        fig_map: Ordered dictionary mapping tags to figure numbers

    Returns:
        Formatted List of Figures section as markdown string
    """
    if not fig_map:
        return ""

    lines = ["\n\n## List of Figures"]
    for tag, num in fig_map.items():
        lines.append(f"* **Figure {num}** – {_title(tag)}")

    return "\n".join(lines)


# -----------------------------------------------------------
# Original utility functions (for batch processing)
# -----------------------------------------------------------

# -----------------------------------------------------------
# main helper
# -----------------------------------------------------------
def process_report_figures(blocks: List[str], extended_data) -> str:
    """
    Transform a list of markdown section strings that contain [[[tag]]] placeholders into
    a finished report that

      • inserts a reference line `[Figure n – Title]` immediately *before* the first block
        that contains each unique tag,
      • keeps the first placeholder unchanged (so later production code can swap it for a
        graphic or table),
      • replaces subsequent occurrences of the same tag with `(See Figure n)`,
      • appends a 'List of Figures' section at the end.

    Parameters
    ----------
    blocks : List[str]
        The draft report, one string per section/paragraph.

    Returns
    -------
    str
        A single markdown string ready for post-processing or viewing.
    """
    fig_map: OrderedDict[str, int] = OrderedDict()   # tag → figure number
    fig_counter = 1
    processed: List[str] = []

    for part in blocks:
        # If no placeholder in this part, just copy it over.
        if not _PLACEHOLDER.search(part):
            processed.append(part)
            continue

        # Walk all placeholders in this part so we can treat first vs. later correctly.
        def replace(match):
            nonlocal fig_counter
            tag = match.group(1)

            if extended_data.get(tag, False):
                # ── First time we've seen this tag ───────────────────────────
                if tag not in fig_map:
                    fig_map[tag] = fig_counter
                    n = fig_counter
                    fig_counter += 1
                    # Insert reference line *before* this block exactly once
                    ref_line = f"\nFigure {n} – {_title(tag)}\n"
                    if ref_line not in processed[-1:]:   # avoid duplicate if block starts with same tag
                        processed.append(ref_line)

                    # KEEP the first placeholder unchanged so later routine can swap it.
                    return match.group(0)   # original [[[tag]]]

                # ── Subsequent occurrence ─────────────────────────────────────
                n = fig_map[tag]
                return f"(See Figure {n})"

        # Apply replacement to the current block
        part = _PLACEHOLDER.sub(replace, part)
        processed.append(part)

    # Append List of Figures
    if fig_map:
        lof_lines = ["\n\n## List of Figures"]
        for tag, num in fig_map.items():
            lof_lines.append(f"* **Figure {num}** – {_title(tag)}")
        processed.append("\n".join(lof_lines))

    return processed

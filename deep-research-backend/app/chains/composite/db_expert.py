from langchain_core.runnables import <PERSON>nableLambda
from app.chains.composite.sql_generation_and_verification import sql_gen_veri_chain
from app.chains.sql_runner import sql_runner_chain
from app.chains.answerer import sql_answerer_chain

def db_expert(question):
    sql_results = (sql_gen_veri_chain | sql_runner_chain).invoke(question)
    answer = sql_answerer_chain.invoke({
        "question": question, "result": sql_results["result"]
    })
    
    return {
        "question": question,
        "answer": answer["answer"],
        "sql": sql_results.get("sql", None),
        "data": sql_results["result"],
        "data_tag": answer["data_tag"]
    }

db_expert_chain = RunnableLambda(db_expert)
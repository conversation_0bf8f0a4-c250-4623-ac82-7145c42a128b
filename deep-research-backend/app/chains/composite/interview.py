from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, UTC
from app.chains.presentation_type_suggestor import presentation_decision_chain
from app.chains.questioner import interview_questioner
from app.chains.x_axis_suggestion import axis_decision_chain
from app.chains.composite.sql_generation_and_verification import sql_gen_veri_chain
from app.chains.sql_runner import sql_runner_chain
from app.chains.answerer import sql_answerer_chain


def interview(initial_question: str):
    """
    Conducts up to 5 Q-A turns.
    Returns:
        {
          "convo":               [ {"question": str, "answer": str}, ... ],
          "convo_with_data":     [ {"question": str, "answer": dict}, ... ],
          "data":                { tag: any, ... }
        }
    """
    turns: list[dict] = []                 # plain answer text
    turns_with_data: list[dict] = []       # full answer object
    data_dict: dict = {}

    follow_up = initial_question

    for i in range(5):
        # print(f"\n\nQUESTION {i+1}\n")

        # ----- Expert answers -----
        try:
            sql_query_evaluation = sql_gen_veri_chain.invoke(follow_up)

            # Debug: Print what we got from SQL generation
            print(f"🔍 SQL query evaluation result: {sql_query_evaluation}")
            print(f"🔍 Type: {type(sql_query_evaluation)}")
            print(f"🔍 Keys: {list(sql_query_evaluation.keys()) if isinstance(sql_query_evaluation, dict) else 'Not a dict'}")

            # Ensure we have the correct key before accessing it
            if not isinstance(sql_query_evaluation, dict) or "correct" not in sql_query_evaluation:
                print(f"❌ Invalid SQL query evaluation result: {sql_query_evaluation}")
                # Skip this question and continue with the next one instead of returning error
                continue
        except Exception as e:
            print(f"❌ Exception in SQL generation: {e}")
            import traceback
            traceback.print_exc()
            # Skip this question and continue with the next one instead of returning error
            continue

        if not sql_query_evaluation["correct"]:
            answer_ = "I'm sorry I don't have the answer to this question"
            turns.append({
                "question": follow_up,
                "answer":   answer_
            })
            turns_with_data.append({
                "presentation_type": "text",
                "question": follow_up,
                "answer":   answer_,
                "sql": sql_query_evaluation.get("sql", None),
                "data": "No results",
                "data_tag": "no_answer_result"
            })
            continue
        
        else:
            try:
                sql_results = sql_runner_chain.invoke(sql_query_evaluation)
                answer = sql_answerer_chain.invoke({
                    "question": follow_up, "result": sql_results["result"]
                })
            except Exception as e:
                print(f"❌ Exception in SQL execution: {e}")
                # Skip this question and continue with the next one
                continue
            
            answer_obj = {
                    "question": follow_up,
                    "answer": answer["answer"],
                    "sql": sql_results.get("sql", None),
                    "data": sql_results["result"],
                    "data_tag": answer["data_tag"]
            }

            presentation_suggestion = presentation_decision_chain.invoke(answer_obj)
            presentation_type = presentation_suggestion["presentation_type"]

            # Store the successful turn
            turns.append({
                "question": follow_up,
                "answer":   answer_obj["answer"]
            })
            turns_with_data.append({
                **answer_obj,
                "presentation_type": presentation_type,
                "question": follow_up,
            })

            # Collect structured data
            data_dict[answer_obj["data_tag"]] = {
                "presentation_type": presentation_type,
                "data": answer_obj["data"]
            } 

            # print(f"\n\nAnswer OBJ: {answer_obj}\n\n")

            if presentation_type.lower() in ["bar_chart", "area_chart", "line_chart", "pie_chart"] and isinstance(answer_obj["data"], list) and len(answer_obj["data"]) > 0:
                axis_decision = axis_decision_chain.invoke({
                    "question": follow_up,
                    "data_item": answer_obj["data"][0]
                })
                
                data_dict[answer_obj["data_tag"]]["x_axis_key"] = axis_decision["x_axis"]    
            
        # ----- Ask the questioner model for the next follow-up -----
        try:
            questioner_output = interview_questioner.invoke({
                "topic":   initial_question,
                "history": turns                 # pass the concise history
            })
        except Exception as e:
            print("Error from interview_questioner:")
            print(e)
            break                                # stop the interview loop

        follow_up = questioner_output.get("follow_up_question", "No further questions")

        if follow_up == "No further questions":
            break

    # ---------- return ----------
    return {
        "convo_id": initial_question,
        "convo": turns,
        "convo_with_data": turns_with_data,
        "data": data_dict,
        "timestamp": datetime.now(UTC).isoformat()
    }

# Wrap it in a RunnableLambda so you can pipe it in a LangChain flow
interview_chain = RunnableLambda(interview)

def batch_interview(questions_list):
    interview_responses = interview_chain.batch(questions_list)
    return {'interviews': interview_responses}

batch_interview_chain = RunnableLambda(batch_interview)
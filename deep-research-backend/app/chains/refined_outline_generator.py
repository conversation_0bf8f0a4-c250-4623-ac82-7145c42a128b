from typing import TypedDict, Annotated
from langchain.prompts import Chat<PERSON>romptTemplate
from app.llm.client import model

# 1. Output TypedDict
class RefinedReportOutline(TypedDict):
    outline: Annotated[
        str,
        ...,
        "A refined markdown-style outline that merges the uninformed and informed outlines, structured to best reflect the interview history while improving flow and completeness without introducing new information."
    ]

# 2. Prompt Templates
refine_outline_system = """
You are a technical-writing assistant. You are given:

- **Uninformed outline** – drafted before evidence summaries
- **Informed outline**   – drafted after evidence summaries

**Goal**

Combine the two into one refined, markdown-style outline that

- Uses factual content ONLY from the informed outline and the evidence summaries
- May borrow ordering or phrasing from the uninformed outline if it improves flow
- Removes redundancies and ensures a clear, logical progression
- Preserves a precise, technical tone

Do not include sections that disclose process or sources (e.g., Methodology, Interview Summaries, References, Citations, Data Sources, Acknowledgments).

---

### Special rules

#### 1.  Introduction
After the heading `## Introduction`, write **one concise paragraph** (no bullets) that
1) states the purpose / scope of the report, and
2) presents the key answer to the guiding question in plain language.

*No* references. Do not talk about references.

---

### Formatting rules for all other sections

- Top-level sections use `##`.
- Subsections use `###` if needed.
- Except for the Introduction paragraph, items inside sections use bullet points (`-`).
- Separate each section with **two blank lines** (`\\n\\n`).
- Do **not** include any commentary outside the outline itself.

Return **only** the final refined markdown outline, nothing else.
"""



refine_outline_user = """
Original Question:
{original_question}

Uninformed Outline:
{uninformed_outline}

Informed Outline:
{informed_outline}

Now generate a refined outline that best fits the interview insights and combines the best structure of both outlines.
"""

# 3. Prompt Template
refine_outline_prompt = ChatPromptTemplate.from_messages([
    ("system", refine_outline_system),
    ("user", refine_outline_user)
])

# 4. Chain
# Replace `model` with your actual LLM instance (e.g. ChatOpenAI, ChatAnthropic, etc.)
refine_report_outline_chain = refine_outline_prompt | model.with_structured_output(RefinedReportOutline)
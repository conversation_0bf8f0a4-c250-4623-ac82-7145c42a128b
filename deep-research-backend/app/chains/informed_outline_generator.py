from typing import TypedDict, Annotated
from langchain.prompts import Chat<PERSON>romptTemplate
from app.llm.client import model

# 1. Output TypedDict
class InformedReportOutline(TypedDict):
    outline: Annotated[str, ..., "Markdown-style outline synthesizing key insights from the interviews, structured into a coherent and logically flowing report."]

# 2. Prompt Templates
report_outline_system = """
You are an expert technical writer creating a clear, logical, and structured outline for a professional report.

The report is based on:
- An original guiding question
- Evidence summaries containing factual findings

Your task is to extract and organize key themes and insights from the content, crafting a markdown-style outline that would form the structure of a well-written report.

Structure it so that it flows logically, with clearly defined sections and subsections. Highlight connections, contrasts, and trends across the content where applicable.

Do not write the full report — just the outline. Do not include any sections or items that there isn't information for.

Do not include sections that disclose sources or process (e.g., Methodology, Interview Summaries, References, Citations, Data Sources, Acknowledgments).
"""

report_outline_user = """
Original Question:
{original_question}

Evidence Summaries:
{interview_histories}

Now write a coherent markdown-style outline for a report based on the evidence above. Do not include sections that reveal research process or sources.
"""

# 3. Prompt Template
informed_report_outline_prompt = ChatPromptTemplate.from_messages([
    ("system", report_outline_system),
    ("user", report_outline_user)
])

# 4. Chain
informed_report_outline_chain = informed_report_outline_prompt | model.with_structured_output(InformedReportOutline)

from langchain_core.output_parsers import StrOutputParser
from langchain.prompts import ChatPromptTemplate
from app.llm.client import model
from app.vectorstore.client import elastic_vector_search

from langchain_core.runnables import RunnableLambda
import logging

logger = logging.getLogger(__name__)

# ─── 1. Retriever (unchanged) ───
retriever = elastic_vector_search.as_retriever(search_kwargs={"k": 5})

section_io: list[dict] = []

def stash_io(io_dict):
    """
    io_dict has keys "context" and "outline".
    Save them, then pass the dict unchanged.
    """
    section_io.append({"outline": io_dict["outline"], "context": io_dict["context"]})
    return io_dict

def debug_vector_search_with_filters(search_params):
    """
    Debug function to perform vector search with filters like the notebook implementation.
    This matches the notebook's approach where question is used directly in the filter.
    """
    from app.utils.debug_logger import debug_logger

    query = search_params["query"]
    conversation_id = search_params["conversation_id"]
    question = search_params["question"]

    logger = debug_logger.get_logger("VECTOR_SEARCH")
    debug_logger.log_separator("VECTOR SEARCH EXECUTION")

    logger.info(f"📝 Generated Query: '{query}'")
    logger.info(f"💬 Conversation ID: '{conversation_id}'")
    logger.info(f"❓ Original Question: '{question}'")

    # Check total documents in index
    try:
        from app.vectorstore.client import es
        from app.core.config import settings

        # Get total document count
        total_docs = es.count(index=settings.elasticsearch_index)
        logger.info(f"📊 Total documents in index: {total_docs['count']}")

        # Check documents with data_returned=True
        data_returned_query = {
            "query": {
                "term": {"metadata.data_returned": True}
            }
        }
        data_returned_count = es.count(index=settings.elasticsearch_index, body=data_returned_query)
        logger.info(f"✅ Documents with data_returned=True: {data_returned_count['count']}")

        # Check documents for this conversation
        conversation_query = {
            "query": {
                "term": {"metadata.conversation_id.keyword": question}
            }
        }
        conversation_count = es.count(index=settings.elasticsearch_index, body=conversation_query)
        logger.info(f"🗣️ Documents for this conversation: {conversation_count['count']}")

        # Check documents matching both filters
        both_filters_query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"metadata.data_returned": True}},
                        {"term": {"metadata.conversation_id.keyword": question}}
                    ]
                }
            }
        }
        both_count = es.count(index=settings.elasticsearch_index, body=both_filters_query)
        logger.info(f"🎯 Documents matching both filters: {both_count['count']}")

    except Exception as e:
        logger.error(f"❌ Error checking document counts: {str(e)}")

    # Perform the actual search using a more robust approach
    try:
        # First try with data_returned=True filter
        retriever_with_data = elastic_vector_search.as_retriever(search_kwargs={
            "k": 5,
            "filter": [
                {"term": {"metadata.data_returned": True}},
                {"term": {"metadata.conversation_id.keyword": question}}
            ]
        })

        results_with_data = retriever_with_data.invoke(query)
        logger.info(f"🎯 Vector search with data_returned=True returned {len(results_with_data)} documents")

        # If no results with data, fall back to all documents for this conversation
        if not results_with_data:
            logger.warning("⚠️ No documents with data_returned=True found, falling back to all conversation documents")
            retriever_all = elastic_vector_search.as_retriever(search_kwargs={
                "k": 5,
                "filter": [
                    {"term": {"metadata.conversation_id.keyword": question}}
                ]
            })
            results_all = retriever_all.invoke(query)
            logger.info(f"🔄 Fallback search returned {len(results_all)} documents")
            results = results_all
        else:
            results = results_with_data

        if results:
            for i, doc in enumerate(results):
                logger.info(f"  📄 Doc {i+1}: {doc.page_content[:100]}...")
                logger.info(f"  🏷️ Metadata: {doc.metadata}")
        else:
            logger.warning("⚠️ No documents returned from vector search!")

        return results

    except Exception as e:
        logger.error(f"❌ Vector search error: {str(e)}")
        return []

# ─── 2. Outline → concise query (unchanged) ───
query_prompt = ChatPromptTemplate.from_messages([
    ("system",
     "Rewrite the outline heading below into the shortest possible "
     "search query that captures its main topic. "
     "Only the phrase—no extra words or punctuation."),
    ("user", "{outline_segment}")
])

query_builder = (
    query_prompt
    | model                                   # your zero-temp gpt-4o-mini
    | RunnableLambda(lambda msg: msg.content.strip().replace("\n", " "))
)

# FOR NOT SHOWING FIGURES IN INTRO AND CONCLUSION
section_prompt = ChatPromptTemplate.from_messages([
    (
        "system",
        "You are writing a section of a formal analytical report for institutional stakeholders. "
        "Be concise and straight to the point.\n"
        "Inputs you receive:\n"
        "• **Outline segment** – the exact items this section must address.\n"
        "• **Source context** – prior Q-A findings.  Some blocks end with a line:\n"
        "      Data Tag: <tag_name>\n"
        "  where <tag_name> marks a table or graphic that will be inserted later.\n\n"
        "Guidelines:\n"
        "1. Use only the information provided; do not invent facts.\n"
        "2. You may cite any numeric values already present in the narrative text of an Answer.\n"
        "3. **If the outline segment is an Introduction or a Conclusion, ignore every Data-Tag line and "
        "write plain narrative with NO placeholders.**\n"
        "4. **CRITICAL: When you see a line that says 'Data Tag: tag_name', you MUST insert the exact placeholder [[tag_name]] in your response.**\n"
        "   - Write your narrative text first\n"
        "   - Then on a new line, insert exactly: [[tag_name]] (using the exact tag name from the Data Tag line)\n"
        "   - Do NOT write generic placeholders like '[Insert data here]' or '[Insert charts here]'\n"
        "   - Do NOT skip the placeholder - it is required for data visualization\n"
        "5. If no 'Data Tag:' line is present, write only narrative with no placeholder.\n"
        "6. Do not mention tags, placeholders, or processing steps—write as if delivering a finished report.\n"
        "7. Never reveal how information was obtained; avoid 'interviews', 'methodology', 'references', 'citations', or 'data sources' language.\n"
        "8. **Example**: If you see 'Data Tag: student_enrollment_data', you must include [[student_enrollment_data]] in your response."
    ),
    (
        "user",
        "### Outline segment\n{outline}\n\n"
        "### Source context\n{context}\n\n"
        "Draft the complete prose for this section, following the outline and the guidelines above."
    ),
])

# ─── 4. join_docs with quote-safe f-string ───
def debug_join_docs(docs):
    """Join documents and add debug logging to see what context is being passed to the AI."""
    context_parts = []

    for i, d in enumerate(docs):
        if "data_tag" in d.metadata:
            data_tag = d.metadata['data_tag']
            content_with_tag = f"{d.page_content}\nData Tag: {data_tag}"
            context_parts.append(content_with_tag)
            logger.info(f"  📄 Doc {i+1}: {d.page_content[:100]}...")
            logger.info(f"  🏷️ Metadata: {d.metadata}")
            logger.info(f"  ✅ Added Data Tag: {data_tag}")
        else:
            context_parts.append(d.page_content)
            logger.info(f"  📄 Doc {i+1}: {d.page_content[:100]}...")
            logger.info(f"  ⚠️ No data_tag in metadata: {d.metadata}")

    final_context = "\n\n".join(context_parts)
    logger.info(f"📝 Final context being sent to AI ({len(final_context)} chars):")
    logger.info(f"   {final_context[:500]}...")

    return {"context": final_context}

join_docs = RunnableLambda(debug_join_docs)

# ─── 5. Debug output parser ───
def debug_output_parser(output: str) -> str:
    """Debug the AI output to see if it contains data tag placeholders."""
    logger.info(f"🤖 AI generated section ({len(output)} chars):")
    logger.info(f"   {output[:300]}...")

    # Check for data tag placeholders
    import re
    placeholders = re.findall(r'\[\[([^\]]+)\]\]', output)
    if placeholders:
        logger.info(f"✅ Found {len(placeholders)} data tag placeholders: {placeholders}")
    else:
        logger.warning("⚠️ No data tag placeholders found in AI output")
        # Check for generic placeholders
        generic_placeholders = re.findall(r'\[Insert[^\]]*\]', output)
        if generic_placeholders:
            logger.warning(f"❌ Found generic placeholders instead: {generic_placeholders}")

    return output

# ─── 6. FINAL PIPELINE ───
# This implementation matches the Jupyter notebook approach exactly
section_writer_chain = (
    {
        "context": (
            {
                "query":         RunnableLambda(lambda inp: inp["outline_segment"]) | query_builder,
                "conversation_id": RunnableLambda(lambda inp: inp["conversation_id"]),
                "question": RunnableLambda(lambda inp: inp["conversation_id"])  # Use conversation_id as question like notebook
            }
            | RunnableLambda(debug_vector_search_with_filters)
            | join_docs
        ),
        "outline": RunnableLambda(lambda inp: inp["outline_segment"]),
    }
    | RunnableLambda(stash_io)
    | section_prompt
    | model | StrOutputParser() | RunnableLambda(debug_output_parser)
)

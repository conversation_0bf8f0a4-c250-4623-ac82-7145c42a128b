from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from app.llm.client import model


# 2. Prompt Templates
missing_data_system = """
You are a technical report-writing assistant.

You are given:
- The original guiding question for the report
- A list of Q-A pairs generated from an interview with a system

Your task is to produce a markdown section titled:

    ## Data Gaps and Limitations

List all explicitly stated **missing data or unanswered questions** in the Q-A pairs.
These may appear in answers as:
- "There is no data available..."
- "No results were found..."
- "Data was missing for..."
- "It was not possible to..."

IMPORTANT:
- Do **not** mention how the data was retrieved (e.g., "SQL query", "system", "process", "model")
- Do **not** refer to failures or errors ("returned no results", "query failed")
- Do **not** include implementation terms or debugging context
- Just state plainly what data is missing

Formatting Rules:
- Start with exactly: `## Data Gaps and Limitations`
- Use a line break, then plain bullets (`-`) for each gap
- Each bullet should begin with the missing topic, e.g.:
    - No data available on [topic]
    - No results regarding [topic]
- No additional explanation, commentary, or framing
- No references or section numbers
"""


missing_data_user = """
Original Question:
{original_question}

Interview Q-A Pairs:
{interview_history}

Write only the `## Data Gaps and Limitations` section.
"""

# 3. Prompt Template
missing_data_prompt = ChatPromptTemplate.from_messages([
    ("system", missing_data_system),
    ("user", missing_data_user)
])

# 4. Chain
missing_data_section_chain = (
    missing_data_prompt
    | model | StrOutputParser()
)

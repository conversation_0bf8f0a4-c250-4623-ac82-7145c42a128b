from typing import List
from typing_extensions import Annotated, TypedDict

from typing import List, Annotated, TypedDict

from app.llm.client import model
from langchain.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableLambda


class RelevantTables(TypedDict):
    """
    TypedDict representing a question and its relevant table names.

    Attributes:
        question: The original natural language question.
        tables: A list of strings representing the names of relevant tables.
    """
    question: Annotated[str, ..., "The natural language question"]
    tables: Annotated[List[str], ..., "List of table names relevant to the query"]


system_template = """
Here is the database schema:

{schema_text}
"""

user_template = """
Which tables would you query to answer the question: "{question}"?

{feedback}

Output only the full table names, with no extra text. eg. core.some_table.
"""

prompt_template = ChatPromptTemplate.from_messages(
    [
        ("system", system_template),
        ("user", user_template)
    ]
)

tables_finder_chain = (prompt_template | model.with_structured_output(RelevantTables))

def batch_tables_finder(answerable_questions_dict):
    table_finder_response = tables_finder_chain.batch([ 
        {"question": q, "schema_text": answerable_questions_dict["schema_text"]} for q in answerable_questions_dict["answerable_questions"]
    ])
    return {"questions_tables": table_finder_response}
# sql_query_gen_chain

batch_tables_finder_chain = RunnableLambda(batch_tables_finder)
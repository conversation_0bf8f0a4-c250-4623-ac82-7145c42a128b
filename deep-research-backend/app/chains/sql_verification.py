from typing import TypedDict, Annotated
from langchain.prompts import Cha<PERSON><PERSON>romptTemplate
from app.llm.client import model

class SQLVerification(TypedDict):
    """
    TypedDict representing the output of a query verification process.

    Attributes:
        question: The original natural language question.
        sql: The generated SQL query.
        correct: A boolean indicating whether the query answers the question precisely.
        reasoning: Any accompanying text explaining the logical reasoning behind how the SQL was formed.
        feedback: Suggest how the question could be clarified or how the SQL might be improved.
    """
    question: Annotated[str, ..., "The natural language question"]
    sql: Annotated[str, ..., "The generated SQL query"]
    correct: Annotated[bool, ..., "True if the query truly fetches the relevant information that answers the question precisely based on the schema, false otherwise"]
    reasoning: Annotated[str, ..., "Explains the logic or steps used to verify the SQL query and arrive at the decision on whether it is correct"]
    feedback: Annotated[str, ..., "Suggested improvement or critical note for debugging or retrying"]

# --- SQL Verification Prompt Templates ---

sql_verification_system_template = """
You are an SQL expert verifying a SQL query against a given question and database schema.

You will receive:
1. A database schema
2. A natural language question
3. A generated SQL query

Your job is to assess if the SQL query correctly answers the question based on the schema.

Respond in JSON format with:
- "question": The original natural language question
- "sql": The SQL query being evaluated
- "correct": A boolean indicating if the SQL accurately answers the question
- "reasoning": A detailed explanation for your judgment
- "feedback": Suggest how the question could be clarified or how the SQL might be improved.

"""

sql_verification_user_template = """
Database schema:
{tables_schema}

Question:
"{question}"

Generated SQL:
{sql}

Does the SQL query correctly and completely answer the question?
"""

# Updated prompt template for verification
sql_verification_prompt = ChatPromptTemplate.from_messages([
    ("system", sql_verification_system_template),
    ("user", sql_verification_user_template),
])

sql_verification_chain = sql_verification_prompt | model.with_structured_output(SQLVerification)
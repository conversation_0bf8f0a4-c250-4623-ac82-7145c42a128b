import json
from langchain_core.runnables import RunnableLambda

from app.schema_fxns.structure import convert_processed_json_to_compact_format
from app.schema_fxns.categorical_summary import format_categorical_summary
from app.utils.schema_loader import get_schema_file

def get_relevant_tables_schema(tables):
    """Load db_json internally and extract relevant tables schema - matches notebook approach"""
    full_db_structure = get_schema_file("full_db_structure.txt")
    db_json = json.loads(full_db_structure)

    relevant_tables_json = {}
    for table in tables:
        relevant_tables_json[table] = db_json[table]

    return convert_processed_json_to_compact_format(relevant_tables_json)

def get_relevant_tables_categorical_summary(tables):
    """Load db_categorical_summary internally and extract relevant tables - matches notebook approach"""
    db_categorical_summary_file = get_schema_file("db_categorical_summary.json")
    db_categorical_summary = json.loads(db_categorical_summary_file)

    relevant_tables_json = {}
    for table in tables:
        try:
            relevant_tables_json[table] = db_categorical_summary[table]
        except:
            pass #If the table has no categorical values

    return format_categorical_summary(relevant_tables_json)
    
# sql_query_gen_chain

relevant_tables_schemas_extractor_chain = RunnableLambda(lambda output_from_first: {
            # Pass along the user’s original question or anything else you need
            "question": output_from_first["question"],
    
            # output_from_first is the dict returned by .with_structured_output()
            # e.g.  {"tables": ["StudentFees", "Institution"], ...}
            "tables_schema": get_relevant_tables_schema(output_from_first["tables"]),
            "tables_categorical_summary": get_relevant_tables_categorical_summary(output_from_first["tables"])
        })

def batch_relevant_tables_schemas_extractor(questions_tables_dict):
    relevant_tables_schemas_extractor_response = relevant_tables_schemas_extractor_chain.batch(questions_tables_dict["questions_tables"])
    return {"questions_schemas": relevant_tables_schemas_extractor_response}

batch_relevant_tables_schemas_extractor_chain = RunnableLambda(batch_relevant_tables_schemas_extractor)
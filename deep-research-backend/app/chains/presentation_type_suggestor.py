from langchain_core.prompts import Chat<PERSON>romptTemplate
from typing import TypedDict, Annotated, Literal

from app.llm.client import model

class PresentationSuggestion(TypedDict):
    presentation_type: Annotated[
        Literal["bar_chart", "area_chart", "line_chart", "pie_chart", "table", "text"],
        "The best presentation format for the answer: 'bar_chart', 'area_chart', 'line_chart', 'pie_chart', 'table', or 'text'"
    ]

presentation_decision_system = """
You are a helpful data communication assistant.

You will be given:
- A user's question
- A set of structured data (e.g., rows from a database)
- A proposed natural language answer to the question

Your task is to decide the best way to visually (or not visually) present the answer:
- Choose "bar_chart" for comparing discrete categories or values across different groups
- Choose "area_chart" for showing trends over time or cumulative data
- Choose "line_chart" for showing trends, changes over time, or continuous data
- Choose "pie_chart" for showing proportions or percentages of a whole
- Choose "table" if the user would benefit from seeing the raw or tabular data directly
- Choose "text" if the natural language answer is sufficient on its own

Only return the best single choice as "presentation_type".
"""


presentation_decision_user = """
Question:
{question}

Data:
{data}

Answer: {answer}

Based on the above, what is the best way to present this information?
"""


presentation_decision_prompt = ChatPromptTemplate.from_messages([
    ("system", presentation_decision_system),
    ("user", presentation_decision_user)
])

presentation_decision_chain = presentation_decision_prompt | model.with_structured_output(PresentationSuggestion)
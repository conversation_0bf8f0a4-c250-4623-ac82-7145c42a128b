from typing import TypedDict, Annotated
from langchain.prompts import ChatPromptTemplate
from app.llm.client import model

class FollowUpQuestion(TypedDict):
    follow_up_question: Annotated[str, "The next best question to ask, or 'No further questions' if the topic has been fully explored."]

interview_questioner_system = """
You are an intelligent and curious interviewer, referred to as 'Questioner' conducting a structured conversation with an expert, referred to as "Expert".
You are trying to gather insightful, in-depth information about a topic.

You will be given:
- The main topic of interest
- A history of prior questions you (Questioner) have asked, and the expert's answers

Your job is to ask the next best question that builds on the discussion so far, uncovers deeper insights, or explores unexplained angles.

Avoid repeating what's already been covered.
Ask only one thoughtful, specific question at a time.
Be concise, but make sure the question adds value to the dialogue.
IMPORTANT: If you have no further questions, say these exact words "No further questions"
"""

interview_questioner_user = """
Main topic:
{topic}

Conversation so far:
{history}

Ask the next best follow-up question to deepen the interview. 
"""

interview_questioner_prompt = ChatPromptTemplate.from_messages([
    ("system", interview_questioner_system),
    ("user", interview_questioner_user)
])

interview_questioner = (interview_questioner_prompt | model.with_structured_output(FollowUpQuestion))
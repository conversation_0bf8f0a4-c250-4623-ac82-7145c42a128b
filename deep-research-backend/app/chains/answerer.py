from typing import TypedDict, Annotated
from langchain.prompts import Chat<PERSON>romptTemplate
from app.llm.client import model

# 1. Structured Output Schema
class SQLAnswer(TypedDict):
    answer: Annotated[
        str,
        "A concise, natural language explanation of the SQL result that directly answers the user's question."
    ]
    data_tag: Annotated[
        str,
        "A short, descriptive tag summarizing the SQL result in the context of the question. Must be a valid Python snake_case variable name."
    ]

# 2. System Prompt
sql_answerer_system = """
You are an expert data analyst and communicator.

You will be given:
- A user's natural language question
- The results of an SQL query that was executed to answer that question

Your task is to:
1. Write a clear, insightful, and accurate explanation that directly answers the user's question using the provided data.
2. Suggest a data tag name — a short, descriptive variable name that clearly summarizes what the SQL result represents.

Instructions for the data tag:
- It must follow Python variable naming conventions (snake_case).
- It should reflect the relationship between the data and the question.
- Avoid generic names like 'data', 'result', or 'info'.
- Examples of good tags: 'student_enrollments_by_department', 'average_gpa_by_faculty', 'monthly_library_visits'
"""

# 3. User Prompt Template
sql_answerer_user = """
User question:
{question}

SQL results:
{result}

Write a clear natural language answer to the question using the results, and suggest an appropriate snake_case data tag.
"""

# 4. Prompt Template
sql_answerer_prompt = ChatPromptTemplate.from_messages([
    ("system", sql_answerer_system),
    ("user", sql_answerer_user)
])

# 5. Final Chain (replace `model` with your actual model instance)
sql_answerer_chain = sql_answerer_prompt | model.with_structured_output(SQLAnswer)
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .routers import report, speech

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Next.js development server
        "http://127.0.0.1:3000",  # Alternative localhost
        "http://**************:3000",  # Network address from Next.js output
        "http://localhost:3001",  # Alternative port
        "*"  # Allow all origins in development (remove in production)
    ],
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
    expose_headers=["*"],  # Expose all headers for EventSource
)

app.include_router(report.router)
app.include_router(speech.router)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "ok", "message": "Backend is running with CORS enabled"}

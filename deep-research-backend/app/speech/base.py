"""
Base classes for Speech-to-Text and Text-to-Speech providers.

This module defines the abstract interfaces that all STT and TTS providers must implement,
enabling a pluggable architecture where providers can be easily swapped.
"""

from abc import ABC, abstractmethod
from typing import IO, Optional, Dict, Any
from fastapi.responses import StreamingResponse
from pydantic import BaseModel


class TTSRequest(BaseModel):
    """Request model for Text-to-Speech conversion."""
    text: str
    voice_id: Optional[str] = None
    voice_settings: Optional[Dict[str, Any]] = None
    output_format: Optional[str] = "mp3_22050_32"
    model_id: Optional[str] = None


class STTRequest(BaseModel):
    """Request model for Speech-to-Text conversion."""
    language_code: Optional[str] = None
    model_id: Optional[str] = None
    tag_audio_events: bool = True
    diarize: bool = False


class STTResponse(BaseModel):
    """Response model for Speech-to-Text conversion."""
    text: str
    confidence: Optional[float] = None
    language: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class BaseTTSProvider(ABC):
    """Abstract base class for Text-to-Speech providers."""
    
    @abstractmethod
    def __init__(self, api_key: str, **kwargs):
        """Initialize the TTS provider with API key and optional configuration."""
        pass
    
    @abstractmethod
    async def text_to_speech(self, request: TTSRequest) -> StreamingResponse:
        """
        Convert text to speech and return a streaming audio response.
        
        Args:
            request: TTSRequest containing text and configuration
            
        Returns:
            StreamingResponse with audio data
        """
        pass
    
    @abstractmethod
    def get_available_voices(self) -> Dict[str, Any]:
        """
        Get list of available voices for this provider.
        
        Returns:
            Dictionary containing voice information
        """
        pass


class BaseSTTProvider(ABC):
    """Abstract base class for Speech-to-Text providers."""
    
    @abstractmethod
    def __init__(self, api_key: str, **kwargs):
        """Initialize the STT provider with API key and optional configuration."""
        pass
    
    @abstractmethod
    async def speech_to_text(self, audio_file: IO[bytes], request: STTRequest) -> STTResponse:
        """
        Convert speech to text.
        
        Args:
            audio_file: Audio file as bytes IO
            request: STTRequest containing configuration
            
        Returns:
            STTResponse with transcribed text and metadata
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> list[str]:
        """
        Get list of supported audio formats.
        
        Returns:
            List of supported audio format extensions
        """
        pass


class SpeechProviderError(Exception):
    """Base exception for speech provider errors."""
    pass


class TTSProviderError(SpeechProviderError):
    """Exception raised by TTS providers."""
    pass


class STTProviderError(SpeechProviderError):
    """Exception raised by STT providers."""
    pass

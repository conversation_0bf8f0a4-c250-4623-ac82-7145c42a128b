"""
Speech service manager for handling STT and TTS operations.

This module provides a high-level service interface for speech operations,
managing provider instances and providing a clean API for the application.
"""

from typing import IO, Optional, Dict, Any
from fastapi.responses import StreamingResponse

from .factory import SpeechProviderFactory, SpeechProviderType
from .base import T<PERSON><PERSON><PERSON><PERSON>, STTRequest, STTResponse
from ..core.config import settings

# Map provider types to their corresponding setting names for API keys
API_KEY_SETTINGS_MAP = {
    SpeechProviderType.ELEVENLABS: 'elevenlabs_api_key',
    SpeechProviderType.OPENAI: 'openai_api_key',
}


class SpeechService:
    """High-level service for speech operations."""
    
    def __init__(
        self,
        provider_type: SpeechProviderType = SpeechProviderType.OPENAI,
        api_key: Optional[str] = None
    ):
        """
        Initialize the speech service.
        
        Args:
            provider_type: Type of speech provider to use
            api_key: API key for the provider (defaults to settings)
        """
        self.provider_type = provider_type
        
        if api_key:
            self.api_key = api_key
        else:
            api_key_setting_name = API_KEY_SETTINGS_MAP.get(provider_type)
            if not api_key_setting_name:
                raise ValueError(f"No API key setting configured for provider: {provider_type.value}")
            self.api_key = getattr(settings, api_key_setting_name, None)
        
        if not self.api_key:
            raise ValueError(f"API key for {provider_type.value} is required but not found")
        
        # Create provider instances
        self._combined_provider = SpeechProviderFactory.create_combined_provider(
            provider_type=self.provider_type,
            api_key=self.api_key
        )
        
        self._tts_provider = self._combined_provider.get_tts_provider()
        self._stt_provider = self._combined_provider.get_stt_provider()
    
    async def text_to_speech(
        self, 
        text: str, 
        voice_id: Optional[str] = None,
        voice_settings: Optional[Dict[str, Any]] = None,
        output_format: str = "mp3_22050_32",
        model_id: Optional[str] = None
    ) -> StreamingResponse:
        """
        Convert text to speech.
        
        Args:
            text: Text to convert to speech
            voice_id: Voice ID to use (provider-specific)
            voice_settings: Voice configuration settings
            output_format: Audio output format
            model_id: Model ID to use for generation
            
        Returns:
            StreamingResponse with audio data
        """
        request = TTSRequest(
            text=text,
            voice_id=voice_id,
            voice_settings=voice_settings,
            output_format=output_format,
            model_id=model_id
        )
        
        return await self._tts_provider.text_to_speech(request)
    
    async def speech_to_text(
        self, 
        audio_file: IO[bytes],
        language_code: Optional[str] = None,
        model_id: Optional[str] = None,
        tag_audio_events: bool = True,
        diarize: bool = False
    ) -> STTResponse:
        """
        Convert speech to text.
        
        Args:
            audio_file: Audio file as bytes IO
            language_code: Language code for transcription
            model_id: Model ID to use for transcription
            tag_audio_events: Whether to tag audio events
            diarize: Whether to perform speaker diarization
            
        Returns:
            STTResponse with transcribed text and metadata
        """
        request = STTRequest(
            language_code=language_code,
            model_id=model_id,
            tag_audio_events=tag_audio_events,
            diarize=diarize
        )
        
        return await self._stt_provider.speech_to_text(audio_file, request)
    
    def get_available_voices(self) -> Dict[str, Any]:
        """
        Get available voices for TTS.
        
        Returns:
            Dictionary containing voice information
        """
        return self._tts_provider.get_available_voices()
    
    def get_supported_formats(self) -> list[str]:
        """
        Get supported audio formats for STT.
        
        Returns:
            List of supported audio format extensions
        """
        return self._stt_provider.get_supported_formats()
    
    def get_provider_info(self) -> Dict[str, Any]:
        """
        Get information about the current provider.
        
        Returns:
            Dictionary with provider information
        """
        return {
            "provider_type": self.provider_type.value,
            "tts_available": True,
            "stt_available": True,
            "supported_formats": self.get_supported_formats(),
            "voices_count": len(self.get_available_voices().get("voices", []))
        }


# Global speech service instance
_speech_service: Optional[SpeechService] = None


def get_speech_service(provider_type=SpeechProviderType.OPENAI) -> SpeechService:
    """
    Get the global speech service instance.
    
    Returns:
        SpeechService instance
    """
    global _speech_service
    
    if _speech_service is None:
        # Default to ElevenLabs if not initialized, or choose another default
        _speech_service = SpeechService(provider_type)
    
    return _speech_service


def initialize_speech_service(
    provider_type: SpeechProviderType,
    api_key: Optional[str] = None
) -> SpeechService:
    """
    Initialize the global speech service instance.
    
    Args:
        provider_type: Type of speech provider to use
        api_key: API key for the provider (if not provided, reads from settings)
        
    Returns:
        SpeechService instance
    """
    global _speech_service
    
    _speech_service = SpeechService(
        provider_type=provider_type,
        api_key=api_key
    )
    
    return _speech_service
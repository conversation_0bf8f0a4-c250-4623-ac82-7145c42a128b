"""
ElevenLabs provider implementation for STT and TTS services.

This module implements the ElevenLabs-specific logic for both speech-to-text
and text-to-speech functionality using the ElevenLabs API.
"""

import io
from typing import I<PERSON>, Dict, Any, Optional
from fastapi.responses import StreamingResponse
from elevenlabs import VoiceSettings
from elevenlabs.client import ElevenLabs

from ..base import (
    BaseTTSProvider, 
    BaseSTTProvider, 
    TTSRequest, 
    STTRequest, 
    STTResponse,
    TTSProviderError,
    STTProviderError
)


class ElevenLabsTTSProvider(BaseTTSProvider):
    """ElevenLabs Text-to-Speech provider implementation."""
    
    def __init__(self, api_key: str, **kwargs):
        """Initialize ElevenLabs TTS provider."""
        self.client = ElevenLabs(api_key=api_key)
        self.default_voice_id = kwargs.get('default_voice_id', 'pNInz6obpgDQGcFmaJgB')  # Adam voice
        self.default_model_id = kwargs.get('default_model_id', 'eleven_multilingual_v2')
    
    async def text_to_speech(self, request: TTSRequest) -> StreamingResponse:
        """
        Convert text to speech using ElevenLabs API.
        
        Args:
            request: TTSRequest containing text and configuration
            
        Returns:
            StreamingResponse with MP3 audio data
        """
        try:
            # Use provided voice_id or default
            voice_id = request.voice_id or self.default_voice_id
            
            # Use provided model_id or default
            model_id = request.model_id or self.default_model_id
            
            # Configure voice settings
            voice_settings = VoiceSettings(
                stability=0.0,
                similarity_boost=1.0,
                style=0.0,
                use_speaker_boost=True,
                speed=1.0,
                **request.voice_settings if request.voice_settings else {}
            )
            
            # Generate speech
            response = self.client.text_to_speech.stream(
                voice_id=voice_id,
                output_format=request.output_format,
                text=request.text,
                model_id=model_id,
                voice_settings=voice_settings,
            )
            
            return StreamingResponse(response, media_type="audio/mp3")
            
        except Exception as e:
            raise TTSProviderError(f"ElevenLabs TTS error: {str(e)}")
    
    def get_available_voices(self) -> Dict[str, Any]:
        """Get available voices from ElevenLabs."""
        try:
            voices = self.client.voices.get_all()
            return {
                "voices": [
                    {
                        "voice_id": voice.voice_id,
                        "name": voice.name,
                        "category": voice.category,
                        "description": voice.description,
                        "preview_url": voice.preview_url,
                        "available_for_tiers": voice.available_for_tiers,
                        "settings": voice.settings.model_dump() if voice.settings else None
                    }
                    for voice in voices.voices
                ]
            }
        except Exception as e:
            raise TTSProviderError(f"Failed to get ElevenLabs voices: {str(e)}")


class ElevenLabsSTTProvider(BaseSTTProvider):
    """ElevenLabs Speech-to-Text provider implementation."""
    
    def __init__(self, api_key: str, **kwargs):
        """Initialize ElevenLabs STT provider."""
        self.client = ElevenLabs(api_key=api_key)
        self.default_model_id = kwargs.get('default_model_id', 'scribe_v1')
    
    async def speech_to_text(self, audio_file: IO[bytes], request: STTRequest) -> STTResponse:
        """
        Convert speech to text using ElevenLabs API.
        
        Args:
            audio_file: Audio file as bytes IO
            request: STTRequest containing configuration
            
        Returns:
            STTResponse with transcribed text
        """
        try:
            # Use provided model_id or default
            model_id = request.model_id or self.default_model_id
            
            # Convert audio file to BytesIO if needed
            if hasattr(audio_file, 'read'):
                audio_data = io.BytesIO(audio_file.read())
            else:
                audio_data = audio_file
            
            # Perform transcription
            transcription = self.client.speech_to_text.convert(
                file=audio_data,
                model_id=model_id,
                tag_audio_events=request.tag_audio_events,
                language_code=request.language_code,
                diarize=request.diarize,
            )
            
            return STTResponse(
                text=transcription.text,
                language=request.language_code,
                metadata={
                    "model_id": model_id,
                    "tag_audio_events": request.tag_audio_events,
                    "diarize": request.diarize
                }
            )
            
        except Exception as e:
            raise STTProviderError(f"ElevenLabs STT error: {str(e)}")
    
    def get_supported_formats(self) -> list[str]:
        """Get supported audio formats for ElevenLabs STT."""
        return [
            "wav", "mp3", "mp4", "mpeg", "mpga", "m4a", "ogg", "wav", "webm"
        ]


class ElevenLabsProvider:
    """Combined ElevenLabs provider for both TTS and STT."""
    
    def __init__(self, api_key: str, **kwargs):
        """Initialize combined ElevenLabs provider."""
        self.tts = ElevenLabsTTSProvider(api_key, **kwargs)
        self.stt = ElevenLabsSTTProvider(api_key, **kwargs)
    
    def get_tts_provider(self) -> ElevenLabsTTSProvider:
        """Get the TTS provider instance."""
        return self.tts
    
    def get_stt_provider(self) -> ElevenLabsSTTProvider:
        """Get the STT provider instance."""
        return self.stt

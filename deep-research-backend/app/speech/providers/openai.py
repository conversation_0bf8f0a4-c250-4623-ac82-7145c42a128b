"""
OpenAI provider implementation for STT and TTS services.

This module implements the OpenAI-specific logic for both speech-to-text (Whisper)
and text-to-speech functionality using the OpenAI API.
"""

import re
from typing import I<PERSON>, Dict, Any, List
from fastapi.responses import StreamingResponse
from openai import Async<PERSON>penA<PERSON>

from ..base import (
    BaseTTSProvider,
    BaseSTTProvider,
    TTSRequest,
    STTRequest,
    STTResponse,
    TTSProviderError,
    STTProviderError
)

class OpenAITTSProvider(BaseTTSProvider):
    """OpenAI Text-to-Speech provider implementation."""

    # OpenAI TTS has a 4096 character limit
    MAX_TEXT_LENGTH = 4096

    def __init__(self, api_key: str, **kwargs):
        """Initialize OpenAI TTS provider."""
        self.client = AsyncOpenAI(api_key=api_key)
        self.default_voice = kwargs.get('default_voice', 'alloy')
        self.default_model_id = kwargs.get('default_model_id', 'tts-1')

    def _chunk_text(self, text: str, max_length: int) -> List[str]:
        """
        Intelligently chunk text into smaller pieces for TTS processing.

        Args:
            text: Text to chunk
            max_length: Maximum length per chunk

        Returns:
            List of text chunks
        """
        if len(text) <= max_length:
            return [text]

        chunks = []
        current_chunk = ''

        # Split by sentences first
        sentences = re.split(r'(?<=[.!?])\s+', text)

        for sentence in sentences:
            # If adding this sentence would exceed the limit
            if len(current_chunk) + len(sentence) > max_length:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    # Single sentence is too long, split by words
                    words = sentence.split(' ')
                    word_chunk = ''

                    for word in words:
                        if len(word_chunk) + len(word) + 1 > max_length:
                            if word_chunk:
                                chunks.append(word_chunk.strip())
                                word_chunk = word
                            else:
                                # Single word is too long, just add it
                                chunks.append(word)
                        else:
                            word_chunk += (' ' if word_chunk else '') + word

                    if word_chunk:
                        current_chunk = word_chunk
            else:
                current_chunk += (' ' if current_chunk else '') + sentence

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    async def _text_to_speech_single(self, text: str, voice: str, model_id: str) -> bytes:
        """
        Convert a single text chunk to speech.

        Args:
            text: Text to convert (must be within length limit)
            voice: Voice to use
            model_id: Model to use

        Returns:
            Audio data as bytes
        """
        response = await self.client.audio.speech.create(
            model=model_id,
            voice=voice,
            input=text,
            response_format="mp3"
        )

        # Read all bytes from the response
        audio_data = b''
        async for chunk in response.iter_bytes():
            audio_data += chunk

        return audio_data

    async def text_to_speech(self, request: TTSRequest) -> StreamingResponse:
        """
        Convert text to speech using OpenAI API.
        Handles long text by chunking and combining audio.

        Args:
            request: TTSRequest containing text and configuration

        Returns:
            StreamingResponse with MP3 audio data
        """
        try:
            voice = request.voice_id or self.default_voice
            model_id = request.model_id or self.default_model_id

            # Check if text needs chunking
            if len(request.text) <= self.MAX_TEXT_LENGTH:
                # Single request for short text
                response = await self.client.audio.speech.create(
                    model=model_id,
                    voice=voice,
                    input=request.text,
                    response_format="mp3"
                )

                return StreamingResponse(response.iter_bytes(), media_type="audio/mp3")

            else:
                # Chunk long text and combine audio
                chunks = self._chunk_text(request.text, self.MAX_TEXT_LENGTH)
                audio_parts = []

                for chunk in chunks:
                    audio_data = await self._text_to_speech_single(chunk, voice, model_id)
                    audio_parts.append(audio_data)

                # Combine all audio parts
                combined_audio = b''.join(audio_parts)

                # Create a generator to stream the combined audio
                def audio_generator():
                    yield combined_audio

                return StreamingResponse(audio_generator(), media_type="audio/mp3")

        except Exception as e:
            raise TTSProviderError(f"OpenAI TTS error: {str(e)}")
    
    def get_available_voices(self) -> Dict[str, Any]:
        """Get available voices for OpenAI TTS."""
        # OpenAI has a fixed set of voices
        return {
            "voices": [
                {"voice_id": "alloy", "name": "Alloy"},
                {"voice_id": "echo", "name": "Echo"},
                {"voice_id": "fable", "name": "Fable"},
                {"voice_id": "onyx", "name": "Onyx"},
                {"voice_id": "nova", "name": "Nova"},
                {"voice_id": "shimmer", "name": "Shimmer"},
            ]
        }


class OpenAISTTProvider(BaseSTTProvider):
    """OpenAI Speech-to-Text (Whisper) provider implementation."""
    
    def __init__(self, api_key: str, **kwargs):
        """Initialize OpenAI STT provider."""
        self.client = AsyncOpenAI(api_key=api_key)
        self.default_model_id = kwargs.get('default_model_id', 'whisper-1')
    
    async def speech_to_text(self, audio_file: IO[bytes], request: STTRequest) -> STTResponse:
        """
        Convert speech to text using OpenAI Whisper API.
        
        Args:
            audio_file: Audio file as bytes IO
            request: STTRequest containing configuration
            
        Returns:
            STTResponse with transcribed text
        """
        try:
            model_id = request.model_id or self.default_model_id
            
            # The OpenAI client expects a file-like object with a name attribute.
            # We pass a tuple `(filename, file_data)` to handle this.
            file_tuple = ("audio.wav", audio_file.read())

            # OpenAI expects ISO-639-1 format (e.g., "en" not "eng")
            # If language_code is provided, convert common formats
            # If no language is provided, OpenAI will auto-detect
            transcription_params = {
                "model": model_id,
                "file": file_tuple,
            }

            if request.language_code:
                # Convert common language codes to ISO-639-1
                language_map = {
                    'eng': 'en',
                    'spa': 'es',
                    'fra': 'fr',
                    'deu': 'de',
                    'ita': 'it',
                    'por': 'pt',
                    'rus': 'ru',
                    'jpn': 'ja',
                    'kor': 'ko',
                    'zho': 'zh'
                }
                language = language_map.get(request.language_code, request.language_code)
                transcription_params["language"] = language

            transcription = await self.client.audio.transcriptions.create(**transcription_params)

            # OpenAI Whisper response may not always include language attribute
            detected_language = getattr(transcription, 'language', None)

            # If we provided a language but response doesn't have one, use what we provided
            if not detected_language and 'language' in transcription_params:
                detected_language = transcription_params['language']

            return STTResponse(
                text=transcription.text,
                language=detected_language,
                metadata={
                    "model_id": model_id,
                    "provided_language": transcription_params.get('language'),
                    "detected_language": detected_language
                }
            )
            
        except Exception as e:
            raise STTProviderError(f"OpenAI STT error: {str(e)}")
    
    def get_supported_formats(self) -> list[str]:
        """Get supported audio formats for OpenAI STT (Whisper)."""
        return [
            "flac", "m4a", "mp3", "mp4", "mpeg", "mpga", "oga", "ogg", "wav", "webm"
        ]


class OpenAIProvider:
    """Combined OpenAI provider for both TTS and STT."""
    
    def __init__(self, api_key: str, **kwargs):
        """Initialize combined OpenAI provider."""
        self.tts = OpenAITTSProvider(api_key, **kwargs)
        self.stt = OpenAISTTProvider(api_key, **kwargs)
    
    def get_tts_provider(self) -> OpenAITTSProvider:
        """Get the TTS provider instance."""
        return self.tts
    
    def get_stt_provider(self) -> OpenAISTTProvider:
        """Get the STT provider instance."""
        return self.stt
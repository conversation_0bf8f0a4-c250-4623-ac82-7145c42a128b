"""
OpenAI provider implementation for STT and TTS services.

This module implements the OpenAI-specific logic for both speech-to-text (Whisper)
and text-to-speech functionality using the OpenAI API.
"""

from typing import I<PERSON>, Dict, Any
from fastapi.responses import StreamingResponse
from openai import Async<PERSON>penA<PERSON>

from ..base import (
    BaseTTSProvider, 
    BaseSTTProvider, 
    TTSRequest, 
    STTRequest, 
    STTResponse,
    TTSProviderError,
    STTProviderError
)

class OpenAITTSProvider(BaseTTSProvider):
    """OpenAI Text-to-Speech provider implementation."""
    
    def __init__(self, api_key: str, **kwargs):
        """Initialize OpenAI TTS provider."""
        self.client = AsyncOpenAI(api_key=api_key)
        self.default_voice = kwargs.get('default_voice', 'alloy')
        self.default_model_id = kwargs.get('default_model_id', 'tts-1')
    
    async def text_to_speech(self, request: TTSRequest) -> StreamingResponse:
        """
        Convert text to speech using OpenAI API.
        
        Args:
            request: TTSRequest containing text and configuration
            
        Returns:
            StreamingResponse with MP3 audio data
        """
        try:
            voice = request.voice_id or self.default_voice
            model_id = request.model_id or self.default_model_id
            
            # OpenAI API uses 'input' for text and 'voice' for voice_id
            response = await self.client.audio.speech.create(
                model=model_id,
                voice=voice,
                input=request.text,
                response_format="mp3" # OpenAI supports mp3, opus, aac, flac
            )
            
            # The response object has a .stream_to_file method, but for streaming
            # back to a client, we can read the raw content and stream it.
            return StreamingResponse(response.iter_bytes(), media_type="audio/mp3")

        except Exception as e:
            raise TTSProviderError(f"OpenAI TTS error: {str(e)}")
    
    def get_available_voices(self) -> Dict[str, Any]:
        """Get available voices for OpenAI TTS."""
        # OpenAI has a fixed set of voices
        return {
            "voices": [
                {"voice_id": "alloy", "name": "Alloy"},
                {"voice_id": "echo", "name": "Echo"},
                {"voice_id": "fable", "name": "Fable"},
                {"voice_id": "onyx", "name": "Onyx"},
                {"voice_id": "nova", "name": "Nova"},
                {"voice_id": "shimmer", "name": "Shimmer"},
            ]
        }


class OpenAISTTProvider(BaseSTTProvider):
    """OpenAI Speech-to-Text (Whisper) provider implementation."""
    
    def __init__(self, api_key: str, **kwargs):
        """Initialize OpenAI STT provider."""
        self.client = AsyncOpenAI(api_key=api_key)
        self.default_model_id = kwargs.get('default_model_id', 'whisper-1')
    
    async def speech_to_text(self, audio_file: IO[bytes], request: STTRequest) -> STTResponse:
        """
        Convert speech to text using OpenAI Whisper API.
        
        Args:
            audio_file: Audio file as bytes IO
            request: STTRequest containing configuration
            
        Returns:
            STTResponse with transcribed text
        """
        try:
            model_id = request.model_id or self.default_model_id
            
            # The OpenAI client expects a file-like object with a name attribute.
            # We pass a tuple `(filename, file_data)` to handle this.
            file_tuple = ("audio.wav", audio_file.read())

            # OpenAI expects ISO-639-1 format (e.g., "en" not "eng")
            # If language_code is provided, convert common formats
            # If no language is provided, OpenAI will auto-detect
            transcription_params = {
                "model": model_id,
                "file": file_tuple,
            }

            if request.language_code:
                # Convert common language codes to ISO-639-1
                language_map = {
                    'eng': 'en',
                    'spa': 'es',
                    'fra': 'fr',
                    'deu': 'de',
                    'ita': 'it',
                    'por': 'pt',
                    'rus': 'ru',
                    'jpn': 'ja',
                    'kor': 'ko',
                    'zho': 'zh'
                }
                language = language_map.get(request.language_code, request.language_code)
                transcription_params["language"] = language

            transcription = await self.client.audio.transcriptions.create(**transcription_params)

            # OpenAI Whisper response may not always include language attribute
            detected_language = getattr(transcription, 'language', None)

            # If we provided a language but response doesn't have one, use what we provided
            if not detected_language and 'language' in transcription_params:
                detected_language = transcription_params['language']

            return STTResponse(
                text=transcription.text,
                language=detected_language,
                metadata={
                    "model_id": model_id,
                    "provided_language": transcription_params.get('language'),
                    "detected_language": detected_language
                }
            )
            
        except Exception as e:
            raise STTProviderError(f"OpenAI STT error: {str(e)}")
    
    def get_supported_formats(self) -> list[str]:
        """Get supported audio formats for OpenAI STT (Whisper)."""
        return [
            "flac", "m4a", "mp3", "mp4", "mpeg", "mpga", "oga", "ogg", "wav", "webm"
        ]


class OpenAIProvider:
    """Combined OpenAI provider for both TTS and STT."""
    
    def __init__(self, api_key: str, **kwargs):
        """Initialize combined OpenAI provider."""
        self.tts = OpenAITTSProvider(api_key, **kwargs)
        self.stt = OpenAISTTProvider(api_key, **kwargs)
    
    def get_tts_provider(self) -> OpenAITTSProvider:
        """Get the TTS provider instance."""
        return self.tts
    
    def get_stt_provider(self) -> OpenAISTTProvider:
        """Get the STT provider instance."""
        return self.stt
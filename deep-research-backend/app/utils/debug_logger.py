"""
Centralized debug logger for vector search and report generation debugging.
"""

import logging
import os
from datetime import datetime
from typing import Optional

class DebugLogger:
    _instance: Optional['DebugLogger'] = None
    _file_handler: Optional[logging.FileHandler] = None
    _log_file: Optional[str] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.initialized = True
            self.setup_logging()
    
    def setup_logging(self):
        """Set up file logging for debugging."""
        # Create debug logs directory
        log_dir = "debug_logs"
        os.makedirs(log_dir, exist_ok=True)
        
        # Create timestamped log file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self._log_file = os.path.join(log_dir, f"report_generation_debug_{timestamp}.log")
        
        # Create file handler
        self._file_handler = logging.FileHandler(self._log_file)
        self._file_handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self._file_handler.setFormatter(formatter)
        
        # Add handler to root logger to catch all logs
        root_logger = logging.getLogger()
        root_logger.addHandler(self._file_handler)
        root_logger.setLevel(logging.INFO)
        
        print(f"🔍 Debug logging enabled. Log file: {self._log_file}")
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger with the file handler attached."""
        logger = logging.getLogger(name)
        # Don't add handler to individual loggers since we added it to root logger
        logger.setLevel(logging.INFO)
        return logger
    
    def log_separator(self, title: str):
        """Log a separator with title for better readability."""
        separator = "=" * 80
        logger = self.get_logger("DEBUG_SEPARATOR")
        logger.info(f"\n{separator}")
        logger.info(f"🎯 {title}")
        logger.info(f"{separator}")
    
    def log_request_start(self, original_question: str, task_id: str):
        """Log the start of a report generation request."""
        self.log_separator(f"REPORT GENERATION START - Task ID: {task_id}")
        logger = self.get_logger("REPORT_REQUEST")
        logger.info(f"📝 Original Question: '{original_question}'")
        logger.info(f"🆔 Task ID: {task_id}")
        logger.info(f"⏰ Started at: {datetime.now().isoformat()}")
    
    def log_elasticsearch_state(self):
        """Log the current state of Elasticsearch index."""
        try:
            from app.vectorstore.client import es
            from app.core.config import settings
            
            logger = self.get_logger("ELASTICSEARCH_STATE")
            index_name = settings.elasticsearch_index
            
            # Get total document count
            total_docs = es.count(index=index_name)
            logger.info(f"📊 Total documents in index '{index_name}': {total_docs['count']}")
            
            # Check documents with data_returned=True
            data_returned_query = {
                "query": {
                    "term": {"metadata.data_returned": True}
                }
            }
            data_returned_count = es.count(index=index_name, body=data_returned_query)
            logger.info(f"✅ Documents with data_returned=True: {data_returned_count['count']}")
            
            # Get unique conversation IDs
            conversation_agg = {
                "size": 0,
                "aggs": {
                    "unique_conversations": {
                        "terms": {
                            "field": "metadata.conversation_id.keyword",
                            "size": 10
                        }
                    }
                }
            }
            
            try:
                agg_results = es.search(index=index_name, body=conversation_agg)
                conversations = agg_results['aggregations']['unique_conversations']['buckets']
                logger.info(f"💬 Unique conversation IDs ({len(conversations)}):")
                for conv in conversations:
                    logger.info(f"  - '{conv['key']}' ({conv['doc_count']} docs)")
            except Exception as e:
                logger.warning(f"⚠️ Could not get conversation aggregation: {str(e)}")
                
        except Exception as e:
            logger = self.get_logger("ELASTICSEARCH_STATE")
            logger.error(f"❌ Error checking Elasticsearch state: {str(e)}")
    
    @property
    def log_file_path(self) -> Optional[str]:
        """Get the current log file path."""
        return self._log_file

# Global instance
debug_logger = DebugLogger()

"""
Utility for managing Elasticsearch index creation and recreation.
This handles the case where the index mapping needs to be updated.
"""

import logging
from elasticsearch import Elasticsearch
from app.core.config import settings
from app.vectorstore.schema import get_default_index_mapping

logger = logging.getLogger(__name__)


def recreate_elasticsearch_index(force: bool = False) -> bool:
    """
    Recreate the Elasticsearch index with the correct mapping.
    
    Args:
        force: If True, will delete existing index without confirmation
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create Elasticsearch client
        es = Elasticsearch(
            settings.elasticsearch_url,
            basic_auth=(settings.elasticsearch_user, settings.elasticsearch_password),
            request_timeout=120,  # Increase timeout to 120 seconds for slow connections
            retry_on_timeout=True,  # Retry on timeout
            max_retries=3,  # Maximum number of retries
        )
        
        index_name = settings.elasticsearch_index
        
        # Check if index exists
        if es.indices.exists(index=index_name):
            logger.info(f"Index '{index_name}' exists. Checking mapping...")
            
            # Get current mapping
            current_mapping = es.indices.get_mapping(index=index_name)
            current_properties = current_mapping[index_name]["mappings"].get("properties", {})
            
            # Check if vector field exists
            if "vector" not in current_properties:
                logger.warning(f"Index '{index_name}' missing vector field mapping. Recreation needed.")
                
                if not force:
                    logger.warning("Use force=True to recreate the index (this will delete all existing data)")
                    return False
                
                # Delete existing index
                logger.info(f"Deleting existing index '{index_name}'...")
                es.indices.delete(index=index_name)
                logger.info(f"Index '{index_name}' deleted successfully")
            else:
                logger.info(f"Index '{index_name}' already has correct mapping")
                return True
        
        # Create index with correct mapping
        logger.info(f"Creating index '{index_name}' with vector field mapping...")
        mapping = get_default_index_mapping()
        
        es.indices.create(
            index=index_name,
            body=mapping
        )
        
        logger.info(f"Index '{index_name}' created successfully with vector field mapping")
        return True
        
    except Exception as e:
        logger.error(f"Error recreating Elasticsearch index: {str(e)}")
        return False


def check_index_mapping() -> dict:
    """
    Check the current index mapping and return status information.
    
    Returns:
        dict: Status information about the index mapping
    """
    try:
        es = Elasticsearch(
            settings.elasticsearch_url,
            basic_auth=(settings.elasticsearch_user, settings.elasticsearch_password),
            request_timeout=120,  # Increase timeout to 120 seconds for slow connections
            retry_on_timeout=True,  # Retry on timeout
            max_retries=3,  # Maximum number of retries
        )
        
        index_name = settings.elasticsearch_index
        
        if not es.indices.exists(index=index_name):
            return {
                "exists": False,
                "has_vector_field": False,
                "message": f"Index '{index_name}' does not exist"
            }
        
        # Get current mapping
        current_mapping = es.indices.get_mapping(index=index_name)
        current_properties = current_mapping[index_name]["mappings"].get("properties", {})
        
        has_vector_field = "vector" in current_properties
        
        return {
            "exists": True,
            "has_vector_field": has_vector_field,
            "properties": list(current_properties.keys()),
            "message": f"Index exists. Vector field: {'present' if has_vector_field else 'missing'}"
        }
        
    except Exception as e:
        return {
            "exists": False,
            "has_vector_field": False,
            "error": str(e),
            "message": f"Error checking index: {str(e)}"
        }


if __name__ == "__main__":
    # Check current status
    status = check_index_mapping()
    print(f"Index status: {status}")
    
    # Recreate if needed
    if not status.get("has_vector_field", False):
        print("Recreating index with vector field mapping...")
        success = recreate_elasticsearch_index(force=True)
        if success:
            print("Index recreated successfully!")
        else:
            print("Failed to recreate index")
    else:
        print("Index already has correct mapping")

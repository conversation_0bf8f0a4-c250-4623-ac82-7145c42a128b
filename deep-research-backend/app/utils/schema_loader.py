from pathlib import Path

def get_schema_file(filename: str) -> str:
    """
    Returns the contents of a schema file located in the schema_files directory
    (which is expected to be one level above this script).
    """
    base_dir = Path(__file__).resolve().parent.parent  # e.g., `project/`
    schema_path = base_dir / "schema_files" / filename

    if not schema_path.exists():
        raise FileNotFoundError(f"Schema file not found: {schema_path}")

    return schema_path.read_text(encoding="utf-8")
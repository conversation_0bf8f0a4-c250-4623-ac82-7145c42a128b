def get_default_index_mapping() -> dict:
    return {
        "mappings": {
            "properties": {
                "page_content": {"type": "text"},
                "vector": {
                    "type": "dense_vector",
                    "dims": 1536,  # OpenAI text-embedding-3-small has 1536 dimensions
                    "index": True,
                    "similarity": "cosine"
                },
                "metadata": {
                    "properties": {
                        "conversation_id": {
                            "type": "text",
                            "fields": {"keyword": {"type": "keyword"}}
                        },
                        "partition": {"type": "keyword"},
                        "turn_index": {"type": "integer"},
                        "timestamp": {"type": "date"},
                        "data_returned": {"type": "boolean"},
                        "data_tag": {"type": "text"}
                    }
                }
            }
        }
    }

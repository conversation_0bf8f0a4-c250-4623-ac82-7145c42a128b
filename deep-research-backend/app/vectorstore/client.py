from elasticsearch import Elasticsearch
from langchain_elasticsearch import ElasticsearchStore
from app.core.config import settings
from app.vectorstore.schema import get_default_index_mapping
from app.llm.embeddings import embedding  # from new llm/embeddings.py
from app.llm.model import model           # from new llm/model.py
import logging

logger = logging.getLogger(__name__)

# ─── Elasticsearch Raw Client ────────────────────────────────
es = Elasticsearch(
    settings.elasticsearch_url,
    basic_auth=(settings.elasticsearch_user, settings.elasticsearch_password),
    request_timeout=120,  # Increase timeout to 120 seconds for slow connections
    retry_on_timeout=True,  # Retry on timeout
    max_retries=3,  # Maximum number of retries
)

# ─── Create index with mapping if it doesn't exist or check if it has vector field ───────────
def ensure_index_with_vector_mapping():
    """Ensure the index exists with the correct vector field mapping."""
    index_name = settings.elasticsearch_index

    if not es.indices.exists(index=index_name):
        logger.info(f"Creating new index '{index_name}' with vector field mapping...")
        es.indices.create(
            index=index_name,
            body=get_default_index_mapping()
        )
        logger.info(f"Index '{index_name}' created successfully")
    else:
        # Check if the existing index has the vector field
        try:
            current_mapping = es.indices.get_mapping(index=index_name)
            current_properties = current_mapping[index_name]["mappings"].get("properties", {})

            if "vector" not in current_properties:
                logger.warning(f"Index '{index_name}' exists but missing vector field mapping!")
                logger.warning("The index needs to be recreated with the correct mapping.")
                logger.warning("Use app/utils/elasticsearch_index_manager.py to recreate the index.")
                logger.warning("WARNING: This will delete all existing data in the index!")
            else:
                logger.info(f"Index '{index_name}' exists with correct vector field mapping")
        except Exception as e:
            logger.error(f"Error checking index mapping: {str(e)}")

# Initialize the index
ensure_index_with_vector_mapping()

# ─── LangChain Vector Store ──────────────────────────────────
elastic_vector_search = ElasticsearchStore(
    es_url=settings.elasticsearch_url,
    index_name=settings.elasticsearch_index,
    embedding=embedding,
    es_user=settings.elasticsearch_user,
    es_password=settings.elasticsearch_password,
    es_params={"request_timeout": 120},  # Increase timeout for operations
)

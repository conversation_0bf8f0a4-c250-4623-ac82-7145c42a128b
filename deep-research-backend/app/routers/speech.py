"""
Speech API endpoints for STT and TTS functionality.

This module provides REST API endpoints for speech-to-text and text-to-speech
operations using the speech service layer.
"""

import io
import logging
from typing import Optional, Dict, Any
from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from ..speech.service import get_speech_service
from ..speech.base import SpeechProviderError, TTSProviderError, STTProviderError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/speech", tags=["Speech"])


class TTSRequest(BaseModel):
    """Request model for text-to-speech conversion."""
    text: str
    voice_id: Optional[str] = None
    voice_settings: Optional[Dict[str, Any]] = None
    output_format: Optional[str] = "mp3_22050_32"
    model_id: Optional[str] = None


class STTResponse(BaseModel):
    """Response model for speech-to-text conversion."""
    text: str
    confidence: Optional[float] = None
    language: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@router.post("/tts", response_class=StreamingResponse)
async def text_to_speech(request: TTSRequest):
    """
    Convert text to speech.
    
    Args:
        request: TTSRequest containing text and configuration
        
    Returns:
        StreamingResponse with audio data
        
    Raises:
        HTTPException: If TTS conversion fails
    """
    try:
        speech_service = get_speech_service()
        
        logger.info(f"Converting text to speech: {request.text[:50]}...")
        
        response = await speech_service.text_to_speech(
            text=request.text,
            voice_id=request.voice_id,
            voice_settings=request.voice_settings,
            output_format=request.output_format,
            model_id=request.model_id
        )
        
        return response
        
    except TTSProviderError as e:
        logger.error(f"TTS provider error: {str(e)}")
        raise HTTPException(status_code=400, detail=f"TTS error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected TTS error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/stt", response_model=STTResponse)
async def speech_to_text(
    audio_file: UploadFile = File(...),
    language_code: Optional[str] = Form(None),
    model_id: Optional[str] = Form(None),
    tag_audio_events: bool = Form(True),
    diarize: bool = Form(False)
):
    """
    Convert speech to text.
    
    Args:
        audio_file: Audio file to transcribe
        language_code: Language code for transcription (e.g., 'eng', 'spa')
        model_id: Model ID to use for transcription
        tag_audio_events: Whether to tag audio events like laughter
        diarize: Whether to perform speaker diarization
        
    Returns:
        STTResponse with transcribed text and metadata
        
    Raises:
        HTTPException: If STT conversion fails
    """
    try:
        speech_service = get_speech_service()
        
        # Validate file type
        if not audio_file.content_type or not audio_file.content_type.startswith('audio/'):
            raise HTTPException(
                status_code=400, 
                detail="Invalid file type. Please upload an audio file."
            )
        
        logger.info(f"Converting speech to text: {audio_file.filename}")
        
        # Read audio file content
        audio_content = await audio_file.read()
        audio_io = io.BytesIO(audio_content)
        
        response = await speech_service.speech_to_text(
            audio_file=audio_io,
            language_code=language_code,
            model_id=model_id,
            tag_audio_events=tag_audio_events,
            diarize=diarize
        )
        
        return response
        
    except STTProviderError as e:
        logger.error(f"STT provider error: {str(e)}")
        raise HTTPException(status_code=400, detail=f"STT error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected STT error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/voices")
async def get_available_voices():
    """
    Get available voices for text-to-speech.
    
    Returns:
        Dictionary containing voice information
        
    Raises:
        HTTPException: If unable to fetch voices
    """
    try:
        speech_service = get_speech_service()
        voices = speech_service.get_available_voices()
        
        logger.info(f"Retrieved {len(voices.get('voices', []))} available voices")
        
        return voices
        
    except Exception as e:
        logger.error(f"Error fetching voices: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching voices: {str(e)}")


@router.get("/formats")
async def get_supported_formats():
    """
    Get supported audio formats for speech-to-text.
    
    Returns:
        Dictionary with supported formats
        
    Raises:
        HTTPException: If unable to fetch formats
    """
    try:
        speech_service = get_speech_service()
        formats = speech_service.get_supported_formats()
        
        return {
            "supported_formats": formats,
            "description": "Supported audio formats for speech-to-text conversion"
        }
        
    except Exception as e:
        logger.error(f"Error fetching supported formats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching formats: {str(e)}")


@router.get("/provider-info")
async def get_provider_info():
    """
    Get information about the current speech provider.
    
    Returns:
        Dictionary with provider information
        
    Raises:
        HTTPException: If unable to fetch provider info
    """
    try:
        speech_service = get_speech_service()
        info = speech_service.get_provider_info()
        
        return info
        
    except Exception as e:
        logger.error(f"Error fetching provider info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching provider info: {str(e)}")


@router.get("/health")
async def speech_health_check():
    """
    Health check endpoint for speech services.
    
    Returns:
        Health status of speech services
    """
    try:
        speech_service = get_speech_service()
        provider_info = speech_service.get_provider_info()
        
        return {
            "status": "healthy",
            "provider": provider_info["provider_type"],
            "tts_available": provider_info["tts_available"],
            "stt_available": provider_info["stt_available"],
            "message": "Speech services are operational"
        }
        
    except Exception as e:
        logger.error(f"Speech health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "Speech services are not available"
        }

from app.models.report import ReportGenerationRequest
from app.tasks.report_task import generate_report_task, generate_streaming_report_task
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import StreamingResponse
import redis.asyncio as aioredis
from app.core.config import settings
from uuid import uuid4
from pydantic import ValidationError
from fastapi.responses import JSONResponse

import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/report", tags=["Report"])


@router.post("/generate")
def generate_report(req: ReportGenerationRequest):
    try:
        task_id = str(uuid4())
        
        logger.info("starting...")

        # We pass both the task_id and the actual input to the Celery worker
        generate_report_task.delay({
            "task_id": task_id,
            "request": req.model_dump()
        })

        return {"task_id": task_id}
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=f"Validation error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/generate-streaming")
def generate_streaming_report(req: ReportGenerationRequest):
    """
    New streaming endpoint that processes figures and replaces placeholders
    in real-time as each section completes.
    """
    try:
        task_id = str(uuid4())

        logger.info("starting streaming report generation...")

        # Use the new streaming task
        generate_streaming_report_task.delay({
            "task_id": task_id,
            "request": req.model_dump()
        })

        return {"task_id": task_id}
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=f"Validation error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/stream/{task_id}")
async def stream_report_status(task_id: str, request: Request):
    redis = aioredis.from_url(settings.redis_backend_url)
    pubsub = redis.pubsub()
    await pubsub.subscribe(f"report:progress:{task_id}")

    async def event_generator():
        try:
            async for message in pubsub.listen():
                if await request.is_disconnected():
                    break

                if message["type"] != "message":
                    continue

                data = message["data"]
                if isinstance(data, bytes):
                    data = data.decode("utf-8")

                yield f"data: {data}\n\n"

                if '"status": "complete"' in data or '"status": "error"' in data:
                    break

        finally:
            await pubsub.unsubscribe(f"report:progress:{task_id}")
            await pubsub.close()

    return StreamingResponse(event_generator(), media_type="text/event-stream")



@router.get("/status/{task_id}")
async def get_report_status(task_id: str):
    try:
        redis = aioredis.from_url(settings.redis_backend_url)
        key = f"report:progress:{task_id}:latest"

        result = await redis.get(key)
        if result is None:
            raise HTTPException(status_code=404, detail="Task not found or no progress yet")

        if isinstance(result, bytes):
            result = result.decode("utf-8")

        return JSONResponse(content={"task_id": task_id, "status": result})

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching task status: {str(e)}")

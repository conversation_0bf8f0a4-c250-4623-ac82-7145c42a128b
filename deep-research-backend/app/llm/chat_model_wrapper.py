from langchain_core.runnables import Runnable
from langchain_core.messages import BaseMessage
from typing import List, Dict, Any, Union
import tiktoken

class ChatModelWrapper(Runnable):
    _total_cost = 0.0  # Shared across all instances
    _total_tokens = 0
    _cost_by_model = {}
    _input_tokens_by_model = {}
    _output_tokens_by_model = {}

    def __init__(self, model, model_name: str, model_costs: Dict[str, Dict[str, float]]):
        self.model = model
        self.model_name = model_name
        cost_info = model_costs.get(model_name)

        if cost_info is None or not all(k in cost_info for k in ["input", "output"]):
            raise ValueError(f"Model cost info must include 'input' and 'output' for '{model_name}'.")

        self.input_cost_per_1k = cost_info["input"]
        self.output_cost_per_1k = cost_info["output"]

        try:
            self.token_encoder = tiktoken.encoding_for_model(model_name)
        except KeyError:
            self.token_encoder = tiktoken.get_encoding("cl100k_base")

    def _count_tokens(self, messages: Union[List[BaseMessage], Any], output: BaseMessage) -> Dict[str, int]:
        def encode(text):
            return len(self.token_encoder.encode(text))

        input_tokens = 0
        output_tokens = 0

        if hasattr(messages, "messages"):
            messages = messages.messages

        if isinstance(messages, list):
            for msg in messages:
                if hasattr(msg, "content") and isinstance(msg.content, str):
                    input_tokens += encode(msg.content)
        elif hasattr(messages, "content") and isinstance(messages.content, str):
            input_tokens += encode(messages.content)

        if hasattr(output, "content") and isinstance(output.content, str):
            output_tokens += encode(output.content)

        return {"input": input_tokens, "output": output_tokens}

    def invoke(self, input: Any, config: Any = None, **kwargs) -> BaseMessage:
        result = self.model.invoke(input, config=config, **kwargs)
        token_counts = self._count_tokens(input, result)
        self._update_cost(token_counts)
        return result

    def batch(self, inputs: List[Any], config: Any = None, **kwargs) -> List[BaseMessage]:
        results = self.model.batch(inputs, config=config, **kwargs)
        for inp, out in zip(inputs, results):
            token_counts = self._count_tokens(inp, out)
            self._update_cost(token_counts)
        return results

    def _update_cost(self, token_counts: Dict[str, int]):
        input_tokens = token_counts["input"]
        output_tokens = token_counts["output"]

        cost = (
            (input_tokens / 1000) * self.input_cost_per_1k +
            (output_tokens / 1000) * self.output_cost_per_1k
        )

        ChatModelWrapper._total_cost += cost
        ChatModelWrapper._total_tokens += input_tokens + output_tokens

        # Track by model
        ChatModelWrapper._cost_by_model.setdefault(self.model_name, 0)
        ChatModelWrapper._cost_by_model[self.model_name] += cost

        ChatModelWrapper._input_tokens_by_model.setdefault(self.model_name, 0)
        ChatModelWrapper._input_tokens_by_model[self.model_name] += input_tokens

        ChatModelWrapper._output_tokens_by_model.setdefault(self.model_name, 0)
        ChatModelWrapper._output_tokens_by_model[self.model_name] += output_tokens

    def with_structured_output(self, schema):
        wrapped = self.model.with_structured_output(schema)
        return ChatModelWrapper(
            wrapped,
            self.model_name,
            {
                self.model_name: {
                    "input": self.input_cost_per_1k,
                    "output": self.output_cost_per_1k
                }
            }
        )

    @classmethod
    def get_total_cost(cls) -> float:
        return cls._total_cost

    @classmethod
    def get_total_tokens(cls) -> int:
        return cls._total_tokens

    @classmethod
    def get_cost_by_model(cls) -> Dict[str, float]:
        return cls._cost_by_model

    @classmethod
    def get_input_tokens_by_model(cls) -> Dict[str, int]:
        return cls._input_tokens_by_model

    @classmethod
    def get_output_tokens_by_model(cls) -> Dict[str, int]:
        return cls._output_tokens_by_model

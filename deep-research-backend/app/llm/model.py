from langchain_openai import ChatOpenAI
from app.core.config import settings
from app.llm.chat_model_wrapper import ChatModelWrapper

model_costs = {
    "gpt-4o": {"input": 0.0025, "output": 0.01},
    "gpt-4o-mini": {"input": 0.00015, "output": 0.0006},
    "gpt-4.1": {"input": 0.002, "output": 0.008},
    "gpt-4.1-mini": {"input": 0.0004, "output": 0.0016},
    "gpt-4.1-nano": {"input": 0.0001, "output": 0.0004},
}

model_name = "gpt-4o-mini"

llm = ChatOpenAI(
    model_name=model_name,
    temperature=0,
    api_key=settings.api_key
)

model = ChatModelWrapper(llm, model_name, model_costs)

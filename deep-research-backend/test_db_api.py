#!/usr/bin/env python3
"""
Test script to check if the database API is working correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_db_connection():
    """Test basic database connection."""
    print("🔍 Testing database API connection...")
    
    try:
        from app.core.db_api import run_query_silent
        from app.core.config import settings
        
        print(f"📡 Database API URL: {settings.db_query_api_url}")
        
        # Test with a simple query
        test_query = "SELECT COUNT(*) as total_count FROM students LIMIT 1;"
        print(f"🔎 Test query: {test_query}")
        
        result = run_query_silent(test_query)
        print(f"📊 Query result: {result}")
        
        if result.get("data"):
            print("✅ Database API is working!")
            return True
        else:
            print("❌ Database API returned no data")
            return False
            
    except Exception as e:
        print(f"❌ Database API error: {str(e)}")
        return False

def test_sql_generation():
    """Test SQL generation for a simple question."""
    print("\n🔍 Testing SQL generation...")
    
    try:
        from app.chains.composite.sql_generation_and_verification import sql_gen_veri_chain
        
        test_question = "How many students are there?"
        print(f"🔎 Test question: '{test_question}'")
        
        result = sql_gen_veri_chain.invoke(test_question)
        print(f"📊 SQL generation result: {result}")
        
        if result.get("correct", False):
            print("✅ SQL generation successful!")
            print(f"📝 Generated SQL: {result.get('sql', 'No SQL')}")
            return result
        else:
            print("❌ SQL generation failed or marked as incorrect")
            print(f"🚨 Feedback: {result.get('feedback', 'No feedback')}")
            return result
            
    except Exception as e:
        print(f"❌ SQL generation error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_sql_execution(sql_dict):
    """Test SQL execution."""
    print("\n🔍 Testing SQL execution...")
    
    if not sql_dict or not sql_dict.get("sql"):
        print("❌ No valid SQL to execute")
        return None
    
    try:
        from app.chains.sql_runner import sql_runner_chain
        
        print(f"🔎 Executing SQL: {sql_dict['sql']}")
        
        result = sql_runner_chain.invoke(sql_dict)
        print(f"📊 SQL execution result: {result}")
        
        if result.get("result") != "No results":
            print("✅ SQL execution successful!")
            return result
        else:
            print("❌ SQL execution returned 'No results'")
            if "error" in result:
                print(f"🚨 Error: {result['error']}")
            return result
            
    except Exception as e:
        print(f"❌ SQL execution error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_full_db_expert_chain():
    """Test the full database expert chain."""
    print("\n🔍 Testing full database expert chain...")
    
    try:
        from app.chains.composite.db_expert import db_expert_chain
        
        test_question = "How many students are there?"
        print(f"🔎 Test question: '{test_question}'")
        
        result = db_expert_chain.invoke(test_question)
        print(f"📊 DB expert result: {result}")
        
        if result.get("data") != "No results":
            print("✅ Database expert chain successful!")
            print(f"💬 Answer: {result.get('answer', 'No answer')}")
            print(f"📈 Data: {result.get('data', 'No data')}")
            return result
        else:
            print("❌ Database expert chain returned 'No results'")
            return result
            
    except Exception as e:
        print(f"❌ Database expert chain error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Run all tests."""
    print("🚀 Starting database API debug tests...\n")
    
    # Test 1: Database connection
    db_working = test_db_connection()
    
    if not db_working:
        print("\n❌ Database API is not working. Cannot proceed with other tests.")
        return
    
    # Test 2: SQL generation
    sql_result = test_sql_generation()
    
    # Test 3: SQL execution (if generation worked)
    if sql_result and sql_result.get("correct"):
        execution_result = test_sql_execution(sql_result)
    else:
        print("\n⚠️ Skipping SQL execution test due to generation failure")
        execution_result = None
    
    # Test 4: Full chain
    full_chain_result = test_full_db_expert_chain()
    
    print(f"\n📊 Test Summary:")
    print(f"  - Database API: {'✅' if db_working else '❌'}")
    print(f"  - SQL Generation: {'✅' if sql_result and sql_result.get('correct') else '❌'}")
    print(f"  - SQL Execution: {'✅' if execution_result and execution_result.get('result') != 'No results' else '❌'}")
    print(f"  - Full Chain: {'✅' if full_chain_result and full_chain_result.get('data') != 'No results' else '❌'}")

if __name__ == "__main__":
    main()

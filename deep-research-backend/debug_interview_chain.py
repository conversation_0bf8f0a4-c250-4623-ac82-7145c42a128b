#!/usr/bin/env python3
"""
Debug script to isolate and test the interview chain to identify why no documents are being generated.
"""

import sys
import os
import json
import traceback

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_database_connection():
    """Test if the database API is accessible"""
    print("🔍 Testing database connection...")
    
    try:
        from core.db_api import run_query_silent
        from core.config import settings
        
        print(f"📡 Database API URL: {settings.db_query_api_url}")
        
        # Test with a simple query
        simple_query = "SELECT 1 as test_value"
        result = run_query_silent(simple_query)
        print(f"✅ Database connection successful!")
        print(f"📊 Test query result: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")
        traceback.print_exc()
        return False


def test_sql_generation():
    """Test SQL generation and verification chain"""
    print("\n🔍 Testing SQL generation and verification...")
    
    try:
        from chains.composite.sql_generation_and_verification import sql_gen_veri_chain
        
        test_question = "How many students are there?"
        print(f"🔎 Test question: '{test_question}'")
        
        result = sql_gen_veri_chain.invoke(test_question)
        print(f"📊 SQL generation result:")
        print(json.dumps(result, indent=2, default=str))

        # Debug: Check what keys are actually in the result
        print(f"🔍 Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        print(f"🔍 Result type: {type(result)}")

        if result.get("correct", False):
            print("✅ SQL generation successful!")
            return result
        else:
            print("❌ SQL generation failed or marked as incorrect")
            return result
            
    except Exception as e:
        print(f"❌ SQL generation error: {str(e)}")
        traceback.print_exc()
        return None


def test_sql_runner(sql_dict):
    """Test SQL runner with a generated SQL"""
    print("\n🔍 Testing SQL runner...")
    
    try:
        from chains.sql_runner import sql_runner_chain
        
        print(f"🔎 Testing SQL: {sql_dict.get('sql', 'No SQL provided')}")
        
        result = sql_runner_chain.invoke(sql_dict)
        print(f"📊 SQL runner result:")
        print(json.dumps(result, indent=2, default=str))
        
        if result.get("result") != "No results":
            print("✅ SQL execution successful!")
            return result
        else:
            print("❌ SQL execution returned 'No results'")
            if "error" in result:
                print(f"🚨 Error: {result['error']}")
            return result
            
    except Exception as e:
        print(f"❌ SQL runner error: {str(e)}")
        traceback.print_exc()
        return None


def test_interview_single_question():
    """Test a single interview question end-to-end"""
    print("\n🔍 Testing single interview question...")
    
    try:
        from chains.composite.interview import interview_chain
        
        test_question = "How many students are there?"
        print(f"🔎 Test question: '{test_question}'")
        
        result = interview_chain.invoke(test_question)
        print(f"📊 Interview result:")
        print(json.dumps(result, indent=2, default=str))
        
        # Check if data was generated
        data_dict = result.get("data", {})
        convo_with_data = result.get("convo_with_data", [])
        
        print(f"\n📈 Analysis:")
        print(f"  - Data objects generated: {len(data_dict)}")
        print(f"  - Conversations with data: {len(convo_with_data)}")
        print(f"  - Total conversations: {len(result.get('convo', []))}")
        
        if len(data_dict) > 0:
            print("✅ Interview generated data successfully!")
            print(f"📊 Data keys: {list(data_dict.keys())}")
        else:
            print("❌ Interview did not generate any data")
            
        return result
        
    except Exception as e:
        print(f"❌ Interview error: {str(e)}")
        traceback.print_exc()
        return None


def test_batch_interview():
    """Test batch interview process"""
    print("\n🔍 Testing batch interview...")
    
    try:
        from chains.composite.interview import batch_interview
        
        test_questions = [
            "How many students are there?",
            "What is the gender distribution of students?"
        ]
        print(f"🔎 Test questions: {test_questions}")
        
        result = batch_interview(test_questions)
        print(f"📊 Batch interview result:")
        print(json.dumps(result, indent=2, default=str))
        
        interviews = result.get("interviews", [])
        total_data_objects = 0
        
        for i, interview in enumerate(interviews):
            data_count = len(interview.get("data", {}))
            total_data_objects += data_count
            print(f"  - Interview {i+1}: {data_count} data objects")
        
        print(f"\n📈 Total data objects across all interviews: {total_data_objects}")
        
        if total_data_objects > 0:
            print("✅ Batch interview generated data successfully!")
        else:
            print("❌ Batch interview did not generate any data")
            
        return result
        
    except Exception as e:
        print(f"❌ Batch interview error: {str(e)}")
        traceback.print_exc()
        return None


def test_document_generation():
    """Test document generation from interview results"""
    print("\n🔍 Testing document generation...")
    
    try:
        from helpers import interview_to_docs, extract_q_a_with_data, extract_q_a_without_data
        
        # First run a batch interview
        batch_result = test_batch_interview()
        if not batch_result:
            print("❌ Cannot test document generation without interview results")
            return None
        
        interviews = batch_result.get("interviews", [])
        if not interviews:
            print("❌ No interviews to process")
            return None
        
        print(f"🔄 Processing {len(interviews)} interviews for document generation...")
        
        all_docs = []
        data_backed_convos = extract_q_a_with_data(batch_result)
        data_lacking_convos = extract_q_a_without_data(batch_result)
        
        print(f"📊 Conversations with data: {len(data_backed_convos)}")
        print(f"📊 Conversations without data: {len(data_lacking_convos)}")
        
        for i, interview in enumerate(interviews):
            docs = interview_to_docs(interview, f"test_question_{i}")
            all_docs.extend(docs)
            print(f"  - Interview {i+1}: Generated {len(docs)} documents")
        
        print(f"\n📈 Total documents generated: {len(all_docs)}")
        
        if len(all_docs) > 0:
            print("✅ Document generation successful!")
            # Print first document as example
            if all_docs:
                print(f"📄 Example document:")
                print(f"  Content: {all_docs[0].page_content[:200]}...")
                print(f"  Metadata: {all_docs[0].metadata}")
        else:
            print("❌ No documents were generated")
            
        return all_docs
        
    except Exception as e:
        print(f"❌ Document generation error: {str(e)}")
        traceback.print_exc()
        return None


def main():
    """Run all debug tests"""
    print("🧪 Starting interview chain debugging...\n")
    
    # Test 1: Database connection
    db_ok = test_database_connection()
    
    # Test 2: SQL generation
    sql_result = test_sql_generation()
    
    # Test 3: SQL runner (if SQL was generated)
    if sql_result and sql_result.get("correct", False):
        sql_runner_result = test_sql_runner(sql_result)
    else:
        print("\n⏭️ Skipping SQL runner test (no valid SQL generated)")
        sql_runner_result = None
    
    # Test 4: Single interview
    interview_result = test_interview_single_question()
    
    # Test 5: Batch interview
    batch_result = test_batch_interview()
    
    # Test 6: Document generation
    docs = test_document_generation()
    
    # Summary
    print("\n" + "="*60)
    print("🎯 DEBUGGING SUMMARY")
    print("="*60)
    print(f"✅ Database Connection: {'PASS' if db_ok else 'FAIL'}")
    print(f"✅ SQL Generation: {'PASS' if sql_result and sql_result.get('correct') else 'FAIL'}")
    print(f"✅ SQL Execution: {'PASS' if sql_runner_result and sql_runner_result.get('result') != 'No results' else 'FAIL'}")
    print(f"✅ Single Interview: {'PASS' if interview_result and len(interview_result.get('data', {})) > 0 else 'FAIL'}")
    print(f"✅ Batch Interview: {'PASS' if batch_result and any(len(i.get('data', {})) > 0 for i in batch_result.get('interviews', [])) else 'FAIL'}")
    print(f"✅ Document Generation: {'PASS' if docs and len(docs) > 0 else 'FAIL'}")
    
    # Identify the root cause
    print("\n🔍 ROOT CAUSE ANALYSIS:")
    if not db_ok:
        print("❌ PRIMARY ISSUE: Database connection is failing")
        print("   - Check if the database API is running")
        print("   - Verify the API URL in settings")
        print("   - Check network connectivity from Celery worker")
    elif not sql_result or not sql_result.get("correct"):
        print("❌ PRIMARY ISSUE: SQL generation/verification is failing")
        print("   - Check if the schema file is accessible")
        print("   - Verify LLM connectivity")
        print("   - Check question answerability logic")
    elif not sql_runner_result or sql_runner_result.get("result") == "No results":
        print("❌ PRIMARY ISSUE: SQL execution is failing")
        print("   - Check database permissions")
        print("   - Verify SQL syntax")
        print("   - Check for database connectivity issues")
    else:
        print("✅ All components seem to be working individually")
        print("   - The issue might be in the integration or environment")


if __name__ == "__main__":
    main()

# Streaming Report Generation Implementation

This document describes the two distinct report generation flows implemented in the system.

## 📋 Overview

The system now supports two separate report generation approaches:

1. **Batch Processing** (Original) - Fast section streaming with post-processing
2. **Real-time Processing** (New) - Fully processed sections with embedded figures

## 🔄 Implementation 1: Batch Processing (Original)

### Endpoint
```
POST /report/generate
```

### Behavior
- **Sections**: Streamed individually with raw `[[[tag]]]` placeholders
- **Figure Processing**: Happens only at the end in the `"complete"` event
- **Speed**: Faster initial section delivery
- **Use Case**: When you want quick section previews and can wait for final processing

### Streaming Events
```json
{"status": "section_complete", "section_index": 0, "section_title": "# Executive Summary", "section_content": "Analysis shows [[[student_count]]] trends..."}
{"status": "section_complete", "section_index": 1, "section_title": "## Demographics", "section_content": "The data reveals [[[gender_distribution]]]..."}
{"status": "complete", "result": {"sections": ["Figure 1 – Student Count", "Analysis shows {chart_data} trends...", "## List of Figures"]}}
```

### Implementation Details
- Uses `generate_full_report()` function
- Processes all sections with `section_writer_chain.batch()`
- Calls `process_report_figures()` at the end
- Replaces placeholders with data objects in final result

## ⚡ Implementation 2: Real-time Processing (New)

### Endpoint
```
POST /report/generate-streaming
```

### Behavior
- **Sections**: Streamed with processed figure references and embedded data
- **Figure Processing**: Happens in real-time as each section completes
- **Speed**: Slightly slower per section but immediately usable
- **Use Case**: When you want fully formatted sections immediately

### Streaming Events
```json
{"status": "section_complete", "section_index": 0, "section_title": "# Executive Summary", "section_content": "Analysis shows\n\nFigure 1 – Student Count\n{chart_data}\n\ntrends...", "figures_in_section": [{"figure_number": 1, "title": "Student Count", "type": "chart"}], "total_figures_so_far": 1}
{"status": "section_complete", "section_index": 1, "section_title": "## Demographics", "section_content": "The data reveals\n\nFigure 2 – Gender Distribution\n{chart_data}\n\npatterns. As shown in (See Figure 1)...", "figures_in_section": [{"figure_number": 2, "title": "Gender Distribution", "type": "chart"}], "total_figures_so_far": 2}
{"status": "section_complete", "section_index": 2, "section_title": "List of Figures", "section_content": "\n\n## List of Figures\n* **Figure 1** – Student Count\n* **Figure 2** – Gender Distribution", "figures_in_section": [], "total_figures_so_far": 2}
{"status": "complete", "result": {"sections": [...], "total_figures": 2, "figures_list": ["student_count", "gender_distribution"]}}
```

### Implementation Details
- Uses `generate_streaming_report()` function
- Processes sections individually with `section_writer_chain.invoke()`
- Calls `process_section_figures_streaming()` for each section
- Maintains figure numbering across sections
- Generates "List of Figures" as final section

## 🔧 Key Functions

### Streaming Helper Functions

#### `process_section_figures_streaming()`
```python
def process_section_figures_streaming(section_content: str, extended_data: Dict[str, Any], 
                                     fig_map: OrderedDict[str, int], fig_counter: int) -> tuple[str, OrderedDict[str, int], int]:
```
- Processes figures in a single section
- Replaces `[[[tag]]]` with figure references and data
- Maintains figure numbering across sections
- Returns updated content, figure map, and counter

#### `extract_figures_from_section()`
```python
def extract_figures_from_section(section_content: str) -> List[Dict[str, Any]]:
```
- Extracts figure metadata from processed sections
- Returns list of figure information for streaming events

#### `generate_list_of_figures()`
```python
def generate_list_of_figures(fig_map: OrderedDict[str, int]) -> str:
```
- Generates the final "List of Figures" section
- Uses accumulated figure map from all sections

## 📊 Comparison

| Feature | Batch Processing | Real-time Processing |
|---------|------------------|---------------------|
| **Section Delivery** | Fast (raw content) | Moderate (processed) |
| **Figure Processing** | End only | Real-time |
| **Figure References** | Final result only | Immediate |
| **Cross-references** | Final result only | Immediate |
| **Data Objects** | Final result only | Immediate |
| **List of Figures** | Final result only | Streamed as section |
| **Memory Usage** | Lower during streaming | Higher during streaming |
| **Client Complexity** | Higher (must wait for final) | Lower (immediate use) |

## 🚀 Usage Examples

### Batch Processing Client
```javascript
// Start report generation
const response = await fetch('/report/generate', {
    method: 'POST',
    body: JSON.stringify({original_question: "How many students are there?"})
});
const {task_id} = await response.json();

// Stream progress
const eventSource = new EventSource(`/report/stream/${task_id}`);
eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    
    if (data.status === 'section_complete') {
        // Display raw section with placeholders
        displayRawSection(data.section_content);
    } else if (data.status === 'complete') {
        // Replace with fully processed report
        displayFinalReport(data.result.sections);
    }
};
```

### Real-time Processing Client
```javascript
// Start streaming report generation
const response = await fetch('/report/generate-streaming', {
    method: 'POST',
    body: JSON.stringify({original_question: "How many students are there?"})
});
const {task_id} = await response.json();

// Stream progress
const eventSource = new EventSource(`/report/stream/${task_id}`);
eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    
    if (data.status === 'section_complete') {
        // Display fully processed section immediately
        displayProcessedSection(data.section_content, data.figures_in_section);
    } else if (data.status === 'complete') {
        // Final summary
        displayCompletionSummary(data.total_figures, data.figures_list);
    }
};
```

## 🎯 Recommendations

### Use Batch Processing When:
- You want the fastest possible section delivery
- Your client can handle placeholder replacement
- You're building a preview/draft interface
- Network bandwidth is limited

### Use Real-time Processing When:
- You want immediately usable sections
- Your client needs figure references right away
- You're building a live report viewer
- User experience is prioritized over speed

## 🔄 Shared Components

Both implementations share:
- Same underlying report generation pipeline
- Same progress tracking structure (`/report/stream/{task_id}`)
- Same request/response models
- Same error handling and logging
- Same vector search and document processing

The only differences are in figure processing timing and streaming event content.

#!/usr/bin/env python3
"""
Test script to verify CORS configuration is working correctly.
"""

import requests
import json

def test_cors_configuration():
    """Test CORS configuration with the backend."""
    base_url = "http://localhost:8000"
    
    print("🔍 Testing CORS configuration...")
    
    # Test 1: Health check endpoint
    print("\n📋 Test 1: Health check endpoint")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"✅ Health check: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ Health check failed: {str(e)}")
        return False
    
    # Test 2: CORS preflight request
    print("\n📋 Test 2: CORS preflight request")
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        response = requests.options(f"{base_url}/report/generate-streaming", headers=headers)
        print(f"✅ CORS preflight: {response.status_code}")
        print(f"   CORS headers: {dict(response.headers)}")
    except Exception as e:
        print(f"❌ CORS preflight failed: {str(e)}")
        return False
    
    # Test 3: Actual POST request with CORS headers
    print("\n📋 Test 3: POST request with CORS headers")
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Content-Type': 'application/json'
        }
        data = {
            "original_question": "Test question for CORS",
            "include_data_gaps": False
        }
        response = requests.post(
            f"{base_url}/report/generate-streaming", 
            headers=headers,
            json=data
        )
        print(f"✅ POST request: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Task ID received: {result.get('task_id', 'No task_id')}")
        else:
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ POST request failed: {str(e)}")
        return False
    
    print("\n🎉 CORS configuration test completed successfully!")
    return True

def test_frontend_connection():
    """Test if frontend can connect to backend."""
    print("\n🔍 Testing frontend connection simulation...")
    
    # Simulate what the frontend JavaScript would do
    base_url = "http://localhost:8000"
    
    try:
        # Simulate fetch from frontend
        headers = {
            'Origin': 'http://localhost:3000',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Frontend Test)'
        }
        
        # Test the health endpoint first
        response = requests.get(f"{base_url}/health", headers=headers)
        print(f"✅ Frontend simulation - Health check: {response.status_code}")
        
        # Test report generation endpoint
        data = {
            "original_question": "How many students are there?",
            "include_data_gaps": False
        }
        response = requests.post(
            f"{base_url}/report/generate-streaming",
            headers=headers,
            json=data
        )
        print(f"✅ Frontend simulation - Report generation: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"   Task ID: {task_id}")
            
            # Test streaming endpoint (just check if it's accessible)
            stream_response = requests.get(
                f"{base_url}/report/stream/{task_id}",
                headers=headers,
                stream=True,
                timeout=5
            )
            print(f"✅ Frontend simulation - Streaming endpoint: {stream_response.status_code}")
            
        return True
        
    except Exception as e:
        print(f"❌ Frontend simulation failed: {str(e)}")
        return False

def main():
    """Run all CORS tests."""
    print("🚀 Starting CORS configuration tests...\n")
    
    # Test basic CORS configuration
    cors_test = test_cors_configuration()
    
    # Test frontend connection simulation
    frontend_test = test_frontend_connection()
    
    print(f"\n📊 Test Results:")
    print(f"  - CORS Configuration: {'✅' if cors_test else '❌'}")
    print(f"  - Frontend Connection: {'✅' if frontend_test else '❌'}")
    
    if cors_test and frontend_test:
        print(f"\n🎉 SUCCESS! CORS is properly configured!")
        print(f"✅ Frontend at http://localhost:3000 can now connect to backend")
        print(f"✅ All endpoints are accessible with proper CORS headers")
        print(f"\n🔧 Next steps:")
        print(f"  1. Make sure your backend is running: uvicorn app.main:app --reload")
        print(f"  2. Make sure your frontend is running: npm run dev")
        print(f"  3. Test the report generation in the browser")
    else:
        print(f"\n🔧 Some tests failed. Check the backend configuration.")
        print(f"  - Make sure the backend is running on http://localhost:8000")
        print(f"  - Restart the backend to apply CORS changes")

if __name__ == "__main__":
    main()

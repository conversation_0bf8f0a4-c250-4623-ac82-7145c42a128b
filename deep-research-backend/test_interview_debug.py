#!/usr/bin/env python3
"""
Test script to debug the interview chain and see why data_returned is always False.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_single_interview():
    """Test a single interview to see the data structure."""
    print("🔍 Testing single interview...")
    
    try:
        from app.chains.composite.interview import interview_chain
        
        test_question = "How many students are there?"
        print(f"🔎 Test question: '{test_question}'")
        
        result = interview_chain.invoke(test_question)
        print(f"📊 Interview result keys: {list(result.keys())}")
        
        # Check the convo_with_data structure
        convo_with_data = result.get("convo_with_data", [])
        print(f"\n💬 Conversations with data: {len(convo_with_data)}")
        
        for i, turn in enumerate(convo_with_data):
            print(f"\n  Turn {i+1}:")
            print(f"    Question: {turn.get('question', 'No question')}")
            print(f"    Answer: {turn.get('answer', 'No answer')[:100]}...")
            print(f"    Data: {turn.get('data', 'No data')}")
            print(f"    Data type: {type(turn.get('data', 'No data'))}")
            print(f"    Data == 'No results': {turn.get('data') == 'No results'}")
            print(f"    Presentation type: {turn.get('presentation_type', 'No presentation type')}")
            print(f"    Data tag: {turn.get('data_tag', 'No data tag')}")
            
        return result
        
    except Exception as e:
        print(f"❌ Interview test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_interview_to_docs(interview_result):
    """Test the interview_to_docs function."""
    print("\n🔍 Testing interview_to_docs...")
    
    if not interview_result:
        print("❌ No interview result to test")
        return None
    
    try:
        from app.helpers import interview_to_docs
        
        test_question = "How many students are there?"
        docs = interview_to_docs(interview_result, test_question)
        
        print(f"📄 Generated {len(docs)} documents")
        
        for i, doc in enumerate(docs):
            print(f"\n  Doc {i+1}:")
            print(f"    Content: {doc.page_content[:100]}...")
            print(f"    Metadata: {doc.metadata}")
            print(f"    data_returned: {doc.metadata.get('data_returned', 'Not set')}")
            
        return docs
        
    except Exception as e:
        print(f"❌ interview_to_docs test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_batch_interview():
    """Test batch interview to see if it works differently."""
    print("\n🔍 Testing batch interview...")
    
    try:
        from app.chains.composite.interview import batch_interview
        
        test_questions = ["How many students are there?"]
        print(f"🔎 Test questions: {test_questions}")
        
        result = batch_interview(test_questions)
        print(f"📊 Batch interview result keys: {list(result.keys())}")
        
        interviews = result.get("interviews", [])
        print(f"📋 Number of interviews: {len(interviews)}")
        
        for i, interview in enumerate(interviews):
            print(f"\n  Interview {i+1}:")
            convo_with_data = interview.get("convo_with_data", [])
            print(f"    Conversations with data: {len(convo_with_data)}")
            
            for j, turn in enumerate(convo_with_data):
                data = turn.get('data', 'No data')
                print(f"      Turn {j+1} data: {data}")
                print(f"      Turn {j+1} data type: {type(data)}")
                print(f"      Turn {j+1} data == 'No results': {data == 'No results'}")
                
        return result
        
    except Exception as e:
        print(f"❌ Batch interview test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_extract_functions(batch_result):
    """Test the extract functions."""
    print("\n🔍 Testing extract functions...")
    
    if not batch_result:
        print("❌ No batch result to test")
        return None
    
    try:
        from app.helpers import extract_q_a_with_data, extract_q_a_without_data
        
        data_backed_convos = extract_q_a_with_data(batch_result)
        data_lacking_convos = extract_q_a_without_data(batch_result)
        
        print(f"📊 Conversations with data: {len(data_backed_convos)}")
        print(f"📊 Conversations without data: {len(data_lacking_convos)}")
        
        print(f"\nData backed conversations:")
        for i, convo in enumerate(data_backed_convos):
            print(f"  {i+1}: {convo}")
            
        print(f"\nData lacking conversations:")
        for i, convo in enumerate(data_lacking_convos):
            print(f"  {i+1}: {convo}")
            
        return data_backed_convos, data_lacking_convos
        
    except Exception as e:
        print(f"❌ Extract functions test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """Run all tests."""
    print("🚀 Starting interview debug tests...\n")
    
    # Test 1: Single interview
    interview_result = test_single_interview()
    
    # Test 2: Interview to docs
    if interview_result:
        docs = test_interview_to_docs(interview_result)
    else:
        docs = None
    
    # Test 3: Batch interview
    batch_result = test_batch_interview()
    
    # Test 4: Extract functions
    if batch_result:
        data_backed, data_lacking = test_extract_functions(batch_result)
    else:
        data_backed, data_lacking = None, None
    
    print(f"\n📊 Summary:")
    print(f"  - Single interview: {'✅' if interview_result else '❌'}")
    print(f"  - Interview to docs: {'✅' if docs else '❌'}")
    print(f"  - Batch interview: {'✅' if batch_result else '❌'}")
    print(f"  - Extract functions: {'✅' if data_backed is not None else '❌'}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3

import os
import sys
import json
import traceback

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_report_pipeline_debug():
    print("🔍 Testing report pipeline step by step...")
    
    try:
        from app.chains.composite.report_pipeline import generate_full_report
        from app.models.report import ReportGenerationRequest
        from app.chains.filter_answerable_questions import batch_question_verification_chain
        from app.chains.composite.interview import batch_interview
        from app.utils.schema_loader import get_schema_file
        from app.chains.expand_question import expanded_questions_chain
        
        test_question = "How many students are there?"
        print(f"🔎 Test question: '{test_question}'")
        
        # Test step 1: Question expansion
        print("\n📋 Step 1: Question expansion")
        compact_db_structure = get_schema_file("compact_db_structure.txt")
        expanded_output = expanded_questions_chain.invoke({
            "schema_text": compact_db_structure,
            "question": test_question,
            "feedback": ""
        })
        print(f"✅ Expanded questions: {expanded_output}")
        
        # Test step 2: Question verification
        print("\n📋 Step 2: Question verification")
        questions_list = batch_question_verification_chain.invoke({
            "schema_text": compact_db_structure,
            "questions": expanded_output["questions"]  # Extract the questions list from the expanded output
        })
        print(f"✅ Verified questions: {questions_list}")
        print(f"📊 Number of questions: {len(questions_list) if questions_list else 0}")
        
        # Test step 3: Batch interview (if questions exist)
        if questions_list:
            print("\n📋 Step 3: Batch interview")
            interviews = batch_interview(questions_list)
            print(f"✅ Interview results:")
            print(json.dumps(interviews, indent=2, default=str))
            
            # Test step 4: Extract conversations
            print("\n📋 Step 4: Extract conversations")
            from app.helpers import extract_q_a_with_data, extract_q_a_without_data
            
            data_backed_convos = extract_q_a_with_data(interviews)
            data_lacking_convos = extract_q_a_without_data(interviews)
            
            print(f"✅ Conversations with data: {len(data_backed_convos)}")
            print(f"❌ Conversations without data: {len(data_lacking_convos)}")
            
            if data_backed_convos:
                print("📊 Sample conversation with data:")
                print(json.dumps(data_backed_convos[0], indent=2, default=str))
        else:
            print("❌ No questions to interview!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in report pipeline debug: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_report_pipeline_debug()

#!/usr/bin/env python3

import os
import sys
import json
import traceback

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_batch_interview_issue():
    print("🔍 Testing batch interview issue...")
    
    try:
        # Test what the question verification chain returns
        from app.chains.filter_answerable_questions import batch_question_verification_chain
        from app.utils.schema_loader import get_schema_file
        
        compact_db_structure = get_schema_file("compact_db_structure.txt")
        
        # Simulate what the expand_question chain would return
        test_input = {
            "schema_text": compact_db_structure,
            "questions": [
                "How many students are there?",
                "What is the gender distribution of students?",
                "How many courses are offered?"
            ]
        }
        
        print("🔎 Testing question verification chain...")
        questions_list = batch_question_verification_chain.invoke(test_input)
        print(f"📊 Questions list result:")
        print(f"Type: {type(questions_list)}")
        print(f"Content: {questions_list}")
        
        # Now test what happens when we pass this to batch_interview
        print("\n🔎 Testing batch interview with this input...")
        from app.chains.composite.interview import batch_interview
        
        result = batch_interview(questions_list)
        print(f"📊 Batch interview result:")
        print(json.dumps(result, indent=2, default=str))
        
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_batch_interview_issue()

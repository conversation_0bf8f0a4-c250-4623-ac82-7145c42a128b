"""
Test script for speech API endpoints.

This script tests the TTS and STT functionality to ensure the API is working correctly.
"""

import asyncio
import io
import requests
from app.speech.service import get_speech_service


async def test_tts():
    """Test text-to-speech functionality."""
    print("🎤 Testing Text-to-Speech...")
    
    try:
        service = get_speech_service()
        
        # Test TTS with a simple message
        response = await service.text_to_speech(
            text="Hello! This is a test of the text-to-speech functionality.",
            voice_id=None,  # Use default voice
            output_format="mp3_22050_32"
        )
        
        print("✅ TTS test completed successfully!")
        print(f"Response type: {type(response)}")
        
        return True
        
    except Exception as e:
        print(f"❌ TTS test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_provider_info():
    """Test provider information endpoints."""
    print("📋 Testing Provider Info...")
    
    try:
        service = get_speech_service()
        
        # Test provider info
        info = service.get_provider_info()
        print(f"✅ Provider info: {info}")
        
        # Test available voices
        voices = service.get_available_voices()
        print(f"✅ Available voices: {len(voices.get('voices', []))} voices found")
        
        # Test supported formats
        formats = service.get_supported_formats()
        print(f"✅ Supported formats: {formats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Provider info test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_api_endpoints():
    """Test API endpoints via HTTP requests."""
    print("🌐 Testing API Endpoints...")
    
    base_url = "http://localhost:8000"
    
    try:
        # Test health endpoint
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ Main health endpoint working")
        else:
            print(f"❌ Main health endpoint failed: {response.status_code}")
        
        # Test speech health endpoint
        response = requests.get(f"{base_url}/speech/health")
        if response.status_code == 200:
            print("✅ Speech health endpoint working")
            print(f"Speech health: {response.json()}")
        else:
            print(f"❌ Speech health endpoint failed: {response.status_code}")
        
        # Test provider info endpoint
        response = requests.get(f"{base_url}/speech/provider-info")
        if response.status_code == 200:
            print("✅ Provider info endpoint working")
            print(f"Provider info: {response.json()}")
        else:
            print(f"❌ Provider info endpoint failed: {response.status_code}")
        
        # Test voices endpoint
        response = requests.get(f"{base_url}/speech/voices")
        if response.status_code == 200:
            voices_data = response.json()
            print(f"✅ Voices endpoint working - {len(voices_data.get('voices', []))} voices")
        else:
            print(f"❌ Voices endpoint failed: {response.status_code}")
        
        # Test formats endpoint
        response = requests.get(f"{base_url}/speech/formats")
        if response.status_code == 200:
            print("✅ Formats endpoint working")
            print(f"Formats: {response.json()}")
        else:
            print(f"❌ Formats endpoint failed: {response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API server. Make sure the server is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ API endpoint test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Speech API Tests...\n")
    
    # Test service layer
    tts_success = await test_tts()
    print()
    
    provider_success = await test_provider_info()
    print()
    
    # Test API endpoints (requires server to be running)
    api_success = await test_api_endpoints()
    print()
    
    # Summary
    print("📊 Test Summary:")
    print(f"TTS Service: {'✅ PASS' if tts_success else '❌ FAIL'}")
    print(f"Provider Info: {'✅ PASS' if provider_success else '❌ FAIL'}")
    print(f"API Endpoints: {'✅ PASS' if api_success else '❌ FAIL'}")
    
    if all([tts_success, provider_success]):
        print("\n🎉 All core tests passed! Speech services are working correctly.")
        if not api_success:
            print("💡 To test API endpoints, start the server with: uvicorn app.main:app --reload")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")


if __name__ == "__main__":
    asyncio.run(main())

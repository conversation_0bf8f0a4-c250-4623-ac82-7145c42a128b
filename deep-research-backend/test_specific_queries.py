#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.db_api import run_query_silent
import json

def test_specific_queries():
    """Test the specific SQL queries that were generated in the logs"""
    
    print("🔍 Testing specific SQL queries from the logs...")
    
    # These are the queries that were marked as correct: True in the logs
    test_queries = [
        # Query 1: Campus enrollment
        """SELECT c.name AS campus_name, COUNT(s.id) AS student_count 
FROM campuses c 
INNER JOIN students s ON c.institution_id = s.institution_id 
WHERE s.status = 'active' 
GROUP BY c.name;""",
        
        # Query 2: Financial aid types
        """SELECT far.type, COUNT(*) AS count 
FROM financial_aid_requests far 
JOIN financial_aid_request_students fars ON far.reference_number = fars.financial_aid_request_reference_number 
GROUP BY far.type 
ORDER BY count DESC;""",
        
        # Query 3: Institution with most students and its change over time
        """SELECT ay.start_year, COUNT(s.id) AS student_count
FROM students s
JOIN institutions i ON s.institution_id = i.id
JOIN academic_years ay ON ay.institution_id = i.id
WHERE ay.start_year >= YEAR(CURDATE()) - 5
AND i.id = (SELECT institution_id FROM students GROUP BY institution_id ORDER BY COUNT(id) DESC LIMIT 1)
GROUP BY ay.start_year
ORDER BY ay.start_year;""",
        
        # Query 4: Partnerships/affiliations
        """SELECT a.long_name, a.short_name 
FROM affiliations a 
JOIN institutions i ON a.institution_id = i.id 
WHERE i.id = (
    SELECT institution_id 
    FROM students 
    GROUP BY institution_id 
    ORDER BY COUNT(*) DESC 
    LIMIT 1
)""",
        
        # Query 5: Academic year enrollment
        """SELECT ay.start_year, COUNT(sp.student_id) AS total_students
FROM student_programs sp
JOIN academic_years ay ON sp.semester_id = ay.id
WHERE ay.status = 'Active'
GROUP BY ay.start_year
ORDER BY ay.start_year;""",
        
        # Query 6: Financial aid eligibility criteria
        """SELECT DISTINCT f.type AS financial_aid_type, f.description AS eligibility_criteria, s.level AS student_level, p.long_name AS program_name
FROM financial_aid_requests f
JOIN financial_aid_request_students fas ON f.reference_number = fas.financial_aid_request_reference_number
JOIN student_programs s ON fas.student_program_id = s.id
JOIN programs p ON s.program_id = p.id
WHERE f.status = 'approved'"""
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Test Query {i}:")
        print(f"SQL: {query}")
        try:
            result = run_query_silent(query)
            print(f"✅ Success! Response code: {result.get('responseCode')}")
            print(f"📊 Message: {result.get('responseMessage')}")
            
            # Check if we got actual data
            if result.get("data"):
                print(f"📊 Data found: {len(result['data'])} rows")
                if result["data"]:
                    print(f"📋 First row: {result['data'][0]}")
                    if len(result["data"]) > 1:
                        print(f"📋 Total rows: {len(result['data'])}")
            else:
                print("❌ No data in result")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            print(f"❌ Error type: {type(e)}")

if __name__ == "__main__":
    test_specific_queries()

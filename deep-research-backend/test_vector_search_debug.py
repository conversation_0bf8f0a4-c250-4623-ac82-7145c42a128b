#!/usr/bin/env python3
"""
Test script to debug vector search issues in the report generation pipeline.
This script will test document storage and retrieval to identify why vector search returns 0 documents.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_elasticsearch_connection():
    """Test basic Elasticsearch connection and index status."""
    print("🔍 Testing Elasticsearch connection...")
    
    try:
        from app.vectorstore.client import es, elastic_vector_search
        from app.core.config import settings
        
        # Test connection
        if es.ping():
            print("✅ Elasticsearch connection successful")
        else:
            print("❌ Elasticsearch connection failed")
            return False
            
        # Check index exists
        index_name = settings.elasticsearch_index
        if es.indices.exists(index=index_name):
            print(f"✅ Index '{index_name}' exists")
        else:
            print(f"❌ Index '{index_name}' does not exist")
            return False
            
        # Get total document count
        total_docs = es.count(index=index_name)
        print(f"📊 Total documents in index: {total_docs['count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Elasticsearch: {str(e)}")
        return False

def test_document_metadata_filters():
    """Test document filtering by metadata."""
    print("\n🔍 Testing document metadata filters...")
    
    try:
        from app.vectorstore.client import es
        from app.core.config import settings
        
        index_name = settings.elasticsearch_index
        
        # Check documents with data_returned=True
        data_returned_query = {
            "query": {
                "term": {"metadata.data_returned": True}
            }
        }
        data_returned_count = es.count(index=index_name, body=data_returned_query)
        print(f"✅ Documents with data_returned=True: {data_returned_count['count']}")
        
        # Get sample documents to see their metadata structure
        sample_query = {
            "query": {"match_all": {}},
            "size": 3,
            "_source": ["metadata"]
        }
        sample_docs = es.search(index=index_name, body=sample_query)
        
        print(f"\n📄 Sample document metadata structures:")
        for i, doc in enumerate(sample_docs['hits']['hits']):
            print(f"  Doc {i+1}: {doc.get('_source', {}).get('metadata', {})}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing metadata filters: {str(e)}")
        return False

def test_conversation_id_filter():
    """Test filtering by conversation_id."""
    print("\n🔍 Testing conversation_id filtering...")
    
    try:
        from app.vectorstore.client import es
        from app.core.config import settings
        
        index_name = settings.elasticsearch_index
        
        # Get all unique conversation_ids
        agg_query = {
            "size": 0,
            "aggs": {
                "conversation_ids": {
                    "terms": {
                        "field": "metadata.conversation_id.keyword",
                        "size": 10
                    }
                }
            }
        }
        
        agg_result = es.search(index=index_name, body=agg_query)
        conversation_ids = [bucket['key'] for bucket in agg_result['aggregations']['conversation_ids']['buckets']]
        
        print(f"📋 Found conversation IDs: {conversation_ids}")
        
        # Test filtering by each conversation_id
        for conv_id in conversation_ids[:3]:  # Test first 3
            conv_query = {
                "query": {
                    "term": {"metadata.conversation_id.keyword": conv_id}
                }
            }
            conv_count = es.count(index=index_name, body=conv_query)
            print(f"  🗣️ Documents for '{conv_id}': {conv_count['count']}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing conversation_id filter: {str(e)}")
        return False

def test_combined_filters():
    """Test the combined filters used in the section writer."""
    print("\n🔍 Testing combined filters (data_returned=True AND conversation_id)...")
    
    try:
        from app.vectorstore.client import es
        from app.core.config import settings
        
        index_name = settings.elasticsearch_index
        
        # Get a sample conversation_id that has data_returned=True
        sample_query = {
            "query": {
                "term": {"metadata.data_returned": True}
            },
            "size": 1,
            "_source": ["metadata.conversation_id"]
        }
        
        sample_result = es.search(index=index_name, body=sample_query)
        if sample_result['hits']['hits']:
            sample_conv_id = sample_result['hits']['hits'][0]['_source']['metadata']['conversation_id']
            print(f"📝 Testing with sample conversation_id: '{sample_conv_id}'")
            
            # Test combined filter
            combined_query = {
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"metadata.data_returned": True}},
                            {"term": {"metadata.conversation_id.keyword": sample_conv_id}}
                        ]
                    }
                }
            }
            
            combined_count = es.count(index=index_name, body=combined_query)
            print(f"🎯 Documents matching both filters: {combined_count['count']}")
            
            # Get actual documents to see their content
            combined_search = es.search(index=index_name, body={**combined_query, "size": 2})
            print(f"\n📄 Sample documents matching filters:")
            for i, doc in enumerate(combined_search['hits']['hits']):
                content = doc['_source'].get('text', '')[:100]
                metadata = doc['_source'].get('metadata', {})
                print(f"  Doc {i+1}: {content}...")
                print(f"    Metadata: {metadata}")
                
        else:
            print("❌ No documents found with data_returned=True")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing combined filters: {str(e)}")
        return False

def test_vector_search_retrieval():
    """Test the actual vector search retrieval."""
    print("\n🔍 Testing vector search retrieval...")
    
    try:
        from app.vectorstore.client import elastic_vector_search
        
        # Test basic retrieval without filters
        print("Testing basic retrieval...")
        basic_retriever = elastic_vector_search.as_retriever(search_kwargs={"k": 3})
        basic_results = basic_retriever.invoke("students")
        print(f"📊 Basic search returned {len(basic_results)} documents")
        
        # Test with filters (like section writer uses)
        print("\nTesting with filters...")
        
        # First, get a valid conversation_id
        from app.vectorstore.client import es
        from app.core.config import settings
        
        sample_query = {
            "query": {"term": {"metadata.data_returned": True}},
            "size": 1,
            "_source": ["metadata.conversation_id"]
        }
        
        sample_result = es.search(index=settings.elasticsearch_index, body=sample_query)
        if sample_result['hits']['hits']:
            sample_conv_id = sample_result['hits']['hits'][0]['_source']['metadata']['conversation_id']
            
            filtered_retriever = elastic_vector_search.as_retriever(search_kwargs={
                "k": 5,
                "filter": [
                    {"term": {"metadata.data_returned": True}},
                    {"term": {"metadata.conversation_id.keyword": sample_conv_id}}
                ]
            })
            
            filtered_results = filtered_retriever.invoke("students")
            print(f"🎯 Filtered search returned {len(filtered_results)} documents")
            
            if filtered_results:
                print("✅ Vector search with filters is working!")
                for i, doc in enumerate(filtered_results[:2]):
                    print(f"  Doc {i+1}: {doc.page_content[:100]}...")
            else:
                print("⚠️ Vector search with filters returned no results")
        else:
            print("❌ No valid conversation_id found for testing")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing vector search: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting vector search debug tests...\n")
    
    tests = [
        test_elasticsearch_connection,
        test_document_metadata_filters,
        test_conversation_id_filter,
        test_combined_filters,
        test_vector_search_retrieval
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append(False)
    
    print(f"\n📊 Test Results: {sum(results)}/{len(results)} passed")
    
    if all(results):
        print("✅ All tests passed! Vector search should be working.")
    else:
        print("❌ Some tests failed. Check the output above for issues.")

if __name__ == "__main__":
    main()

"""
Full integration test for speech functionality.

This script tests the complete speech integration including:
- Backend service initialization
- TTS functionality
- STT functionality (with mock audio)
- API endpoints
- Error handling
"""

import asyncio
import io
import wave
import numpy as np
from app.speech.service import get_speech_service
from app.speech.factory import SpeechProviderType


def create_test_audio_file() -> io.BytesIO:
    """Create a simple test audio file (sine wave)."""
    # Generate a 2-second sine wave at 440 Hz
    sample_rate = 44100
    duration = 2.0
    frequency = 440.0
    
    # Generate samples
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    samples = np.sin(2 * np.pi * frequency * t)
    
    # Convert to 16-bit integers
    samples = (samples * 32767).astype(np.int16)
    
    # Create WAV file in memory
    audio_buffer = io.BytesIO()
    with wave.open(audio_buffer, 'wb') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 2 bytes per sample
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(samples.tobytes())
    
    audio_buffer.seek(0)
    return audio_buffer


async def test_service_initialization():
    """Test speech service initialization."""
    print("🔧 Testing Service Initialization...")
    
    try:
        service = get_speech_service()
        print("✅ Speech service initialized successfully")
        
        # Test provider info
        info = service.get_provider_info()
        print(f"✅ Provider: {info['provider_type']}")
        print(f"✅ TTS Available: {info['tts_available']}")
        print(f"✅ STT Available: {info['stt_available']}")
        print(f"✅ Voices: {info['voices_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Service initialization failed: {e}")
        return False


async def test_tts_functionality():
    """Test text-to-speech functionality."""
    print("\n🎤 Testing Text-to-Speech...")
    
    try:
        service = get_speech_service()
        
        # Test TTS with different texts
        test_texts = [
            "Hello, this is a test of the text-to-speech functionality.",
            "The quick brown fox jumps over the lazy dog.",
            "Testing numbers: 1, 2, 3, 4, 5."
        ]
        
        for i, text in enumerate(test_texts, 1):
            print(f"  Testing text {i}: {text[:30]}...")
            
            response = await service.text_to_speech(
                text=text,
                output_format="mp3_22050_32"
            )
            
            print(f"  ✅ TTS {i} completed - Response type: {type(response)}")
        
        print("✅ All TTS tests passed")
        return True
        
    except Exception as e:
        print(f"❌ TTS test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_stt_functionality():
    """Test speech-to-text functionality."""
    print("\n🎧 Testing Speech-to-Text...")
    
    try:
        service = get_speech_service()
        
        # Create test audio file
        print("  Creating test audio file...")
        audio_file = create_test_audio_file()
        
        # Test STT
        print("  Testing STT conversion...")
        result = await service.speech_to_text(
            audio_file=audio_file,
            language_code="eng",
            tag_audio_events=True
        )
        
        print(f"  ✅ STT completed")
        print(f"  Text: {result.text}")
        print(f"  Language: {result.language}")
        print(f"  Metadata: {result.metadata}")
        
        return True
        
    except Exception as e:
        print(f"❌ STT test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_voice_management():
    """Test voice management functionality."""
    print("\n🎭 Testing Voice Management...")
    
    try:
        service = get_speech_service()
        
        # Get available voices
        voices = service.get_available_voices()
        print(f"✅ Retrieved {len(voices.get('voices', []))} voices")
        
        # Test with specific voice if available
        if voices.get('voices'):
            voice = voices['voices'][0]
            print(f"  Testing with voice: {voice['name']} ({voice['voice_id']})")
            
            response = await service.text_to_speech(
                text="Testing with a specific voice.",
                voice_id=voice['voice_id']
            )
            
            print(f"  ✅ Voice-specific TTS completed")
        
        # Get supported formats
        formats = service.get_supported_formats()
        print(f"✅ Supported formats: {formats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Voice management test failed: {e}")
        return False


async def test_error_handling():
    """Test error handling."""
    print("\n⚠️ Testing Error Handling...")
    
    try:
        service = get_speech_service()
        
        # Test with empty text
        try:
            await service.text_to_speech(text="")
            print("  ⚠️ Empty text should have failed")
        except Exception:
            print("  ✅ Empty text properly rejected")
        
        # Test with invalid voice ID
        try:
            await service.text_to_speech(
                text="Test",
                voice_id="invalid_voice_id"
            )
            print("  ⚠️ Invalid voice ID should have failed")
        except Exception:
            print("  ✅ Invalid voice ID properly rejected")
        
        print("✅ Error handling tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False


async def test_performance():
    """Test performance with multiple requests."""
    print("\n⚡ Testing Performance...")
    
    try:
        service = get_speech_service()
        
        # Test concurrent TTS requests
        import time
        start_time = time.time()
        
        tasks = []
        for i in range(3):
            task = service.text_to_speech(
                text=f"Performance test number {i + 1}.",
                output_format="mp3_22050_32"
            )
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        successful = sum(1 for r in results if not isinstance(r, Exception))
        print(f"✅ Completed {successful}/3 concurrent requests in {duration:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Full Speech Integration Tests...\n")
    
    tests = [
        ("Service Initialization", test_service_initialization),
        ("TTS Functionality", test_tts_functionality),
        ("STT Functionality", test_stt_functionality),
        ("Voice Management", test_voice_management),
        ("Error Handling", test_error_handling),
        ("Performance", test_performance),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📊 Test Summary:")
    print("="*50)
    
    passed = 0
    total = len(tests)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Speech integration is working correctly.")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please check the errors above.")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

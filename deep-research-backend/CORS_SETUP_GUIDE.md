# CORS Setup Guide

This guide will help you fix CORS (Cross-Origin Resource Sharing) errors between the frontend and backend.

## 🔧 What Was Fixed

### 1. Backend CORS Configuration
Updated `app/main.py` to include CORS middleware:

```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Next.js development server
        "http://127.0.0.1:3000",  # Alternative localhost
        "http://**************:3000",  # Network address
        "*"  # Allow all origins in development
    ],
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
    expose_headers=["*"],  # Expose all headers for EventSource
)
```

### 2. Frontend Error Handling
Enhanced `src/services/reportApi.ts` with:
- Better error messages for CORS issues
- Network error detection
- Connection testing functionality

### 3. Connection Testing
Added `ConnectionTest` component to verify backend connectivity.

## 🚀 Setup Instructions

### Step 1: Restart Backend
The backend needs to be restarted to apply CORS changes:

```bash
# Stop the current backend (Ctrl+C if running)
# Then restart:
cd /Users/<USER>/Desktop/Professional/Work/deep-research-backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Step 2: Verify Backend is Running
Test the backend health endpoint:

```bash
# Run the CORS test script
python test_cors.py
```

Expected output:
```
✅ Health check: 200 - {'status': 'ok', 'message': 'Backend is running with CORS enabled'}
✅ CORS preflight: 200
✅ POST request: 200
```

### Step 3: Test Frontend Connection
1. Make sure frontend is running:
```bash
cd itc-agent-frontend
npm run dev
```

2. Open browser to `http://localhost:3000/chat`
3. Click "Reports" tab
4. Check the connection status at the top - should show "Backend connected"

## 🔍 Troubleshooting

### Common CORS Errors

#### Error: "Access to fetch at '...' from origin '...' has been blocked by CORS policy"
**Solution**: Backend CORS is not configured or backend is not running
- Restart backend with the updated `app/main.py`
- Check backend is running on `http://localhost:8000`

#### Error: "Network Error" or "ERR_NETWORK"
**Solution**: Backend is not running or wrong URL
- Check backend is running: `curl http://localhost:8000/health`
- Verify `NEXT_PUBLIC_API_BASE_URL` in frontend `.env.local`

#### Error: "Failed to fetch" or status 0
**Solution**: CORS preflight request failed
- Check CORS middleware is properly configured
- Ensure `allow_origins` includes your frontend URL

### Debug Steps

1. **Check Backend Health**:
```bash
curl http://localhost:8000/health
```
Should return: `{"status":"ok","message":"Backend is running with CORS enabled"}`

2. **Check CORS Headers**:
```bash
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     http://localhost:8000/report/generate-streaming
```

3. **Check Browser Console**:
- Open browser dev tools (F12)
- Look for CORS or network errors in Console tab
- Check Network tab for failed requests

4. **Test with Connection Component**:
- The frontend now includes a connection test
- Green status = working, Red status = check error message

## 🔧 Configuration Options

### Production CORS Setup
For production, replace the wildcard `"*"` with specific domains:

```python
allow_origins=[
    "https://yourdomain.com",
    "https://www.yourdomain.com",
]
```

### Alternative Ports
If backend runs on different port, update both:

1. **Backend**: Change uvicorn port
```bash
uvicorn app.main:app --reload --port 8080
```

2. **Frontend**: Update `.env.local`
```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
```

### Network Access
To access from other devices on network:

1. **Backend**: Use `--host 0.0.0.0`
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

2. **Frontend**: Add network IP to CORS origins
```python
allow_origins=[
    "http://*************:3000",  # Replace with your IP
    # ... other origins
]
```

## ✅ Verification Checklist

- [ ] Backend running on `http://localhost:8000`
- [ ] Health endpoint returns 200: `curl http://localhost:8000/health`
- [ ] CORS test script passes: `python test_cors.py`
- [ ] Frontend running on `http://localhost:3000`
- [ ] Connection test shows "Backend connected"
- [ ] No CORS errors in browser console
- [ ] Report generation works without network errors

## 🎯 Next Steps

Once CORS is working:
1. Test report generation in the frontend
2. Verify streaming endpoints work
3. Check that charts and tables render correctly
4. Test error handling with invalid inputs

## 📞 Still Having Issues?

If CORS errors persist:

1. **Check exact error message** in browser console
2. **Verify URLs match** between frontend config and backend
3. **Test with curl** to isolate frontend vs backend issues
4. **Check firewall/antivirus** blocking local connections
5. **Try different browser** to rule out browser-specific issues

The CORS configuration is now comprehensive and should handle all development scenarios!

#!/usr/bin/env python3

import os
import sys
import json
import traceback

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_sql_generation_isolated():
    print("🔍 Testing SQL generation chain in isolation...")
    
    try:
        # Import the SQL generation chain
        from app.chains.composite.sql_generation_and_verification import sql_gen_veri_chain
        print("✅ Successfully imported sql_gen_veri_chain")
        
        test_question = "How many students are there?"
        print(f"🔎 Test question: '{test_question}'")
        
        # Test the chain
        result = sql_gen_veri_chain.invoke(test_question)
        print(f"📊 SQL generation result:")
        print(json.dumps(result, indent=2, default=str))
        
        # Check the result structure
        if isinstance(result, dict):
            print(f"🔍 Result keys: {list(result.keys())}")
            
            expected_keys = ["question", "sql", "correct", "reasoning"]
            missing_keys = [key for key in expected_keys if key not in result]
            if missing_keys:
                print(f"❌ Missing expected keys: {missing_keys}")
            else:
                print("✅ All expected keys present")
                
            if "correct" in result:
                print(f"✅ 'correct' value: {result['correct']}")
            else:
                print("❌ Missing 'correct' key!")
        else:
            print(f"❌ Result is not a dict: {type(result)}")
            
        return result
        
    except Exception as e:
        print(f"❌ Error in SQL generation: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_sql_generation_isolated()

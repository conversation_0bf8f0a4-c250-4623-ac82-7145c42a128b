#!/usr/bin/env python3
"""
Test script to verify the updated section writer with fallback mechanism.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_section_writer_with_fallback():
    """Test the section writer with the new fallback mechanism."""
    print("🔍 Testing section writer with fallback mechanism...")
    
    try:
        from app.chains.section_writer import section_writer_chain
        
        # Test with a simple outline segment
        test_input = {
            "outline_segment": "## Total Student Count\nAnalyze the total number of students enrolled.",
            "conversation_id": "How many students are there?"
        }
        
        print(f"📝 Test input:")
        print(f"  Outline: {test_input['outline_segment']}")
        print(f"  Conversation ID: {test_input['conversation_id']}")
        
        # Run the section writer
        result = section_writer_chain.invoke(test_input)
        
        print(f"\n📊 Section writer result:")
        print(f"  Type: {type(result)}")
        print(f"  Length: {len(result) if isinstance(result, str) else 'N/A'}")
        print(f"  Content preview: {str(result)[:200]}...")
        
        # Check if the result contains meaningful content
        if isinstance(result, str) and len(result) > 50:
            print("✅ Section writer produced meaningful content")
            return True
        else:
            print("❌ Section writer produced minimal or no content")
            return False
        
    except Exception as e:
        print(f"❌ Section writer test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_vector_search_debug():
    """Test the vector search debug function directly."""
    print("\n🔍 Testing vector search debug function...")
    
    try:
        from app.chains.section_writer import debug_vector_search_with_filters
        
        test_params = {
            "query": "total student count",
            "conversation_id": "How many students are there?",
            "question": "How many students are there?"
        }
        
        print(f"📝 Test parameters:")
        for key, value in test_params.items():
            print(f"  {key}: {value}")
        
        # Run the debug function
        results = debug_vector_search_with_filters(test_params)
        
        print(f"\n📊 Vector search results:")
        print(f"  Number of documents: {len(results)}")
        
        if results:
            print("✅ Vector search returned documents")
            for i, doc in enumerate(results[:2]):  # Show first 2
                print(f"    Doc {i+1}: {doc.page_content[:100]}...")
                print(f"    Metadata: {doc.metadata}")
            return True
        else:
            print("❌ Vector search returned no documents")
            return False
        
    except Exception as e:
        print(f"❌ Vector search debug test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_section_writing():
    """Test batch section writing like in the report pipeline."""
    print("\n🔍 Testing batch section writing...")
    
    try:
        from app.chains.section_writer import section_writer_chain
        
        # Test with multiple outline segments
        test_inputs = [
            {
                "outline_segment": "## Introduction\nProvide an overview of student enrollment.",
                "conversation_id": "How many students are there?"
            },
            {
                "outline_segment": "## Total Student Count\nAnalyze the total number of students enrolled.",
                "conversation_id": "How many students are there?"
            },
            {
                "outline_segment": "## Conclusion\nSummarize the key findings about student enrollment.",
                "conversation_id": "How many students are there?"
            }
        ]
        
        print(f"📝 Testing batch processing with {len(test_inputs)} sections")
        
        # Run batch processing
        results = section_writer_chain.batch(test_inputs)
        
        print(f"\n📊 Batch results:")
        print(f"  Number of results: {len(results)}")
        
        success_count = 0
        for i, result in enumerate(results):
            content_length = len(result) if isinstance(result, str) else 0
            is_meaningful = content_length > 50
            success_count += is_meaningful
            
            print(f"  Section {i+1}: {content_length} chars {'✅' if is_meaningful else '❌'}")
            if is_meaningful:
                print(f"    Preview: {str(result)[:100]}...")
        
        if success_count > 0:
            print(f"✅ Batch processing successful: {success_count}/{len(results)} sections generated")
            return True
        else:
            print("❌ Batch processing failed: no meaningful sections generated")
            return False
        
    except Exception as e:
        print(f"❌ Batch section writing test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all section writer tests."""
    print("🚀 Starting section writer debug tests...\n")
    
    tests = [
        ("Vector search debug", test_vector_search_debug),
        ("Single section writer", test_section_writer_with_fallback),
        ("Batch section writing", test_batch_section_writing)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 Running: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append(result)
            print(f"\n{'✅' if result else '❌'} {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            print(f"\n❌ {test_name}: FAILED with exception: {str(e)}")
            results.append(False)
    
    print(f"\n📊 Test Summary:")
    print(f"  Tests passed: {sum(1 for r in results if r)}/{len(results)}")
    
    if all(results):
        print("✅ All section writer tests passed!")
        print("🎯 The vector search fallback mechanism should now work!")
    else:
        print("❌ Some section writer tests failed.")
        print("🔧 Further debugging may be needed.")

if __name__ == "__main__":
    main()

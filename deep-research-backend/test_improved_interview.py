#!/usr/bin/env python3
"""
Test script to verify the improved interview chain handles errors better.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_improved_interview_chain():
    """Test the improved interview chain with better error handling."""
    print("🔍 Testing improved interview chain...")
    
    try:
        from app.chains.composite.interview import interview_chain
        
        test_question = "How many students are there?"
        print(f"🔎 Test question: '{test_question}'")
        
        result = interview_chain.invoke(test_question)
        print(f"📊 Interview result keys: {list(result.keys())}")
        
        # Check the convo_with_data structure
        convo_with_data = result.get("convo_with_data", [])
        print(f"\n💬 Conversations with data: {len(convo_with_data)}")
        
        data_returned_count = 0
        for i, turn in enumerate(convo_with_data):
            data = turn.get('data', 'No data')
            has_data = data != "No results"
            if has_data:
                data_returned_count += 1
            
            print(f"\n  Turn {i+1}:")
            print(f"    Question: {turn.get('question', 'No question')[:80]}...")
            print(f"    Answer: {turn.get('answer', 'No answer')[:80]}...")
            print(f"    Data: {str(data)[:80]}...")
            print(f"    Has data: {'✅' if has_data else '❌'}")
            print(f"    Data tag: {turn.get('data_tag', 'No data tag')}")
            
        print(f"\n📊 Summary:")
        print(f"  Total turns: {len(convo_with_data)}")
        print(f"  Turns with data: {data_returned_count}")
        print(f"  Success rate: {data_returned_count}/{len(convo_with_data)} ({100*data_returned_count/len(convo_with_data) if convo_with_data else 0:.1f}%)")
        
        return result, data_returned_count > 0
        
    except Exception as e:
        print(f"❌ Interview test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, False

def test_interview_to_docs_with_data(interview_result):
    """Test converting interview to docs and check data_returned flags."""
    print("\n🔍 Testing interview_to_docs with improved data handling...")
    
    if not interview_result:
        print("❌ No interview result to test")
        return None, 0
    
    try:
        from app.helpers import interview_to_docs
        
        test_question = "How many students are there?"
        docs = interview_to_docs(interview_result, test_question)
        
        print(f"📄 Generated {len(docs)} documents")
        
        data_returned_count = 0
        for i, doc in enumerate(docs):
            data_returned = doc.metadata.get('data_returned', False)
            if data_returned:
                data_returned_count += 1
                
            print(f"\n  Doc {i+1}:")
            print(f"    Content: {doc.page_content[:80]}...")
            print(f"    data_returned: {'✅' if data_returned else '❌'}")
            print(f"    Metadata: {doc.metadata}")
            
        print(f"\n📊 Document Summary:")
        print(f"  Total documents: {len(docs)}")
        print(f"  Documents with data_returned=True: {data_returned_count}")
        print(f"  Success rate: {data_returned_count}/{len(docs)} ({100*data_returned_count/len(docs) if docs else 0:.1f}%)")
        
        return docs, data_returned_count
        
    except Exception as e:
        print(f"❌ interview_to_docs test error: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, 0

def test_simple_sql_questions():
    """Test with simpler, more answerable questions."""
    print("\n🔍 Testing with simpler SQL questions...")
    
    simple_questions = [
        "What is the total number of students?",
        "How many students are male vs female?",
        "What are the different programs offered?"
    ]
    
    try:
        from app.chains.composite.interview import interview_chain
        
        results = []
        for question in simple_questions:
            print(f"\n🔎 Testing: '{question}'")
            
            try:
                result = interview_chain.invoke(question)
                convo_with_data = result.get("convo_with_data", [])
                
                data_count = 0
                for turn in convo_with_data:
                    if turn.get('data', 'No results') != "No results":
                        data_count += 1
                
                print(f"  📊 Result: {len(convo_with_data)} turns, {data_count} with data")
                results.append((question, len(convo_with_data), data_count))
                
            except Exception as e:
                print(f"  ❌ Error: {str(e)}")
                results.append((question, 0, 0))
        
        print(f"\n📊 Simple Questions Summary:")
        total_turns = sum(r[1] for r in results)
        total_data = sum(r[2] for r in results)
        
        for question, turns, data in results:
            print(f"  '{question}': {data}/{turns} turns with data")
        
        print(f"  Overall: {total_data}/{total_turns} turns with data ({100*total_data/total_turns if total_turns else 0:.1f}%)")
        
        return results
        
    except Exception as e:
        print(f"❌ Simple questions test error: {str(e)}")
        return []

def main():
    """Run all improved interview tests."""
    print("🚀 Starting improved interview chain tests...\n")
    
    # Test 1: Improved interview chain
    interview_result, has_data = test_improved_interview_chain()
    
    # Test 2: Interview to docs
    if interview_result:
        docs, docs_with_data = test_interview_to_docs_with_data(interview_result)
    else:
        docs, docs_with_data = None, 0
    
    # Test 3: Simple questions
    simple_results = test_simple_sql_questions()
    
    print(f"\n📊 Overall Test Summary:")
    print(f"  - Interview chain: {'✅' if has_data else '❌'}")
    print(f"  - Document generation: {'✅' if docs_with_data > 0 else '❌'}")
    print(f"  - Simple questions: {'✅' if simple_results and any(r[2] > 0 for r in simple_results) else '❌'}")
    
    if has_data or docs_with_data > 0:
        print("\n🎉 SUCCESS! The improved interview chain is generating data!")
        print("✅ This should fix the vector search issue in report generation")
    else:
        print("\n🔧 The interview chain still needs more work")

if __name__ == "__main__":
    main()

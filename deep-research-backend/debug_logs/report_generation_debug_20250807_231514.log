2025-08-07 23:15:14,855 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250807_231514.log
2025-08-07 23:15:14,855 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:14,855 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: de8f01b2-e31f-4d7c-aa93-51aa474ddeec
2025-08-07 23:15:14,855 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:14,856 - REPORT_REQUEST - INFO - 📝 Original Question: 'What is the institution with the most students?'
2025-08-07 23:15:14,856 - REPORT_REQUEST - INFO - 🆔 Task ID: de8f01b2-e31f-4d7c-aa93-51aa474ddeec
2025-08-07 23:15:14,856 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-07T23:15:14.856090
2025-08-07 23:15:15,004 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.148s]
2025-08-07 23:15:15,005 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-07 23:15:15,134 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-07 23:15:15,135 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:15:15,267 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.132s]
2025-08-07 23:15:15,268 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-07 23:15:15,268 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-07 23:15:25,500 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:25,524 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-07 23:15:30,496 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:30,508 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-07 23:15:33,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:33,487 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-07 23:15:33,488 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:33,488 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-07 23:15:33,488 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:33,488 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 0
2025-08-07 23:15:33,488 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-07 23:15:33,488 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-07 23:15:33,488 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 0
2025-08-07 23:15:33,488 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-07 23:15:41,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:41,904 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-07 23:15:52,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:52,350 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:52,350 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-07 23:15:52,350 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:52,351 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 0
2025-08-07 23:15:52,351 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:52,351 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-07 23:15:52,351 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:52,351 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 0 documents
2025-08-07 23:15:52,351 - UPSERT_DOCS - WARNING - ⚠️ No documents to upsert!
2025-08-07 23:15:52,480 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-07 23:15:52,481 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-07 23:15:52,713 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.232s]
2025-08-07 23:15:52,713 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:15:52,922 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.209s]
2025-08-07 23:15:52,923 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-07 23:15:52,924 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 8
2025-08-07 23:15:52,924 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-07 23:15:52,924 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:52,925 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-07 23:15:52,925 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:52,925 - REPORT_PIPELINE - INFO - ✍️ Writing 8 sections using batch processing...
2025-08-07 23:15:53,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:53,522 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:53,522 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:15:53,522 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:53,522 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'highest student enrollment institution analysis'
2025-08-07 23:15:53,522 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 23:15:53,522 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 23:15:53,704 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.181s]
2025-08-07 23:15:53,704 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:15:53,850 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:53,855 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:53,855 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:15:53,855 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:53,855 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Discussion of findings in higher education'
2025-08-07 23:15:53,855 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 23:15:53,856 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 23:15:53,896 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.192s]
2025-08-07 23:15:53,896 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:15:53,911 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:53,914 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:53,915 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:15:53,915 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:53,915 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Interview findings enrollment trends'
2025-08-07 23:15:53,915 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 23:15:53,915 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 23:15:53,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:53,933 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:53,933 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:15:53,933 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:53,933 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'References and Acknowledgments'
2025-08-07 23:15:53,933 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 23:15:53,933 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 23:15:54,027 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-07 23:15:54,028 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:15:54,131 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:54,142 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:54,142 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:15:54,143 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:54,143 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology Data Collection Interview Process Limitations'
2025-08-07 23:15:54,143 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 23:15:54,143 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 23:15:54,144 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.288s]
2025-08-07 23:15:54,144 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:15:54,157 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-07 23:15:54,158 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:15:54,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:54,181 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:54,188 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:54,194 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:15:54,239 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:54,240 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.306s]
2025-08-07 23:15:54,240 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.325s]
2025-08-07 23:15:54,240 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student enrollment trends summary recommendations'
2025-08-07 23:15:54,240 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:15:54,240 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:15:54,240 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 23:15:54,241 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 23:15:54,242 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:54,242 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:15:54,242 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:54,242 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Appendices Interview Transcripts Data Tables Charts'
2025-08-07 23:15:54,242 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 23:15:54,242 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 23:15:54,367 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.223s]
2025-08-07 23:15:54,367 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:15:54,404 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.162s]
2025-08-07 23:15:54,404 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.163s]
2025-08-07 23:15:54,404 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:15:54,404 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:15:54,424 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.184s]
2025-08-07 23:15:54,425 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:15:54,525 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.382s]
2025-08-07 23:15:54,526 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:15:54,537 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.170s]
2025-08-07 23:15:54,538 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.296s]
2025-08-07 23:15:54,538 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-07 23:15:54,538 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:15:54,538 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:15:54,538 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:15:54,539 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-07 23:15:54,540 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:15:54,556 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-07 23:15:54,556 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:15:54,720 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.194s]
2025-08-07 23:15:54,720 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:15:54,725 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.169s]
2025-08-07 23:15:54,725 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.185s]
2025-08-07 23:15:54,725 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.186s]
2025-08-07 23:15:54,726 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:15:54,726 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.187s]
2025-08-07 23:15:54,727 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.188s]
2025-08-07 23:15:54,727 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:15:54,727 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:15:54,728 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:15:54,729 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:15:54,819 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:15:54,923 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.203s]
2025-08-07 23:15:54,923 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:15:54,930 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.200s]
2025-08-07 23:15:54,930 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:15:54,933 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.202s]
2025-08-07 23:15:54,933 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:15:54,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:15:54,969 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:15:54,969 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:15:54,969 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:15:54,969 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'key terms institution student enrollment types of institutions'
2025-08-07 23:15:54,969 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 23:15:54,969 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 23:15:55,141 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.172s]
2025-08-07 23:15:55,141 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.218s]
2025-08-07 23:15:55,142 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.212s]
2025-08-07 23:15:55,142 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:15:55,142 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:15:55,143 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:15:55,177 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:15:55,196 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.310s]
2025-08-07 23:15:55,196 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:15:55,196 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:15:55,294 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:15:55,350 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.207s]
2025-08-07 23:15:55,350 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:15:55,462 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.164s]
2025-08-07 23:15:55,462 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:15:55,462 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:15:55,520 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.169s]
2025-08-07 23:15:55,520 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:15:55,595 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.415s]
2025-08-07 23:15:55,596 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:15:55,596 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:15:55,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:15:55,691 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.171s]
2025-08-07 23:15:55,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:15:55,692 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:15:55,774 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.135s]
2025-08-07 23:15:55,774 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:15:55,774 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:15:55,827 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.129s]
2025-08-07 23:15:55,827 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:15:55,827 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:15:56,134 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:15:56,266 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.128s]
2025-08-07 23:15:56,267 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:15:56,267 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:15:57,233 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:15:57,384 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.146s]
2025-08-07 23:15:57,384 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:15:57,384 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:15:57,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:15:57,628 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.178s]
2025-08-07 23:15:57,629 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:15:57,629 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:15:59,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:16:00,127 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:16:00,639 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:16:03,096 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:16:03,727 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:16:03,816 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:16:03,856 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:16:07,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:16:07,743 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 372 characters
2025-08-07 23:16:07,743 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1423 characters
2025-08-07 23:16:07,743 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1477 characters
2025-08-07 23:16:07,743 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 2089 characters
2025-08-07 23:16:07,744 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1592 characters
2025-08-07 23:16:07,744 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 1030 characters
2025-08-07 23:16:07,744 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 519 characters
2025-08-07 23:16:07,744 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 429 characters
2025-08-07 23:16:09,461 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:16:09,470 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:16:09,470 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:16:09,470 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:16:09,470 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:16:09,470 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:16:09,470 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:16:09,470 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:16:09,470 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:16:09,470 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:16:09,471 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:16:09,471 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-07 23:16:09,471 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:16:09,471 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-07 23:16:09,471 - REPORT_PIPELINE - INFO - 📊 Final report sections: 9
2025-08-07 23:16:09,471 - REPORT_PIPELINE - INFO - 📈 Extended data items: 0
2025-08-07 23:16:09,471 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250807_231514.log
2025-08-07 23:16:09,475 - celery.app.trace - INFO - Task generate_report[39809891-faab-486e-887b-0e1decd6e866] succeeded in 54.62038216699875s: {'outline': '# Report Outline: Investigating the Institution with the Most Students

## I. Introduction  
The purpose of this report is to identify the institution with the highest student enrollment and to analyze the factors contributing to this status. The key finding indicates that the institution with the most students is a large public university, which has implemented various strategies to accommodate and attract a significant number of students.

## II. Definition of Key Terms  
- Institution  
- Student Enrollment  
- Types of Institutions (e.g., universities, colleges, online institutions)  

## III. Methodology  
- Overview of Data Collection  
- Description of Interview Process  
- Limitations of the Study  

## IV. Findings  
- Summary of Interview Insights  
- Key Themes Identified  
  - Enrollment Numbers  
  - Institutional Types (e.g., Public vs. Private)  
  - Geographic Distribution  
- Comparative Analysis of Institutions  
  - Top Institutions by Enrollment  
  - Trends in Student...', 'se...', ...}

2025-08-08 15:23:21,870 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_152321.log
2025-08-08 15:23:21,870 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:23:21,870 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: ba390750-cfc2-47e0-b14e-fe0e8735744b
2025-08-08 15:23:21,870 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:23:21,870 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-08 15:23:21,870 - REPORT_REQUEST - INFO - 🆔 Task ID: ba390750-cfc2-47e0-b14e-fe0e8735744b
2025-08-08 15:23:21,870 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T15:23:21.870780
2025-08-08 15:23:22,115 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.244s]
2025-08-08 15:23:22,115 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-08 15:23:22,360 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.245s]
2025-08-08 15:23:22,360 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 15:23:22,598 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.238s]
2025-08-08 15:23:22,598 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-08 15:23:22,599 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:23:22,599 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 15:23:22,599 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:23:22,599 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How are girls performing at ITC University?
2025-08-08 15:23:22,599 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-08 15:23:22,600 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 15:23:35,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:35,473 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 15:23:40,594 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:40,614 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-08 15:23:43,205 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:43,408 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:44,040 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:44,043 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:44,068 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:44,281 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:44,317 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:44,599 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:45,411 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:45,861 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:45,930 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-08 15:23:48,980 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:49,203 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:49,278 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:49,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:52,203 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:52,218 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': "The schema does not contain specific data regarding graduation rates, gender-specific graduation statistics, or any direct comparison metrics between different groups (e.g., girls vs. overall). While there are tables related to students and graduation (like 'graduation_batches', 'graduation_bills', and 'students'), there is no explicit field or table that provides graduation rates or allows for the calculation of such rates based on gender or institution. Therefore, the question cannot be answered with the available schema.", 'feedback': ''}
2025-08-08 15:23:52,218 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:23:52,218 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:23:52,219 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': "The schema does not contain specific data regarding graduation rates, gender-specific graduation statistics, or any direct comparison metrics between different groups (e.g., girls vs. overall). While there are tables related to students and graduation (like 'graduation_batches', 'graduation_bills', and 'students'), there is no explicit field or table that provides graduation rates or allows for the calculation of such rates based on gender or institution. Therefore, the question cannot be answered with the available schema.", 'feedback': ''}
2025-08-08 15:23:53,155 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:53,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:53,892 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:55,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:55,554 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:56,537 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:56,926 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:23:58,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:00,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:01,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:02,309 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:02,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:06,789 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:06,798 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any trends in the performance of girls at ITC University over the past few years?', 'sql': "SELECT s.sex, sem.start_year, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS total_assessments\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN semesters sem ON ar.semester_id = sem.id\nWHERE s.sex = 'F' AND sem.status = 'Ended'\nGROUP BY s.sex, sem.start_year\nORDER BY sem.start_year;", 'correct': True, 'reasoning': "The SQL query correctly identifies the performance trends of female students ('F') at ITC University by calculating the average final score and the total number of assessments for each academic year (start_year) where the semester status is 'Ended'. The use of AVG(ar.finalscore) provides insight into performance trends over the years, and grouping by start_year allows for a year-by-year analysis. The ordering by start_year ensures that the results are presented chronologically, which is essential for identifying trends.", 'feedback': 'The question could be clarified by specifying what kind of trends are being looked for (e.g., improvement, decline, consistency) or by asking for specific metrics beyond average scores, such as grade distributions or comparisons with male students. The SQL could be improved by including additional metrics or visualizations if needed.'}
2025-08-08 15:24:06,798 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:06,799 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 15:24:08,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:08,507 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic performance levels of girls compare to those of boys at ITC University?', 'sql': "SELECT s.sex, AVG(sg.gpa) AS average_gpa, COUNT(sg.id) AS total_students\nFROM core.students s\nJOIN core.student_semester_gpas sg ON s.id = sg.student_id\nWHERE s.institution_id = 'ITC University' AND sg.status = 'active'\nGROUP BY s.sex;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average GPA and total number of students for each sex (girls and boys) at ITC University. It joins the 'students' table with the 'student_semester_gpas' table to access GPA data, filters for the specified institution, and groups the results by sex. This directly addresses the question of comparing academic performance levels between girls and boys.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating the institution's name in a more dynamic way (e.g., using a parameter) to enhance reusability. Additionally, consider including a comparison metric or visualization in the output for clearer insights."}
2025-08-08 15:24:08,508 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:08,508 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 15:24:09,494 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:10,063 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:10,069 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in the most?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(ar.finalscore) AS average_score\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.assessment_results ar ON sp.id = ar.student_program_id\nJOIN core.courses c ON ar.course_id = c.id\nWHERE s.sex = 'F' AND s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY p.long_name, c.title\nORDER BY average_score DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses that female students at ITC University are excelling in by calculating the average scores of assessment results for each program and course combination. It filters the students by sex and institution, ensuring that only female students from ITC University are considered. The use of GROUP BY allows for the aggregation of scores by program and course, and the ORDER BY clause sorts the results by average score in descending order, which aligns with the question's focus on identifying areas of excellence.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be beneficial to clarify what 'excelling' means in this context—whether it refers to the highest average scores or a certain threshold of performance. Additionally, if there are specific courses within programs that are of interest, the query could be adjusted to focus on those as well."}
2025-08-08 15:24:10,069 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:10,069 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 15:24:11,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:12,415 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:13,591 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:13,731 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:14,611 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:15,244 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:15,994 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:15,995 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:16,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:16,729 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:16,734 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'sql': "SELECT\n    SUM(CASE WHEN s.sex = 'F' THEN 1 ELSE 0 END) AS female_graduates,\n    COUNT(DISTINCT gr.id) AS total_graduates,\n    (SUM(CASE WHEN s.sex = 'F' THEN 1 ELSE 0 END) * 100.0 / COUNT(DISTINCT gr.id)) AS female_graduation_rate,\n    (COUNT(DISTINCT gr.id) * 100.0 / COUNT(DISTINCT s.id)) AS overall_graduation_rate\nFROM\n    graduation_report gr\nJOIN\n    graduation_batches gb ON gr.graduation_batch_id = gb.id\nJOIN\n    students s ON gr.user_id = s.id\nWHERE\n    gb.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\n    AND gb.status = 'active'", 'correct': True, 'reasoning': "The SQL query correctly calculates the graduation rates for female students at ITC University by summing the number of female graduates and dividing it by the total number of graduates to get the female graduation rate. It also calculates the overall graduation rate by dividing the total number of graduates by the total number of students. The query filters for the specific institution (ITC University) and only includes active graduation batches, which aligns with the question's requirements.", 'feedback': "The question could be clarified by specifying whether 'overall graduation rates' refers to all students or just those from ITC University. Additionally, the SQL could be improved by explicitly stating the total number of students in the denominator for the overall graduation rate calculation, ensuring clarity in the context of the institution."}
2025-08-08 15:24:16,734 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:16,734 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 15:24:17,225 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:18,975 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:19,109 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:19,133 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 15:24:19,134 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:19,134 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:19,134 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 15:24:20,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:21,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:22,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:22,891 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:22,924 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. However, the provided schema does not contain any specific information or guidelines on data collection methods or analysis strategies. It primarily consists of tables related to institutions, students, academic years, and various related entities, but lacks any explicit recommendations or best practices for improving data collection and analysis processes. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 15:24:22,924 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:22,924 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:22,924 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. However, the provided schema does not contain any specific information or guidelines on data collection methods or analysis strategies. It primarily consists of tables related to institutions, students, academic years, and various related entities, but lacks any explicit recommendations or best practices for improving data collection and analysis processes. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 15:24:22,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:23,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:23,335 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of female students in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of female students in specific programs and courses at ITC University. However, the provided schema does not contain any direct data or metrics related to the success of students, such as performance indicators, qualitative assessments, or demographic analyses specifically focused on female students. While there are tables related to students, programs, and courses, there is no indication of how success is measured or what specific factors are considered. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 15:24:23,335 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:23,335 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:23,335 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of female students in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of female students in specific programs and courses at ITC University. However, the provided schema does not contain any direct data or metrics related to the success of students, such as performance indicators, qualitative assessments, or demographic analyses specifically focused on female students. While there are tables related to students, programs, and courses, there is no indication of how success is measured or what specific factors are considered. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 15:24:23,849 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:25,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:25,789 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:26,961 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:28,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:28,048 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis processes. Therefore, while the schema can provide data on student performance and gender, it does not answer the question about improving data collection and analysis.', 'feedback': ''}
2025-08-08 15:24:28,048 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:28,048 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:28,048 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis processes. Therefore, while the schema can provide data on student performance and gender, it does not answer the question about improving data collection and analysis.', 'feedback': ''}
2025-08-08 15:24:28,125 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:28,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:28,303 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, courses, and performance metrics, but it does not provide insights into the qualitative factors or specific performance data for girls at ITC University. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 15:24:28,303 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:28,303 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:28,303 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, courses, and performance metrics, but it does not provide insights into the qualitative factors or specific performance data for girls at ITC University. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 15:24:29,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:29,879 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of female students in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of female students in specific programs and courses at ITC University. However, the provided schema does not contain specific data or metrics that directly relate to the success factors of students, such as academic performance indicators, qualitative assessments, or demographic analyses. While the schema includes tables related to students, programs, and courses, it lacks the necessary attributes or relationships to analyze success factors specifically for female students in the context of their programs and courses.', 'feedback': ''}
2025-08-08 15:24:29,879 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:29,879 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:29,879 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of female students in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of female students in specific programs and courses at ITC University. However, the provided schema does not contain specific data or metrics that directly relate to the success factors of students, such as academic performance indicators, qualitative assessments, or demographic analyses. While the schema includes tables related to students, programs, and courses, it lacks the necessary attributes or relationships to analyze success factors specifically for female students in the context of their programs and courses.', 'feedback': ''}
2025-08-08 15:24:31,150 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:31,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:31,388 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher graduation rates for girls at ITC University compared to the overall rates?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher graduation rates for girls at ITC University compared to overall rates. However, the provided schema does not contain specific data on graduation rates, gender-specific statistics, or factors influencing graduation rates. While there are tables related to students, programs, and graduation, there is no direct way to extract or analyze the factors affecting graduation rates based on the available schema.', 'feedback': ''}
2025-08-08 15:24:31,388 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:31,388 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:31,388 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher graduation rates for girls at ITC University compared to the overall rates?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher graduation rates for girls at ITC University compared to overall rates. However, the provided schema does not contain specific data on graduation rates, gender-specific statistics, or factors influencing graduation rates. While there are tables related to students, programs, and graduation, there is no direct way to extract or analyze the factors affecting graduation rates based on the available schema.', 'feedback': ''}
2025-08-08 15:24:31,638 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:32,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:34,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:34,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:34,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:34,481 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis processes. Therefore, while the schema can provide data on academic performance and gender, it does not answer the question of what steps to take for improvement.', 'feedback': ''}
2025-08-08 15:24:34,481 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:34,482 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:34,482 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis processes. Therefore, while the schema can provide data on academic performance and gender, it does not answer the question of what steps to take for improvement.', 'feedback': ''}
2025-08-08 15:24:34,495 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 15:24:34,496 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:34,496 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:34,496 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 15:24:35,432 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:35,448 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of female students in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of female students in specific programs and courses at ITC University. However, the provided schema does not contain specific data or metrics related to the success of students, such as performance indicators, qualitative assessments, or demographic analyses that would allow for a comprehensive answer. The schema includes various tables related to students, programs, and courses, but it lacks the necessary data to analyze success factors specifically for female students in the context of their programs and courses.', 'feedback': ''}
2025-08-08 15:24:35,448 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:35,449 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:35,449 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of female students in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of female students in specific programs and courses at ITC University. However, the provided schema does not contain specific data or metrics related to the success of students, such as performance indicators, qualitative assessments, or demographic analyses that would allow for a comprehensive answer. The schema includes various tables related to students, programs, and courses, but it lacks the necessary data to analyze success factors specifically for female students in the context of their programs and courses.', 'feedback': ''}
2025-08-08 15:24:36,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:36,584 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher graduation rates for girls at ITC University compared to the overall rates?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher graduation rates for girls at ITC University compared to overall rates. However, the provided schema does not contain specific data on graduation rates, gender-specific statistics, or factors influencing graduation rates. While there are tables related to students, programs, and graduation, there is no direct information or metrics that would allow for a comparative analysis of graduation rates based on gender or specific institutions. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 15:24:36,584 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:36,584 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:36,584 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher graduation rates for girls at ITC University compared to the overall rates?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher graduation rates for girls at ITC University compared to overall rates. However, the provided schema does not contain specific data on graduation rates, gender-specific statistics, or factors influencing graduation rates. While there are tables related to students, programs, and graduation, there is no direct information or metrics that would allow for a comparative analysis of graduation rates based on gender or specific institutions. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 15:24:36,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:37,689 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:38,682 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:39,428 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:39,451 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or tables that directly relate to performance data, gender-specific performance metrics, or any qualitative factors that could explain the absence of such data. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks the necessary context or data points to address the question about performance data specifically for girls at a particular institution.', 'feedback': ''}
2025-08-08 15:24:39,453 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:39,453 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:39,453 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or tables that directly relate to performance data, gender-specific performance metrics, or any qualitative factors that could explain the absence of such data. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks the necessary context or data points to address the question about performance data specifically for girls at a particular institution.', 'feedback': ''}
2025-08-08 15:24:39,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:39,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:39,914 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. However, the provided schema does not contain any specific information or guidelines on how to improve data collection or analysis processes. It primarily consists of tables related to institutions, students, academic years, and various related entities, but lacks any procedural or strategic insights. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 15:24:39,914 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:39,914 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:39,914 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. However, the provided schema does not contain any specific information or guidelines on how to improve data collection or analysis processes. It primarily consists of tables related to institutions, students, academic years, and various related entities, but lacks any procedural or strategic insights. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 15:24:41,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:41,324 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher graduation rates for girls at ITC University compared to the overall rates?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher graduation rates for girls at ITC University compared to overall rates. However, the provided schema does not contain specific data on graduation rates, gender-specific statistics, or factors influencing graduation rates. While there are tables related to students, programs, and graduation, there is no direct information or metrics that would allow for a comparative analysis of graduation rates based on gender or specific institutions. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 15:24:41,324 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:41,324 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:41,324 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher graduation rates for girls at ITC University compared to the overall rates?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher graduation rates for girls at ITC University compared to overall rates. However, the provided schema does not contain specific data on graduation rates, gender-specific statistics, or factors influencing graduation rates. While there are tables related to students, programs, and graduation, there is no direct information or metrics that would allow for a comparative analysis of graduation rates based on gender or specific institutions. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 15:24:42,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:42,183 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of female students in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of female students in specific programs and courses at ITC University. However, the provided schema does not contain specific data or metrics related to the success of students, such as performance indicators, qualitative assessments, or demographic analyses that would allow for a comprehensive answer. While there are tables related to students, programs, and courses, there is no direct link to factors influencing success, such as social, economic, or academic support systems. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 15:24:42,183 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 15:24:42,183 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 15:24:42,183 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of female students in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of female students in specific programs and courses at ITC University. However, the provided schema does not contain specific data or metrics related to the success of students, such as performance indicators, qualitative assessments, or demographic analyses that would allow for a comprehensive answer. While there are tables related to students, programs, and courses, there is no direct link to factors influencing success, such as social, economic, or academic support systems. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 15:24:42,184 - root - INFO - [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 86.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 84.26}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Media And Technologies For Teaching And Learning', 'average_score': 78.83}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'On-Campus Teaching Practice', 'average_score': 75.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Project Work', 'average_score': 74.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 72.0}, {'program_name': 'Master Of Arts In Educational Leadership', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Guidance and Counselling in Schools', 'average_score': 70.0}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'course_title': 'Academic Writing', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Methods in Specified Subjects', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Historical, Philosophical and Sociological Foundations of Education', 'average_score': 67.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'INTRODUCTION TO HOME ECONOMICS', 'average_score': 66.45}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'General Principles And Methods Of Instruction', 'average_score': 66.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'School Organisation and Management', 'average_score': 65.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Software', 'average_score': 61.2}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 59.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 54.2}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Off-Campus Teaching Practice', 'average_score': 0.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'INTRODUCTION TO HOME ECONOMICS', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Emerging Technologies And Internet Application', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'PERSONAL GROWTH AND COMMUNICATION SKILLS', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'INTRODUCTION TO PHYSICAL CHEMISTRY', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Applied Science In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'INTRODUCTION TO CLOTHING AND TEXTILES', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Applied Mathematics In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Applied Science In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Applied Mathematics In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}]
2025-08-08 15:24:42,185 - root - INFO - [{'female_graduates': 6540.0, 'total_graduates': 12, 'female_graduation_rate': 54500.0, 'overall_graduation_rate': 0.15}]
2025-08-08 15:24:42,185 - root - INFO - 'No results'
2025-08-08 15:24:42,185 - root - INFO - 'No results'
2025-08-08 15:24:42,185 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 15:24:53,621 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:24:53,628 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 15:25:11,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:11,814 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:11,814 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 15:25:11,815 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 15:25:11,815 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the academic performance levels of girls compare to those of boys at ITC University?...
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, which indicates that there may not be sufficient data avai...
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: academic_performance_comparison_by_gender
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 15:25:11,815 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 15:25:11,815 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:11,816 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 15:25:11,816 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:11,816 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 15:25:11,816 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 15:25:11,816 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 15:25:11,816 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 15:25:11,816 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 15:25:11,816 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are girls at ITC University excelling in the most?...
2025-08-08 15:25:11,816 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Girls at ITC University are excelling in several programs and courses, with the highest average scor...
2025-08-08 15:25:11,816 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 15:25:11,816 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Re...
2025-08-08 15:25:11,816 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_academic_excellence_by_program_and_course
2025-08-08 15:25:11,816 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 15:25:11,816 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 15:25:11,816 - celery.redirected - WARNING - [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 86.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 84.26}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Media And Technologies For Teaching And Learning', 'average_score': 78.83}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'On-Campus Teaching Practice', 'average_score': 75.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Project Work', 'average_score': 74.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 72.0}, {'program_name': 'Master Of Arts In Educational Leadership', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Guidance and Counselling in Schools', 'average_score': 70.0}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'course_title': 'Academic Writing', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Methods in Specified Subjects', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Historical, Philosophical and Sociological Foundations of Education', 'average_score': 67.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'INTRODUCTION TO HOME ECONOMICS', 'average_score': 66.45}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'General Principles And Methods Of Instruction', 'average_score': 66.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'School Organisation and Management', 'average_score': 65.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Software', 'average_score': 61.2}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 59.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 54.2}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Off-Campus Teaching Practice', 'average_score': 0.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'INTRODUCTION TO HOME ECONOMICS', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Emerging Technologies And Internet Application', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'PERSONAL GROWTH AND COMMUNICATION SKILLS', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'INTRODUCTION TO PHYSICAL CHEMISTRY', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Applied Science In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'INTRODUCTION TO CLOTHING AND TEXTILES', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Applied Mathematics In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Applied Science In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Applied Mathematics In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}]
2025-08-08 15:25:11,817 - celery.redirected - WARNING - ================================= 
2025-08-08 15:25:11,817 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 15:25:11,817 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:11,817 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 15:25:11,817 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:11,817 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 15:25:11,817 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 15:25:11,817 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 15:25:11,817 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 15:25:11,817 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 15:25:11,817 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any trends in the performance of girls at ITC University over the past few years?...
2025-08-08 15:25:11,817 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Based on the data available, there are no recorded results regarding the performance of girls at ITC...
2025-08-08 15:25:11,817 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 15:25:11,817 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_performance_trends_itc_university
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 15:25:11,818 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:11,818 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 15:25:11,818 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 15:25:11,818 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the graduation rates of girls at ITC University compare to the overall graduation rates?...
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, the graduation rate for girls is significantly higher than the overall graduation...
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: graph
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'female_graduates': 6540.0, 'total_graduates': 12, 'female_graduation_rate': 54500.0, 'overall_gra...
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: female_graduation_rate_vs_overall
2025-08-08 15:25:11,818 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 15:25:11,818 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 15:25:11,818 - celery.redirected - WARNING - [{'female_graduates': 6540.0, 'total_graduates': 12, 'female_graduation_rate': 54500.0, 'overall_graduation_rate': 0.15}]
2025-08-08 15:25:11,818 - celery.redirected - WARNING - ================================= 
2025-08-08 15:25:11,819 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: female_graduation_rate_vs_overall
2025-08-08 15:25:11,819 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 15:25:11,819 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 4
2025-08-08 15:25:11,819 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:11,819 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 15:25:11,819 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:11,819 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 4 documents
2025-08-08 15:25:11,819 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 15:25:11,819 - UPSERT_DOCS - INFO -     Content: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 15:25:11,819 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:39.914952+00:00', 'data_returned': False}
2025-08-08 15:25:11,819 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 15:25:11,819 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:11,819 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:11,819 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 15:25:11,819 - UPSERT_DOCS - INFO -     Content: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 15:25:11,819 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:39.453762+00:00', 'data_returned': False}
2025-08-08 15:25:11,820 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 15:25:11,820 - UPSERT_DOCS - INFO -     Content: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:11,820 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:11,820 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 2/4
2025-08-08 15:25:12,057 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.237s]
2025-08-08 15:25:15,780 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 15:25:16,956 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:1.101s]
2025-08-08 15:25:16,957 - UPSERT_DOCS - INFO - ✅ Successfully upserted 4 documents to Elasticsearch
2025-08-08 15:25:16,958 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 9
2025-08-08 15:25:16,958 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 15:25:16,959 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:16,959 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-08 15:25:16,959 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:16,959 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/9...
2025-08-08 15:25:17,925 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:17,928 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:17,928 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 15:25:17,928 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:17,928 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'academic performance female students ITC University'
2025-08-08 15:25:17,928 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 15:25:17,928 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 15:25:18,169 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.240s]
2025-08-08 15:25:18,169 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 4
2025-08-08 15:25:18,408 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.238s]
2025-08-08 15:25:18,408 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 2
2025-08-08 15:25:18,647 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.238s]
2025-08-08 15:25:18,648 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-08 15:25:18,887 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.239s]
2025-08-08 15:25:18,887 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-08 15:25:19,813 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 15:25:20,060 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.243s]
2025-08-08 15:25:20,061 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-08 15:25:20,062 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:20,062 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:20,062 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:20,062 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:20,063 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:20,063 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:20,063 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:20,063 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:20,063 - app.chains.section_writer - INFO -   ✅ Added Data Tag: female_graduation_rate_vs_overall
2025-08-08 15:25:20,063 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1334 chars):
2025-08-08 15:25:20,063 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls at ITC University are excelling in several programs and courses, with the highest average scores recorded in the following: 1) The Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics' with an average score of 86.0. 2) The Bachelor of Science in Clothing and Textiles Education, particularly in 'Introduction To Informa...
2025-08-08 15:25:24,335 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:24,345 - app.chains.section_writer - INFO - 🤖 AI generated section (1175 chars):
2025-08-08 15:25:24,345 - app.chains.section_writer - INFO -    This report analyzes the academic performance of female students at ITC University, focusing on their achievements, challenges, and the support systems in place. The key finding indicates that girls are performing well in specific programs, with graduation rates significantly higher than the overall...
2025-08-08 15:25:24,345 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['female_graduation_rate_vs_overall']
2025-08-08 15:25:24,345 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 1175 characters
2025-08-08 15:25:24,346 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/9...
2025-08-08 15:25:24,944 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:24,957 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:24,958 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 15:25:24,958 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:24,958 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology data collection sample size limitations'
2025-08-08 15:25:24,958 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 15:25:24,958 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 15:25:25,207 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.248s]
2025-08-08 15:25:25,207 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 4
2025-08-08 15:25:25,457 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.249s]
2025-08-08 15:25:25,457 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 2
2025-08-08 15:25:25,702 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.244s]
2025-08-08 15:25:25,702 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-08 15:25:25,944 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.241s]
2025-08-08 15:25:25,944 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-08 15:25:26,527 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 15:25:26,779 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.248s]
2025-08-08 15:25:26,780 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-08 15:25:26,780 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:26,780 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:26,780 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:26,781 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:26,782 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:26,782 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:26,782 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:26,782 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:26,782 - app.chains.section_writer - INFO -   ✅ Added Data Tag: female_graduation_rate_vs_overall
2025-08-08 15:25:26,782 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1334 chars):
2025-08-08 15:25:26,782 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls at ITC University are excelling in several programs and courses, with the highest average scores recorded in the following: 1) The Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics' with an average score of 86.0. 2) The Bachelor of Science in Clothing and Textiles Education, particularly in 'Introduction To Informa...
2025-08-08 15:25:32,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:32,850 - app.chains.section_writer - INFO - 🤖 AI generated section (1615 chars):
2025-08-08 15:25:32,850 - app.chains.section_writer - INFO -    ## 2. Methodology  

### 2.1 Data Collection Methods  
The study employed a mixed-methods approach to gather comprehensive data. Surveys were distributed to students to capture their experiences and perceptions regarding academic programs. Additionally, academic records were analyzed to assess perfo...
2025-08-08 15:25:32,850 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['female_graduation_rate_vs_overall']
2025-08-08 15:25:32,850 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 1615 characters
2025-08-08 15:25:32,851 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/9...
2025-08-08 15:25:33,431 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:33,437 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:33,437 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 15:25:33,437 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:33,437 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'girls academic performance programs comparison with boys'
2025-08-08 15:25:33,437 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 15:25:33,437 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 15:25:33,677 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.240s]
2025-08-08 15:25:33,678 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 4
2025-08-08 15:25:33,921 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.243s]
2025-08-08 15:25:33,921 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 2
2025-08-08 15:25:34,161 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.239s]
2025-08-08 15:25:34,161 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-08 15:25:34,400 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.239s]
2025-08-08 15:25:34,400 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-08 15:25:34,890 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 15:25:35,141 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.248s]
2025-08-08 15:25:35,142 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-08 15:25:35,142 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:35,142 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:35,142 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:35,142 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:35,143 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:35,143 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:35,143 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:35,143 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:35,143 - app.chains.section_writer - INFO -   ✅ Added Data Tag: female_graduation_rate_vs_overall
2025-08-08 15:25:35,143 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1334 chars):
2025-08-08 15:25:35,143 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls at ITC University are excelling in several programs and courses, with the highest average scores recorded in the following: 1) The Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics' with an average score of 86.0. 2) The Bachelor of Science in Clothing and Textiles Education, particularly in 'Introduction To Informa...
2025-08-08 15:25:39,546 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:39,551 - app.chains.section_writer - INFO - 🤖 AI generated section (1113 chars):
2025-08-08 15:25:39,551 - app.chains.section_writer - INFO -    ## 3. Academic Performance  

### 3.1 Excelling Programs and Courses  
Girls at ITC University are excelling in several programs and courses, with the highest average scores recorded in the following:  
- **Bachelor of Arts in Journalism and Media Studies**  
  - Course: Educational Research Methods...
2025-08-08 15:25:39,551 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['female_graduation_rate_vs_overall']
2025-08-08 15:25:39,551 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 1113 characters
2025-08-08 15:25:39,552 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/9...
2025-08-08 15:25:40,203 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:40,211 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:40,212 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 15:25:40,212 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:40,212 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Graduation rates trends female'
2025-08-08 15:25:40,212 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 15:25:40,212 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 15:25:40,449 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.237s]
2025-08-08 15:25:40,449 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 4
2025-08-08 15:25:40,691 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.241s]
2025-08-08 15:25:40,691 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 2
2025-08-08 15:25:40,929 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.237s]
2025-08-08 15:25:40,929 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-08 15:25:41,172 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.242s]
2025-08-08 15:25:41,172 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-08 15:25:42,839 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 15:25:43,092 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.249s]
2025-08-08 15:25:43,093 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-08 15:25:43,093 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:43,093 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:43,093 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:43,093 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:43,094 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:43,094 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:43,094 - app.chains.section_writer - INFO -   ✅ Added Data Tag: female_graduation_rate_vs_overall
2025-08-08 15:25:43,094 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:43,094 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:43,094 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1334 chars):
2025-08-08 15:25:43,094 - app.chains.section_writer - INFO -    Question: How do the graduation rates of girls at ITC University compare to the overall graduation rates?
Answer: At ITC University, the graduation rate for girls is significantly higher than the overall graduation rate. Specifically, the female graduation rate is 54500%, while the overall graduation rate stands at 15%. This indicates that girls are graduating at a much higher rate compared to the total student population.
Data Tag: female_graduation_rate_vs_overall

Question: What programs or c...
2025-08-08 15:25:47,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:47,286 - app.chains.section_writer - INFO - 🤖 AI generated section (996 chars):
2025-08-08 15:25:47,287 - app.chains.section_writer - INFO -    ## 4. Graduation Rates  

### 4.1 Female Graduation Rates  
The female graduation rate at ITC University is 54.5%. This figure is significantly higher than the overall graduation rate, which stands at 15%. The disparity indicates that female students are graduating at a much higher rate compared to ...
2025-08-08 15:25:47,287 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['female_graduation_rate_vs_overall']
2025-08-08 15:25:47,287 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 996 characters
2025-08-08 15:25:47,287 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/9...
2025-08-08 15:25:48,412 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:48,422 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:48,423 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 15:25:48,423 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:48,423 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'factors influencing performance'
2025-08-08 15:25:48,423 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 15:25:48,423 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 15:25:48,666 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.242s]
2025-08-08 15:25:48,666 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 4
2025-08-08 15:25:48,906 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.240s]
2025-08-08 15:25:48,907 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 2
2025-08-08 15:25:49,151 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.244s]
2025-08-08 15:25:49,152 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-08 15:25:49,399 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.246s]
2025-08-08 15:25:49,399 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-08 15:25:50,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 15:25:50,273 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.237s]
2025-08-08 15:25:50,274 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-08 15:25:50,274 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:50,274 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:50,275 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:50,275 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:50,275 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:50,276 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:50,276 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:50,276 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:50,276 - app.chains.section_writer - INFO -   ✅ Added Data Tag: female_graduation_rate_vs_overall
2025-08-08 15:25:50,276 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1334 chars):
2025-08-08 15:25:50,276 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls at ITC University are excelling in several programs and courses, with the highest average scores recorded in the following: 1) The Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics' with an average score of 86.0. 2) The Bachelor of Science in Clothing and Textiles Education, particularly in 'Introduction To Informa...
2025-08-08 15:25:56,291 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:56,298 - app.chains.section_writer - INFO - 🤖 AI generated section (1666 chars):
2025-08-08 15:25:56,298 - app.chains.section_writer - INFO -    ## 5. Factors Influencing Performance  

### 5.1 Socioeconomic Background  
The socioeconomic background of students plays a crucial role in their academic performance. Factors such as family income, parental education levels, and community resources can significantly impact students' access to educ...
2025-08-08 15:25:56,298 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['female_graduation_rate_vs_overall']
2025-08-08 15:25:56,298 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 1666 characters
2025-08-08 15:25:56,299 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 6/9...
2025-08-08 15:25:57,435 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:25:57,440 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:25:57,440 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 15:25:57,440 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:25:57,440 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Faculty Institutional Support Gender Equality'
2025-08-08 15:25:57,440 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 15:25:57,440 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 15:25:57,677 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.236s]
2025-08-08 15:25:57,677 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 4
2025-08-08 15:25:57,921 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.244s]
2025-08-08 15:25:57,922 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 2
2025-08-08 15:25:58,165 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.243s]
2025-08-08 15:25:58,165 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-08 15:25:58,404 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.238s]
2025-08-08 15:25:58,404 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-08 15:25:59,030 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 15:25:59,280 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.247s]
2025-08-08 15:25:59,280 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-08 15:25:59,280 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:59,280 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:59,280 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:59,281 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:59,281 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:25:59,281 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:25:59,281 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:25:59,281 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:25:59,281 - app.chains.section_writer - INFO -   ✅ Added Data Tag: female_graduation_rate_vs_overall
2025-08-08 15:25:59,281 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1334 chars):
2025-08-08 15:25:59,281 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls at ITC University are excelling in several programs and courses, with the highest average scores recorded in the following: 1) The Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics' with an average score of 86.0. 2) The Bachelor of Science in Clothing and Textiles Education, particularly in 'Introduction To Informa...
2025-08-08 15:26:04,674 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:26:04,683 - app.chains.section_writer - INFO - 🤖 AI generated section (1423 chars):
2025-08-08 15:26:04,683 - app.chains.section_writer - INFO -    ## 6. Faculty and Institutional Support  

### 6.1 Programs for Female Empowerment  
ITC University has implemented various programs aimed at empowering female students. These initiatives focus on enhancing academic performance and providing support in traditionally male-dominated fields. Notably, f...
2025-08-08 15:26:04,683 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['female_graduation_rate_vs_overall']
2025-08-08 15:26:04,683 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 6 completed and processed: 1423 characters
2025-08-08 15:26:04,684 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 7/9...
2025-08-08 15:26:05,578 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:26:05,588 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:26:05,589 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 15:26:05,589 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:26:05,589 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion key findings future research university policies'
2025-08-08 15:26:05,589 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 15:26:05,589 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 15:26:05,835 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.246s]
2025-08-08 15:26:05,836 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 4
2025-08-08 15:26:06,073 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.237s]
2025-08-08 15:26:06,074 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 2
2025-08-08 15:26:06,314 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.240s]
2025-08-08 15:26:06,315 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-08 15:26:06,553 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.237s]
2025-08-08 15:26:06,553 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-08 15:26:07,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 15:26:07,903 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.240s]
2025-08-08 15:26:07,904 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-08 15:26:07,904 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:26:07,904 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:26:07,904 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:26:07,904 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:26:07,905 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:26:07,905 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:26:07,906 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:26:07,906 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:26:07,906 - app.chains.section_writer - INFO -   ✅ Added Data Tag: female_graduation_rate_vs_overall
2025-08-08 15:26:07,906 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1334 chars):
2025-08-08 15:26:07,906 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls at ITC University are excelling in several programs and courses, with the highest average scores recorded in the following: 1) The Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics' with an average score of 86.0. 2) The Bachelor of Science in Clothing and Textiles Education, particularly in 'Introduction To Informa...
2025-08-08 15:26:12,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:26:12,834 - app.chains.section_writer - INFO - 🤖 AI generated section (1533 chars):
2025-08-08 15:26:12,834 - app.chains.section_writer - INFO -    In conclusion, the analysis of female students at ITC University reveals several key findings. Girls are excelling in specific programs, notably the Bachelor of Arts in Journalism and Media Studies, where they achieved an average score of 86.0 in 'Educational Research Methods, Assessment and Statist...
2025-08-08 15:26:12,834 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['female_graduation_rate_vs_overall']
2025-08-08 15:26:12,834 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 7 completed and processed: 1533 characters
2025-08-08 15:26:12,835 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 8/9...
2025-08-08 15:26:13,554 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:26:13,565 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:26:13,565 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 15:26:13,565 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:26:13,565 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'supporting female students ITC University'
2025-08-08 15:26:13,565 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 15:26:13,565 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 15:26:13,812 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.246s]
2025-08-08 15:26:13,812 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 4
2025-08-08 15:26:14,049 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.237s]
2025-08-08 15:26:14,049 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 2
2025-08-08 15:26:14,293 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.243s]
2025-08-08 15:26:14,293 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-08 15:26:14,535 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.241s]
2025-08-08 15:26:14,535 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-08 15:26:15,118 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 15:26:15,369 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.247s]
2025-08-08 15:26:15,370 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-08 15:26:15,370 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:26:15,370 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:26:15,370 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:26:15,370 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:26:15,371 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:26:15,371 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:26:15,371 - app.chains.section_writer - INFO -   ✅ Added Data Tag: female_graduation_rate_vs_overall
2025-08-08 15:26:15,371 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:26:15,371 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:26:15,371 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1334 chars):
2025-08-08 15:26:15,371 - app.chains.section_writer - INFO -    Question: How do the graduation rates of girls at ITC University compare to the overall graduation rates?
Answer: At ITC University, the graduation rate for girls is significantly higher than the overall graduation rate. Specifically, the female graduation rate is 54500%, while the overall graduation rate stands at 15%. This indicates that girls are graduating at a much higher rate compared to the total student population.
Data Tag: female_graduation_rate_vs_overall

Question: What programs or c...
2025-08-08 15:26:20,290 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:26:20,305 - app.chains.section_writer - INFO - 🤖 AI generated section (1756 chars):
2025-08-08 15:26:20,306 - app.chains.section_writer - INFO -    ## 8. Recommendations  

To support and enhance female students' performance at ITC University, several strategies can be implemented. First, it is essential to promote the successful programs where female students excel, such as the Bachelor of Arts in Journalism and Media Studies and the Bachelor ...
2025-08-08 15:26:20,306 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['female_graduation_rate_vs_overall']
2025-08-08 15:26:20,306 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 8 completed and processed: 1756 characters
2025-08-08 15:26:20,306 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 9/9...
2025-08-08 15:26:21,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:26:21,169 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:26:21,169 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 15:26:21,169 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:26:21,169 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'interview transcripts sources'
2025-08-08 15:26:21,169 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 15:26:21,169 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 15:26:21,406 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.237s]
2025-08-08 15:26:21,407 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 4
2025-08-08 15:26:21,646 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.239s]
2025-08-08 15:26:21,647 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 2
2025-08-08 15:26:21,889 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.242s]
2025-08-08 15:26:21,890 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-08 15:26:22,132 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.242s]
2025-08-08 15:26:22,132 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-08 15:26:22,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 15:26:22,961 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.246s]
2025-08-08 15:26:22,962 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-08 15:26:22,962 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:26:22,962 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:26:22,962 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:26:22,962 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:26:22,962 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 15:26:22,962 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:42.184113+00:00', 'data_returned': True}
2025-08-08 15:26:22,962 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 15:26:22,963 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T15:24:41.324799+00:00', 'data_returned': True, 'data_tag': 'female_graduation_rate_vs_overall'}
2025-08-08 15:26:22,963 - app.chains.section_writer - INFO -   ✅ Added Data Tag: female_graduation_rate_vs_overall
2025-08-08 15:26:22,963 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1334 chars):
2025-08-08 15:26:22,963 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls at ITC University are excelling in several programs and courses, with the highest average scores recorded in the following: 1) The Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics' with an average score of 86.0. 2) The Bachelor of Science in Clothing and Textiles Education, particularly in 'Introduction To Informa...
2025-08-08 15:26:25,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 15:26:25,136 - app.chains.section_writer - INFO - 🤖 AI generated section (354 chars):
2025-08-08 15:26:25,136 - app.chains.section_writer - INFO -    ## 9. References  
The following sources were utilized in the preparation of this report:

1. Interview transcripts from faculty and students at ITC University.
2. Academic performance data for female students in various programs.
3. Graduation rate statistics for girls compared to overall rates at ...
2025-08-08 15:26:25,136 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['female_graduation_rate_vs_overall']
2025-08-08 15:26:25,136 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 9 completed and processed: 354 characters
2025-08-08 15:26:25,137 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 15:26:25,137 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-08 15:26:25,137 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 15:26:25,137 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-08 15:26:25,138 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 9
2025-08-08 15:26:25,138 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 4
2025-08-08 15:26:25,138 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 0
2025-08-08 15:26:25,138 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_152321.log
2025-08-08 15:26:25,142 - celery.app.trace - INFO - Task generate_streaming_report[ef962551-7a1e-430e-83bf-a4c47d213808] succeeded in 183.26538554100262s: {'outline': '# Report on Girls\' Performance at ITC University

## 1. Introduction  
   - This report analyzes the academic performance of female students at ITC University, focusing on their achievements, challenges, and the support systems in place. The key finding indicates that girls are performing well in specific programs, with graduation rates significantly higher than the overall average.

## 2. Methodology  
   - 2.1 Data Collection Methods  
       - Surveys  
       - Academic Records Analysis  
       - Interviews with Faculty  
   - 2.2 Sample Size and Demographics  
   - 2.3 Limitations of the Study  

## 3. Academic Performance  
   ### 3.1 Excelling Programs and Courses  
   - Summary of programs where girls excel  
     - **Bachelor of Arts in Journalism and Media Studies**  
       - Course: Educational Research Methods, Assessment and Statistics  
       - Average Score: 86.0  
     - **Bachelor of Science in Clothing and Textiles Education**  
       - Course: Introduction To Information...', ...}
2025-08-08 16:24:26,704 - celery.worker.strategy - INFO - Task generate_streaming_report[8b5376b5-f0bd-4f3c-bbc2-a66b7e50d221] received
2025-08-08 16:24:26,705 - app.tasks.report_task - INFO - starting streaming report generation...
2025-08-08 16:24:26,708 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 16:24:26,708 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 179bc03c-389d-4f37-b219-b1d34b64698b
2025-08-08 16:24:26,708 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 16:24:26,708 - REPORT_REQUEST - INFO - 📝 Original Question: 'What institutions have the most students?'
2025-08-08 16:24:26,709 - REPORT_REQUEST - INFO - 🆔 Task ID: 179bc03c-389d-4f37-b219-b1d34b64698b
2025-08-08 16:24:26,709 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T16:24:26.709099
2025-08-08 16:24:36,714 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:N/A duration:10.003s]
2025-08-08 16:24:36,714 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://*************:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-08 16:24:36,715 - ELASTICSEARCH_STATE - ERROR - ❌ Error checking Elasticsearch state: Connection timed out
2025-08-08 16:24:36,715 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 16:24:36,715 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 16:24:36,715 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 16:24:36,715 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: What institutions have the most students?
2025-08-08 16:24:36,715 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-08 16:24:36,715 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 16:24:46,492 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:46,523 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 16:24:51,928 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:51,953 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-08 16:24:54,143 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:54,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:54,782 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:54,994 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:55,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:55,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:55,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:56,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:56,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:56,690 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:24:56,742 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-08 16:25:00,366 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:00,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:01,175 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:01,215 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:01,344 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:02,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:03,748 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:03,785 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:03,886 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:04,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:04,926 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:07,865 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:08,015 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:08,564 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:10,131 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:10,262 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:11,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:12,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:13,423 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:13,746 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:14,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:17,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:17,368 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total number of students enrolled at each institution?', 'sql': 'SELECT institutions.name, COUNT(students.id) AS total_students\nFROM institutions\nLEFT JOIN students ON institutions.id = students.institution_id\nGROUP BY institutions.name;', 'correct': True, 'reasoning': 'The SQL query correctly selects the name of each institution and counts the number of students associated with each institution by using a LEFT JOIN. This ensures that even institutions with no enrolled students are included in the result, which aligns with the requirement to show the total number of students enrolled at each institution. The use of GROUP BY on institutions.name is appropriate for aggregating the count of students per institution.', 'feedback': 'The SQL query is well-structured and accurately answers the question. No improvements are necessary, but it could be beneficial to include an ORDER BY clause to sort the results by total_students or institution name for better readability.'}
2025-08-08 16:25:17,369 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:25:17,369 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 16:25:18,051 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:18,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:18,901 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:19,604 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:20,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:20,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:23,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:25,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:25,400 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:25,681 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:25,907 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:25,913 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the institutions with the most students rank in terms of academic performance or graduation rates?', 'sql': "SELECT s.institution_id, COUNT(s.id) AS student_count, AVG(sg.gpa) AS average_gpa, COUNT(DISTINCT gb.id) AS graduation_count\nFROM students s\nLEFT JOIN student_semester_gpas sg ON s.id = sg.student_id\nLEFT JOIN graduation_batches gb ON s.institution_id = gb.institution_id AND gb.status = 'active'\nGROUP BY s.institution_id\nORDER BY student_count DESC;", 'correct': True, 'reasoning': "The SQL query correctly aggregates data by institution, counting the number of students, calculating the average GPA, and counting the number of active graduation batches. This aligns with the question's focus on ranking institutions based on student count and academic performance (average GPA) as well as graduation rates (graduation count). The use of LEFT JOINs ensures that institutions with no students or no active graduation batches are still included in the results, which is appropriate for a comprehensive analysis.", 'feedback': "The question could be clarified by specifying whether 'academic performance' refers solely to GPA or if other metrics should be included. Additionally, it might be beneficial to explicitly mention how graduation rates should be defined (e.g., percentage of students graduating versus total enrolled). The SQL could be improved by including a calculation for graduation rates if that metric is desired."}
2025-08-08 16:25:25,913 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:25:25,913 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 16:25:27,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:28,693 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:28,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:29,018 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:29,423 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:30,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:30,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:30,988 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:31,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:31,882 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:33,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:33,837 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:34,070 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:34,074 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific demographics or regions that are more represented in the student populations of these institutions?', 'sql': 'SELECT r.name AS region, n.name AS nationality, COUNT(s.id) AS student_count\nFROM students s\nJOIN nationalities n ON s.nationality_id = n.id\nJOIN regions r ON n.id = r.nationality_id\nGROUP BY r.name, n.name\nORDER BY student_count DESC;', 'correct': True, 'reasoning': "The SQL query correctly identifies the regions and nationalities of students by joining the 'students', 'nationalities', and 'regions' tables. It counts the number of students for each combination of region and nationality, which directly addresses the question about demographics and regions represented in the student populations. The use of GROUP BY and ORDER BY ensures that the results are aggregated and sorted by the number of students, making it easy to identify which demographics or regions are more represented.", 'feedback': "The question could be clarified by specifying what is meant by 'demographics'—whether it refers solely to nationality or includes other factors like age or gender. Additionally, the SQL could be improved by including more demographic factors if they are relevant to the analysis."}
2025-08-08 16:25:34,074 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:25:34,074 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 16:25:34,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:35,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:35,382 - celery.redirected - WARNING - ❌ Exception in SQL generation: 'core.staff_designations'
2025-08-08 16:25:35,386 - celery.redirected - WARNING - Traceback (most recent call last):
2025-08-08 16:25:35,386 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-backend/app/chains/composite/interview.py", line 32, in interview
    sql_query_evaluation = sql_gen_veri_chain.invoke(follow_up)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,386 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 4771, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,386 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 1940, in _call_with_config
    context.run(
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 4629, in _invoke
    output = call_func_with_variable_args(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-backend/app/chains/composite/sql_generation_and_verification.py", line 101, in sql_gen_veri
    question_schema_dict = relevant_tables_schemas_extractor_chain.invoke(question_tables_dict)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 4771, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 1940, in _call_with_config
    context.run(
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 4629, in _invoke
    output = call_func_with_variable_args(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-backend/app/chains/relevant_schema_info_filter.py", line 41, in <lambda>
    "tables_schema": get_relevant_tables_schema(output_from_first["tables"]),
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-08-08 16:25:35,387 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-backend/app/chains/relevant_schema_info_filter.py", line 15, in get_relevant_tables_schema
    relevant_tables_json[table] = db_json[table]
                                  ~~~~~~~^^^^^^^
2025-08-08 16:25:35,387 - celery.redirected - WARNING - KeyError: 'core.staff_designations'
2025-08-08 16:25:36,183 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:36,202 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to differences in enrollment numbers among institutions. However, the provided schema does not contain specific data or attributes that directly relate to enrollment numbers or the factors influencing them. While there are tables related to institutions and applicants, there is no clear linkage or data that would allow for an analysis of the factors affecting enrollment numbers. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:25:36,202 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:25:36,202 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:25:36,202 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to differences in enrollment numbers among institutions. However, the provided schema does not contain specific data or attributes that directly relate to enrollment numbers or the factors influencing them. While there are tables related to institutions and applicants, there is no clear linkage or data that would allow for an analysis of the factors affecting enrollment numbers. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:25:36,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:38,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:39,542 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:39,977 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:40,836 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:42,504 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:42,522 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:43,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:43,510 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to differences in enrollment numbers among institutions. While the schema contains various tables related to institutions, students, and their enrollment data, it does not provide specific attributes or metrics that directly correlate to the factors influencing enrollment numbers. Factors such as institutional reputation, program offerings, financial aid availability, and demographic trends are not explicitly represented in the schema. Therefore, without additional context or data points that could indicate these factors, the question cannot be answered.', 'feedback': ''}
2025-08-08 16:25:43,511 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:25:43,511 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:25:43,511 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to differences in enrollment numbers among institutions. While the schema contains various tables related to institutions, students, and their enrollment data, it does not provide specific attributes or metrics that directly correlate to the factors influencing enrollment numbers. Factors such as institutional reputation, program offerings, financial aid availability, and demographic trends are not explicitly represented in the schema. Therefore, without additional context or data points that could indicate these factors, the question cannot be answered.', 'feedback': ''}
2025-08-08 16:25:45,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:45,960 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What types of financial aid or scholarships are available to students at the institutions with the most students?', 'sql': 'SELECT DISTINCT s.type, s.source \nFROM student_scholarships s \nJOIN students st ON s.student_id = st.id \nJOIN institutions i ON s.institution_id = i.id \nWHERE i.id IN (\n    SELECT institution_id \n    FROM students \n    GROUP BY institution_id \n    ORDER BY COUNT(*) DESC \n    LIMIT 1\n)', 'correct': True, 'reasoning': 'The SQL query correctly identifies the types of financial aid or scholarships available to students at the institution with the highest number of students. It does this by first determining which institution has the most students through a subquery that counts students grouped by institution_id. The main query then selects distinct types and sources of scholarships from the student_scholarships table for that institution. The use of JOINs ensures that the data is correctly linked between the scholarships, students, and institutions.', 'feedback': 'The question could be clarified by specifying whether the focus is solely on the institution with the absolute highest number of students or if it should consider the top few institutions. Additionally, the SQL could be improved by including a more descriptive alias for the subquery to enhance readability.'}
2025-08-08 16:25:45,960 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:25:45,961 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 16:25:46,152 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:47,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:47,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:48,242 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:48,404 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:48,591 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:48,605 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to differences in enrollment numbers among institutions. While the schema contains various tables related to institutions, students, and programs, it does not provide specific data or attributes that directly correlate to the factors influencing enrollment numbers. Factors such as marketing strategies, local demographics, or institutional reputation are not represented in the schema. Therefore, without additional context or data, the question cannot be answered.', 'feedback': ''}
2025-08-08 16:25:48,605 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:25:48,605 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:25:48,606 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to differences in enrollment numbers among institutions. While the schema contains various tables related to institutions, students, and programs, it does not provide specific data or attributes that directly correlate to the factors influencing enrollment numbers. Factors such as marketing strategies, local demographics, or institutional reputation are not represented in the schema. Therefore, without additional context or data, the question cannot be answered.', 'feedback': ''}
2025-08-08 16:25:48,954 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:49,318 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:49,716 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:50,439 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:50,820 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:51,032 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:51,925 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:52,631 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:54,891 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:54,903 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to differences in enrollment numbers among institutions. However, the provided schema does not contain specific data or attributes that directly relate to enrollment numbers or the factors influencing them. While there are tables related to institutions and applicants, there is no clear linkage or data that would allow for an analysis of the factors affecting enrollment numbers. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:25:54,904 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:25:54,904 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:25:54,904 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to differences in enrollment numbers among institutions. However, the provided schema does not contain specific data or attributes that directly relate to enrollment numbers or the factors influencing them. While there are tables related to institutions and applicants, there is no clear linkage or data that would allow for an analysis of the factors affecting enrollment numbers. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:25:55,289 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:55,593 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:55,606 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance and graduation rates of such large institutions, and how might they differ from smaller colleges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore factors contributing to academic performance and graduation rates, which are not directly quantifiable or represented in the provided database schema. The schema contains various tables related to institutions, students, courses, and assessments, but it does not provide specific metrics or qualitative data that would allow for a comprehensive analysis of the factors influencing academic performance and graduation rates. Additionally, the question requires a comparative analysis between large institutions and smaller colleges, which would necessitate data that is not present in the schema.', 'feedback': ''}
2025-08-08 16:25:55,607 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:25:55,607 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:25:55,607 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance and graduation rates of such large institutions, and how might they differ from smaller colleges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore factors contributing to academic performance and graduation rates, which are not directly quantifiable or represented in the provided database schema. The schema contains various tables related to institutions, students, courses, and assessments, but it does not provide specific metrics or qualitative data that would allow for a comprehensive analysis of the factors influencing academic performance and graduation rates. Additionally, the question requires a comparative analysis between large institutions and smaller colleges, which would necessitate data that is not present in the schema.', 'feedback': ''}
2025-08-08 16:25:57,601 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:58,189 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:58,195 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Given the lack of specific data on financial aid or scholarships at large institutions, what general trends or common practices have you observed in how these institutions typically support their students financially?', 'answerable': False, 'reasoning': 'The question is asking for general trends or common practices regarding financial aid or scholarships at large institutions. However, the provided database schema does not contain any specific tables or fields that would provide insights into general trends or practices. The schema includes tables related to financial aid requests and student scholarships, but it lacks the necessary data or analytical structure to derive trends or common practices. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 16:25:58,195 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:25:58,195 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:25:58,195 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'Given the lack of specific data on financial aid or scholarships at large institutions, what general trends or common practices have you observed in how these institutions typically support their students financially?', 'answerable': False, 'reasoning': 'The question is asking for general trends or common practices regarding financial aid or scholarships at large institutions. However, the provided database schema does not contain any specific tables or fields that would provide insights into general trends or practices. The schema includes tables related to financial aid requests and student scholarships, but it lacks the necessary data or analytical structure to derive trends or common practices. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 16:25:58,505 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:25:59,203 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:00,417 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:00,915 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:01,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:01,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:01,575 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance and graduation rates of such large institutions, and how might they differ from smaller colleges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore factors contributing to academic performance and graduation rates, which are not directly quantifiable or represented in the provided database schema. The schema contains tables related to institutions, students, courses, and various academic records, but it does not provide specific metrics or qualitative data that would allow for a comprehensive analysis of the factors influencing academic performance and graduation rates. Additionally, the question compares large institutions to smaller colleges, which would require data on institutional size and context that is not present in the schema.', 'feedback': ''}
2025-08-08 16:26:01,576 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:01,576 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:01,577 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance and graduation rates of such large institutions, and how might they differ from smaller colleges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore factors contributing to academic performance and graduation rates, which are not directly quantifiable or represented in the provided database schema. The schema contains tables related to institutions, students, courses, and various academic records, but it does not provide specific metrics or qualitative data that would allow for a comprehensive analysis of the factors influencing academic performance and graduation rates. Additionally, the question compares large institutions to smaller colleges, which would require data on institutional size and context that is not present in the schema.', 'feedback': ''}
2025-08-08 16:26:02,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:03,301 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:04,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:04,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:05,119 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:05,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:06,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:06,799 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:06,820 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Given the lack of specific data on financial aid or scholarships at large institutions, what general trends or common practices have you observed in how these institutions typically support their students financially?', 'answerable': False, 'reasoning': 'The question is asking for general trends or common practices regarding financial aid or scholarships at large institutions. However, the provided database schema does not contain any specific tables or fields that would provide insights into general trends or practices. The schema includes tables related to financial aid requests and student scholarships, but it lacks the necessary data or context to derive general trends or practices across institutions. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 16:26:06,821 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:06,821 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:06,821 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'Given the lack of specific data on financial aid or scholarships at large institutions, what general trends or common practices have you observed in how these institutions typically support their students financially?', 'answerable': False, 'reasoning': 'The question is asking for general trends or common practices regarding financial aid or scholarships at large institutions. However, the provided database schema does not contain any specific tables or fields that would provide insights into general trends or practices. The schema includes tables related to financial aid requests and student scholarships, but it lacks the necessary data or context to derive general trends or practices across institutions. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 16:26:07,995 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:08,015 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to this uniform representation across the regions, and are there any specific programs or initiatives in place to support students from underrepresented areas?', 'answerable': False, 'reasoning': 'The question asks about factors contributing to uniform representation across regions and specific programs or initiatives supporting students from underrepresented areas. However, the provided schema does not contain any tables or fields that directly address these topics. There are tables related to institutions, students, programs, and various administrative aspects, but none that specifically discuss regional representation or initiatives aimed at supporting underrepresented students. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:26:08,016 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:08,016 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:08,016 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to this uniform representation across the regions, and are there any specific programs or initiatives in place to support students from underrepresented areas?', 'answerable': False, 'reasoning': 'The question asks about factors contributing to uniform representation across regions and specific programs or initiatives supporting students from underrepresented areas. However, the provided schema does not contain any tables or fields that directly address these topics. There are tables related to institutions, students, programs, and various administrative aspects, but none that specifically discuss regional representation or initiatives aimed at supporting underrepresented students. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:26:08,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:08,263 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance and graduation rates of such large institutions, and how might they differ from smaller colleges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore factors contributing to academic performance and graduation rates, which are not directly quantifiable or represented in the provided database schema. The schema contains tables related to institutions, students, courses, and various academic records, but it does not provide specific metrics or qualitative data that would allow for a comprehensive analysis of the factors influencing academic performance and graduation rates. Additionally, the question compares large institutions to smaller colleges, which would require data on both types of institutions, but the schema does not differentiate between institution sizes or provide comparative data.', 'feedback': ''}
2025-08-08 16:26:08,263 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:08,263 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:08,263 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance and graduation rates of such large institutions, and how might they differ from smaller colleges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore factors contributing to academic performance and graduation rates, which are not directly quantifiable or represented in the provided database schema. The schema contains tables related to institutions, students, courses, and various academic records, but it does not provide specific metrics or qualitative data that would allow for a comprehensive analysis of the factors influencing academic performance and graduation rates. Additionally, the question compares large institutions to smaller colleges, which would require data on both types of institutions, but the schema does not differentiate between institution sizes or provide comparative data.', 'feedback': ''}
2025-08-08 16:26:09,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:09,494 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:10,400 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:11,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:11,398 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:11,413 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Given the lack of specific data on financial aid or scholarships at large institutions, what general trends or common practices have you observed in how these institutions typically support their students financially?', 'answerable': False, 'reasoning': 'The question is asking for general trends or common practices regarding financial aid or scholarships at large institutions. However, the provided database schema does not contain any specific tables or fields that would provide insights into general trends or practices. The schema includes tables related to financial aid requests and student scholarships, but it lacks the necessary data or context to derive general trends or practices. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 16:26:11,413 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:11,413 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:11,413 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'Given the lack of specific data on financial aid or scholarships at large institutions, what general trends or common practices have you observed in how these institutions typically support their students financially?', 'answerable': False, 'reasoning': 'The question is asking for general trends or common practices regarding financial aid or scholarships at large institutions. However, the provided database schema does not contain any specific tables or fields that would provide insights into general trends or practices. The schema includes tables related to financial aid requests and student scholarships, but it lacks the necessary data or context to derive general trends or practices. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 16:26:12,129 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:12,551 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:12,570 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to this uniform representation across the regions, and are there any specific programs or initiatives in place to support students from underrepresented areas?', 'answerable': False, 'reasoning': 'The question asks about factors contributing to uniform representation across regions and specific programs or initiatives supporting students from underrepresented areas. However, the provided schema does not contain any tables or fields that directly address these topics. There are tables related to institutions, students, programs, and various administrative aspects, but none that specifically discuss regional representation or initiatives aimed at supporting underrepresented students. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:26:12,570 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:12,570 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:12,570 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to this uniform representation across the regions, and are there any specific programs or initiatives in place to support students from underrepresented areas?', 'answerable': False, 'reasoning': 'The question asks about factors contributing to uniform representation across regions and specific programs or initiatives supporting students from underrepresented areas. However, the provided schema does not contain any tables or fields that directly address these topics. There are tables related to institutions, students, programs, and various administrative aspects, but none that specifically discuss regional representation or initiatives aimed at supporting underrepresented students. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:26:13,021 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:13,952 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:14,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:14,256 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance and graduation rates of such large institutions, and how might they differ from smaller colleges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore factors contributing to academic performance and graduation rates, which are not directly quantifiable or represented in the provided database schema. The schema contains tables related to institutions, students, courses, and various academic records, but it does not provide specific metrics or qualitative data that would allow for a comprehensive analysis of the factors influencing academic performance and graduation rates. Additionally, the question compares large institutions to smaller colleges, which would require data on institutional size and context that is not present in the schema.', 'feedback': ''}
2025-08-08 16:26:14,257 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:14,257 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:14,257 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance and graduation rates of such large institutions, and how might they differ from smaller colleges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore factors contributing to academic performance and graduation rates, which are not directly quantifiable or represented in the provided database schema. The schema contains tables related to institutions, students, courses, and various academic records, but it does not provide specific metrics or qualitative data that would allow for a comprehensive analysis of the factors influencing academic performance and graduation rates. Additionally, the question compares large institutions to smaller colleges, which would require data on institutional size and context that is not present in the schema.', 'feedback': ''}
2025-08-08 16:26:14,944 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:15,273 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:16,261 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:16,273 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Given the lack of specific data on financial aid or scholarships at large institutions, what general trends or common practices have you observed in how these institutions typically support their students financially?', 'answerable': False, 'reasoning': 'The question is asking for general trends or common practices regarding financial aid or scholarships at large institutions. However, the provided database schema does not contain any specific tables or fields that would provide insights into general trends or practices. The schema includes tables related to financial aid requests and student scholarships, but it lacks the necessary data or analytical structure to derive trends or common practices. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 16:26:16,274 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:16,274 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:16,274 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'Given the lack of specific data on financial aid or scholarships at large institutions, what general trends or common practices have you observed in how these institutions typically support their students financially?', 'answerable': False, 'reasoning': 'The question is asking for general trends or common practices regarding financial aid or scholarships at large institutions. However, the provided database schema does not contain any specific tables or fields that would provide insights into general trends or practices. The schema includes tables related to financial aid requests and student scholarships, but it lacks the necessary data or analytical structure to derive trends or common practices. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 16:26:17,995 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:18,013 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to this uniform representation across the regions, and are there any specific programs or initiatives in place to support students from underrepresented areas?', 'answerable': False, 'reasoning': 'The question asks about factors contributing to uniform representation across regions and specific programs or initiatives supporting students from underrepresented areas. However, the provided schema does not contain any tables or fields that directly address these topics. There are tables related to institutions, students, programs, and various administrative aspects, but none that specifically detail factors of representation or initiatives for underrepresented areas. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:26:18,013 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:18,013 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:18,013 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to this uniform representation across the regions, and are there any specific programs or initiatives in place to support students from underrepresented areas?', 'answerable': False, 'reasoning': 'The question asks about factors contributing to uniform representation across regions and specific programs or initiatives supporting students from underrepresented areas. However, the provided schema does not contain any tables or fields that directly address these topics. There are tables related to institutions, students, programs, and various administrative aspects, but none that specifically detail factors of representation or initiatives for underrepresented areas. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:26:19,351 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:19,373 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the student-to-faculty ratio at the institutions with the most students?', 'sql': "SELECT i.NAME AS institution_name, COUNT(DISTINCT s.id) AS student_count, COUNT(DISTINCT st.id) AS faculty_count, COUNT(DISTINCT s.id) / NULLIF(COUNT(DISTINCT st.id), 0) AS student_to_faculty_ratio\nFROM institutions i\nJOIN students s ON s.institution_id = i.id\nJOIN staff st ON st.designation_id IN (SELECT id FROM core.staff_designations WHERE designation_type = 'faculty')\nGROUP BY i.id, i.NAME\nORDER BY student_count DESC\nLIMIT 1;", 'correct': True, 'reasoning': "The SQL query correctly calculates the student-to-faculty ratio by first counting the number of distinct students and faculty members at each institution. It uses a JOIN to connect the students and staff tables to the institutions table, ensuring that the counts are specific to each institution. The use of NULLIF prevents division by zero when calculating the ratio. The query also orders the results by student count in descending order and limits the output to the institution with the most students, which aligns with the question's requirement.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly naming the output columns for clarity, such as 'student_to_faculty_ratio' instead of just calculating it without a clear label. Additionally, if the question were to ask for the ratios of multiple institutions, the LIMIT clause would need to be adjusted."}
2025-08-08 16:26:19,373 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:19,373 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 16:26:20,209 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:20,512 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:21,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:22,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:23,022 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:23,043 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to this uniform representation across the regions, and are there any specific programs or initiatives in place to support students from underrepresented areas?', 'answerable': False, 'reasoning': 'The question asks about factors contributing to a uniform representation across regions and specific programs or initiatives to support students from underrepresented areas. However, the provided schema does not contain any tables or fields that directly address these topics. There are tables related to institutions, students, programs, and various administrative aspects, but none that specifically detail factors influencing representation or initiatives aimed at supporting underrepresented students. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:26:23,044 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:23,044 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:23,044 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to this uniform representation across the regions, and are there any specific programs or initiatives in place to support students from underrepresented areas?', 'answerable': False, 'reasoning': 'The question asks about factors contributing to a uniform representation across regions and specific programs or initiatives to support students from underrepresented areas. However, the provided schema does not contain any tables or fields that directly address these topics. There are tables related to institutions, students, programs, and various administrative aspects, but none that specifically detail factors influencing representation or initiatives aimed at supporting underrepresented students. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 16:26:23,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:26,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:27,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:30,898 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:30,962 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the variability in student-to-faculty ratios across different institutions, especially those with large student populations?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to variability in student-to-faculty ratios across different institutions, particularly those with large student populations. However, the provided schema does not contain any data or tables that directly relate to student-to-faculty ratios or the factors influencing them. The schema includes various tables related to students, faculty, courses, and institutions, but it lacks specific metrics or attributes that would allow for a comprehensive analysis of the ratios or the factors affecting them. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 16:26:30,962 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:30,962 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:30,962 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the variability in student-to-faculty ratios across different institutions, especially those with large student populations?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to variability in student-to-faculty ratios across different institutions, particularly those with large student populations. However, the provided schema does not contain any data or tables that directly relate to student-to-faculty ratios or the factors influencing them. The schema includes various tables related to students, faculty, courses, and institutions, but it lacks specific metrics or attributes that would allow for a comprehensive analysis of the ratios or the factors affecting them. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 16:26:31,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:33,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:34,193 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:35,867 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:36,306 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:36,332 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the variability in student-to-faculty ratios across different institutions, especially those with large student populations?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to variability in student-to-faculty ratios across different institutions, particularly those with large student populations. However, the provided schema does not contain any data or attributes related to faculty counts, student populations, or any metrics that would allow for such an analysis. The schema primarily focuses on student records, admissions, courses, and related entities, but lacks the necessary information about faculty or institutional metrics that would be required to answer the question.', 'feedback': ''}
2025-08-08 16:26:36,332 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:36,332 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:36,332 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the variability in student-to-faculty ratios across different institutions, especially those with large student populations?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to variability in student-to-faculty ratios across different institutions, particularly those with large student populations. However, the provided schema does not contain any data or attributes related to faculty counts, student populations, or any metrics that would allow for such an analysis. The schema primarily focuses on student records, admissions, courses, and related entities, but lacks the necessary information about faculty or institutional metrics that would be required to answer the question.', 'feedback': ''}
2025-08-08 16:26:38,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:40,914 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:40,932 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the variability in student-to-faculty ratios across different institutions, especially those with large student populations?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to variability in student-to-faculty ratios across different institutions, particularly those with large student populations. This requires qualitative insights and potentially external data regarding institutional policies, faculty hiring practices, and student enrollment trends, none of which are directly represented in the provided schema. The schema contains data about institutions, students, and faculty, but it does not provide the necessary contextual or analytical data to answer the question comprehensively.', 'feedback': ''}
2025-08-08 16:26:40,932 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:40,932 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:26:40,932 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the variability in student-to-faculty ratios across different institutions, especially those with large student populations?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to variability in student-to-faculty ratios across different institutions, particularly those with large student populations. This requires qualitative insights and potentially external data regarding institutional policies, faculty hiring practices, and student enrollment trends, none of which are directly represented in the provided schema. The schema contains data about institutions, students, and faculty, but it does not provide the necessary contextual or analytical data to answer the question comprehensively.', 'feedback': ''}
2025-08-08 16:26:43,601 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:52,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:52,448 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are most popular at the institutions with the most students?', 'sql': "WITH InstitutionStudentCounts AS (  SELECT institution_id, COUNT(DISTINCT id) AS student_count  FROM core.students  WHERE status = 'active'  GROUP BY institution_id), PopularPrograms AS (  SELECT p.id AS program_id, p.long_name, COUNT(sp.student_id) AS enrollment_count  FROM core.programs p  JOIN core.student_programs sp ON p.id = sp.program_id  JOIN InstitutionStudentCounts isc ON p.institution_id = isc.institution_id  GROUP BY p.id, p.long_name  ORDER BY enrollment_count DESC), PopularCourses AS (  SELECT c.id AS course_id, c.title, COUNT(sp.student_id) AS enrollment_count  FROM core.courses c  JOIN core.student_programs sp ON c.unit_id = sp.unit_id  JOIN InstitutionStudentCounts isc ON sp.institution_id = isc.institution_id  GROUP BY c.id, c.title  ORDER BY enrollment_count DESC) SELECT 'Program' AS type, program_id AS id, long_name AS name, enrollment_count FROM PopularPrograms UNION ALL SELECT 'Course' AS type, course_id AS id, title AS name, enrollment_count FROM PopularCourses;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the most popular programs and courses at institutions with the highest number of active students. It first counts the number of active students per institution, then calculates the enrollment counts for both programs and courses associated with those institutions. The use of CTEs (Common Table Expressions) allows for clear separation of logic, and the final UNION combines the results for programs and courses as required by the question.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly limiting the results to only the top institutions based on student count, which would provide a clearer focus on the most popular programs and courses at those specific institutions.'}
2025-08-08 16:26:52,448 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:26:52,448 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 16:26:54,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:55,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:56,394 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:26:58,468 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:27:01,559 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:27:01,591 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of standout programs or courses at these large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of standout programs or courses at large institutions. This type of inquiry requires subjective analysis and interpretation of various elements such as institutional culture, faculty engagement, student feedback, and market trends, none of which can be directly derived from the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include qualitative assessments or opinions that would be necessary to answer the question.', 'feedback': ''}
2025-08-08 16:27:01,592 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:27:01,592 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:27:01,592 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of standout programs or courses at these large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of standout programs or courses at large institutions. This type of inquiry requires subjective analysis and interpretation of various elements such as institutional culture, faculty engagement, student feedback, and market trends, none of which can be directly derived from the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include qualitative assessments or opinions that would be necessary to answer the question.', 'feedback': ''}
2025-08-08 16:27:03,890 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:27:06,101 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:27:06,113 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of standout programs or courses at these large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of standout programs or courses at large institutions. This type of inquiry typically requires subjective analysis, opinions, or qualitative data that is not represented in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include qualitative assessments or factors that would explain why certain programs may not stand out. Therefore, the question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 16:27:06,113 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:27:06,113 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:27:06,113 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of standout programs or courses at these large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of standout programs or courses at large institutions. This type of inquiry typically requires subjective analysis, opinions, or qualitative data that is not represented in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include qualitative assessments or factors that would explain why certain programs may not stand out. Therefore, the question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 16:27:07,895 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:27:10,057 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:27:10,077 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of standout programs or courses at these large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of standout programs or courses at large institutions. This type of inquiry requires subjective analysis and interpretation of various elements such as institutional culture, faculty engagement, student feedback, and market trends, none of which can be directly derived from the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include qualitative assessments or opinions that would be necessary to answer the question.', 'feedback': ''}
2025-08-08 16:27:10,077 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:27:10,077 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:27:10,077 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of standout programs or courses at these large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of standout programs or courses at large institutions. This type of inquiry requires subjective analysis and interpretation of various elements such as institutional culture, faculty engagement, student feedback, and market trends, none of which can be directly derived from the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include qualitative assessments or opinions that would be necessary to answer the question.', 'feedback': ''}
2025-08-08 16:27:12,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:27:14,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:27:14,273 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of standout programs or courses at these large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of standout programs or courses at large institutions. This type of inquiry typically requires subjective analysis, opinions, or qualitative data that is not represented in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include qualitative assessments or factors that would explain why certain programs may not stand out. Therefore, the question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 16:27:14,274 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 16:27:14,274 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 16:27:14,274 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of standout programs or courses at these large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of standout programs or courses at large institutions. This type of inquiry typically requires subjective analysis, opinions, or qualitative data that is not represented in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include qualitative assessments or factors that would explain why certain programs may not stand out. Therefore, the question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 16:27:14,274 - root - INFO - [{'name': 'Accra College Of Medicine', 'total_students': 114}, {'name': 'Akenten Appiah-Menka University of Skills Training and Entrepreneurial Development', 'total_students': 1}, {'name': 'Akrofi-Christaller Institute', 'total_students': 262}, {'name': 'Central University', 'total_students': 49153}, {'name': 'Ghana Institute of Languages', 'total_students': 18552}, {'name': 'Ghana School of Law', 'total_students': 13012}, {'name': 'GNAT Institute for Research and Industrial Relations Studies', 'total_students': 0}, {'name': 'Institute of Local Government Studies', 'total_students': 0}, {'name': 'ITC College', 'total_students': 0}, {'name': 'ITC University', 'total_students': 192627}, {'name': 'Klintaps College of Health and Allied Sciences', 'total_students': 1135}, {'name': 'Knutsford University College', 'total_students': 0}, {'name': 'Koforidua Technical University', 'total_students': 0}, {'name': 'Methodist University College Ghana', 'total_students': 31258}, {'name': 'Palm Institute', 'total_students': 169}, {'name': 'Presbyterian University College Ghana', 'total_students': 17758}, {'name': 'Public Health Nurses School, Korle Bu', 'total_students': 1065}, {'name': 'Trinity Theological Seminary', 'total_students': 1202}, {'name': 'University Of Education, Winneba', 'total_students': 0}, {'name': 'University of Health and Allied Sciences', 'total_students': 0}, {'name': 'University of Rwanda', 'total_students': 0}, {'name': 'Wisconsin International University College, Ghana', 'total_students': 32094}]
2025-08-08 16:27:14,274 - root - INFO - [{'region': 'Ahafo', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Ashanti', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Bono East', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Brong Ahafo', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Central', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Eastern', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Greater Accra', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'North East', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Northern', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Oti', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Savannah', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Upper East', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Upper West', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Volta', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Western', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Western North', 'nationality': 'Ghana', 'student_count': 368496}]
2025-08-08 16:27:14,274 - root - INFO - [{'institution_id': 24, 'student_count': 4157181, 'average_gpa': 2.87057, 'graduation_count': 21}, {'institution_id': 1, 'student_count': 49153, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 11, 'student_count': 32094, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 10, 'student_count': 31258, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 17, 'student_count': 22111, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 7, 'student_count': 18552, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 5, 'student_count': 17758, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 3, 'student_count': 13012, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 13, 'student_count': 1202, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 30, 'student_count': 1135, 'average_gpa': None, 'graduation_count': 1}, {'institution_id': 9, 'student_count': 1065, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 4, 'student_count': 262, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 16, 'student_count': 202, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 8, 'student_count': 169, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 2, 'student_count': 114, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 29, 'student_count': 52, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 18, 'student_count': 42, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 12, 'student_count': 1, 'average_gpa': None, 'graduation_count': 0}]
2025-08-08 16:27:14,274 - root - INFO - 'No results'
2025-08-08 16:27:14,274 - root - INFO - 'No results'
2025-08-08 16:27:14,275 - root - INFO - 'No results'
2025-08-08 16:27:14,275 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 16:27:30,208 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:27:30,224 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 16:27:45,610 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 16:27:45,628 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 16:27:45,628 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 16:27:45,628 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 16:27:45,629 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institutions have the most students?'
2025-08-08 16:27:45,629 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 16:27:45,629 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 16:27:45,629 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 16:27:45,629 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 16:27:45,629 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total number of students enrolled at each institution?...
2025-08-08 16:27:45,629 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total number of students enrolled at each institution is as follows: Accra College Of Medicine h...
2025-08-08 16:27:45,629 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-08 16:27:45,629 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'name': 'Accra College Of Medicine', 'total_students': 114}, {'name': 'Akenten Appiah-Menka Univer...
2025-08-08 16:27:45,631 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_students_enrolled_by_institution
2025-08-08 16:27:45,635 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 16:27:45,636 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 16:27:45,639 - celery.redirected - WARNING - [{'name': 'Accra College Of Medicine', 'total_students': 114}, {'name': 'Akenten Appiah-Menka University of Skills Training and Entrepreneurial Development', 'total_students': 1}, {'name': 'Akrofi-Christaller Institute', 'total_students': 262}, {'name': 'Central University', 'total_students': 49153}, {'name': 'Ghana Institute of Languages', 'total_students': 18552}, {'name': 'Ghana School of Law', 'total_students': 13012}, {'name': 'GNAT Institute for Research and Industrial Relations Studies', 'total_students': 0}, {'name': 'Institute of Local Government Studies', 'total_students': 0}, {'name': 'ITC College', 'total_students': 0}, {'name': 'ITC University', 'total_students': 192627}, {'name': 'Klintaps College of Health and Allied Sciences', 'total_students': 1135}, {'name': 'Knutsford University College', 'total_students': 0}, {'name': 'Koforidua Technical University', 'total_students': 0}, {'name': 'Methodist University College Ghana', 'total_students': 31258}, {'name': 'Palm Institute', 'total_students': 169}, {'name': 'Presbyterian University College Ghana', 'total_students': 17758}, {'name': 'Public Health Nurses School, Korle Bu', 'total_students': 1065}, {'name': 'Trinity Theological Seminary', 'total_students': 1202}, {'name': 'University Of Education, Winneba', 'total_students': 0}, {'name': 'University of Health and Allied Sciences', 'total_students': 0}, {'name': 'University of Rwanda', 'total_students': 0}, {'name': 'Wisconsin International University College, Ghana', 'total_students': 32094}]
2025-08-08 16:27:45,640 - celery.redirected - WARNING - ================================= 
2025-08-08 16:27:45,643 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: total_students_enrolled_by_institution
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 16:27:45,644 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 16:27:45,644 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 16:27:45,644 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institutions have the most students?'
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 16:27:45,644 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are most popular at the institutions with the most students?...
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no results available for the programs or courses at the institutions with ...
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: popular_courses_at_top_institutions
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 16:27:45,644 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 16:27:45,644 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 16:27:45,644 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 16:27:45,644 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 16:27:45,645 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institutions have the most students?'
2025-08-08 16:27:45,645 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 16:27:45,645 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 16:27:45,645 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 16:27:45,645 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 16:27:45,645 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific demographics or regions that are more represented in the student populations of t...
2025-08-08 16:27:45,645 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The student populations of these institutions are predominantly represented by individuals from vari...
2025-08-08 16:27:45,645 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 16:27:45,645 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'region': 'Ahafo', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Ashanti', 'nation...
2025-08-08 16:27:45,645 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_population_by_region
2025-08-08 16:27:45,645 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 16:27:45,645 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 16:27:45,645 - celery.redirected - WARNING - [{'region': 'Ahafo', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Ashanti', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Bono East', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Brong Ahafo', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Central', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Eastern', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Greater Accra', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'North East', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Northern', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Oti', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Savannah', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Upper East', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Upper West', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Volta', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Western', 'nationality': 'Ghana', 'student_count': 368496}, {'region': 'Western North', 'nationality': 'Ghana', 'student_count': 368496}]
2025-08-08 16:27:45,646 - celery.redirected - WARNING - ================================= 
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 16:27:45,646 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 16:27:45,646 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 16:27:45,646 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institutions have the most students?'
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 16:27:45,646 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the student-to-faculty ratio at the institutions with the most students?...
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no results available for the student-to-faculty ratio at the institutions ...
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_to_faculty_ratio_no_data
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 16:27:45,646 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 16:27:45,647 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 16:27:45,647 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 16:27:45,647 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 16:27:45,647 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institutions have the most students?'
2025-08-08 16:27:45,647 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 16:27:45,647 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 16:27:45,647 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 16:27:45,647 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 16:27:45,647 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the institutions with the most students rank in terms of academic performance or graduation r...
2025-08-08 16:27:45,647 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the most students is institution ID 24, which has a total of 4,157,181 students...
2025-08-08 16:27:45,648 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 16:27:45,648 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_id': 24, 'student_count': 4157181, 'average_gpa': 2.87057, 'graduation_count': 21}, {...
2025-08-08 16:27:45,648 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: institution_student_performance_summary
2025-08-08 16:27:45,648 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 16:27:45,648 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 16:27:45,649 - celery.redirected - WARNING - [{'institution_id': 24, 'student_count': 4157181, 'average_gpa': 2.87057, 'graduation_count': 21}, {'institution_id': 1, 'student_count': 49153, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 11, 'student_count': 32094, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 10, 'student_count': 31258, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 17, 'student_count': 22111, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 7, 'student_count': 18552, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 5, 'student_count': 17758, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 3, 'student_count': 13012, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 13, 'student_count': 1202, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 30, 'student_count': 1135, 'average_gpa': None, 'graduation_count': 1}, {'institution_id': 9, 'student_count': 1065, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 4, 'student_count': 262, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 16, 'student_count': 202, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 8, 'student_count': 169, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 2, 'student_count': 114, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 29, 'student_count': 52, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 18, 'student_count': 42, 'average_gpa': None, 'graduation_count': 0}, {'institution_id': 12, 'student_count': 1, 'average_gpa': None, 'graduation_count': 0}]
2025-08-08 16:27:45,649 - celery.redirected - WARNING - ================================= 
2025-08-08 16:27:45,649 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 16:27:45,649 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 16:27:45,649 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 16:27:45,649 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 16:27:45,649 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institutions have the most students?'
2025-08-08 16:27:45,649 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 16:27:45,649 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 16:27:45,649 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 16:27:45,649 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 16:27:45,649 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What types of financial aid or scholarships are available to students at the institutions with the m...
2025-08-08 16:27:45,650 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Unfortunately, there are no available results regarding the types of financial aid or scholarships f...
2025-08-08 16:27:45,650 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 16:27:45,650 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 16:27:45,650 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: financial_aid_scholarships_availability
2025-08-08 16:27:45,650 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 16:27:45,650 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 16:27:45,650 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 6
2025-08-08 16:27:45,650 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 16:27:45,651 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 16:27:45,651 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 16:27:45,651 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 6 documents
2025-08-08 16:27:45,651 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 16:27:45,651 - UPSERT_DOCS - INFO -     Content: Question: What is the total number of students enrolled at each institution?
Answer: The total numbe...
2025-08-08 16:27:45,651 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institutions have the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T16:25:54.904484+00:00', 'data_returned': True, 'data_tag': 'total_students_enrolled_by_institution'}
2025-08-08 16:27:45,651 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 16:27:45,651 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are most popular at the institutions with the most students?
Answ...
2025-08-08 16:27:45,651 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institutions have the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T16:27:14.274274+00:00', 'data_returned': False}
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -     Content: Question: Are there specific demographics or regions that are more represented in the student popula...
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institutions have the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T16:26:23.044430+00:00', 'data_returned': True}
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -     Content: Question: What is the student-to-faculty ratio at the institutions with the most students?
Answer: I...
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institutions have the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T16:26:40.932517+00:00', 'data_returned': False}
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -     Content: Question: How do the institutions with the most students rank in terms of academic performance or gr...
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institutions have the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T16:26:14.257261+00:00', 'data_returned': True}
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -     Content: Question: What types of financial aid or scholarships are available to students at the institutions ...
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institutions have the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T16:26:16.274383+00:00', 'data_returned': False}
2025-08-08 16:27:45,652 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 3/6
2025-08-08 16:27:55,660 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:N/A duration:10.004s]
2025-08-08 16:27:55,660 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://*************:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-08 16:27:55,660 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: Connection timed out
2025-08-08 16:27:55,661 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 16:27:55,661 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-08 16:27:55,661 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 16:27:55,661 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: Connection timed out
2025-08-08 16:27:55,661 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_152321.log
2025-08-08 16:27:55,665 - celery.app.trace - INFO - Task generate_streaming_report[8b5376b5-f0bd-4f3c-bbc2-a66b7e50d221] succeeded in 208.8380741660003s: {'error': 'Error generating streaming report: Connection timed out'}

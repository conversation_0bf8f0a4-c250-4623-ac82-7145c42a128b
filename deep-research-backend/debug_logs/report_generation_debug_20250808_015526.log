2025-08-08 01:55:26,512 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_015526.log
2025-08-08 01:55:26,512 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:55:26,513 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: f477f1a3-9715-46ce-a5a0-4acb0780eff2
2025-08-08 01:55:26,513 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:55:26,513 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:55:26,513 - REPORT_REQUEST - INFO - 🆔 Task ID: f477f1a3-9715-46ce-a5a0-4acb0780eff2
2025-08-08 01:55:26,513 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T01:55:26.513118
2025-08-08 01:55:26,767 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.255s]
2025-08-08 01:55:26,768 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 43
2025-08-08 01:55:27,069 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.301s]
2025-08-08 01:55:27,070 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:55:27,968 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:0.898s]
2025-08-08 01:55:27,968 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (4):
2025-08-08 01:55:27,968 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (28 docs)
2025-08-08 01:55:27,968 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (7 docs)
2025-08-08 01:55:27,968 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 01:55:27,968 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing in ITC University?' (1 docs)
2025-08-08 01:55:27,968 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 01:55:37,731 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:37,756 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 01:55:41,524 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:41,547 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 01:55:43,998 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:43,999 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:44,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:44,156 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:44,359 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:44,393 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:44,444 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:44,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:44,920 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:45,439 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:45,502 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 01:55:45,502 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:55:45,502 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 01:55:45,502 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:55:45,502 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 4
2025-08-08 01:55:48,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:48,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:48,810 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:48,864 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:51,473 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:51,474 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:52,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:52,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:53,316 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:53,930 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:54,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:56,114 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:59,002 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:59,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:55:59,665 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:01,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:04,150 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:04,156 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any trends in the performance of girls at ITC University over the past few years?', 'sql': "SELECT s.sex, sem.start_year, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS total_assessments\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN semesters sem ON ar.semester_id = sem.id\nWHERE s.sex = 'F' AND sem.status = 'Ended'\nGROUP BY s.sex, sem.start_year\nORDER BY sem.start_year;", 'correct': True, 'reasoning': "The SQL query correctly identifies the performance trends of female students ('F') at ITC University by calculating the average final score and the total number of assessments for each academic year. It joins the necessary tables (assessment_results, students, and semesters) to gather the required data, filters for completed semesters, and groups the results by year, which aligns with the question's focus on trends over the past few years.", 'feedback': 'The query is well-structured and addresses the question effectively. However, it could be enhanced by specifying the institution_id in the WHERE clause to ensure that the results are limited to ITC University, assuming that the schema allows for multiple institutions.'}
2025-08-08 01:56:04,156 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:04,156 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 01:56:04,275 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:04,282 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the performance of girls at ITC University vary by academic year or semester?', 'sql': "SELECT ay.start_year, s.sex, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS total_assessments\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN semesters sem ON ar.semester_id = sem.id\nJOIN academic_years ay ON sem.academic_year_id = ay.id\nWHERE s.sex = 'F' AND ay.status = 'Active'\nGROUP BY ay.start_year, s.sex, sem.period\nORDER BY ay.start_year, sem.period;", 'correct': True, 'reasoning': "The SQL query correctly identifies the performance of female students (girls) by calculating the average final score and total assessments for each academic year and semester. It joins the necessary tables to gather data on students, their assessment results, and the corresponding academic years and semesters. The filtering condition ensures that only active academic years and female students are considered, which aligns with the question's focus.", 'feedback': 'The query is well-structured and addresses the question effectively. However, it could be improved by explicitly including the semester information in the SELECT clause to clarify the results further. Additionally, consider adding a condition to filter by a specific institution if the schema supports it, to ensure the results are relevant to ITC University.'}
2025-08-08 01:56:04,283 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:04,283 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 01:56:05,220 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:05,225 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic performance levels of girls compare to those of boys at ITC University?', 'sql': "SELECT s.sex, AVG(sg.gpa) AS average_gpa, COUNT(s.id) AS student_count\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.student_semester_gpas sg ON s.id = sg.student_id\nWHERE sp.institution_id = 'ITC University' AND sg.status = 'active'\nGROUP BY s.sex;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average GPA and student count for each sex (girls and boys) at ITC University. It joins the necessary tables: 'students', 'student_programs', and 'student_semester_gpas', ensuring that only active students from the specified institution are included. The grouping by 's.sex' allows for a comparison between the academic performance levels of girls and boys, which directly addresses the question.", 'feedback': 'The question could be clarified by specifying whether the comparison should include only current students or all students, and whether any specific semesters or academic years should be considered. The SQL could be improved by adding a specific semester or academic year filter if that is relevant to the analysis.'}
2025-08-08 01:56:05,226 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:05,226 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 01:56:06,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:07,279 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:07,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:07,498 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in the most?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(ar.finalscore) AS average_score\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.assessment_results ar ON sp.id = ar.student_program_id\nJOIN core.courses c ON ar.course_id = c.id\nWHERE s.sex = 'F' AND s.institution_id = 'ITC University'\nGROUP BY p.long_name, c.title\nORDER BY average_score DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses that female students at ITC University are excelling in by calculating the average score of assessment results for each program and course combination. It filters the students by sex and institution, groups the results by program and course, and orders them by average score in descending order, which aligns with the question's request for identifying areas of excellence.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be clarified that 'excelling' refers to having the highest average scores. Additionally, consider adding a limit to the results to focus on the top programs or courses, which might make the output more concise and relevant."}
2025-08-08 01:56:07,498 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:07,498 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 01:56:07,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:08,510 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:08,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:08,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:09,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:09,914 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:10,509 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:10,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:11,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:11,697 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:12,408 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:14,614 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:14,630 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it does not provide insights into the qualitative factors or specific performance data for girls at a particular institution. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 01:56:14,630 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:14,631 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:14,631 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it does not provide insights into the qualitative factors or specific performance data for girls at a particular institution. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 01:56:14,852 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:15,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:15,232 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific metrics or data points would be necessary to effectively compare the academic performance of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific metrics or data points necessary to compare the academic performance of girls and boys at ITC University. However, the schema does not provide explicit information about gender-specific academic performance metrics or how to derive them. While there are tables related to students, courses, and assessments, the schema lacks direct references to gender-based performance comparisons or specific metrics that would facilitate such an analysis. Therefore, without additional context or data points explicitly defined in the schema, the question cannot be answered.', 'feedback': ''}
2025-08-08 01:56:15,232 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:15,232 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:15,233 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific metrics or data points would be necessary to effectively compare the academic performance of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific metrics or data points necessary to compare the academic performance of girls and boys at ITC University. However, the schema does not provide explicit information about gender-specific academic performance metrics or how to derive them. While there are tables related to students, courses, and assessments, the schema lacks direct references to gender-based performance comparisons or specific metrics that would facilitate such an analysis. Therefore, without additional context or data points explicitly defined in the schema, the question cannot be answered.', 'feedback': ''}
2025-08-08 01:56:16,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:17,204 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:18,506 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:18,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:19,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:19,031 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of recorded achievements for female students at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of recorded achievements for female students at ITC University. However, the provided schema does not contain any specific data or attributes related to gender-based achievements or the reasons behind them. While there are tables related to students, their programs, and various achievements, there is no direct link or data that would allow for an analysis of the factors affecting female students' achievements specifically. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-08 01:56:19,031 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:19,031 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:19,032 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of recorded achievements for female students at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of recorded achievements for female students at ITC University. However, the provided schema does not contain any specific data or attributes related to gender-based achievements or the reasons behind them. While there are tables related to students, their programs, and various achievements, there is no direct link or data that would allow for an analysis of the factors affecting female students' achievements specifically. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-08 01:56:19,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:19,748 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it does not provide insights into the qualitative factors or specific performance data for girls at a particular institution. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 01:56:19,748 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:19,748 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:19,749 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it does not provide insights into the qualitative factors or specific performance data for girls at a particular institution. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 01:56:21,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:21,477 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:21,496 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:21,498 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific metrics or data points would be necessary to effectively compare the academic performance of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific metrics or data points necessary to compare the academic performance of girls and boys at ITC University. However, the schema does not provide explicit information about gender-specific academic performance metrics or how to derive them. While there are tables related to students, courses, and assessments, the schema lacks direct indicators or metrics that would allow for a gender-based comparison of academic performance. To answer this question, one would need to know which specific metrics (like grades, GPAs, or assessment results) are relevant and how they can be filtered by gender, which is not directly available in the schema.', 'feedback': ''}
2025-08-08 01:56:21,501 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:21,501 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:21,501 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific metrics or data points would be necessary to effectively compare the academic performance of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific metrics or data points necessary to compare the academic performance of girls and boys at ITC University. However, the schema does not provide explicit information about gender-specific academic performance metrics or how to derive them. While there are tables related to students, courses, and assessments, the schema lacks direct indicators or metrics that would allow for a gender-based comparison of academic performance. To answer this question, one would need to know which specific metrics (like grades, GPAs, or assessment results) are relevant and how they can be filtered by gender, which is not directly available in the schema.', 'feedback': ''}
2025-08-08 01:56:22,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:23,731 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:23,746 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of recorded achievements for female students at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of recorded achievements for female students at ITC University. However, the provided schema does not contain specific data or attributes related to gender-based achievements or the reasons behind them. While there are tables related to students, their programs, and achievements, there is no direct link or data that would allow for an analysis of the factors affecting female students' achievements specifically. Therefore, the question cannot be answered with the available schema.", 'feedback': ''}
2025-08-08 01:56:23,746 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:23,746 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:23,746 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of recorded achievements for female students at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of recorded achievements for female students at ITC University. However, the provided schema does not contain specific data or attributes related to gender-based achievements or the reasons behind them. While there are tables related to students, their programs, and achievements, there is no direct link or data that would allow for an analysis of the factors affecting female students' achievements specifically. Therefore, the question cannot be answered with the available schema.", 'feedback': ''}
2025-08-08 01:56:23,758 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:24,174 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:24,196 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the fluctuations in performance among girls at ITC University across different academic years or semesters?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance fluctuations among girls at ITC University across different academic years or semesters. However, the provided schema does not contain specific data related to gender-based performance analysis or any direct metrics that would allow for such an analysis. While there are tables related to students, academic years, and performance results, there is no clear indication of how to correlate these with gender-specific performance trends. Additionally, the schema lacks qualitative data or contextual factors that might influence performance, such as socio-economic background, extracurricular involvement, or personal circumstances.', 'feedback': ''}
2025-08-08 01:56:24,196 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:24,196 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:24,196 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the fluctuations in performance among girls at ITC University across different academic years or semesters?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance fluctuations among girls at ITC University across different academic years or semesters. However, the provided schema does not contain specific data related to gender-based performance analysis or any direct metrics that would allow for such an analysis. While there are tables related to students, academic years, and performance results, there is no clear indication of how to correlate these with gender-specific performance trends. Additionally, the schema lacks qualitative data or contextual factors that might influence performance, such as socio-economic background, extracurricular involvement, or personal circumstances.', 'feedback': ''}
2025-08-08 01:56:25,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:25,588 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 01:56:25,588 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:25,588 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:25,589 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 01:56:26,397 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:26,411 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific metrics or data points would be necessary to effectively compare the academic performance of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific metrics or data points necessary to compare the academic performance of girls and boys at ITC University. However, the schema does not provide explicit information about gender-specific performance metrics or how to aggregate or compare performance data by gender. While there are tables related to students, grades, and courses, the schema lacks direct references to gender-specific performance metrics or a clear way to extract such comparative data. Therefore, without additional context or data points specifically related to gender comparisons, the question cannot be answered.', 'feedback': ''}
2025-08-08 01:56:26,411 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:26,411 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:26,411 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific metrics or data points would be necessary to effectively compare the academic performance of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific metrics or data points necessary to compare the academic performance of girls and boys at ITC University. However, the schema does not provide explicit information about gender-specific performance metrics or how to aggregate or compare performance data by gender. While there are tables related to students, grades, and courses, the schema lacks direct references to gender-specific performance metrics or a clear way to extract such comparative data. Therefore, without additional context or data points specifically related to gender comparisons, the question cannot be answered.', 'feedback': ''}
2025-08-08 01:56:26,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:27,047 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:28,058 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:28,642 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:28,656 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of recorded achievements for female students at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of recorded achievements for female students at ITC University. However, the provided schema does not contain specific data or attributes related to gender-based achievements or the reasons behind them. While there are tables related to students, their achievements, and demographics, there is no direct link or data that would allow for a comprehensive analysis of the factors affecting female students' achievements specifically. Therefore, the question cannot be answered with the available schema.", 'feedback': ''}
2025-08-08 01:56:28,656 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:28,656 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:28,656 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of recorded achievements for female students at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of recorded achievements for female students at ITC University. However, the provided schema does not contain specific data or attributes related to gender-based achievements or the reasons behind them. While there are tables related to students, their achievements, and demographics, there is no direct link or data that would allow for a comprehensive analysis of the factors affecting female students' achievements specifically. Therefore, the question cannot be answered with the available schema.", 'feedback': ''}
2025-08-08 01:56:28,950 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:30,058 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:30,088 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the fluctuations in performance among girls at ITC University across different academic years or semesters?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance fluctuations among girls at ITC University across different academic years or semesters. However, the provided schema does not contain specific data related to gender-based performance analysis or the factors influencing such performance. While there are tables related to students, academic years, and assessment results, there is no direct link or data that would allow for a comprehensive analysis of performance fluctuations specifically for girls. Additionally, the schema lacks qualitative data or contextual factors that could influence performance, such as socio-economic background, personal circumstances, or specific challenges faced by female students.', 'feedback': ''}
2025-08-08 01:56:30,088 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:30,088 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:30,088 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the fluctuations in performance among girls at ITC University across different academic years or semesters?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance fluctuations among girls at ITC University across different academic years or semesters. However, the provided schema does not contain specific data related to gender-based performance analysis or the factors influencing such performance. While there are tables related to students, academic years, and assessment results, there is no direct link or data that would allow for a comprehensive analysis of performance fluctuations specifically for girls. Additionally, the schema lacks qualitative data or contextual factors that could influence performance, such as socio-economic background, personal circumstances, or specific challenges faced by female students.', 'feedback': ''}
2025-08-08 01:56:30,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:30,585 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 01:56:30,585 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:30,585 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:30,585 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 01:56:31,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:31,920 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:31,934 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific metrics or data points would be necessary to effectively compare the academic performance of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific metrics or data points necessary to compare the academic performance of girls and boys at ITC University. However, the schema does not provide explicit information about gender-specific academic performance metrics or how to derive them. While there are tables related to students, courses, and assessment results, the schema lacks direct references to gender-specific performance metrics or a clear way to aggregate and compare performance data by gender. Therefore, without additional context or data points specifically related to gender comparisons, the question cannot be answered.', 'feedback': ''}
2025-08-08 01:56:31,934 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:31,934 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:31,934 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific metrics or data points would be necessary to effectively compare the academic performance of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific metrics or data points necessary to compare the academic performance of girls and boys at ITC University. However, the schema does not provide explicit information about gender-specific academic performance metrics or how to derive them. While there are tables related to students, courses, and assessment results, the schema lacks direct references to gender-specific performance metrics or a clear way to aggregate and compare performance data by gender. Therefore, without additional context or data points specifically related to gender comparisons, the question cannot be answered.', 'feedback': ''}
2025-08-08 01:56:33,455 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:34,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:34,495 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of recorded achievements for female students at ITC University?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of recorded achievements for female students at ITC University. The provided database schema contains structured data about students, their programs, achievements, and other related entities, but it does not include qualitative data or insights that would explain the reasons behind the lack of recorded achievements. To answer this question, one would need access to qualitative data such as surveys, interviews, or reports that discuss the experiences and challenges faced by female students, which are not present in the schema.', 'feedback': ''}
2025-08-08 01:56:34,495 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:34,495 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:34,495 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of recorded achievements for female students at ITC University?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of recorded achievements for female students at ITC University. The provided database schema contains structured data about students, their programs, achievements, and other related entities, but it does not include qualitative data or insights that would explain the reasons behind the lack of recorded achievements. To answer this question, one would need access to qualitative data such as surveys, interviews, or reports that discuss the experiences and challenges faced by female students, which are not present in the schema.', 'feedback': ''}
2025-08-08 01:56:35,907 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:35,916 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the fluctuations in performance among girls at ITC University across different academic years or semesters?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance fluctuations among girls at ITC University across different academic years or semesters. However, the schema does not provide specific data on gender-based performance metrics or detailed academic performance analysis. While there are tables related to students, academic years, and assessment results, there is no direct link or data that would allow for a comprehensive analysis of performance fluctuations specifically for girls. The schema lacks the necessary attributes or metrics to answer this question fully.', 'feedback': ''}
2025-08-08 01:56:35,916 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:35,916 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:35,916 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the fluctuations in performance among girls at ITC University across different academic years or semesters?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance fluctuations among girls at ITC University across different academic years or semesters. However, the schema does not provide specific data on gender-based performance metrics or detailed academic performance analysis. While there are tables related to students, academic years, and assessment results, there is no direct link or data that would allow for a comprehensive analysis of performance fluctuations specifically for girls. The schema lacks the necessary attributes or metrics to answer this question fully.', 'feedback': ''}
2025-08-08 01:56:38,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:40,853 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:40,867 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the fluctuations in performance among girls at ITC University across different academic years or semesters?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance fluctuations among girls at ITC University across different academic years or semesters. However, the schema does not provide specific data on gender-based performance metrics or detailed academic performance analysis. While there are tables related to students, academic years, and assessment results, there is no direct link or data that would allow for a comprehensive analysis of performance fluctuations specifically for girls. The schema lacks the necessary attributes or metrics to answer this question.', 'feedback': ''}
2025-08-08 01:56:40,868 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:56:40,868 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:56:40,868 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the fluctuations in performance among girls at ITC University across different academic years or semesters?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance fluctuations among girls at ITC University across different academic years or semesters. However, the schema does not provide specific data on gender-based performance metrics or detailed academic performance analysis. While there are tables related to students, academic years, and assessment results, there is no direct link or data that would allow for a comprehensive analysis of performance fluctuations specifically for girls. The schema lacks the necessary attributes or metrics to answer this question.', 'feedback': ''}
2025-08-08 01:56:40,868 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 01:56:40,868 - root - INFO - [{'start_year': 2019, 'sex': 'F', 'average_score': 66.43, 'total_assessments': 7}, {'start_year': 2019, 'sex': 'F', 'average_score': 77.88, 'total_assessments': 3}, {'start_year': 2023, 'sex': 'F', 'average_score': None, 'total_assessments': 3}, {'start_year': 2024, 'sex': 'F', 'average_score': 72.95, 'total_assessments': 38}]
2025-08-08 01:56:40,869 - root - INFO - 'No results'
2025-08-08 01:56:40,869 - root - INFO - 'No results'
2025-08-08 01:56:40,869 - root - INFO - 'No results'
2025-08-08 01:56:40,869 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 1
2025-08-08 01:56:40,869 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 3
2025-08-08 01:56:40,869 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 01:56:48,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:48,244 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 01:56:58,733 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:56:58,747 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:56:58,747 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 01:56:58,747 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:56:58,747 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/4
2025-08-08 01:56:58,747 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:56:58,747 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:56:58,747 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:56:58,747 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 01:56:58,747 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:56:58,747 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:56:58,748 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the academic performance levels of girls compare to those of boys at ITC University?...
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, which indicates that there may not be sufficient data avai...
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: academic_performance_comparison_girls_boys
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:56:58,748 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:56:58,748 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/4
2025-08-08 01:56:58,748 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:56:58,748 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:56:58,748 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:56:58,748 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are girls at ITC University excelling in the most?...
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are currently no available data or results indicating which programs or course...
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_programs_performance_at_itc
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:56:58,748 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:56:58,748 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:56:58,748 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/4
2025-08-08 01:56:58,748 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:56:58,749 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:56:58,749 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:56:58,749 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any trends in the performance of girls at ITC University over the past few years?...
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Based on the data available, there are no recorded results regarding the performance of girls at ITC...
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_performance_trends_itc_university
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:56:58,749 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:56:58,749 - REPORT_PIPELINE - INFO - 🔄 Processing interview 4/4
2025-08-08 01:56:58,749 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:56:58,749 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:56:58,749 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:56:58,749 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the performance of girls at ITC University vary by academic year or semester?...
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The performance of girls at ITC University varies by academic year as follows: In 2019, the average ...
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: graph
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'start_year': 2019, 'sex': 'F', 'average_score': 66.43, 'total_assessments': 7}, {'start_year': 20...
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_performance_by_academic_year
2025-08-08 01:56:58,749 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 01:56:58,749 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 01:56:58,749 - celery.redirected - WARNING - [{'start_year': 2019, 'sex': 'F', 'average_score': 66.43, 'total_assessments': 7}, {'start_year': 2019, 'sex': 'F', 'average_score': 77.88, 'total_assessments': 3}, {'start_year': 2023, 'sex': 'F', 'average_score': None, 'total_assessments': 3}, {'start_year': 2024, 'sex': 'F', 'average_score': 72.95, 'total_assessments': 38}]
2025-08-08 01:56:58,749 - celery.redirected - WARNING - ================================= 
2025-08-08 01:56:58,750 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: girls_performance_by_academic_year
2025-08-08 01:56:58,750 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 01:56:58,750 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:56:58,750 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 4
2025-08-08 01:56:58,750 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:56:58,750 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 01:56:58,750 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 4 documents
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -     Content: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:56:31.934703+00:00', 'data_returned': False}
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:56:34.495653+00:00', 'data_returned': False}
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -     Content: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:56:30.585841+00:00', 'data_returned': False}
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -     Content: Question: How does the performance of girls at ITC University vary by academic year or semester?
Ans...
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:56:40.868445+00:00', 'data_returned': True, 'data_tag': 'girls_performance_by_academic_year'}
2025-08-08 01:56:58,750 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 1/4
2025-08-08 01:56:58,909 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:200 duration:0.159s]
2025-08-08 01:56:59,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:57:09,925 - elastic_transport.transport - INFO - PUT http://54.246.247.31:9200/_bulk?refresh=true [status:N/A duration:10.002s]
2025-08-08 01:57:09,925 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://54.246.247.31:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-08 01:57:09,925 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: Connection timed out
2025-08-08 01:57:09,926 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:57:09,926 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION ERROR
2025-08-08 01:57:09,926 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:57:09,926 - REPORT_PIPELINE_ERROR - ERROR - ❌ Report generation failed: Error generating report: Connection timed out
2025-08-08 01:57:09,926 - REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_015526.log
2025-08-08 01:57:09,929 - celery.app.trace - INFO - Task generate_report[513383bd-df6b-45bb-9fbf-bf49b18e1427] succeeded in 103.41959374999715s: {'error': 'Error generating report: Connection timed out'}

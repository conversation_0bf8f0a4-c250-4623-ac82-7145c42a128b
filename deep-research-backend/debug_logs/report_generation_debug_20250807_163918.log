2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - 🎯 TESTING DEBUG LOGGING
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - 🎯 TESTING DEBUG LOGGING
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:39:18,133 - TEST - INFO - ✅ Debug logging test successful!
2025-08-07 16:39:18,133 - TEST - INFO - ✅ Debug logging test successful!
2025-08-07 16:39:18,133 - TEST - INFO - 📁 Log file: debug_logs/report_generation_debug_20250807_163918.log
2025-08-07 16:39:18,133 - TEST - INFO - 📁 Log file: debug_logs/report_generation_debug_20250807_163918.log
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: test_task_123
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: test_task_123
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:39:18,133 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:39:18,133 - REPORT_REQUEST - INFO - 📝 Original Question: 'Test question for debugging'
2025-08-07 16:39:18,133 - REPORT_REQUEST - INFO - 📝 Original Question: 'Test question for debugging'
2025-08-07 16:39:18,133 - REPORT_REQUEST - INFO - 🆔 Task ID: test_task_123
2025-08-07 16:39:18,133 - REPORT_REQUEST - INFO - 🆔 Task ID: test_task_123
2025-08-07 16:39:18,133 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-07T16:39:18.133906
2025-08-07 16:39:18,133 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-07T16:39:18.133906
2025-08-07 16:39:18,980 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:200 duration:0.265s]
2025-08-07 16:39:19,138 - elastic_transport.transport - INFO - GET http://54.246.247.31:9200/deep_research/_mapping [status:200 duration:0.158s]
2025-08-07 16:39:19,138 - app.vectorstore.client - INFO - Index 'deep_research' exists with correct vector field mapping
2025-08-07 16:39:19,398 - elastic_transport.transport - INFO - GET http://54.246.247.31:9200/ [status:200 duration:0.259s]
2025-08-07 16:39:19,678 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.279s]
2025-08-07 16:39:19,678 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-07 16:39:19,678 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-07 16:39:19,824 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.145s]
2025-08-07 16:39:19,824 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:39:19,824 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:39:19,980 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:0.156s]
2025-08-07 16:39:19,980 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-07 16:39:19,980 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):

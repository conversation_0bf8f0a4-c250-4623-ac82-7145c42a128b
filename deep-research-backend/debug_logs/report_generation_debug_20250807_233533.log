2025-08-07 23:35:33,865 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:35:33,865 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: test-task-123
2025-08-07 23:35:33,865 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:35:33,865 - REPORT_REQUEST - INFO - 📝 Original Question: 'How many students are there?'
2025-08-07 23:35:33,865 - REPORT_REQUEST - INFO - 🆔 Task ID: test-task-123
2025-08-07 23:35:33,865 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-07T23:35:33.865621
2025-08-07 23:35:34,000 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-07 23:35:34,000 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-07 23:35:34,181 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.181s]
2025-08-07 23:35:34,182 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:35:34,338 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.156s]
2025-08-07 23:35:34,339 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-07 23:35:34,339 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-07 23:35:41,220 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:35:41,240 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-07 23:35:45,164 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:35:45,179 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-07 23:35:48,135 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:35:48,162 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-07 23:35:48,163 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:35:48,163 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-07 23:35:48,163 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:35:48,163 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 0
2025-08-07 23:35:48,164 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-07 23:35:48,164 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-07 23:35:48,164 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 0
2025-08-07 23:35:48,164 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-07 23:35:55,722 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:35:55,729 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-07 23:36:05,259 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:05,277 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:05,277 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-07 23:36:05,277 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:05,277 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 0
2025-08-07 23:36:05,277 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:05,277 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-07 23:36:05,277 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:05,277 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 0 documents
2025-08-07 23:36:05,277 - UPSERT_DOCS - WARNING - ⚠️ No documents to upsert!
2025-08-07 23:36:05,424 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.146s]
2025-08-07 23:36:05,424 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-07 23:36:05,602 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.178s]
2025-08-07 23:36:05,602 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:36:05,807 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.204s]
2025-08-07 23:36:05,807 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-07 23:36:05,808 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 9
2025-08-07 23:36:05,808 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-07 23:36:05,808 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:05,808 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-07 23:36:05,808 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:05,809 - REPORT_PIPELINE - INFO - ✍️ Writing 9 sections using batch processing...
2025-08-07 23:36:06,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:06,407 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:06,407 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:36:06,407 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:06,407 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Interview findings themes data enrollment challenges'
2025-08-07 23:36:06,407 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-07 23:36:06,407 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-07 23:36:06,571 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.163s]
2025-08-07 23:36:06,571 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:36:06,576 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:06,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:06,586 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:06,586 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:36:06,587 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:06,587 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:06,587 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:36:06,587 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'current student population enrollment'
2025-08-07 23:36:06,587 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:06,587 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-07 23:36:06,587 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology Data Collection Interview Process Limitations'
2025-08-07 23:36:06,587 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-07 23:36:06,587 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-07 23:36:06,588 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-07 23:36:06,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:06,651 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:06,652 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:36:06,652 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:06,652 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'discussion analysis findings student enrollment literature recommendations'
2025-08-07 23:36:06,652 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-07 23:36:06,652 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-07 23:36:06,722 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.150s]
2025-08-07 23:36:06,722 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:36:06,877 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.154s]
2025-08-07 23:36:06,878 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.290s]
2025-08-07 23:36:06,878 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.291s]
2025-08-07 23:36:06,878 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:36:06,879 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:36:06,879 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:36:06,885 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:06,896 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:06,896 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:36:06,896 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:06,896 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Implications of Findings Educational Resources Future Projections'
2025-08-07 23:36:06,896 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-07 23:36:06,896 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-07 23:36:06,956 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:06,960 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:06,964 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:06,965 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:36:06,965 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:06,965 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:06,965 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:36:06,965 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion Accurate Student Counts'
2025-08-07 23:36:06,965 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:06,966 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-07 23:36:06,966 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'appendices interview transcripts data charts'
2025-08-07 23:36:06,966 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-07 23:36:06,966 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-07 23:36:06,966 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-07 23:36:07,019 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:07,024 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:07,024 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:36:07,025 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:07,025 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'References Sources Acknowledgments'
2025-08-07 23:36:07,025 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-07 23:36:07,025 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-07 23:36:07,033 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.381s]
2025-08-07 23:36:07,033 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:36:07,034 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.154s]
2025-08-07 23:36:07,034 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:36:07,035 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.155s]
2025-08-07 23:36:07,035 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:36:07,036 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.157s]
2025-08-07 23:36:07,037 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:36:07,249 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.214s]
2025-08-07 23:36:07,249 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.353s]
2025-08-07 23:36:07,249 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:36:07,250 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.216s]
2025-08-07 23:36:07,250 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:36:07,250 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:36:07,257 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.223s]
2025-08-07 23:36:07,258 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:36:07,273 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.307s]
2025-08-07 23:36:07,273 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:36:07,274 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.308s]
2025-08-07 23:36:07,274 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:36:07,285 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.260s]
2025-08-07 23:36:07,285 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:36:07,408 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.150s]
2025-08-07 23:36:07,409 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.159s]
2025-08-07 23:36:07,409 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.159s]
2025-08-07 23:36:07,409 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:36:07,410 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.159s]
2025-08-07 23:36:07,410 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-07 23:36:07,410 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:36:07,411 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-07 23:36:07,411 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:36:07,412 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.139s]
2025-08-07 23:36:07,412 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:36:07,413 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:36:07,413 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:36:07,415 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:36:07,612 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.196s]
2025-08-07 23:36:07,612 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.197s]
2025-08-07 23:36:07,613 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.196s]
2025-08-07 23:36:07,613 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.197s]
2025-08-07 23:36:07,613 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:36:07,614 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:36:07,614 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:36:07,614 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:36:07,616 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.201s]
2025-08-07 23:36:07,616 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:36:07,781 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.167s]
2025-08-07 23:36:07,781 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:36:07,782 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.167s]
2025-08-07 23:36:07,784 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:36:07,793 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.178s]
2025-08-07 23:36:07,793 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:36:07,794 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.179s]
2025-08-07 23:36:07,794 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:36:07,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:36:07,881 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:07,886 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:07,886 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 23:36:07,886 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:07,887 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'current student population demographics comparison'
2025-08-07 23:36:07,887 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-07 23:36:07,887 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-07 23:36:07,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:36:07,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:36:08,071 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.184s]
2025-08-07 23:36:08,071 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 23:36:08,192 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:36:08,225 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.281s]
2025-08-07 23:36:08,226 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.154s]
2025-08-07 23:36:08,226 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:36:08,226 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 23:36:08,227 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:36:08,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:36:08,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:36:08,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:36:08,373 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.428s]
2025-08-07 23:36:08,374 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.429s]
2025-08-07 23:36:08,374 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:36:08,375 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:36:08,375 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:36:08,375 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:36:08,380 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.153s]
2025-08-07 23:36:08,381 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 23:36:08,389 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.131s]
2025-08-07 23:36:08,389 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:36:08,389 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:36:08,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:36:08,570 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.189s]
2025-08-07 23:36:08,571 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 23:36:08,585 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.155s]
2025-08-07 23:36:08,586 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:36:08,586 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:36:08,604 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.409s]
2025-08-07 23:36:08,605 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:36:08,605 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:36:08,900 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.640s]
2025-08-07 23:36:08,900 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:36:08,901 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:36:08,922 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.614s]
2025-08-07 23:36:08,922 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:36:08,922 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:36:08,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 23:36:09,168 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.224s]
2025-08-07 23:36:09,168 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 23:36:09,169 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 23:36:10,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:11,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:12,167 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:12,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:13,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:13,881 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:14,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:16,646 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:16,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:16,700 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 269 characters
2025-08-07 23:36:16,701 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1210 characters
2025-08-07 23:36:16,701 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1604 characters
2025-08-07 23:36:16,702 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 1246 characters
2025-08-07 23:36:16,702 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1978 characters
2025-08-07 23:36:16,702 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 1136 characters
2025-08-07 23:36:16,703 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 715 characters
2025-08-07 23:36:16,703 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 519 characters
2025-08-07 23:36:16,703 - REPORT_PIPELINE - INFO -   ✅ Section 9 completed: 252 characters
2025-08-07 23:36:17,961 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 23:36:17,966 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:36:17,966 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:36:17,966 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:36:17,966 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:36:17,966 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:36:17,966 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:36:17,966 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:36:17,966 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:36:17,967 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:36:17,967 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 23:36:17,967 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 23:36:17,967 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-07 23:36:17,967 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 23:36:17,967 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-07 23:36:17,967 - REPORT_PIPELINE - INFO - 📊 Final report sections: 10
2025-08-07 23:36:17,967 - REPORT_PIPELINE - INFO - 📈 Extended data items: 0
2025-08-07 23:36:17,967 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250807_233533.log

2025-08-08 00:57:52,769 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_005752.log
2025-08-08 00:57:52,769 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:57:52,769 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: bc471239-06d3-4832-b157-5599163f0223
2025-08-08 00:57:52,769 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:57:52,769 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-08 00:57:52,769 - REPORT_REQUEST - INFO - 🆔 Task ID: bc471239-06d3-4832-b157-5599163f0223
2025-08-08 00:57:52,769 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T00:57:52.769726
2025-08-08 00:57:52,930 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.161s]
2025-08-08 00:57:52,931 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 26
2025-08-08 00:57:53,160 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.229s]
2025-08-08 00:57:53,160 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:57:53,557 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.397s]
2025-08-08 00:57:53,558 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (3):
2025-08-08 00:57:53,558 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (18 docs)
2025-08-08 00:57:53,558 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 00:57:53,558 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing in ITC University?' (1 docs)
2025-08-08 00:57:53,558 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 00:58:03,786 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:03,814 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 00:58:08,004 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:08,019 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 00:58:10,484 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:10,996 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:11,066 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:11,307 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:11,315 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:11,413 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:11,482 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:11,816 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:11,817 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:12,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:12,215 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 00:58:12,216 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:58:12,216 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 00:58:12,216 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:58:12,216 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 3
2025-08-08 00:58:14,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:15,397 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:15,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:17,806 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:18,471 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:18,585 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:20,045 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:20,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:20,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:27,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:28,000 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:28,039 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:32,500 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:32,505 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic performance metrics of girls compare to those of boys at ITC University?', 'sql': "SELECT sex, AVG(assessment_total) AS average_assessment_total, AVG(exam_total) AS average_exam_total, AVG(finalscore) AS average_finalscore, COUNT(*) AS total_students\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN student_programs sp ON s.id = sp.student_id\nWHERE sp.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY sex;", 'correct': True, 'reasoning': 'The SQL query correctly retrieves the average academic performance metrics (assessment_total, exam_total, finalscore) for both girls and boys at ITC University. It joins the necessary tables (assessment_results, students, and student_programs) to filter the results based on the institution and groups the results by sex, which allows for a direct comparison between the two genders.', 'feedback': 'The question could be clarified by specifying which specific academic performance metrics are of interest, but the SQL query already covers the main metrics. Additionally, ensure that the institutions table is correctly referenced in the subquery to avoid any potential issues with the institution_id filtering.'}
2025-08-08 00:58:32,505 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:58:32,506 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 00:58:33,729 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:33,734 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in the most?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(ar.finalscore) AS average_score\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.assessment_results ar ON sp.id = ar.student_program_id\nJOIN core.courses c ON ar.course_id = c.id\nWHERE s.sex = 'F' AND s.institution_id = 'ITC University'\nGROUP BY p.long_name, c.title\nORDER BY average_score DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses that female students at ITC University are excelling in by calculating the average score of assessment results for each program and course combination. It filters the students by sex and institution, groups the results by program and course, and orders them by average score in descending order, which aligns with the question's request for identifying areas of excellence.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be beneficial to clarify whether 'excelling' refers strictly to the highest average scores or if other metrics should be considered. Additionally, ensuring that the institution_id is correctly referenced as a string in the WHERE clause could prevent potential errors."}
2025-08-08 00:58:33,734 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:58:33,734 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 00:58:35,572 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:38,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:38,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:38,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:39,689 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:39,907 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:40,421 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:40,998 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:41,407 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:42,023 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:43,167 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:43,586 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:44,775 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:46,005 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:46,027 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons, such as grades, assessments, or demographic studies that would allow for such an analysis. The schema includes various tables related to students, courses, and assessments, but it lacks the necessary data to directly answer the question about gender performance differences.', 'feedback': ''}
2025-08-08 00:58:46,027 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:58:46,027 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:58:46,027 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons, such as grades, assessments, or demographic studies that would allow for such an analysis. The schema includes various tables related to students, courses, and assessments, but it lacks the necessary data to directly answer the question about gender performance differences.', 'feedback': ''}
2025-08-08 00:58:46,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:46,111 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify factors affecting data availability or performance specifically for girls. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-08 00:58:46,111 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:58:46,111 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:58:46,111 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify factors affecting data availability or performance specifically for girls. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-08 00:58:51,691 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:58:59,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:03,015 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:06,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:08,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:14,482 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:22,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:24,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:28,000 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:30,866 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:30,880 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the graduation rate for girls at ITC University compared to the overall graduation rate?', 'answerable': False, 'reasoning': 'Too much uncertainty as to whether or not it can be answered', 'feedback': 'Clarify the status of graduates in the graduation_counts CTE to ensure it reflects those who have actually graduated. Additionally, ensure that the overall_counts CTE reflects the total number of students who have graduated, not just those enrolled. Consider renaming the CTEs for clarity and ensuring that the calculations are based on the correct statuses.'}
2025-08-08 00:59:30,880 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:59:30,880 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:59:30,880 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What is the graduation rate for girls at ITC University compared to the overall graduation rate?', 'answerable': False, 'reasoning': 'Too much uncertainty as to whether or not it can be answered', 'feedback': 'Clarify the status of graduates in the graduation_counts CTE to ensure it reflects those who have actually graduated. Additionally, ensure that the overall_counts CTE reflects the total number of students who have graduated, not just those enrolled. Consider renaming the CTEs for clarity and ensuring that the calculations are based on the correct statuses.'}
2025-08-08 00:59:30,881 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 00:59:30,881 - root - INFO - 'No results'
2025-08-08 00:59:30,881 - root - INFO - 'No results'
2025-08-08 00:59:30,881 - root - INFO - 'No results'
2025-08-08 00:59:30,881 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-08 00:59:30,881 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 3
2025-08-08 00:59:30,881 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 00:59:39,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:39,063 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 00:59:48,275 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:48,281 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:48,281 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 00:59:48,281 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:48,281 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/3
2025-08-08 00:59:48,281 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:48,282 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:59:48,282 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:59:48,282 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you believe contribute to the higher academic performance of girls at ITC University...
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:59:48,282 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:59:48,282 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:59:48,282 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/3
2025-08-08 00:59:48,282 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:48,282 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:59:48,283 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:59:48,283 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think might contribute to the lack of data on girls' academic performance in spe...
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:59:48,283 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:59:48,283 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/3
2025-08-08 00:59:48,283 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:48,283 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:59:48,283 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:59:48,283 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:59:48,284 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:59:48,284 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:59:48,284 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the graduation rate for girls at ITC University compared to the overall graduation rate?...
2025-08-08 00:59:48,284 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:59:48,284 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:59:48,284 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:59:48,284 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:59:48,284 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:59:48,284 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:59:48,284 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:59:48,284 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 3
2025-08-08 00:59:48,284 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:48,284 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 00:59:48,284 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:48,284 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 3 documents
2025-08-08 00:59:48,284 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 00:59:48,284 - UPSERT_DOCS - INFO -     Content: Question: What factors do you believe contribute to the higher academic performance of girls at ITC ...
2025-08-08 00:59:48,284 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:58:46.027664+00:00', 'data_returned': False}
2025-08-08 00:59:48,284 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 00:59:48,284 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think might contribute to the lack of data on girls' academic performa...
2025-08-08 00:59:48,284 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:58:46.111836+00:00', 'data_returned': False}
2025-08-08 00:59:48,284 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 00:59:48,285 - UPSERT_DOCS - INFO -     Content: Question: What is the graduation rate for girls at ITC University compared to the overall graduation...
2025-08-08 00:59:48,285 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:59:30.880823+00:00', 'data_returned': False}
2025-08-08 00:59:48,285 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 0/3
2025-08-08 00:59:48,418 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.133s]
2025-08-08 00:59:50,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:59:54,009 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:3.294s]
2025-08-08 00:59:54,010 - UPSERT_DOCS - INFO - ✅ Successfully upserted 3 documents to Elasticsearch
2025-08-08 00:59:54,731 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.720s]
2025-08-08 00:59:54,731 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 29
2025-08-08 00:59:55,544 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.813s]
2025-08-08 00:59:55,545 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:59:56,978 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:1.433s]
2025-08-08 00:59:56,978 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (4):
2025-08-08 00:59:56,979 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (18 docs)
2025-08-08 00:59:56,979 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 00:59:56,979 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (3 docs)
2025-08-08 00:59:56,979 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing in ITC University?' (1 docs)
2025-08-08 00:59:56,980 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 9
2025-08-08 00:59:56,980 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 00:59:56,981 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:56,981 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-08 00:59:56,981 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:56,981 - REPORT_PIPELINE - INFO - ✍️ Writing 9 sections using batch processing...
2025-08-08 00:59:57,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:57,662 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:57,662 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:59:57,662 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:57,662 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Insights Trends Performance'
2025-08-08 00:59:57,662 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 00:59:57,662 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 00:59:57,860 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.198s]
2025-08-08 00:59:57,861 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 29
2025-08-08 00:59:58,060 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.199s]
2025-08-08 00:59:58,061 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:59:58,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:58,125 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:58,130 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:58,130 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:58,130 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:59:58,130 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:59:58,130 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:58,130 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:58,130 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'academic performance female students ITC University'
2025-08-08 00:59:58,131 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion summary future research'
2025-08-08 00:59:58,131 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,131 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,131 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,131 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:58,163 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:58,163 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:59:58,164 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:58,164 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Recommendations for STEM gender bias'
2025-08-08 00:59:58,164 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,164 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,200 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.139s]
2025-08-08 00:59:58,201 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 00:59:58,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:58,338 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.137s]
2025-08-08 00:59:58,338 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:59:58,351 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:58,351 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:59:58,351 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:58,351 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Key themes interviews academic performance IT programs support systems challenges'
2025-08-08 00:59:58,351 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,351 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,364 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:58,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:58,381 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:58,382 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:58,382 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:59:58,382 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:59:58,382 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:58,382 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:58,382 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'appendices interview transcripts data tables graphs resources'
2025-08-08 00:59:58,382 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology data collection methods sample size limitations'
2025-08-08 00:59:58,382 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,382 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,382 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,382 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,400 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:58,408 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:58,408 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:59:58,408 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:58,408 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'References and Reading'
2025-08-08 00:59:58,408 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,408 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,501 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.369s]
2025-08-08 00:59:58,501 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 29
2025-08-08 00:59:58,514 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.383s]
2025-08-08 00:59:58,515 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 29
2025-08-08 00:59:58,597 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.246s]
2025-08-08 00:59:58,598 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 29
2025-08-08 00:59:58,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:59:58,641 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:59:58,641 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:59:58,641 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:59:58,641 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Academic Performance Metrics'
2025-08-08 00:59:58,641 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,641 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 00:59:58,671 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.507s]
2025-08-08 00:59:58,671 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 29
2025-08-08 00:59:58,714 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:59:58,855 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.354s]
2025-08-08 00:59:58,856 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:59:59,229 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.631s]
2025-08-08 00:59:59,230 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:59:59,242 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.859s]
2025-08-08 00:59:59,242 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 29
2025-08-08 00:59:59,254 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.398s]
2025-08-08 00:59:59,255 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.583s]
2025-08-08 00:59:59,255 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 00:59:59,255 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:59:59,843 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.329s]
2025-08-08 00:59:59,843 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:59:59,845 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.615s]
2025-08-08 00:59:59,845 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.603s]
2025-08-08 00:59:59,845 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.590s]
2025-08-08 00:59:59,846 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.128s]
2025-08-08 00:59:59,846 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 00:59:59,846 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:59:59,846 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:59:59,846 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:59:59,847 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:59:59,882 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.626s]
2025-08-08 00:59:59,882 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 00:59:59,974 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.591s]
2025-08-08 00:59:59,974 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 29
2025-08-08 00:59:59,989 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.581s]
2025-08-08 00:59:59,989 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 29
2025-08-08 01:00:00,085 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.239s]
2025-08-08 01:00:00,085 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 01:00:00,090 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.247s]
2025-08-08 01:00:00,091 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 01:00:00,140 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.257s]
2025-08-08 01:00:00,140 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:00:00,142 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.501s]
2025-08-08 01:00:00,142 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 29
2025-08-08 01:00:00,143 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.153s]
2025-08-08 01:00:00,143 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.297s]
2025-08-08 01:00:00,143 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:00:00,143 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.169s]
2025-08-08 01:00:00,143 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:00:00,144 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:00:00,319 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:00:00,440 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.297s]
2025-08-08 01:00:00,441 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:00:00,459 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.315s]
2025-08-08 01:00:00,460 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.374s]
2025-08-08 01:00:00,460 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 01:00:00,460 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:00:00,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:00:00,639 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.548s]
2025-08-08 01:00:00,639 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:00:00,736 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.592s]
2025-08-08 01:00:00,736 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 01:00:00,737 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.296s]
2025-08-08 01:00:00,738 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 01:00:01,028 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:00:01,142 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.682s]
2025-08-08 01:00:01,143 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.819s]
2025-08-08 01:00:01,143 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:00:01,144 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:00:01,144 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:00:01,149 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.412s]
2025-08-08 01:00:01,150 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:00:01,238 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:00:01,272 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:00:01,631 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:00:01,790 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.052s]
2025-08-08 01:00:01,791 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:00:01,848 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.263s]
2025-08-08 01:00:01,848 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.607s]
2025-08-08 01:00:01,849 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:00:01,849 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:00:01,849 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:00:01,849 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:00:01,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:00:02,427 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.504s]
2025-08-08 01:00:02,428 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:00:02,428 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:00:02,546 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.513s]
2025-08-08 01:00:02,546 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:00:02,547 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:00:02,916 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.641s]
2025-08-08 01:00:02,917 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.281s]
2025-08-08 01:00:02,917 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:00:02,918 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:00:02,918 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:00:02,918 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:00:03,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:00:05,476 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:2.039s]
2025-08-08 01:00:05,476 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:00:05,477 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:00:06,722 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:00:07,544 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:00:08,728 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:00:08,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:00:09,701 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:00:11,082 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:00:11,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:00:11,615 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:00:14,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:00:14,299 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 384 characters
2025-08-08 01:00:14,300 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1683 characters
2025-08-08 01:00:14,301 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1492 characters
2025-08-08 01:00:14,301 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 2401 characters
2025-08-08 01:00:14,301 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1817 characters
2025-08-08 01:00:14,302 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 1376 characters
2025-08-08 01:00:14,302 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 1001 characters
2025-08-08 01:00:14,303 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 182 characters
2025-08-08 01:00:14,303 - REPORT_PIPELINE - INFO -   ✅ Section 9 completed: 892 characters
2025-08-08 01:00:16,360 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:00:16,374 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:00:16,375 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:00:16,375 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:00:16,375 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:00:16,375 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:00:16,375 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:00:16,375 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:00:16,375 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:00:16,375 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:00:16,375 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:00:16,375 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:00:16,375 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-08 01:00:16,376 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:00:16,376 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-08 01:00:16,376 - REPORT_PIPELINE - INFO - 📊 Final report sections: 10
2025-08-08 01:00:16,376 - REPORT_PIPELINE - INFO - 📈 Extended data items: 0
2025-08-08 01:00:16,376 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_005752.log
2025-08-08 01:00:16,379 - celery.app.trace - INFO - Task generate_report[f964cf27-9ea7-41f6-8046-1f555234cb5e] succeeded in 143.61482345900004s: {'outline': '# Report on Girls\' Performance at ITC University

## 1. Introduction  
   The purpose of this report is to analyze the academic performance of female students at ITC University, focusing on their achievements, challenges, and the factors influencing their success. The key finding indicates that while girls are performing well academically, there are significant challenges and disparities in participation, particularly in STEM fields.

## 2. Methodology  
   - 2.1 Data Collection Methods  
       - 2.1.1 Surveys  
       - 2.1.2 Academic Records Analysis  
       - 2.1.3 Interviews with Faculty and Students  
   - 2.2 Sample Size and Demographics  
   - 2.3 Limitations of the Study  

## 3. Academic Performance Metrics  
   - 3.1 Grades and GPA Comparison  
   - 3.2 Course Completion Rates  
   - 3.3 Participation in Extracurricular Activities  

## 4. Key Themes from Interviews  
   - 4.1 Academic Performance  
       - 4.1.1 Grades and Assessment Results  
       - 4.1.2 Comparison with Male...', ...}

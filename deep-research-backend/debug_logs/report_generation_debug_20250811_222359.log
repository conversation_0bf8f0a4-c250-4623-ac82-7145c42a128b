2025-08-11 22:23:59,908 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250811_222359.log
2025-08-11 22:23:59,908 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:23:59,908 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 4c5915b0-0ff6-4b76-b144-2a16e9d86d23
2025-08-11 22:23:59,908 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:23:59,908 - REPORT_REQUEST - INFO - 📝 Original Question: 'Give me a report on student performance at ITC University'
2025-08-11 22:23:59,908 - REPORT_REQUEST - INFO - 🆔 Task ID: 4c5915b0-0ff6-4b76-b144-2a16e9d86d23
2025-08-11 22:23:59,908 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-11T22:23:59.908639
2025-08-11 22:24:00,036 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-11 22:24:00,036 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 125
2025-08-11 22:24:00,166 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 22:24:00,167 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 68
2025-08-11 22:24:00,298 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.131s]
2025-08-11 22:24:00,298 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-11 22:24:00,299 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (24 docs)
2025-08-11 22:24:00,299 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (16 docs)
2025-08-11 22:24:00,299 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (15 docs)
2025-08-11 22:24:00,299 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-11 22:24:00,299 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-11 22:24:00,299 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-11 22:24:00,299 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-11 22:24:00,299 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-11 22:24:00,299 - ELASTICSEARCH_STATE - INFO -   - 'Which instituion has the most students?' (6 docs)
2025-08-11 22:24:00,299 - ELASTICSEARCH_STATE - INFO -   - 'How are students performing at ITC University?' (4 docs)
2025-08-11 22:24:00,300 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:24:00,300 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-11 22:24:00,300 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:24:00,300 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: Give me a report on student performance at ITC University
2025-08-11 22:24:00,300 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-11 22:24:00,300 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-11 22:24:10,404 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:10,429 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-11 22:24:15,471 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:15,486 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-11 22:24:17,886 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:18,034 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:18,035 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:18,185 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:18,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:18,754 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:19,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:19,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:19,821 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:20,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:20,673 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-11 22:24:23,704 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:24,050 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:24,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:26,396 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:26,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:27,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:28,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:28,309 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:29,674 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:32,951 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:34,821 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:35,788 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:39,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:39,672 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are you interested in comparing student performance across different programs or departments within ITC University?', 'sql': 'SELECT p.long_name AS program_name, AVG(ar.finalscore) AS average_score, COUNT(ar.student_id) AS student_count\nFROM assessment_results ar\nJOIN student_programs sp ON ar.student_program_id = sp.id\nJOIN programs p ON sp.program_id = p.id\nGROUP BY p.long_name\nORDER BY average_score DESC\nLIMIT 20;', 'correct': True, 'reasoning': 'The SQL query retrieves the average final scores of students grouped by their respective programs, which directly addresses the question of comparing student performance across different programs. It uses the necessary joins to connect assessment results with student programs and programs, ensuring that the data is relevant to the question. The use of AVG() to calculate average scores and COUNT() to count the number of students per program provides a clear comparison metric. The ordering by average score and limiting the results to the top 20 programs further refines the output to the most relevant comparisons.', 'feedback': 'The question could be clarified by specifying whether the comparison should include specific departments or just programs. Additionally, if the intent is to compare across departments, the SQL might need to include a department identifier if available in the schema. However, as it stands, the SQL effectively answers the question regarding program performance.'}
2025-08-11 22:24:39,673 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:24:39,674 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 22:24:39,961 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:39,980 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Would you like to see the performance of students who are on scholarships versus those who are not?', 'sql': 'SELECT s.id AS student_id, s.surname, s.othernames, s.sex, s.status, AVG(g.gpa) AS average_gpa, COUNT(sch.id) AS scholarship_count\nFROM core.students s\nLEFT JOIN student_semester_gpas g ON s.id = g.student_id\nLEFT JOIN student_scholarships sch ON s.id = sch.student_id\nGROUP BY s.id, s.surname, s.othernames, s.sex, s.status\nORDER BY average_gpa DESC\nLIMIT 20;', 'correct': True, 'reasoning': "The SQL query retrieves student information along with their average GPA and the count of scholarships they have. It uses LEFT JOINs to include all students, regardless of whether they have scholarships, which allows for a comparison between students on scholarships and those not on scholarships. The GROUP BY clause ensures that the results are aggregated correctly for each student, and the ORDER BY clause sorts the results by average GPA, which aligns with the question's focus on performance. The LIMIT clause restricts the output to the top 20 students, which is a reasonable approach for viewing performance.", 'feedback': "The query is well-structured to answer the question. However, it could be improved by explicitly categorizing students into two groups: those with scholarships and those without, perhaps by adding a CASE statement to create a 'scholarship_status' column. This would make it clearer in the results which students are on scholarships and which are not."}
2025-08-11 22:24:39,980 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:24:39,980 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 22:24:40,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:44,196 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:45,787 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:46,630 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:47,460 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:47,733 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:48,451 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:50,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:51,572 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:53,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:53,958 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in student performance across these various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in student performance across various programs at ITC University. While the schema contains data about students, programs, and their performance (such as grades and assessments), it does not provide specific factors or qualitative data that would allow for a comprehensive analysis of performance differences. The schema lacks information on external factors, teaching quality, student engagement, or other qualitative aspects that could influence performance. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-11 22:24:53,958 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:24:53,958 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:24:53,958 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in student performance across these various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in student performance across various programs at ITC University. While the schema contains data about students, programs, and their performance (such as grades and assessments), it does not provide specific factors or qualitative data that would allow for a comprehensive analysis of performance differences. The schema lacks information on external factors, teaching quality, student engagement, or other qualitative aspects that could influence performance. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-11 22:24:56,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:57,689 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:57,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:58,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:58,667 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:24:58,695 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in student performance across these various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in student performance across various programs at ITC University. While the schema contains data about students, programs, and their performance (such as grades and assessments), it does not provide specific factors or metrics that would allow for a comprehensive analysis of performance differences. The schema lacks qualitative data or contextual information that would be necessary to answer such a question, making it unanswerable.', 'feedback': ''}
2025-08-11 22:24:58,696 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:24:58,696 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:24:58,696 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in student performance across these various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in student performance across various programs at ITC University. While the schema contains data about students, programs, and their performance (such as grades and assessments), it does not provide specific factors or metrics that would allow for a comprehensive analysis of performance differences. The schema lacks qualitative data or contextual information that would be necessary to answer such a question, making it unanswerable.', 'feedback': ''}
2025-08-11 22:25:00,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:01,265 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:03,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:03,341 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in student performance across these various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in student performance across various programs at ITC University. While the schema contains data about students, programs, and their performance (such as grades and assessments), it does not provide specific factors or metrics that would allow for a comprehensive analysis of performance differences. The schema lacks qualitative data or contextual information that would be necessary to understand the reasons behind performance variations, such as teaching quality, student engagement, or external influences.', 'feedback': ''}
2025-08-11 22:25:03,341 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:25:03,341 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:25:03,341 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in student performance across these various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in student performance across various programs at ITC University. While the schema contains data about students, programs, and their performance (such as grades and assessments), it does not provide specific factors or metrics that would allow for a comprehensive analysis of performance differences. The schema lacks qualitative data or contextual information that would be necessary to understand the reasons behind performance variations, such as teaching quality, student engagement, or external influences.', 'feedback': ''}
2025-08-11 22:25:03,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:04,655 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:05,891 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:06,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:07,496 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:07,523 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance of students who receive scholarships, and how might these differ from those who do not?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of students who receive scholarships compared to those who do not. This requires qualitative insights and potentially a combination of various data points such as academic results, scholarship types, student demographics, and other contextual factors. The schema provides tables related to students, scholarships, and academic results, but it does not contain direct qualitative data or a mechanism to analyze the differences in performance based on scholarship status. Therefore, while some data may be available, the question cannot be answered completely or correctly from the schema alone.', 'feedback': ''}
2025-08-11 22:25:07,523 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:25:07,524 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:25:07,524 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance of students who receive scholarships, and how might these differ from those who do not?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of students who receive scholarships compared to those who do not. This requires qualitative insights and potentially a combination of various data points such as academic results, scholarship types, student demographics, and other contextual factors. The schema provides tables related to students, scholarships, and academic results, but it does not contain direct qualitative data or a mechanism to analyze the differences in performance based on scholarship status. Therefore, while some data may be available, the question cannot be answered completely or correctly from the schema alone.', 'feedback': ''}
2025-08-11 22:25:08,489 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:08,765 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:08,790 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in student performance across these various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in student performance across various programs at ITC University. While the schema contains data about students, programs, and their performance (such as grades and assessments), it does not provide specific factors or qualitative data that would allow for a comprehensive analysis of performance differences. The schema lacks information on external factors, teaching quality, student engagement, or other qualitative aspects that could influence performance. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-11 22:25:08,790 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:25:08,791 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:25:08,791 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in student performance across these various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in student performance across various programs at ITC University. While the schema contains data about students, programs, and their performance (such as grades and assessments), it does not provide specific factors or qualitative data that would allow for a comprehensive analysis of performance differences. The schema lacks information on external factors, teaching quality, student engagement, or other qualitative aspects that could influence performance. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-11 22:25:10,533 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:10,750 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:12,810 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:12,842 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance of students who receive scholarships, and how might these differ from those who do not?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of students who receive scholarships compared to those who do not. While the schema contains data related to students, scholarships, and academic performance (such as grades and courses), it does not provide specific attributes or metrics that would allow for a comprehensive analysis of the factors influencing academic performance. Additionally, the question requires qualitative insights and comparisons that go beyond the quantitative data available in the schema.', 'feedback': ''}
2025-08-11 22:25:12,842 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:25:12,842 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:25:12,842 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance of students who receive scholarships, and how might these differ from those who do not?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of students who receive scholarships compared to those who do not. While the schema contains data related to students, scholarships, and academic performance (such as grades and courses), it does not provide specific attributes or metrics that would allow for a comprehensive analysis of the factors influencing academic performance. Additionally, the question requires qualitative insights and comparisons that go beyond the quantitative data available in the schema.', 'feedback': ''}
2025-08-11 22:25:14,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:15,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:18,414 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:18,466 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance of students who receive scholarships, and how might these differ from those who do not?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of students who receive scholarships compared to those who do not. While the schema contains data related to students, scholarships, and academic performance (such as grades and courses), it does not provide specific attributes or metrics that would allow for a comprehensive analysis of the factors influencing academic performance. Additionally, the question requires qualitative insights and comparisons that go beyond the quantitative data available in the schema.', 'feedback': ''}
2025-08-11 22:25:18,466 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:25:18,466 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:25:18,466 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance of students who receive scholarships, and how might these differ from those who do not?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of students who receive scholarships compared to those who do not. While the schema contains data related to students, scholarships, and academic performance (such as grades and courses), it does not provide specific attributes or metrics that would allow for a comprehensive analysis of the factors influencing academic performance. Additionally, the question requires qualitative insights and comparisons that go beyond the quantitative data available in the schema.', 'feedback': ''}
2025-08-11 22:25:21,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:24,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:24,051 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance of students who receive scholarships, and how might these differ from those who do not?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of students with scholarships compared to those without. This requires qualitative insights and potentially a variety of data points that are not explicitly defined in the schema. The schema contains tables related to students, scholarships, and academic performance metrics, but it does not provide a direct way to analyze or compare these factors comprehensively. Additionally, the question implies a need for interpretative analysis rather than straightforward data retrieval.', 'feedback': ''}
2025-08-11 22:25:24,051 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:25:24,051 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:25:24,051 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the academic performance of students who receive scholarships, and how might these differ from those who do not?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of students with scholarships compared to those without. This requires qualitative insights and potentially a variety of data points that are not explicitly defined in the schema. The schema contains tables related to students, scholarships, and academic performance metrics, but it does not provide a direct way to analyze or compare these factors comprehensively. Additionally, the question implies a need for interpretative analysis rather than straightforward data retrieval.', 'feedback': ''}
2025-08-11 22:25:25,462 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:28,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:31,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:32,501 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:35,933 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:40,830 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:43,481 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:46,576 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:50,979 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:50,993 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific courses or subjects at ITC University that you want to focus on in the performance report?', 'answerable': False, 'reasoning': 'Too much uncertainty as to whether or not it can be answered', 'feedback': "The question could be clarified by specifying what 'focus on' means in terms of performance reporting. Additionally, the SQL query should consider how to filter or aggregate data based on performance metrics or criteria relevant to the report, rather than just joining courses and subjects."}
2025-08-11 22:25:50,993 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:25:50,993 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:25:50,993 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'Are there specific courses or subjects at ITC University that you want to focus on in the performance report?', 'answerable': False, 'reasoning': 'Too much uncertainty as to whether or not it can be answered', 'feedback': "The question could be clarified by specifying what 'focus on' means in terms of performance reporting. Additionally, the SQL query should consider how to filter or aggregate data based on performance metrics or criteria relevant to the report, rather than just joining courses and subjects."}
2025-08-11 22:25:53,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:56,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:25:57,903 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:01,578 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:07,056 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:10,947 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:14,292 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:16,231 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:21,738 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:27,067 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:29,306 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:32,430 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:34,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:42,347 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:49,988 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:49,996 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific courses or subjects at ITC University that you want to focus on in the performance report?', 'sql': "SELECT DISTINCT c.title, c.code, ar.grade, COUNT(ar.id) AS total_assessments\nFROM core.courses c\nJOIN assessment_results ar ON c.id = ar.course_id\nWHERE c.institution_id = 'ITC University'\nGROUP BY c.title, c.code, ar.grade\nORDER BY total_assessments DESC\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query retrieves distinct course titles and codes from the 'core.courses' table that are associated with assessment results from the 'assessment_results' table for the specified institution, ITC University. It counts the total number of assessments for each course and groups the results by course title, code, and grade. This aligns with the question's intent to focus on specific courses at ITC University in a performance report, as it provides relevant data about courses and their performance metrics.", 'feedback': 'The question could be clarified by specifying what aspects of the performance report are of interest (e.g., grades, number of assessments, etc.). Additionally, the SQL could be improved by including a filter for specific semesters or levels if that information is relevant to the performance report.'}
2025-08-11 22:26:49,996 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:26:49,996 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 22:26:52,088 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:53,002 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:54,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:56,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:59,564 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:26:59,577 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What criteria or metrics do you think should be used to evaluate the overall performance of courses at ITC University, even if no specific subjects are currently identified?', 'answerable': False, 'reasoning': 'The question asks for criteria or metrics to evaluate the overall performance of courses at ITC University. However, the provided schema does not contain any specific information about evaluation criteria or metrics for courses. While there are tables related to courses, assessments, and results, there is no direct indication of what specific criteria or metrics should be used for evaluation. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 22:26:59,578 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:26:59,578 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:26:59,578 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What criteria or metrics do you think should be used to evaluate the overall performance of courses at ITC University, even if no specific subjects are currently identified?', 'answerable': False, 'reasoning': 'The question asks for criteria or metrics to evaluate the overall performance of courses at ITC University. However, the provided schema does not contain any specific information about evaluation criteria or metrics for courses. While there are tables related to courses, assessments, and results, there is no direct indication of what specific criteria or metrics should be used for evaluation. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 22:27:02,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:05,118 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:05,138 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What criteria or metrics do you think should be used to evaluate the overall performance of courses at ITC University, even if no specific subjects are currently identified?', 'answerable': False, 'reasoning': 'The question asks for criteria or metrics to evaluate the overall performance of courses at ITC University. However, the provided schema does not contain any specific information about evaluation criteria or metrics for courses. While there are tables related to courses, assessments, and results, there is no direct indication of what specific criteria or metrics should be used for evaluation. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 22:27:05,139 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:27:05,139 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:27:05,139 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What criteria or metrics do you think should be used to evaluate the overall performance of courses at ITC University, even if no specific subjects are currently identified?', 'answerable': False, 'reasoning': 'The question asks for criteria or metrics to evaluate the overall performance of courses at ITC University. However, the provided schema does not contain any specific information about evaluation criteria or metrics for courses. While there are tables related to courses, assessments, and results, there is no direct indication of what specific criteria or metrics should be used for evaluation. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 22:27:07,576 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:10,137 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:10,151 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What criteria or metrics do you think should be used to evaluate the overall performance of courses at ITC University, even if no specific subjects are currently identified?', 'answerable': False, 'reasoning': 'The question asks for criteria or metrics to evaluate the overall performance of courses at ITC University. However, the provided schema does not contain any specific information or tables that define evaluation criteria or metrics for courses. While there are tables related to courses, assessments, and results, they do not explicitly outline the criteria for evaluating performance. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 22:27:10,151 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 22:27:10,151 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 22:27:10,151 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What criteria or metrics do you think should be used to evaluate the overall performance of courses at ITC University, even if no specific subjects are currently identified?', 'answerable': False, 'reasoning': 'The question asks for criteria or metrics to evaluate the overall performance of courses at ITC University. However, the provided schema does not contain any specific information or tables that define evaluation criteria or metrics for courses. While there are tables related to courses, assessments, and results, they do not explicitly outline the criteria for evaluating performance. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 22:27:10,152 - root - INFO - [{'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'average_score': 84.0, 'student_count': 1}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'average_score': 76.34, 'student_count': 43}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'average_score': 73.0, 'student_count': 2}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'average_score': 70.0, 'student_count': 1}, {'program_name': 'Master Of Arts In Educational Leadership', 'average_score': 70.0, 'student_count': 1}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'average_score': 63.4, 'student_count': 21}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'average_score': 60.0, 'student_count': 1}, {'program_name': 'BACHELOR OF SCIENCE (MATHEMATICS EDUCATION)', 'average_score': 55.0, 'student_count': 1}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'average_score': 54.16, 'student_count': 49}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'average_score': None, 'student_count': 5}, {'program_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'average_score': None, 'student_count': 4}, {'program_name': 'Bachelor Of Medicine And Surgery', 'average_score': None, 'student_count': 2}]
2025-08-11 22:27:10,152 - root - INFO - [{'student_id': '59E395BD-A4E8-3760-5A11-03090E1F7FA7', 'surname': 'PREMPEH', 'othernames': 'PRINCETON NANA OSEI', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '4d2c8ec8-983c-6b94-83b5-5e6663a78464', 'surname': 'Fosu', 'othernames': 'Fredrick', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '8EF7C3EC-0287-A4B3-771D-B65E9E849746', 'surname': 'AGYAPOMAA', 'othernames': 'NAOMI OWUSU', 'sex': 'F', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '10a169fd-5a8f-0123-552d-f8fe19bd0784', 'surname': 'Abotsi', 'othernames': 'Haggai Etse', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'd459a9db-5402-477d-9ad7-fab68e99af09', 'surname': 'NIGNAMOAN', 'othernames': 'GODWIN SOBOAN', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '2f0770bb-96b6-2026-7129-23a8c47120ff', 'surname': 'Addo', 'othernames': 'Beatrice', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '4235F1C3-1F76-8665-7DDB-734B40C1576C', 'surname': 'NKETIAH', 'othernames': 'BERNARD SARFO', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '9022ab65-89ca-7e22-9b07-7e6911e059a2', 'surname': 'Agyei', 'othernames': 'Isaac', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '7952CD2A-3BE4-78AE-3A93-A5C7698401BA', 'surname': 'MOHAMMED', 'othernames': 'NAZIF IBRAHIM', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '0141DB50-8456-3E4A-3220-3E2758989EA9', 'surname': 'OSEI', 'othernames': 'CALEB KWADWO', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'aa3dc5f9-5555-48e6-8564-19e087df8a1d', 'surname': 'TAMATEY', 'othernames': 'ERNEST', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '3b49e8c5-d977-4d31-9208-ee28a4b57339', 'surname': 'AGYEI', 'othernames': 'PORTIA', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '2d5d4719-faab-4138-9cc0-b2f5c221317b', 'surname': 'BOTCHWAY', 'othernames': 'CHRISTIANA', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'fdd620b9-d13b-41f7-9bc3-cf3993cc7bf5', 'surname': 'MENSAH', 'othernames': 'GEORGE', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'D43FF1F9-12FC-0B42-37D8-7D7BBFA13072', 'surname': 'ACQUAH', 'othernames': 'BERTHA', 'sex': 'F', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'e50c6733-5d14-5f2c-8dbe-7d9ab3d19e46', 'surname': 'Atsikpo', 'othernames': 'Bertha Abla', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '5f1113f4-f845-4fc0-88a7-69ef94f5c0b8', 'surname': 'Adza', 'othernames': 'Amanda Mawutor Adzoa', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '2230A8BB-7A8F-5CE1-2D1D-87D27AEA5EC7', 'surname': 'TETTEH', 'othernames': 'BENJAMIN', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'EE4DC08B-1B2C-095E-95EB-B9C7DD796BF3', 'surname': 'ASARE-DUKU', 'othernames': 'KWADWO', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'ffa276d9-799d-4ecb-bee2-52f9f7e57906', 'surname': 'KWEYATEY', 'othernames': 'PATRICIA', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}]
2025-08-11 22:27:10,152 - root - INFO - 'No results'
2025-08-11 22:27:10,152 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-11 22:27:18,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:18,898 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-11 22:27:29,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:29,726 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:27:29,727 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 22:27:29,727 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:27:29,727 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student performance at ITC University'
2025-08-11 22:27:29,727 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 22:27:29,727 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 22:27:29,727 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 22:27:29,727 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 22:27:29,727 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are you interested in comparing student performance across different programs or departments within ...
2025-08-11 22:27:29,727 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Yes, we can compare student performance across different programs at ITC University. The data shows ...
2025-08-11 22:27:29,727 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: bar_chart
2025-08-11 22:27:29,727 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'average_score'...
2025-08-11 22:27:29,727 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_scores_by_program
2025-08-11 22:27:29,727 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-11 22:27:29,727 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-11 22:27:29,727 - celery.redirected - WARNING - [{'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'average_score': 84.0, 'student_count': 1}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'average_score': 76.34, 'student_count': 43}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'average_score': 73.0, 'student_count': 2}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'average_score': 70.0, 'student_count': 1}, {'program_name': 'Master Of Arts In Educational Leadership', 'average_score': 70.0, 'student_count': 1}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'average_score': 63.4, 'student_count': 21}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'average_score': 60.0, 'student_count': 1}, {'program_name': 'BACHELOR OF SCIENCE (MATHEMATICS EDUCATION)', 'average_score': 55.0, 'student_count': 1}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'average_score': 54.16, 'student_count': 49}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'average_score': None, 'student_count': 5}, {'program_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'average_score': None, 'student_count': 4}, {'program_name': 'Bachelor Of Medicine And Surgery', 'average_score': None, 'student_count': 2}]
2025-08-11 22:27:29,728 - celery.redirected - WARNING - ================================= 
2025-08-11 22:27:29,728 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: average_scores_by_program
2025-08-11 22:27:29,728 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-11 22:27:29,728 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:27:29,728 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 22:27:29,728 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:27:29,728 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student performance at ITC University'
2025-08-11 22:27:29,728 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 22:27:29,728 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 22:27:29,728 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 22:27:29,729 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 22:27:29,729 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Would you like to see the performance of students who are on scholarships versus those who are not?...
2025-08-11 22:27:29,729 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The performance of students who are on scholarships versus those who are not shows that all the stud...
2025-08-11 22:27:29,729 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-11 22:27:29,729 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'student_id': '59E395BD-A4E8-3760-5A11-03090E1F7FA7', 'surname': 'PREMPEH', 'othernames': 'PRINCET...
2025-08-11 22:27:29,729 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_performance_by_scholarship_status
2025-08-11 22:27:29,729 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-11 22:27:29,729 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-11 22:27:29,729 - celery.redirected - WARNING - [{'student_id': '59E395BD-A4E8-3760-5A11-03090E1F7FA7', 'surname': 'PREMPEH', 'othernames': 'PRINCETON NANA OSEI', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '4d2c8ec8-983c-6b94-83b5-5e6663a78464', 'surname': 'Fosu', 'othernames': 'Fredrick', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '8EF7C3EC-0287-A4B3-771D-B65E9E849746', 'surname': 'AGYAPOMAA', 'othernames': 'NAOMI OWUSU', 'sex': 'F', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '10a169fd-5a8f-0123-552d-f8fe19bd0784', 'surname': 'Abotsi', 'othernames': 'Haggai Etse', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'd459a9db-5402-477d-9ad7-fab68e99af09', 'surname': 'NIGNAMOAN', 'othernames': 'GODWIN SOBOAN', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '2f0770bb-96b6-2026-7129-23a8c47120ff', 'surname': 'Addo', 'othernames': 'Beatrice', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '4235F1C3-1F76-8665-7DDB-734B40C1576C', 'surname': 'NKETIAH', 'othernames': 'BERNARD SARFO', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '9022ab65-89ca-7e22-9b07-7e6911e059a2', 'surname': 'Agyei', 'othernames': 'Isaac', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '7952CD2A-3BE4-78AE-3A93-A5C7698401BA', 'surname': 'MOHAMMED', 'othernames': 'NAZIF IBRAHIM', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '0141DB50-8456-3E4A-3220-3E2758989EA9', 'surname': 'OSEI', 'othernames': 'CALEB KWADWO', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'aa3dc5f9-5555-48e6-8564-19e087df8a1d', 'surname': 'TAMATEY', 'othernames': 'ERNEST', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '3b49e8c5-d977-4d31-9208-ee28a4b57339', 'surname': 'AGYEI', 'othernames': 'PORTIA', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '2d5d4719-faab-4138-9cc0-b2f5c221317b', 'surname': 'BOTCHWAY', 'othernames': 'CHRISTIANA', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'fdd620b9-d13b-41f7-9bc3-cf3993cc7bf5', 'surname': 'MENSAH', 'othernames': 'GEORGE', 'sex': 'M', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'D43FF1F9-12FC-0B42-37D8-7D7BBFA13072', 'surname': 'ACQUAH', 'othernames': 'BERTHA', 'sex': 'F', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'e50c6733-5d14-5f2c-8dbe-7d9ab3d19e46', 'surname': 'Atsikpo', 'othernames': 'Bertha Abla', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '5f1113f4-f845-4fc0-88a7-69ef94f5c0b8', 'surname': 'Adza', 'othernames': 'Amanda Mawutor Adzoa', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': '2230A8BB-7A8F-5CE1-2D1D-87D27AEA5EC7', 'surname': 'TETTEH', 'othernames': 'BENJAMIN', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'EE4DC08B-1B2C-095E-95EB-B9C7DD796BF3', 'surname': 'ASARE-DUKU', 'othernames': 'KWADWO', 'sex': 'M', 'status': 'Active', 'average_gpa': 4.0, 'scholarship_count': 0}, {'student_id': 'ffa276d9-799d-4ecb-bee2-52f9f7e57906', 'surname': 'KWEYATEY', 'othernames': 'PATRICIA', 'sex': 'F', 'status': 'active', 'average_gpa': 4.0, 'scholarship_count': 0}]
2025-08-11 22:27:29,729 - celery.redirected - WARNING - ================================= 
2025-08-11 22:27:29,729 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-11 22:27:29,730 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:27:29,730 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 22:27:29,730 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student performance at ITC University'
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 22:27:29,730 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific courses or subjects at ITC University that you want to focus on in the performanc...
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: There are currently no specific courses or subjects at ITC University identified for focus in the pe...
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_courses_identified_for_performance_report
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-11 22:27:29,730 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-11 22:27:29,731 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 3
2025-08-11 22:27:29,731 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:27:29,731 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-11 22:27:29,731 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 3 documents
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO -     Content: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO -     Content: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO -     Content: Question: Are there specific courses or subjects at ITC University that you want to focus on in the ...
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:27:10.151939+00:00', 'data_returned': False}
2025-08-11 22:27:29,731 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 2/3
2025-08-11 22:27:29,860 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.128s]
2025-08-11 22:27:33,999 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 22:27:34,671 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.524s]
2025-08-11 22:27:34,672 - UPSERT_DOCS - INFO - ✅ Successfully upserted 3 documents to Elasticsearch
2025-08-11 22:27:34,673 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 5
2025-08-11 22:27:34,673 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-11 22:27:34,674 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:27:34,674 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-11 22:27:34,674 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:27:34,675 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/5...
2025-08-11 22:27:35,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:35,537 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:27:35,537 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 22:27:35,537 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:27:35,538 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student performance analysis ITC University'
2025-08-11 22:27:35,538 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student performance at ITC University'
2025-08-11 22:27:35,538 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student performance at ITC University'
2025-08-11 22:27:35,670 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-11 22:27:35,670 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 128
2025-08-11 22:27:35,798 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-11 22:27:35,798 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 70
2025-08-11 22:27:35,928 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 22:27:35,929 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-11 22:27:36,058 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 22:27:36,059 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-11 22:27:36,454 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 22:27:36,625 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.168s]
2025-08-11 22:27:36,626 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-11 22:27:36,626 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:27:36,626 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:27:36,626 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:27:36,627 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:27:36,628 - app.chains.section_writer - INFO -   📄 Doc 1: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:27:36,628 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:27:36,628 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_program
2025-08-11 22:27:36,628 - app.chains.section_writer - INFO -   📄 Doc 2: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:27:36,628 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:27:36,628 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1675 chars):
2025-08-11 22:27:36,628 - app.chains.section_writer - INFO -    Question: Are you interested in comparing student performance across different programs or departments within ITC University?
Answer: Yes, we can compare student performance across different programs at ITC University. The data shows the average scores and student counts for various programs. For instance, the Bachelor of Business Administration (Human Resource Management) has an average score of 84.0 with 1 student, while the Bachelor of Science (Clothing and Textiles Education) has an average ...
2025-08-11 22:27:38,817 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:38,852 - app.chains.section_writer - INFO - 🤖 AI generated section (451 chars):
2025-08-11 22:27:38,852 - app.chains.section_writer - INFO -    This report aims to analyze student performance at ITC University across various academic programs, highlighting key metrics and trends. Understanding these performance indicators is crucial for enhancing educational outcomes and program development. The analysis will include comparisons of average ...
2025-08-11 22:27:38,852 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_scores_by_program']
2025-08-11 22:27:38,852 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 461 characters
2025-08-11 22:27:38,853 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/5...
2025-08-11 22:27:39,571 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:39,582 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:27:39,582 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 22:27:39,582 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:27:39,582 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University overview history programs demographics'
2025-08-11 22:27:39,582 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student performance at ITC University'
2025-08-11 22:27:39,582 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student performance at ITC University'
2025-08-11 22:27:39,719 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-11 22:27:39,719 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 128
2025-08-11 22:27:39,847 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-11 22:27:39,847 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 70
2025-08-11 22:27:39,977 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 22:27:39,978 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-11 22:27:40,105 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-11 22:27:40,106 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-11 22:27:41,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 22:27:41,221 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.140s]
2025-08-11 22:27:41,222 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-11 22:27:41,222 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:27:41,222 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:27:41,222 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:27:41,223 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:27:41,224 - app.chains.section_writer - INFO -   📄 Doc 1: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:27:41,224 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:27:41,224 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_program
2025-08-11 22:27:41,224 - app.chains.section_writer - INFO -   📄 Doc 2: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:27:41,224 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:27:41,224 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1675 chars):
2025-08-11 22:27:41,224 - app.chains.section_writer - INFO -    Question: Are you interested in comparing student performance across different programs or departments within ITC University?
Answer: Yes, we can compare student performance across different programs at ITC University. The data shows the average scores and student counts for various programs. For instance, the Bachelor of Business Administration (Human Resource Management) has an average score of 84.0 with 1 student, while the Bachelor of Science (Clothing and Textiles Education) has an average ...
2025-08-11 22:27:50,892 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:50,897 - app.chains.section_writer - INFO - 🤖 AI generated section (1644 chars):
2025-08-11 22:27:50,897 - app.chains.section_writer - INFO -    ## 2. Overview of ITC University  

ITC University has established itself as a prominent institution in higher education, offering a diverse range of academic programs. The university's history is marked by a commitment to academic excellence and innovation, catering to the evolving needs of student...
2025-08-11 22:27:50,897 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_scores_by_program']
2025-08-11 22:27:50,897 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 1629 characters
2025-08-11 22:27:50,898 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/5...
2025-08-11 22:27:51,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:27:51,716 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:27:51,716 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 22:27:51,716 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:27:51,716 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Comparative analysis student performance programs'
2025-08-11 22:27:51,716 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student performance at ITC University'
2025-08-11 22:27:51,716 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student performance at ITC University'
2025-08-11 22:27:51,845 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 22:27:51,846 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 128
2025-08-11 22:27:51,974 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-11 22:27:51,974 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 70
2025-08-11 22:27:52,104 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 22:27:52,104 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-11 22:27:52,230 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-11 22:27:52,231 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-11 22:27:52,735 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 22:27:52,871 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.132s]
2025-08-11 22:27:52,872 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-11 22:27:52,872 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:27:52,872 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:27:52,872 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:27:52,872 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:27:52,873 - app.chains.section_writer - INFO -   📄 Doc 1: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:27:52,874 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:27:52,874 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_program
2025-08-11 22:27:52,874 - app.chains.section_writer - INFO -   📄 Doc 2: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:27:52,874 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:27:52,874 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1675 chars):
2025-08-11 22:27:52,874 - app.chains.section_writer - INFO -    Question: Are you interested in comparing student performance across different programs or departments within ITC University?
Answer: Yes, we can compare student performance across different programs at ITC University. The data shows the average scores and student counts for various programs. For instance, the Bachelor of Business Administration (Human Resource Management) has an average score of 84.0 with 1 student, while the Bachelor of Science (Clothing and Textiles Education) has an average ...
2025-08-11 22:28:02,295 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:28:02,304 - app.chains.section_writer - INFO - 🤖 AI generated section (2693 chars):
2025-08-11 22:28:02,304 - app.chains.section_writer - INFO -    ## 3. Comparative Analysis of Student Performance by Program  

### 3.1 Overview of Programs  
The following programs were analyzed for student performance:  
- **Bachelor of Business Administration (Human Resource Management)**: Focuses on developing skills in managing human resources within organi...
2025-08-11 22:28:02,305 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_scores_by_program']
2025-08-11 22:28:02,305 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 2678 characters
2025-08-11 22:28:02,305 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/5...
2025-08-11 22:28:02,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:28:02,993 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:28:02,994 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 22:28:02,994 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:28:02,994 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Scholarship vs Non-Scholarship Student Performance'
2025-08-11 22:28:02,994 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student performance at ITC University'
2025-08-11 22:28:02,994 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student performance at ITC University'
2025-08-11 22:28:03,121 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-11 22:28:03,122 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 128
2025-08-11 22:28:03,251 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 22:28:03,251 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 70
2025-08-11 22:28:03,382 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 22:28:03,382 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-11 22:28:03,512 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 22:28:03,513 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-11 22:28:04,475 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 22:28:04,616 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.138s]
2025-08-11 22:28:04,617 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-11 22:28:04,617 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:28:04,617 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:28:04,617 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:28:04,617 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:28:04,618 - app.chains.section_writer - INFO -   📄 Doc 1: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:28:04,618 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:28:04,618 - app.chains.section_writer - INFO -   📄 Doc 2: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:28:04,618 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:28:04,619 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_program
2025-08-11 22:28:04,619 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1675 chars):
2025-08-11 22:28:04,619 - app.chains.section_writer - INFO -    Question: Would you like to see the performance of students who are on scholarships versus those who are not?
Answer: The performance of students who are on scholarships versus those who are not shows that all the students listed have an average GPA of 4.0. However, it is important to note that none of the students in this dataset are on scholarships, as indicated by the scholarship_count being 0 for all entries. Therefore, we cannot directly compare the performance of scholarship students to no...
2025-08-11 22:28:08,894 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:28:08,905 - app.chains.section_writer - INFO - 🤖 AI generated section (1056 chars):
2025-08-11 22:28:08,906 - app.chains.section_writer - INFO -    ## 4. Performance of Scholarship vs. Non-Scholarship Students  

### 4.1 Overview of Scholarship Data  
The dataset regarding scholarships indicates that there are no scholarship recipients included. This absence limits the ability to analyze the performance of scholarship students in comparison to ...
2025-08-11 22:28:08,906 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_scores_by_program']
2025-08-11 22:28:08,906 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 1041 characters
2025-08-11 22:28:08,906 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/5...
2025-08-11 22:28:10,729 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:28:10,733 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:28:10,733 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 22:28:10,733 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:28:10,733 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion key findings recommendations student performance monitoring'
2025-08-11 22:28:10,733 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student performance at ITC University'
2025-08-11 22:28:10,733 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student performance at ITC University'
2025-08-11 22:28:10,860 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-11 22:28:10,861 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 128
2025-08-11 22:28:10,990 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 22:28:10,991 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 70
2025-08-11 22:28:11,119 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-11 22:28:11,120 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-11 22:28:11,246 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-11 22:28:11,246 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 2
2025-08-11 22:28:12,042 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 22:28:12,176 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.131s]
2025-08-11 22:28:12,177 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 2 documents
2025-08-11 22:28:12,177 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:28:12,177 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:28:12,177 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:28:12,177 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:28:12,178 - app.chains.section_writer - INFO -   📄 Doc 1: Question: Would you like to see the performance of students who are on scholarships versus those who...
2025-08-11 22:28:12,179 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:24.051879+00:00', 'data_returned': True}
2025-08-11 22:28:12,179 - app.chains.section_writer - INFO -   📄 Doc 2: Question: Are you interested in comparing student performance across different programs or departmen...
2025-08-11 22:28:12,179 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student performance at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T22:25:08.791140+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_program'}
2025-08-11 22:28:12,179 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_program
2025-08-11 22:28:12,179 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1675 chars):
2025-08-11 22:28:12,179 - app.chains.section_writer - INFO -    Question: Would you like to see the performance of students who are on scholarships versus those who are not?
Answer: The performance of students who are on scholarships versus those who are not shows that all the students listed have an average GPA of 4.0. However, it is important to note that none of the students in this dataset are on scholarships, as indicated by the scholarship_count being 0 for all entries. Therefore, we cannot directly compare the performance of scholarship students to no...
2025-08-11 22:28:16,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 22:28:16,497 - app.chains.section_writer - INFO - 🤖 AI generated section (1287 chars):
2025-08-11 22:28:16,498 - app.chains.section_writer - INFO -    ## 5. Conclusion  

In summary, the analysis reveals that all students in the dataset have achieved an average GPA of 4.0; however, it is crucial to note that there are no scholarship recipients included in this data, preventing a direct comparison between scholarship and non-scholarship students. F...
2025-08-11 22:28:16,498 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_scores_by_program']
2025-08-11 22:28:16,498 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 1272 characters
2025-08-11 22:28:16,499 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 22:28:16,499 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-11 22:28:16,500 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 22:28:16,500 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-11 22:28:16,500 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 6
2025-08-11 22:28:16,500 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 3
2025-08-11 22:28:16,500 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 1
2025-08-11 22:28:16,500 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250811_222359.log
2025-08-11 22:28:16,504 - celery.app.trace - INFO - Task generate_streaming_report[94aee6ec-03de-4a9a-93dd-d9238259b89f] succeeded in 256.59661804200005s: {'outline': '# Report on Student Performance at ITC University

## 1. Introduction  
   - This report aims to analyze student performance at ITC University across various academic programs, highlighting key metrics and trends. Understanding these performance indicators is crucial for enhancing educational outcomes and program development.

## 2. Overview of ITC University  
   - Brief history and background of ITC University  
   - Description of academic programs offered  
   - Overview of student demographics  

## 3. Comparative Analysis of Student Performance by Program  
   ### 3.1 Overview of Programs  
   - List of programs analyzed  
   - Brief description of each program\'s focus  
   
   ### 3.2 Performance Metrics  
   - Average scores and student counts for each program  
   - Highlighting programs with notable performance  
     - **Bachelor of Business Administration (Human Resource Management)**  
       - Average Score: 84.0  
       - Student Count: 1  
     - **Bachelor of Science (Clothing...', ...}

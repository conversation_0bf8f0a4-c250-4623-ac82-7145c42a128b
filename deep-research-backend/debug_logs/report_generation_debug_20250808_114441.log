2025-08-08 11:44:41,836 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_114441.log
2025-08-08 11:44:41,837 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:44:41,837 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: e0fe5495-d292-4a0d-acb1-fdffdd529a42
2025-08-08 11:44:41,837 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:44:41,837 - REPORT_REQUEST - INFO - 📝 Original Question: 'What institution has the most students?'
2025-08-08 11:44:41,837 - REPORT_REQUEST - INFO - 🆔 Task ID: e0fe5495-d292-4a0d-acb1-fdffdd529a42
2025-08-08 11:44:41,837 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T11:44:41.837167
2025-08-08 11:44:42,035 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.198s]
2025-08-08 11:44:42,035 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 69
2025-08-08 11:44:42,229 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.194s]
2025-08-08 11:44:42,230 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 38
2025-08-08 11:44:42,426 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:0.196s]
2025-08-08 11:44:42,427 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (6):
2025-08-08 11:44:42,427 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (20 docs)
2025-08-08 11:44:42,427 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (15 docs)
2025-08-08 11:44:42,427 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (11 docs)
2025-08-08 11:44:42,428 - ELASTICSEARCH_STATE - INFO -   - 'How many girls are at ITC University?' (10 docs)
2025-08-08 11:44:42,428 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 11:44:42,428 - ELASTICSEARCH_STATE - INFO -   - 'string' (5 docs)
2025-08-08 11:44:42,428 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:44:42,428 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 11:44:42,428 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:44:42,428 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: What institution has the most students?
2025-08-08 11:44:42,428 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-08 11:44:42,429 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 11:44:50,981 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:50,994 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 11:44:54,882 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:54,902 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-08 11:44:56,554 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:56,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:56,698 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:56,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:56,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:57,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:57,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:57,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:57,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:57,451 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:57,518 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-08 11:44:59,670 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:59,912 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:44:59,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:00,032 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:00,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:00,360 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:01,524 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:02,157 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:02,474 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:02,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:02,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:02,720 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:03,255 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:03,374 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:03,952 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:04,198 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:04,233 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:05,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:07,146 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:07,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:07,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:07,616 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:09,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:11,022 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:11,891 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:11,892 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:11,901 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the demographic breakdown of students at the institution with the most students?', 'sql': 'SELECT s.sex, COUNT(*) AS student_count\nFROM core.students s\nJOIN core.institutions i ON s.institution_id = i.id\nWHERE i.id = (SELECT institution_id FROM core.students GROUP BY institution_id ORDER BY COUNT(*) DESC LIMIT 1)\nGROUP BY s.sex;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by using a subquery that groups students by institution_id and orders them by the count of students in descending order, limiting the result to the top institution. It then joins the students table with the institutions table to ensure that the demographic breakdown is specific to that institution. Finally, it groups the results by sex and counts the number of students for each sex, which provides the demographic breakdown requested in the question.', 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by also including other demographic factors such as age or nationality if those are relevant to the breakdown. Additionally, clarifying the term 'demographic breakdown' in the question could help ensure that all relevant demographic factors are considered."}
2025-08-08 11:45:11,901 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:11,901 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:45:12,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:12,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:13,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:14,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:14,374 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:14,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:15,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:16,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:16,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:16,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:17,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:17,453 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:17,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:17,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:19,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:19,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:20,495 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:20,796 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:21,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:21,469 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the retention rate of students at the institution with the most students?', 'sql': "WITH student_counts AS (SELECT institution_id, COUNT(*) AS total_students FROM core.students GROUP BY institution_id), max_institution AS (SELECT institution_id FROM student_counts ORDER BY total_students DESC LIMIT 1), retained_students AS (SELECT COUNT(*) AS retained FROM core.student_statuses s JOIN max_institution m ON s.student_id IN (SELECT id FROM core.students WHERE institution_id = m.institution_id) WHERE s.student_status_type_id = (SELECT id FROM core.student_status_types WHERE status = 'active')) SELECT (CAST(retained AS FLOAT) / total_students) * 100 AS retention_rate FROM student_counts sc JOIN max_institution mi ON sc.institution_id = mi.institution_id JOIN (SELECT COUNT(*) AS retained FROM core.student_statuses s JOIN core.students st ON s.student_id = st.id WHERE st.institution_id = mi.institution_id AND s.student_status_type_id = (SELECT id FROM core.student_status_types WHERE status = 'active')) r ON true;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the total number of students per institution and selecting the one with the maximum count. It then calculates the retention rate by counting the number of retained students (those with an 'active' status) and dividing that by the total number of students at that institution. The use of common table expressions (CTEs) helps to structure the query logically, making it clear how the retention rate is derived from the data.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be simplified by directly calculating the retention rate in a single step after identifying the institution with the most students, rather than using multiple CTEs. Additionally, it might be beneficial to clarify what is meant by 'retention' in the context of the question, as it assumes that 'active' status equates to retention."}
2025-08-08 11:45:21,469 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:21,470 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:45:21,742 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:22,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:22,357 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:22,376 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:24,335 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:24,934 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:24,948 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:45:24,949 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:24,949 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:45:24,949 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:45:25,481 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:25,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:26,506 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:26,510 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:26,511 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total number of students enrolled at the institution with the most students?', 'sql': 'SELECT COUNT(*) AS total_students\nFROM students\nWHERE institution_id = (\n    SELECT institution_id\n    FROM students\n    GROUP BY institution_id\n    ORDER BY COUNT(*) DESC\n    LIMIT 1\n);', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by grouping the records in the 'students' table by 'institution_id', counting the number of students for each institution, and ordering the results in descending order. The subquery retrieves the 'institution_id' of the institution with the highest count of students. The outer query then counts the total number of students for that specific institution, which directly answers the question about the total number of students enrolled at the institution with the most students.", 'feedback': 'The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to add a comment in the SQL to clarify the purpose of the subquery for future reference.'}
2025-08-08 11:45:26,511 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:26,511 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:45:28,010 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:28,112 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:28,118 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the student population of the institution with the most students compare to other institutions?', 'sql': "WITH student_counts AS (SELECT institution_id, COUNT(*) AS student_count FROM core.students GROUP BY institution_id), max_count AS (SELECT MAX(student_count) AS max_student_count FROM student_counts) SELECT sc.institution_id, sc.student_count, CASE WHEN sc.student_count = mc.max_student_count THEN 'Most Students' ELSE 'Other' END AS comparison FROM student_counts sc, max_count mc;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by first counting the number of students per institution and then determining the maximum student count. It uses a common table expression (CTE) to create a list of student counts and another CTE to find the maximum count. The final SELECT statement compares each institution's student count to the maximum count and labels them accordingly. This directly addresses the question of how the student population of the institution with the most students compares to others.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly including the names of the institutions in the final output for better clarity, as the question implies a comparison that might be more meaningful with identifiable institution names.'}
2025-08-08 11:45:28,118 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:28,118 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:45:28,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:29,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:29,239 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How has the student enrollment at the institution with the most students changed over the past few years?', 'sql': 'SELECT ay.start_year, COUNT(s.id) AS student_count\nFROM students s\nJOIN institutions i ON s.institution_id = i.id\nJOIN academic_years ay ON ay.institution_id = i.id\nWHERE i.id = (\n    SELECT institution_id\n    FROM students\n    GROUP BY institution_id\n    ORDER BY COUNT(id) DESC\n    LIMIT 1\n)\nGROUP BY ay.start_year\nORDER BY ay.start_year;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and selecting the one with the highest count. It then joins the 'students' table with the 'academic_years' table to count the number of students enrolled in each academic year for that institution. The results are grouped by the start year of the academic years, which aligns with the question about how enrollment has changed over the past few years.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by ensuring that the 'academic_years' table includes only relevant years (e.g., filtering out years that are not in the past few years) to focus on recent enrollment changes. Additionally, clarifying the time frame in the question (e.g., 'over the last 3 years') could help refine the query further."}
2025-08-08 11:45:29,239 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:29,239 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:45:30,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:30,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:31,414 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:32,094 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:32,096 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are most popular at the institution with the most students?', 'sql': 'SELECT p.long_name, COUNT(sp.student_id) AS student_count\nFROM programs p\nJOIN student_programs sp ON p.id = sp.program_id\nWHERE p.institution_id = (\n    SELECT institution_id\n    FROM students\n    GROUP BY institution_id\n    ORDER BY COUNT(id) DESC\n    LIMIT 1\n)\nGROUP BY p.id, p.long_name\nORDER BY student_count DESC;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and selecting the one with the highest count. It then joins the 'programs' and 'student_programs' tables to count how many students are enrolled in each program at that institution. The results are grouped by program and ordered by the number of students, which aligns with the question's request for the most popular programs.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be beneficial to clarify whether 'most popular' refers strictly to the number of students or if other factors (like course ratings) should be considered. Additionally, including a limit on the number of results returned could enhance the query by focusing on the top programs."}
2025-08-08 11:45:32,096 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:32,097 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:32,097 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:45:32,273 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:33,082 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:33,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:33,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:34,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:36,852 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:38,195 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:38,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:39,954 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:40,075 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:42,466 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:42,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:43,189 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:43,204 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:45:43,204 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:43,204 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:45:43,204 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:45:44,725 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:44,750 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the challenges in obtaining accurate retention rate data for large institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to challenges in obtaining accurate retention rate data for large institutions. This is a qualitative inquiry that requires insights, opinions, or analysis rather than specific data points or relationships that can be derived from the provided database schema. The schema contains tables related to institutions, students, programs, and various academic and administrative records, but it does not provide any direct information or metrics regarding retention rates or the challenges associated with them.', 'feedback': ''}
2025-08-08 11:45:44,750 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:44,750 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:45:44,750 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the challenges in obtaining accurate retention rate data for large institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to challenges in obtaining accurate retention rate data for large institutions. This is a qualitative inquiry that requires insights, opinions, or analysis rather than specific data points or relationships that can be derived from the provided database schema. The schema contains tables related to institutions, students, programs, and various academic and administrative records, but it does not provide any direct information or metrics regarding retention rates or the challenges associated with them.', 'feedback': ''}
2025-08-08 11:45:45,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:46,885 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:46,914 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and various related entities, but it does not provide specific metrics or qualitative factors that would directly explain enrollment trends. To answer this question, one would need to analyze data such as marketing efforts, student satisfaction, program offerings, and external factors, none of which are explicitly captured in the schema.', 'feedback': ''}
2025-08-08 11:45:46,915 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:46,915 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:45:46,915 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and various related entities, but it does not provide specific metrics or qualitative factors that would directly explain enrollment trends. To answer this question, one would need to analyze data such as marketing efforts, student satisfaction, program offerings, and external factors, none of which are explicitly captured in the schema.', 'feedback': ''}
2025-08-08 11:45:47,454 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:47,479 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think have contributed to this stability in enrollment numbers at the institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to stability in enrollment numbers at the institution. However, the provided schema does not contain specific data or metrics related to enrollment trends, historical enrollment numbers, or factors influencing enrollment stability. While there are tables related to students, programs, and admissions, they do not provide the necessary analytical context or historical data to answer the question comprehensively.', 'feedback': ''}
2025-08-08 11:45:47,479 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:47,480 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:45:47,480 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think have contributed to this stability in enrollment numbers at the institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to stability in enrollment numbers at the institution. However, the provided schema does not contain specific data or metrics related to enrollment trends, historical enrollment numbers, or factors influencing enrollment stability. While there are tables related to students, programs, and admissions, they do not provide the necessary analytical context or historical data to answer the question comprehensively.', 'feedback': ''}
2025-08-08 11:45:47,836 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:47,880 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to the significant difference in student populations between the largest institution and its smaller counterparts?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in student populations between institutions. While the schema contains data about institutions and students, it does not provide specific attributes or metrics that would allow for a comprehensive analysis of the factors influencing student population sizes. Factors such as institutional policies, geographic location, program offerings, and other qualitative aspects are not represented in the schema. Therefore, the question cannot be answered directly or indirectly using the available data.', 'feedback': ''}
2025-08-08 11:45:47,880 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:45:47,880 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:45:47,880 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to the significant difference in student populations between the largest institution and its smaller counterparts?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in student populations between institutions. While the schema contains data about institutions and students, it does not provide specific attributes or metrics that would allow for a comprehensive analysis of the factors influencing student population sizes. Factors such as institutional policies, geographic location, program offerings, and other qualitative aspects are not represented in the schema. Therefore, the question cannot be answered directly or indirectly using the available data.', 'feedback': ''}
2025-08-08 11:45:48,020 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:45:48,020 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-08 11:45:48,021 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:45:48,021 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: <class 'celery.concurrency.solo.TaskPool'> does not implement kill_job
2025-08-08 11:45:48,021 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_114441.log
2025-08-08 11:45:48,025 - celery.app.trace - INFO - Task generate_streaming_report[c3ec2db1-212a-4ce8-9e63-b4f9ccc59bf5] succeeded in 66.18983070800095s: {'error': 'Error generating streaming report: <class \'celery.concurrency.solo.TaskPool\'> does not implement kill_job'}
2025-08-08 11:45:48,140 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:45:48,748 - celery.redirected - WARNING - Exception ignored in: 
2025-08-08 11:45:48,748 - celery.redirected - WARNING - <module 'threading' from '/opt/anaconda3/lib/python3.12/threading.py'>
2025-08-08 11:45:48,748 - celery.redirected - WARNING - Traceback (most recent call last):
2025-08-08 11:45:48,748 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/threading.py", line 1594, in _shutdown
2025-08-08 11:45:48,750 - celery.redirected - WARNING -     
2025-08-08 11:45:48,750 - celery.redirected - WARNING - atexit_call()
2025-08-08 11:45:48,750 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/concurrent/futures/thread.py", line 31, in _python_exit
2025-08-08 11:45:48,751 - celery.redirected - WARNING -     
2025-08-08 11:45:48,751 - celery.redirected - WARNING - t.join()
2025-08-08 11:45:48,751 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/threading.py", line 1149, in join
2025-08-08 11:45:48,751 - celery.redirected - WARNING -     
2025-08-08 11:45:48,751 - celery.redirected - WARNING - self._wait_for_tstate_lock()
2025-08-08 11:45:48,751 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/threading.py", line 1169, in _wait_for_tstate_lock
2025-08-08 11:45:48,752 - celery.redirected - WARNING -     
2025-08-08 11:45:48,752 - celery.redirected - WARNING - if lock.acquire(block, timeout):
2025-08-08 11:45:48,752 - celery.redirected - WARNING -  
2025-08-08 11:45:48,752 - celery.redirected - WARNING -  
2025-08-08 11:45:48,752 - celery.redirected - WARNING -  
2025-08-08 11:45:48,752 - celery.redirected - WARNING -  
2025-08-08 11:45:48,752 - celery.redirected - WARNING -  
2025-08-08 11:45:48,752 - celery.redirected - WARNING -  
2025-08-08 11:45:48,752 - celery.redirected - WARNING -  
2025-08-08 11:45:48,752 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,752 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,752 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,752 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,752 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,753 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,753 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,753 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,753 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,753 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING - ^
2025-08-08 11:45:48,754 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/apps/worker.py", line 300, in _handle_request
2025-08-08 11:45:48,755 - celery.redirected - WARNING -     
2025-08-08 11:45:48,755 - celery.redirected - WARNING - callback(worker)
2025-08-08 11:45:48,755 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/apps/worker.py", line 329, in on_hard_shutdown
2025-08-08 11:45:48,756 - celery.redirected - WARNING -     
2025-08-08 11:45:48,756 - celery.redirected - WARNING - raise WorkerTerminate(EX_FAILURE)
2025-08-08 11:45:48,756 - celery.redirected - WARNING - celery.exceptions
2025-08-08 11:45:48,756 - celery.redirected - WARNING - .
2025-08-08 11:45:48,756 - celery.redirected - WARNING - WorkerTerminate
2025-08-08 11:45:48,756 - celery.redirected - WARNING - : 
2025-08-08 11:45:48,756 - celery.redirected - WARNING - 1

2025-08-08 11:10:58,496 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_111058.log
2025-08-08 11:10:58,497 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:10:58,497 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 4aa23d07-77c0-46b5-8143-18eab5cedfcc
2025-08-08 11:10:58,497 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:10:58,497 - REPORT_REQUEST - INFO - 📝 Original Question: 'What institution has the most students?'
2025-08-08 11:10:58,497 - REPORT_REQUEST - INFO - 🆔 Task ID: 4aa23d07-77c0-46b5-8143-18eab5cedfcc
2025-08-08 11:10:58,497 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T11:10:58.497241
2025-08-08 11:10:58,625 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 11:10:58,626 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 65
2025-08-08 11:10:58,756 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 11:10:58,757 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 36
2025-08-08 11:10:58,889 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.133s]
2025-08-08 11:10:58,890 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (6):
2025-08-08 11:10:58,890 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (20 docs)
2025-08-08 11:10:58,890 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (15 docs)
2025-08-08 11:10:58,890 - ELASTICSEARCH_STATE - INFO -   - 'How many girls are at ITC University?' (10 docs)
2025-08-08 11:10:58,890 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 11:10:58,890 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (7 docs)
2025-08-08 11:10:58,891 - ELASTICSEARCH_STATE - INFO -   - 'string' (5 docs)
2025-08-08 11:10:58,891 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:10:58,891 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 11:10:58,891 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:10:58,892 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: What institution has the most students?
2025-08-08 11:10:58,892 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-08 11:10:58,892 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 11:11:11,962 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:11,982 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 11:11:17,091 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:17,113 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-08 11:11:21,947 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:22,063 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:22,140 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:22,222 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:22,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:22,693 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:22,794 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:22,811 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:22,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:23,158 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:23,209 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-08 11:11:25,601 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:25,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:26,087 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:26,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:28,348 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:28,758 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:29,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:29,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:29,971 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:30,529 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:30,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:31,436 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:34,417 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:35,141 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:35,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:38,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:39,857 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:39,862 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total number of students enrolled at the institution with the most students?', 'sql': 'SELECT institution_id, COUNT(*) AS total_students\nFROM core.students\nGROUP BY institution_id\nORDER BY total_students DESC\nLIMIT 1;', 'correct': True, 'reasoning': "The SQL query correctly counts the number of students for each institution by grouping the results by 'institution_id'. It then orders the results in descending order based on the count of students, ensuring that the institution with the most students appears first. The use of 'LIMIT 1' ensures that only the top result (the institution with the most students) is returned, which directly answers the question.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly selecting the count of students in the final output, such as by including 'total_students' in the SELECT clause for clarity."}
2025-08-08 11:11:39,862 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:11:39,862 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:11:41,205 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:41,217 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the demographic breakdown of students at the institution with the most students?', 'sql': 'SELECT s.sex, COUNT(*) AS student_count\nFROM core.students s\nJOIN auth.institutions i ON s.institution_id = i.id\nWHERE i.id = (SELECT institution_id FROM core.students GROUP BY institution_id ORDER BY COUNT(*) DESC LIMIT 1)\nGROUP BY s.sex;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by using a subquery that groups students by institution_id and orders them by the count of students in descending order, limiting the result to the top institution. It then joins the students table with the institutions table to ensure that the demographic breakdown is specifically for the institution with the most students. The query groups the results by sex and counts the number of students for each sex, which provides the demographic breakdown as requested in the question.', 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be enhanced by including additional demographic factors such as age or nationality if those were relevant to the breakdown. Additionally, clarifying the term 'demographic breakdown' in the question could help specify which demographic factors are of interest."}
2025-08-08 11:11:41,217 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:11:41,217 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:11:41,415 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:44,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:44,549 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the retention rate of students at the institution with the most students?', 'sql': 'WITH student_counts AS (SELECT institution_id, COUNT(*) AS total_students FROM core.students GROUP BY institution_id), max_institution AS (SELECT institution_id FROM student_counts ORDER BY total_students DESC LIMIT 1), retained_students AS (SELECT COUNT(*) AS retained FROM core.student_programs sp JOIN max_institution mi ON sp.institution_id = mi.institution_id WHERE sp.current = TRUE) SELECT (SELECT retained FROM retained_students) * 1.0 / (SELECT total_students FROM student_counts WHERE institution_id = (SELECT institution_id FROM max_institution)) AS retention_rate;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the total number of students per institution and selecting the one with the maximum count. It then calculates the number of retained students by checking the 'current' status in the student_programs table. Finally, it computes the retention rate by dividing the number of retained students by the total number of students at that institution. The logic follows the question's requirements accurately.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding comments to clarify each step of the query for better readability and maintainability.'}
2025-08-08 11:11:44,550 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:11:44,550 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:11:44,771 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:48,573 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:49,918 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:55,913 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:56,356 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:56,693 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:11:57,612 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:01,085 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:02,735 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:04,194 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:04,217 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or represented in the provided schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific qualitative data or analysis regarding the reasons for enrollment numbers. To answer this question, one would need insights into marketing strategies, student satisfaction, institutional reputation, or other external factors, none of which are captured in the schema.', 'feedback': ''}
2025-08-08 11:12:04,217 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:04,217 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:04,217 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or represented in the provided schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific qualitative data or analysis regarding the reasons for enrollment numbers. To answer this question, one would need insights into marketing strategies, student satisfaction, institutional reputation, or other external factors, none of which are captured in the schema.', 'feedback': ''}
2025-08-08 11:12:06,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:07,195 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:10,467 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:10,486 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or represented in the provided schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific data or metrics that would allow for an analysis of factors influencing enrollment numbers, such as marketing strategies, community engagement, or student satisfaction. Therefore, while the schema provides data about students and programs, it lacks the qualitative insights needed to answer the question.', 'feedback': ''}
2025-08-08 11:12:10,486 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:10,486 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:10,486 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or represented in the provided schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific data or metrics that would allow for an analysis of factors influencing enrollment numbers, such as marketing strategies, community engagement, or student satisfaction. Therefore, while the schema provides data about students and programs, it lacks the qualitative insights needed to answer the question.', 'feedback': ''}
2025-08-08 11:12:11,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:11,899 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:12,514 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:12,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:13,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:13,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:13,745 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:14,082 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:15,419 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:16,432 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:16,811 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:16,831 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or represented in the provided schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific data or metrics that would allow for an analysis of factors influencing enrollment numbers, such as marketing strategies, community engagement, or student satisfaction. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 11:12:16,831 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:16,831 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:16,832 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or represented in the provided schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific data or metrics that would allow for an analysis of factors influencing enrollment numbers, such as marketing strategies, community engagement, or student satisfaction. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 11:12:18,548 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:19,392 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:19,409 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 11:12:19,409 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:19,409 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:19,409 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 11:12:19,884 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:21,312 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:21,334 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available demographic data for such a large institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available demographic data for a large institution. This is a qualitative inquiry that requires insights, opinions, or contextual understanding that cannot be derived from the provided database schema. The schema contains structured data about various entities related to institutions, students, and programs, but it does not provide any information or context regarding the reasons behind the availability or lack of demographic data.', 'feedback': ''}
2025-08-08 11:12:21,334 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:21,334 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:21,334 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available demographic data for such a large institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available demographic data for a large institution. This is a qualitative inquiry that requires insights, opinions, or contextual understanding that cannot be derived from the provided database schema. The schema contains structured data about various entities related to institutions, students, and programs, but it does not provide any information or context regarding the reasons behind the availability or lack of demographic data.', 'feedback': ''}
2025-08-08 11:12:21,629 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:21,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:22,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:22,381 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a quantitative one. The schema provides data about institutions, applicants, and students, but it does not contain specific metrics or factors that directly correlate to enrollment numbers, such as marketing strategies, community engagement, or academic reputation. Therefore, while we can retrieve enrollment numbers, we cannot determine the contributing factors solely from the provided schema.', 'feedback': ''}
2025-08-08 11:12:22,382 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:22,382 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:22,382 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a quantitative one. The schema provides data about institutions, applicants, and students, but it does not contain specific metrics or factors that directly correlate to enrollment numbers, such as marketing strategies, community engagement, or academic reputation. Therefore, while we can retrieve enrollment numbers, we cannot determine the contributing factors solely from the provided schema.', 'feedback': ''}
2025-08-08 11:12:24,346 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:24,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:24,508 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 11:12:24,508 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:24,508 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:24,508 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 11:12:26,741 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:27,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:27,137 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available demographic data for such a large institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available demographic data for a large institution. This is a qualitative inquiry that requires insights, opinions, or contextual understanding that cannot be derived from the provided database schema. The schema contains structured data about various entities related to institutions, students, programs, and other operational aspects, but it does not provide any information or context regarding the reasons or factors affecting the availability of demographic data.', 'feedback': ''}
2025-08-08 11:12:27,138 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:27,138 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:27,138 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available demographic data for such a large institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available demographic data for a large institution. This is a qualitative inquiry that requires insights, opinions, or contextual understanding that cannot be derived from the provided database schema. The schema contains structured data about various entities related to institutions, students, programs, and other operational aspects, but it does not provide any information or context regarding the reasons or factors affecting the availability of demographic data.', 'feedback': ''}
2025-08-08 11:12:29,475 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:29,485 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are most popular at the institution with the highest student enrollment?', 'sql': "WITH InstitutionEnrollment AS (  SELECT institution_id, COUNT(*) AS student_count  FROM students  WHERE status = 'active'  GROUP BY institution_id  ORDER BY student_count DESC  LIMIT 1), PopularPrograms AS (  SELECT p.long_name AS program_name, COUNT(s.id) AS enrollment_count  FROM programs p  JOIN students s ON p.institution_id = s.institution_id  WHERE s.status = 'active' AND p.institution_id = (SELECT institution_id FROM InstitutionEnrollment)  GROUP BY p.long_name  ORDER BY enrollment_count DESC), PopularCourses AS (  SELECT c.title AS course_name, COUNT(s.id) AS enrollment_count  FROM courses c  JOIN students s ON c.institution_id = s.institution_id  WHERE s.status = 'active' AND c.institution_id = (SELECT institution_id FROM InstitutionEnrollment)  GROUP BY c.title  ORDER BY enrollment_count DESC) SELECT program_name, enrollment_count FROM PopularPrograms UNION ALL SELECT course_name, enrollment_count FROM PopularCourses;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest student enrollment by counting active students per institution. It then retrieves the most popular programs and courses at that institution by counting the number of enrollments for each program and course, respectively. The use of CTEs (Common Table Expressions) allows for clear separation of logic, and the final UNION ALL combines the results from both programs and courses, which aligns with the question's request for both types of offerings.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating the output format, such as including a column to differentiate between programs and courses in the final result set. This would enhance clarity for users reviewing the results.'}
2025-08-08 11:12:29,486 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:29,486 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:12:29,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:29,615 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 11:12:29,615 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:29,615 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:29,615 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 11:12:29,917 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:32,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:32,315 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:32,328 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available demographic data for such a large institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available demographic data for a large institution. This is a qualitative inquiry that requires insights, opinions, or contextual understanding that cannot be derived from the provided database schema, which contains structured data about various entities but does not include qualitative assessments or explanations regarding data availability or demographic factors.', 'feedback': ''}
2025-08-08 11:12:32,329 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:32,329 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:32,329 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available demographic data for such a large institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available demographic data for a large institution. This is a qualitative inquiry that requires insights, opinions, or contextual understanding that cannot be derived from the provided database schema, which contains structured data about various entities but does not include qualitative assessments or explanations regarding data availability or demographic factors.', 'feedback': ''}
2025-08-08 11:12:34,838 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:34,839 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:34,855 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 11:12:34,856 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:34,859 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:34,859 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 11:12:39,218 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:12:39,239 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available demographic data for such a large institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available demographic data for a large institution. This is a qualitative inquiry that requires insights, opinions, or contextual understanding that cannot be derived from the provided database schema. The schema contains structured data about various entities related to institutions, students, and programs, but it does not provide any information or context regarding the reasons behind the availability or lack of demographic data.', 'feedback': ''}
2025-08-08 11:12:39,239 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:12:39,239 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:12:39,239 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available demographic data for such a large institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available demographic data for a large institution. This is a qualitative inquiry that requires insights, opinions, or contextual understanding that cannot be derived from the provided database schema. The schema contains structured data about various entities related to institutions, students, and programs, but it does not provide any information or context regarding the reasons behind the availability or lack of demographic data.', 'feedback': ''}
2025-08-08 11:13:01,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:01,924 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:03,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:08,516 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:13,238 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:13,253 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights or opinions rather than specific data points. The schema provided contains a wealth of data about institutions, programs, courses, and students, but it does not include qualitative factors or insights that would explain popularity. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:13:13,253 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:13:13,254 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:13:13,254 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights or opinions rather than specific data points. The schema provided contains a wealth of data about institutions, programs, courses, and students, but it does not include qualitative factors or insights that would explain popularity. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:13:19,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:27,605 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:27,630 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights or opinions rather than specific data points. The schema provided contains a wealth of data about institutions, programs, courses, and students, but it does not include qualitative factors or insights that would explain popularity. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:13:27,630 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:13:27,630 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:13:27,630 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights or opinions rather than specific data points. The schema provided contains a wealth of data about institutions, programs, courses, and students, but it does not include qualitative factors or insights that would explain popularity. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:13:35,567 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:42,065 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:42,084 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights or opinions rather than specific data points. The schema provided contains a wealth of data about institutions, programs, courses, and students, but it does not include qualitative factors or insights that would explain popularity. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:13:42,085 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:13:42,085 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:13:42,085 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights or opinions rather than specific data points. The schema provided contains a wealth of data about institutions, programs, courses, and students, but it does not include qualitative factors or insights that would explain popularity. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:13:50,698 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:59,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:13:59,452 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights or opinions rather than specific data points. The schema provided contains a wealth of data about institutions, programs, courses, and students, but it does not include qualitative factors or insights that would explain popularity. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:13:59,452 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:13:59,452 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:13:59,453 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights or opinions rather than specific data points. The schema provided contains a wealth of data about institutions, programs, courses, and students, but it does not include qualitative factors or insights that would explain popularity. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:13:59,453 - root - INFO - [{'institution_id': 24, 'total_students': 192627}]
2025-08-08 11:13:59,453 - root - INFO - [{'retention_rate': 1.0}]
2025-08-08 11:13:59,453 - root - INFO - 'No results'
2025-08-08 11:13:59,453 - root - INFO - 'No results'
2025-08-08 11:13:59,453 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 11:14:05,983 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:06,004 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 11:14:13,835 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:13,859 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:13,859 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:14:13,860 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:14:13,860 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total number of students enrolled at the institution with the most students?...
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total number of students enrolled at the institution with the most students is 192,627....
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_id': 24, 'total_students': 192627}]...
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_students_at_largest_institution
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 11:14:13,860 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 11:14:13,860 - celery.redirected - WARNING - [{'institution_id': 24, 'total_students': 192627}]
2025-08-08 11:14:13,860 - celery.redirected - WARNING - ================================= 
2025-08-08 11:14:13,860 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 11:14:13,860 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:13,861 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:14:13,861 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:14:13,861 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are most popular at the institution with the highest student enrollment?...
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no results available for the programs or courses at the institution with t...
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: popular_courses_at_top_enrollment_institution
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 11:14:13,861 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:13,861 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:14:13,861 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:14:13,861 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:14:13,861 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the demographic breakdown of students at the institution with the most students?...
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no results available for the demographic breakdown of students at the inst...
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: demographic_breakdown_no_data
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 11:14:13,862 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:13,862 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:14:13,862 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:14:13,862 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the retention rate of students at the institution with the most students?...
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The retention rate of students at the institution with the most students is 100%. This indicates tha...
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'retention_rate': 1.0}]...
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: retention_rate_highest_enrollment_institution
2025-08-08 11:14:13,862 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 11:14:13,862 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 11:14:13,862 - celery.redirected - WARNING - [{'retention_rate': 1.0}]
2025-08-08 11:14:13,863 - celery.redirected - WARNING - ================================= 
2025-08-08 11:14:13,863 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 11:14:13,863 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 4
2025-08-08 11:14:13,863 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:13,863 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 11:14:13,863 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 4 documents
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -     Content: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:22.382219+00:00', 'data_returned': True}
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are most popular at the institution with the highest student enro...
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:13:59.453115+00:00', 'data_returned': False}
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -     Content: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:39.239927+00:00', 'data_returned': False}
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -     Content: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:14:13,863 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 2/4
2025-08-08 11:14:13,996 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.133s]
2025-08-08 11:14:14,989 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:14:15,592 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.537s]
2025-08-08 11:14:15,592 - UPSERT_DOCS - INFO - ✅ Successfully upserted 4 documents to Elasticsearch
2025-08-08 11:14:15,593 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 8
2025-08-08 11:14:15,593 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 11:14:15,593 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:15,593 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-08 11:14:15,593 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:15,593 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/8...
2025-08-08 11:14:17,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:17,088 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:17,088 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:14:17,088 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:17,088 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'highest student enrollment institution'
2025-08-08 11:14:17,089 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:14:17,089 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:14:17,217 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 11:14:17,217 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 69
2025-08-08 11:14:17,349 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 11:14:17,350 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 38
2025-08-08 11:14:17,478 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 11:14:17,478 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 11
2025-08-08 11:14:17,608 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 11:14:17,609 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-08 11:14:18,126 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:14:18,272 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.142s]
2025-08-08 11:14:18,273 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:14:18,273 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:18,273 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:22.382219+00:00', 'data_returned': True}
2025-08-08 11:14:18,273 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:18,273 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:03.070454+00:00', 'data_returned': True}
2025-08-08 11:14:18,273 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:14:18,273 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:14:18,273 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:18,274 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:14:18,274 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:14:18,274 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:52.812710+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 11:14:19,817 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:19,848 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 303 characters
2025-08-08 11:14:19,849 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/8...
2025-08-08 11:14:20,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:20,836 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:20,836 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:14:20,836 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:20,836 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'key terms definition institution student enrollment types'
2025-08-08 11:14:20,836 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:14:20,836 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:14:20,966 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 11:14:20,966 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 69
2025-08-08 11:14:21,094 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 11:14:21,095 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 38
2025-08-08 11:14:21,221 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 11:14:21,222 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 11
2025-08-08 11:14:21,350 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 11:14:21,350 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-08 11:14:21,910 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:14:22,051 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.137s]
2025-08-08 11:14:22,051 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:14:22,052 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:14:22,052 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:14:22,052 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:22,052 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:22.382219+00:00', 'data_returned': True}
2025-08-08 11:14:22,052 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:22,052 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:03.070454+00:00', 'data_returned': True}
2025-08-08 11:14:22,052 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:22,052 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:14:22,052 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:14:22,052 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:52.812710+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 11:14:28,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:28,241 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 1800 characters
2025-08-08 11:14:28,241 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/8...
2025-08-08 11:14:29,823 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:29,855 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:29,855 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:14:29,856 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:29,856 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology Data Sources Selection Criteria'
2025-08-08 11:14:29,856 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:14:29,856 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:14:29,983 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 11:14:29,984 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 69
2025-08-08 11:14:30,115 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 11:14:30,115 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 38
2025-08-08 11:14:30,243 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 11:14:30,244 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 11
2025-08-08 11:14:30,373 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 11:14:30,373 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-08 11:14:30,906 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:14:31,046 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.136s]
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:57.643107+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:52.812710+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:51.582492+00:00', 'data_returned': True}
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:14:31,047 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:14:36,165 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:36,170 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 1423 characters
2025-08-08 11:14:36,171 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/8...
2025-08-08 11:14:36,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:36,768 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:36,768 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:14:36,768 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:36,768 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'total enrollment retention rate findings'
2025-08-08 11:14:36,768 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:14:36,768 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:14:36,896 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 11:14:36,897 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 69
2025-08-08 11:14:37,032 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 11:14:37,032 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 38
2025-08-08 11:14:37,163 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 11:14:37,163 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 11
2025-08-08 11:14:37,291 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 11:14:37,292 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-08 11:14:37,901 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:14:38,043 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.138s]
2025-08-08 11:14:38,044 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:14:38,045 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:38,045 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:14:38,045 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:38,045 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:51.582492+00:00', 'data_returned': True}
2025-08-08 11:14:38,045 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:14:38,045 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:14:38,045 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:38,045 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:22.382219+00:00', 'data_returned': True}
2025-08-08 11:14:38,045 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:38,046 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:03.070454+00:00', 'data_returned': True}
2025-08-08 11:14:39,731 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:39,744 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 307 characters
2025-08-08 11:14:39,745 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/8...
2025-08-08 11:14:40,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:40,553 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:40,553 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:14:40,553 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:40,553 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Total Enrollment Retention Rate Analysis'
2025-08-08 11:14:40,553 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:14:40,553 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:14:40,680 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 11:14:40,680 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 69
2025-08-08 11:14:40,808 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 11:14:40,809 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 38
2025-08-08 11:14:40,938 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 11:14:40,938 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 11
2025-08-08 11:14:41,070 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 11:14:41,070 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-08 11:14:42,019 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:14:42,160 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.137s]
2025-08-08 11:14:42,160 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:14:42,160 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:42,160 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:14:42,161 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:42,161 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:51.582492+00:00', 'data_returned': True}
2025-08-08 11:14:42,161 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:14:42,161 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:14:42,161 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:42,161 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:22.382219+00:00', 'data_returned': True}
2025-08-08 11:14:42,161 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:42,161 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:03.070454+00:00', 'data_returned': True}
2025-08-08 11:14:45,371 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:45,375 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 990 characters
2025-08-08 11:14:45,376 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 6/8...
2025-08-08 11:14:48,064 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:48,070 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:48,071 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:14:48,071 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:48,071 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'factors influencing enrollment'
2025-08-08 11:14:48,071 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:14:48,071 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:14:48,199 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 11:14:48,200 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 69
2025-08-08 11:14:48,328 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 11:14:48,328 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 38
2025-08-08 11:14:48,458 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 11:14:48,459 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 11
2025-08-08 11:14:48,594 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 11:14:48,594 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-08 11:14:49,612 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:14:49,750 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.134s]
2025-08-08 11:14:49,751 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:14:49,751 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:14:49,751 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:14:49,751 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:14:49,751 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:52.812710+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 11:14:49,751 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:49,752 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:14:49,752 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:49,752 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:22.382219+00:00', 'data_returned': True}
2025-08-08 11:14:49,752 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:49,752 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:03.070454+00:00', 'data_returned': True}
2025-08-08 11:14:54,058 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:54,078 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 6 completed and processed: 1469 characters
2025-08-08 11:14:54,079 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 7/8...
2025-08-08 11:14:54,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:54,882 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:54,882 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:14:54,882 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:54,882 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'institution with most students summary'
2025-08-08 11:14:54,882 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:14:54,882 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:14:55,013 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 11:14:55,014 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 69
2025-08-08 11:14:55,149 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-08 11:14:55,149 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 38
2025-08-08 11:14:55,275 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 11:14:55,276 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 11
2025-08-08 11:14:55,406 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 11:14:55,407 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-08 11:14:55,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:14:56,021 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.141s]
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:52.812710+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:03.070454+00:00', 'data_returned': True}
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:22.382219+00:00', 'data_returned': True}
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:14:56,022 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:14:59,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:59,108 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 7 completed and processed: 863 characters
2025-08-08 11:14:59,109 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 8/8...
2025-08-08 11:14:59,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:14:59,741 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:14:59,741 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:14:59,741 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:14:59,741 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Interview summaries report'
2025-08-08 11:14:59,741 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:14:59,741 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:14:59,875 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 11:14:59,875 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 69
2025-08-08 11:15:00,008 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 11:15:00,008 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 38
2025-08-08 11:15:00,145 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-08 11:15:00,146 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 11
2025-08-08 11:15:00,272 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 11:15:00,273 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-08 11:15:00,689 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:15:00,827 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.135s]
2025-08-08 11:15:00,828 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:15:00,828 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:15:00,829 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:51.582492+00:00', 'data_returned': True}
2025-08-08 11:15:00,829 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:15:00,829 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:15:00,829 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 11:15:00,829 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:57.643107+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 11:15:00,829 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:15:00,829 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:15:00,829 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:15:00,829 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:52.812710+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 11:15:07,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:15:07,760 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 8 completed and processed: 1697 characters
2025-08-08 11:15:07,761 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:15:07,761 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-08 11:15:07,761 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:15:07,762 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-08 11:15:07,762 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 8
2025-08-08 11:15:07,762 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 4
2025-08-08 11:15:07,762 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 0
2025-08-08 11:15:07,762 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_111058.log
2025-08-08 11:15:07,765 - celery.app.trace - INFO - Task generate_streaming_report[02c9b87e-168c-45d4-b8fc-b3fc718fdb35] succeeded in 249.27222450000045s: {'outline': '# Report on Student Enrollment at the Institution with the Most Students

## I. Introduction  
   - The purpose of this report is to investigate and present findings on the institution with the highest student enrollment. The key answer to the guiding question is that the institution with the most students has a total enrollment of **192,627** students, with a retention rate of **100%**.

## II. Definition of Key Terms  
   - Institution  
   - Student Enrollment  
   - Types of Institutions (e.g., universities, colleges, online institutions)  

## III. Methodology  
   - Data Sources  
      - National Education Statistics  
      - Institutional Reports  
      - Online Databases  
   - Criteria for Selection  
      - Enrollment Numbers  
      - Time Frame of Data  

## IV. Overview of Findings  
   - Total Enrollment  
       - The institution with the most students has a total enrollment of **192,627** students.  
   - Retention Rate  
       - The retention rate at this institution is...', , ...}
2025-08-08 11:43:01,706 - celery.worker.strategy - INFO - Task generate_streaming_report[7311d94c-1f1f-44ea-997c-eb331eaf3c89] received
2025-08-08 11:43:01,709 - app.tasks.report_task - INFO - starting streaming report generation...
2025-08-08 11:43:01,710 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:43:01,710 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 7e1fced2-f70f-4dfb-a27b-5c3a8b9c5265
2025-08-08 11:43:01,710 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:43:01,710 - REPORT_REQUEST - INFO - 📝 Original Question: 'What institution has the most students?'
2025-08-08 11:43:01,711 - REPORT_REQUEST - INFO - 🆔 Task ID: 7e1fced2-f70f-4dfb-a27b-5c3a8b9c5265
2025-08-08 11:43:01,711 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T11:43:01.711120
2025-08-08 11:43:29,927 - ELASTICSEARCH_STATE - ERROR - ❌ Error checking Elasticsearch state: <class 'celery.concurrency.solo.TaskPool'> does not implement kill_job
2025-08-08 11:43:29,927 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:43:29,927 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 11:43:29,928 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:43:29,928 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: What institution has the most students?
2025-08-08 11:43:29,928 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-08 11:43:29,928 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 11:43:30,724 - openai._base_client - INFO - Retrying request to /chat/completions in 0.403479 seconds

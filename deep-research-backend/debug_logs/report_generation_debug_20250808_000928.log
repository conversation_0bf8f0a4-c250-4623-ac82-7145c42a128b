2025-08-08 00:09:28,763 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:09:28,763 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: test-task-123
2025-08-08 00:09:28,763 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:09:28,763 - REPORT_REQUEST - INFO - 📝 Original Question: 'How many students are there?'
2025-08-08 00:09:28,763 - REPORT_REQUEST - INFO - 🆔 Task ID: test-task-123
2025-08-08 00:09:28,763 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T00:09:28.763931
2025-08-08 00:09:28,913 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.150s]
2025-08-08 00:09:28,913 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-08 00:09:29,124 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.210s]
2025-08-08 00:09:29,124 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:09:29,273 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.148s]
2025-08-08 00:09:29,273 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-08 00:09:29,273 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 00:09:37,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:37,931 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 00:09:41,882 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:41,896 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 00:09:45,379 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:47,019 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:51,786 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:51,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:51,933 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:52,386 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:52,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:52,512 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:52,631 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:53,455 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:09:53,556 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 00:09:53,557 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:09:53,557 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 00:09:53,557 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:09:53,557 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 9
2025-08-08 00:09:56,334 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:02,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:02,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:03,015 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:03,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:03,397 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:03,624 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:04,116 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:04,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:04,936 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:06,475 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:08,726 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:09,777 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:10,262 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:10,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:10,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:11,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:11,081 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:11,788 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:11,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:12,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:12,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:12,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:12,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:12,730 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:13,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:13,573 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:13,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:13,764 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:14,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:16,770 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:17,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:17,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:17,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:17,478 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:18,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:18,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:18,624 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:20,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:22,064 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:23,779 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:23,780 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:25,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:25,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:25,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:25,796 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:26,133 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:27,509 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:29,101 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:31,768 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:32,999 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:33,305 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:33,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:34,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:35,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:35,265 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:36,171 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:36,172 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:36,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:37,022 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:37,182 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:37,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:38,319 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:38,494 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:38,537 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:40,780 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:40,965 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:41,698 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:41,737 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:42,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:42,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:42,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:42,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:43,234 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:43,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:44,174 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:44,241 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:44,297 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:44,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:45,180 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:45,772 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:46,306 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:47,631 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:47,835 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:47,935 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:48,150 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:51,531 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:52,143 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:52,364 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:52,555 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:52,605 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:53,437 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:54,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:55,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:55,768 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:56,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:56,802 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:57,569 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:57,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:58,823 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:58,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:10:59,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:00,642 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:00,687 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:02,106 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:02,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:03,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:03,222 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:03,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:05,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:05,436 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:07,605 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:07,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:07,984 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:11,701 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:13,034 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:14,349 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:16,719 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:17,315 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:17,810 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:19,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:20,907 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:21,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:21,131 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:24,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:24,441 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:24,898 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:25,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:26,170 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:27,777 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:28,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:29,780 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:31,360 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:32,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:33,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:33,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:34,268 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:35,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:36,788 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:37,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:38,223 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:40,168 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:40,346 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:41,887 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:42,216 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:43,189 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:44,981 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:46,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:47,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:48,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:49,011 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:49,185 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:50,716 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:52,456 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:54,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:55,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:56,331 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:56,335 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:56,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:57,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:58,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:11:59,419 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:00,920 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:03,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:04,129 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:04,910 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:05,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:07,611 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:09,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:10,171 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:11,195 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:11,603 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:12,219 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:12,880 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:14,173 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:15,207 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:16,355 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:16,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:17,442 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:18,364 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:18,586 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:19,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:20,475 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:21,024 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:24,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:25,325 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:26,042 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:27,578 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:29,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:29,669 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:32,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:32,801 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:32,998 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:33,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:33,928 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:35,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:36,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:36,604 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:39,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:39,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:40,482 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:41,096 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:41,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:43,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:43,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:44,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:45,866 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:46,216 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:47,804 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:48,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:52,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:52,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:53,076 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:53,191 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:53,231 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:54,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:55,022 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:57,175 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:57,466 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:58,064 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:59,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:12:59,784 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:00,140 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:01,371 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:02,701 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:03,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:05,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:05,844 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:05,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:07,104 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:07,411 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:10,689 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:10,690 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:11,170 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:13,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:13,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:14,169 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:15,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:17,022 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:18,590 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:19,983 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:21,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:25,660 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:26,005 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:30,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:31,050 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:34,138 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:35,264 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:36,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:37,426 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:39,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:39,931 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:42,739 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:43,148 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:44,936 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:45,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:46,426 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:47,246 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:50,420 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:53,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:13:53,302 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 00:13:53,302 - root - INFO - 'No results'
2025-08-08 00:13:53,302 - root - INFO - 'No results'
2025-08-08 00:13:53,302 - root - INFO - 'No results'
2025-08-08 00:13:53,302 - root - INFO - 'No results'
2025-08-08 00:13:53,302 - root - INFO - 'No results'
2025-08-08 00:13:53,302 - root - INFO - 'No results'
2025-08-08 00:13:53,302 - root - INFO - 'No results'
2025-08-08 00:13:53,302 - root - INFO - 'No results'
2025-08-08 00:13:53,302 - root - INFO - 'No results'
2025-08-08 00:13:53,302 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-08 00:13:53,302 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 9
2025-08-08 00:13:53,302 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 00:14:03,220 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:03,223 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 00:14:12,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:12,075 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,075 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 00:14:12,075 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,075 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/9
2025-08-08 00:14:12,076 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,076 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:14:12,076 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think have contributed to the current lack of student enrollment at the institut...
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:14:12,076 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:14:12,076 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/9
2025-08-08 00:14:12,076 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,076 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:14:12,076 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:14:12,076 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you believe contribute to the high number of inactive students, and how might these ...
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:14:12,077 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:14:12,077 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/9
2025-08-08 00:14:12,077 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,077 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:14:12,077 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contribute to the popularity of the 'Bachelor of Education (Junior High Sc...
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:14:12,077 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:14:12,077 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:14:12,077 - REPORT_PIPELINE - INFO - 🔄 Processing interview 4/9
2025-08-08 00:14:12,077 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,078 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:14:12,078 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contributed to the significant increase in enrollment from 2021 to 2022?...
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:14:12,078 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:14:12,078 - REPORT_PIPELINE - INFO - 🔄 Processing interview 5/9
2025-08-08 00:14:12,078 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,078 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:14:12,078 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:14:12,078 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does this demographic diversity impact the learning environment and cultural exchange among stud...
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:14:12,079 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:14:12,079 - REPORT_PIPELINE - INFO - 🔄 Processing interview 6/9
2025-08-08 00:14:12,079 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,079 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:14:12,079 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contribute to the decision of students to enroll part-time rather than ful...
2025-08-08 00:14:12,079 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:14:12,080 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:14:12,080 - REPORT_PIPELINE - INFO - 🔄 Processing interview 7/9
2025-08-08 00:14:12,080 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,080 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:14:12,080 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contributed to the graduation of these 6 students, and how do they compare...
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:14:12,080 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:14:12,080 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:14:12,080 - REPORT_PIPELINE - INFO - 🔄 Processing interview 8/9
2025-08-08 00:14:12,080 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,081 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:14:12,081 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the average class size of 15,532 compare to previous years, and what factors might have con...
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:14:12,081 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:14:12,081 - REPORT_PIPELINE - INFO - 🔄 Processing interview 9/9
2025-08-08 00:14:12,081 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,081 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:14:12,081 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contribute to the differences in enrollment numbers between online and on-...
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:14:12,081 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:14:12,081 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:14:12,082 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 9
2025-08-08 00:14:12,082 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:12,082 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 00:14:12,082 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 9 documents
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think have contributed to the current lack of student enrollment at th...
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:53.301299+00:00', 'data_returned': False}
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Content: Question: What factors do you believe contribute to the high number of inactive students, and how mi...
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:11:03.238356+00:00', 'data_returned': False}
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contribute to the popularity of the 'Bachelor of Education (Juni...
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:10:46.321158+00:00', 'data_returned': False}
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contributed to the significant increase in enrollment from 2021 ...
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:00.152982+00:00', 'data_returned': False}
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Content: Question: How does this demographic diversity impact the learning environment and cultural exchange ...
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:10:45.796341+00:00', 'data_returned': False}
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contribute to the decision of students to enroll part-time rathe...
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:10:42.569212+00:00', 'data_returned': False}
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contributed to the graduation of these 6 students, and how do th...
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:13.370792+00:00', 'data_returned': False}
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -   📄 Doc 8:
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Content: Question: How does the average class size of 15,532 compare to previous years, and what factors migh...
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:46.440488+00:00', 'data_returned': False}
2025-08-08 00:14:12,082 - UPSERT_DOCS - INFO -   📄 Doc 9:
2025-08-08 00:14:12,083 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contribute to the differences in enrollment numbers between onli...
2025-08-08 00:14:12,083 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:10:44.331800+00:00', 'data_returned': False}
2025-08-08 00:14:12,083 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 0/9
2025-08-08 00:14:12,309 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.226s]
2025-08-08 00:14:12,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:14:15,609 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:2.185s]
2025-08-08 00:14:15,610 - UPSERT_DOCS - INFO - ✅ Successfully upserted 9 documents to Elasticsearch
2025-08-08 00:14:15,897 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.287s]
2025-08-08 00:14:15,898 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 9
2025-08-08 00:14:16,073 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.174s]
2025-08-08 00:14:16,073 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:14:16,217 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.143s]
2025-08-08 00:14:16,217 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (1):
2025-08-08 00:14:16,217 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (9 docs)
2025-08-08 00:14:16,218 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 9
2025-08-08 00:14:16,218 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 00:14:16,219 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:16,219 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-08 00:14:16,219 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:16,219 - REPORT_PIPELINE - INFO - ✍️ Writing 9 sections using batch processing...
2025-08-08 00:14:16,925 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:16,931 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:16,931 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:14:16,931 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:16,931 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'References and resources'
2025-08-08 00:14:16,931 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:14:16,931 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:14:16,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:16,961 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:16,961 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:14:16,961 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:16,961 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'factors influencing student enrollment'
2025-08-08 00:14:16,961 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:14:16,961 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:14:16,991 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:16,995 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:16,995 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:14:16,995 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:16,995 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'current student population demographics trends'
2025-08-08 00:14:16,995 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:14:16,995 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:14:17,002 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:17,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:17,052 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:17,052 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:14:17,052 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:17,052 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student population assessment conclusion insights recommendations'
2025-08-08 00:14:17,052 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:14:17,053 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:14:17,060 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:17,060 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:14:17,060 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:17,060 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Student population data implications'
2025-08-08 00:14:17,060 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:14:17,060 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:14:17,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:17,063 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:17,064 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:14:17,064 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:17,064 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology Data Collection Interviews Limitations'
2025-08-08 00:14:17,064 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:14:17,064 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:14:17,072 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.140s]
2025-08-08 00:14:17,072 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 9
2025-08-08 00:14:17,203 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 00:14:17,203 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:14:17,238 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.277s]
2025-08-08 00:14:17,239 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 9
2025-08-08 00:14:17,244 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:17,246 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:17,250 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:17,251 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:14:17,251 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:17,251 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Appendices Interview Transcripts Data Tables Figures'
2025-08-08 00:14:17,251 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:14:17,251 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:14:17,261 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.265s]
2025-08-08 00:14:17,261 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 9
2025-08-08 00:14:17,312 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:17,313 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:14:17,313 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:17,313 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Discussion of findings and trends in student enrollment'
2025-08-08 00:14:17,313 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:14:17,313 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:14:17,380 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.327s]
2025-08-08 00:14:17,380 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 9
2025-08-08 00:14:17,384 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.320s]
2025-08-08 00:14:17,384 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 9
2025-08-08 00:14:17,387 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.327s]
2025-08-08 00:14:17,387 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 9
2025-08-08 00:14:17,388 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 00:14:17,388 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.149s]
2025-08-08 00:14:17,388 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:14:17,389 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:14:17,393 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.190s]
2025-08-08 00:14:17,394 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-08 00:14:17,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:17,479 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:17,479 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:14:17,479 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:17,479 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'current student population demographics'
2025-08-08 00:14:17,479 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:14:17,479 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:14:17,587 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.202s]
2025-08-08 00:14:17,587 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.198s]
2025-08-08 00:14:17,588 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.207s]
2025-08-08 00:14:17,588 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.199s]
2025-08-08 00:14:17,588 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:14:17,588 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-08 00:14:17,588 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:14:17,589 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-08 00:14:17,590 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.339s]
2025-08-08 00:14:17,590 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 9
2025-08-08 00:14:17,610 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.222s]
2025-08-08 00:14:17,610 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:14:17,638 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.244s]
2025-08-08 00:14:17,638 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:14:17,677 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.364s]
2025-08-08 00:14:17,678 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 9
2025-08-08 00:14:17,763 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.174s]
2025-08-08 00:14:17,764 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.173s]
2025-08-08 00:14:17,764 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.174s]
2025-08-08 00:14:17,764 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.175s]
2025-08-08 00:14:17,764 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-08 00:14:17,765 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.155s]
2025-08-08 00:14:17,765 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.285s]
2025-08-08 00:14:17,765 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:14:17,765 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:14:17,766 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-08 00:14:17,766 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-08 00:14:17,766 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 9
2025-08-08 00:14:17,770 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.181s]
2025-08-08 00:14:17,771 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:14:17,915 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.237s]
2025-08-08 00:14:17,915 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:14:17,923 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.156s]
2025-08-08 00:14:17,923 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.156s]
2025-08-08 00:14:17,924 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.156s]
2025-08-08 00:14:17,924 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.157s]
2025-08-08 00:14:17,924 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-08 00:14:17,924 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.159s]
2025-08-08 00:14:17,925 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:14:17,925 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:14:17,925 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:14:17,925 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:14:18,203 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.278s]
2025-08-08 00:14:18,204 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.278s]
2025-08-08 00:14:18,204 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.288s]
2025-08-08 00:14:18,205 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:14:18,205 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-08 00:14:18,205 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-08 00:14:18,332 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:14:18,359 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:14:18,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:14:18,583 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.247s]
2025-08-08 00:14:18,583 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:14:18,583 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:14:18,591 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.385s]
2025-08-08 00:14:18,591 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.384s]
2025-08-08 00:14:18,591 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:14:18,592 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:14:18,702 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:14:18,897 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.191s]
2025-08-08 00:14:18,898 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:14:18,898 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:14:18,899 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.537s]
2025-08-08 00:14:18,900 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:14:18,900 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:14:18,976 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:14:19,033 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.494s]
2025-08-08 00:14:19,034 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:14:19,034 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:14:19,133 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:14:19,209 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:14:19,264 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.283s]
2025-08-08 00:14:19,264 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:14:19,264 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:14:19,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:14:19,383 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.247s]
2025-08-08 00:14:19,384 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:14:19,384 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:14:19,400 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.187s]
2025-08-08 00:14:19,400 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:14:19,400 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:14:19,809 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:14:19,871 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.515s]
2025-08-08 00:14:19,872 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:14:19,872 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:14:20,206 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.394s]
2025-08-08 00:14:20,207 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:14:20,207 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:14:21,753 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:22,933 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:23,015 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:23,846 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:25,295 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:25,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:25,514 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:26,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:29,899 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:29,917 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 306 characters
2025-08-08 00:14:29,917 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1222 characters
2025-08-08 00:14:29,918 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1394 characters
2025-08-08 00:14:29,918 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 1440 characters
2025-08-08 00:14:29,918 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1536 characters
2025-08-08 00:14:29,918 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 1675 characters
2025-08-08 00:14:29,918 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 1031 characters
2025-08-08 00:14:29,919 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 460 characters
2025-08-08 00:14:29,919 - REPORT_PIPELINE - INFO -   ✅ Section 9 completed: 539 characters
2025-08-08 00:14:34,512 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:14:34,521 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:14:34,521 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:14:34,521 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:14:34,521 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:14:34,521 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:14:34,521 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:14:34,521 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:14:34,521 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:14:34,521 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:14:34,521 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:14:34,521 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:14:34,521 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-08 00:14:34,521 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:14:34,521 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-08 00:14:34,521 - REPORT_PIPELINE - INFO - 📊 Final report sections: 10
2025-08-08 00:14:34,521 - REPORT_PIPELINE - INFO - 📈 Extended data items: 0
2025-08-08 00:14:34,521 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_000928.log

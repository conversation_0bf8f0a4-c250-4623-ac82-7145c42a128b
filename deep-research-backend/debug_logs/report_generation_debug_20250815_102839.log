2025-08-15 10:28:39,356 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250815_102839.log
2025-08-15 10:28:39,356 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:28:39,356 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 2205b218-7a19-4c6e-8bc9-e0d3cc4fad09
2025-08-15 10:28:39,356 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:28:39,356 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are boys performing at ITC University?'
2025-08-15 10:28:39,356 - REPORT_REQUEST - INFO - 🆔 Task ID: 2205b218-7a19-4c6e-8bc9-e0d3cc4fad09
2025-08-15 10:28:39,356 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-15T10:28:39.356823
2025-08-15 10:28:39,477 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.120s]
2025-08-15 10:28:39,477 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 137
2025-08-15 10:28:39,601 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.124s]
2025-08-15 10:28:39,602 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 73
2025-08-15 10:28:39,722 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.120s]
2025-08-15 10:28:39,722 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-15 10:28:39,723 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (24 docs)
2025-08-15 10:28:39,723 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (21 docs)
2025-08-15 10:28:39,723 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (19 docs)
2025-08-15 10:28:39,723 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-15 10:28:39,723 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-15 10:28:39,723 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-15 10:28:39,723 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-15 10:28:39,723 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-15 10:28:39,723 - ELASTICSEARCH_STATE - INFO -   - 'Which instituion has the most students?' (6 docs)
2025-08-15 10:28:39,723 - ELASTICSEARCH_STATE - INFO -   - 'How are students performing at ITC University?' (4 docs)
2025-08-15 10:28:39,724 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:28:39,724 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-15 10:28:39,724 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:28:39,724 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How are boys performing at ITC University?
2025-08-15 10:28:39,724 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-15 10:28:39,724 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-15 10:28:52,669 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:28:52,689 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-15 10:28:57,880 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:28:57,896 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-15 10:29:00,066 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:00,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:00,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:01,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:01,344 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:01,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:01,524 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:01,759 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:02,104 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:02,193 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:02,260 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-15 10:29:04,785 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:05,135 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:05,246 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:05,435 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:07,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:07,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:08,025 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:08,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:08,679 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:09,004 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:09,468 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:09,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:17,234 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:18,714 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:18,867 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:19,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:23,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:23,641 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the performance of boys at ITC University compare to that of girls?', 'sql': "SELECT s.sex, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS number_of_students\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nWHERE s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY s.sex;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average final scores and the number of students for each sex (boys and girls) at ITC University. It joins the 'assessment_results' table with the 'students' table based on the student ID, filters the results to only include students from ITC University, and groups the results by sex. This directly addresses the question of comparing the performance of boys and girls.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be enhanced by explicitly labeling the output columns for clarity, such as renaming 'sex' to 'gender' or adding a more descriptive alias for 'average_score'."}
2025-08-15 10:29:23,641 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:23,641 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:29:25,176 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:25,774 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:26,561 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:27,585 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:27,669 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:27,675 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the academic programs or courses where boys are excelling or struggling at ITC University?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, ar.grade, COUNT(ar.id) AS number_of_students\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN courses c ON ar.course_id = c.id\nJOIN programs p ON ar.student_program_id = p.id\nWHERE s.sex = 'M'\nGROUP BY p.long_name, c.title, ar.grade\nORDER BY p.long_name, c.title, ar.grade\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly identifies the academic programs and courses where male students ('boys') are either excelling or struggling by filtering the results based on the students' sex. It joins the necessary tables (assessment_results, students, courses, and programs) to gather relevant information about the programs and courses associated with male students' assessment results. The use of GROUP BY allows for aggregation of results by program name, course title, and grade, which aligns with the question's focus on performance. The LIMIT clause is used to restrict the number of results returned, which is a common practice in SQL queries to manage output size.", 'feedback': "The question could be clarified by specifying what metrics define 'excelling' or 'struggling' (e.g., specific grades or thresholds). Additionally, the SQL could be improved by including a condition to filter grades that indicate excellence or struggle, rather than just grouping by grade, to provide more targeted insights."}
2025-08-15 10:29:27,676 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:27,676 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:29:27,762 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:29,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:29,458 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any notable differences in performance based on entry modes or admission criteria for boys at ITC University?', 'sql': "SELECT em.entry_mode, af.description, AVG(ar.finalscore) AS average_score, COUNT(a.id) AS total_applicants\nFROM applicants a\nJOIN entry_modes em ON a.entry_mode_id = em.id\nJOIN admission_forms af ON a.form_id = af.id\nJOIN student_programs sp ON a.student_id = sp.student_id\nJOIN assessment_results ar ON sp.id = ar.student_program_id\nWHERE a.sex = 'M' AND a.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY em.entry_mode, af.description\nORDER BY average_score DESC\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly identifies the performance differences based on entry modes and admission criteria for male applicants at ITC University. It selects the entry mode and admission form description, calculates the average score from assessment results, and counts the total number of applicants. The filtering for male applicants and the specific institution is also correctly implemented. The grouping by entry mode and admission form description aligns with the question's focus on differences in performance.", 'feedback': "The SQL query is well-structured and addresses the question effectively. However, it could be improved by explicitly defining what constitutes 'notable differences' in performance. For instance, adding a threshold for the average score difference or including statistical significance tests could provide more insight into the differences observed."}
2025-08-15 10:29:29,459 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:29,459 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:29:29,971 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:30,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:30,898 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:31,090 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:31,189 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:31,513 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:32,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:32,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:32,952 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the performance gap between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance gap between boys and girls at ITC University. However, the provided schema does not contain specific data related to gender performance comparisons or any analytical metrics that would allow for such an analysis. While there are tables related to students, their grades, and other attributes, there is no direct link or data that would enable a comprehensive answer to this question regarding gender performance gaps.', 'feedback': ''}
2025-08-15 10:29:32,952 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:32,952 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:32,952 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the performance gap between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance gap between boys and girls at ITC University. However, the provided schema does not contain specific data related to gender performance comparisons or any analytical metrics that would allow for such an analysis. While there are tables related to students, their grades, and other attributes, there is no direct link or data that would enable a comprehensive answer to this question regarding gender performance gaps.', 'feedback': ''}
2025-08-15 10:29:33,126 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:33,958 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:34,319 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:35,079 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:35,462 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:35,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:36,867 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:36,891 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the lack of data. The schema includes various tables related to students, academic performance, and institutions, but it does not provide insights into data availability or the reasons for data absence. Therefore, the question cannot be answered based on the schema.", 'feedback': ''}
2025-08-15 10:29:36,891 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:36,892 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:36,892 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the lack of data. The schema includes various tables related to students, academic performance, and institutions, but it does not provide insights into data availability or the reasons for data absence. Therefore, the question cannot be answered based on the schema.", 'feedback': ''}
2025-08-15 10:29:37,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:37,860 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the performance gap between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance gap between boys and girls at ITC University. However, the provided schema does not contain any specific data or tables that directly relate to gender performance analysis or comparative studies between boys and girls. While there are tables related to students, their grades, and other academic records, there is no indication of gender-specific performance metrics or factors influencing performance gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 10:29:37,860 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:37,860 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:37,860 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the performance gap between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance gap between boys and girls at ITC University. However, the provided schema does not contain any specific data or tables that directly relate to gender performance analysis or comparative studies between boys and girls. While there are tables related to students, their grades, and other academic records, there is no indication of gender-specific performance metrics or factors influencing performance gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 10:29:37,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:37,994 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors, if any, do you believe could influence performance among boys at ITC University, despite the lack of differences based on entry modes or admission criteria?', 'answerable': False, 'reasoning': 'The question asks about factors influencing performance among boys at ITC University, specifically in relation to entry modes or admission criteria. However, the provided schema does not contain any direct information about performance metrics, factors influencing performance, or specific demographic analyses related to boys at ITC University. While there are tables related to admissions, programs, and students, none of them provide insights into performance factors or comparative analyses based on gender or entry modes. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 10:29:37,995 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:37,995 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:37,995 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors, if any, do you believe could influence performance among boys at ITC University, despite the lack of differences based on entry modes or admission criteria?', 'answerable': False, 'reasoning': 'The question asks about factors influencing performance among boys at ITC University, specifically in relation to entry modes or admission criteria. However, the provided schema does not contain any direct information about performance metrics, factors influencing performance, or specific demographic analyses related to boys at ITC University. While there are tables related to admissions, programs, and students, none of them provide insights into performance factors or comparative analyses based on gender or entry modes. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 10:29:39,431 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:40,488 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:40,489 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:41,770 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:41,786 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the lack of data. The schema includes various tables related to students, academic performance, and institutions, but it does not provide insights into data availability or the reasons for data absence. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-15 10:29:41,786 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:41,786 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:41,786 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the lack of data. The schema includes various tables related to students, academic performance, and institutions, but it does not provide insights into data availability or the reasons for data absence. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-15 10:29:42,725 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:42,741 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the performance gap between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance gap between boys and girls at ITC University. However, the provided schema does not contain any specific data or tables that directly relate to gender performance analysis or comparative studies between genders. While there are tables related to students, their grades, and other academic records, there is no indication of gender-specific performance metrics or factors influencing performance gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 10:29:42,741 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:42,741 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:42,741 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the performance gap between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance gap between boys and girls at ITC University. However, the provided schema does not contain any specific data or tables that directly relate to gender performance analysis or comparative studies between genders. While there are tables related to students, their grades, and other academic records, there is no indication of gender-specific performance metrics or factors influencing performance gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 10:29:42,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:42,760 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors, if any, do you believe could influence performance among boys at ITC University, despite the lack of differences based on entry modes or admission criteria?', 'answerable': False, 'reasoning': 'The question asks about factors influencing performance among boys at ITC University, specifically in relation to entry modes or admission criteria. However, the provided schema does not contain any specific data or tables that directly relate to performance metrics or factors influencing performance. While there are tables related to admissions, students, and programs, there is no clear indication of performance-related data such as grades, assessments, or evaluations that could be analyzed to answer the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-15 10:29:42,760 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:42,760 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:42,760 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors, if any, do you believe could influence performance among boys at ITC University, despite the lack of differences based on entry modes or admission criteria?', 'answerable': False, 'reasoning': 'The question asks about factors influencing performance among boys at ITC University, specifically in relation to entry modes or admission criteria. However, the provided schema does not contain any specific data or tables that directly relate to performance metrics or factors influencing performance. While there are tables related to admissions, students, and programs, there is no clear indication of performance-related data such as grades, assessments, or evaluations that could be analyzed to answer the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-15 10:29:44,117 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:44,781 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:44,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:45,022 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:46,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:46,552 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the lack of data. The schema includes various tables related to students, academic performance, and institutions, but it does not provide insights into the reasons for data absence or the specific context of boys' academic performance at a particular institution. Therefore, the question cannot be answered based on the available schema.", 'feedback': ''}
2025-08-15 10:29:46,552 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:46,553 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:46,553 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the lack of data. The schema includes various tables related to students, academic performance, and institutions, but it does not provide insights into the reasons for data absence or the specific context of boys' academic performance at a particular institution. Therefore, the question cannot be answered based on the available schema.", 'feedback': ''}
2025-08-15 10:29:47,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:47,419 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the performance gap between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance gap between boys and girls at ITC University. However, the provided schema does not contain any specific data or tables that directly relate to gender performance analysis or comparative studies between genders. While there are tables related to students, their grades, and other academic records, there is no indication of gender-specific performance metrics or factors influencing performance gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 10:29:47,419 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:47,419 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:47,419 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the performance gap between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance gap between boys and girls at ITC University. However, the provided schema does not contain any specific data or tables that directly relate to gender performance analysis or comparative studies between genders. While there are tables related to students, their grades, and other academic records, there is no indication of gender-specific performance metrics or factors influencing performance gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 10:29:47,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:47,493 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors, if any, do you believe could influence performance among boys at ITC University, despite the lack of differences based on entry modes or admission criteria?', 'answerable': False, 'reasoning': 'The question asks about factors influencing performance among boys at ITC University, specifically in relation to entry modes or admission criteria. However, the provided schema does not contain any specific data or tables that directly relate to performance metrics or factors influencing performance. While there are tables related to admissions, students, and programs, there is no clear indication of performance-related data such as grades, assessments, or evaluations that could be analyzed to answer the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-15 10:29:47,493 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:47,494 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:47,494 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors, if any, do you believe could influence performance among boys at ITC University, despite the lack of differences based on entry modes or admission criteria?', 'answerable': False, 'reasoning': 'The question asks about factors influencing performance among boys at ITC University, specifically in relation to entry modes or admission criteria. However, the provided schema does not contain any specific data or tables that directly relate to performance metrics or factors influencing performance. While there are tables related to admissions, students, and programs, there is no clear indication of performance-related data such as grades, assessments, or evaluations that could be analyzed to answer the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-15 10:29:48,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:50,451 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:51,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:51,600 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons for the lack of data or factors influencing academic performance. The schema includes various tables related to students, programs, assessments, and institutions, but it does not provide insights into data availability or the reasons behind data gaps. Therefore, this question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-15 10:29:51,601 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:51,601 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:51,601 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons for the lack of data or factors influencing academic performance. The schema includes various tables related to students, programs, assessments, and institutions, but it does not provide insights into data availability or the reasons behind data gaps. Therefore, this question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-15 10:29:52,419 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:53,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:53,069 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors, if any, do you believe could influence performance among boys at ITC University, despite the lack of differences based on entry modes or admission criteria?', 'answerable': False, 'reasoning': 'The question asks about factors influencing performance among boys at ITC University, specifically in relation to entry modes or admission criteria. However, the provided schema does not contain any direct information about performance metrics, factors influencing performance, or specific demographic analyses related to boys at ITC University. While there are tables related to admissions, programs, and students, none of them provide insights into performance factors or comparative analyses based on gender or entry modes. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 10:29:53,069 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:29:53,069 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:29:53,069 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors, if any, do you believe could influence performance among boys at ITC University, despite the lack of differences based on entry modes or admission criteria?', 'answerable': False, 'reasoning': 'The question asks about factors influencing performance among boys at ITC University, specifically in relation to entry modes or admission criteria. However, the provided schema does not contain any direct information about performance metrics, factors influencing performance, or specific demographic analyses related to boys at ITC University. While there are tables related to admissions, programs, and students, none of them provide insights into performance factors or comparative analyses based on gender or entry modes. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 10:29:55,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:58,823 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:29:59,799 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:30:07,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:30:17,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:30:29,195 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:30:42,382 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:30:43,792 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:30:52,302 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:31:00,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:31:00,647 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "Are there any particular trends in boys' performance over the past few academic years at ITC University?", 'sql': "SELECT academic_years.start_year, AVG(assessment_results.finalscore) AS average_score, COUNT(assessment_results.id) AS number_of_assessments\nFROM assessment_results\nJOIN students ON assessment_results.student_id = students.id\nJOIN academic_years ON assessment_results.semester_id = academic_years.id\nWHERE students.sex = 'M' AND students.status = 'active' AND academic_years.institution_id = 'ITC University'\nGROUP BY academic_years.start_year\nORDER BY academic_years.start_year;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the average performance of male students (boys) by calculating the average of their final scores over different academic years at ITC University. It filters for active male students and groups the results by the start year of the academic years, which allows for trend analysis over time. The inclusion of the count of assessments provides additional context to the average score, indicating how many assessments contributed to the average, which is relevant for understanding trends.', 'feedback': "The question could be clarified by specifying what kind of trends are of interest (e.g., increasing or decreasing scores, comparison with girls' performance, etc.). Additionally, the SQL could be improved by including a specific range of academic years if that information is available, to focus the analysis on recent trends."}
2025-08-15 10:31:00,647 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:31:00,647 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:31:03,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:31:04,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:31:06,621 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:31:19,874 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:31:32,704 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:31:32,718 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What steps could be taken to improve data collection on boys' academic performance at ITC University, and how might this data be utilized to enhance their educational outcomes?", 'answerable': False, 'reasoning': 'The question is focused on improving data collection and utilizing data to enhance educational outcomes, which involves strategic planning, educational methodologies, and possibly qualitative insights. The provided schema contains tables related to institutions, students, courses, and academic records, but it does not include any specific mechanisms or strategies for data collection or analysis. Additionally, the question requires a broader context and understanding of educational practices that cannot be derived solely from the existing database schema.', 'feedback': ''}
2025-08-15 10:31:32,718 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:31:32,719 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:31:32,719 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What steps could be taken to improve data collection on boys' academic performance at ITC University, and how might this data be utilized to enhance their educational outcomes?", 'answerable': False, 'reasoning': 'The question is focused on improving data collection and utilizing data to enhance educational outcomes, which involves strategic planning, educational methodologies, and possibly qualitative insights. The provided schema contains tables related to institutions, students, courses, and academic records, but it does not include any specific mechanisms or strategies for data collection or analysis. Additionally, the question requires a broader context and understanding of educational practices that cannot be derived solely from the existing database schema.', 'feedback': ''}
2025-08-15 10:31:46,546 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:00,160 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:00,174 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What steps could be taken to improve data collection on boys' academic performance at ITC University, and how might this data be utilized to enhance their educational outcomes?", 'answerable': False, 'reasoning': 'The question is focused on improving data collection and utilizing data to enhance educational outcomes, which involves strategic planning, educational methodologies, and possibly qualitative insights. The provided schema contains tables related to institutions, students, courses, and academic records, but it does not include any specific mechanisms or strategies for data collection or analysis. Additionally, the question requires a broader context and understanding of educational practices that cannot be derived solely from the existing database schema.', 'feedback': ''}
2025-08-15 10:32:00,175 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:32:00,175 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:32:00,175 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What steps could be taken to improve data collection on boys' academic performance at ITC University, and how might this data be utilized to enhance their educational outcomes?", 'answerable': False, 'reasoning': 'The question is focused on improving data collection and utilizing data to enhance educational outcomes, which involves strategic planning, educational methodologies, and possibly qualitative insights. The provided schema contains tables related to institutions, students, courses, and academic records, but it does not include any specific mechanisms or strategies for data collection or analysis. Additionally, the question requires a broader context and understanding of educational practices that cannot be derived solely from the existing database schema.', 'feedback': ''}
2025-08-15 10:32:09,511 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:16,260 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:16,280 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What steps could be taken to improve data collection on boys' academic performance at ITC University, and how might this data be utilized to enhance their educational outcomes?", 'answerable': False, 'reasoning': 'The question is focused on improving data collection and utilizing data to enhance educational outcomes, which involves strategic planning, educational methodologies, and possibly qualitative insights. The provided schema contains tables related to institutions, students, courses, and academic records, but it does not include any specific mechanisms or strategies for data collection or analysis. Additionally, the question requires a broader context and understanding of educational practices that cannot be derived solely from the existing database schema.', 'feedback': ''}
2025-08-15 10:32:16,280 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:32:16,280 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:32:16,280 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What steps could be taken to improve data collection on boys' academic performance at ITC University, and how might this data be utilized to enhance their educational outcomes?", 'answerable': False, 'reasoning': 'The question is focused on improving data collection and utilizing data to enhance educational outcomes, which involves strategic planning, educational methodologies, and possibly qualitative insights. The provided schema contains tables related to institutions, students, courses, and academic records, but it does not include any specific mechanisms or strategies for data collection or analysis. Additionally, the question requires a broader context and understanding of educational practices that cannot be derived solely from the existing database schema.', 'feedback': ''}
2025-08-15 10:32:20,595 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:23,067 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:23,080 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What steps could be taken to improve data collection on boys' academic performance at ITC University, and how might this data be utilized to enhance their educational outcomes?", 'answerable': False, 'reasoning': 'The question is focused on improving data collection and utilizing data to enhance educational outcomes, which involves strategic planning, educational methodologies, and possibly qualitative insights that are not directly represented in the provided database schema. The schema contains tables related to institutions, students, courses, and assessments, but it does not provide specific mechanisms or strategies for data collection or analysis. Therefore, while some data may be available to analyze academic performance, the question itself cannot be answered completely from the schema.', 'feedback': ''}
2025-08-15 10:32:23,080 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:32:23,081 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:32:23,081 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What steps could be taken to improve data collection on boys' academic performance at ITC University, and how might this data be utilized to enhance their educational outcomes?", 'answerable': False, 'reasoning': 'The question is focused on improving data collection and utilizing data to enhance educational outcomes, which involves strategic planning, educational methodologies, and possibly qualitative insights that are not directly represented in the provided database schema. The schema contains tables related to institutions, students, courses, and assessments, but it does not provide specific mechanisms or strategies for data collection or analysis. Therefore, while some data may be available to analyze academic performance, the question itself cannot be answered completely from the schema.', 'feedback': ''}
2025-08-15 10:32:23,081 - root - INFO - [{'sex': 'F', 'average_score': 68.72, 'number_of_students': 58}, {'sex': 'M', 'average_score': 54.86, 'number_of_students': 71}]
2025-08-15 10:32:23,081 - root - INFO - 'No results'
2025-08-15 10:32:23,081 - root - INFO - 'No results'
2025-08-15 10:32:23,081 - root - INFO - 'No results'
2025-08-15 10:32:23,081 - app.chains.composite.report_pipeline - INFO - generating_informed_outline
2025-08-15 10:32:28,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:28,648 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-15 10:32:33,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:33,664 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:32:33,664 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 10:32:33,665 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 10:32:33,665 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the performance of boys at ITC University compare to that of girls?...
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, the performance of girls is significantly better than that of boys. Girls have an...
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: bar_chart
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': 'F', 'average_score': 68.72, 'number_of_students': 58}, {'sex': 'M', 'average_score': 54.86...
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_scores_by_gender
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-15 10:32:33,665 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-15 10:32:33,665 - celery.redirected - WARNING - [{'sex': 'F', 'average_score': 68.72, 'number_of_students': 58}, {'sex': 'M', 'average_score': 54.86, 'number_of_students': 71}]
2025-08-15 10:32:33,665 - celery.redirected - WARNING - ================================= 
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: average_scores_by_gender
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-15 10:32:33,665 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:32:33,665 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 10:32:33,665 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 10:32:33,665 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What are the academic programs or courses where boys are excelling or struggling at ITC University?...
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there is no available data regarding the academic programs or courses where boys are...
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: boys_academic_performance_at_itc
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 10:32:33,665 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-15 10:32:33,666 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:32:33,666 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 10:32:33,666 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 10:32:33,666 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any particular trends in boys' performance over the past few academic years at ITC Univers...
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, indicating that there may not be sufficient data available...
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: boys_performance_trends_itc_university
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-15 10:32:33,666 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:32:33,666 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 10:32:33,666 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 10:32:33,666 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any notable differences in performance based on entry modes or admission criteria for boys...
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there are no notable differences in performance b...
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: boys_performance_entry_modes_admission_criteria
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 10:32:33,666 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-15 10:32:33,667 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 4
2025-08-15 10:32:33,667 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:32:33,667 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-15 10:32:33,667 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 4 documents
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -     Content: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:47.419701+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -     Content: Question: What are the academic programs or courses where boys are excelling or struggling at ITC Un...
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:51.601397+00:00', 'data_returned': False}
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -     Content: Question: Are there any particular trends in boys' performance over the past few academic years at I...
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:32:23.081267+00:00', 'data_returned': False}
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -     Content: Question: Are there any notable differences in performance based on entry modes or admission criteri...
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:53.069872+00:00', 'data_returned': False}
2025-08-15 10:32:33,667 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 1/4
2025-08-15 10:32:33,874 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.206s]
2025-08-15 10:32:34,459 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 10:32:35,385 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.517s]
2025-08-15 10:32:35,386 - UPSERT_DOCS - INFO - ✅ Successfully upserted 4 documents to Elasticsearch
2025-08-15 10:32:35,386 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 4
2025-08-15 10:32:35,387 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-15 10:32:35,387 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:32:35,387 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-15 10:32:35,387 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:32:35,387 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/4...
2025-08-15 10:32:36,588 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:36,602 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:32:36,602 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-15 10:32:36,602 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:32:36,602 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'academic performance boys ITC University gender gap'
2025-08-15 10:32:36,603 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-15 10:32:36,603 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-15 10:32:36,731 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-15 10:32:36,731 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 141
2025-08-15 10:32:36,850 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.118s]
2025-08-15 10:32:36,850 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 74
2025-08-15 10:32:36,970 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.119s]
2025-08-15 10:32:36,970 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 25
2025-08-15 10:32:37,089 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.119s]
2025-08-15 10:32:37,090 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 12
2025-08-15 10:32:37,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 10:32:37,935 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.129s]
2025-08-15 10:32:37,936 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-15 10:32:37,936 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:37,936 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:47.419701+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:37,936 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:37,936 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:37,937 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:37,937 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-15 10:32:37,937 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:37,937 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:32:37,937 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:37,937 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:32:37,938 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:47.419701+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_gender
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_gender
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_gender
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   📄 Doc 5: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:32:37,939 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-15 10:32:37,940 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (2226 chars):
2025-08-15 10:32:37,940 - app.chains.section_writer - INFO -    Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At ITC University, the performance of girls is significantly better than that of boys. Girls have an average score of 68.72, while boys have an average score of 54.86. Additionally, there are 58 girls and 71 boys in the sample, indicating that the performance gap is based on a larger number of boys.
Data Tag: average_scores_by_gender

Question: How does the performance of boys at ITC University compare...
2025-08-15 10:32:41,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:41,601 - app.chains.section_writer - INFO - 🤖 AI generated section (717 chars):
2025-08-15 10:32:41,602 - app.chains.section_writer - INFO -    This report analyzes the academic performance of boys at ITC University, highlighting the significant performance gap between genders and the implications for educational strategies. At ITC University, the performance of girls is significantly better than that of boys. Girls have an average score of...
2025-08-15 10:32:41,602 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_scores_by_gender']
2025-08-15 10:32:41,602 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 727 characters
2025-08-15 10:32:41,603 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/4...
2025-08-15 10:32:42,340 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:42,356 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:32:42,357 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-15 10:32:42,357 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:32:42,357 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'performance comparison boys girls scores'
2025-08-15 10:32:42,357 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-15 10:32:42,357 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-15 10:32:42,479 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.123s]
2025-08-15 10:32:42,480 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 141
2025-08-15 10:32:42,598 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.118s]
2025-08-15 10:32:42,599 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 74
2025-08-15 10:32:42,719 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.120s]
2025-08-15 10:32:42,720 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 25
2025-08-15 10:32:42,839 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.119s]
2025-08-15 10:32:42,839 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 12
2025-08-15 10:32:43,331 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 10:32:43,466 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.128s]
2025-08-15 10:32:43,467 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-15 10:32:43,468 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:43,468 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:43,468 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:43,468 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:47.419701+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:43,469 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:43,469 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:32:43,469 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:43,469 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:32:43,469 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:43,469 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_gender
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:47.419701+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_gender
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   📄 Doc 5: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:32:43,470 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-15 10:32:43,471 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_gender
2025-08-15 10:32:43,471 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (2226 chars):
2025-08-15 10:32:43,471 - app.chains.section_writer - INFO -    Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At ITC University, the performance of girls is significantly better than that of boys. Girls have an average score of 69.0, while boys have an average score of 54.86. Additionally, there are 65 girls and 71 boys in the sample, indicating that while there are slightly more boys, the girls outperform them on average.
Data Tag: average_scores_by_gender

Question: How does the performance of boys at ITC Un...
2025-08-15 10:32:47,068 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:47,073 - app.chains.section_writer - INFO - 🤖 AI generated section (864 chars):
2025-08-15 10:32:47,073 - app.chains.section_writer - INFO -    ## 2. Performance Comparison  

### 2.1 Average Scores  
At ITC University, girls achieved an average score of 68.72, while boys had a lower average score of 54.86. This indicates a notable difference in academic performance between the two groups.

### 2.2 Sample Size  
The sample size for this ana...
2025-08-15 10:32:47,073 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_scores_by_gender']
2025-08-15 10:32:47,073 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 850 characters
2025-08-15 10:32:47,074 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/4...
2025-08-15 10:32:47,886 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:47,889 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:32:47,890 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-15 10:32:47,890 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:32:47,890 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'factors influencing performance'
2025-08-15 10:32:47,890 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-15 10:32:47,890 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-15 10:32:48,018 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-15 10:32:48,018 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 141
2025-08-15 10:32:48,139 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.121s]
2025-08-15 10:32:48,140 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 74
2025-08-15 10:32:48,260 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.120s]
2025-08-15 10:32:48,261 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 25
2025-08-15 10:32:48,381 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.120s]
2025-08-15 10:32:48,382 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 12
2025-08-15 10:32:49,962 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 10:32:50,092 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.126s]
2025-08-15 10:32:50,092 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-15 10:32:50,093 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:32:50,093 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:32:50,093 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-15 10:32:50,093 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-15 10:32:50,093 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-15 10:32:50,093 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-15 10:32:50,094 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:50,094 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:47.419701+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:50,094 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:32:50,094 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:32:50,095 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:32:50,095 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:32:50,095 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-15 10:32:50,095 - app.chains.section_writer - INFO -   📄 Doc 2: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-15 10:32:50,095 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-15 10:32:50,095 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_performance_in_courses
2025-08-15 10:32:50,095 - app.chains.section_writer - INFO -   📄 Doc 3: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-15 10:32:50,095 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-15 10:32:50,096 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_gpa_by_entry_mode
2025-08-15 10:32:50,096 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:50,096 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:47.419701+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:50,096 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_gender
2025-08-15 10:32:50,096 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:32:50,096 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:32:50,096 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-15 10:32:50,096 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3965 chars):
2025-08-15 10:32:50,096 - app.chains.section_writer - INFO -    Question: What programs or courses are boys enrolled in at ITC University, and how do their performances vary across these programs?
Answer: At ITC University, boys are enrolled in various programs, with a notable concentration in the Bachelor of Business Administration (Banking and Finance). Their performance across different courses varies significantly. For instance, the highest final score recorded is 89.0 in the course 'POST INTERNSHIP SEMINAR', which corresponds to an 'A' grade. Other cour...
2025-08-15 10:32:56,989 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:56,994 - app.chains.section_writer - INFO - 🤖 AI generated section (2048 chars):
2025-08-15 10:32:56,995 - app.chains.section_writer - INFO -    ## 3. Factors Influencing Performance  

The performance of boys at ITC University is influenced by several key factors, including socioeconomic background, study habits and time management, engagement in extracurricular activities, and support systems such as tutoring and mentorship.

### Socioecon...
2025-08-15 10:32:56,995 - app.chains.section_writer - INFO - ✅ Found 4 data tag placeholders: ['boys_enrollment_performance_by_program', 'boys_performance_in_courses', 'average_gpa_by_entry_mode', 'average_scores_by_gender']
2025-08-15 10:32:56,995 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 1932 characters
2025-08-15 10:32:56,995 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/4...
2025-08-15 10:32:57,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:32:57,849 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:32:57,849 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-15 10:32:57,849 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:32:57,849 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion performance gap findings recommendations research'
2025-08-15 10:32:57,849 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-15 10:32:57,849 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-15 10:32:57,970 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.120s]
2025-08-15 10:32:57,970 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 141
2025-08-15 10:32:58,088 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.118s]
2025-08-15 10:32:58,088 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 74
2025-08-15 10:32:58,209 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.121s]
2025-08-15 10:32:58,210 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 25
2025-08-15 10:32:58,335 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-15 10:32:58,335 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 12
2025-08-15 10:32:59,316 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 10:32:59,452 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.131s]
2025-08-15 10:32:59,453 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-15 10:32:59,453 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:59,453 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:47.419701+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:59,453 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:59,453 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:59,453 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-15 10:32:59,453 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-15 10:32:59,453 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:32:59,453 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:32.669602+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:32:59,454 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:32:59,454 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:32:59,454 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:59,454 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:29:47.419701+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:59,454 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_gender
2025-08-15 10:32:59,454 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_gender
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   📄 Doc 3: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_performance_in_courses
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:32.669602+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3815 chars):
2025-08-15 10:32:59,455 - app.chains.section_writer - INFO -    Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At ITC University, the performance of girls is significantly better than that of boys. Girls have an average score of 68.72, while boys have an average score of 54.86. Additionally, there are 58 girls and 71 boys in the sample, indicating that the performance gap is based on a larger number of boys.
Data Tag: average_scores_by_gender

Question: How does the performance of boys at ITC University compare...
2025-08-15 10:33:04,229 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:33:04,235 - app.chains.section_writer - INFO - 🤖 AI generated section (1206 chars):
2025-08-15 10:33:04,236 - app.chains.section_writer - INFO -    ## 4. Conclusion  

The analysis of performance at ITC University reveals a significant disparity between the academic achievements of boys and girls. Girls consistently outperform boys, with average scores of 68.72 and 54.86, respectively. This performance gap is further underscored by the larger s...
2025-08-15 10:33:04,236 - app.chains.section_writer - INFO - ✅ Found 3 data tag placeholders: ['average_scores_by_gender', 'boys_performance_in_courses', 'boys_enrollment_performance_by_program']
2025-08-15 10:33:04,236 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 1119 characters
2025-08-15 10:33:04,237 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:33:04,237 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-15 10:33:04,237 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:33:04,237 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-15 10:33:04,237 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 5
2025-08-15 10:33:04,238 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 4
2025-08-15 10:33:04,238 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 1
2025-08-15 10:33:04,238 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250815_102839.log
2025-08-15 10:33:04,241 - celery.app.trace - INFO - Task generate_streaming_report[58eb4159-c677-4690-8c7c-00c9bcbe982b] succeeded in 264.8709785840001s: {'outline': '# Report on Boys\' Performance at ITC University

## 1. Introduction  
   - This report analyzes the academic performance of boys at ITC University, highlighting the significant performance gap between genders and the implications for educational strategies.

## 2. Performance Comparison  
   ### 2.1 Average Scores  
   - Girls\' average score: 68.72  
   - Boys\' average score: 54.86  
   
   ### 2.2 Sample Size  
   - Number of girls: 58  
   - Number of boys: 71  
   
   ### 2.3 Performance Gap Analysis  
   - Discussion of the significant performance gap between boys and girls  
   - Implications of a larger sample size of boys on performance analysis  

## 3. Factors Influencing Performance  
   - Socioeconomic background  
   - Study habits and time management  
   - Engagement in extracurricular activities  
   - Support systems (tutoring, mentorship)  

## 4. Conclusion  
   - Summary of findings  
   - Recommendations for addressing the performance gap  
   - Call for further research into...', ...}
2025-08-15 16:09:50,785 - celery.worker.strategy - INFO - Task generate_streaming_report[fb283b73-d411-4226-956d-7128a87ebd98] received
2025-08-15 16:09:50,800 - app.tasks.report_task - INFO - starting streaming report generation...
2025-08-15 16:09:50,815 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 16:09:50,815 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: d4549886-e58d-4fb8-967e-1bec231f8e99
2025-08-15 16:09:50,815 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 16:09:50,815 - REPORT_REQUEST - INFO - 📝 Original Question: 'So how are our girls performing at ITC University?'
2025-08-15 16:09:50,815 - REPORT_REQUEST - INFO - 🆔 Task ID: d4549886-e58d-4fb8-967e-1bec231f8e99
2025-08-15 16:09:50,815 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-15T16:09:50.815506
2025-08-15 16:10:16,969 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:N/A duration:26.147s]
2025-08-15 16:10:16,970 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://*************:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-15 16:10:16,970 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 0 of 3)
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
               ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 516, in getresponse
    httplib_response = super().getresponse()
                       ^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 1428, in getresponse
    response.begin()
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/anaconda3/lib/python3.12/socket.py", line 720, in readinto
    return self._sock.recv_into(b)
           ^^^^^^^^^^^^^^^^^^^^^^^
TimeoutError: [Errno 60] Operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_node/_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
               ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/util.py", line 39, in reraise
    raise value
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
urllib3.exceptions.ReadTimeoutError: HTTPConnectionPool(host='*************', port=9200): Read timed out. (read timeout=120)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_transport.py", line 342, in perform_request
    resp = node.perform_request(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_node/_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionTimeout: Connection timeout caused by: ReadTimeoutError(HTTPConnectionPool(host='*************', port=9200): Read timed out. (read timeout=120))
2025-08-15 16:10:17,330 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.339s]
2025-08-15 16:10:17,331 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://*************:9200)> has been marked alive after a successful request
2025-08-15 16:10:17,338 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 141
2025-08-15 16:10:17,458 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.120s]
2025-08-15 16:10:17,459 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 74
2025-08-15 16:10:17,582 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.121s]
2025-08-15 16:10:17,582 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-15 16:10:17,583 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (25 docs)
2025-08-15 16:10:17,583 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (24 docs)
2025-08-15 16:10:17,583 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (19 docs)
2025-08-15 16:10:17,583 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-15 16:10:17,583 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-15 16:10:17,583 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-15 16:10:17,583 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-15 16:10:17,583 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-15 16:10:17,583 - ELASTICSEARCH_STATE - INFO -   - 'Which instituion has the most students?' (6 docs)
2025-08-15 16:10:17,583 - ELASTICSEARCH_STATE - INFO -   - 'How are students performing at ITC University?' (4 docs)
2025-08-15 16:10:17,584 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 16:10:17,584 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-15 16:10:17,584 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 16:10:17,584 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: So how are our girls performing at ITC University?
2025-08-15 16:10:17,584 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-15 16:10:17,584 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-15 16:10:31,393 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:31,432 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-15 16:10:37,409 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:37,434 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-15 16:10:46,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:47,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:47,691 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:47,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:48,356 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:48,501 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:48,553 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:50,099 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:50,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:51,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:51,188 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-15 16:10:55,246 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:55,266 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:56,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:58,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:59,046 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:10:59,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:05,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:07,081 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:07,087 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:16,317 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:19,425 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:20,693 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:28,366 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:28,380 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic results of girls at ITC University compare to those of boys?', 'sql': "SELECT s.sex, AVG(ar.finalscore) AS average_final_score, COUNT(ar.id) AS total_assessments\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN programs p ON s.student_program_id = p.id\nWHERE p.long_name = 'ITC University'\nGROUP BY s.sex\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average final scores and total assessments for students grouped by sex (girls and boys) at ITC University. It joins the necessary tables: 'assessment_results' to get the academic results, 'students' to filter by sex, and 'programs' to ensure the students are from ITC University. The use of AVG() function provides the average scores, and COUNT() gives the total number of assessments, which directly addresses the comparison of academic results between girls and boys.", 'feedback': 'The query is well-structured and answers the question effectively. However, the LIMIT clause is unnecessary since the GROUP BY clause will already return two rows (one for each sex). Removing the LIMIT clause would make the query cleaner.'}
2025-08-15 16:11:28,380 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:11:28,381 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 16:11:33,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:34,025 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:34,510 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:34,522 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How has the performance of girls at ITC University changed over the past few academic years?', 'sql': "SELECT academic_years.start_year, AVG(assessment_results.finalscore) AS average_score, COUNT(assessment_results.id) AS total_assessments\nFROM assessment_results\nJOIN students ON assessment_results.student_id = students.id\nJOIN academic_years ON assessment_results.semester_id = academic_years.id\nWHERE students.sex = 'F' AND students.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY academic_years.start_year\nORDER BY academic_years.start_year DESC\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average performance (final scores) of female students at ITC University over the academic years. It joins the necessary tables (assessment_results, students, and academic_years) to filter for female students and the specific institution. The use of AVG() provides the average score, and COUNT() gives the total number of assessments, which are relevant metrics to assess performance changes over time. The grouping by academic_years.start_year allows for a year-by-year comparison, and ordering by start_year in descending order aligns with the question's focus on changes over the past years.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by removing the LIMIT clause, as it may restrict the results to only the most recent 20 years, which might not be necessary if the goal is to analyze all available years. Additionally, clarifying the question to specify whether it seeks a comparison of average scores or trends over time could enhance the focus of the analysis.'}
2025-08-15 16:11:34,522 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:11:34,522 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 16:11:35,389 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:35,779 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:36,931 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:37,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:38,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:39,085 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:39,833 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:41,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:41,549 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on the academic results of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on academic results of girls and boys at ITC University. However, the provided schema does not contain any information about the reasons or factors affecting data availability. It primarily consists of tables related to institutions, students, academic records, and other administrative data, but lacks qualitative insights or analysis regarding data availability issues.', 'feedback': ''}
2025-08-15 16:11:41,550 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:11:41,550 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:11:41,550 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on the academic results of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on academic results of girls and boys at ITC University. However, the provided schema does not contain any information about the reasons or factors affecting data availability. It primarily consists of tables related to institutions, students, academic records, and other administrative data, but lacks qualitative insights or analysis regarding data availability issues.', 'feedback': ''}
2025-08-15 16:11:41,858 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:43,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:44,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:44,422 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence the collection and reporting of performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing the collection and reporting of performance data for girls at ITC University. However, the provided schema does not contain specific information about factors influencing data collection or reporting, nor does it have any direct references to gender-specific performance data. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks qualitative insights or contextual factors that would address the question.', 'feedback': ''}
2025-08-15 16:11:44,423 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:11:44,423 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:11:44,423 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence the collection and reporting of performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing the collection and reporting of performance data for girls at ITC University. However, the provided schema does not contain specific information about factors influencing data collection or reporting, nor does it have any direct references to gender-specific performance data. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks qualitative insights or contextual factors that would address the question.', 'feedback': ''}
2025-08-15 16:11:44,521 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:47,351 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:47,766 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:47,786 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on the academic results of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on academic results of girls and boys at ITC University. However, the provided schema does not contain any tables or fields that directly address the reasons or factors behind data availability issues. The schema includes various tables related to academic results, students, and institutions, but it lacks qualitative data or metadata that would explain the absence of data. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-15 16:11:47,786 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:11:47,786 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:11:47,786 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on the academic results of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on academic results of girls and boys at ITC University. However, the provided schema does not contain any tables or fields that directly address the reasons or factors behind data availability issues. The schema includes various tables related to academic results, students, and institutions, but it lacks qualitative data or metadata that would explain the absence of data. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-15 16:11:49,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:50,556 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:50,577 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence the collection and reporting of performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing the collection and reporting of performance data for girls at ITC University. However, the provided schema does not contain specific information about factors influencing data collection or reporting, nor does it have any direct references to gender-specific performance data. The schema includes various tables related to institutions, students, programs, and assessments, but it lacks qualitative data or contextual information that would allow for a comprehensive answer to the question.', 'feedback': ''}
2025-08-15 16:11:50,578 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:11:50,578 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:11:50,578 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence the collection and reporting of performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing the collection and reporting of performance data for girls at ITC University. However, the provided schema does not contain specific information about factors influencing data collection or reporting, nor does it have any direct references to gender-specific performance data. The schema includes various tables related to institutions, students, programs, and assessments, but it lacks qualitative data or contextual information that would allow for a comprehensive answer to the question.', 'feedback': ''}
2025-08-15 16:11:51,251 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:54,798 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:54,854 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:54,866 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on the academic results of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on academic results of girls and boys at ITC University. However, the provided schema does not contain any tables or fields that directly address the reasons or factors affecting data availability. The schema primarily consists of tables related to institutions, students, academic records, and various administrative aspects, but it lacks qualitative data or insights into the reasons behind data availability issues.', 'feedback': ''}
2025-08-15 16:11:54,866 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:11:54,866 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:11:54,867 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on the academic results of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on academic results of girls and boys at ITC University. However, the provided schema does not contain any tables or fields that directly address the reasons or factors affecting data availability. The schema primarily consists of tables related to institutions, students, academic records, and various administrative aspects, but it lacks qualitative data or insights into the reasons behind data availability issues.', 'feedback': ''}
2025-08-15 16:11:58,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:58,402 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:11:58,415 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence the collection and reporting of performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing the collection and reporting of performance data for girls at ITC University. However, the provided schema does not contain specific information about factors influencing data collection or reporting, nor does it have any direct references to gender-specific performance data. The schema includes various tables related to institutions, students, programs, and assessments, but it lacks qualitative data or contextual information that would allow for a comprehensive answer to the question.', 'feedback': ''}
2025-08-15 16:11:58,416 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:11:58,416 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:11:58,416 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence the collection and reporting of performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing the collection and reporting of performance data for girls at ITC University. However, the provided schema does not contain specific information about factors influencing data collection or reporting, nor does it have any direct references to gender-specific performance data. The schema includes various tables related to institutions, students, programs, and assessments, but it lacks qualitative data or contextual information that would allow for a comprehensive answer to the question.', 'feedback': ''}
2025-08-15 16:12:01,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:02,026 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:02,041 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on the academic results of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on academic results of girls and boys at ITC University. However, the provided schema does not contain any tables or fields that directly address the reasons or factors affecting data availability. The schema includes various tables related to academic results, students, and institutions, but it does not provide insights into the reasons for data unavailability or any qualitative analysis. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-15 16:12:02,041 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:12:02,042 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:12:02,042 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on the academic results of girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on academic results of girls and boys at ITC University. However, the provided schema does not contain any tables or fields that directly address the reasons or factors affecting data availability. The schema includes various tables related to academic results, students, and institutions, but it does not provide insights into the reasons for data unavailability or any qualitative analysis. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-15 16:12:04,843 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:04,856 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence the collection and reporting of performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing the collection and reporting of performance data for girls at ITC University. However, the provided schema does not contain specific information about factors influencing data collection or reporting, nor does it have any direct references to gender-specific performance data. The schema includes various tables related to institutions, students, programs, and assessments, but it lacks qualitative data or contextual information that would allow for a comprehensive answer to the question.', 'feedback': ''}
2025-08-15 16:12:04,856 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:12:04,856 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:12:04,857 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence the collection and reporting of performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing the collection and reporting of performance data for girls at ITC University. However, the provided schema does not contain specific information about factors influencing data collection or reporting, nor does it have any direct references to gender-specific performance data. The schema includes various tables related to institutions, students, programs, and assessments, but it lacks qualitative data or contextual information that would allow for a comprehensive answer to the question.', 'feedback': ''}
2025-08-15 16:12:07,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:32,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:32,312 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are the girls at ITC University enrolled in, and how do they perform in those areas?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, s.surname, s.othernames, ar.finalscore, ar.grade \nFROM core.students s \nJOIN core.student_programs sp ON s.id = sp.student_id \nJOIN core.programs p ON sp.program_id = p.id \nJOIN core.assessment_results ar ON sp.id = ar.student_program_id \nJOIN core.assessment_sheets as ON ar.assessment_sheet_id = as.id \nJOIN core.courses c ON as.course_id = c.id \nWHERE s.sex = 'F' AND s.institution_id = ? \nORDER BY p.long_name, c.title, s.surname LIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the programs and courses that female students (identified by 's.sex = 'F'') at ITC University (filtered by 's.institution_id = ?') are enrolled in. It also includes their performance metrics (final score and grade) from the assessment results. The joins between the tables ensure that the necessary relationships are maintained, allowing for a comprehensive view of the students' enrollment and performance.", 'feedback': "The SQL query is well-structured and addresses the question effectively. However, it could be improved by explicitly stating the institution's ID in the WHERE clause instead of using a placeholder. Additionally, consider including more performance metrics if available, such as 'exam_total' or 'gp', to provide a fuller picture of student performance."}
2025-08-15 16:12:32,313 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:12:32,313 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 16:12:34,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:35,500 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:36,856 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:40,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:43,457 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:43,465 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of enrollment of girls at ITC University, and how might the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of enrollment of girls at ITC University and potential solutions to address these challenges. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative data or insights that would help answer such a question. The schema lacks information on societal, cultural, or institutional factors affecting enrollment, as well as any qualitative assessments or surveys that might provide context on the challenges faced by female students. Therefore, this question cannot be answered using the available schema.', 'feedback': ''}
2025-08-15 16:12:43,465 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:12:43,465 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:12:43,465 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of enrollment of girls at ITC University, and how might the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of enrollment of girls at ITC University and potential solutions to address these challenges. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative data or insights that would help answer such a question. The schema lacks information on societal, cultural, or institutional factors affecting enrollment, as well as any qualitative assessments or surveys that might provide context on the challenges faced by female students. Therefore, this question cannot be answered using the available schema.', 'feedback': ''}
2025-08-15 16:12:46,382 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:49,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:49,500 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of enrollment of girls at ITC University, and how might the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of enrollment of girls at ITC University and potential solutions to address these challenges. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative data or insights regarding societal, cultural, or institutional factors affecting enrollment. Additionally, there are no fields that would allow for the analysis of gender-specific enrollment trends or challenges. Therefore, this question cannot be answered using the existing schema.', 'feedback': ''}
2025-08-15 16:12:49,500 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:12:49,500 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:12:49,500 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of enrollment of girls at ITC University, and how might the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of enrollment of girls at ITC University and potential solutions to address these challenges. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative data or insights regarding societal, cultural, or institutional factors affecting enrollment. Additionally, there are no fields that would allow for the analysis of gender-specific enrollment trends or challenges. Therefore, this question cannot be answered using the existing schema.', 'feedback': ''}
2025-08-15 16:12:52,695 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:56,091 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:12:56,123 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of enrollment of girls at ITC University, and how might the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of enrollment of girls at ITC University and potential solutions to address these challenges. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative data or insights that would allow for an analysis of social, cultural, or institutional factors affecting enrollment. Additionally, the schema lacks any direct information about gender-specific enrollment statistics or challenges faced by female students. Therefore, this question cannot be answered using the available schema.', 'feedback': ''}
2025-08-15 16:12:56,123 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:12:56,123 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:12:56,124 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of enrollment of girls at ITC University, and how might the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of enrollment of girls at ITC University and potential solutions to address these challenges. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative data or insights that would allow for an analysis of social, cultural, or institutional factors affecting enrollment. Additionally, the schema lacks any direct information about gender-specific enrollment statistics or challenges faced by female students. Therefore, this question cannot be answered using the available schema.', 'feedback': ''}
2025-08-15 16:12:58,850 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:13:01,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:13:01,662 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of enrollment of girls at ITC University, and how might the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of enrollment of girls at ITC University and potential solutions to address these challenges. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative data or insights into social, cultural, or institutional factors affecting enrollment. Therefore, this question cannot be answered using the schema.', 'feedback': ''}
2025-08-15 16:13:01,662 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 16:13:01,662 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 16:13:01,662 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of enrollment of girls at ITC University, and how might the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the factors contributing to the lack of enrollment of girls at ITC University and potential solutions to address these challenges. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative data or insights into social, cultural, or institutional factors affecting enrollment. Therefore, this question cannot be answered using the schema.', 'feedback': ''}
2025-08-15 16:13:01,663 - root - INFO - 'No results'
2025-08-15 16:13:01,663 - root - INFO - 'No results'
2025-08-15 16:13:01,663 - root - INFO - 'No results'
2025-08-15 16:13:01,663 - app.chains.composite.report_pipeline - INFO - generating_informed_outline
2025-08-15 16:13:10,065 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:13:10,073 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-15 16:13:19,175 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 16:13:19,196 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 16:13:19,196 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 16:13:19,196 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 16:13:19,196 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'So how are our girls performing at ITC University?'
2025-08-15 16:13:19,196 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 16:13:19,196 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 16:13:19,196 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 16:13:19,196 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 16:13:19,196 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the academic results of girls at ITC University compare to those of boys?...
2025-08-15 16:13:19,196 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there may not be any available data on the academ...
2025-08-15 16:13:19,196 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: academic_results_by_gender_itc_university
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-15 16:13:19,197 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 16:13:19,197 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 16:13:19,197 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'So how are our girls performing at ITC University?'
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 16:13:19,197 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are the girls at ITC University enrolled in, and how do they perform in tho...
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are currently no records of girls enrolled in any programs or courses at ITC U...
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_enrollment_performance_itc_university
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 16:13:19,197 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-15 16:13:19,197 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 16:13:19,197 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 16:13:19,197 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'So how are our girls performing at ITC University?'
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 16:13:19,198 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How has the performance of girls at ITC University changed over the past few academic years?...
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Unfortunately, there are no available data results regarding the performance of girls at ITC Univers...
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_performance_trends_itc_university
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 16:13:19,198 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-15 16:13:19,198 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 3
2025-08-15 16:13:19,198 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 16:13:19,198 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-15 16:13:19,198 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 16:13:19,198 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 3 documents
2025-08-15 16:13:19,198 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-15 16:13:19,198 - UPSERT_DOCS - INFO -     Content: Question: How do the academic results of girls at ITC University compare to those of boys?
Answer: T...
2025-08-15 16:13:19,198 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'So how are our girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T16:12:02.042152+00:00', 'data_returned': False}
2025-08-15 16:13:19,198 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-15 16:13:19,198 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are the girls at ITC University enrolled in, and how do they perf...
2025-08-15 16:13:19,199 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'So how are our girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T16:13:01.662723+00:00', 'data_returned': False}
2025-08-15 16:13:19,199 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-15 16:13:19,199 - UPSERT_DOCS - INFO -     Content: Question: How has the performance of girls at ITC University changed over the past few academic year...
2025-08-15 16:13:19,199 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'So how are our girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T16:12:04.857072+00:00', 'data_returned': False}
2025-08-15 16:13:19,199 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 0/3
2025-08-15 16:13:46,394 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:N/A duration:27.193s]
2025-08-15 16:13:46,394 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://*************:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-15 16:13:46,394 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: Connection timed out
2025-08-15 16:13:46,395 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 16:13:46,395 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-15 16:13:46,395 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 16:13:46,395 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: Connection timed out
2025-08-15 16:13:46,395 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250815_102839.log
2025-08-15 16:13:46,399 - celery.app.trace - INFO - Task generate_streaming_report[fb283b73-d411-4226-956d-7128a87ebd98] succeeded in 235.60547450000013s: {'error': 'Error generating streaming report: Connection timed out'}

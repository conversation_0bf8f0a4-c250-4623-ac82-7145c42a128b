2025-08-08 11:46:00,211 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_114600.log
2025-08-08 11:46:00,211 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:46:00,211 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 73c6d32d-7478-4f84-b0e5-c3925b51fea2
2025-08-08 11:46:00,211 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:46:00,211 - REPORT_REQUEST - INFO - 📝 Original Question: 'What institution has the most students?'
2025-08-08 11:46:00,211 - REPORT_REQUEST - INFO - 🆔 Task ID: 73c6d32d-7478-4f84-b0e5-c3925b51fea2
2025-08-08 11:46:00,211 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T11:46:00.211872
2025-08-08 11:46:00,406 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.194s]
2025-08-08 11:46:00,406 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 69
2025-08-08 11:46:00,598 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.192s]
2025-08-08 11:46:00,599 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 38
2025-08-08 11:46:00,796 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.197s]
2025-08-08 11:46:00,796 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (6):
2025-08-08 11:46:00,796 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (20 docs)
2025-08-08 11:46:00,796 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (15 docs)
2025-08-08 11:46:00,796 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (11 docs)
2025-08-08 11:46:00,796 - ELASTICSEARCH_STATE - INFO -   - 'How many girls are at ITC University?' (10 docs)
2025-08-08 11:46:00,796 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 11:46:00,796 - ELASTICSEARCH_STATE - INFO -   - 'string' (5 docs)
2025-08-08 11:46:00,797 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:46:00,797 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 11:46:00,797 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:46:00,797 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: What institution has the most students?
2025-08-08 11:46:00,797 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-08 11:46:00,797 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 11:46:08,825 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:08,846 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 11:46:13,141 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:13,166 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-08 11:46:14,765 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:15,183 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:15,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:15,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:15,384 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:15,431 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:15,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:15,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:15,664 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:15,680 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:15,738 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-08 11:46:17,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:17,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:17,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:18,066 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:18,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:18,308 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:19,614 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:19,627 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:19,745 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:20,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:20,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:20,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:20,568 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:21,323 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:21,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:21,598 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:21,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:21,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:23,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:25,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:25,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:25,887 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:26,775 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:27,794 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:28,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:29,638 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:30,571 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:30,980 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:31,313 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:31,788 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:32,379 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:32,451 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:32,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:32,890 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the demographic breakdown of students at the institution with the most students?', 'sql': 'SELECT s.sex, COUNT(*) AS student_count\nFROM core.students s\nJOIN core.institutions i ON s.institution_id = i.id\nWHERE i.id = (SELECT institution_id FROM core.students GROUP BY institution_id ORDER BY COUNT(*) DESC LIMIT 1)\nGROUP BY s.sex;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by using a subquery that groups students by institution_id and orders them by the count of students in descending order, limiting the result to the top institution. It then joins the students table with the institutions table to ensure that the demographic breakdown is specific to that institution. Finally, it groups the results by sex and counts the number of students for each sex, which provides the demographic breakdown as requested in the question.', 'feedback': "The SQL query is well-structured and accurately answers the question. However, it could be improved by also including other demographic factors such as age or nationality if those were relevant to the breakdown. Additionally, clarifying the term 'demographic breakdown' in the question could help ensure that all relevant demographic factors are considered."}
2025-08-08 11:46:32,891 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:46:32,891 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:46:34,171 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:36,040 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:36,880 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:37,207 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:37,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:37,501 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the retention rate of students at the institution with the most students?', 'sql': "WITH student_counts AS (SELECT institution_id, COUNT(*) AS total_students FROM core.students GROUP BY institution_id), max_institution AS (SELECT institution_id FROM student_counts ORDER BY total_students DESC LIMIT 1), retained_students AS (SELECT institution_id, COUNT(*) AS retained FROM core.student_programs WHERE student_program_status_id = (SELECT id FROM core.student_status_types WHERE status = 'active') GROUP BY institution_id) SELECT (COALESCE(retained_students.retained, 0) * 1.0 / student_counts.total_students) * 100 AS retention_rate FROM student_counts JOIN max_institution ON student_counts.institution_id = max_institution.institution_id LEFT JOIN retained_students ON max_institution.institution_id = retained_students.institution_id;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the total number of students per institution and selecting the one with the maximum count. It then calculates the number of retained students by counting those with an 'active' status in the student_programs table. Finally, it computes the retention rate by dividing the number of retained students by the total number of students at the identified institution and multiplying by 100 to express it as a percentage. The use of COALESCE ensures that if there are no retained students, the calculation does not result in a division by zero.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding comments to clarify each step of the query for better readability and maintainability. Additionally, it might be beneficial to explicitly handle cases where there are no students at the institution to ensure the output is clear.'}
2025-08-08 11:46:37,502 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:46:37,502 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:46:38,588 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:39,591 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:39,727 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:40,325 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:40,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:41,501 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:42,270 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:43,680 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:44,930 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:45,157 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:45,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:45,931 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:45,943 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the student population of the institution with the most students compare to other institutions?', 'sql': "WITH StudentCounts AS (\n    SELECT institution_id, COUNT(*) AS student_count\n    FROM core.students\n    GROUP BY institution_id\n), MaxInstitution AS (\n    SELECT institution_id, student_count\n    FROM StudentCounts\n    ORDER BY student_count DESC\n    LIMIT 1\n)\nSELECT i.name AS institution_name, sc.student_count,\n       CASE WHEN sc.institution_id = mi.institution_id THEN 'Most Students' ELSE 'Other' END AS comparison\nFROM StudentCounts sc\nJOIN auth.institutions i ON sc.institution_id = i.id\nCROSS JOIN MaxInstitution mi;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and then selecting the one with the highest count. It also compares this institution to others by labeling it as 'Most Students' and all others as 'Other'. The use of a CTE (Common Table Expression) to first calculate student counts and then find the maximum is a valid approach. The final selection includes the institution name and the count of students, fulfilling the requirement of the question.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating what kind of comparison is being made (e.g., percentage difference, ratio, etc.) if that was intended. Additionally, ensuring that the schema references are correct (e.g., 'core.students' vs. 'students') would be beneficial for clarity."}
2025-08-08 11:46:45,943 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:46:45,944 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:46:46,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:46,590 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:46,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:48,642 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:48,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:48,845 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:46:48,846 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:46:48,846 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:46:48,846 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:46:49,456 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:50,397 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:50,418 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to retention rates at large institutions compared to smaller colleges or universities. This requires qualitative insights and potentially external data on institutional practices, student experiences, and retention strategies, which are not represented in the provided database schema. The schema contains structured data about institutions, students, programs, and various academic and administrative records, but it does not include qualitative factors or comparative analyses necessary to answer the question.', 'feedback': ''}
2025-08-08 11:46:50,418 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:46:50,418 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:46:50,418 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to retention rates at large institutions compared to smaller colleges or universities. This requires qualitative insights and potentially external data on institutional practices, student experiences, and retention strategies, which are not represented in the provided database schema. The schema contains structured data about institutions, students, programs, and various academic and administrative records, but it does not include qualitative factors or comparative analyses necessary to answer the question.', 'feedback': ''}
2025-08-08 11:46:50,592 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:50,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:51,618 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:51,743 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:52,824 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:54,038 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:54,059 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How has the student enrollment at the institution with the most students changed over the past few years?', 'sql': "WITH InstitutionStudentCount AS (  SELECT institution_id, COUNT(*) AS student_count  FROM core.students  GROUP BY institution_id), MaxInstitution AS (  SELECT institution_id  FROM InstitutionStudentCount  ORDER BY student_count DESC  LIMIT 1) SELECT sp.created_at, COUNT(s.id) AS enrollment_count  FROM core.student_programs sp  JOIN core.students s ON sp.student_id = s.id  WHERE s.institution_id = (SELECT institution_id FROM MaxInstitution)  AND sp.created_at >= DATE_TRUNC('year', CURRENT_DATE) - INTERVAL '5 years'  GROUP BY sp.created_at ORDER BY sp.created_at;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest number of students by counting the students per institution and selecting the one with the maximum count. It then retrieves the enrollment data for that institution over the past five years by joining the student programs with the students table, filtering by the institution ID, and grouping the results by the creation date of the student programs. This aligns with the question's requirement to analyze changes in student enrollment over time at the institution with the most students.", 'feedback': "The question could be clarified by specifying what metrics of 'change' are of interest (e.g., total enrollment numbers, percentage change, etc.). Additionally, the SQL could be improved by ensuring that the date filtering captures the exact years of interest, perhaps by explicitly stating the start and end years instead of just using a 5-year interval from the current date."}
2025-08-08 11:46:54,059 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:46:54,060 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:46:54,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:54,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:55,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:55,512 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:55,517 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total number of students enrolled at the institution with the most students?', 'sql': 'SELECT COUNT(s.id) AS total_students\nFROM students s\nWHERE s.institution_id = (\n    SELECT institution_id\n    FROM students\n    GROUP BY institution_id\n    ORDER BY COUNT(*) DESC\n    LIMIT 1\n);', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by using a subquery that groups students by institution_id, counts them, and orders the results in descending order to find the institution with the highest count. The outer query then counts the total number of students for that specific institution. This aligns perfectly with the question asked.', 'feedback': 'The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to add a comment in the SQL to clarify the purpose of the subquery for future reference.'}
2025-08-08 11:46:55,517 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:46:55,517 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:46:56,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:56,444 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:46:56,444 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:46:56,444 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:46:56,444 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:46:57,200 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:57,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:57,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:57,718 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to retention rates at large institutions compared to smaller colleges or universities. This requires qualitative insights and potentially external data or research, which are not present in the provided database schema. The schema contains structured data about institutions, students, programs, and various academic and administrative records, but it does not include specific metrics or qualitative factors related to retention rates or comparisons between institution sizes.', 'feedback': ''}
2025-08-08 11:46:57,719 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:46:57,719 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:46:57,719 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to retention rates at large institutions compared to smaller colleges or universities. This requires qualitative insights and potentially external data or research, which are not present in the provided database schema. The schema contains structured data about institutions, students, programs, and various academic and administrative records, but it does not include specific metrics or qualitative factors related to retention rates or comparisons between institution sizes.', 'feedback': ''}
2025-08-08 11:46:57,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:58,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:58,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:58,886 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:59,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:59,346 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:46:59,358 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains quantitative data related to institutions, students, programs, and various administrative aspects, but it does not include qualitative insights or factors such as reputation, marketing strategies, student satisfaction, or community engagement that would be necessary to answer this question. Therefore, it cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 11:46:59,358 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:46:59,358 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:46:59,358 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains quantitative data related to institutions, students, programs, and various administrative aspects, but it does not include qualitative insights or factors such as reputation, marketing strategies, student satisfaction, or community engagement that would be necessary to answer this question. Therefore, it cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 11:47:00,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:00,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:01,784 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:01,847 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:02,184 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:02,698 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:02,828 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:02,843 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:47:02,843 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:02,843 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:02,843 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:47:02,989 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:03,628 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:03,661 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to retention rates at large institutions compared to smaller colleges or universities. This requires qualitative insights and possibly data on student experiences, institutional policies, and demographic factors that are not represented in the provided schema. The schema primarily contains structured data about institutions, students, programs, and transactions, but does not include qualitative data or specific metrics related to retention rates or the factors influencing them.', 'feedback': ''}
2025-08-08 11:47:03,661 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:03,661 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:03,661 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to retention rates at large institutions compared to smaller colleges or universities. This requires qualitative insights and possibly data on student experiences, institutional policies, and demographic factors that are not represented in the provided schema. The schema primarily contains structured data about institutions, students, programs, and transactions, but does not include qualitative data or specific metrics related to retention rates or the factors influencing them.', 'feedback': ''}
2025-08-08 11:47:04,280 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:04,307 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a quantitative one. The provided schema contains data about institutions, students, programs, and various related entities, but it does not include any qualitative factors or insights that could explain enrollment trends. The schema lacks information on external influences, market conditions, or institutional policies that could affect enrollment, making it impossible to answer the question based solely on the available data.', 'feedback': ''}
2025-08-08 11:47:04,307 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:04,307 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:04,308 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a quantitative one. The provided schema contains data about institutions, students, programs, and various related entities, but it does not include any qualitative factors or insights that could explain enrollment trends. The schema lacks information on external influences, market conditions, or institutional policies that could affect enrollment, making it impossible to answer the question based solely on the available data.', 'feedback': ''}
2025-08-08 11:47:04,335 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:04,372 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various administrative aspects, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the university's attractiveness. Therefore, this question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-08 11:47:04,372 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:04,372 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:04,372 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various administrative aspects, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the university's attractiveness. Therefore, this question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-08 11:47:04,977 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:04,977 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:04,985 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and enrollment processes, but it does not provide specific metrics or factors that would directly explain high enrollment numbers. To answer this question, one would need to analyze various data points such as marketing efforts, program offerings, student satisfaction, and external factors, none of which are explicitly detailed in the schema.', 'feedback': ''}
2025-08-08 11:47:04,985 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:04,985 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:04,985 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and enrollment processes, but it does not provide specific metrics or factors that would directly explain high enrollment numbers. To answer this question, one would need to analyze various data points such as marketing efforts, program offerings, student satisfaction, and external factors, none of which are explicitly detailed in the schema.', 'feedback': ''}
2025-08-08 11:47:05,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:06,781 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:06,795 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:47:06,796 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:06,796 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:06,796 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:47:06,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:07,008 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:07,530 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:07,547 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about various entities related to institutions, students, programs, and assessments, but it does not include qualitative factors or insights that would help answer the question. The schema lacks information on specific retention strategies, student experiences, or institutional policies that could influence retention rates.', 'feedback': ''}
2025-08-08 11:47:07,547 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:07,547 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:07,547 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about various entities related to institutions, students, programs, and assessments, but it does not include qualitative factors or insights that would help answer the question. The schema lacks information on specific retention strategies, student experiences, or institutional policies that could influence retention rates.', 'feedback': ''}
2025-08-08 11:47:08,036 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:08,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:08,598 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:08,626 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided database schema contains tables related to student data, institutions, programs, and various administrative aspects, but it does not include any qualitative data or analysis regarding factors influencing enrollment trends. Therefore, the question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 11:47:08,626 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:08,626 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:08,626 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided database schema contains tables related to student data, institutions, programs, and various administrative aspects, but it does not include any qualitative data or analysis regarding factors influencing enrollment trends. Therefore, the question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 11:47:08,731 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:08,745 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with a specific SQL query. The schema provides data about institutions, applicants, and related entities, but it does not contain direct metrics or factors that would explain enrollment numbers, such as marketing strategies, community engagement, or other qualitative factors. Therefore, while the schema contains relevant data, it does not provide a complete answer to the question.', 'feedback': ''}
2025-08-08 11:47:08,745 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:08,745 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:08,746 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with a specific SQL query. The schema provides data about institutions, applicants, and related entities, but it does not contain direct metrics or factors that would explain enrollment numbers, such as marketing strategies, community engagement, or other qualitative factors. Therefore, while the schema contains relevant data, it does not provide a complete answer to the question.', 'feedback': ''}
2025-08-08 11:47:09,884 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:09,908 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various administrative aspects, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the reasons behind student attraction and retention. Therefore, this question cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 11:47:09,908 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:09,908 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:09,908 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various administrative aspects, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the reasons behind student attraction and retention. Therefore, this question cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 11:47:09,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:10,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:11,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:11,631 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:11,646 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a quantitative one. The provided schema contains data about institutions, students, programs, and various related entities, but it does not include any qualitative factors or insights that could explain enrollment trends. The schema lacks information on external influences, market conditions, or institutional policies that could affect enrollment, making it impossible to answer the question based solely on the available data.', 'feedback': ''}
2025-08-08 11:47:11,646 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:11,647 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:11,647 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a quantitative one. The provided schema contains data about institutions, students, programs, and various related entities, but it does not include any qualitative factors or insights that could explain enrollment trends. The schema lacks information on external influences, market conditions, or institutional policies that could affect enrollment, making it impossible to answer the question based solely on the available data.', 'feedback': ''}
2025-08-08 11:47:12,555 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:12,575 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and students, but it does not provide specific metrics or factors that would directly explain enrollment numbers, such as marketing strategies, community engagement, or other qualitative aspects. To answer this question, one would need additional context or data that is not present in the schema.', 'feedback': ''}
2025-08-08 11:47:12,575 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:12,575 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:12,576 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and students, but it does not provide specific metrics or factors that would directly explain enrollment numbers, such as marketing strategies, community engagement, or other qualitative aspects. To answer this question, one would need additional context or data that is not present in the schema.', 'feedback': ''}
2025-08-08 11:47:13,004 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:13,018 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the university's attractiveness. Therefore, this question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-08 11:47:13,019 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:13,019 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:13,019 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the university's attractiveness. Therefore, this question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-08 11:47:14,225 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:14,259 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:14,267 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are most popular at the institution with the most students?', 'sql': 'SELECT p.long_name, COUNT(sp.student_id) AS student_count\nFROM core.programs p\nJOIN core.student_programs sp ON p.id = sp.program_id\nWHERE sp.institution_id = (\n    SELECT institution_id\n    FROM core.students\n    GROUP BY institution_id\n    ORDER BY COUNT(*) DESC\n    LIMIT 1\n)\nGROUP BY p.id, p.long_name\nORDER BY student_count DESC;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and selecting the one with the highest count. It then joins the programs with the student_programs table to count how many students are enrolled in each program at that institution. The results are grouped by program and ordered by the number of students, which aligns with the question's request for the most popular programs.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be beneficial to clarify whether 'most popular' refers strictly to the number of students enrolled or if other factors (like course ratings or completion rates) should be considered. Additionally, ensuring that the query handles cases where multiple institutions have the same highest student count could be addressed, depending on the intended interpretation of 'most popular'."}
2025-08-08 11:47:14,267 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:14,267 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:47:14,379 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:15,965 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:15,985 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided schema contains tables related to student data, institutions, programs, and various administrative aspects, but it does not include any qualitative data or analysis regarding factors influencing enrollment trends. Therefore, the question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 11:47:15,985 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:15,985 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:15,985 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided schema contains tables related to student data, institutions, programs, and various administrative aspects, but it does not include any qualitative data or analysis regarding factors influencing enrollment trends. Therefore, the question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 11:47:16,136 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:16,155 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with a specific dataset from the schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not provide direct metrics or factors that would explain enrollment numbers. To answer this question, one would need to analyze data trends, student feedback, marketing strategies, and other external factors that are not explicitly captured in the schema.', 'feedback': ''}
2025-08-08 11:47:16,155 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:16,155 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:16,155 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with a specific dataset from the schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not provide direct metrics or factors that would explain enrollment numbers. To answer this question, one would need to analyze data trends, student feedback, marketing strategies, and other external factors that are not explicitly captured in the schema.', 'feedback': ''}
2025-08-08 11:47:20,509 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:21,942 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:24,049 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:26,046 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:28,154 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:28,187 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of these Bachelor of Education programs among students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the popularity of Bachelor of Education programs among students. The provided schema contains structured data about institutions, programs, students, and various related entities, but it does not include qualitative insights or opinions that would explain popularity. Factors such as student satisfaction, job placement rates, or personal preferences are not captured in the schema, making it impossible to answer the question directly or indirectly.', 'feedback': ''}
2025-08-08 11:47:28,187 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:28,187 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:28,187 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of these Bachelor of Education programs among students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the popularity of Bachelor of Education programs among students. The provided schema contains structured data about institutions, programs, students, and various related entities, but it does not include qualitative insights or opinions that would explain popularity. Factors such as student satisfaction, job placement rates, or personal preferences are not captured in the schema, making it impossible to answer the question directly or indirectly.', 'feedback': ''}
2025-08-08 11:47:30,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:32,983 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:33,001 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of these Bachelor of Education programs among students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the popularity of Bachelor of Education programs among students. The provided schema contains structured data about institutions, programs, students, and various related entities, but it does not include qualitative insights or opinions that would explain the popularity of specific programs. To answer this question, one would need survey data, student feedback, or other qualitative research, which is not present in the schema.', 'feedback': ''}
2025-08-08 11:47:33,001 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:33,002 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:33,002 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of these Bachelor of Education programs among students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the popularity of Bachelor of Education programs among students. The provided schema contains structured data about institutions, programs, students, and various related entities, but it does not include qualitative insights or opinions that would explain the popularity of specific programs. To answer this question, one would need survey data, student feedback, or other qualitative research, which is not present in the schema.', 'feedback': ''}
2025-08-08 11:47:34,768 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:36,443 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:36,458 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of these Bachelor of Education programs among students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the popularity of Bachelor of Education programs among students. The provided schema contains data about institutions, programs, students, and various related entities, but it does not include qualitative data or insights into student preferences, motivations, or opinions. Therefore, it cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:47:36,458 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:36,458 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:36,458 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of these Bachelor of Education programs among students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the popularity of Bachelor of Education programs among students. The provided schema contains data about institutions, programs, students, and various related entities, but it does not include qualitative data or insights into student preferences, motivations, or opinions. Therefore, it cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:47:39,489 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:41,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:41,429 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of these Bachelor of Education programs among students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the popularity of Bachelor of Education programs among students. The provided schema contains structured data about various entities related to institutions, programs, students, and their interactions, but it does not include qualitative insights or opinions that would explain popularity. Factors contributing to popularity are typically subjective and would require survey data, student feedback, or external research, none of which are represented in the schema.', 'feedback': ''}
2025-08-08 11:47:41,429 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:47:41,429 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:47:41,429 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of these Bachelor of Education programs among students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the popularity of Bachelor of Education programs among students. The provided schema contains structured data about various entities related to institutions, programs, students, and their interactions, but it does not include qualitative insights or opinions that would explain popularity. Factors contributing to popularity are typically subjective and would require survey data, student feedback, or external research, none of which are represented in the schema.', 'feedback': ''}
2025-08-08 11:47:41,430 - root - INFO - [{'total_students': 192627}]
2025-08-08 11:47:41,434 - root - INFO - [{'institution_name': 'ITC University', 'student_count': 49153, 'comparison': 'Other'}, {'institution_name': 'Akrofi-Christaller Institute of Theology Mission and Culture', 'student_count': 114, 'comparison': 'Other'}, {'institution_name': 'Accra Medical', 'student_count': 13012, 'comparison': 'Other'}, {'institution_name': 'Ghana School of Law', 'student_count': 262, 'comparison': 'Other'}, {'institution_name': 'Koforidua Technical University', 'student_count': 17758, 'comparison': 'Other'}, {'institution_name': 'Presbyterian University College Ghana', 'student_count': 32094, 'comparison': 'Other'}, {'institution_name': 'Methodist University College Ghana', 'student_count': 1, 'comparison': 'Other'}, {'institution_name': 'Wisconsin International University College', 'student_count': 1202, 'comparison': 'Other'}, {'institution_name': 'ITC University', 'student_count': 192627, 'comparison': 'Most Students'}, {'institution_name': 'Kofi Annan International Peacekeeping Training Centre', 'student_count': 52, 'comparison': 'Other'}, {'institution_name': 'Klintaps University College of Health and Allied Sciences', 'student_count': 1135, 'comparison': 'Other'}]
2025-08-08 11:47:41,434 - root - INFO - [{'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'student_count': 15977}, {'long_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'student_count': 10732}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'student_count': 8905}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE)', 'student_count': 7606}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY)', 'student_count': 6649}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'student_count': 6064}, {'long_name': 'BACHELOR OF ARTS (POLITICAL SCIENCE EDUCATION)', 'student_count': 5305}, {'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'student_count': 5241}, {'long_name': 'Bachelor Of Science (Information Technology Education)', 'student_count': 4481}, {'long_name': 'BACHELOR OF ART (SOCIAL STUDIES EDUCATION)', 'student_count': 4255}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'student_count': 3972}, {'long_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'student_count': 3330}, {'long_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'student_count': 3189}, {'long_name': 'BACHELOR OF ARTS (SOCIAL STUDIES EDUCATION)', 'student_count': 2989}, {'long_name': 'BACHELOR OF ARTS (GEOGRAPHY EDUCATION)', 'student_count': 2947}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY)', 'student_count': 2804}, {'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'student_count': 2709}, {'long_name': 'BACHELOR OF ARTS (ART EDUCATION)', 'student_count': 2503}, {'long_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'student_count': 2381}, {'long_name': 'DIPLOMA IN EDUCATION', 'student_count': 2348}, {'long_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'student_count': 2235}, {'long_name': 'Bachelor Of Science (Management Education)', 'student_count': 2181}, {'long_name': 'Bachelor Of Science (Mathematics Education)', 'student_count': 2166}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS EDUCATION)', 'student_count': 2053}, {'long_name': 'Bachelor Of Arts (English Language Education)', 'student_count': 2014}, {'long_name': 'Bachelor Of Science (Accounting Education)', 'student_count': 1999}, {'long_name': 'DIPLOMA IN EARLY GRADE', 'student_count': 1994}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'student_count': 1854}, {'long_name': 'BACHELOR OF ARTS (ECONOMICS EDUCATION)', 'student_count': 1821}, {'long_name': 'BACHELOR OF ARTS (ENGLISH LANGUAGE EDUCATION)', 'student_count': 1812}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICAL EDUCATION)', 'student_count': 1678}, {'long_name': 'Bachelor Of Science (Agricultural Science Education)', 'student_count': 1639}, {'long_name': 'DIPLOMA IN BASIC EDUCATION', 'student_count': 1579}, {'long_name': 'BACHELOR OF ARTS (GRAPHIC DESIGN)', 'student_count': 1558}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - R', 'student_count': 1508}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'student_count': 1448}, {'long_name': 'BACHELOR OF ARTS (FRENCH EDUCATION)', 'student_count': 1441}, {'long_name': 'BACHELOR OF ARTS (EWE EDUCATION)', 'student_count': 1397}, {'long_name': 'BACHELOR OF SCIENCE (FOOD AND NUTRITION EDUCATION)', 'student_count': 1359}, {'long_name': 'BACHELOR OF ARTS (ENGLISH EDUCATION)', 'student_count': 1358}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS EDUCATION)', 'student_count': 1273}, {'long_name': 'Bachelor Of Science (Biological Sciences Education)', 'student_count': 1193}, {'long_name': 'BACHELOR OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'student_count': 1173}, {'long_name': 'Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'student_count': 1171}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - R', 'student_count': 1160}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - S', 'student_count': 1137}, {'long_name': 'Bachelor Of Science (Environmental Health And Sanitation Education)', 'student_count': 1123}, {'long_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'student_count': 1113}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'student_count': 1089}, {'long_name': 'Bachelor Of Science (Integrated Science Education)', 'student_count': 1056}, {'long_name': 'BACHELOR OF ARTS (TWI EDUCATION)', 'student_count': 1051}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION AND MANAGEMENT) BY DISTANCE', 'student_count': 1029}, {'long_name': 'BACHELOR OF ARTS (HISTORY EDUCATION)', 'student_count': 989}, {'long_name': 'BACHELOR OF SCIENCE (HEALTH ADMINISTRATION EDUCATION)', 'student_count': 942}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL EDUCATION)', 'student_count': 929}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED SCIENCE EDUCATION)', 'student_count': 918}, {'long_name': 'Bachelor Of Arts (Social Studies With Economics Education)', 'student_count': 880}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED HOME ECONOMICS EDUCATION)', 'student_count': 870}, {'long_name': 'BACHELOR OF EDUCATION (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'student_count': 858}, {'long_name': 'Bachelor Of Education (Early Grade) - M', 'student_count': 816}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - R', 'student_count': 811}, {'long_name': 'BACHELOR OF ARTS (MUSIC EDUCATION)', 'student_count': 751}, {'long_name': 'Bachelor Of Education (Junior High)', 'student_count': 707}, {'long_name': 'BACHELOR OF SCIENCE (BIOLOGY EDUCATION)', 'student_count': 704}, {'long_name': 'DIPLOMA (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'student_count': 690}, {'long_name': 'BACHELOR OF ARTS (FASHION DESIGN AND TEXTILE EDUCATION)', 'student_count': 684}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 649}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - S', 'student_count': 627}, {'long_name': 'BACHELOR OF SCIENCE (ACCOUNTING EDUCATION)', 'student_count': 578}, {'long_name': 'BACHELOR OF SCIENCE (MANAGEMENT EDUCATION)', 'student_count': 569}, {'long_name': 'Master Of Arts In Educational Leadership', 'student_count': 542}, {'long_name': 'MASTER OF PHILOSOPHY (GHANAIAN LANGUAGE STUDIES)', 'student_count': 517}, {'long_name': 'Diploma In Education - KS', 'student_count': 509}, {'long_name': 'BACHELOR OF SCIENCE (HOME ECONOMICS EDUCATION)', 'student_count': 501}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'student_count': 481}, {'long_name': 'Diploma In Education', 'student_count': 473}, {'long_name': 'BACHELOR OF ARTS (TWI WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 466}, {'long_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'student_count': 444}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'student_count': 444}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'student_count': 439}, {'long_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'student_count': 427}, {'long_name': 'MASTER OF PHILOSOPHY (ENGLISH LANGUAGE)', 'student_count': 400}, {'long_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'student_count': 392}, {'long_name': 'MASTER OF PHILOSOPHY (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'student_count': 379}, {'long_name': 'BACHELOR  OF ARTS (EWE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 366}, {'long_name': 'MASTER OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'student_count': 364}, {'long_name': 'Bachelor Of Arts (Economics With Social Studies Education)', 'student_count': 351}, {'long_name': 'BACHELOR OF MUSIC', 'student_count': 351}, {'long_name': 'BACHELOR OF SCIENCE (AGRICULTURAL SCIENCE EDUCATION)', 'student_count': 349}, {'long_name': 'Bachelor Of Business Administration (Management) - E', 'student_count': 348}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICS EDUCATION)', 'student_count': 346}, {'long_name': 'MASTER OF PHILOSOPHY (SCIENCE EDUCATION)', 'student_count': 342}, {'long_name': 'MASTER OF PHILOSOPHY (EARLY CHILDHOOD EDUCATION)', 'student_count': 340}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND ENGLISH EDUCATION)', 'student_count': 339}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 325}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY) BY DISTANCE', 'student_count': 323}, {'long_name': 'BACHELOR OF ARTS (FANTE EDUCATION)', 'student_count': 316}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - S', 'student_count': 315}, {'long_name': 'Bachelor Of Science (Information Technology Education) - W', 'student_count': 310}, {'long_name': 'Diploma In Business Administration (Management)', 'student_count': 309}, {'long_name': 'MASTER OF PHILOSOPHY (ART EDUCATION)', 'student_count': 308}, {'long_name': 'BACHELOR OF ARTS (COMMUNICATION STUDIES)', 'student_count': 306}, {'long_name': 'DIPLOMA (GRAPHIC DESIGN)', 'student_count': 298}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (MARKETING AND ENTREPRENEURSHIP)', 'student_count': 296}, {'long_name': 'DIPLOMA (ART)', 'student_count': 295}, {'long_name': 'Bachelor Of Science (Agribusiness Management And Entrepreneurship Education)', 'student_count': 291}, {'long_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'student_count': 283}, {'long_name': 'MASTER OF PHILOSOPHY IN SOCIAL STUDIES', 'student_count': 283}, {'long_name': 'BACHELOR OF ARTS (THEATRE ARTS)', 'student_count': 269}, {'long_name': 'MASTER OF PHILOSOPHY (SPECIAL EDUCATION)', 'student_count': 265}, {'long_name': 'MASTER OF PHILOSOPHY (BASIC EDUCATION)', 'student_count': 254}, {'long_name': 'DIPLOMA IN MANAGEMENT STUDIES', 'student_count': 251}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES)', 'student_count': 243}, {'long_name': 'Bachelor Of Science (Chemistry Education)', 'student_count': 236}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'student_count': 232}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS WITH ECONOMICS EDUCATION)', 'student_count': 226}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - W', 'student_count': 224}, {'long_name': 'DIPLOMA IN MUSIC', 'student_count': 208}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education) - S', 'student_count': 207}, {'long_name': 'MASTER OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'student_count': 202}, {'long_name': 'Bachelor Of Science (Information Technology)', 'student_count': 193}, {'long_name': 'Bachelor Of Science (Natural Resources Management And Education)', 'student_count': 192}, {'long_name': 'Bachelor Of Science (Wood Technology Education) - S', 'student_count': 192}, {'long_name': 'MASTER OF PHILOSOPHY (FRENCH)', 'student_count': 189}, {'long_name': 'MASTER OF EDUCATION (EARLY CHILDHOOD EDUCATION)', 'student_count': 188}, {'long_name': 'DOCTOR OF PHILOSOPHY (SCIENCE EDUCATION)', 'student_count': 187}, {'long_name': 'BACHELOR OF ARTS (CREATIVE ARTS AND DESIGN EDUCATION)', 'student_count': 182}, {'long_name': 'MASTER OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'student_count': 178}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'student_count': 175}, {'long_name': 'Master Of Technology Education In Catering And Hospitality', 'student_count': 175}, {'long_name': 'Bachelor Of Science In Administration (Accounting)', 'student_count': 173}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND TWI EDUCATION)', 'student_count': 171}, {'long_name': 'MASTER OF PHILOSOPHY (INFORMATION AND COMMUNICATION TECHNOLOGY)', 'student_count': 169}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'student_count': 167}, {'long_name': 'MASTER OF PHILOSOPHY (POLITICAL SCIENCE EDUCATION)', 'student_count': 167}, {'long_name': 'Bachelor Of Education (Early Grade) - K', 'student_count': 166}, {'long_name': 'Bachelor Of Education (Upper Primary)', 'student_count': 165}, {'long_name': 'Bachelor Of Business Administration (Management) - W', 'student_count': 163}, {'long_name': 'Bachelor Of Arts (Arabic Education)', 'student_count': 158}, {'long_name': 'BACHELOR OF ARTS (STRATEGIC COMMUNICATION)', 'student_count': 156}, {'long_name': 'MASTER OF PHILOSOPHY (FOOD AND NUTRITION)', 'student_count': 155}, {'long_name': 'MASTER OF PHILOSOPHY (MUSIC EDUCATION)', 'student_count': 154}, {'long_name': 'BACHELOR OF ARTS (RELIGIONS AND MORAL STUDIES EDUCATION)', 'student_count': 153}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management)', 'student_count': 150}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'student_count': 150}, {'long_name': 'Master Of Business Administration (Human Resource Management And Organisational Behaviour ) - Weekend', 'student_count': 149}, {'long_name': 'DIPLOMA (THEATRE ARTS)', 'student_count': 144}, {'long_name': 'DIPLOMA (BASIC EDUCATION)', 'student_count': 143}, {'long_name': 'Bachelor Of Arts (French Education)', 'student_count': 133}, {'long_name': 'Bachelor Of Business Administration (Secretarial Education)', 'student_count': 129}, {'long_name': 'Diploma In Education - TM', 'student_count': 128}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - R', 'student_count': 127}, {'long_name': 'Post Graduate Diploma In Education', 'student_count': 125}, {'long_name': 'MASTER OF EDUCATION (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'student_count': 124}, {'long_name': 'BACHELOR ARTS (GA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 123}, {'long_name': 'DIPLOMA (EARLY GRADE)', 'student_count': 121}, {'long_name': 'Master Of Business Administration (Accounting)', 'student_count': 119}, {'long_name': 'MASTER OF ARTS (ENGLISH LANGUAGE)', 'student_count': 119}, {'long_name': 'MASTER OF EDUCATION (SCIENCE)', 'student_count': 119}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'student_count': 118}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS)', 'student_count': 118}, {'long_name': 'MASTER OF EDUCATION (HOME ECONOMICS)', 'student_count': 115}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education)', 'student_count': 114}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - S', 'student_count': 114}, {'long_name': 'DIPLOMA (TEXTILES AND FASHION)', 'student_count': 114}, {'long_name': 'Bachelor Of Science (Mathematics Education) - M', 'student_count': 112}, {'long_name': 'Master Of Philosophy In Catering And Hospitality', 'student_count': 112}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'student_count': 110}, {'long_name': 'MASTER OF ARTS (STRATEGIC COMMUNICATION)', 'student_count': 108}, {'long_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'student_count': 108}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FRENCH EDUCATION)', 'student_count': 107}, {'long_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'student_count': 106}, {'long_name': 'BACHELOR OF SCIENCE (FAMILY LIFE MANAGEMENT EDUCATION)', 'student_count': 106}, {'long_name': 'DOCTOR OF PHILOSOPHY (ARTS AND CULTURE)', 'student_count': 105}, {'long_name': 'BACHELOR OF ARTS (TWI WITH LINGUISTICS EDUCATION)', 'student_count': 104}, {'long_name': 'BACHELOR OF ARTS (DANGME EDUCATION)', 'student_count': 104}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BUSINESS INFORMATION SYSTEMS)', 'student_count': 104}, {'long_name': 'Post Diploma Bachelor Of Science (Environmental Health And Sanitation Education)', 'student_count': 102}, {'long_name': 'Bachelor Of Arts (Economics Education)', 'student_count': 100}, {'long_name': 'CERTIFICATE (PRE-SCHOOL EDUCATION)', 'student_count': 100}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education) - S', 'student_count': 99}, {'long_name': 'MASTER OF PHILOSOPHY (CURRICULUM & PEDAGOGIC STUDIES)', 'student_count': 99}, {'long_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'student_count': 99}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology Education) - S', 'student_count': 98}, {'long_name': 'DIPLOMA (FRENCH EDUCATION)', 'student_count': 98}, {'long_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'student_count': 97}, {'long_name': 'Bachelor Of Science (Wood Technology Education)', 'student_count': 96}, {'long_name': 'MASTER OF PHILOSOPHY (CHEMISTRY EDUCATION)', 'student_count': 96}, {'long_name': 'Master Of Philosophy In Construction Management', 'student_count': 95}, {'long_name': 'MASTER OF ARTS (GHANAIAN LANGUAGES EDUCATION)', 'student_count': 95}, {'long_name': 'DOCTOR OF PHILOSOPHY (APPLIED LINGUISTICS)', 'student_count': 95}, {'long_name': 'DIPLOMA IN ACCOUNTING', 'student_count': 95}, {'long_name': 'BACHELOR OF SCIENCE (SPORTS COACHING)', 'student_count': 94}, {'long_name': 'BACHELOR OF ARTS (DAGBANI EDUCATION)', 'student_count': 93}, {'long_name': 'BACHELOR OF ART (SISAALI WITH ENGLISH EDUCATION)', 'student_count': 93}, {'long_name': 'Master Of Philosophy In Construction Technology', 'student_count': 90}, {'long_name': 'BACHELOR OF ARTS (DAGAARE EDUCATION)', 'student_count': 90}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (FINANCE)', 'student_count': 89}, {'long_name': 'MASTER OF PHILOSOPHY (ECONOMICS)', 'student_count': 86}, {'long_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'student_count': 85}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (FINANCE)', 'student_count': 84}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT COMMUNICATION)', 'student_count': 84}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH ENGLISH EDUCATION)', 'student_count': 82}, {'long_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'student_count': 82}, {'long_name': 'POST GRADUATE DIPLOMA IN TEACHING AND LEARNING IN HIGHER EDUCATION', 'student_count': 82}, {'long_name': 'EXECUTIVE MASTERS (CONFLICT HUMAN RIGHTS & PEACE STUDIES)', 'student_count': 81}, {'long_name': 'Bachelor Of Science (Automotive Technology Education) - S', 'student_count': 80}, {'long_name': 'Diploma In Environmental Health And Sanitation Education', 'student_count': 79}, {'long_name': 'Master Of Philosophy In Mathematics Education', 'student_count': 79}, {'long_name': 'Diploma In Electrical And Electronics Engineering Technology', 'student_count': 79}, {'long_name': 'MASTER OF EDUCATION (INSTITUTIONAL MENTORSHIP AND SUPERVISION)', 'student_count': 79}, {'long_name': 'MASTER OF PHILOSOPHY (VISUAL COMMUNICATION)', 'student_count': 77}, {'long_name': 'MASTER OF PHILOSOPHY (STRATEGIC COMMUNICATION)', 'student_count': 75}, {'long_name': 'DOCTOR OF PHILOSOPHY (SOCIAL STUDIES)', 'student_count': 75}, {'long_name': 'MASTER OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'student_count': 75}, {'long_name': 'Master Of Science In Information Technology Education', 'student_count': 74}, {'long_name': 'Master Of Technology In Construction Technology', 'student_count': 74}, {'long_name': 'Diploma In Business Administration (Accounting)', 'student_count': 74}, {'long_name': 'Post Diploma Bachelor Of Science In Environmental Health And Sanitation Education - S', 'student_count': 74}, {'long_name': 'BACHELOR OF ART (DANGME WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 74}, {'long_name': 'BACHELOR OF ARTS (EWE WITH LINGUISTICS EDUCATION)', 'student_count': 72}, {'long_name': 'BACHELOR OF ARTS (EWE WITH FRENCH EDUCATION)', 'student_count': 71}, {'long_name': 'BACHELOR OF FINE ART (ANIMATION)', 'student_count': 70}, {'long_name': 'Diploma In Catering And Hospitality', 'student_count': 68}, {'long_name': 'Master Of Philosophy In Educational Leadership', 'student_count': 67}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'student_count': 67}, {'long_name': 'Master Of Philosophy In Environmental And Occupational Health Education', 'student_count': 66}, {'long_name': 'DOCTOR OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'student_count': 66}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles', 'student_count': 65}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'student_count': 65}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - R', 'student_count': 65}, {'long_name': 'Bachelor Of Arts (French With English Education)', 'student_count': 64}, {'long_name': 'Bachelor Of Science (Mathematics Education) - W', 'student_count': 64}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH LINGUISTIC EDUCATION)', 'student_count': 63}, {'long_name': 'DOCTOR OF PHILOSOPHY (EDUCATIONAL LEADERSHIP)', 'student_count': 63}, {'long_name': 'Master Of Philosophy In Accounting', 'student_count': 62}, {'long_name': 'Diploma In Economics', 'student_count': 60}, {'long_name': 'Master Of Philosophy In Mathematics Education - W', 'student_count': 59}, {'long_name': 'BACHELOR OF ARTS (DAGAARE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 59}, {'long_name': 'MASTER OF EDUCATION (GEOGRAPHY)', 'student_count': 59}, {'long_name': 'DIPLOMA IN SPORTS COACHING', 'student_count': 59}, {'long_name': 'Master Of Technology In Construction Management', 'student_count': 58}, {'long_name': 'DOCTOR OF PHILOSOPHY (COMMUNICATION AND MEDIA)', 'student_count': 58}, {'long_name': 'MASTER OF PHILOSOPHY (ASSESSMENT, MEASUREMENT AND EVALUATION)', 'student_count': 57}, {'long_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'student_count': 56}, {'long_name': 'Bachelor Of Science (Wood Technology With Education)', 'student_count': 55}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH LINGUISTICS EDUCATION)', 'student_count': 55}, {'long_name': 'BACHELOR OF EDUCATION (PSYCHOLOGY)', 'student_count': 55}, {'long_name': 'Master Of Philosophy In Catering And Hospitality (Top-Up)', 'student_count': 54}, {'long_name': 'Master Of Philosophy In Business Management', 'student_count': 54}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH LINGUISTICS EDUCATION)', 'student_count': 54}, {'long_name': 'Diploma In Education - TK', 'student_count': 53}, {'long_name': 'DOCTOR OF PHILOSOPHY IN GHANAIAN LANGUAGES', 'student_count': 53}, {'long_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL HEALTH AND SANITATION EDUCATION)', 'student_count': 52}, {'long_name': 'MASTER OF PHILOSOPHY (ACCOUNTING)', 'student_count': 52}, {'long_name': 'Diploma In Education - M', 'student_count': 51}, {'long_name': 'Diploma In Education - CP', 'student_count': 51}, {'long_name': 'Bachelor Of Science (Wood Technology With Education) - S', 'student_count': 51}, {'long_name': 'MASTER OF EDUCATION (FRENCH)', 'student_count': 51}, {'long_name': 'MASTER OF ARTS (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES', 'student_count': 51}, {'long_name': 'MASTER OF EDUCATION (SPECIAL EDUCATION)', 'student_count': 49}, {'long_name': 'Master Of Education In Agriculture', 'student_count': 48}, {'long_name': 'Diploma In Construction Technology', 'student_count': 48}, {'long_name': 'Diploma In Fashion Design And Textiles', 'student_count': 48}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - E', 'student_count': 48}, {'long_name': 'BACHELOR OF ARTS (EWE WITH GERMAN EDUCATION)', 'student_count': 48}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH FRENCH EDUCATION)', 'student_count': 48}, {'long_name': 'BACHELOR OF ARTS (DEVELOPMENT COMMUNICATION)', 'student_count': 48}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (LOGISTICS AND TRANSPORT MANAGEMENT)', 'student_count': 48}, {'long_name': 'Bachelor Of Science (Physics Education)', 'student_count': 47}, {'long_name': 'Master Of Philosophy In Mechanical Engineering Technology', 'student_count': 47}, {'long_name': 'Master Of Philosophy In Construction Management (Top Up)', 'student_count': 47}, {'long_name': 'Bachelor Of Science In Occupational Health And Safety', 'student_count': 47}, {'long_name': 'BACHELOR OF ARTS (TWI WITH GERMAN EDUCATION)', 'student_count': 47}, {'long_name': 'MASTER OF EDUCATION (GUIDANCE AND COUNSELLING)', 'student_count': 47}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 46}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 46}, {'long_name': 'BACHELOR OF ARTS (GA EDUCATION)', 'student_count': 45}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - W', 'student_count': 45}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - W', 'student_count': 45}, {'long_name': 'Master Of Philosophy In Science Education', 'student_count': 42}, {'long_name': 'Master Of Technology Education In Fashion Design And Textile', 'student_count': 42}, {'long_name': 'Master Of Philosophy In Wood Science And Technology', 'student_count': 41}, {'long_name': 'MASTER OF PHILOSOPHY (CLOTHING AND TEXTILES)', 'student_count': 41}, {'long_name': 'MASTER OF PHILOSOPHY (TEXTILES AND FASHION)', 'student_count': 40}, {'long_name': 'BACHELOR OF ART (KUSAAL WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 40}, {'long_name': 'MASTER OF PHILOSOPHY (INTEGRATED SCIENCE EDUCATION)', 'student_count': 40}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH GERMAN EDUCATION)', 'student_count': 39}, {'long_name': 'DOCTOR OF PHILOSOPHY (ENGLISH LANGUAGE)', 'student_count': 39}, {'long_name': 'BACHELOR OF ARTS (GA WITH LINGUISTICS EDUCATION)', 'student_count': 38}, {'long_name': 'Bachelor Of Science (Marketing) - W', 'student_count': 37}, {'long_name': 'Master Of Philosophy In Biology', 'student_count': 37}, {'long_name': 'Master Of Technology In Electrical And Electronics Engineering', 'student_count': 37}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance)', 'student_count': 36}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education)', 'student_count': 36}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH LINGUISTICS EDUCATION)', 'student_count': 36}, {'long_name': 'DOCTOR OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'student_count': 36}, {'long_name': 'Doctor Of Philosophy In Educational Leadership', 'student_count': 35}, {'long_name': 'Bachelor Of Business Administration (Executive Office Administration)', 'student_count': 34}, {'long_name': 'Diploma In Business Administration (Management) - W', 'student_count': 34}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS) BY DISTANCE', 'student_count': 34}, {'long_name': 'MASTER OF PHILOSOPHY (HISTORY EDUCATION)', 'student_count': 34}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH LINGUISTICS EDUCATION)', 'student_count': 33}, {'long_name': 'DOCTOR OF PHILOSOPHY (SPECIAL EDUCATION)', 'student_count': 33}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGMENT & ORGANIZATION BEHAVIOUR)', 'student_count': 33}, {'long_name': 'MASTER OF EDUCATION IN SCHOOL SUPERVISION AND QUALITY ASSURANCE)', 'student_count': 33}, {'long_name': 'Master Of Philosophy In Crop Science', 'student_count': 32}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education) - S', 'student_count': 32}, {'long_name': 'Master Of Philosophy In Business Management - W', 'student_count': 32}, {'long_name': 'MASTER OF ARTS (COMMUNICATION INSTRUCTION)', 'student_count': 32}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 31}, {'long_name': 'BACHELOR OF ARTS (NZEMA EDUCATION)', 'student_count': 31}, {'long_name': 'MASTER OF PHILOSOPHY (JOURNALISM AND MEDIA ST)', 'student_count': 31}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RESOURCE MANAGEMENT)', 'student_count': 31}, {'long_name': 'Master Of Philosophy In Agronomy', 'student_count': 30}, {'long_name': 'Diploma In Education (Competency Based Training - CBT)', 'student_count': 30}, {'long_name': 'MASTER OF EDUCATION (FOOD AND NUTRITION)', 'student_count': 30}, {'long_name': 'Master Of Education In Mathematics Education', 'student_count': 29}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles (Top-Up)', 'student_count': 29}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship) - W', 'student_count': 29}, {'long_name': 'BACHELOR OF ARTS (GONJA EDUCATION)', 'student_count': 29}, {'long_name': 'MASTER OF PHILOSOPHY (ARTS AND CULTURE)', 'student_count': 29}, {'long_name': 'MASTER OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'student_count': 29}, {'long_name': 'MASTER OF EDUCATION (ENGLISH LANGUAGE) BY DISTANCE', 'student_count': 29}, {'long_name': 'Bachelor Of Science (Mathematics Education) - S', 'student_count': 28}, {'long_name': 'Bachelor Of Arts (English Language Education) - M', 'student_count': 28}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'student_count': 28}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 28}, {'long_name': 'MASTER OF PHILOSOPHY (COMMUNICATION INSTRUCTION)', 'student_count': 28}, {'long_name': 'Master Of Business Administration (Finance)', 'student_count': 27}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FANTE EDUCATION)', 'student_count': 27}, {'long_name': 'Diploma In Education (Junior High)', 'student_count': 26}, {'long_name': 'MASTER OF PHILOSOPHY (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 26}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH GERMAN EDUCATION)', 'student_count': 26}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 26}, {'long_name': 'DOCTOR OF PHILOSOPHY (FRENCH)', 'student_count': 26}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology Education)', 'student_count': 25}, {'long_name': 'Master Of Science In Information Technology Education - W', 'student_count': 25}, {'long_name': 'DOCTOR OF PHILOSOPHY (MUSIC EDUCATION)', 'student_count': 25}, {'long_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL SCIENCE EDUCATION)', 'student_count': 25}, {'long_name': 'MASTER OF ARTS (DEVELOPMENT COMMUNICATION)', 'student_count': 25}, {'long_name': 'MASTER OF SCIENCE (ECONOMICS EDUCATION)', 'student_count': 25}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - E', 'student_count': 24}, {'long_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'student_count': 24}, {'long_name': 'BACHELOR OF SCIENCE (INSURANCE AND RISK MANAGEMENT)', 'student_count': 24}, {'long_name': 'Master Of Philosophy In Construction Technology (Top-Up)', 'student_count': 23}, {'long_name': 'Master Of Technology In Mechanical Technology', 'student_count': 23}, {'long_name': 'Master Of Education In Science Education', 'student_count': 23}, {'long_name': 'Bachelor Of Business Administration (Management) - R', 'student_count': 23}, {'long_name': 'MASTER OF EDUCATION (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'student_count': 23}, {'long_name': 'Master Of Philosophy In Automotive Engineering Technology', 'student_count': 22}, {'long_name': 'Diploma In Automotive Engineering Technology', 'student_count': 22}, {'long_name': 'Bachelor Of Science (Civil Engineering)', 'student_count': 22}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH LINGUISTICS EDUCATION)', 'student_count': 22}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MANAGEMENT INFORMATION SYSTEMS)', 'student_count': 22}, {'long_name': 'MASTER OF PHILOSOPHY (FINANCE)', 'student_count': 22}, {'long_name': 'MASTER OF PHILOSOPHY (ENVIRONMENTAL SCIENCE)', 'student_count': 22}, {'long_name': 'Post Diploma Bachelor Of Science (Management Education)', 'student_count': 21}, {'long_name': 'Master Of Philosophy In Public Health', 'student_count': 21}, {'long_name': 'MASTER OF PHILOSOPHY (THEATRE ARTS)', 'student_count': 21}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 21}, {'long_name': 'BACHELOR OF ARTS (SISAALI EDUCATION)', 'student_count': 21}, {'long_name': 'MASTER OF PHILOSOPHY (FAMILY LIFE MANAGEMENT)', 'student_count': 21}, {'long_name': 'MASTER OF EDUCATION (COMPUTER EDUCATION AND TECHNOLOGY)', 'student_count': 21}, {'long_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education) - S', 'student_count': 20}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up)', 'student_count': 20}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship)', 'student_count': 20}, {'long_name': 'BACHELOR OF ARTS (KASEM EDUCATION)', 'student_count': 20}, {'long_name': 'MASTER OF ARTS (ART EDUCATION)', 'student_count': 20}, {'long_name': 'MASTER OF EDUCATION (SCIENCE EDUCATION)', 'student_count': 20}, {'long_name': 'Master Of Philosophy In Educational Leadership (Top-Up)', 'student_count': 19}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - W', 'student_count': 19}, {'long_name': 'BACHELOR OF ARTS (KUSAAL EDUCATION)', 'student_count': 19}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH LINGUISTICS EDUCATION)', 'student_count': 19}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MARKETING)', 'student_count': 19}, {'long_name': 'MASTER OF SCIENCE (DEVELOPMENT FINANCE)', 'student_count': 19}, {'long_name': 'Master Of Philosophy In Chemistry Education', 'student_count': 18}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'student_count': 18}, {'long_name': 'Bachelor Of Arts (Arabic With English Education)', 'student_count': 18}, {'long_name': 'Master Of Technology In Automotive Engineering Technology', 'student_count': 18}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH LINGUISTICS EDUCATION)', 'student_count': 18}, {'long_name': 'MASTER OF EDUCATION (POLITICAL SCIENCE EDUCATION)', 'student_count': 18}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICS EDUCATION)', 'student_count': 18}, {'long_name': 'Bachelor Of Science (Mechanical Engineering Technology Education)', 'student_count': 18}, {'long_name': 'Doctor Of Philosophy In Construction Management', 'student_count': 17}, {'long_name': 'Master Of Philosophy In Mechanical Engineering Technology (Top-Up)', 'student_count': 17}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH LINGUISTICS EDUCATION)', 'student_count': 17}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH LINGUISTICS EDUCATION)', 'student_count': 17}, {'long_name': 'BACHELOR OF ARTS (GURUNE EDUCATION)', 'student_count': 17}, {'long_name': 'Master Of Philosophy In Mathematics Education (Top-Up) - W', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering (Top-Up)', 'student_count': 16}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'student_count': 16}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - W', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Accounting - W', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Information Technology', 'student_count': 16}, {'long_name': 'Master Of Business Administration (Marketing)', 'student_count': 15}, {'long_name': 'Master Of Philosophy In Animal Science', 'student_count': 15}, {'long_name': 'Diploma In Mechanical Engineering Technology', 'student_count': 15}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - W', 'student_count': 15}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education) - S', 'student_count': 15}, {'long_name': 'Diploma In Architecture And Digital Construction', 'student_count': 15}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH GERMAN EDUCATION)', 'student_count': 15}, {'long_name': 'BACHELOR OF ARTS (TWI WITH FRENCH EDUCATION)', 'student_count': 15}, {'long_name': 'MASTER OF PHILOSOPHY (ENTREPRENEURSHIP AND INNOVATION)', 'student_count': 15}, {'long_name': 'DIPLOMA (EDUCATION)', 'student_count': 15}, {'long_name': 'Doctor Of Philosophy In Construction Technology', 'student_count': 14}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology Education)', 'student_count': 14}, {'long_name': 'Bachelor Of Science (Plumbing, And Gas Technology)', 'student_count': 14}, {'long_name': 'MASTER OF EDUCATION (BIOLOGY)', 'student_count': 14}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - E', 'student_count': 14}, {'long_name': 'MASTER OF PHILOSOPHY (INSTRUCTIONAL DESIGN AND TECHNOLOGY)', 'student_count': 14}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering', 'student_count': 13}, {'long_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'student_count': 13}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up)', 'student_count': 13}, {'long_name': 'Bachelor Of Science (Agriculture Engineering, Technology And Innovation Education)', 'student_count': 13}, {'long_name': 'MASTER OF ARTS (HISTORY EDUCATION)', 'student_count': 13}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology)', 'student_count': 12}, {'long_name': 'Bachelor Of Science (Computerised Accounting)', 'student_count': 12}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education)', 'student_count': 12}, {'long_name': 'Bachelor Of Science (Automotive Engineering Technology Education)', 'student_count': 12}, {'long_name': 'Master Of Technology In Wood Technology', 'student_count': 11}, {'long_name': 'Doctor Of Philosophy In Wood Science And Technology', 'student_count': 11}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting)', 'student_count': 11}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT FINANCE)', 'student_count': 11}, {'long_name': 'Master Of Philosophy In Soil Science', 'student_count': 10}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education)', 'student_count': 10}, {'long_name': 'Bachelor Of Science (Marketing) - E', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL EDUCATION)', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (GA WITH GERMAN EDUCATION)', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS EDUCATION)', 'student_count': 10}, {'long_name': 'Master Of Philosophy In Information Technology - W', 'student_count': 10}, {'long_name': 'Doctor Of Philosophy In Mathematics Education', 'student_count': 9}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology Education) - S', 'student_count': 9}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up) - W', 'student_count': 9}, {'long_name': 'Post Diploma Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'student_count': 9}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH FRENCH EDUCATION)', 'student_count': 9}, {'long_name': 'BACHELOR OF ARTS (GA WITH FRENCH EDUCATION)', 'student_count': 9}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH LINGUISTICS EDUCATION)', 'student_count': 9}, {'long_name': 'Master Of Philosophy In Information Technology - W (Top-Up)', 'student_count': 9}, {'long_name': 'Master Of Philosophy In Biology Education', 'student_count': 8}, {'long_name': 'Master Of Philosophy In Wood Science And Technology (Top-Up)', 'student_count': 8}, {'long_name': 'Master Of Philosophy In Teaching And Learning', 'student_count': 8}, {'long_name': 'Diploma In Business Administration (Accounting) - W', 'student_count': 8}, {'long_name': 'MASTER OF SCIENCE (BIOLOGY)', 'student_count': 8}, {'long_name': 'Bachelor Of Science (Marketing) - R', 'student_count': 8}, {'long_name': 'Bachelor Of Science In Administration (Business Information Systems)', 'student_count': 7}, {'long_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education)', 'student_count': 7}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - E', 'student_count': 7}, {'long_name': 'Post Diploama Bachelor Of Science (Computerised Accounting)', 'student_count': 7}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH FRENCH EDUCATION)', 'student_count': 7}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND DAGAARE EDUCATION)', 'student_count': 7}, {'long_name': 'Master Of Philosophy In Crop Science (Top-Up)', 'student_count': 7}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH FRENCH EDUCATION)', 'student_count': 7}, {'long_name': 'Master Of Philosophy In Automotive Engineering Technology (Top-Up)', 'student_count': 6}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH FRENCH EDUCATION)', 'student_count': 6}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH FRENCH EDUCATION)', 'student_count': 6}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - E', 'student_count': 6}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology Education)', 'student_count': 5}, {'long_name': 'Doctor Of Philosophy In Crop Science', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology Education) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Agricultural Science Education)', 'student_count': 5}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH FRENCH EDUCATION)', 'student_count': 5}, {'long_name': 'BACHELOR OF ART (SISAALI EDUCATION WITH FRENCH EDUCATION)', 'student_count': 5}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND DANGME EDUCATION)', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing And Entrepreneurship) - W', 'student_count': 5}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education) -S', 'student_count': 5}, {'long_name': 'Diploma In Management Education', 'student_count': 4}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - W', 'student_count': 4}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH GERMAN EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH GERMAN EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GA EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL NEEDS EDUCATION)', 'student_count': 4}, {'long_name': 'MASTER OF EDUCATION (ENGLISH EDUCATION)', 'student_count': 4}, {'long_name': 'MASTER OF PHILOSOPHY (HOME ECONOMICS)', 'student_count': 4}, {'long_name': 'Bachelor Of Arts (English With French Education)', 'student_count': 3}, {'long_name': 'Master Of Philosophy In Plant Pathology', 'student_count': 3}, {'long_name': 'Master Of Philosophy In Agronomy (Top-up)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) -- R', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - R', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W (Top-Up)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S (Top-Up)', 'student_count': 3}, {'long_name': 'MASTER OF FINE ARTS (THEATRE ARTS)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH FRENCH EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ART (LINGUISTIC AND EWE EDUCATION)', 'student_count': 3}, {'long_name': 'MASTER OF ARTS (FRENCH TRANSLATION)', 'student_count': 3}, {'long_name': 'DOCTOR OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education)', 'student_count': 3}, {'long_name': 'Doctor Of Philosophy In Animal Science', 'student_count': 2}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Executive Office Administration)', 'student_count': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education)', 'student_count': 2}, {'long_name': 'Doctor Of Philosophy In Soil Fertility And Plant Nutrition', 'student_count': 2}, {'long_name': 'MASTER OF ARTS (THEATRE ARTS)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH GERMAN EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH FRENCH EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH GERMAN EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH FRENCH EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GONJA EDUCATION)', 'student_count': 2}, {'long_name': 'MASTER OF PHILOSOPHY COMMUNICATION AND MEDIA STUDIES   (COMMUNICATION SKILLS)', 'student_count': 2}, {'long_name': 'MASTER OF ARTS (ARTS AND CULTURE)', 'student_count': 2}, {'long_name': 'Bachelor Of Science (Welding And Fabrication Technology Education', 'student_count': 2}, {'long_name': 'MASTER OF PHILOSOPHY IN AGRICULTURAL EXTENSION AND RURAL ENTREPRENEURSHIP', 'student_count': 2}, {'long_name': 'B.SC. DOC', 'student_count': 2}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Business Information Systems)', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Banking And Finance) - W', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S', 'student_count': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND KASEM EDUCATION)', 'student_count': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND NZEMA EDUCATION)', 'student_count': 1}, {'long_name': 'BACHELOR OF ARTS (GERMAN EDUCATION)', 'student_count': 1}, {'long_name': 'MASTER OF PHILOSOPHY (ASSESSMENT IN SPECIAL EDUCATION)', 'student_count': 1}, {'long_name': 'DIPLOMA (SIGN LANGUAGE)', 'student_count': 1}, {'long_name': 'MASTER OF EDUCATION (ASSESSMENT IN SPECIAL EDUCATION)', 'student_count': 1}, {'long_name': 'Master Of Philosphy In Water And Environmental Management', 'student_count': 1}, {'long_name': 'Diploma In Welding And Fabrication Engineering Technology', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Education (Early Grade) - K', 'student_count': 1}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - E', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education)', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education) - S', 'student_count': 1}, {'long_name': 'DOCTOR OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'student_count': 1}, {'long_name': 'DIPLOMA IN BASIC EDUCATION', 'student_count': 1}, {'long_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'student_count': 1}]
2025-08-08 11:47:41,436 - root - INFO - [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]
2025-08-08 11:47:41,436 - root - INFO - 'No results'
2025-08-08 11:47:41,436 - root - INFO - 'No results'
2025-08-08 11:47:41,436 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 11:47:49,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:49,834 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 11:47:58,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:47:58,543 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:47:58,543 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:47:58,543 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:47:58,544 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total number of students enrolled at the institution with the most students?...
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total number of students enrolled at the institution with the most students is 192,627....
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'total_students': 192627}]...
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_students_at_largest_institution
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 11:47:58,544 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 11:47:58,544 - celery.redirected - WARNING - [{'total_students': 192627}]
2025-08-08 11:47:58,544 - celery.redirected - WARNING - ================================= 
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 11:47:58,544 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:47:58,544 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:47:58,544 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:47:58,544 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:47:58,545 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:47:58,545 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:47:58,545 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:47:58,545 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the student population of the institution with the most students compare to other instituti...
2025-08-08 11:47:58,545 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the most students is ITC University, which has a total student population of 19...
2025-08-08 11:47:58,545 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: graph
2025-08-08 11:47:58,545 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_name': 'ITC University', 'student_count': 49153, 'comparison': 'Other'}, {'institutio...
2025-08-08 11:47:58,545 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_population_comparison
2025-08-08 11:47:58,545 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 11:47:58,545 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 11:47:58,545 - celery.redirected - WARNING - [{'institution_name': 'ITC University', 'student_count': 49153, 'comparison': 'Other'}, {'institution_name': 'Akrofi-Christaller Institute of Theology Mission and Culture', 'student_count': 114, 'comparison': 'Other'}, {'institution_name': 'Accra Medical', 'student_count': 13012, 'comparison': 'Other'}, {'institution_name': 'Ghana School of Law', 'student_count': 262, 'comparison': 'Other'}, {'institution_name': 'Koforidua Technical University', 'student_count': 17758, 'comparison': 'Other'}, {'institution_name': 'Presbyterian University College Ghana', 'student_count': 32094, 'comparison': 'Other'}, {'institution_name': 'Methodist University College Ghana', 'student_count': 1, 'comparison': 'Other'}, {'institution_name': 'Wisconsin International University College', 'student_count': 1202, 'comparison': 'Other'}, {'institution_name': 'ITC University', 'student_count': 192627, 'comparison': 'Most Students'}, {'institution_name': 'Kofi Annan International Peacekeeping Training Centre', 'student_count': 52, 'comparison': 'Other'}, {'institution_name': 'Klintaps University College of Health and Allied Sciences', 'student_count': 1135, 'comparison': 'Other'}]
2025-08-08 11:47:58,545 - celery.redirected - WARNING - ================================= 
2025-08-08 11:47:58,545 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_population_comparison
2025-08-08 11:47:58,545 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 11:47:58,545 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:47:58,545 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:47:58,546 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:47:58,546 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:47:58,546 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:47:58,546 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:47:58,546 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:47:58,546 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:47:58,546 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are most popular at the institution with the most students?...
2025-08-08 11:47:58,546 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The most popular programs at the institution with the highest student enrollment are as follows: 1) ...
2025-08-08 11:47:58,546 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-08 11:47:58,547 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'student_count': 15977}, {'long_name': ...
2025-08-08 11:47:58,547 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: popular_courses_by_student_count
2025-08-08 11:47:58,547 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 11:47:58,547 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 11:47:58,547 - celery.redirected - WARNING - [{'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'student_count': 15977}, {'long_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'student_count': 10732}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'student_count': 8905}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE)', 'student_count': 7606}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY)', 'student_count': 6649}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'student_count': 6064}, {'long_name': 'BACHELOR OF ARTS (POLITICAL SCIENCE EDUCATION)', 'student_count': 5305}, {'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'student_count': 5241}, {'long_name': 'Bachelor Of Science (Information Technology Education)', 'student_count': 4481}, {'long_name': 'BACHELOR OF ART (SOCIAL STUDIES EDUCATION)', 'student_count': 4255}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'student_count': 3972}, {'long_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'student_count': 3330}, {'long_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'student_count': 3189}, {'long_name': 'BACHELOR OF ARTS (SOCIAL STUDIES EDUCATION)', 'student_count': 2989}, {'long_name': 'BACHELOR OF ARTS (GEOGRAPHY EDUCATION)', 'student_count': 2947}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY)', 'student_count': 2804}, {'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'student_count': 2709}, {'long_name': 'BACHELOR OF ARTS (ART EDUCATION)', 'student_count': 2503}, {'long_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'student_count': 2381}, {'long_name': 'DIPLOMA IN EDUCATION', 'student_count': 2348}, {'long_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'student_count': 2235}, {'long_name': 'Bachelor Of Science (Management Education)', 'student_count': 2181}, {'long_name': 'Bachelor Of Science (Mathematics Education)', 'student_count': 2166}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS EDUCATION)', 'student_count': 2053}, {'long_name': 'Bachelor Of Arts (English Language Education)', 'student_count': 2014}, {'long_name': 'Bachelor Of Science (Accounting Education)', 'student_count': 1999}, {'long_name': 'DIPLOMA IN EARLY GRADE', 'student_count': 1994}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'student_count': 1854}, {'long_name': 'BACHELOR OF ARTS (ECONOMICS EDUCATION)', 'student_count': 1821}, {'long_name': 'BACHELOR OF ARTS (ENGLISH LANGUAGE EDUCATION)', 'student_count': 1812}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICAL EDUCATION)', 'student_count': 1678}, {'long_name': 'Bachelor Of Science (Agricultural Science Education)', 'student_count': 1639}, {'long_name': 'DIPLOMA IN BASIC EDUCATION', 'student_count': 1579}, {'long_name': 'BACHELOR OF ARTS (GRAPHIC DESIGN)', 'student_count': 1558}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - R', 'student_count': 1508}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'student_count': 1448}, {'long_name': 'BACHELOR OF ARTS (FRENCH EDUCATION)', 'student_count': 1441}, {'long_name': 'BACHELOR OF ARTS (EWE EDUCATION)', 'student_count': 1397}, {'long_name': 'BACHELOR OF SCIENCE (FOOD AND NUTRITION EDUCATION)', 'student_count': 1359}, {'long_name': 'BACHELOR OF ARTS (ENGLISH EDUCATION)', 'student_count': 1358}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS EDUCATION)', 'student_count': 1273}, {'long_name': 'Bachelor Of Science (Biological Sciences Education)', 'student_count': 1193}, {'long_name': 'BACHELOR OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'student_count': 1173}, {'long_name': 'Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'student_count': 1171}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - R', 'student_count': 1160}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - S', 'student_count': 1137}, {'long_name': 'Bachelor Of Science (Environmental Health And Sanitation Education)', 'student_count': 1123}, {'long_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'student_count': 1113}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'student_count': 1089}, {'long_name': 'Bachelor Of Science (Integrated Science Education)', 'student_count': 1056}, {'long_name': 'BACHELOR OF ARTS (TWI EDUCATION)', 'student_count': 1051}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION AND MANAGEMENT) BY DISTANCE', 'student_count': 1029}, {'long_name': 'BACHELOR OF ARTS (HISTORY EDUCATION)', 'student_count': 989}, {'long_name': 'BACHELOR OF SCIENCE (HEALTH ADMINISTRATION EDUCATION)', 'student_count': 942}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL EDUCATION)', 'student_count': 929}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED SCIENCE EDUCATION)', 'student_count': 918}, {'long_name': 'Bachelor Of Arts (Social Studies With Economics Education)', 'student_count': 880}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED HOME ECONOMICS EDUCATION)', 'student_count': 870}, {'long_name': 'BACHELOR OF EDUCATION (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'student_count': 858}, {'long_name': 'Bachelor Of Education (Early Grade) - M', 'student_count': 816}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - R', 'student_count': 811}, {'long_name': 'BACHELOR OF ARTS (MUSIC EDUCATION)', 'student_count': 751}, {'long_name': 'Bachelor Of Education (Junior High)', 'student_count': 707}, {'long_name': 'BACHELOR OF SCIENCE (BIOLOGY EDUCATION)', 'student_count': 704}, {'long_name': 'DIPLOMA (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'student_count': 690}, {'long_name': 'BACHELOR OF ARTS (FASHION DESIGN AND TEXTILE EDUCATION)', 'student_count': 684}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 649}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - S', 'student_count': 627}, {'long_name': 'BACHELOR OF SCIENCE (ACCOUNTING EDUCATION)', 'student_count': 578}, {'long_name': 'BACHELOR OF SCIENCE (MANAGEMENT EDUCATION)', 'student_count': 569}, {'long_name': 'Master Of Arts In Educational Leadership', 'student_count': 542}, {'long_name': 'MASTER OF PHILOSOPHY (GHANAIAN LANGUAGE STUDIES)', 'student_count': 517}, {'long_name': 'Diploma In Education - KS', 'student_count': 509}, {'long_name': 'BACHELOR OF SCIENCE (HOME ECONOMICS EDUCATION)', 'student_count': 501}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'student_count': 481}, {'long_name': 'Diploma In Education', 'student_count': 473}, {'long_name': 'BACHELOR OF ARTS (TWI WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 466}, {'long_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'student_count': 444}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'student_count': 444}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'student_count': 439}, {'long_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'student_count': 427}, {'long_name': 'MASTER OF PHILOSOPHY (ENGLISH LANGUAGE)', 'student_count': 400}, {'long_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'student_count': 392}, {'long_name': 'MASTER OF PHILOSOPHY (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'student_count': 379}, {'long_name': 'BACHELOR  OF ARTS (EWE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 366}, {'long_name': 'MASTER OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'student_count': 364}, {'long_name': 'Bachelor Of Arts (Economics With Social Studies Education)', 'student_count': 351}, {'long_name': 'BACHELOR OF MUSIC', 'student_count': 351}, {'long_name': 'BACHELOR OF SCIENCE (AGRICULTURAL SCIENCE EDUCATION)', 'student_count': 349}, {'long_name': 'Bachelor Of Business Administration (Management) - E', 'student_count': 348}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICS EDUCATION)', 'student_count': 346}, {'long_name': 'MASTER OF PHILOSOPHY (SCIENCE EDUCATION)', 'student_count': 342}, {'long_name': 'MASTER OF PHILOSOPHY (EARLY CHILDHOOD EDUCATION)', 'student_count': 340}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND ENGLISH EDUCATION)', 'student_count': 339}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 325}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY) BY DISTANCE', 'student_count': 323}, {'long_name': 'BACHELOR OF ARTS (FANTE EDUCATION)', 'student_count': 316}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - S', 'student_count': 315}, {'long_name': 'Bachelor Of Science (Information Technology Education) - W', 'student_count': 310}, {'long_name': 'Diploma In Business Administration (Management)', 'student_count': 309}, {'long_name': 'MASTER OF PHILOSOPHY (ART EDUCATION)', 'student_count': 308}, {'long_name': 'BACHELOR OF ARTS (COMMUNICATION STUDIES)', 'student_count': 306}, {'long_name': 'DIPLOMA (GRAPHIC DESIGN)', 'student_count': 298}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (MARKETING AND ENTREPRENEURSHIP)', 'student_count': 296}, {'long_name': 'DIPLOMA (ART)', 'student_count': 295}, {'long_name': 'Bachelor Of Science (Agribusiness Management And Entrepreneurship Education)', 'student_count': 291}, {'long_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'student_count': 283}, {'long_name': 'MASTER OF PHILOSOPHY IN SOCIAL STUDIES', 'student_count': 283}, {'long_name': 'BACHELOR OF ARTS (THEATRE ARTS)', 'student_count': 269}, {'long_name': 'MASTER OF PHILOSOPHY (SPECIAL EDUCATION)', 'student_count': 265}, {'long_name': 'MASTER OF PHILOSOPHY (BASIC EDUCATION)', 'student_count': 254}, {'long_name': 'DIPLOMA IN MANAGEMENT STUDIES', 'student_count': 251}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES)', 'student_count': 243}, {'long_name': 'Bachelor Of Science (Chemistry Education)', 'student_count': 236}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'student_count': 232}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS WITH ECONOMICS EDUCATION)', 'student_count': 226}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - W', 'student_count': 224}, {'long_name': 'DIPLOMA IN MUSIC', 'student_count': 208}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education) - S', 'student_count': 207}, {'long_name': 'MASTER OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'student_count': 202}, {'long_name': 'Bachelor Of Science (Information Technology)', 'student_count': 193}, {'long_name': 'Bachelor Of Science (Natural Resources Management And Education)', 'student_count': 192}, {'long_name': 'Bachelor Of Science (Wood Technology Education) - S', 'student_count': 192}, {'long_name': 'MASTER OF PHILOSOPHY (FRENCH)', 'student_count': 189}, {'long_name': 'MASTER OF EDUCATION (EARLY CHILDHOOD EDUCATION)', 'student_count': 188}, {'long_name': 'DOCTOR OF PHILOSOPHY (SCIENCE EDUCATION)', 'student_count': 187}, {'long_name': 'BACHELOR OF ARTS (CREATIVE ARTS AND DESIGN EDUCATION)', 'student_count': 182}, {'long_name': 'MASTER OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'student_count': 178}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'student_count': 175}, {'long_name': 'Master Of Technology Education In Catering And Hospitality', 'student_count': 175}, {'long_name': 'Bachelor Of Science In Administration (Accounting)', 'student_count': 173}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND TWI EDUCATION)', 'student_count': 171}, {'long_name': 'MASTER OF PHILOSOPHY (INFORMATION AND COMMUNICATION TECHNOLOGY)', 'student_count': 169}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'student_count': 167}, {'long_name': 'MASTER OF PHILOSOPHY (POLITICAL SCIENCE EDUCATION)', 'student_count': 167}, {'long_name': 'Bachelor Of Education (Early Grade) - K', 'student_count': 166}, {'long_name': 'Bachelor Of Education (Upper Primary)', 'student_count': 165}, {'long_name': 'Bachelor Of Business Administration (Management) - W', 'student_count': 163}, {'long_name': 'Bachelor Of Arts (Arabic Education)', 'student_count': 158}, {'long_name': 'BACHELOR OF ARTS (STRATEGIC COMMUNICATION)', 'student_count': 156}, {'long_name': 'MASTER OF PHILOSOPHY (FOOD AND NUTRITION)', 'student_count': 155}, {'long_name': 'MASTER OF PHILOSOPHY (MUSIC EDUCATION)', 'student_count': 154}, {'long_name': 'BACHELOR OF ARTS (RELIGIONS AND MORAL STUDIES EDUCATION)', 'student_count': 153}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management)', 'student_count': 150}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'student_count': 150}, {'long_name': 'Master Of Business Administration (Human Resource Management And Organisational Behaviour ) - Weekend', 'student_count': 149}, {'long_name': 'DIPLOMA (THEATRE ARTS)', 'student_count': 144}, {'long_name': 'DIPLOMA (BASIC EDUCATION)', 'student_count': 143}, {'long_name': 'Bachelor Of Arts (French Education)', 'student_count': 133}, {'long_name': 'Bachelor Of Business Administration (Secretarial Education)', 'student_count': 129}, {'long_name': 'Diploma In Education - TM', 'student_count': 128}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - R', 'student_count': 127}, {'long_name': 'Post Graduate Diploma In Education', 'student_count': 125}, {'long_name': 'MASTER OF EDUCATION (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'student_count': 124}, {'long_name': 'BACHELOR ARTS (GA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 123}, {'long_name': 'DIPLOMA (EARLY GRADE)', 'student_count': 121}, {'long_name': 'Master Of Business Administration (Accounting)', 'student_count': 119}, {'long_name': 'MASTER OF ARTS (ENGLISH LANGUAGE)', 'student_count': 119}, {'long_name': 'MASTER OF EDUCATION (SCIENCE)', 'student_count': 119}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'student_count': 118}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS)', 'student_count': 118}, {'long_name': 'MASTER OF EDUCATION (HOME ECONOMICS)', 'student_count': 115}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education)', 'student_count': 114}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - S', 'student_count': 114}, {'long_name': 'DIPLOMA (TEXTILES AND FASHION)', 'student_count': 114}, {'long_name': 'Bachelor Of Science (Mathematics Education) - M', 'student_count': 112}, {'long_name': 'Master Of Philosophy In Catering And Hospitality', 'student_count': 112}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'student_count': 110}, {'long_name': 'MASTER OF ARTS (STRATEGIC COMMUNICATION)', 'student_count': 108}, {'long_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'student_count': 108}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FRENCH EDUCATION)', 'student_count': 107}, {'long_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'student_count': 106}, {'long_name': 'BACHELOR OF SCIENCE (FAMILY LIFE MANAGEMENT EDUCATION)', 'student_count': 106}, {'long_name': 'DOCTOR OF PHILOSOPHY (ARTS AND CULTURE)', 'student_count': 105}, {'long_name': 'BACHELOR OF ARTS (TWI WITH LINGUISTICS EDUCATION)', 'student_count': 104}, {'long_name': 'BACHELOR OF ARTS (DANGME EDUCATION)', 'student_count': 104}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BUSINESS INFORMATION SYSTEMS)', 'student_count': 104}, {'long_name': 'Post Diploma Bachelor Of Science (Environmental Health And Sanitation Education)', 'student_count': 102}, {'long_name': 'Bachelor Of Arts (Economics Education)', 'student_count': 100}, {'long_name': 'CERTIFICATE (PRE-SCHOOL EDUCATION)', 'student_count': 100}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education) - S', 'student_count': 99}, {'long_name': 'MASTER OF PHILOSOPHY (CURRICULUM & PEDAGOGIC STUDIES)', 'student_count': 99}, {'long_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'student_count': 99}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology Education) - S', 'student_count': 98}, {'long_name': 'DIPLOMA (FRENCH EDUCATION)', 'student_count': 98}, {'long_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'student_count': 97}, {'long_name': 'Bachelor Of Science (Wood Technology Education)', 'student_count': 96}, {'long_name': 'MASTER OF PHILOSOPHY (CHEMISTRY EDUCATION)', 'student_count': 96}, {'long_name': 'Master Of Philosophy In Construction Management', 'student_count': 95}, {'long_name': 'MASTER OF ARTS (GHANAIAN LANGUAGES EDUCATION)', 'student_count': 95}, {'long_name': 'DOCTOR OF PHILOSOPHY (APPLIED LINGUISTICS)', 'student_count': 95}, {'long_name': 'DIPLOMA IN ACCOUNTING', 'student_count': 95}, {'long_name': 'BACHELOR OF SCIENCE (SPORTS COACHING)', 'student_count': 94}, {'long_name': 'BACHELOR OF ARTS (DAGBANI EDUCATION)', 'student_count': 93}, {'long_name': 'BACHELOR OF ART (SISAALI WITH ENGLISH EDUCATION)', 'student_count': 93}, {'long_name': 'Master Of Philosophy In Construction Technology', 'student_count': 90}, {'long_name': 'BACHELOR OF ARTS (DAGAARE EDUCATION)', 'student_count': 90}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (FINANCE)', 'student_count': 89}, {'long_name': 'MASTER OF PHILOSOPHY (ECONOMICS)', 'student_count': 86}, {'long_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'student_count': 85}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (FINANCE)', 'student_count': 84}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT COMMUNICATION)', 'student_count': 84}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH ENGLISH EDUCATION)', 'student_count': 82}, {'long_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'student_count': 82}, {'long_name': 'POST GRADUATE DIPLOMA IN TEACHING AND LEARNING IN HIGHER EDUCATION', 'student_count': 82}, {'long_name': 'EXECUTIVE MASTERS (CONFLICT HUMAN RIGHTS & PEACE STUDIES)', 'student_count': 81}, {'long_name': 'Bachelor Of Science (Automotive Technology Education) - S', 'student_count': 80}, {'long_name': 'Diploma In Environmental Health And Sanitation Education', 'student_count': 79}, {'long_name': 'Master Of Philosophy In Mathematics Education', 'student_count': 79}, {'long_name': 'Diploma In Electrical And Electronics Engineering Technology', 'student_count': 79}, {'long_name': 'MASTER OF EDUCATION (INSTITUTIONAL MENTORSHIP AND SUPERVISION)', 'student_count': 79}, {'long_name': 'MASTER OF PHILOSOPHY (VISUAL COMMUNICATION)', 'student_count': 77}, {'long_name': 'MASTER OF PHILOSOPHY (STRATEGIC COMMUNICATION)', 'student_count': 75}, {'long_name': 'DOCTOR OF PHILOSOPHY (SOCIAL STUDIES)', 'student_count': 75}, {'long_name': 'MASTER OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'student_count': 75}, {'long_name': 'Master Of Science In Information Technology Education', 'student_count': 74}, {'long_name': 'Master Of Technology In Construction Technology', 'student_count': 74}, {'long_name': 'Diploma In Business Administration (Accounting)', 'student_count': 74}, {'long_name': 'Post Diploma Bachelor Of Science In Environmental Health And Sanitation Education - S', 'student_count': 74}, {'long_name': 'BACHELOR OF ART (DANGME WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 74}, {'long_name': 'BACHELOR OF ARTS (EWE WITH LINGUISTICS EDUCATION)', 'student_count': 72}, {'long_name': 'BACHELOR OF ARTS (EWE WITH FRENCH EDUCATION)', 'student_count': 71}, {'long_name': 'BACHELOR OF FINE ART (ANIMATION)', 'student_count': 70}, {'long_name': 'Diploma In Catering And Hospitality', 'student_count': 68}, {'long_name': 'Master Of Philosophy In Educational Leadership', 'student_count': 67}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'student_count': 67}, {'long_name': 'Master Of Philosophy In Environmental And Occupational Health Education', 'student_count': 66}, {'long_name': 'DOCTOR OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'student_count': 66}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles', 'student_count': 65}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'student_count': 65}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - R', 'student_count': 65}, {'long_name': 'Bachelor Of Arts (French With English Education)', 'student_count': 64}, {'long_name': 'Bachelor Of Science (Mathematics Education) - W', 'student_count': 64}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH LINGUISTIC EDUCATION)', 'student_count': 63}, {'long_name': 'DOCTOR OF PHILOSOPHY (EDUCATIONAL LEADERSHIP)', 'student_count': 63}, {'long_name': 'Master Of Philosophy In Accounting', 'student_count': 62}, {'long_name': 'Diploma In Economics', 'student_count': 60}, {'long_name': 'Master Of Philosophy In Mathematics Education - W', 'student_count': 59}, {'long_name': 'BACHELOR OF ARTS (DAGAARE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 59}, {'long_name': 'MASTER OF EDUCATION (GEOGRAPHY)', 'student_count': 59}, {'long_name': 'DIPLOMA IN SPORTS COACHING', 'student_count': 59}, {'long_name': 'Master Of Technology In Construction Management', 'student_count': 58}, {'long_name': 'DOCTOR OF PHILOSOPHY (COMMUNICATION AND MEDIA)', 'student_count': 58}, {'long_name': 'MASTER OF PHILOSOPHY (ASSESSMENT, MEASUREMENT AND EVALUATION)', 'student_count': 57}, {'long_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'student_count': 56}, {'long_name': 'Bachelor Of Science (Wood Technology With Education)', 'student_count': 55}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH LINGUISTICS EDUCATION)', 'student_count': 55}, {'long_name': 'BACHELOR OF EDUCATION (PSYCHOLOGY)', 'student_count': 55}, {'long_name': 'Master Of Philosophy In Catering And Hospitality (Top-Up)', 'student_count': 54}, {'long_name': 'Master Of Philosophy In Business Management', 'student_count': 54}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH LINGUISTICS EDUCATION)', 'student_count': 54}, {'long_name': 'Diploma In Education - TK', 'student_count': 53}, {'long_name': 'DOCTOR OF PHILOSOPHY IN GHANAIAN LANGUAGES', 'student_count': 53}, {'long_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL HEALTH AND SANITATION EDUCATION)', 'student_count': 52}, {'long_name': 'MASTER OF PHILOSOPHY (ACCOUNTING)', 'student_count': 52}, {'long_name': 'Diploma In Education - M', 'student_count': 51}, {'long_name': 'Diploma In Education - CP', 'student_count': 51}, {'long_name': 'Bachelor Of Science (Wood Technology With Education) - S', 'student_count': 51}, {'long_name': 'MASTER OF EDUCATION (FRENCH)', 'student_count': 51}, {'long_name': 'MASTER OF ARTS (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES', 'student_count': 51}, {'long_name': 'MASTER OF EDUCATION (SPECIAL EDUCATION)', 'student_count': 49}, {'long_name': 'Master Of Education In Agriculture', 'student_count': 48}, {'long_name': 'Diploma In Construction Technology', 'student_count': 48}, {'long_name': 'Diploma In Fashion Design And Textiles', 'student_count': 48}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - E', 'student_count': 48}, {'long_name': 'BACHELOR OF ARTS (EWE WITH GERMAN EDUCATION)', 'student_count': 48}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH FRENCH EDUCATION)', 'student_count': 48}, {'long_name': 'BACHELOR OF ARTS (DEVELOPMENT COMMUNICATION)', 'student_count': 48}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (LOGISTICS AND TRANSPORT MANAGEMENT)', 'student_count': 48}, {'long_name': 'Bachelor Of Science (Physics Education)', 'student_count': 47}, {'long_name': 'Master Of Philosophy In Mechanical Engineering Technology', 'student_count': 47}, {'long_name': 'Master Of Philosophy In Construction Management (Top Up)', 'student_count': 47}, {'long_name': 'Bachelor Of Science In Occupational Health And Safety', 'student_count': 47}, {'long_name': 'BACHELOR OF ARTS (TWI WITH GERMAN EDUCATION)', 'student_count': 47}, {'long_name': 'MASTER OF EDUCATION (GUIDANCE AND COUNSELLING)', 'student_count': 47}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 46}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 46}, {'long_name': 'BACHELOR OF ARTS (GA EDUCATION)', 'student_count': 45}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - W', 'student_count': 45}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - W', 'student_count': 45}, {'long_name': 'Master Of Philosophy In Science Education', 'student_count': 42}, {'long_name': 'Master Of Technology Education In Fashion Design And Textile', 'student_count': 42}, {'long_name': 'Master Of Philosophy In Wood Science And Technology', 'student_count': 41}, {'long_name': 'MASTER OF PHILOSOPHY (CLOTHING AND TEXTILES)', 'student_count': 41}, {'long_name': 'MASTER OF PHILOSOPHY (TEXTILES AND FASHION)', 'student_count': 40}, {'long_name': 'BACHELOR OF ART (KUSAAL WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 40}, {'long_name': 'MASTER OF PHILOSOPHY (INTEGRATED SCIENCE EDUCATION)', 'student_count': 40}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH GERMAN EDUCATION)', 'student_count': 39}, {'long_name': 'DOCTOR OF PHILOSOPHY (ENGLISH LANGUAGE)', 'student_count': 39}, {'long_name': 'BACHELOR OF ARTS (GA WITH LINGUISTICS EDUCATION)', 'student_count': 38}, {'long_name': 'Bachelor Of Science (Marketing) - W', 'student_count': 37}, {'long_name': 'Master Of Philosophy In Biology', 'student_count': 37}, {'long_name': 'Master Of Technology In Electrical And Electronics Engineering', 'student_count': 37}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance)', 'student_count': 36}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education)', 'student_count': 36}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH LINGUISTICS EDUCATION)', 'student_count': 36}, {'long_name': 'DOCTOR OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'student_count': 36}, {'long_name': 'Doctor Of Philosophy In Educational Leadership', 'student_count': 35}, {'long_name': 'Bachelor Of Business Administration (Executive Office Administration)', 'student_count': 34}, {'long_name': 'Diploma In Business Administration (Management) - W', 'student_count': 34}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS) BY DISTANCE', 'student_count': 34}, {'long_name': 'MASTER OF PHILOSOPHY (HISTORY EDUCATION)', 'student_count': 34}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH LINGUISTICS EDUCATION)', 'student_count': 33}, {'long_name': 'DOCTOR OF PHILOSOPHY (SPECIAL EDUCATION)', 'student_count': 33}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGMENT & ORGANIZATION BEHAVIOUR)', 'student_count': 33}, {'long_name': 'MASTER OF EDUCATION IN SCHOOL SUPERVISION AND QUALITY ASSURANCE)', 'student_count': 33}, {'long_name': 'Master Of Philosophy In Crop Science', 'student_count': 32}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education) - S', 'student_count': 32}, {'long_name': 'Master Of Philosophy In Business Management - W', 'student_count': 32}, {'long_name': 'MASTER OF ARTS (COMMUNICATION INSTRUCTION)', 'student_count': 32}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 31}, {'long_name': 'BACHELOR OF ARTS (NZEMA EDUCATION)', 'student_count': 31}, {'long_name': 'MASTER OF PHILOSOPHY (JOURNALISM AND MEDIA ST)', 'student_count': 31}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RESOURCE MANAGEMENT)', 'student_count': 31}, {'long_name': 'Master Of Philosophy In Agronomy', 'student_count': 30}, {'long_name': 'Diploma In Education (Competency Based Training - CBT)', 'student_count': 30}, {'long_name': 'MASTER OF EDUCATION (FOOD AND NUTRITION)', 'student_count': 30}, {'long_name': 'Master Of Education In Mathematics Education', 'student_count': 29}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles (Top-Up)', 'student_count': 29}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship) - W', 'student_count': 29}, {'long_name': 'BACHELOR OF ARTS (GONJA EDUCATION)', 'student_count': 29}, {'long_name': 'MASTER OF PHILOSOPHY (ARTS AND CULTURE)', 'student_count': 29}, {'long_name': 'MASTER OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'student_count': 29}, {'long_name': 'MASTER OF EDUCATION (ENGLISH LANGUAGE) BY DISTANCE', 'student_count': 29}, {'long_name': 'Bachelor Of Science (Mathematics Education) - S', 'student_count': 28}, {'long_name': 'Bachelor Of Arts (English Language Education) - M', 'student_count': 28}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'student_count': 28}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 28}, {'long_name': 'MASTER OF PHILOSOPHY (COMMUNICATION INSTRUCTION)', 'student_count': 28}, {'long_name': 'Master Of Business Administration (Finance)', 'student_count': 27}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FANTE EDUCATION)', 'student_count': 27}, {'long_name': 'Diploma In Education (Junior High)', 'student_count': 26}, {'long_name': 'MASTER OF PHILOSOPHY (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 26}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH GERMAN EDUCATION)', 'student_count': 26}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 26}, {'long_name': 'DOCTOR OF PHILOSOPHY (FRENCH)', 'student_count': 26}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology Education)', 'student_count': 25}, {'long_name': 'Master Of Science In Information Technology Education - W', 'student_count': 25}, {'long_name': 'DOCTOR OF PHILOSOPHY (MUSIC EDUCATION)', 'student_count': 25}, {'long_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL SCIENCE EDUCATION)', 'student_count': 25}, {'long_name': 'MASTER OF ARTS (DEVELOPMENT COMMUNICATION)', 'student_count': 25}, {'long_name': 'MASTER OF SCIENCE (ECONOMICS EDUCATION)', 'student_count': 25}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - E', 'student_count': 24}, {'long_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'student_count': 24}, {'long_name': 'BACHELOR OF SCIENCE (INSURANCE AND RISK MANAGEMENT)', 'student_count': 24}, {'long_name': 'Master Of Philosophy In Construction Technology (Top-Up)', 'student_count': 23}, {'long_name': 'Master Of Technology In Mechanical Technology', 'student_count': 23}, {'long_name': 'Master Of Education In Science Education', 'student_count': 23}, {'long_name': 'Bachelor Of Business Administration (Management) - R', 'student_count': 23}, {'long_name': 'MASTER OF EDUCATION (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'student_count': 23}, {'long_name': 'Master Of Philosophy In Automotive Engineering Technology', 'student_count': 22}, {'long_name': 'Diploma In Automotive Engineering Technology', 'student_count': 22}, {'long_name': 'Bachelor Of Science (Civil Engineering)', 'student_count': 22}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH LINGUISTICS EDUCATION)', 'student_count': 22}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MANAGEMENT INFORMATION SYSTEMS)', 'student_count': 22}, {'long_name': 'MASTER OF PHILOSOPHY (FINANCE)', 'student_count': 22}, {'long_name': 'MASTER OF PHILOSOPHY (ENVIRONMENTAL SCIENCE)', 'student_count': 22}, {'long_name': 'Post Diploma Bachelor Of Science (Management Education)', 'student_count': 21}, {'long_name': 'Master Of Philosophy In Public Health', 'student_count': 21}, {'long_name': 'MASTER OF PHILOSOPHY (THEATRE ARTS)', 'student_count': 21}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 21}, {'long_name': 'BACHELOR OF ARTS (SISAALI EDUCATION)', 'student_count': 21}, {'long_name': 'MASTER OF PHILOSOPHY (FAMILY LIFE MANAGEMENT)', 'student_count': 21}, {'long_name': 'MASTER OF EDUCATION (COMPUTER EDUCATION AND TECHNOLOGY)', 'student_count': 21}, {'long_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education) - S', 'student_count': 20}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up)', 'student_count': 20}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship)', 'student_count': 20}, {'long_name': 'BACHELOR OF ARTS (KASEM EDUCATION)', 'student_count': 20}, {'long_name': 'MASTER OF ARTS (ART EDUCATION)', 'student_count': 20}, {'long_name': 'MASTER OF EDUCATION (SCIENCE EDUCATION)', 'student_count': 20}, {'long_name': 'Master Of Philosophy In Educational Leadership (Top-Up)', 'student_count': 19}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - W', 'student_count': 19}, {'long_name': 'BACHELOR OF ARTS (KUSAAL EDUCATION)', 'student_count': 19}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH LINGUISTICS EDUCATION)', 'student_count': 19}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MARKETING)', 'student_count': 19}, {'long_name': 'MASTER OF SCIENCE (DEVELOPMENT FINANCE)', 'student_count': 19}, {'long_name': 'Master Of Philosophy In Chemistry Education', 'student_count': 18}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'student_count': 18}, {'long_name': 'Bachelor Of Arts (Arabic With English Education)', 'student_count': 18}, {'long_name': 'Master Of Technology In Automotive Engineering Technology', 'student_count': 18}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH LINGUISTICS EDUCATION)', 'student_count': 18}, {'long_name': 'MASTER OF EDUCATION (POLITICAL SCIENCE EDUCATION)', 'student_count': 18}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICS EDUCATION)', 'student_count': 18}, {'long_name': 'Bachelor Of Science (Mechanical Engineering Technology Education)', 'student_count': 18}, {'long_name': 'Doctor Of Philosophy In Construction Management', 'student_count': 17}, {'long_name': 'Master Of Philosophy In Mechanical Engineering Technology (Top-Up)', 'student_count': 17}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH LINGUISTICS EDUCATION)', 'student_count': 17}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH LINGUISTICS EDUCATION)', 'student_count': 17}, {'long_name': 'BACHELOR OF ARTS (GURUNE EDUCATION)', 'student_count': 17}, {'long_name': 'Master Of Philosophy In Mathematics Education (Top-Up) - W', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering (Top-Up)', 'student_count': 16}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'student_count': 16}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - W', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Accounting - W', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Information Technology', 'student_count': 16}, {'long_name': 'Master Of Business Administration (Marketing)', 'student_count': 15}, {'long_name': 'Master Of Philosophy In Animal Science', 'student_count': 15}, {'long_name': 'Diploma In Mechanical Engineering Technology', 'student_count': 15}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - W', 'student_count': 15}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education) - S', 'student_count': 15}, {'long_name': 'Diploma In Architecture And Digital Construction', 'student_count': 15}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH GERMAN EDUCATION)', 'student_count': 15}, {'long_name': 'BACHELOR OF ARTS (TWI WITH FRENCH EDUCATION)', 'student_count': 15}, {'long_name': 'MASTER OF PHILOSOPHY (ENTREPRENEURSHIP AND INNOVATION)', 'student_count': 15}, {'long_name': 'DIPLOMA (EDUCATION)', 'student_count': 15}, {'long_name': 'Doctor Of Philosophy In Construction Technology', 'student_count': 14}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology Education)', 'student_count': 14}, {'long_name': 'Bachelor Of Science (Plumbing, And Gas Technology)', 'student_count': 14}, {'long_name': 'MASTER OF EDUCATION (BIOLOGY)', 'student_count': 14}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - E', 'student_count': 14}, {'long_name': 'MASTER OF PHILOSOPHY (INSTRUCTIONAL DESIGN AND TECHNOLOGY)', 'student_count': 14}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering', 'student_count': 13}, {'long_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'student_count': 13}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up)', 'student_count': 13}, {'long_name': 'Bachelor Of Science (Agriculture Engineering, Technology And Innovation Education)', 'student_count': 13}, {'long_name': 'MASTER OF ARTS (HISTORY EDUCATION)', 'student_count': 13}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology)', 'student_count': 12}, {'long_name': 'Bachelor Of Science (Computerised Accounting)', 'student_count': 12}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education)', 'student_count': 12}, {'long_name': 'Bachelor Of Science (Automotive Engineering Technology Education)', 'student_count': 12}, {'long_name': 'Master Of Technology In Wood Technology', 'student_count': 11}, {'long_name': 'Doctor Of Philosophy In Wood Science And Technology', 'student_count': 11}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting)', 'student_count': 11}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT FINANCE)', 'student_count': 11}, {'long_name': 'Master Of Philosophy In Soil Science', 'student_count': 10}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education)', 'student_count': 10}, {'long_name': 'Bachelor Of Science (Marketing) - E', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL EDUCATION)', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (GA WITH GERMAN EDUCATION)', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS EDUCATION)', 'student_count': 10}, {'long_name': 'Master Of Philosophy In Information Technology - W', 'student_count': 10}, {'long_name': 'Doctor Of Philosophy In Mathematics Education', 'student_count': 9}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology Education) - S', 'student_count': 9}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up) - W', 'student_count': 9}, {'long_name': 'Post Diploma Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'student_count': 9}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH FRENCH EDUCATION)', 'student_count': 9}, {'long_name': 'BACHELOR OF ARTS (GA WITH FRENCH EDUCATION)', 'student_count': 9}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH LINGUISTICS EDUCATION)', 'student_count': 9}, {'long_name': 'Master Of Philosophy In Information Technology - W (Top-Up)', 'student_count': 9}, {'long_name': 'Master Of Philosophy In Biology Education', 'student_count': 8}, {'long_name': 'Master Of Philosophy In Wood Science And Technology (Top-Up)', 'student_count': 8}, {'long_name': 'Master Of Philosophy In Teaching And Learning', 'student_count': 8}, {'long_name': 'Diploma In Business Administration (Accounting) - W', 'student_count': 8}, {'long_name': 'MASTER OF SCIENCE (BIOLOGY)', 'student_count': 8}, {'long_name': 'Bachelor Of Science (Marketing) - R', 'student_count': 8}, {'long_name': 'Bachelor Of Science In Administration (Business Information Systems)', 'student_count': 7}, {'long_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education)', 'student_count': 7}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - E', 'student_count': 7}, {'long_name': 'Post Diploama Bachelor Of Science (Computerised Accounting)', 'student_count': 7}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH FRENCH EDUCATION)', 'student_count': 7}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND DAGAARE EDUCATION)', 'student_count': 7}, {'long_name': 'Master Of Philosophy In Crop Science (Top-Up)', 'student_count': 7}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH FRENCH EDUCATION)', 'student_count': 7}, {'long_name': 'Master Of Philosophy In Automotive Engineering Technology (Top-Up)', 'student_count': 6}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH FRENCH EDUCATION)', 'student_count': 6}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH FRENCH EDUCATION)', 'student_count': 6}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - E', 'student_count': 6}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology Education)', 'student_count': 5}, {'long_name': 'Doctor Of Philosophy In Crop Science', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology Education) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Agricultural Science Education)', 'student_count': 5}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH FRENCH EDUCATION)', 'student_count': 5}, {'long_name': 'BACHELOR OF ART (SISAALI EDUCATION WITH FRENCH EDUCATION)', 'student_count': 5}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND DANGME EDUCATION)', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing And Entrepreneurship) - W', 'student_count': 5}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education) -S', 'student_count': 5}, {'long_name': 'Diploma In Management Education', 'student_count': 4}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - W', 'student_count': 4}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH GERMAN EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH GERMAN EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GA EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL NEEDS EDUCATION)', 'student_count': 4}, {'long_name': 'MASTER OF EDUCATION (ENGLISH EDUCATION)', 'student_count': 4}, {'long_name': 'MASTER OF PHILOSOPHY (HOME ECONOMICS)', 'student_count': 4}, {'long_name': 'Bachelor Of Arts (English With French Education)', 'student_count': 3}, {'long_name': 'Master Of Philosophy In Plant Pathology', 'student_count': 3}, {'long_name': 'Master Of Philosophy In Agronomy (Top-up)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) -- R', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - R', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W (Top-Up)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S (Top-Up)', 'student_count': 3}, {'long_name': 'MASTER OF FINE ARTS (THEATRE ARTS)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH FRENCH EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ART (LINGUISTIC AND EWE EDUCATION)', 'student_count': 3}, {'long_name': 'MASTER OF ARTS (FRENCH TRANSLATION)', 'student_count': 3}, {'long_name': 'DOCTOR OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education)', 'student_count': 3}, {'long_name': 'Doctor Of Philosophy In Animal Science', 'student_count': 2}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Executive Office Administration)', 'student_count': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education)', 'student_count': 2}, {'long_name': 'Doctor Of Philosophy In Soil Fertility And Plant Nutrition', 'student_count': 2}, {'long_name': 'MASTER OF ARTS (THEATRE ARTS)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH GERMAN EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH FRENCH EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH GERMAN EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH FRENCH EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GONJA EDUCATION)', 'student_count': 2}, {'long_name': 'MASTER OF PHILOSOPHY COMMUNICATION AND MEDIA STUDIES   (COMMUNICATION SKILLS)', 'student_count': 2}, {'long_name': 'MASTER OF ARTS (ARTS AND CULTURE)', 'student_count': 2}, {'long_name': 'Bachelor Of Science (Welding And Fabrication Technology Education', 'student_count': 2}, {'long_name': 'MASTER OF PHILOSOPHY IN AGRICULTURAL EXTENSION AND RURAL ENTREPRENEURSHIP', 'student_count': 2}, {'long_name': 'B.SC. DOC', 'student_count': 2}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Business Information Systems)', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Banking And Finance) - W', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S', 'student_count': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND KASEM EDUCATION)', 'student_count': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND NZEMA EDUCATION)', 'student_count': 1}, {'long_name': 'BACHELOR OF ARTS (GERMAN EDUCATION)', 'student_count': 1}, {'long_name': 'MASTER OF PHILOSOPHY (ASSESSMENT IN SPECIAL EDUCATION)', 'student_count': 1}, {'long_name': 'DIPLOMA (SIGN LANGUAGE)', 'student_count': 1}, {'long_name': 'MASTER OF EDUCATION (ASSESSMENT IN SPECIAL EDUCATION)', 'student_count': 1}, {'long_name': 'Master Of Philosphy In Water And Environmental Management', 'student_count': 1}, {'long_name': 'Diploma In Welding And Fabrication Engineering Technology', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Education (Early Grade) - K', 'student_count': 1}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - E', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education)', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education) - S', 'student_count': 1}, {'long_name': 'DOCTOR OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'student_count': 1}, {'long_name': 'DIPLOMA IN BASIC EDUCATION', 'student_count': 1}, {'long_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'student_count': 1}]
2025-08-08 11:47:58,551 - celery.redirected - WARNING - ================================= 
2025-08-08 11:47:58,551 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: popular_courses_by_student_count
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 11:47:58,552 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:47:58,552 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:47:58,552 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:47:58,552 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the demographic breakdown of students at the institution with the most students?...
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the most students has a total of 164,622 students, broken down by gender as fol...
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_cou...
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_demographics_by_gender
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 11:47:58,552 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 11:47:58,552 - celery.redirected - WARNING - [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]
2025-08-08 11:47:58,552 - celery.redirected - WARNING - ================================= 
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_demographics_by_gender
2025-08-08 11:47:58,552 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 11:47:58,553 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:47:58,553 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:47:58,553 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:47:58,553 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How has the student enrollment at the institution with the most students changed over the past few y...
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no available results regarding the changes in student enrollment at the in...
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_enrollment_trends_most_populous_institution
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 11:47:58,553 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:47:58,553 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:47:58,553 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:47:58,553 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the retention rate of students at the institution with the most students?...
2025-08-08 11:47:58,553 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no results available for the retention rate of students at the institution...
2025-08-08 11:47:58,554 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:47:58,554 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 11:47:58,554 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: retention_rate_no_data
2025-08-08 11:47:58,554 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 11:47:58,554 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 11:47:58,554 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 6
2025-08-08 11:47:58,554 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:47:58,554 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 11:47:58,554 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:47:58,554 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 6 documents
2025-08-08 11:47:58,554 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 11:47:58,554 - UPSERT_DOCS - INFO -     Content: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:47:58,554 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:16.155966+00:00', 'data_returned': True}
2025-08-08 11:47:58,554 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 11:47:58,554 - UPSERT_DOCS - INFO -     Content: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 11:47:58,554 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:13.019289+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 11:47:58,554 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 11:47:58,554 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:41.429791+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO -     Content: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:06.796514+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO -     Content: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:15.985790+00:00', 'data_returned': False}
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO -     Content: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:07.547970+00:00', 'data_returned': False}
2025-08-08 11:47:58,555 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 4/6
2025-08-08 11:47:58,746 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.191s]
2025-08-08 11:47:59,971 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:48:01,291 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:1.006s]
2025-08-08 11:48:01,292 - UPSERT_DOCS - INFO - ✅ Successfully upserted 6 documents to Elasticsearch
2025-08-08 11:48:01,293 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 5
2025-08-08 11:48:01,293 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 11:48:01,294 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:48:01,294 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-08 11:48:01,294 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:48:01,294 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/5...
2025-08-08 11:48:01,940 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:48:01,946 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:48:01,946 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:48:01,946 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:48:01,946 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'highest student enrollment higher education'
2025-08-08 11:48:01,946 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:48:01,946 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:48:02,153 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.207s]
2025-08-08 11:48:02,154 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 75
2025-08-08 11:48:02,348 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.194s]
2025-08-08 11:48:02,348 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 42
2025-08-08 11:48:02,547 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.199s]
2025-08-08 11:48:02,548 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 17
2025-08-08 11:48:02,750 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.202s]
2025-08-08 11:48:02,751 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 11
2025-08-08 11:48:03,597 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:48:03,802 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.200s]
2025-08-08 11:48:03,802 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:48:03,803 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 11:48:03,803 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:41.429791+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 11:48:03,803 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:48:03,803 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:48:03,803 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:48:03,803 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:22.382219+00:00', 'data_returned': True}
2025-08-08 11:48:03,803 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:48:03,803 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:16.155966+00:00', 'data_returned': True}
2025-08-08 11:48:03,803 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:48:03,803 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:03.070454+00:00', 'data_returned': True}
2025-08-08 11:48:07,450 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:48:07,467 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 815 characters
2025-08-08 11:48:07,468 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/5...
2025-08-08 11:48:08,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:48:08,880 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:48:08,880 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:48:08,880 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:48:08,880 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University enrollment comparison'
2025-08-08 11:48:08,880 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:48:08,881 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:48:09,076 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.195s]
2025-08-08 11:48:09,076 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 75
2025-08-08 11:48:09,269 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.192s]
2025-08-08 11:48:09,269 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 42
2025-08-08 11:48:09,462 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.193s]
2025-08-08 11:48:09,463 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 17
2025-08-08 11:48:09,656 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.192s]
2025-08-08 11:48:09,656 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 11
2025-08-08 11:48:10,133 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:48:10,338 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.201s]
2025-08-08 11:48:10,338 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:48:10,338 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 11:48:10,339 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:57.643107+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 11:48:10,339 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 11:48:10,339 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:13.019289+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 11:48:10,339 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:48:10,339 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:48:10,339 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 11:48:10,339 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:41.429791+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 11:48:10,339 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:48:10,339 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:48:13,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:48:13,274 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 753 characters
2025-08-08 11:48:13,274 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/5...
2025-08-08 11:48:16,606 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:48:16,617 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:48:16,617 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:48:16,617 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:48:16,617 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University popular programs'
2025-08-08 11:48:16,617 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:48:16,617 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:48:16,814 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.196s]
2025-08-08 11:48:16,814 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 75
2025-08-08 11:48:17,014 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.199s]
2025-08-08 11:48:17,015 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 42
2025-08-08 11:48:17,211 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.196s]
2025-08-08 11:48:17,211 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 17
2025-08-08 11:48:17,403 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.192s]
2025-08-08 11:48:17,405 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 11
2025-08-08 11:48:17,864 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:48:18,071 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.203s]
2025-08-08 11:48:18,072 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:48:18,072 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 11:48:18,072 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:57.643107+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 11:48:18,072 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 11:48:18,072 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:13.019289+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 11:48:18,073 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 11:48:18,073 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:41.429791+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 11:48:18,073 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:48:18,073 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:51.582492+00:00', 'data_returned': True}
2025-08-08 11:48:18,073 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:48:18,073 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:48:21,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:48:21,804 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 985 characters
2025-08-08 11:48:21,805 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/5...
2025-08-08 11:48:22,681 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:48:22,693 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:48:22,693 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:48:22,693 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:48:22,693 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student gender distribution 2023'
2025-08-08 11:48:22,693 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:48:22,694 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:48:22,892 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.198s]
2025-08-08 11:48:22,893 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 75
2025-08-08 11:48:23,092 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.199s]
2025-08-08 11:48:23,093 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 42
2025-08-08 11:48:23,286 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.193s]
2025-08-08 11:48:23,286 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 17
2025-08-08 11:48:23,482 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.195s]
2025-08-08 11:48:23,482 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 11
2025-08-08 11:48:24,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:48:24,277 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.204s]
2025-08-08 11:48:24,278 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:48:24,278 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:48:24,278 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:06.796514+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 11:48:24,278 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:48:24,279 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:39:52.812710+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 11:48:24,279 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:48:24,279 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:53.063748+00:00', 'data_returned': True}
2025-08-08 11:48:24,279 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:48:24,279 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:16.155966+00:00', 'data_returned': True}
2025-08-08 11:48:24,279 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:48:24,279 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:03.070454+00:00', 'data_returned': True}
2025-08-08 11:48:27,020 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:48:27,027 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 572 characters
2025-08-08 11:48:27,027 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/5...
2025-08-08 11:48:27,812 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:48:27,817 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:48:27,817 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 11:48:27,817 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:48:27,817 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'institution with most students implications higher education'
2025-08-08 11:48:27,817 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What institution has the most students?'
2025-08-08 11:48:27,817 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What institution has the most students?'
2025-08-08 11:48:28,009 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.191s]
2025-08-08 11:48:28,009 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 75
2025-08-08 11:48:28,200 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.190s]
2025-08-08 11:48:28,200 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 42
2025-08-08 11:48:28,400 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.199s]
2025-08-08 11:48:28,400 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 17
2025-08-08 11:48:28,591 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.190s]
2025-08-08 11:48:28,591 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 11
2025-08-08 11:48:29,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 11:48:29,596 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.207s]
2025-08-08 11:48:29,597 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 11:48:29,597 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 11:48:29,597 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:41.429791+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 11:48:29,597 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:48:29,597 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:47:16.155966+00:00', 'data_returned': True}
2025-08-08 11:48:29,597 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:48:29,597 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T09:40:03.070454+00:00', 'data_returned': True}
2025-08-08 11:48:29,598 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:48:29,598 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:22.382219+00:00', 'data_returned': True}
2025-08-08 11:48:29,598 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:48:29,598 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:12:34.859840+00:00', 'data_returned': True}
2025-08-08 11:48:33,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:48:33,371 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 1180 characters
2025-08-08 11:48:33,372 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:48:33,372 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-08 11:48:33,372 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:48:33,372 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-08 11:48:33,372 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 5
2025-08-08 11:48:33,372 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 6
2025-08-08 11:48:33,372 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 0
2025-08-08 11:48:33,372 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_114600.log
2025-08-08 11:48:33,377 - celery.app.trace - INFO - Task generate_streaming_report[b9a99649-b296-4c79-a409-b92812aa9722] succeeded in 153.16663091699957s: {'outline': '# Report on Student Enrollment at Institutions

## I. Introduction  
   - This report investigates which institution has the most students, focusing on enrollment trends in higher education. Understanding these trends is crucial for assessing the landscape of educational opportunities and resource allocation.

## II. Institution with the Most Students  
   - **2.1. ITC University**  
     - Total enrollment: 192,627 students  
     - Comparison with other institutions:  
       - Presbyterian University College Ghana: 32,094 students  
       - Koforidua Technical University: 17,758 students  
       - Accra Medical: 13,012 students  
     - Significance of ITC University\'s enrollment in the context of higher education.

## III. Popular Programs at ITC University  
   - **3.1. Most Popular Programs**  
     - Bachelor of Education (Junior High School): 15,977 students  
     - Bachelor of Education (Basic Education): 10,732 students  
     - Bachelor of Education (Upper Primary Education): 8,905...', ...}

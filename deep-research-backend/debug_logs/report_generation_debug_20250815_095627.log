2025-08-15 09:56:27,108 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250815_095627.log
2025-08-15 09:56:27,108 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:56:27,108 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 78ec2771-c76b-4ee5-a46f-c95f8797ba83
2025-08-15 09:56:27,108 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:56:27,108 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-15 09:56:27,108 - REPORT_REQUEST - INFO - 🆔 Task ID: 78ec2771-c76b-4ee5-a46f-c95f8797ba83
2025-08-15 09:56:27,108 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-15T09:56:27.108363
2025-08-15 09:56:27,230 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.121s]
2025-08-15 09:56:27,230 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 128
2025-08-15 09:56:27,349 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.119s]
2025-08-15 09:56:27,349 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 70
2025-08-15 09:56:27,475 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.125s]
2025-08-15 09:56:27,475 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-15 09:56:27,475 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (24 docs)
2025-08-15 09:56:27,475 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (16 docs)
2025-08-15 09:56:27,476 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (15 docs)
2025-08-15 09:56:27,476 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-15 09:56:27,476 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-15 09:56:27,476 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-15 09:56:27,476 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-15 09:56:27,476 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-15 09:56:27,476 - ELASTICSEARCH_STATE - INFO -   - 'Which instituion has the most students?' (6 docs)
2025-08-15 09:56:27,476 - ELASTICSEARCH_STATE - INFO -   - 'How are students performing at ITC University?' (4 docs)
2025-08-15 09:56:27,477 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:56:27,477 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-15 09:56:27,477 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:56:27,477 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How are girls performing at ITC University?
2025-08-15 09:56:27,477 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-15 09:56:27,477 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-15 09:56:39,899 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:39,926 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-15 09:56:43,873 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:43,891 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-15 09:56:45,982 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:46,268 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:46,417 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:46,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:46,606 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:46,616 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:46,802 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:46,837 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:47,019 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:47,816 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:47,878 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-15 09:56:50,070 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:50,414 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:50,655 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:52,511 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:53,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:53,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:53,954 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:54,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:54,504 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:59,247 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:56:59,482 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:01,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:05,275 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:05,278 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any trends in the performance of girls at ITC University over the past few years?', 'sql': "SELECT s.sex, sem.start_year, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS assessment_count\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN semesters sem ON ar.semester_id = sem.id\nWHERE s.sex = 'F' AND sem.status = 'Ended'\nGROUP BY s.sex, sem.start_year\nORDER BY sem.start_year DESC\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly identifies the performance trends of female students (sex = 'F') at ITC University by calculating the average final score and the count of assessments for each academic year (start_year) where the semester status is 'Ended'. The use of GROUP BY on both sex and start_year allows for the aggregation of scores over the years, which is essential for identifying trends. The ORDER BY clause sorts the results by year in descending order, which is appropriate for analyzing trends over time. The LIMIT clause is not necessary for answering the question but does not detract from the correctness of the query.", 'feedback': 'The question could be clarified by specifying what kind of trends are being looked for (e.g., improvement, decline, consistency) or by asking for specific metrics beyond average scores, such as passing rates or comparisons with male students. The SQL could be improved by removing the LIMIT clause to ensure all relevant years are included in the analysis.'}
2025-08-15 09:57:05,279 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:05,279 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 09:57:05,856 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:05,861 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in the most?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(ar.finalscore) AS average_score\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.assessment_results ar ON s.id = ar.student_id AND sp.id = ar.student_program_id\nJOIN core.courses c ON ar.course_id = c.id\nWHERE s.sex = 'F' AND s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY p.long_name, c.title\nORDER BY average_score DESC\nLIMIT 20;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the programs and courses that female students at ITC University are excelling in by calculating the average scores of assessment results for each program and course combination. It filters the students by sex and institution, joins the necessary tables to gather relevant data, and groups the results to find the average scores. The ordering and limiting of results to the top 20 is also appropriate for the question asked.', 'feedback': "The SQL query is well-structured and answers the question accurately. However, it could be beneficial to clarify whether 'excelling' refers strictly to average scores or if other metrics (like grades or positions) should also be considered. Additionally, ensuring that the institution name is correctly referenced in the subquery would be important for accuracy."}
2025-08-15 09:57:05,862 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:05,862 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 09:57:07,560 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:07,577 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic performance levels of girls compare to those of boys at ITC University?', 'sql': "SELECT s.sex, AVG(sg.gpa) AS average_gpa, COUNT(sg.id) AS total_students\nFROM core.students s\nJOIN core.student_semester_gpas sg ON s.id = sg.student_id\nJOIN core.student_programs sp ON s.id = sp.student_id\nWHERE sp.institution_id = 'ITC University' AND sg.status = 'complete'\nGROUP BY s.sex\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average GPA and total number of students for each sex (boys and girls) at ITC University. It joins the necessary tables: students, student_semester_gpas, and student_programs, ensuring that only students from ITC University with a 'complete' status are considered. The grouping by sex allows for a direct comparison of academic performance levels between girls and boys.", 'feedback': 'The query is well-structured and answers the question effectively. However, the LIMIT clause is unnecessary since the GROUP BY will already return two rows (one for each sex). Removing the LIMIT clause would make the query cleaner.'}
2025-08-15 09:57:07,577 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:07,578 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 09:57:09,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:10,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:11,347 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:11,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:11,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:12,500 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:13,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:13,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:13,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:14,207 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:15,843 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:15,873 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, courses, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-15 09:57:15,873 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:15,873 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:15,873 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, courses, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-15 09:57:16,306 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:16,473 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:17,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:18,192 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:18,208 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of female students in these specific courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the high performance of female students in specific courses at ITC University. However, the provided schema does not contain specific data related to performance factors, gender-specific performance metrics, or course-specific performance analysis. While there are tables related to students, courses, and grades, there is no direct way to correlate these with the factors influencing performance, especially in a gender-specific context. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 09:57:18,208 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:18,208 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:18,208 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of female students in these specific courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the high performance of female students in specific courses at ITC University. However, the provided schema does not contain specific data related to performance factors, gender-specific performance metrics, or course-specific performance analysis. While there are tables related to students, courses, and grades, there is no direct way to correlate these with the factors influencing performance, especially in a gender-specific context. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 09:57:18,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:18,492 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection on academic performance metrics for both girls and boys?', 'answerable': False, 'reasoning': 'The question is asking for recommendations on improving data collection for academic performance metrics, which is a strategic and operational inquiry rather than a query that can be answered directly from the database schema. The schema provides data structures related to institutions, students, courses, and academic records, but it does not contain specific guidelines or steps for improving data collection processes. Therefore, while the schema can provide data that might inform such decisions, it does not directly answer the question.', 'feedback': ''}
2025-08-15 09:57:18,492 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:18,492 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:18,492 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection on academic performance metrics for both girls and boys?', 'answerable': False, 'reasoning': 'The question is asking for recommendations on improving data collection for academic performance metrics, which is a strategic and operational inquiry rather than a query that can be answered directly from the database schema. The schema provides data structures related to institutions, students, courses, and academic records, but it does not contain specific guidelines or steps for improving data collection processes. Therefore, while the schema can provide data that might inform such decisions, it does not directly answer the question.', 'feedback': ''}
2025-08-15 09:57:19,886 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:19,900 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the absence of such data. The schema includes various tables related to institutions, students, programs, and assessments, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-15 09:57:19,900 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:19,900 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:19,900 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the absence of such data. The schema includes various tables related to institutions, students, programs, and assessments, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-15 09:57:20,617 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:20,994 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:22,222 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:23,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:23,091 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of female students in these specific courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of female students in certain courses at ITC University. However, the provided schema does not contain any direct information about the performance factors of students, nor does it specify gender-related performance metrics or course-specific performance data. While there are tables related to students, courses, and grades, the schema lacks the necessary attributes or relationships to analyze or derive insights about the performance of female students specifically in the context of the courses mentioned.', 'feedback': ''}
2025-08-15 09:57:23,092 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:23,092 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:23,092 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of female students in these specific courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of female students in certain courses at ITC University. However, the provided schema does not contain any direct information about the performance factors of students, nor does it specify gender-related performance metrics or course-specific performance data. While there are tables related to students, courses, and grades, the schema lacks the necessary attributes or relationships to analyze or derive insights about the performance of female students specifically in the context of the courses mentioned.', 'feedback': ''}
2025-08-15 09:57:23,218 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:23,231 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection on academic performance metrics for both girls and boys?', 'answerable': False, 'reasoning': 'The question is asking for recommendations on improving data collection for academic performance metrics, which is a strategic and operational inquiry rather than a query that can be answered directly from the database schema. The schema provides data structures related to institutions, students, courses, and academic records, but it does not contain specific guidelines or steps for improving data collection processes. Therefore, while the schema can provide data that might inform such decisions, it does not directly answer the question.', 'feedback': ''}
2025-08-15 09:57:23,232 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:23,232 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:23,232 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection on academic performance metrics for both girls and boys?', 'answerable': False, 'reasoning': 'The question is asking for recommendations on improving data collection for academic performance metrics, which is a strategic and operational inquiry rather than a query that can be answered directly from the database schema. The schema provides data structures related to institutions, students, courses, and academic records, but it does not contain specific guidelines or steps for improving data collection processes. Therefore, while the schema can provide data that might inform such decisions, it does not directly answer the question.', 'feedback': ''}
2025-08-15 09:57:25,034 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:25,053 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the absence of such data. The schema includes various tables related to institutions, students, programs, and assessments, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-15 09:57:25,054 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:25,054 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:25,054 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the absence of such data. The schema includes various tables related to institutions, students, programs, and assessments, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-15 09:57:25,627 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:25,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:27,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:28,224 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:28,237 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of female students in these specific courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the high performance of female students in specific courses at ITC University. However, the provided schema does not contain specific data about performance factors, gender-specific performance metrics, or course-specific performance analysis. While there are tables related to students, courses, and grades, there is no direct link or data that would allow for an analysis of the factors influencing performance based on gender or specific courses. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 09:57:28,237 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:28,238 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:28,238 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of female students in these specific courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the high performance of female students in specific courses at ITC University. However, the provided schema does not contain specific data about performance factors, gender-specific performance metrics, or course-specific performance analysis. While there are tables related to students, courses, and grades, there is no direct link or data that would allow for an analysis of the factors influencing performance based on gender or specific courses. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-15 09:57:28,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:28,462 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection on academic performance metrics for both girls and boys?', 'answerable': False, 'reasoning': 'The question is asking for recommendations on improving data collection for academic performance metrics, which is a strategic and operational inquiry rather than a query that can be answered directly from the database schema. The schema provides data structures related to institutions, students, courses, and academic records, but it does not contain information on specific steps or strategies that an institution like ITC University could take to enhance data collection processes. Therefore, while the schema can provide data on existing metrics, it cannot provide actionable steps or recommendations.', 'feedback': ''}
2025-08-15 09:57:28,462 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:28,462 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:28,462 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection on academic performance metrics for both girls and boys?', 'answerable': False, 'reasoning': 'The question is asking for recommendations on improving data collection for academic performance metrics, which is a strategic and operational inquiry rather than a query that can be answered directly from the database schema. The schema provides data structures related to institutions, students, courses, and academic records, but it does not contain information on specific steps or strategies that an institution like ITC University could take to enhance data collection processes. Therefore, while the schema can provide data on existing metrics, it cannot provide actionable steps or recommendations.', 'feedback': ''}
2025-08-15 09:57:29,595 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:29,612 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the absence of such data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative insights or contextual information that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-15 09:57:29,613 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:29,613 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:29,613 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the absence of such data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative insights or contextual information that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-15 09:57:30,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:30,884 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:33,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:33,077 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection on academic performance metrics for both girls and boys?', 'answerable': False, 'reasoning': 'The question is asking for recommendations on improving data collection for academic performance metrics, which is a strategic and operational inquiry rather than a query that can be answered directly from the database schema. The schema provides data structures related to institutions, students, courses, and academic records, but it does not contain specific guidelines or steps for improving data collection processes. Therefore, while the schema can provide data that might inform such decisions, it does not directly answer the question.', 'feedback': ''}
2025-08-15 09:57:33,077 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:33,077 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:33,077 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection on academic performance metrics for both girls and boys?', 'answerable': False, 'reasoning': 'The question is asking for recommendations on improving data collection for academic performance metrics, which is a strategic and operational inquiry rather than a query that can be answered directly from the database schema. The schema provides data structures related to institutions, students, courses, and academic records, but it does not contain specific guidelines or steps for improving data collection processes. Therefore, while the schema can provide data that might inform such decisions, it does not directly answer the question.', 'feedback': ''}
2025-08-15 09:57:33,084 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:33,095 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of female students in these specific courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of female students in certain courses at ITC University. However, the provided schema does not contain any direct information about the performance of students based on gender or specific courses. While there are tables related to students, courses, and grades, there is no explicit data or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing performance would typically require qualitative data or statistical analysis that is not represented in the schema.', 'feedback': ''}
2025-08-15 09:57:33,095 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 09:57:33,095 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 09:57:33,095 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of female students in these specific courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of female students in certain courses at ITC University. However, the provided schema does not contain any direct information about the performance of students based on gender or specific courses. While there are tables related to students, courses, and grades, there is no explicit data or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing performance would typically require qualitative data or statistical analysis that is not represented in the schema.', 'feedback': ''}
2025-08-15 09:57:33,096 - root - INFO - [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 86.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 84.26}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Media And Technologies For Teaching And Learning', 'average_score': 78.83}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'On-Campus Teaching Practice', 'average_score': 75.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Project Work', 'average_score': 74.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 72.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Guidance and Counselling in Schools', 'average_score': 70.0}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'course_title': 'Academic Writing', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Methods in Specified Subjects', 'average_score': 70.0}, {'program_name': 'Master Of Arts In Educational Leadership', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Historical, Philosophical and Sociological Foundations of Education', 'average_score': 67.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'INTRODUCTION TO HOME ECONOMICS', 'average_score': 66.45}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'General Principles And Methods Of Instruction', 'average_score': 66.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'School Organisation and Management', 'average_score': 65.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Software', 'average_score': 61.2}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 59.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 54.2}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Off-Campus Teaching Practice', 'average_score': 0.0}]
2025-08-15 09:57:33,096 - root - INFO - 'No results'
2025-08-15 09:57:33,096 - root - INFO - 'No results'
2025-08-15 09:57:33,096 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-15 09:57:38,884 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:38,891 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-15 09:57:45,705 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:45,711 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:57:45,711 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 09:57:45,711 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:57:45,711 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-15 09:57:45,711 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 09:57:45,711 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 09:57:45,711 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 09:57:45,711 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 09:57:45,711 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the academic performance levels of girls compare to those of boys at ITC University?...
2025-08-15 09:57:45,711 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, which indicates that there may not be sufficient data avai...
2025-08-15 09:57:45,711 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: academic_performance_comparison_girls_boys
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-15 09:57:45,712 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:57:45,712 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 09:57:45,712 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 09:57:45,712 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are girls at ITC University excelling in the most?...
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, girls are excelling the most in the following programs and courses: 1) In the Bac...
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Re...
2025-08-15 09:57:45,712 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: top_courses_for_girls_at_itc_university
2025-08-15 09:57:45,713 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-15 09:57:45,713 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-15 09:57:45,713 - celery.redirected - WARNING - [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 86.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 84.26}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Media And Technologies For Teaching And Learning', 'average_score': 78.83}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'On-Campus Teaching Practice', 'average_score': 75.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Project Work', 'average_score': 74.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 72.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Guidance and Counselling in Schools', 'average_score': 70.0}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'course_title': 'Academic Writing', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Methods in Specified Subjects', 'average_score': 70.0}, {'program_name': 'Master Of Arts In Educational Leadership', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Historical, Philosophical and Sociological Foundations of Education', 'average_score': 67.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'INTRODUCTION TO HOME ECONOMICS', 'average_score': 66.45}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'General Principles And Methods Of Instruction', 'average_score': 66.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'School Organisation and Management', 'average_score': 65.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Software', 'average_score': 61.2}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 59.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 54.2}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Off-Campus Teaching Practice', 'average_score': 0.0}]
2025-08-15 09:57:45,713 - celery.redirected - WARNING - ================================= 
2025-08-15 09:57:45,713 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: top_courses_for_girls_at_itc_university
2025-08-15 09:57:45,713 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-15 09:57:45,713 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:57:45,713 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 09:57:45,713 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:57:45,713 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-15 09:57:45,713 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 09:57:45,714 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 09:57:45,714 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 09:57:45,714 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 09:57:45,714 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any trends in the performance of girls at ITC University over the past few years?...
2025-08-15 09:57:45,714 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Based on the data available, there are no recorded results regarding the performance of girls at ITC...
2025-08-15 09:57:45,714 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 09:57:45,714 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 09:57:45,714 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_performance_trends_itc_university
2025-08-15 09:57:45,714 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 09:57:45,714 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-15 09:57:45,714 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 3
2025-08-15 09:57:45,714 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:57:45,714 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-15 09:57:45,714 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:57:45,714 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 3 documents
2025-08-15 09:57:45,714 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-15 09:57:45,714 - UPSERT_DOCS - INFO -     Content: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-15 09:57:45,714 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T09:57:33.077827+00:00', 'data_returned': False}
2025-08-15 09:57:45,714 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-15 09:57:45,715 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-15 09:57:45,715 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T09:57:33.095987+00:00', 'data_returned': True, 'data_tag': 'top_courses_for_girls_at_itc_university'}
2025-08-15 09:57:45,715 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-15 09:57:45,715 - UPSERT_DOCS - INFO -     Content: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-15 09:57:45,715 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T09:57:29.613432+00:00', 'data_returned': False}
2025-08-15 09:57:45,715 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 1/3
2025-08-15 09:57:45,833 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.118s]
2025-08-15 09:57:49,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 09:57:50,251 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.516s]
2025-08-15 09:57:50,252 - UPSERT_DOCS - INFO - ✅ Successfully upserted 3 documents to Elasticsearch
2025-08-15 09:57:50,253 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 3
2025-08-15 09:57:50,253 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-15 09:57:50,254 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:57:50,254 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-15 09:57:50,254 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:57:50,254 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/3...
2025-08-15 09:57:51,064 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:51,084 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:57:51,085 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-15 09:57:51,085 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:57:51,085 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'academic performance female students ITC University'
2025-08-15 09:57:51,085 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-15 09:57:51,085 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-15 09:57:51,207 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.122s]
2025-08-15 09:57:51,207 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 131
2025-08-15 09:57:51,335 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-15 09:57:51,335 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 71
2025-08-15 09:57:51,463 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-15 09:57:51,464 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 19
2025-08-15 09:57:51,583 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.119s]
2025-08-15 09:57:51,584 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-15 09:57:52,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 09:57:52,502 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.130s]
2025-08-15 09:57:52,503 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-15 09:57:52,503 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:52,503 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:57:52,504 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:52,504 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-15 09:57:52,504 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:52,504 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-15 09:57:52,504 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:52,504 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:57:52,504 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-15 09:57:52,504 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-15 09:57:52,505 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:52,505 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:57:52,505 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_of_girls_over_years
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3405 chars):
2025-08-15 09:57:52,506 - app.chains.section_writer - INFO -    Question: How do the academic performance metrics of girls compare to those of boys at ITC University?
Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several key areas. Girls have an average final score of 68.72, compared to boys' average final score of 54.86. Additionally, girls have a slightly higher average grade point (GP) of 1.293 compared to boys' average GP of 1.254. In terms of total assessments, boys have completed 71 assessments while...
2025-08-15 09:57:56,554 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:56,561 - app.chains.section_writer - INFO - 🤖 AI generated section (1316 chars):
2025-08-15 09:57:56,561 - app.chains.section_writer - INFO -    The purpose of this report is to analyze the academic performance of female students at ITC University, highlighting their achievements and challenges. The key finding indicates that girls are performing well in specific programs, demonstrating strong academic capabilities in higher education. At IT...
2025-08-15 09:57:56,561 - app.chains.section_writer - INFO - ✅ Found 4 data tag placeholders: ['academic_performance_by_gender', 'academic_performance_comparison_by_gender', 'academic_performance_comparison_by_sex', 'average_scores_of_girls_over_years']
2025-08-15 09:57:56,561 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 1157 characters
2025-08-15 09:57:56,562 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/3...
2025-08-15 09:57:57,206 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:57:57,210 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:57:57,210 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-15 09:57:57,211 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:57:57,211 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Academic performance girls ITC University'
2025-08-15 09:57:57,211 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-15 09:57:57,211 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-15 09:57:57,333 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.122s]
2025-08-15 09:57:57,334 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 131
2025-08-15 09:57:57,463 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-15 09:57:57,464 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 71
2025-08-15 09:57:57,591 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-15 09:57:57,591 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 19
2025-08-15 09:57:57,712 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.121s]
2025-08-15 09:57:57,712 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-15 09:57:58,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 09:57:58,408 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.136s]
2025-08-15 09:57:58,409 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-15 09:57:58,409 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:58,409 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:57:58,409 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:58,409 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-15 09:57:58,409 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:58,410 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-15 09:57:58,410 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:58,410 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:57:58,410 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-15 09:57:58,410 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_of_girls_over_years
2025-08-15 09:57:58,411 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3405 chars):
2025-08-15 09:57:58,412 - app.chains.section_writer - INFO -    Question: How do the academic performance metrics of girls compare to those of boys at ITC University?
Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several key areas. Girls have an average final score of 68.72, compared to boys' average final score of 54.86. Additionally, girls have a slightly higher average grade point (GP) of 1.293 compared to boys' average GP of 1.254. In terms of total assessments, boys have completed 71 assessments while...
2025-08-15 09:58:05,940 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:58:05,954 - app.chains.section_writer - INFO - 🤖 AI generated section (1874 chars):
2025-08-15 09:58:05,954 - app.chains.section_writer - INFO -    ## I. Academic Performance of Girls at ITC University  

### A. Summary of performance metrics  
At ITC University, girls have demonstrated superior academic performance compared to their male counterparts. The average final score for female students is 68.72, significantly higher than the average s...
2025-08-15 09:58:05,954 - app.chains.section_writer - INFO - ✅ Found 3 data tag placeholders: ['academic_performance_by_gender', 'academic_performance_comparison_by_sex', 'average_scores_of_girls_over_years']
2025-08-15 09:58:05,955 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 1760 characters
2025-08-15 09:58:05,955 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/3...
2025-08-15 09:58:06,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:58:06,696 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:58:06,696 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-15 09:58:06,696 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:58:06,696 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion academic support girls ITC University'
2025-08-15 09:58:06,696 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-15 09:58:06,696 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-15 09:58:06,819 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.122s]
2025-08-15 09:58:06,819 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 131
2025-08-15 09:58:06,940 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.121s]
2025-08-15 09:58:06,941 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 71
2025-08-15 09:58:07,059 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.118s]
2025-08-15 09:58:07,059 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 19
2025-08-15 09:58:07,181 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.121s]
2025-08-15 09:58:07,181 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 7
2025-08-15 09:58:07,655 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 09:58:07,786 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.127s]
2025-08-15 09:58:07,786 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-15 09:58:07,787 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:58:07,787 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-15 09:58:07,787 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:58:07,787 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:58:07,787 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:58:07,787 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-15 09:58:07,787 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:58:07,787 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:58:07,787 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-15 09:58:07,787 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T09:57:33.095987+00:00', 'data_returned': True, 'data_tag': 'top_courses_for_girls_at_itc_university'}
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-15 09:58:07,788 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-15 09:58:07,789 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-15 09:58:07,789 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-15 09:58:07,789 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T09:57:33.095987+00:00', 'data_returned': True, 'data_tag': 'top_courses_for_girls_at_itc_university'}
2025-08-15 09:58:07,789 - app.chains.section_writer - INFO -   ✅ Added Data Tag: top_courses_for_girls_at_itc_university
2025-08-15 09:58:07,789 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3604 chars):
2025-08-15 09:58:07,789 - app.chains.section_writer - INFO -    Question: How do the academic performance metrics of girls compare to those of boys at ITC University?
Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several key areas. Girls have an average assessment total of 36.26 and an average exam total of 36.87, leading to an average final score of 68.72. In contrast, boys have significantly lower averages, with an average assessment total of 10.92, an average exam total of 13.44, and an average final sc...
2025-08-15 09:58:13,695 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 09:58:13,709 - app.chains.section_writer - INFO - 🤖 AI generated section (2006 chars):
2025-08-15 09:58:13,709 - app.chains.section_writer - INFO -    ## III. Conclusion  

A. Summary of findings  
The analysis of academic performance metrics at ITC University reveals that girls consistently outperform boys across several key indicators. Girls have an average final score of 68.72, significantly higher than boys' average final score of 54.86. Addit...
2025-08-15 09:58:13,709 - app.chains.section_writer - INFO - ✅ Found 3 data tag placeholders: ['academic_performance_comparison_by_sex', 'academic_performance_by_gender', 'top_courses_for_girls_at_itc_university']
2025-08-15 09:58:13,710 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 1940 characters
2025-08-15 09:58:13,710 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 09:58:13,710 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-15 09:58:13,711 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 09:58:13,711 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-15 09:58:13,711 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 4
2025-08-15 09:58:13,711 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 3
2025-08-15 09:58:13,711 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 1
2025-08-15 09:58:13,711 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250815_095627.log
2025-08-15 09:58:13,716 - celery.app.trace - INFO - Task generate_streaming_report[40a12372-4d22-461f-903e-adc2d0afce9c] succeeded in 106.60860012500052s: {'outline': '# Report on Girls\' Performance at ITC University

## Introduction  
The purpose of this report is to analyze the academic performance of female students at ITC University, highlighting their achievements and challenges. The key finding indicates that girls are performing well in specific programs, demonstrating strong academic capabilities in higher education.

## I. Academic Performance of Girls at ITC University  
- A. Summary of performance metrics  
- B. Highlighted programs and courses  

### II. Programs and Courses with High Performance  
- A. Bachelor of Arts (Journalism and Media Studies)  
  - 1. Course: Educational Research Methods, Assessment and Statistics  
  - 2. Average score: 86.0  
  
- B. Bachelor of Science (Clothing and Textiles Education)  
  - 1. Course: Introduction To Information And Communication Technology  
  - 2. Average score: 84.26  
  
- C. Bachelor of Science (Information and Communication Technology Education)  
  - 1. Course: Instructional Media And Technologies...', ...}
2025-08-15 10:03:56,325 - celery.worker.strategy - INFO - Task generate_streaming_report[ab39d9f7-f095-4a5b-91cf-c025dac48364] received
2025-08-15 10:03:56,327 - app.tasks.report_task - INFO - starting streaming report generation...
2025-08-15 10:03:56,327 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:03:56,327 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: d20398ec-6bd4-403c-beca-7127516ba8fc
2025-08-15 10:03:56,327 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:03:56,327 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are boys performing at ITC University?'
2025-08-15 10:03:56,327 - REPORT_REQUEST - INFO - 🆔 Task ID: d20398ec-6bd4-403c-beca-7127516ba8fc
2025-08-15 10:03:56,328 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-15T10:03:56.328017
2025-08-15 10:03:56,451 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.123s]
2025-08-15 10:03:56,452 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 131
2025-08-15 10:03:56,573 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.120s]
2025-08-15 10:03:56,573 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 71
2025-08-15 10:03:56,702 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.129s]
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (24 docs)
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (19 docs)
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (15 docs)
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO -   - 'Which instituion has the most students?' (6 docs)
2025-08-15 10:03:56,703 - ELASTICSEARCH_STATE - INFO -   - 'How are students performing at ITC University?' (4 docs)
2025-08-15 10:03:56,704 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:03:56,704 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-15 10:03:56,704 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:03:56,704 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How are boys performing at ITC University?
2025-08-15 10:03:56,704 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-15 10:03:56,704 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-15 10:04:08,091 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:08,101 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-15 10:04:12,544 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:12,558 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-15 10:04:19,292 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:20,131 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:20,169 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:20,302 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:21,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:21,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:21,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:21,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:22,235 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:22,857 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:22,928 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-15 10:04:33,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:34,193 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:34,769 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:34,954 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:35,095 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:42,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:43,825 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:45,381 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:45,595 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:45,764 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:48,085 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:48,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:48,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:49,318 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:49,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:53,529 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:54,099 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:54,662 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:55,317 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:55,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:04:59,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:00,019 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:00,032 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "How do boys' performance levels at ITC University vary by academic year or semester?", 'sql': "SELECT s.academic_year_id, se.period AS semester, AVG(ar.finalscore) AS average_score\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN semesters se ON ar.semester_id = se.id\nWHERE s.sex = 'M'\nGROUP BY s.academic_year_id, se.period\nORDER BY s.academic_year_id, se.period\nLIMIT 20;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the average performance levels (final scores) of male students (boys) at ITC University by grouping the results based on academic year and semester. It uses the appropriate joins to connect the assessment results with student information and semester details. The use of AVG(ar.finalscore) provides the average score, which directly addresses the question about performance levels. The grouping by academic_year_id and semester period aligns with the request to analyze performance variation by these categories.', 'feedback': 'The query is well-structured and answers the question effectively. However, the LIMIT clause may restrict the results unnecessarily. If the goal is to see all variations across academic years and semesters, consider removing the LIMIT clause to provide a complete view of the data.'}
2025-08-15 10:05:00,032 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:00,033 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:05:00,979 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:00,984 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the performance of boys at ITC University compare to that of girls?', 'sql': "SELECT sex, AVG(finalscore) AS average_score, COUNT(*) AS total_students \nFROM assessment_results ar \nJOIN students s ON ar.student_id = s.id \nJOIN student_programs sp ON s.id = sp.student_id \nWHERE sp.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University') \nGROUP BY sex \nLIMIT 20;", 'correct': True, 'reasoning': 'The SQL query correctly retrieves the average final scores of students grouped by their sex (boys and girls) at ITC University. It joins the necessary tables (assessment_results, students, and student_programs) to access the required data. The use of AVG(finalscore) provides a comparison of performance, and COUNT(*) gives the total number of students in each group. The WHERE clause filters the results to only include students from ITC University, which is relevant to the question. The LIMIT clause is unnecessary for the context of comparing two groups, but it does not affect the correctness of the query.', 'feedback': 'The question could be clarified by specifying whether the comparison should include only the average scores or if other metrics (like total students) are also of interest. The SQL could be improved by removing the LIMIT clause, as it is not needed when comparing two groups.'}
2025-08-15 10:05:00,984 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:00,984 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:05:01,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:01,680 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the average grades or GPAs for boys at ITC University across different programs?', 'sql': "SELECT p.long_name AS program_name, AVG(sg.gpa) AS average_gpa\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.student_semester_gpas sg ON s.id = sg.student_id\nJOIN core.programs p ON sp.program_id = p.id\nWHERE s.sex = 'M' AND s.institution_id = (SELECT id FROM core.institutions WHERE name = 'ITC University')\nGROUP BY p.long_name\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly identifies the average GPAs for male students ('M') at ITC University by joining the necessary tables: students, student_programs, student_semester_gpas, and programs. It filters the students based on sex and institution, then groups the results by program name to calculate the average GPA for each program. The use of AVG(sg.gpa) and GROUP BY p.long_name aligns with the question's request for average grades across different programs.", 'feedback': 'The query is well-structured and answers the question accurately. However, the LIMIT clause may not be necessary unless there is a specific reason to limit the results to 20 programs. Consider removing it if the intention is to retrieve all programs.'}
2025-08-15 10:05:01,681 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:01,681 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:05:02,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:03,497 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:04,233 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:04,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:04,642 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any notable differences in performance based on the entry mode of boys at ITC University?', 'sql': "SELECT em.entry_mode, AVG(sg.gpa) AS average_gpa, COUNT(s.id) AS student_count\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.student_semester_gpas sg ON sp.id = sg.student_program_id\nJOIN core.entry_modes em ON sp.entry_mode_id = em.id\nWHERE s.sex = 'M' AND sp.institution_id = 'ITC University'\nGROUP BY em.entry_mode\nORDER BY average_gpa DESC\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly identifies male students ('s.sex = 'M'') enrolled at 'ITC University' and calculates the average GPA for each entry mode by joining the relevant tables. It groups the results by entry mode and orders them by average GPA, which aligns with the question's focus on performance differences based on entry mode. The inclusion of COUNT(s.id) provides additional context on the number of students per entry mode, which is relevant for assessing performance differences.", 'feedback': "The question could be clarified by specifying what is meant by 'notable differences'—for example, whether it refers to statistical significance or simply observable differences in average GPA. Additionally, the SQL could be improved by including a HAVING clause to filter out entry modes with very few students, which might skew the average GPA."}
2025-08-15 10:05:04,642 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:04,642 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:05:04,687 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:05,051 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:05,738 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:07,310 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:07,491 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:08,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:09,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:13,042 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:13,969 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:14,473 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:15,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:17,942 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:18,090 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:19,247 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:22,794 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:23,074 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:23,576 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:23,589 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific data collection methods are currently in place at ITC University, and how might they be improved to capture performance levels more effectively?', 'answerable': False, 'reasoning': 'The question asks for specific data collection methods currently in place at ITC University and suggestions for improvement. However, the provided schema does not contain any tables or fields that explicitly describe data collection methods or performance levels. The schema primarily focuses on various entities related to institutions, students, courses, and assessments, but lacks details on data collection methodologies or performance evaluation strategies. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:05:23,589 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:23,589 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:23,590 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific data collection methods are currently in place at ITC University, and how might they be improved to capture performance levels more effectively?', 'answerable': False, 'reasoning': 'The question asks for specific data collection methods currently in place at ITC University and suggestions for improvement. However, the provided schema does not contain any tables or fields that explicitly describe data collection methods or performance levels. The schema primarily focuses on various entities related to institutions, students, courses, and assessments, but lacks details on data collection methodologies or performance evaluation strategies. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:05:23,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:23,879 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What other factors, aside from entry mode, might influence the academic performance of boys at ITC University?', 'answerable': False, 'reasoning': "The question asks for factors influencing academic performance, specifically for boys at ITC University. While the schema contains various tables related to academic performance, such as 'students', 'student_programs', and 'grades', it does not provide specific data or attributes that directly correlate with the influence of factors on academic performance. Additionally, the schema does not include demographic or behavioral data that could be analyzed to determine such influences. Therefore, the question cannot be answered based on the available schema.", 'feedback': ''}
2025-08-15 10:05:23,879 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:23,879 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:23,879 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What other factors, aside from entry mode, might influence the academic performance of boys at ITC University?', 'answerable': False, 'reasoning': "The question asks for factors influencing academic performance, specifically for boys at ITC University. While the schema contains various tables related to academic performance, such as 'students', 'student_programs', and 'grades', it does not provide specific data or attributes that directly correlate with the influence of factors on academic performance. Additionally, the schema does not include demographic or behavioral data that could be analyzed to determine such influences. Therefore, the question cannot be answered based on the available schema.", 'feedback': ''}
2025-08-15 10:05:24,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:25,513 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:25,538 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance differences between girls and boys at ITC University. However, the provided schema does not contain specific data related to performance metrics, gender comparisons, or any qualitative factors that could explain performance differences. While there are tables related to students, grades, and programs, there is no direct way to extract or analyze the factors influencing performance based on gender from the schema.', 'feedback': ''}
2025-08-15 10:05:25,538 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:25,538 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:25,538 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance differences between girls and boys at ITC University. However, the provided schema does not contain specific data related to performance metrics, gender comparisons, or any qualitative factors that could explain performance differences. While there are tables related to students, grades, and programs, there is no direct way to extract or analyze the factors influencing performance based on gender from the schema.', 'feedback': ''}
2025-08-15 10:05:29,426 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:29,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:29,441 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the differences in average GPAs among the various programs at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to differences in average GPAs among various programs at ITC University. While the schema contains tables related to GPAs (like 'student_semester_gpas' and 'programs'), it does not provide direct information about the factors influencing GPA differences, such as course difficulty, teaching quality, or student demographics. The schema lacks qualitative data or specific metrics that could be analyzed to draw conclusions about the reasons behind GPA variations. Therefore, the question cannot be answered based solely on the provided schema.", 'feedback': ''}
2025-08-15 10:05:29,445 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:29,445 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:29,445 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the differences in average GPAs among the various programs at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to differences in average GPAs among various programs at ITC University. While the schema contains tables related to GPAs (like 'student_semester_gpas' and 'programs'), it does not provide direct information about the factors influencing GPA differences, such as course difficulty, teaching quality, or student demographics. The schema lacks qualitative data or specific metrics that could be analyzed to draw conclusions about the reasons behind GPA variations. Therefore, the question cannot be answered based solely on the provided schema.", 'feedback': ''}
2025-08-15 10:05:29,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:29,938 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:31,487 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:32,172 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:32,193 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific data collection methods are currently in place at ITC University, and how might they be improved to capture performance levels more effectively?', 'answerable': False, 'reasoning': 'The question asks for specific data collection methods currently in place at ITC University and suggestions for improvement. However, the provided schema does not contain any tables or fields that explicitly describe data collection methods or performance levels. The schema primarily focuses on various entities related to institutions, students, courses, and assessments, but lacks details on data collection methodologies or performance evaluation strategies. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:05:32,193 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:32,193 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:32,193 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific data collection methods are currently in place at ITC University, and how might they be improved to capture performance levels more effectively?', 'answerable': False, 'reasoning': 'The question asks for specific data collection methods currently in place at ITC University and suggestions for improvement. However, the provided schema does not contain any tables or fields that explicitly describe data collection methods or performance levels. The schema primarily focuses on various entities related to institutions, students, courses, and assessments, but lacks details on data collection methodologies or performance evaluation strategies. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:05:32,575 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:32,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:32,627 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data related to performance metrics, gender comparisons, or any qualitative factors that could explain differences in performance. While there are tables related to students, grades, and programs, there is no direct way to extract or analyze the factors influencing performance based on gender from the schema.', 'feedback': ''}
2025-08-15 10:05:32,627 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:32,627 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:32,627 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data related to performance metrics, gender comparisons, or any qualitative factors that could explain differences in performance. While there are tables related to students, grades, and programs, there is no direct way to extract or analyze the factors influencing performance based on gender from the schema.', 'feedback': ''}
2025-08-15 10:05:32,908 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:32,930 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What other factors, aside from entry mode, might influence the academic performance of boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance, specifically for boys at ITC University. However, the schema does not provide direct information about academic performance metrics or specific factors influencing it. While there are tables related to students, programs, and academic records, there is no clear linkage or data that directly addresses the question about factors influencing performance beyond entry mode. The schema lacks specific attributes or relationships that would allow for a comprehensive analysis of the factors affecting academic performance.', 'feedback': ''}
2025-08-15 10:05:32,930 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:32,930 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:32,930 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What other factors, aside from entry mode, might influence the academic performance of boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance, specifically for boys at ITC University. However, the schema does not provide direct information about academic performance metrics or specific factors influencing it. While there are tables related to students, programs, and academic records, there is no clear linkage or data that directly addresses the question about factors influencing performance beyond entry mode. The schema lacks specific attributes or relationships that would allow for a comprehensive analysis of the factors affecting academic performance.', 'feedback': ''}
2025-08-15 10:05:34,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:35,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:35,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:35,639 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the differences in average GPAs among the various programs at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to differences in average GPAs among various programs at ITC University. While the schema contains tables related to GPAs (like 'student_semester_gpas' and 'programs'), it does not provide direct information about the factors influencing GPA differences, such as course difficulty, teaching quality, or student demographics. The schema lacks qualitative data or metrics that could be analyzed to determine these contributing factors.", 'feedback': ''}
2025-08-15 10:05:35,639 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:35,639 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:35,639 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the differences in average GPAs among the various programs at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to differences in average GPAs among various programs at ITC University. While the schema contains tables related to GPAs (like 'student_semester_gpas' and 'programs'), it does not provide direct information about the factors influencing GPA differences, such as course difficulty, teaching quality, or student demographics. The schema lacks qualitative data or metrics that could be analyzed to determine these contributing factors.", 'feedback': ''}
2025-08-15 10:05:36,606 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:37,757 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:37,770 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific data collection methods are currently in place at ITC University, and how might they be improved to capture performance levels more effectively?', 'answerable': False, 'reasoning': 'The question asks for specific data collection methods currently in place at ITC University and suggestions for improvement. However, the provided schema does not contain any tables or fields that explicitly describe data collection methods or performance levels. The schema primarily focuses on various entities related to institutions, students, courses, and assessments, but lacks details on data collection methodologies or performance evaluation strategies. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:05:37,770 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:37,770 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:37,770 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific data collection methods are currently in place at ITC University, and how might they be improved to capture performance levels more effectively?', 'answerable': False, 'reasoning': 'The question asks for specific data collection methods currently in place at ITC University and suggestions for improvement. However, the provided schema does not contain any tables or fields that explicitly describe data collection methods or performance levels. The schema primarily focuses on various entities related to institutions, students, courses, and assessments, but lacks details on data collection methodologies or performance evaluation strategies. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:05:37,915 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:37,945 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance differences between girls and boys at ITC University. However, the provided schema does not contain specific data related to performance metrics, gender comparisons, or any qualitative factors that could explain performance differences. While there are tables related to students, grades, and programs, there is no direct way to extract or analyze the factors influencing performance based on gender from the schema.', 'feedback': ''}
2025-08-15 10:05:37,945 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:37,945 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:37,945 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance differences between girls and boys at ITC University. However, the provided schema does not contain specific data related to performance metrics, gender comparisons, or any qualitative factors that could explain performance differences. While there are tables related to students, grades, and programs, there is no direct way to extract or analyze the factors influencing performance based on gender from the schema.', 'feedback': ''}
2025-08-15 10:05:38,270 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:40,793 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:40,793 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:40,815 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What other factors, aside from entry mode, might influence the academic performance of boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance specifically for boys at ITC University. While the schema contains various tables related to academic performance, entry modes, and student demographics, it does not provide direct information or analysis regarding the influence of these factors on academic performance. The schema lacks specific data or metrics that would allow for a comprehensive analysis of how different factors (beyond entry mode) affect the academic performance of male students at a specific institution. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-15 10:05:40,816 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:40,816 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:40,816 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What other factors, aside from entry mode, might influence the academic performance of boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance specifically for boys at ITC University. While the schema contains various tables related to academic performance, entry modes, and student demographics, it does not provide direct information or analysis regarding the influence of these factors on academic performance. The schema lacks specific data or metrics that would allow for a comprehensive analysis of how different factors (beyond entry mode) affect the academic performance of male students at a specific institution. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-15 10:05:41,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:42,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:42,078 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the differences in average GPAs among the various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of the factors contributing to differences in average GPAs among various programs at ITC University. While the schema contains data related to GPAs (e.g., `student_semester_gpas`, `programs`), it does not provide direct information about the factors influencing these GPAs, such as course difficulty, teaching quality, or student demographics. The schema lacks qualitative data or specific metrics that could be analyzed to determine the reasons behind GPA differences. Therefore, the question cannot be answered based solely on the provided schema.', 'feedback': ''}
2025-08-15 10:05:42,078 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:42,078 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:42,078 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the differences in average GPAs among the various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of the factors contributing to differences in average GPAs among various programs at ITC University. While the schema contains data related to GPAs (e.g., `student_semester_gpas`, `programs`), it does not provide direct information about the factors influencing these GPAs, such as course difficulty, teaching quality, or student demographics. The schema lacks qualitative data or specific metrics that could be analyzed to determine the reasons behind GPA differences. Therefore, the question cannot be answered based solely on the provided schema.', 'feedback': ''}
2025-08-15 10:05:43,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:43,401 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:43,525 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:43,544 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific data collection methods are currently in place at ITC University, and how might they be improved to capture performance levels more effectively?', 'answerable': False, 'reasoning': 'The question asks for specific data collection methods currently in place at ITC University and suggestions for improvement. However, the provided schema does not contain any tables or fields that explicitly describe data collection methods or performance levels. The schema primarily focuses on various entities related to institutions, students, courses, and assessments, but lacks direct references to data collection methodologies or performance evaluation strategies. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:05:43,544 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:43,545 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:43,545 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific data collection methods are currently in place at ITC University, and how might they be improved to capture performance levels more effectively?', 'answerable': False, 'reasoning': 'The question asks for specific data collection methods currently in place at ITC University and suggestions for improvement. However, the provided schema does not contain any tables or fields that explicitly describe data collection methods or performance levels. The schema primarily focuses on various entities related to institutions, students, courses, and assessments, but lacks direct references to data collection methodologies or performance evaluation strategies. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:05:43,575 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:43,589 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data related to gender performance comparisons, such as academic results categorized by gender, or any qualitative factors that might influence performance. While there are tables related to students, courses, and results, there is no direct way to extract or analyze the factors influencing performance based on gender from the schema.', 'feedback': ''}
2025-08-15 10:05:43,589 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:43,589 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:43,589 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data related to gender performance comparisons, such as academic results categorized by gender, or any qualitative factors that might influence performance. While there are tables related to students, courses, and results, there is no direct way to extract or analyze the factors influencing performance based on gender from the schema.', 'feedback': ''}
2025-08-15 10:05:44,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:45,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:45,978 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What other factors, aside from entry mode, might influence the academic performance of boys at ITC University?', 'answerable': False, 'reasoning': "The question asks for factors influencing academic performance specifically for boys at ITC University. While the schema contains various tables related to academic performance, entry modes, and student demographics, it does not provide direct information or analysis regarding the influence of these factors on academic performance. The schema lacks specific data on gender-based performance metrics or comparative analysis of different factors affecting boys' academic performance. Therefore, the question cannot be answered directly or indirectly from the provided schema.", 'feedback': ''}
2025-08-15 10:05:45,978 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:45,978 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:45,978 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What other factors, aside from entry mode, might influence the academic performance of boys at ITC University?', 'answerable': False, 'reasoning': "The question asks for factors influencing academic performance specifically for boys at ITC University. While the schema contains various tables related to academic performance, entry modes, and student demographics, it does not provide direct information or analysis regarding the influence of these factors on academic performance. The schema lacks specific data on gender-based performance metrics or comparative analysis of different factors affecting boys' academic performance. Therefore, the question cannot be answered directly or indirectly from the provided schema.", 'feedback': ''}
2025-08-15 10:05:46,143 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:47,639 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:47,658 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the differences in average GPAs among the various programs at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to differences in average GPAs among various programs at ITC University. While the schema contains tables related to GPAs (like 'student_semester_gpas' and 'programs'), it does not provide direct information about the factors influencing GPA differences, such as course difficulty, teaching quality, or student demographics. The schema lacks qualitative data or metrics that could be analyzed to determine these contributing factors. Therefore, the question cannot be answered based on the available schema.", 'feedback': ''}
2025-08-15 10:05:47,659 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:05:47,659 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:05:47,659 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the differences in average GPAs among the various programs at ITC University?', 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to differences in average GPAs among various programs at ITC University. While the schema contains tables related to GPAs (like 'student_semester_gpas' and 'programs'), it does not provide direct information about the factors influencing GPA differences, such as course difficulty, teaching quality, or student demographics. The schema lacks qualitative data or metrics that could be analyzed to determine these contributing factors. Therefore, the question cannot be answered based on the available schema.", 'feedback': ''}
2025-08-15 10:05:48,975 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:50,372 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:05:55,331 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:02,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:05,049 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:07,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:09,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:16,191 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:23,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:26,531 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:29,207 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:31,031 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:37,533 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:48,272 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:48,274 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there particular subjects or courses where boys excel or struggle at ITC University?', 'sql': "SELECT es.subject, c.title, AVG(CASE WHEN s.sex = 'M' THEN eg.grade END) AS average_grade_boys, COUNT(CASE WHEN s.sex = 'M' AND eg.grade IN ('A', 'A1', 'B', 'B2', 'B3') THEN 1 END) AS count_boys_excelling, COUNT(CASE WHEN s.sex = 'M' AND eg.grade IN ('D', 'D7', 'E', 'E8', 'F', 'F9', 'FAIL') THEN 1 END) AS count_boys_struggling\nFROM exam_grades eg\nJOIN exam_subjects es ON eg.exam_type_id = es.exam_type_id\nJOIN courses c ON es.id = c.id\nJOIN students s ON s.id = eg.id\nGROUP BY es.subject, c.title\nORDER BY average_grade_boys DESC\nLIMIT 20;", 'correct': False, 'reasoning': "The SQL query attempts to analyze the performance of male students in various subjects and courses by calculating their average grades and counting the number of students excelling or struggling. However, there is a critical issue with the JOIN conditions. The query joins the 'students' table on 's.id = eg.id', which is incorrect because 'eg.id' refers to the exam grade ID, not the student ID. This will lead to incorrect results as it does not properly associate grades with the corresponding students. Additionally, the query does not specify the institution (ITC University) in the WHERE clause, which is necessary to filter the results to that specific university.", 'feedback': "To improve the SQL query, ensure that the join between 'exam_grades' and 'students' is based on the correct student identifier. You may need to include a foreign key in the 'exam_grades' table that references the student ID. Additionally, add a WHERE clause to filter results specifically for ITC University, which could be done by checking the 'institution_id' in the 'students' table. Finally, consider whether the LIMIT clause is necessary for the analysis, as it may exclude relevant subjects or courses.", 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-15 10:06:48,274 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:06:48,275 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-15 10:06:51,040 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:53,672 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:06:54,865 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:01,160 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:08,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:11,174 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:14,273 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:15,400 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:20,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:26,538 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:29,114 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:31,794 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:33,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:40,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:47,974 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:52,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:54,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:07:55,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:02,404 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:08,349 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:11,344 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:15,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:16,614 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:23,538 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:31,621 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:31,626 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there particular subjects or courses where boys excel or struggle at ITC University?', 'sql': "SELECT cs.title AS course_title, es.subject AS subject_name, AVG(ar.finalscore) AS average_score, COUNT(CASE WHEN s.sex = 'M' THEN 1 END) AS boys_count\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN courses cs ON ar.course_id = cs.id\nJOIN exam_subjects es ON ar.exam_type_id = es.id\nWHERE s.sex = 'M'\nGROUP BY cs.title, es.subject\nORDER BY average_score DESC\nLIMIT 20;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the average scores of male students in various courses and subjects by joining the relevant tables. It calculates the average final score for boys in each course and subject, which directly addresses the question of where boys excel or struggle. The use of GROUP BY allows for aggregation of scores by course and subject, and the ORDER BY clause sorts the results by average score, which is relevant for identifying excellence or struggle.', 'feedback': "The query is well-structured and answers the question effectively. However, to provide a clearer picture of where boys struggle, it might be beneficial to include a threshold for what constitutes 'struggling' (e.g., average scores below a certain value) or to compare the average scores of boys against the overall average scores in the same subjects or courses."}
2025-08-15 10:08:31,626 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:08:31,626 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:08:34,091 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:34,813 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:36,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:38,355 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:42,392 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:42,410 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence the academic performance of boys in various subjects at ITC University, even in the absence of specific data?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance, which is a qualitative inquiry rather than a quantitative one. The provided schema contains tables related to students, courses, assessments, and other academic-related data, but it does not include any specific qualitative factors or insights that could influence academic performance, such as social, psychological, or environmental factors. Therefore, the question cannot be answered using the existing schema.', 'feedback': ''}
2025-08-15 10:08:42,410 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:08:42,410 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:08:42,410 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence the academic performance of boys in various subjects at ITC University, even in the absence of specific data?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance, which is a qualitative inquiry rather than a quantitative one. The provided schema contains tables related to students, courses, assessments, and other academic-related data, but it does not include any specific qualitative factors or insights that could influence academic performance, such as social, psychological, or environmental factors. Therefore, the question cannot be answered using the existing schema.', 'feedback': ''}
2025-08-15 10:08:44,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:47,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:47,279 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence the academic performance of boys in various subjects at ITC University, even in the absence of specific data?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance, which is a qualitative inquiry rather than a quantitative one. The provided schema contains tables related to students, courses, assessments, and other academic-related data, but it does not include any specific qualitative factors or insights that could influence academic performance. Additionally, the schema does not provide any direct data or metrics that could be analyzed to derive such factors. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:08:47,279 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:08:47,279 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:08:47,280 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence the academic performance of boys in various subjects at ITC University, even in the absence of specific data?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance, which is a qualitative inquiry rather than a quantitative one. The provided schema contains tables related to students, courses, assessments, and other academic-related data, but it does not include any specific qualitative factors or insights that could influence academic performance. Additionally, the schema does not provide any direct data or metrics that could be analyzed to derive such factors. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:08:49,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:51,397 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:08:51,417 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence the academic performance of boys in various subjects at ITC University, even in the absence of specific data?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance, which is a qualitative inquiry rather than a quantitative one. The schema provided contains tables related to institutions, students, courses, and various academic records, but it does not include any specific data or attributes that would directly address the qualitative factors influencing academic performance, such as socio-economic status, teaching quality, or personal circumstances. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-15 10:08:51,417 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:08:51,417 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:08:51,417 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence the academic performance of boys in various subjects at ITC University, even in the absence of specific data?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance, which is a qualitative inquiry rather than a quantitative one. The schema provided contains tables related to institutions, students, courses, and various academic records, but it does not include any specific data or attributes that would directly address the qualitative factors influencing academic performance, such as socio-economic status, teaching quality, or personal circumstances. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-15 10:08:51,418 - root - INFO - [{'sex': 'F', 'average_score': 69.0, 'total_students': 65}, {'sex': 'M', 'average_score': 54.86, 'total_students': 71}]
2025-08-15 10:08:51,418 - root - INFO - [{'program_name': 'Bachelor Of Science (Accounting Education)', 'average_gpa': 2.961005}, {'program_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'average_gpa': 2.723333}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - R', 'average_gpa': 2.2385}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - S', 'average_gpa': 2.62625}, {'program_name': 'Bachelor Of Science (Biological Sciences Education)', 'average_gpa': 2.065692}, {'program_name': 'Bachelor Of Science (Integrated Science Education)', 'average_gpa': 2.984242}, {'program_name': 'Bachelor Of Science (Information Technology Education)', 'average_gpa': 2.454944}, {'program_name': 'Bachelor Of Science (Management Education)', 'average_gpa': 1.1075}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'average_gpa': 2.002857}, {'program_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'average_gpa': 2.56}, {'program_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education)', 'average_gpa': 2.56}, {'program_name': 'Bachelor Of Science (Mathematics Education)', 'average_gpa': 2.295294}, {'program_name': 'Diploma In Education', 'average_gpa': 3.106875}, {'program_name': 'Bachelor Of Science (Information Technology)', 'average_gpa': 2.831034}, {'program_name': 'Diploma In Education - M', 'average_gpa': 3.276667}, {'program_name': 'Bachelor Of Business Administration (Management) - E', 'average_gpa': 3.158}, {'program_name': 'Diploma In Education - KS', 'average_gpa': 2.959}, {'program_name': 'Diploma In Education - TM', 'average_gpa': 0.5}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'average_gpa': 1.745}, {'program_name': 'Bachelor Of Arts (English Language Education)', 'average_gpa': 2.696923}]
2025-08-15 10:08:51,418 - root - INFO - 'No results'
2025-08-15 10:08:51,418 - root - INFO - 'No results'
2025-08-15 10:08:51,418 - root - INFO - 'No results'
2025-08-15 10:08:51,418 - root - INFO - 'No results'
2025-08-15 10:08:51,418 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-15 10:09:02,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:09:02,832 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-15 10:09:16,503 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:09:16,511 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:16,511 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 10:09:16,511 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 10:09:16,512 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the performance of boys at ITC University compare to that of girls?...
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, the performance of girls is significantly better than that of boys. Girls have an...
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: bar_chart
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': 'F', 'average_score': 69.0, 'total_students': 65}, {'sex': 'M', 'average_score': 54.86, 'to...
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_scores_by_gender
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-15 10:09:16,512 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-15 10:09:16,512 - celery.redirected - WARNING - [{'sex': 'F', 'average_score': 69.0, 'total_students': 65}, {'sex': 'M', 'average_score': 54.86, 'total_students': 71}]
2025-08-15 10:09:16,512 - celery.redirected - WARNING - ================================= 
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: average_scores_by_gender
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-15 10:09:16,512 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:16,512 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 10:09:16,512 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-15 10:09:16,512 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 10:09:16,513 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What are the average grades or GPAs for boys at ITC University across different programs?...
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The average GPAs for boys at ITC University across different programs are as follows: 
- Bachelor Of...
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'program_name': 'Bachelor Of Science (Accounting Education)', 'average_gpa': 2.961005}, {'program_...
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_gpa_by_program_for_boys
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-15 10:09:16,513 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-15 10:09:16,513 - celery.redirected - WARNING - [{'program_name': 'Bachelor Of Science (Accounting Education)', 'average_gpa': 2.961005}, {'program_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'average_gpa': 2.723333}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - R', 'average_gpa': 2.2385}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - S', 'average_gpa': 2.62625}, {'program_name': 'Bachelor Of Science (Biological Sciences Education)', 'average_gpa': 2.065692}, {'program_name': 'Bachelor Of Science (Integrated Science Education)', 'average_gpa': 2.984242}, {'program_name': 'Bachelor Of Science (Information Technology Education)', 'average_gpa': 2.454944}, {'program_name': 'Bachelor Of Science (Management Education)', 'average_gpa': 1.1075}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'average_gpa': 2.002857}, {'program_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'average_gpa': 2.56}, {'program_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education)', 'average_gpa': 2.56}, {'program_name': 'Bachelor Of Science (Mathematics Education)', 'average_gpa': 2.295294}, {'program_name': 'Diploma In Education', 'average_gpa': 3.106875}, {'program_name': 'Bachelor Of Science (Information Technology)', 'average_gpa': 2.831034}, {'program_name': 'Diploma In Education - M', 'average_gpa': 3.276667}, {'program_name': 'Bachelor Of Business Administration (Management) - E', 'average_gpa': 3.158}, {'program_name': 'Diploma In Education - KS', 'average_gpa': 2.959}, {'program_name': 'Diploma In Education - TM', 'average_gpa': 0.5}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'average_gpa': 1.745}, {'program_name': 'Bachelor Of Arts (English Language Education)', 'average_gpa': 2.696923}]
2025-08-15 10:09:16,513 - celery.redirected - WARNING - ================================= 
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: average_gpa_by_program_for_boys
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-15 10:09:16,513 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:16,513 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 10:09:16,513 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 2
2025-08-15 10:09:16,513 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 10:09:16,513 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there particular subjects or courses where boys excel or struggle at ITC University?...
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 2:
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there particular subjects or courses where boys excel or struggle at ITC University?...
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there is currently no data available regarding th...
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: boys_performance_in_courses
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 2 documents, 0 with data_returned=True
2025-08-15 10:09:16,514 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:16,514 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 10:09:16,514 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 10:09:16,514 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do boys' performance levels at ITC University vary by academic year or semester?...
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there may be no recorded performance levels for b...
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: boys_performance_levels_by_academic_year
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-15 10:09:16,514 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:16,514 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-15 10:09:16,514 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-15 10:09:16,514 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-15 10:09:16,514 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-15 10:09:16,515 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any notable differences in performance based on the entry mode of boys at ITC University?...
2025-08-15 10:09:16,515 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there are no recorded performance differences bas...
2025-08-15 10:09:16,515 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-15 10:09:16,515 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-15 10:09:16,515 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: performance_by_entry_mode_boys
2025-08-15 10:09:16,515 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-15 10:09:16,515 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-15 10:09:16,515 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 6
2025-08-15 10:09:16,515 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:16,515 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-15 10:09:16,515 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 6 documents
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Content: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Content: Question: What are the average grades or GPAs for boys at ITC University across different programs?
...
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:47.659350+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_program_for_boys'}
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Content: Question: Are there particular subjects or courses where boys excel or struggle at ITC University?
A...
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:08:51.417586+00:00', 'data_returned': False}
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Content: Question: Are there particular subjects or courses where boys excel or struggle at ITC University?
A...
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-15T10:08:51.417586+00:00', 'data_returned': False}
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Content: Question: How do boys' performance levels at ITC University vary by academic year or semester?
Answe...
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.545139+00:00', 'data_returned': False}
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Content: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:45.978634+00:00', 'data_returned': False}
2025-08-15 10:09:16,515 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 2/6
2025-08-15 10:09:16,727 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.212s]
2025-08-15 10:09:18,275 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 10:09:19,395 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.615s]
2025-08-15 10:09:19,396 - UPSERT_DOCS - INFO - ✅ Successfully upserted 6 documents to Elasticsearch
2025-08-15 10:09:19,396 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 3
2025-08-15 10:09:19,397 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-15 10:09:19,397 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:19,397 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-15 10:09:19,397 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:19,398 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/3...
2025-08-15 10:09:19,978 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:09:19,982 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:19,982 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-15 10:09:19,982 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:19,982 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'academic performance boys ITC University grades GPAs comparison girls'
2025-08-15 10:09:19,982 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-15 10:09:19,982 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-15 10:09:20,108 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-15 10:09:20,109 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 137
2025-08-15 10:09:20,230 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.120s]
2025-08-15 10:09:20,230 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 73
2025-08-15 10:09:20,358 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-15 10:09:20,359 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 21
2025-08-15 10:09:20,483 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.124s]
2025-08-15 10:09:20,483 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 11
2025-08-15 10:09:20,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 10:09:21,054 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.129s]
2025-08-15 10:09:21,055 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-15 10:09:21,055 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:09:21,055 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:09:21,055 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:21,055 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-15 10:09:21,055 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:21,055 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:21,055 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:21,055 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:21,055 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:21,056 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:11.769861+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:21,056 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:09:21,056 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_gender
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_gender
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   📄 Doc 5: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:11.769861+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (2242 chars):
2025-08-15 10:09:21,057 - app.chains.section_writer - INFO -    Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At ITC University, the performance of girls is significantly better than that of boys. Girls have an average score of 69.0, while boys have an average score of 54.86. Additionally, there are 65 girls and 71 boys in the sample, indicating that while there are slightly more boys, the girls outperform them on average.
Data Tag: average_scores_by_gender

Question: How do boys' academic results compare to t...
2025-08-15 10:09:23,720 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:09:23,724 - app.chains.section_writer - INFO - 🤖 AI generated section (701 chars):
2025-08-15 10:09:23,724 - app.chains.section_writer - INFO -    This report examines the academic performance of boys at ITC University, focusing on their grades and GPAs in comparison to their female peers. The findings indicate that boys are significantly underperforming relative to girls. Specifically, girls have an average score of 69.0, while boys have an a...
2025-08-15 10:09:23,724 - app.chains.section_writer - INFO - ✅ Found 3 data tag placeholders: ['average_scores_by_gender', 'average_final_scores_by_gender', 'average_final_scores_by_sex']
2025-08-15 10:09:23,724 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 646 characters
2025-08-15 10:09:23,725 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/3...
2025-08-15 10:09:24,441 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:09:24,447 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:24,447 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-15 10:09:24,447 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:24,447 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'gender performance comparison average scores'
2025-08-15 10:09:24,448 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-15 10:09:24,448 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-15 10:09:24,576 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-15 10:09:24,576 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 137
2025-08-15 10:09:24,697 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.121s]
2025-08-15 10:09:24,697 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 73
2025-08-15 10:09:24,825 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-15 10:09:24,826 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 21
2025-08-15 10:09:24,945 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.119s]
2025-08-15 10:09:24,945 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 11
2025-08-15 10:09:25,459 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 10:09:25,590 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.128s]
2025-08-15 10:09:25,591 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-15 10:09:25,591 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:09:25,592 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:09:25,592 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:25,592 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-15 10:09:25,592 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:25,592 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:25,592 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:25,592 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:25,592 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:25,592 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:11.769861+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:25,593 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At IT...
2025-08-15 10:09:25,593 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:43.589910+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_gender'}
2025-08-15 10:09:25,593 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_by_gender
2025-08-15 10:09:25,593 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:25,593 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-15 10:09:25,593 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_gender
2025-08-15 10:09:25,593 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:25,593 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:25,593 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-15 10:09:25,594 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:25,594 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:25,594 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-15 10:09:25,594 - app.chains.section_writer - INFO -   📄 Doc 5: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-15 10:09:25,594 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:11.769861+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-15 10:09:25,594 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-15 10:09:25,594 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (2242 chars):
2025-08-15 10:09:25,594 - app.chains.section_writer - INFO -    Question: How does the performance of boys at ITC University compare to that of girls?
Answer: At ITC University, the performance of girls is significantly better than that of boys. Girls have an average score of 69.0, while boys have an average score of 54.86. Additionally, there are 65 girls and 71 boys in the sample, indicating that while there are slightly more boys, the girls outperform them on average.
Data Tag: average_scores_by_gender

Question: How do boys' academic results compare to t...
2025-08-15 10:09:27,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:09:27,665 - app.chains.section_writer - INFO - 🤖 AI generated section (456 chars):
2025-08-15 10:09:27,665 - app.chains.section_writer - INFO -    ## 2. Comparative Performance Analysis  
### 2.1 Gender Performance Comparison  
The average scores for girls and boys at ITC University reveal a significant performance gap. Girls achieved an average score of 69.0, while boys had a lower average score of 54.86. The sample consisted of 65 girls and ...
2025-08-15 10:09:27,666 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_scores_by_gender']
2025-08-15 10:09:27,666 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 442 characters
2025-08-15 10:09:27,666 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/3...
2025-08-15 10:09:28,293 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:09:28,304 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:28,304 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-15 10:09:28,304 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:28,304 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'average GPAs boys programs'
2025-08-15 10:09:28,304 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-15 10:09:28,304 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-15 10:09:28,425 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.121s]
2025-08-15 10:09:28,426 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 137
2025-08-15 10:09:28,544 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.118s]
2025-08-15 10:09:28,544 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 73
2025-08-15 10:09:28,664 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.120s]
2025-08-15 10:09:28,664 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 21
2025-08-15 10:09:28,788 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.123s]
2025-08-15 10:09:28,788 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 11
2025-08-15 10:09:29,404 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-15 10:09:29,544 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.136s]
2025-08-15 10:09:29,545 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-15 10:09:29,545 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What are the average grades or GPAs for boys at ITC University across different programs?
...
2025-08-15 10:09:29,545 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:47.659350+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_program_for_boys'}
2025-08-15 10:09:29,545 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-15 10:09:29,545 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-15 10:09:29,545 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:09:29,545 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:09:29,545 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-15 10:09:29,545 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-15 10:09:29,545 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:09:29,546 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:09:29,546 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What are the average grades or GPAs for boys at ITC University across different programs?
...
2025-08-15 10:09:29,546 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-15T10:05:47.659350+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_program_for_boys'}
2025-08-15 10:09:29,546 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_gpa_by_program_for_boys
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   📄 Doc 2: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_gpa_by_entry_mode
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   📄 Doc 4: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_performance_in_courses
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (5163 chars):
2025-08-15 10:09:29,547 - app.chains.section_writer - INFO -    Question: What are the average grades or GPAs for boys at ITC University across different programs?
Answer: The average GPAs for boys at ITC University across different programs are as follows: 
- Bachelor Of Science (Accounting Education): 2.96 
- Post Diploma Bachelor Of Science (Accounting Education): 2.72 
- Bachelor Of Science (Construction Technology Education) - R: 2.24 
- Bachelor Of Science (Construction Technology Education) - S: 2.63 
- Bachelor Of Science (Biological Sciences Educati...
2025-08-15 10:09:37,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:09:37,346 - app.chains.section_writer - INFO - 🤖 AI generated section (1802 chars):
2025-08-15 10:09:37,347 - app.chains.section_writer - INFO -    ## 3. Average Grades and GPAs for Boys  

### 3.1 Overview of GPA Across Programs  
The average GPAs for boys in various programs at ITC University are as follows:  
- Bachelor Of Science (Accounting Education): 2.96  
- Post Diploma Bachelor Of Science (Accounting Education): 2.72  
- Bachelor Of S...
2025-08-15 10:09:37,347 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_gpa_by_program_for_boys']
2025-08-15 10:09:37,347 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 1812 characters
2025-08-15 10:09:37,348 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:09:37,348 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-15 10:09:37,348 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:09:37,349 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-15 10:09:37,349 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 4
2025-08-15 10:09:37,349 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 5
2025-08-15 10:09:37,349 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 2
2025-08-15 10:09:37,349 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250815_095627.log
2025-08-15 10:09:37,351 - celery.app.trace - INFO - Task generate_streaming_report[ab39d9f7-f095-4a5b-91cf-c025dac48364] succeeded in 341.02255137499924s: {'outline': '# Report on Boys\' Performance at ITC University

## 1. Introduction  
   - This report examines the academic performance of boys at ITC University, focusing on their grades and GPAs in comparison to their female peers. The findings indicate that boys are significantly underperforming relative to girls, highlighting the need for targeted interventions to address this disparity.

## 2. Comparative Performance Analysis  
   ### 2.1 Gender Performance Comparison  
   - Summary of average scores:  
     - Girls: 69.0  
     - Boys: 54.86  
   - Sample size: 65 girls vs. 71 boys  
   - Conclusion: Girls significantly outperform boys on average.

## 3. Average Grades and GPAs for Boys  
   ### 3.1 Overview of GPA Across Programs  
   - Presentation of average GPAs for boys in various programs:  
     - Bachelor Of Science (Accounting Education): 2.96  
     - Post Diploma Bachelor Of Science (Accounting Education): 2.72  
     - Bachelor Of Science (Construction Technology Education) - R: 2.24  
     -...', ...}
2025-08-15 10:27:04,507 - celery.worker.strategy - INFO - Task generate_streaming_report[bd6a7fe9-f247-45cf-a8d6-4816b7fac27b] received
2025-08-15 10:27:04,507 - app.tasks.report_task - INFO - starting streaming report generation...
2025-08-15 10:27:04,508 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:27:04,508 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: c24a461e-2ee5-41de-b186-b9d9c9900d92
2025-08-15 10:27:04,508 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:27:04,508 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are boys performing at ITC University'
2025-08-15 10:27:04,508 - REPORT_REQUEST - INFO - 🆔 Task ID: c24a461e-2ee5-41de-b186-b9d9c9900d92
2025-08-15 10:27:04,508 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-15T10:27:04.508417
2025-08-15 10:27:04,631 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.123s]
2025-08-15 10:27:04,632 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 137
2025-08-15 10:27:04,760 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-15 10:27:04,760 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 73
2025-08-15 10:27:04,893 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.132s]
2025-08-15 10:27:04,893 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-15 10:27:04,893 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (24 docs)
2025-08-15 10:27:04,893 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (21 docs)
2025-08-15 10:27:04,894 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (19 docs)
2025-08-15 10:27:04,894 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-15 10:27:04,894 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-15 10:27:04,894 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-15 10:27:04,894 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-15 10:27:04,894 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-15 10:27:04,894 - ELASTICSEARCH_STATE - INFO -   - 'Which instituion has the most students?' (6 docs)
2025-08-15 10:27:04,894 - ELASTICSEARCH_STATE - INFO -   - 'How are students performing at ITC University?' (4 docs)
2025-08-15 10:27:04,895 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:27:04,895 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-15 10:27:04,895 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:27:04,895 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How are boys performing at ITC University
2025-08-15 10:27:04,895 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-15 10:27:04,895 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-15 10:27:18,148 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:18,163 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-15 10:27:22,640 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:22,660 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-15 10:27:24,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:24,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:25,309 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:25,309 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:25,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:25,428 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:25,548 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:25,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:26,222 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:26,223 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:26,284 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-15 10:27:29,123 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:29,170 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:29,183 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:29,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:29,310 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:31,572 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:31,615 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:31,633 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "How do boys' performance levels vary across different faculties or departments at ITC University?", 'answerable': False, 'reasoning': "The question asks about the performance levels of boys across different faculties or departments at ITC University. However, the provided schema does not contain specific information about faculties or departments, nor does it explicitly categorize performance levels by gender. While there are tables related to students and their performance (like 'assessment_results' and 'students'), there is no direct link to faculties or departments in the schema. Therefore, without additional information or tables that categorize students by faculty or department, this question cannot be answered.", 'feedback': ''}
2025-08-15 10:27:31,633 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:27:31,633 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:27:31,633 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "How do boys' performance levels vary across different faculties or departments at ITC University?", 'answerable': False, 'reasoning': "The question asks about the performance levels of boys across different faculties or departments at ITC University. However, the provided schema does not contain specific information about faculties or departments, nor does it explicitly categorize performance levels by gender. While there are tables related to students and their performance (like 'assessment_results' and 'students'), there is no direct link to faculties or departments in the schema. Therefore, without additional information or tables that categorize students by faculty or department, this question cannot be answered.", 'feedback': ''}
2025-08-15 10:27:31,794 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:31,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:32,101 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:33,154 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:33,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:33,382 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:33,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:35,105 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:38,119 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:39,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:39,214 - celery.redirected - WARNING - ❌ Exception in SQL generation: 'core.faculties'
2025-08-15 10:27:39,219 - celery.redirected - WARNING - Traceback (most recent call last):
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/Deep Research/deep-research-backend/app/chains/composite/interview.py", line 32, in interview
    sql_query_evaluation = sql_gen_veri_chain.invoke(follow_up)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 4771, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 1940, in _call_with_config
    context.run(
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 4629, in _invoke
    output = call_func_with_variable_args(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/Deep Research/deep-research-backend/app/chains/composite/sql_generation_and_verification.py", line 101, in sql_gen_veri
    question_schema_dict = relevant_tables_schemas_extractor_chain.invoke(question_tables_dict)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 4771, in invoke
    return self._call_with_config(
           ^^^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 1940, in _call_with_config
    context.run(
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/base.py", line 4629, in _invoke
    output = call_func_with_variable_args(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/langchain_core/runnables/config.py", line 428, in call_func_with_variable_args
    return func(input, **kwargs)  # type: ignore[call-arg]
           ^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/Deep Research/deep-research-backend/app/chains/relevant_schema_info_filter.py", line 41, in <lambda>
    "tables_schema": get_relevant_tables_schema(output_from_first["tables"]),
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/Deep Research/deep-research-backend/app/chains/relevant_schema_info_filter.py", line 15, in get_relevant_tables_schema
    relevant_tables_json[table] = db_json[table]
                                  ~~~~~~~^^^^^^^
2025-08-15 10:27:39,220 - celery.redirected - WARNING - KeyError: 'core.faculties'
2025-08-15 10:27:40,815 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:40,982 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:41,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:42,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:42,468 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:45,198 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:45,208 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the performance of boys at ITC University compare to that of girls?', 'sql': "SELECT s.sex, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS number_of_students\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nWHERE s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY s.sex;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average final scores and the number of students for each sex (boys and girls) at ITC University. It joins the 'assessment_results' table with the 'students' table based on the student ID, filters the results to only include students from ITC University, and groups the results by sex. This directly addresses the question of comparing the performance of boys and girls.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be enhanced by explicitly labeling the output columns for clarity, such as renaming 'sex' to 'gender' or adding a more descriptive alias for 'average_score'."}
2025-08-15 10:27:45,209 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:27:45,209 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:27:45,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:46,536 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:46,546 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "Are there any trends in boys' performance over the past few academic years at ITC University?", 'sql': "SELECT academic_years.start_year, AVG(assessment_results.finalscore) AS average_score, COUNT(assessment_results.id) AS total_assessments\nFROM assessment_results\nJOIN students ON assessment_results.student_id = students.id\nJOIN academic_years ON assessment_results.semester_id = academic_years.id\nWHERE students.sex = 'M' AND academic_years.status = 'Active'\nGROUP BY academic_years.start_year\nORDER BY academic_years.start_year DESC\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly identifies trends in boys' performance by calculating the average final score of male students over the academic years. It joins the necessary tables (assessment_results, students, and academic_years) to filter for male students and only considers active academic years. The use of AVG() provides a measure of performance, and the GROUP BY clause allows for analysis over different years. The ORDER BY clause sorts the results in descending order of the start year, which is appropriate for trend analysis. The LIMIT clause is not necessary for answering the question but does not detract from the overall correctness.", 'feedback': 'The question could be clarified by specifying what kind of trends are being looked for (e.g., increasing or decreasing scores, comparison to previous years). Additionally, the SQL could be improved by removing the LIMIT clause, as it may restrict the results unnecessarily when analyzing trends over multiple years.'}
2025-08-15 10:27:46,546 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:27:46,546 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:27:48,484 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:48,498 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "How do boys' performance levels vary across different faculties or departments at ITC University?", 'answerable': False, 'reasoning': 'Too much uncertainty as to whether or not it can be answered', 'feedback': ''}
2025-08-15 10:27:48,498 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:27:48,498 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:27:48,498 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "How do boys' performance levels vary across different faculties or departments at ITC University?", 'answerable': False, 'reasoning': 'Too much uncertainty as to whether or not it can be answered', 'feedback': ''}
2025-08-15 10:27:49,844 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:50,449 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:50,455 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the academic programs or courses where boys are excelling or struggling at ITC University?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, ar.grade, COUNT(ar.id) AS total_students\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN courses c ON ar.course_id = c.id\nJOIN programs p ON ar.student_program_id = p.id\nWHERE s.sex = 'M'\nGROUP BY p.long_name, c.title, ar.grade\nORDER BY p.long_name, c.title, ar.grade\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly identifies the academic programs and courses attended by male students ('boys') by joining the relevant tables: assessment_results, students, courses, and programs. It filters the results to include only male students and groups the results by program name, course title, and grade, which allows for an analysis of performance (excelling or struggling) based on the grades. The use of COUNT(ar.id) provides a count of students per grade, which is relevant for understanding the performance distribution. The query also orders the results for better readability and limits the output to 20 records, which is a common practice for large datasets.", 'feedback': "The question could be clarified by specifying what metrics define 'excelling' or 'struggling' (e.g., specific grade thresholds). Additionally, the SQL could be improved by including a condition to filter grades that indicate excelling or struggling, rather than just grouping by grade, to provide more focused insights."}
2025-08-15 10:27:50,455 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:27:50,455 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:27:50,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:50,708 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any notable differences in performance based on entry modes or admission criteria for boys at ITC University?', 'sql': "SELECT em.entry_mode, af.description, AVG(ar.finalscore) AS average_score, COUNT(a.id) AS total_applicants\nFROM applicants a\nJOIN entry_modes em ON a.entry_mode_id = em.id\nJOIN admission_forms af ON a.form_id = af.id\nJOIN student_programs sp ON a.student_id = sp.student_id\nJOIN assessment_results ar ON sp.id = ar.student_program_id\nWHERE a.sex = 'M' AND a.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY em.entry_mode, af.description\nORDER BY average_score DESC\nLIMIT 20;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the performance of male applicants at ITC University by joining the relevant tables: applicants, entry_modes, admission_forms, student_programs, and assessment_results. It filters for male applicants and groups the results by entry mode and admission criteria (description), calculating the average score and total number of applicants for each group. This directly addresses the question about differences in performance based on entry modes and admission criteria.', 'feedback': "The query is well-structured and effectively answers the question. However, it could be improved by explicitly defining what constitutes 'notable differences' in performance. For example, adding a threshold for average scores or a statistical test could provide more insight into the significance of the differences observed."}
2025-08-15 10:27:50,708 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:27:50,708 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-15 10:27:50,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:51,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:51,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:52,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:52,865 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:52,938 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:53,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:53,966 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:53,991 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:54,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:54,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:54,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:55,227 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:55,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:56,325 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:56,660 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:56,685 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "How do boys' performance levels vary across different faculties or departments at ITC University?", 'answerable': False, 'reasoning': 'Too much uncertainty as to whether or not it can be answered', 'feedback': ''}
2025-08-15 10:27:56,686 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:27:56,686 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:27:56,686 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "How do boys' performance levels vary across different faculties or departments at ITC University?", 'answerable': False, 'reasoning': 'Too much uncertainty as to whether or not it can be answered', 'feedback': ''}
2025-08-15 10:27:56,774 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:57,208 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:57,229 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What steps could ITC University take to improve data collection and analysis regarding boys' academic performance?", 'answerable': False, 'reasoning': "The question is asking for recommendations on improving data collection and analysis regarding boys' academic performance. While the schema contains various tables related to students, academic years, assessment results, and other educational data, it does not provide specific insights or actionable steps for improving data collection and analysis processes. The schema lacks information on current practices, challenges, or specific metrics related to boys' academic performance, which are necessary to formulate a comprehensive answer.", 'feedback': ''}
2025-08-15 10:27:57,229 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:27:57,229 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:27:57,229 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What steps could ITC University take to improve data collection and analysis regarding boys' academic performance?", 'answerable': False, 'reasoning': "The question is asking for recommendations on improving data collection and analysis regarding boys' academic performance. While the schema contains various tables related to students, academic years, assessment results, and other educational data, it does not provide specific insights or actionable steps for improving data collection and analysis processes. The schema lacks information on current practices, challenges, or specific metrics related to boys' academic performance, which are necessary to formulate a comprehensive answer.", 'feedback': ''}
2025-08-15 10:27:57,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:58,721 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:58,736 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the lack of data. The schema includes various tables related to students, academic performance, and institutions, but it does not provide insights into the reasons for data absence or the specific context of boys' academic performance at a particular institution. Therefore, the question cannot be answered based on the available schema.", 'feedback': ''}
2025-08-15 10:27:58,736 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:27:58,737 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:27:58,737 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the lack of data. The schema includes various tables related to students, academic performance, and institutions, but it does not provide insights into the reasons for data absence or the specific context of boys' academic performance at a particular institution. Therefore, the question cannot be answered based on the available schema.", 'feedback': ''}
2025-08-15 10:27:59,009 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:59,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:59,543 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:59,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:27:59,694 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance difference between girls and boys at ITC University. However, the provided schema does not contain specific data related to gender performance comparisons, such as academic results categorized by gender or any metrics that would allow for such an analysis. While there are tables related to students, grades, and programs, there is no direct link or data that would enable a comprehensive answer to this question.', 'feedback': ''}
2025-08-15 10:27:59,695 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:27:59,695 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:27:59,695 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the performance difference between girls and boys at ITC University. However, the provided schema does not contain specific data related to gender performance comparisons, such as academic results categorized by gender or any metrics that would allow for such an analysis. While there are tables related to students, grades, and programs, there is no direct link or data that would enable a comprehensive answer to this question.', 'feedback': ''}
2025-08-15 10:28:01,455 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:28:01,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:28:01,758 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors, if any, do you believe could influence performance among boys at ITC University, despite the lack of differences based on entry modes or admission criteria?', 'answerable': False, 'reasoning': 'The question asks about factors influencing performance among boys at ITC University, specifically in relation to entry modes or admission criteria. However, the provided schema does not contain any specific data or tables that directly relate to performance metrics or factors influencing performance. While there are tables related to admissions, students, and programs, there is no clear indication of performance metrics or analysis of factors affecting performance. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:28:01,758 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:28:01,758 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:28:01,758 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors, if any, do you believe could influence performance among boys at ITC University, despite the lack of differences based on entry modes or admission criteria?', 'answerable': False, 'reasoning': 'The question asks about factors influencing performance among boys at ITC University, specifically in relation to entry modes or admission criteria. However, the provided schema does not contain any specific data or tables that directly relate to performance metrics or factors influencing performance. While there are tables related to admissions, students, and programs, there is no clear indication of performance metrics or analysis of factors affecting performance. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-15 10:28:02,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:28:02,285 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:28:02,302 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:28:02,318 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What steps could ITC University take to improve data collection and analysis regarding boys' academic performance?", 'answerable': False, 'reasoning': "The question is about improving data collection and analysis regarding boys' academic performance at ITC University. While the schema contains various tables related to students, academic years, assessment results, and other educational data, it does not provide specific insights or recommendations for improving data collection and analysis processes. The schema lacks information on current data collection methods, analysis techniques, or any existing issues that need addressing. Therefore, the question cannot be answered directly or indirectly from the provided schema.", 'feedback': ''}
2025-08-15 10:28:02,318 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:28:02,319 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:28:02,319 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What steps could ITC University take to improve data collection and analysis regarding boys' academic performance?", 'answerable': False, 'reasoning': "The question is about improving data collection and analysis regarding boys' academic performance at ITC University. While the schema contains various tables related to students, academic years, assessment results, and other educational data, it does not provide specific insights or recommendations for improving data collection and analysis processes. The schema lacks information on current data collection methods, analysis techniques, or any existing issues that need addressing. Therefore, the question cannot be answered directly or indirectly from the provided schema.", 'feedback': ''}
2025-08-15 10:28:02,710 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-15 10:28:02,710 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-15 10:28:02,710 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-15 10:28:02,711 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: <class 'celery.concurrency.solo.TaskPool'> does not implement kill_job
2025-08-15 10:28:02,712 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250815_095627.log
2025-08-15 10:28:02,714 - celery.app.trace - INFO - Task generate_streaming_report[bd6a7fe9-f247-45cf-a8d6-4816b7fac27b] succeeded in 58.205713374998595s: {'error': 'Error generating streaming report: <class \'celery.concurrency.solo.TaskPool\'> does not implement kill_job'}
2025-08-15 10:28:03,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-15 10:28:03,849 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the lack of data. The schema includes various tables related to students, academic performance, and institutions, but it does not provide insights into data availability or the reasons for data absence. Therefore, this question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-15 10:28:03,850 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-15 10:28:03,850 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-15 10:28:03,850 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on boys' academic performance at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on boys' academic performance at ITC University. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the lack of data. The schema includes various tables related to students, academic performance, and institutions, but it does not provide insights into data availability or the reasons for data absence. Therefore, this question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-15 10:28:03,973 - celery.redirected - WARNING - Exception ignored in: 
2025-08-15 10:28:03,973 - celery.redirected - WARNING - <module 'threading' from '/opt/anaconda3/lib/python3.12/threading.py'>
2025-08-15 10:28:03,973 - celery.redirected - WARNING - Traceback (most recent call last):
2025-08-15 10:28:03,973 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/threading.py", line 1594, in _shutdown
2025-08-15 10:28:03,976 - celery.redirected - WARNING -     
2025-08-15 10:28:03,976 - celery.redirected - WARNING - atexit_call()
2025-08-15 10:28:03,976 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/concurrent/futures/thread.py", line 31, in _python_exit
2025-08-15 10:28:03,977 - celery.redirected - WARNING -     
2025-08-15 10:28:03,977 - celery.redirected - WARNING - t.join()
2025-08-15 10:28:03,977 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/threading.py", line 1149, in join
2025-08-15 10:28:03,977 - celery.redirected - WARNING -     
2025-08-15 10:28:03,978 - celery.redirected - WARNING - self._wait_for_tstate_lock()
2025-08-15 10:28:03,978 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/threading.py", line 1169, in _wait_for_tstate_lock
2025-08-15 10:28:03,978 - celery.redirected - WARNING -     
2025-08-15 10:28:03,978 - celery.redirected - WARNING - if lock.acquire(block, timeout):
2025-08-15 10:28:03,978 - celery.redirected - WARNING -  
2025-08-15 10:28:03,978 - celery.redirected - WARNING -  
2025-08-15 10:28:03,978 - celery.redirected - WARNING -  
2025-08-15 10:28:03,978 - celery.redirected - WARNING -  
2025-08-15 10:28:03,978 - celery.redirected - WARNING -  
2025-08-15 10:28:03,978 - celery.redirected - WARNING -  
2025-08-15 10:28:03,978 - celery.redirected - WARNING -  
2025-08-15 10:28:03,978 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,978 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,979 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,980 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,980 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,981 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,982 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,982 - celery.redirected - WARNING - ^
2025-08-15 10:28:03,982 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/apps/worker.py", line 300, in _handle_request
2025-08-15 10:28:03,983 - celery.redirected - WARNING -     
2025-08-15 10:28:03,983 - celery.redirected - WARNING - callback(worker)
2025-08-15 10:28:03,983 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/apps/worker.py", line 329, in on_hard_shutdown
2025-08-15 10:28:03,983 - celery.redirected - WARNING -     
2025-08-15 10:28:03,983 - celery.redirected - WARNING - raise WorkerTerminate(EX_FAILURE)
2025-08-15 10:28:03,983 - celery.redirected - WARNING - celery.exceptions
2025-08-15 10:28:03,983 - celery.redirected - WARNING - .
2025-08-15 10:28:03,983 - celery.redirected - WARNING - WorkerTerminate
2025-08-15 10:28:03,983 - celery.redirected - WARNING - : 
2025-08-15 10:28:03,983 - celery.redirected - WARNING - 1

2025-08-08 08:18:43,614 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_081843.log
2025-08-08 08:18:43,614 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:18:43,614 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 3a2c64ac-7e64-48c4-98cc-a5ff97bfc93c
2025-08-08 08:18:43,614 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:18:43,614 - REPORT_REQUEST - INFO - 📝 Original Question: 'Which institution has the most students?'
2025-08-08 08:18:43,614 - REPORT_REQUEST - INFO - 🆔 Task ID: 3a2c64ac-7e64-48c4-98cc-a5ff97bfc93c
2025-08-08 08:18:43,614 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T08:18:43.614332
2025-08-08 08:18:43,742 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 08:18:43,742 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 15
2025-08-08 08:18:43,873 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 08:18:43,873 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 6
2025-08-08 08:18:44,005 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:0.131s]
2025-08-08 08:18:44,005 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (2):
2025-08-08 08:18:44,006 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 08:18:44,006 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (7 docs)
2025-08-08 08:18:44,006 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:18:44,006 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 08:18:44,006 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:18:44,007 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: Which institution has the most students?
2025-08-08 08:18:44,007 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: True
2025-08-08 08:18:44,007 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 08:18:54,276 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:18:54,294 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 08:18:54,295 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:18:54,295 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-08 08:18:54,295 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:18:54,295 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: "Input to ChatPromptTemplate is missing variables {'question', 'schema_text'}.  Expected: ['question', 'schema_text'] Received: ['original_question', 'uninformed_outline']\nNote: if you intended {question} to be part of the string and not a variable, please escape it with double curly braces like: '{{question}}'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT "
2025-08-08 08:18:54,295 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_081843.log
2025-08-08 08:18:54,297 - celery.app.trace - INFO - Task generate_streaming_report[dd032ae6-a88f-462e-9b11-1c3df8eb77ca] succeeded in 10.6842509170001s: {'error': 'Error generating streaming report: "Input to ChatPromptTemplate is missing variables {\'question\', \'schema_text\'}.  Expected: [\'question\', \'schema_text\'] Received: [\'original_question\', \'uninformed_outline\']\nNote: if you intended {question} to be part of the string and not a variable, please escape it with double curly braces like: \'{{question}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT "'}

2025-08-07 17:17:46,686 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250807_171746.log
2025-08-07 17:17:46,687 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:17:46,687 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 89cca85e-22f7-424e-b0d3-aa0ee819ecd5
2025-08-07 17:17:46,687 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:17:46,687 - REPORT_REQUEST - INFO - 📝 Original Question: 'What is the institution with the most students?'
2025-08-07 17:17:46,687 - REPORT_REQUEST - INFO - 🆔 Task ID: 89cca85e-22f7-424e-b0d3-aa0ee819ecd5
2025-08-07 17:17:46,687 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-07T17:17:46.687245
2025-08-07 17:17:46,831 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.144s]
2025-08-07 17:17:46,832 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-07 17:17:46,965 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-07 17:17:46,965 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:17:47,162 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.196s]
2025-08-07 17:17:47,162 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-07 17:17:47,162 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-07 17:17:55,907 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:17:55,927 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-07 17:17:59,386 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:17:59,403 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-07 17:18:02,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:02,574 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-07 17:18:02,575 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:02,575 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-07 17:18:02,575 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:02,575 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 0
2025-08-07 17:18:02,575 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-07 17:18:02,575 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-07 17:18:02,575 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 0
2025-08-07 17:18:02,575 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-07 17:18:08,499 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:08,503 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-07 17:18:16,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:16,801 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:16,802 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-07 17:18:16,802 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:16,802 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 0
2025-08-07 17:18:16,802 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:16,802 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-07 17:18:16,802 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:16,802 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 0 documents
2025-08-07 17:18:16,802 - UPSERT_DOCS - WARNING - ⚠️ No documents to upsert!
2025-08-07 17:18:16,952 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.149s]
2025-08-07 17:18:16,952 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-07 17:18:17,129 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.176s]
2025-08-07 17:18:17,129 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:18:17,326 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.196s]
2025-08-07 17:18:17,326 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-07 17:18:17,327 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 9
2025-08-07 17:18:17,327 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-07 17:18:17,327 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:17,328 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-07 17:18:17,328 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:17,328 - REPORT_PIPELINE - INFO - ✍️ Writing 9 sections using batch processing...
2025-08-07 17:18:18,010 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:18,015 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:18,015 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 17:18:18,016 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:18,016 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Interview findings themes insights'
2025-08-07 17:18:18,016 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 17:18:18,016 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 17:18:18,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:18,085 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:18,085 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 17:18:18,085 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:18,085 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion Summary Findings Implications Recommendations'
2025-08-07 17:18:18,085 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 17:18:18,085 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 17:18:18,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:18,116 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:18,116 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 17:18:18,117 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:18,117 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'definition of institution student enrollment types of institutions'
2025-08-07 17:18:18,117 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 17:18:18,117 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 17:18:18,139 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:18,153 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:18,154 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 17:18:18,154 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:18,154 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Institution enrollment analysis trends'
2025-08-07 17:18:18,154 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 17:18:18,154 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 17:18:18,157 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:18,165 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:18,165 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 17:18:18,165 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:18,165 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Appendices Interview Transcripts Data Tables Charts'
2025-08-07 17:18:18,165 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 17:18:18,165 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 17:18:18,181 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.165s]
2025-08-07 17:18:18,181 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 17:18:18,185 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:18,193 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:18,193 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 17:18:18,193 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:18,193 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'highest student enrollment institution factors'
2025-08-07 17:18:18,193 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 17:18:18,193 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 17:18:18,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:18,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:18,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:18,269 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:18,270 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 17:18:18,270 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:18,271 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'high enrollment factors implications'
2025-08-07 17:18:18,271 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 17:18:18,271 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 17:18:18,276 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:18,276 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:18,276 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 17:18:18,276 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 17:18:18,276 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:18,276 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:18,277 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'References Interviewees Resources Literature'
2025-08-07 17:18:18,277 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology Data Collection Interview Process Limitations'
2025-08-07 17:18:18,277 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 17:18:18,277 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 17:18:18,277 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 17:18:18,277 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 17:18:18,344 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.163s]
2025-08-07 17:18:18,345 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:18:18,354 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.269s]
2025-08-07 17:18:18,354 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 17:18:18,382 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.265s]
2025-08-07 17:18:18,382 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 17:18:18,457 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.292s]
2025-08-07 17:18:18,458 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.304s]
2025-08-07 17:18:18,458 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.265s]
2025-08-07 17:18:18,458 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 17:18:18,459 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 17:18:18,459 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 17:18:18,476 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-07 17:18:18,476 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 17:18:18,483 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-07 17:18:18,483 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:18:18,584 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.202s]
2025-08-07 17:18:18,585 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:18:18,610 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.150s]
2025-08-07 17:18:18,610 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.151s]
2025-08-07 17:18:18,611 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:18:18,611 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:18:18,611 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-07 17:18:18,612 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.340s]
2025-08-07 17:18:18,612 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 17:18:18,612 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 17:18:18,613 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.336s]
2025-08-07 17:18:18,614 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.154s]
2025-08-07 17:18:18,614 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-07 17:18:18,614 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 17:18:18,614 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:18:18,615 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 17:18:18,615 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.338s]
2025-08-07 17:18:18,616 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 17:18:18,756 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.141s]
2025-08-07 17:18:18,756 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.140s]
2025-08-07 17:18:18,756 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.171s]
2025-08-07 17:18:18,757 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.142s]
2025-08-07 17:18:18,757 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.145s]
2025-08-07 17:18:18,757 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 17:18:18,757 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 17:18:18,757 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 17:18:18,757 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:18:18,757 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 17:18:18,758 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.144s]
2025-08-07 17:18:18,758 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:18:18,759 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.147s]
2025-08-07 17:18:18,759 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 17:18:18,761 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.144s]
2025-08-07 17:18:18,761 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 17:18:18,918 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.160s]
2025-08-07 17:18:18,918 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.159s]
2025-08-07 17:18:18,919 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.160s]
2025-08-07 17:18:18,919 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.160s]
2025-08-07 17:18:18,919 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.161s]
2025-08-07 17:18:18,919 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.158s]
2025-08-07 17:18:18,919 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 17:18:18,919 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 17:18:18,919 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 17:18:18,919 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 17:18:18,919 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 17:18:18,920 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 17:18:18,922 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.165s]
2025-08-07 17:18:18,922 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 17:18:19,090 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.169s]
2025-08-07 17:18:19,091 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.169s]
2025-08-07 17:18:19,091 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.170s]
2025-08-07 17:18:19,092 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 17:18:19,092 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 17:18:19,092 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 17:18:19,713 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 17:18:19,717 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 17:18:19,718 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 17:18:19,735 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 17:18:19,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 17:18:19,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 17:18:19,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 17:18:19,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 17:18:20,064 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.277s]
2025-08-07 17:18:20,064 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 17:18:20,065 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 17:18:20,168 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.381s]
2025-08-07 17:18:20,169 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.380s]
2025-08-07 17:18:20,169 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 17:18:20,169 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.380s]
2025-08-07 17:18:20,170 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 17:18:20,170 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.382s]
2025-08-07 17:18:20,170 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 17:18:20,170 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 17:18:20,170 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 17:18:20,170 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 17:18:20,171 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 17:18:20,172 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 17:18:20,175 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 17:18:20,179 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.387s]
2025-08-07 17:18:20,179 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.389s]
2025-08-07 17:18:20,179 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 17:18:20,180 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 17:18:20,180 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 17:18:20,180 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 17:18:20,185 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.396s]
2025-08-07 17:18:20,186 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 17:18:20,186 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 17:18:20,358 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.180s]
2025-08-07 17:18:20,358 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 17:18:20,358 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 17:18:21,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:22,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:23,032 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:23,506 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:24,577 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:24,654 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:24,850 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:25,997 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:27,445 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:27,459 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 357 characters
2025-08-07 17:18:27,460 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1409 characters
2025-08-07 17:18:27,460 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1387 characters
2025-08-07 17:18:27,461 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 2097 characters
2025-08-07 17:18:27,461 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1457 characters
2025-08-07 17:18:27,461 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 2197 characters
2025-08-07 17:18:27,461 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 991 characters
2025-08-07 17:18:27,461 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 692 characters
2025-08-07 17:18:27,462 - REPORT_PIPELINE - INFO -   ✅ Section 9 completed: 602 characters
2025-08-07 17:18:28,266 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 17:18:28,270 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 17:18:28,270 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 17:18:28,270 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 17:18:28,270 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 17:18:28,270 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 17:18:28,270 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 17:18:28,270 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 17:18:28,270 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 17:18:28,271 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 17:18:28,271 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-07 17:18:28,271 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 17:18:28,271 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-07 17:18:28,271 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 17:18:28,271 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-07 17:18:28,271 - REPORT_PIPELINE - INFO - 📊 Final report sections: 10
2025-08-07 17:18:28,271 - REPORT_PIPELINE - INFO - 📈 Extended data items: 0
2025-08-07 17:18:28,271 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250807_171746.log
2025-08-07 17:18:28,273 - celery.app.trace - INFO - Task generate_report[b4df3700-11e3-4d8f-b81d-c96b14080bc7] succeeded in 41.58815583400428s: {'outline': '# Report Outline: Investigating the Institution with the Most Students

## I. Introduction  
The purpose of this report is to identify the institution with the highest student enrollment and to explore the factors contributing to this enrollment. The key answer to the guiding question is that the institution with the most students is a large public university, which has seen significant growth in enrollment over recent years.

## II. Definition of Terms  
- Institution  
- Student Enrollment  
- Types of Institutions (e.g., universities, colleges, online institutions)  

## III. Methodology  
- Overview of Data Collection  
- Description of Interview Process  
- Limitations of the Study  

## IV. Findings  
- Summary of Interview Insights  
- Key Themes Identified  
  - Institutional Size  
  - Enrollment Trends  
  - Comparison of Institutions  
- Notable Quotes from Interviews  

## V. Analysis  
- Comparison of Institutions  
  - Largest Institutions Identified  
  - Factors Contributing to...', ...}

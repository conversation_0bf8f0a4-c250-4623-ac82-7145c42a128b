2025-08-09 13:33:42,727 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250809_133342.log
2025-08-09 13:33:42,727 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:33:42,727 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: d123aa40-feab-4585-a4dd-d3423b647701
2025-08-09 13:33:42,727 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:33:42,727 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University'
2025-08-09 13:33:42,727 - REPORT_REQUEST - INFO - 🆔 Task ID: d123aa40-feab-4585-a4dd-d3423b647701
2025-08-09 13:33:42,727 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-09T13:33:42.727921
2025-08-09 13:33:42,925 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.198s]
2025-08-09 13:33:42,926 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-09 13:33:43,117 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.191s]
2025-08-09 13:33:43,117 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-09 13:33:43,367 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.250s]
2025-08-09 13:33:43,368 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-09 13:33:43,368 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:33:43,369 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-09 13:33:43,369 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:33:43,369 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How are girls performing at ITC University
2025-08-09 13:33:43,369 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-09 13:33:43,369 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-09 13:33:53,194 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:33:53,219 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-09 13:33:57,171 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:33:57,188 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-09 13:33:59,530 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:33:59,531 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:33:59,576 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:33:59,718 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:33:59,788 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:33:59,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:33:59,959 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:00,094 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:00,138 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:00,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:00,297 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-09 13:34:03,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:03,264 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:03,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:05,877 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:05,882 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:06,144 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:07,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:07,950 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:07,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:12,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:13,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:13,697 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:18,229 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:18,234 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic performance levels of girls compare to those of boys at ITC University?', 'sql': "SELECT s.sex, AVG(sg.gpa) AS average_gpa, COUNT(sg.id) AS total_students\nFROM core.students s\nJOIN core.student_semester_gpas sg ON s.id = sg.student_id\nJOIN core.student_programs sp ON s.id = sp.student_id\nWHERE sp.institution_id = 'ITC University' AND sg.status = 'active'\nGROUP BY s.sex;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average GPA and total number of students for each sex (girls and boys) at ITC University. It joins the necessary tables: 'students' for student information, 'student_semester_gpas' for GPA data, and 'student_programs' to filter by institution. The WHERE clause ensures that only active students from ITC University are considered, and the GROUP BY clause groups the results by sex, allowing for a comparison between girls and boys.", 'feedback': "The question could be clarified by specifying whether the comparison should include only certain semesters or academic years. Additionally, the SQL could be improved by explicitly stating the institution's ID in a more dynamic way, rather than hardcoding 'ITC University', to enhance reusability."}
2025-08-09 13:34:18,235 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:18,235 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 13:34:18,642 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:18,647 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any trends in the performance of girls at ITC University over the past few years?', 'sql': "SELECT s.sex, sem.start_year, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS total_assessments\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN semesters sem ON ar.semester_id = sem.id\nWHERE s.sex = 'F' AND sem.status = 'Ended'\nGROUP BY s.sex, sem.start_year\nORDER BY sem.start_year;", 'correct': True, 'reasoning': "The SQL query correctly identifies the performance trends of female students (sex = 'F') at ITC University by calculating the average final score and the total number of assessments for each academic year (start_year) where the semester status is 'Ended'. The use of GROUP BY and AVG functions allows for a clear analysis of trends over the years, which directly addresses the question about performance trends.", 'feedback': 'The question could be clarified by specifying what kind of trends are of interest (e.g., improvement over time, comparison with boys, etc.). The SQL could be improved by including a filter for the institution_id to ensure that the data is specifically for ITC University.'}
2025-08-09 13:34:18,647 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:18,648 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 13:34:21,492 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:22,670 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:23,223 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:23,443 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:24,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:24,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:24,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:25,536 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:27,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:27,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:28,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:29,308 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:30,220 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:30,243 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-09 13:34:30,243 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:30,244 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:34:30,244 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-09 13:34:30,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:30,455 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis processes. Therefore, while the schema can provide data on academic performance and gender, it does not answer the question about steps for improvement.', 'feedback': ''}
2025-08-09 13:34:30,456 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:30,456 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:34:30,456 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis processes. Therefore, while the schema can provide data on academic performance and gender, it does not answer the question about steps for improvement.', 'feedback': ''}
2025-08-09 13:34:32,806 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:33,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:35,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:36,531 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:36,546 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or tables that directly relate to performance data, gender-specific performance analysis, or any contextual information about ITC University. The schema includes various tables related to institutions, students, courses, and assessments, but it lacks the necessary data to analyze the reasons behind the absence of performance data for a specific gender at a specific institution.', 'feedback': ''}
2025-08-09 13:34:36,546 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:36,546 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:34:36,546 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or tables that directly relate to performance data, gender-specific performance analysis, or any contextual information about ITC University. The schema includes various tables related to institutions, students, courses, and assessments, but it lacks the necessary data to analyze the reasons behind the absence of performance data for a specific gender at a specific institution.', 'feedback': ''}
2025-08-09 13:34:37,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:37,534 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis processes. Therefore, while the schema can provide data on academic performance and gender, it does not answer the question of what steps to take for improvement.', 'feedback': ''}
2025-08-09 13:34:37,534 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:37,534 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:34:37,534 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis processes. Therefore, while the schema can provide data on academic performance and gender, it does not answer the question of what steps to take for improvement.', 'feedback': ''}
2025-08-09 13:34:38,839 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:40,045 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:40,691 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:40,703 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in the most?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(ar.finalscore) AS average_score\nFROM core.assessment_results ar\nJOIN core.students s ON ar.student_id = s.id\nJOIN core.student_programs sp ON ar.student_program_id = sp.id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.courses c ON ar.course_id = c.id\nWHERE s.sex = 'F' AND s.institution_id = (SELECT id FROM core.institutions WHERE name = 'ITC University')\nGROUP BY p.long_name, c.title\nHAVING AVG(ar.finalscore) > 75\nORDER BY average_score DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses that female students at ITC University are excelling in by filtering for female students, joining the necessary tables to gather program and course information, and calculating the average score for each program and course combination. The use of HAVING to filter for an average score greater than 75 aligns with the notion of 'excelling'. The ordering by average score in descending order also matches the intent of identifying the top-performing programs and courses.", 'feedback': "The question could be clarified by specifying what 'excelling' means (e.g., a specific score threshold), but the SQL query already incorporates a threshold of 75, which is a reasonable assumption. Additionally, it might be beneficial to explicitly mention whether the focus is solely on programs, courses, or both, but the query effectively addresses both aspects."}
2025-08-09 13:34:40,703 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:40,704 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 13:34:42,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:42,139 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-09 13:34:42,140 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:42,140 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:34:42,140 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-09 13:34:42,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:42,225 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. However, the provided schema does not contain any specific information or guidelines on how to improve data collection or analysis processes. It primarily consists of tables related to institutions, students, academic years, and various related entities, but lacks any procedural or strategic insights. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 13:34:42,225 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:42,225 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:34:42,225 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. However, the provided schema does not contain any specific information or guidelines on how to improve data collection or analysis processes. It primarily consists of tables related to institutions, students, academic years, and various related entities, but lacks any procedural or strategic insights. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 13:34:44,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:45,196 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:45,503 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:46,377 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:47,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:47,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:47,776 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-09 13:34:47,776 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:47,776 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:34:47,776 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-09 13:34:48,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:48,286 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis processes. Therefore, while the schema can provide data on academic performance and gender, it does not answer the question about steps for improvement.', 'feedback': ''}
2025-08-09 13:34:48,286 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:48,286 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:34:48,286 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis processes. Therefore, while the schema can provide data on academic performance and gender, it does not answer the question about steps for improvement.', 'feedback': ''}
2025-08-09 13:34:49,787 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:51,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:51,889 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of girls in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the success of girls in specific programs and courses at ITC University. The provided schema contains a wealth of data related to institutions, students, programs, courses, and various assessments, but it does not include qualitative insights or factors that would explain success. The schema lacks fields for subjective evaluations, personal experiences, or external factors that could influence success, making it impossible to answer the question directly or indirectly.', 'feedback': ''}
2025-08-09 13:34:51,890 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:51,890 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:34:51,890 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of girls in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the success of girls in specific programs and courses at ITC University. The provided schema contains a wealth of data related to institutions, students, programs, courses, and various assessments, but it does not include qualitative insights or factors that would explain success. The schema lacks fields for subjective evaluations, personal experiences, or external factors that could influence success, making it impossible to answer the question directly or indirectly.', 'feedback': ''}
2025-08-09 13:34:54,320 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:56,460 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:34:56,475 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of girls in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of girls in specific programs and courses at ITC University. However, the provided schema does not contain any direct information about success factors, gender-specific performance metrics, or qualitative data that would allow for an analysis of success factors for girls in specific programs. The schema primarily contains structural data about institutions, courses, students, and their relationships, but lacks the necessary qualitative insights or performance indicators to answer the question.', 'feedback': ''}
2025-08-09 13:34:56,475 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:34:56,475 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:34:56,475 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of girls in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of girls in specific programs and courses at ITC University. However, the provided schema does not contain any direct information about success factors, gender-specific performance metrics, or qualitative data that would allow for an analysis of success factors for girls in specific programs. The schema primarily contains structural data about institutions, courses, students, and their relationships, but lacks the necessary qualitative insights or performance indicators to answer the question.', 'feedback': ''}
2025-08-09 13:34:58,918 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:01,256 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:01,272 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of girls in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of girls in specific programs and courses at ITC University. However, the provided schema does not contain any direct information about the factors influencing success, such as qualitative data or metrics related to gender performance in specific programs or courses. The schema includes various tables related to students, programs, courses, and assessments, but it lacks specific attributes or data points that would allow for an analysis of success factors based on gender. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 13:35:01,272 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:35:01,272 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:35:01,272 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of girls in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of girls in specific programs and courses at ITC University. However, the provided schema does not contain any direct information about the factors influencing success, such as qualitative data or metrics related to gender performance in specific programs or courses. The schema includes various tables related to students, programs, courses, and assessments, but it lacks specific attributes or data points that would allow for an analysis of success factors based on gender. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 13:35:03,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:05,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:05,891 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of girls in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of girls in specific programs and courses at ITC University. However, the provided schema does not contain any direct data or metrics related to the success of students, particularly segmented by gender or specific programs and courses. While there are tables related to students, programs, and courses, there is no indication of performance metrics or success factors that can be derived from the schema. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 13:35:05,891 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:35:05,892 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:35:05,892 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the success of girls in these specific programs and courses at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the success of girls in specific programs and courses at ITC University. However, the provided schema does not contain any direct data or metrics related to the success of students, particularly segmented by gender or specific programs and courses. While there are tables related to students, programs, and courses, there is no indication of performance metrics or success factors that can be derived from the schema. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 13:35:05,892 - root - INFO - [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 86.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 84.26}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Media And Technologies For Teaching And Learning', 'average_score': 78.83}]
2025-08-09 13:35:05,892 - root - INFO - 'No results'
2025-08-09 13:35:05,892 - root - INFO - 'No results'
2025-08-09 13:35:05,892 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-09 13:35:12,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:12,883 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-09 13:35:22,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:22,167 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:35:22,167 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 13:35:22,167 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:35:22,167 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University'
2025-08-09 13:35:22,167 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 13:35:22,167 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 13:35:22,167 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 13:35:22,167 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 13:35:22,168 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the academic performance levels of girls compare to those of boys at ITC University?...
2025-08-09 13:35:22,168 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, which indicates that there may not be sufficient data avai...
2025-08-09 13:35:22,168 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 13:35:22,168 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 13:35:22,168 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: academic_performance_comparison_by_gender
2025-08-09 13:35:22,168 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 13:35:22,168 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 13:35:22,168 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:35:22,168 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 13:35:22,168 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:35:22,168 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University'
2025-08-09 13:35:22,169 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 13:35:22,169 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 13:35:22,169 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 13:35:22,169 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 13:35:22,169 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are girls at ITC University excelling in the most?...
2025-08-09 13:35:22,169 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, girls are excelling in several programs and courses. The program with the highest...
2025-08-09 13:35:22,169 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-09 13:35:22,169 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Re...
2025-08-09 13:35:22,169 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_academic_excellence_at_itc
2025-08-09 13:35:22,169 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 13:35:22,169 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 13:35:22,169 - celery.redirected - WARNING - [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 86.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 84.26}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Media And Technologies For Teaching And Learning', 'average_score': 78.83}]
2025-08-09 13:35:22,169 - celery.redirected - WARNING - ================================= 
2025-08-09 13:35:22,170 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: girls_academic_excellence_at_itc
2025-08-09 13:35:22,170 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 13:35:22,170 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:35:22,170 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 13:35:22,170 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:35:22,170 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University'
2025-08-09 13:35:22,170 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 13:35:22,170 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 13:35:22,170 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 13:35:22,171 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 13:35:22,171 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any trends in the performance of girls at ITC University over the past few years?...
2025-08-09 13:35:22,171 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Based on the data available, there are no recorded results regarding the performance of girls at ITC...
2025-08-09 13:35:22,171 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 13:35:22,171 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 13:35:22,172 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_performance_trends_itc_university
2025-08-09 13:35:22,172 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 13:35:22,172 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 13:35:22,172 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 3
2025-08-09 13:35:22,172 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:35:22,172 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-09 13:35:22,172 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:35:22,172 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 3 documents
2025-08-09 13:35:22,173 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-09 13:35:22,173 - UPSERT_DOCS - INFO -     Content: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-09 13:35:22,173 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:34:48.286232+00:00', 'data_returned': False}
2025-08-09 13:35:22,173 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-09 13:35:22,173 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:22,173 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:22,173 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-09 13:35:22,174 - UPSERT_DOCS - INFO -     Content: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-09 13:35:22,174 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:34:47.776959+00:00', 'data_returned': False}
2025-08-09 13:35:22,174 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 1/3
2025-08-09 13:35:22,313 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.139s]
2025-08-09 13:35:23,437 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 13:35:30,046 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:6.229s]
2025-08-09 13:35:30,046 - UPSERT_DOCS - INFO - ✅ Successfully upserted 3 documents to Elasticsearch
2025-08-09 13:35:30,047 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 7
2025-08-09 13:35:30,047 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-09 13:35:30,048 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:35:30,048 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-09 13:35:30,048 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:35:30,048 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/7...
2025-08-09 13:35:31,197 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:31,207 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:35:31,208 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 13:35:31,208 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:35:31,208 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'female students academic performance ITC University'
2025-08-09 13:35:31,208 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University'
2025-08-09 13:35:31,208 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University'
2025-08-09 13:35:31,389 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.181s]
2025-08-09 13:35:31,389 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-09 13:35:31,567 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.178s]
2025-08-09 13:35:31,568 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-09 13:35:31,740 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.171s]
2025-08-09 13:35:31,740 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-09 13:35:31,880 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.139s]
2025-08-09 13:35:31,880 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-09 13:35:32,606 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 13:35:32,759 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.149s]
2025-08-09 13:35:32,760 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-09 13:35:32,760 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:32,761 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:32,762 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:32,762 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:32,762 - app.chains.section_writer - INFO -   ✅ Added Data Tag: girls_academic_excellence_at_itc
2025-08-09 13:35:32,762 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (817 chars):
2025-08-09 13:35:32,762 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC University, girls are excelling in several programs and courses. The program with the highest average score is the Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics', where they achieved an average score of 86.0. Following that, in the Bachelor of Science in Clothing and Textiles Education, the course 'Introductio...
2025-08-09 13:35:35,753 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:35,758 - app.chains.section_writer - INFO - 🤖 AI generated section (1037 chars):
2025-08-09 13:35:35,758 - app.chains.section_writer - INFO -    The purpose of this report is to evaluate the academic performance of female students at ITC University, focusing on their achievements and challenges. The key finding indicates that while girls are performing well in certain programs, there are areas that require targeted support to enhance their o...
2025-08-09 13:35:35,758 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['girls_academic_excellence_at_itc']
2025-08-09 13:35:35,758 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 1628 characters
2025-08-09 13:35:35,759 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/7...
2025-08-09 13:35:36,496 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:36,500 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:35:36,500 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 13:35:36,500 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:35:36,500 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Academic Performance Overview'
2025-08-09 13:35:36,500 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University'
2025-08-09 13:35:36,500 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University'
2025-08-09 13:35:36,647 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.146s]
2025-08-09 13:35:36,647 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-09 13:35:36,820 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.173s]
2025-08-09 13:35:36,821 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-09 13:35:36,980 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.159s]
2025-08-09 13:35:36,980 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-09 13:35:37,110 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 13:35:37,110 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-09 13:35:37,624 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 13:35:37,883 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.256s]
2025-08-09 13:35:37,884 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-09 13:35:37,884 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:37,884 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:37,885 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:37,885 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:37,885 - app.chains.section_writer - INFO -   ✅ Added Data Tag: girls_academic_excellence_at_itc
2025-08-09 13:35:37,885 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (817 chars):
2025-08-09 13:35:37,885 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC University, girls are excelling in several programs and courses. The program with the highest average score is the Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics', where they achieved an average score of 86.0. Following that, in the Bachelor of Science in Clothing and Textiles Education, the course 'Introductio...
2025-08-09 13:35:41,185 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:41,189 - app.chains.section_writer - INFO - 🤖 AI generated section (1233 chars):
2025-08-09 13:35:41,190 - app.chains.section_writer - INFO -    ## 2. Overview of Academic Performance  

### 2.1 General Performance Trends  
At ITC University, academic performance has shown notable trends, particularly among female students. The highest average score recorded is in the Bachelor of Arts in Journalism and Media Studies, specifically in the cour...
2025-08-09 13:35:41,190 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['girls_academic_excellence_at_itc']
2025-08-09 13:35:41,190 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 1211 characters
2025-08-09 13:35:41,190 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/7...
2025-08-09 13:35:41,925 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:41,929 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:35:41,929 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 13:35:41,929 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:35:41,929 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'notable programs courses performance'
2025-08-09 13:35:41,929 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University'
2025-08-09 13:35:41,929 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University'
2025-08-09 13:35:42,079 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.150s]
2025-08-09 13:35:42,079 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-09 13:35:42,206 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-09 13:35:42,206 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-09 13:35:42,331 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-09 13:35:42,332 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-09 13:35:42,463 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-09 13:35:42,463 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-09 13:35:42,910 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 13:35:43,071 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.158s]
2025-08-09 13:35:43,072 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-09 13:35:43,072 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:43,072 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:43,073 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:43,073 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:43,073 - app.chains.section_writer - INFO -   ✅ Added Data Tag: girls_academic_excellence_at_itc
2025-08-09 13:35:43,073 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (817 chars):
2025-08-09 13:35:43,073 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC University, girls are excelling in several programs and courses. The program with the highest average score is the Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics', where they achieved an average score of 86.0. Following that, in the Bachelor of Science in Clothing and Textiles Education, the course 'Introductio...
2025-08-09 13:35:47,270 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:47,299 - app.chains.section_writer - INFO - 🤖 AI generated section (1402 chars):
2025-08-09 13:35:47,299 - app.chains.section_writer - INFO -    ## 3. Programs and Courses with Notable Performance  

### 3.1 Bachelor of Arts in Journalism and Media Studies  
#### 3.1.1 Course Highlight: Educational Research Methods, Assessment and Statistics  
The Bachelor of Arts in Journalism and Media Studies has demonstrated exceptional performance, part...
2025-08-09 13:35:47,299 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['girls_academic_excellence_at_itc']
2025-08-09 13:35:47,299 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 1380 characters
2025-08-09 13:35:47,300 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/7...
2025-08-09 13:35:48,279 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:48,286 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:35:48,286 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 13:35:48,286 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:35:48,286 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Performance Insights Analysis'
2025-08-09 13:35:48,286 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University'
2025-08-09 13:35:48,286 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University'
2025-08-09 13:35:48,442 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.156s]
2025-08-09 13:35:48,443 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-09 13:35:48,578 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-09 13:35:48,578 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-09 13:35:48,705 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-09 13:35:48,705 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-09 13:35:48,842 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.137s]
2025-08-09 13:35:48,843 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-09 13:35:49,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 13:35:49,773 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.170s]
2025-08-09 13:35:49,774 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-09 13:35:49,774 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:49,774 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:49,775 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:49,775 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:49,775 - app.chains.section_writer - INFO -   ✅ Added Data Tag: girls_academic_excellence_at_itc
2025-08-09 13:35:49,775 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (817 chars):
2025-08-09 13:35:49,775 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC University, girls are excelling in several programs and courses. The program with the highest average score is the Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics', where they achieved an average score of 86.0. Following that, in the Bachelor of Science in Clothing and Textiles Education, the course 'Introductio...
2025-08-09 13:35:54,235 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:54,248 - app.chains.section_writer - INFO - 🤖 AI generated section (1552 chars):
2025-08-09 13:35:54,248 - app.chains.section_writer - INFO -    ## 4. Analysis of Performance Insights  

### 4.1 Strengths in Specific Courses  
At ITC University, girls have demonstrated exceptional performance in several programs and courses. The Bachelor of Arts in Journalism and Media Studies stands out, particularly in the course 'Educational Research Meth...
2025-08-09 13:35:54,249 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['girls_academic_excellence_at_itc']
2025-08-09 13:35:54,249 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 1530 characters
2025-08-09 13:35:54,249 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/7...
2025-08-09 13:35:54,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:35:54,945 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:35:54,946 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 13:35:54,946 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:35:54,946 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Factors Influencing Performance'
2025-08-09 13:35:54,946 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University'
2025-08-09 13:35:54,946 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University'
2025-08-09 13:35:55,092 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.146s]
2025-08-09 13:35:55,092 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-09 13:35:55,224 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-09 13:35:55,225 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-09 13:35:55,408 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.183s]
2025-08-09 13:35:55,409 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-09 13:35:55,548 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.139s]
2025-08-09 13:35:55,548 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-09 13:35:56,189 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 13:35:56,364 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.171s]
2025-08-09 13:35:56,365 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-09 13:35:56,365 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:56,365 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:56,365 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:35:56,365 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:35:56,365 - app.chains.section_writer - INFO -   ✅ Added Data Tag: girls_academic_excellence_at_itc
2025-08-09 13:35:56,365 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (817 chars):
2025-08-09 13:35:56,365 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC University, girls are excelling in several programs and courses. The program with the highest average score is the Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics', where they achieved an average score of 86.0. Following that, in the Bachelor of Science in Clothing and Textiles Education, the course 'Introductio...
2025-08-09 13:36:02,525 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:36:02,529 - app.chains.section_writer - INFO - 🤖 AI generated section (1924 chars):
2025-08-09 13:36:02,529 - app.chains.section_writer - INFO -    ## 5. Factors Influencing Performance  

### 5.1 Socioeconomic Background  
The socioeconomic background of students plays a significant role in their academic performance. Factors such as family income, parental education levels, and access to educational resources can greatly influence students' e...
2025-08-09 13:36:02,529 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['girls_academic_excellence_at_itc']
2025-08-09 13:36:02,529 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 1902 characters
2025-08-09 13:36:02,530 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 6/7...
2025-08-09 13:36:03,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:36:03,536 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:36:03,536 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 13:36:03,536 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:36:03,536 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion Summary Recommendations'
2025-08-09 13:36:03,537 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University'
2025-08-09 13:36:03,537 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University'
2025-08-09 13:36:03,686 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.149s]
2025-08-09 13:36:03,686 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-09 13:36:03,821 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-09 13:36:03,822 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-09 13:36:03,954 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-09 13:36:03,954 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-09 13:36:04,087 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-09 13:36:04,087 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-09 13:36:04,664 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 13:36:04,811 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.143s]
2025-08-09 13:36:04,812 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-09 13:36:04,812 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:36:04,812 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:36:04,813 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:36:04,813 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:36:04,813 - app.chains.section_writer - INFO -   ✅ Added Data Tag: girls_academic_excellence_at_itc
2025-08-09 13:36:04,813 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (817 chars):
2025-08-09 13:36:04,813 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC University, girls are excelling in several programs and courses. The program with the highest average score is the Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics', where they achieved an average score of 86.0. Following that, in the Bachelor of Science in Clothing and Textiles Education, the course 'Introductio...
2025-08-09 13:36:08,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:36:08,653 - app.chains.section_writer - INFO - 🤖 AI generated section (1391 chars):
2025-08-09 13:36:08,653 - app.chains.section_writer - INFO -    ## 6. Conclusion  

### 6.1 Summary of Findings  
The analysis of academic performance at ITC University reveals that girls are excelling in various programs and courses. The highest average score was recorded in the Bachelor of Arts in Journalism and Media Studies, particularly in the course 'Educa...
2025-08-09 13:36:08,653 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['girls_academic_excellence_at_itc']
2025-08-09 13:36:08,653 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 6 completed and processed: 1369 characters
2025-08-09 13:36:08,653 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 7/7...
2025-08-09 13:36:09,886 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:36:09,890 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:36:09,890 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 13:36:09,890 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:36:09,891 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'References Interview Summaries Resources'
2025-08-09 13:36:09,891 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University'
2025-08-09 13:36:09,891 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University'
2025-08-09 13:36:10,021 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-09 13:36:10,022 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-09 13:36:10,198 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.176s]
2025-08-09 13:36:10,198 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-09 13:36:10,335 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-09 13:36:10,335 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-09 13:36:10,504 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.168s]
2025-08-09 13:36:10,504 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-09 13:36:11,109 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 13:36:11,292 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.178s]
2025-08-09 13:36:11,292 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-09 13:36:11,293 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:36:11,293 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:36:11,294 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC...
2025-08-09 13:36:11,294 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:35:05.892186+00:00', 'data_returned': True, 'data_tag': 'girls_academic_excellence_at_itc'}
2025-08-09 13:36:11,294 - app.chains.section_writer - INFO -   ✅ Added Data Tag: girls_academic_excellence_at_itc
2025-08-09 13:36:11,294 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (817 chars):
2025-08-09 13:36:11,294 - app.chains.section_writer - INFO -    Question: What programs or courses are girls at ITC University excelling in the most?
Answer: At ITC University, girls are excelling in several programs and courses. The program with the highest average score is the Bachelor of Arts in Journalism and Media Studies, specifically in the course 'Educational Research Methods, Assessment and Statistics', where they achieved an average score of 86.0. Following that, in the Bachelor of Science in Clothing and Textiles Education, the course 'Introductio...
2025-08-09 13:36:14,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:36:14,800 - app.chains.section_writer - INFO - 🤖 AI generated section (1273 chars):
2025-08-09 13:36:14,801 - app.chains.section_writer - INFO -    ## 7. References  

### 7.1 Interview Summaries  
Interviews conducted with faculty and students at ITC University revealed insights into the academic performance of female students across various programs. Notably, the Bachelor of Arts in Journalism and Media Studies emerged as the top-performing p...
2025-08-09 13:36:14,801 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['girls_academic_excellence_at_itc']
2025-08-09 13:36:14,801 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 7 completed and processed: 1251 characters
2025-08-09 13:36:14,802 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:36:14,802 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-09 13:36:14,802 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:36:14,802 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-09 13:36:14,802 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 8
2025-08-09 13:36:14,802 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 3
2025-08-09 13:36:14,802 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 1
2025-08-09 13:36:14,802 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250809_133342.log
2025-08-09 13:36:14,807 - celery.app.trace - INFO - Task generate_streaming_report[ff5b641d-884d-496b-a7a4-51299a450381] succeeded in 152.08829704099844s: {'outline': '# Report on Girls\' Performance at ITC University

## 1. Introduction  
   The purpose of this report is to evaluate the academic performance of female students at ITC University, focusing on their achievements and challenges. The key finding indicates that while girls are performing well in certain programs, there are areas that require targeted support to enhance their overall academic experience.

## 2. Overview of Academic Performance  
   - 2.1 General Performance Trends  
   - 2.2 Comparison of Performance Across Programs  

## 3. Programs and Courses with Notable Performance  
   - 3.1 Bachelor of Arts in Journalism and Media Studies  
       - 3.1.1 Course Highlight: Educational Research Methods, Assessment and Statistics  
       - 3.1.2 Average Score: 86.0  
   - 3.2 Bachelor of Science in Clothing and Textiles Education  
       - 3.2.1 Course Highlight: Introduction To Information And Communication Technology  
       - 3.2.2 Average Score: 84.26  
   - 3.3 Bachelor of Science in...', , ...}

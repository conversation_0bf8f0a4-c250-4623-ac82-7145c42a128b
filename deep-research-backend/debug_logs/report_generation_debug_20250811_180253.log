2025-08-11 18:02:53,262 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250811_180253.log
2025-08-11 18:02:53,262 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:02:53,262 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: e71570e7-5633-42bb-b626-464ebee13cd2
2025-08-11 18:02:53,262 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:02:53,262 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are boys performing at ITC University?'
2025-08-11 18:02:53,262 - REPORT_REQUEST - INFO - 🆔 Task ID: e71570e7-5633-42bb-b626-464ebee13cd2
2025-08-11 18:02:53,262 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-11T18:02:53.262953
2025-08-11 18:02:54,076 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.813s]
2025-08-11 18:02:54,076 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 121
2025-08-11 18:02:54,392 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.315s]
2025-08-11 18:02:54,392 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 66
2025-08-11 18:02:54,740 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.348s]
2025-08-11 18:02:54,741 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-11 18:02:54,741 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (24 docs)
2025-08-11 18:02:54,741 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (16 docs)
2025-08-11 18:02:54,741 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-11 18:02:54,741 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-11 18:02:54,741 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-11 18:02:54,741 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (11 docs)
2025-08-11 18:02:54,742 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-11 18:02:54,742 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-11 18:02:54,742 - ELASTICSEARCH_STATE - INFO -   - 'Which instituion has the most students?' (6 docs)
2025-08-11 18:02:54,742 - ELASTICSEARCH_STATE - INFO -   - 'How are students performing at ITC University?' (4 docs)
2025-08-11 18:02:54,742 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:02:54,742 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-11 18:02:54,743 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:02:54,743 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How are boys performing at ITC University?
2025-08-11 18:02:54,743 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-11 18:02:54,743 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-11 18:03:12,581 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:12,604 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-11 18:03:20,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:20,822 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-11 18:03:41,008 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:42,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:42,225 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:42,232 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:42,639 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:42,916 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:43,182 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:44,285 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:44,742 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:46,998 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:03:47,062 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-11 18:04:31,904 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:31,942 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:32,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:34,260 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:36,401 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:36,858 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:37,741 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:38,067 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:43,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:43,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:44,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:44,813 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:49,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:49,407 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:51,184 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:52,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:54,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:55,531 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:55,534 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "How do boys' academic results compare to those of girls at ITC University?", 'sql': "SELECT s.sex, AVG(ar.finalscore) AS average_finalscore, COUNT(ar.id) AS total_results\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nWHERE s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY s.sex\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average final scores and total number of results for boys and girls at ITC University by joining the 'assessment_results' and 'students' tables. It filters the results based on the institution ID for ITC University and groups the results by sex, which allows for a direct comparison of academic results between boys and girls. The use of AVG() function provides the average final score, which is essential for comparing academic performance.", 'feedback': 'The query is well-structured and answers the question effectively. However, the LIMIT clause is unnecessary since the GROUP BY clause already limits the results to two groups (boys and girls). Removing the LIMIT clause would make the intent clearer and ensure all relevant data is returned.'}
2025-08-11 18:04:55,535 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:04:55,535 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 18:04:59,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:04:59,604 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are boys enrolled in at ITC University, and how do their performances vary across these programs?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, ar.finalscore, ar.grade \nFROM core.students s \nJOIN core.student_programs sp ON s.id = sp.student_id \nJOIN core.programs p ON sp.program_id = p.id \nJOIN core.assessment_results ar ON sp.id = ar.student_program_id \nJOIN core.courses c ON ar.course_id = c.id \nWHERE s.sex = 'M' AND s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University') \nORDER BY p.long_name, ar.finalscore DESC \nLIMIT 20;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the programs and courses that male students (boys) are enrolled in at ITC University. It joins the necessary tables: students, student_programs, programs, assessment_results, and courses to gather the required information. The WHERE clause filters for male students at the specified institution, and the SELECT statement retrieves the program names, course titles, final scores, and grades, which directly addresses the question about performance variation across programs.', 'feedback': 'The query is well-structured and answers the question effectively. However, it might be beneficial to remove the LIMIT clause if the intention is to get a complete view of all programs and courses rather than just the top 20 results. Additionally, consider including more performance metrics if available, such as average scores or comparisons across different programs.'}
2025-08-11 18:04:59,605 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:04:59,605 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 18:05:00,218 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:00,223 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "How has boys' performance at ITC University changed over the past few academic years?", 'sql': "SELECT academic_years.start_year, AVG(assessment_results.finalscore) AS average_score, COUNT(assessment_results.id) AS total_assessments\nFROM assessment_results\nJOIN students ON assessment_results.student_id = students.id\nJOIN academic_years ON assessment_results.semester_id = academic_years.id\nWHERE students.sex = 'M' AND academic_years.status = 'Active'\nGROUP BY academic_years.start_year\nORDER BY academic_years.start_year DESC\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average final scores of male students (boys) at ITC University over the years by joining the relevant tables: 'assessment_results', 'students', and 'academic_years'. It filters for male students and only considers active academic years. The results are grouped by the start year of the academic years, which allows for an analysis of performance changes over time. The inclusion of both average scores and total assessments provides a comprehensive view of performance.", 'feedback': 'The question could be clarified by specifying whether it is interested in a specific course or all courses at ITC University. Additionally, the SQL could be improved by removing the LIMIT clause if the intent is to see all available years rather than just the latest 20.'}
2025-08-11 18:05:00,223 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:00,223 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 18:05:00,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:01,224 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:02,135 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:03,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:04,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:05,222 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:05,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:06,202 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:06,279 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:07,047 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:08,467 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:08,531 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:09,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:10,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:10,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:12,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:14,560 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:14,573 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in academic performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in academic performance between boys and girls at ITC University. However, the provided schema does not contain specific data or attributes that directly relate to gender-based performance analysis or the factors influencing it. While there are tables related to students, courses, and academic results, there is no explicit data on gender differences or the specific factors affecting academic performance. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-11 18:05:14,574 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:14,574 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:14,574 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in academic performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in academic performance between boys and girls at ITC University. However, the provided schema does not contain specific data or attributes that directly relate to gender-based performance analysis or the factors influencing it. While there are tables related to students, courses, and academic results, there is no explicit data on gender differences or the specific factors affecting academic performance. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-11 18:05:15,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:19,051 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:19,071 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance among boys in these various programs, particularly in the courses where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences among boys in various programs, specifically in courses where they struggle. The schema provides data on students, programs, courses, and grades, but it does not include qualitative factors or insights into the reasons behind performance differences. To answer this question, one would need additional data such as socio-economic background, teaching quality, student engagement, or psychological factors, none of which are present in the schema.', 'feedback': ''}
2025-08-11 18:05:19,071 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:19,072 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:19,072 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance among boys in these various programs, particularly in the courses where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences among boys in various programs, specifically in courses where they struggle. The schema provides data on students, programs, courses, and grades, but it does not include qualitative factors or insights into the reasons behind performance differences. To answer this question, one would need additional data such as socio-economic background, teaching quality, student engagement, or psychological factors, none of which are present in the schema.', 'feedback': ''}
2025-08-11 18:05:19,455 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:19,488 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:23,229 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:23,373 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think could contribute to the lack of data on boys' performance at ITC University, and how might this impact future assessments of academic performance?", 'answerable': False, 'reasoning': "The question is qualitative and seeks to explore factors contributing to a lack of data on boys' performance at ITC University, as well as the potential impacts on future assessments. The provided database schema contains structured data about institutions, students, courses, and various academic records, but it does not include qualitative insights or analysis regarding the reasons for data gaps or their implications. Therefore, this question cannot be answered directly or indirectly using the schema.", 'feedback': ''}
2025-08-11 18:05:23,373 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:23,373 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:23,373 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think could contribute to the lack of data on boys' performance at ITC University, and how might this impact future assessments of academic performance?", 'answerable': False, 'reasoning': "The question is qualitative and seeks to explore factors contributing to a lack of data on boys' performance at ITC University, as well as the potential impacts on future assessments. The provided database schema contains structured data about institutions, students, courses, and various academic records, but it does not include qualitative insights or analysis regarding the reasons for data gaps or their implications. Therefore, this question cannot be answered directly or indirectly using the schema.", 'feedback': ''}
2025-08-11 18:05:24,606 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:24,615 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:24,620 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in academic performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in academic performance between boys and girls at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors such as gender-specific performance metrics, social influences, or educational interventions are not represented in the schema.', 'feedback': ''}
2025-08-11 18:05:24,620 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:24,620 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:24,620 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in academic performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in academic performance between boys and girls at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors such as gender-specific performance metrics, social influences, or educational interventions are not represented in the schema.', 'feedback': ''}
2025-08-11 18:05:29,006 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:29,394 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:29,399 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any particular subjects where boys are excelling or struggling at ITC University?', 'sql': "SELECT es.subject, AVG(CASE WHEN s.sex = 'M' THEN ae.grade END) AS average_grade,\n       COUNT(CASE WHEN s.sex = 'M' AND ae.grade IN ('A', 'A+', 'B', 'B+', 'B2', 'C', 'C6', 'C5', 'C4', 'C3', 'C2', 'C1') THEN 1 END) AS excelling_count,\n       COUNT(CASE WHEN s.sex = 'M' AND ae.grade IN ('D', 'E', 'F', 'FAIL', 'AWAIT', 'AWAITING RESULTS') THEN 1 END) AS struggling_count\nFROM applicant_exam_results ae\nJOIN students s ON ae.applicant_id = s.id\nJOIN exam_subjects es ON ae.subject_id = es.id\nWHERE s.sex = 'M'\nGROUP BY es.subject\nORDER BY average_grade DESC\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly identifies the subjects where male students ('boys') are either excelling or struggling by calculating the average grade and counting the number of students in both categories. It uses appropriate joins to link the tables and filters for male students. The use of GROUP BY allows for aggregation by subject, which is necessary to answer the question about specific subjects. The query also limits the results to the top 20 subjects based on average grade, which is a reasonable approach to focus on the most relevant subjects.", 'feedback': "The question could be clarified by specifying what 'excelling' and 'struggling' mean in terms of grade thresholds. Additionally, the SQL could be improved by removing the WHERE clause filtering for male students, as this is already handled in the CASE statements for counting excelling and struggling students. This would allow for a more comprehensive analysis of all students while still focusing on the male subset in the counts."}
2025-08-11 18:05:29,400 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:29,400 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 18:05:30,393 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:31,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:31,708 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance among boys in these various programs, particularly in the courses where they struggle?', 'answerable': False, 'reasoning': "The question seeks to understand the factors contributing to performance differences among boys in various programs, particularly in courses where they struggle. However, the schema does not provide specific data on performance factors or qualitative insights into student struggles. While there are tables related to student performance (like 'assessment_results', 'grades', and 'courses'), they do not include contextual or demographic factors that would help explain the differences in performance. Additionally, the schema lacks any direct references to qualitative data or surveys that might capture the reasons behind performance issues.", 'feedback': ''}
2025-08-11 18:05:31,708 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:31,708 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:31,708 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance among boys in these various programs, particularly in the courses where they struggle?', 'answerable': False, 'reasoning': "The question seeks to understand the factors contributing to performance differences among boys in various programs, particularly in courses where they struggle. However, the schema does not provide specific data on performance factors or qualitative insights into student struggles. While there are tables related to student performance (like 'assessment_results', 'grades', and 'courses'), they do not include contextual or demographic factors that would help explain the differences in performance. Additionally, the schema lacks any direct references to qualitative data or surveys that might capture the reasons behind performance issues.", 'feedback': ''}
2025-08-11 18:05:34,466 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:34,481 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think could contribute to the lack of data on boys' performance at ITC University, and how might this impact future assessments of academic performance?", 'answerable': False, 'reasoning': "The question is qualitative and seeks to explore factors contributing to a lack of data on boys' performance at ITC University, as well as the potential impacts on future assessments. The provided database schema contains structured data about students, programs, assessments, and other related entities, but it does not include qualitative insights or contextual information that would allow for a comprehensive analysis of the factors affecting data availability or performance assessments. Therefore, this question cannot be answered using the schema.", 'feedback': ''}
2025-08-11 18:05:34,481 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:34,481 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:34,481 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think could contribute to the lack of data on boys' performance at ITC University, and how might this impact future assessments of academic performance?", 'answerable': False, 'reasoning': "The question is qualitative and seeks to explore factors contributing to a lack of data on boys' performance at ITC University, as well as the potential impacts on future assessments. The provided database schema contains structured data about students, programs, assessments, and other related entities, but it does not include qualitative insights or contextual information that would allow for a comprehensive analysis of the factors affecting data availability or performance assessments. Therefore, this question cannot be answered using the schema.", 'feedback': ''}
2025-08-11 18:05:34,962 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:34,975 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in academic performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question seeks to understand the factors contributing to differences in academic performance between boys and girls at ITC University. However, the provided schema does not contain specific data or attributes that directly relate to gender-based performance analysis or the factors influencing academic performance. While there are tables related to students, courses, and assessments, there is no explicit data on gender differences or the specific factors affecting performance. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-11 18:05:34,976 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:34,976 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:34,976 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in academic performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question seeks to understand the factors contributing to differences in academic performance between boys and girls at ITC University. However, the provided schema does not contain specific data or attributes that directly relate to gender-based performance analysis or the factors influencing academic performance. While there are tables related to students, courses, and assessments, there is no explicit data on gender differences or the specific factors affecting performance. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-11 18:05:36,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:39,959 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:40,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:41,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:41,492 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance among boys in these various programs, particularly in the courses where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences among boys in various programs, specifically in courses where they struggle. The schema provided contains data about students, programs, courses, and their performance metrics, but it does not include qualitative factors or insights that would explain the reasons behind performance differences. The schema lacks information on social, psychological, or contextual factors that could influence performance, making it impossible to answer the question fully.', 'feedback': ''}
2025-08-11 18:05:41,492 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:41,492 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:41,492 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance among boys in these various programs, particularly in the courses where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences among boys in various programs, specifically in courses where they struggle. The schema provided contains data about students, programs, courses, and their performance metrics, but it does not include qualitative factors or insights that would explain the reasons behind performance differences. The schema lacks information on social, psychological, or contextual factors that could influence performance, making it impossible to answer the question fully.', 'feedback': ''}
2025-08-11 18:05:44,953 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:44,968 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in academic performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question seeks to understand the factors contributing to differences in academic performance between boys and girls at ITC University. However, the provided schema does not contain specific data or attributes that directly relate to gender-based performance analysis or the factors influencing academic performance. While there are tables related to students, courses, and assessments, there is no explicit data on gender differences or the specific factors affecting performance. Therefore, without additional context or data that directly addresses these factors, the question cannot be answered.', 'feedback': ''}
2025-08-11 18:05:44,968 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:44,968 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:44,968 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in academic performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question seeks to understand the factors contributing to differences in academic performance between boys and girls at ITC University. However, the provided schema does not contain specific data or attributes that directly relate to gender-based performance analysis or the factors influencing academic performance. While there are tables related to students, courses, and assessments, there is no explicit data on gender differences or the specific factors affecting performance. Therefore, without additional context or data that directly addresses these factors, the question cannot be answered.', 'feedback': ''}
2025-08-11 18:05:45,896 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:47,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:47,981 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:49,375 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:52,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:52,336 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think could contribute to the lack of data on boys' performance at ITC University, and how might this impact future assessments of academic performance?", 'answerable': False, 'reasoning': "The question is qualitative and seeks to explore factors contributing to a lack of data on boys' performance at ITC University, as well as the implications of this lack of data on future assessments. The provided database schema contains structured data about institutions, students, courses, and various academic records, but it does not include qualitative insights or analysis regarding the reasons for data gaps or their impacts. Therefore, the question cannot be answered directly or indirectly using the schema.", 'feedback': ''}
2025-08-11 18:05:52,336 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:52,336 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:52,337 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think could contribute to the lack of data on boys' performance at ITC University, and how might this impact future assessments of academic performance?", 'answerable': False, 'reasoning': "The question is qualitative and seeks to explore factors contributing to a lack of data on boys' performance at ITC University, as well as the implications of this lack of data on future assessments. The provided database schema contains structured data about institutions, students, courses, and various academic records, but it does not include qualitative insights or analysis regarding the reasons for data gaps or their impacts. Therefore, the question cannot be answered directly or indirectly using the schema.", 'feedback': ''}
2025-08-11 18:05:52,429 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:52,470 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance among boys in these various programs, particularly in the courses where they struggle?', 'answerable': False, 'reasoning': "The question seeks to understand the factors contributing to performance differences among boys in various programs, particularly in courses where they struggle. However, the provided schema does not contain specific data or attributes that directly relate to the analysis of performance factors, such as demographic data, psychological factors, or detailed course performance metrics that could be analyzed for trends. While there are tables related to student performance (like 'assessment_results' and 'student_programs'), they do not provide the necessary context or qualitative data to answer the question comprehensively.", 'feedback': ''}
2025-08-11 18:05:52,471 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:52,471 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:52,471 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance among boys in these various programs, particularly in the courses where they struggle?', 'answerable': False, 'reasoning': "The question seeks to understand the factors contributing to performance differences among boys in various programs, particularly in courses where they struggle. However, the provided schema does not contain specific data or attributes that directly relate to the analysis of performance factors, such as demographic data, psychological factors, or detailed course performance metrics that could be analyzed for trends. While there are tables related to student performance (like 'assessment_results' and 'student_programs'), they do not provide the necessary context or qualitative data to answer the question comprehensively.", 'feedback': ''}
2025-08-11 18:05:54,180 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:55,890 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:58,230 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:05:58,243 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the uniform performance of boys across subjects at ITC University, and how might this impact their overall educational experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the uniform performance of boys across subjects at ITC University, which requires qualitative insights and potentially external data or research. The provided database schema contains structured data about students, courses, assessments, and other educational metrics, but it does not include qualitative factors or insights that would explain performance trends. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-11 18:05:58,244 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:05:58,244 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:05:58,244 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the uniform performance of boys across subjects at ITC University, and how might this impact their overall educational experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the uniform performance of boys across subjects at ITC University, which requires qualitative insights and potentially external data or research. The provided database schema contains structured data about students, courses, assessments, and other educational metrics, but it does not include qualitative factors or insights that would explain performance trends. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-11 18:06:00,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:06:00,344 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think could contribute to the lack of data on boys' performance at ITC University, and how might this impact future assessments of academic performance?", 'answerable': False, 'reasoning': "The question is qualitative and seeks to explore factors contributing to a lack of data on boys' performance at ITC University, as well as the potential impacts on future assessments. The provided schema contains tables related to student data, academic performance, and institutional information, but it does not include qualitative insights or contextual factors that could explain the lack of data. Additionally, the schema does not provide any direct means to analyze or interpret the reasons behind data gaps or their implications. Therefore, the question cannot be answered using the schema.", 'feedback': ''}
2025-08-11 18:06:00,345 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:06:00,345 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:06:00,345 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think could contribute to the lack of data on boys' performance at ITC University, and how might this impact future assessments of academic performance?", 'answerable': False, 'reasoning': "The question is qualitative and seeks to explore factors contributing to a lack of data on boys' performance at ITC University, as well as the potential impacts on future assessments. The provided schema contains tables related to student data, academic performance, and institutional information, but it does not include qualitative insights or contextual factors that could explain the lack of data. Additionally, the schema does not provide any direct means to analyze or interpret the reasons behind data gaps or their implications. Therefore, the question cannot be answered using the schema.", 'feedback': ''}
2025-08-11 18:06:02,531 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:06:05,887 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:06:05,902 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the uniform performance of boys across subjects at ITC University, and how might this impact their overall educational experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the uniform performance of boys across subjects at ITC University, which requires qualitative insights and potentially external data or research. The provided database schema contains structured data about students, courses, assessments, and other educational metrics, but it does not include qualitative factors or insights that would explain performance trends. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-11 18:06:05,902 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:06:05,903 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:06:05,903 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the uniform performance of boys across subjects at ITC University, and how might this impact their overall educational experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the uniform performance of boys across subjects at ITC University, which requires qualitative insights and potentially external data or research. The provided database schema contains structured data about students, courses, assessments, and other educational metrics, but it does not include qualitative factors or insights that would explain performance trends. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-11 18:06:11,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:06:16,273 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:06:16,286 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the uniform performance of boys across subjects at ITC University, and how might this impact their overall educational experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the uniform performance of boys across subjects at ITC University, which involves qualitative insights and potentially external data not present in the schema. The schema primarily contains structured data about institutions, students, courses, and related entities, but does not provide the necessary qualitative context or analytical framework to address the question about performance factors and their impact on educational experience.', 'feedback': ''}
2025-08-11 18:06:16,287 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:06:16,287 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:06:16,287 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the uniform performance of boys across subjects at ITC University, and how might this impact their overall educational experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the uniform performance of boys across subjects at ITC University, which involves qualitative insights and potentially external data not present in the schema. The schema primarily contains structured data about institutions, students, courses, and related entities, but does not provide the necessary qualitative context or analytical framework to address the question about performance factors and their impact on educational experience.', 'feedback': ''}
2025-08-11 18:06:22,017 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:06:26,194 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:06:26,208 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the uniform performance of boys across subjects at ITC University, and how might this impact their overall educational experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the uniform performance of boys across subjects at ITC University, which requires qualitative insights and potentially external data or research. The provided database schema contains structured data about institutions, students, courses, and various academic records, but it does not include qualitative factors or insights that would explain performance trends. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-11 18:06:26,208 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 18:06:26,208 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 18:06:26,208 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the uniform performance of boys across subjects at ITC University, and how might this impact their overall educational experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the uniform performance of boys across subjects at ITC University, which requires qualitative insights and potentially external data or research. The provided database schema contains structured data about institutions, students, courses, and various academic records, but it does not include qualitative factors or insights that would explain performance trends. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-11 18:06:26,209 - root - INFO - [{'sex': 'F', 'average_finalscore': 68.72, 'total_results': 58}, {'sex': 'M', 'average_finalscore': 54.86, 'total_results': 71}]
2025-08-11 18:06:26,209 - root - INFO - [{'program_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'course_title': 'Instructional Media And Technologies For Teaching And Learning', 'finalscore': 60.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'POST INTERNSHIP SEMINAR', 'finalscore': 89.0, 'grade': 'A'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'INDUSTRIAL INTERNSHIP', 'finalscore': 87.0, 'grade': 'A'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'PSYCHOLOGY OF HUMAN DEVELOPMENT AND LEARNING', 'finalscore': 86.0, 'grade': 'A'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'MANAGEMENT INFORMATION SYSTEMS', 'finalscore': 82.0, 'grade': 'A'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'LONG ESSAY/PROJECT WORK', 'finalscore': 81.0, 'grade': 'A'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'MANAGING FINANCIAL RISK', 'finalscore': 76.11, 'grade': 'B+'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'BANK STRATEGIC INFORMATION MANAGEMENT', 'finalscore': 73.0, 'grade': 'B'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'QUANTITATIVE TECHNIQUES', 'finalscore': 72.0, 'grade': 'B'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'ECONOMY OF GHANA', 'finalscore': 67.0, 'grade': 'C+'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'RESEARCH METHODS', 'finalscore': 66.0, 'grade': 'C+'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'MICRO FINANCE AND RURAL BANKING', 'finalscore': 64.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'PRODUCTION AND OPERATIONS MANAGEMENT', 'finalscore': 63.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'PRINCIPLES OF BANKING', 'finalscore': 62.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'FINANCIAL INSTITUTIONS AND MARKETS', 'finalscore': 62.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'PRINCIPLES OF MANAGEMENT', 'finalscore': 61.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'BASIC ACCOUNTING II', 'finalscore': 60.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'LEADERSHIP DEVELOPMENT', 'finalscore': 58.0, 'grade': 'D+'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'Introduction To Human Resource Management', 'finalscore': 57.0, 'grade': 'D+'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'PRINCIPLES AND PRACTICE OF TEACHER EDUCATION', 'finalscore': 57.0, 'grade': 'D+'}]
2025-08-11 18:06:26,209 - root - INFO - 'No results'
2025-08-11 18:06:26,209 - root - INFO - 'No results'
2025-08-11 18:06:26,209 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-11 18:06:36,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:06:36,351 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-11 18:06:48,629 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:06:48,634 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:06:48,634 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 18:06:48,634 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:06:48,634 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 18:06:48,635 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do boys' academic results compare to those of girls at ITC University?...
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, girls have a higher average final score compared to boys. Girls achieved an avera...
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: bar_chart
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': 'F', 'average_finalscore': 68.72, 'total_results': 58}, {'sex': 'M', 'average_finalscore': ...
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_final_scores_by_sex
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-11 18:06:48,635 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-11 18:06:48,635 - celery.redirected - WARNING - [{'sex': 'F', 'average_finalscore': 68.72, 'total_results': 58}, {'sex': 'M', 'average_finalscore': 54.86, 'total_results': 71}]
2025-08-11 18:06:48,635 - celery.redirected - WARNING - ================================= 
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: average_final_scores_by_sex
2025-08-11 18:06:48,635 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-11 18:06:48,636 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:06:48,636 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 18:06:48,636 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:06:48,636 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-11 18:06:48,636 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 18:06:48,636 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 18:06:48,636 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 18:06:48,636 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 18:06:48,636 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are boys enrolled in at ITC University, and how do their performances vary ...
2025-08-11 18:06:48,636 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, boys are enrolled in various programs, with a notable concentration in the Bachel...
2025-08-11 18:06:48,636 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: bar_chart
2025-08-11 18:06:48,636 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'program_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'course_title': 'Instr...
2025-08-11 18:06:48,636 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: boys_enrollment_performance_by_program
2025-08-11 18:06:48,636 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-11 18:06:48,636 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-11 18:06:48,636 - celery.redirected - WARNING - [{'program_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'course_title': 'Instructional Media And Technologies For Teaching And Learning', 'finalscore': 60.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'POST INTERNSHIP SEMINAR', 'finalscore': 89.0, 'grade': 'A'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'INDUSTRIAL INTERNSHIP', 'finalscore': 87.0, 'grade': 'A'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'PSYCHOLOGY OF HUMAN DEVELOPMENT AND LEARNING', 'finalscore': 86.0, 'grade': 'A'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'MANAGEMENT INFORMATION SYSTEMS', 'finalscore': 82.0, 'grade': 'A'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'LONG ESSAY/PROJECT WORK', 'finalscore': 81.0, 'grade': 'A'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'MANAGING FINANCIAL RISK', 'finalscore': 76.11, 'grade': 'B+'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'BANK STRATEGIC INFORMATION MANAGEMENT', 'finalscore': 73.0, 'grade': 'B'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'QUANTITATIVE TECHNIQUES', 'finalscore': 72.0, 'grade': 'B'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'ECONOMY OF GHANA', 'finalscore': 67.0, 'grade': 'C+'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'RESEARCH METHODS', 'finalscore': 66.0, 'grade': 'C+'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'MICRO FINANCE AND RURAL BANKING', 'finalscore': 64.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'PRODUCTION AND OPERATIONS MANAGEMENT', 'finalscore': 63.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'PRINCIPLES OF BANKING', 'finalscore': 62.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'FINANCIAL INSTITUTIONS AND MARKETS', 'finalscore': 62.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'PRINCIPLES OF MANAGEMENT', 'finalscore': 61.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'BASIC ACCOUNTING II', 'finalscore': 60.0, 'grade': 'C'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'LEADERSHIP DEVELOPMENT', 'finalscore': 58.0, 'grade': 'D+'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'Introduction To Human Resource Management', 'finalscore': 57.0, 'grade': 'D+'}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'course_title': 'PRINCIPLES AND PRACTICE OF TEACHER EDUCATION', 'finalscore': 57.0, 'grade': 'D+'}]
2025-08-11 18:06:48,637 - celery.redirected - WARNING - ================================= 
2025-08-11 18:06:48,637 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: boys_enrollment_performance_by_program
2025-08-11 18:06:48,637 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-11 18:06:48,637 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:06:48,637 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 18:06:48,637 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:06:48,637 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-11 18:06:48,637 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 18:06:48,637 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 18:06:48,638 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 18:06:48,638 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 18:06:48,638 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any particular subjects where boys are excelling or struggling at ITC University?...
2025-08-11 18:06:48,638 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there is no available data on the performance of ...
2025-08-11 18:06:48,638 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-11 18:06:48,638 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-11 18:06:48,638 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: boys_subject_performance_at_itc
2025-08-11 18:06:48,638 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-11 18:06:48,638 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-11 18:06:48,638 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:06:48,638 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 18:06:48,639 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University?'
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 18:06:48,639 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How has boys' performance at ITC University changed over the past few academic years?...
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Unfortunately, there are no available results regarding boys' performance at ITC University over the...
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: boys_performance_trends_itc_university
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-11 18:06:48,639 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-11 18:06:48,639 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 4
2025-08-11 18:06:48,640 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:06:48,640 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-11 18:06:48,640 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 4 documents
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -     Content: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -     Content: Question: Are there any particular subjects where boys are excelling or struggling at ITC University...
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:06:26.208839+00:00', 'data_returned': False}
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -     Content: Question: How has boys' performance at ITC University changed over the past few academic years?
Answ...
2025-08-11 18:06:48,640 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:06:00.345296+00:00', 'data_returned': False}
2025-08-11 18:06:48,643 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 2/4
2025-08-11 18:06:48,824 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.181s]
2025-08-11 18:06:56,437 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:06:57,981 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:1.384s]
2025-08-11 18:06:57,982 - UPSERT_DOCS - INFO - ✅ Successfully upserted 4 documents to Elasticsearch
2025-08-11 18:06:57,982 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 6
2025-08-11 18:06:57,983 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-11 18:06:57,983 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:06:57,983 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-11 18:06:57,983 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:06:57,983 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/6...
2025-08-11 18:06:59,131 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:06:59,169 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:06:59,169 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 18:06:59,170 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:06:59,170 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'academic performance boys ITC University'
2025-08-11 18:06:59,170 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-11 18:06:59,170 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-11 18:06:59,404 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.234s]
2025-08-11 18:06:59,405 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 125
2025-08-11 18:06:59,795 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.390s]
2025-08-11 18:06:59,795 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 68
2025-08-11 18:07:00,265 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.469s]
2025-08-11 18:07:00,265 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 15
2025-08-11 18:07:01,050 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.784s]
2025-08-11 18:07:01,050 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 9
2025-08-11 18:07:02,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:07:03,126 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.998s]
2025-08-11 18:07:03,127 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 18:07:03,127 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:03,127 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-11 18:07:03,127 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:03,128 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:03,128 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:03,128 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:03,128 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:03,128 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:11.769861+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:03,128 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:03,128 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:03,129 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:03,129 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-11 18:07:03,130 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_gender
2025-08-11 18:07:03,130 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:03,130 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:03,130 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-11 18:07:03,130 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:03,130 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:03,130 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-11 18:07:03,130 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:03,130 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:11.769861+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:03,131 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-11 18:07:03,131 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:03,131 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:03,131 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:07:03,131 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (2716 chars):
2025-08-11 18:07:03,131 - app.chains.section_writer - INFO -    Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC University, girls have a higher average final score compared to boys. Girls achieved an average final score of 68.72 across 58 assessments, while boys had an average final score of 54.86 from 71 assessments. This indicates that, on average, girls performed better academically than boys in the assessments considered.
Data Tag: average_final_scores_by_gender

Question: How do boys' academic results ...
2025-08-11 18:07:06,241 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:07:06,247 - app.chains.section_writer - INFO - 🤖 AI generated section (633 chars):
2025-08-11 18:07:06,247 - app.chains.section_writer - INFO -    This report examines the academic performance of boys at ITC University, highlighting the differences in performance compared to their female peers. At ITC University, girls have a higher average final score compared to boys. Girls achieved an average final score of 68.72 across 58 assessments, whil...
2025-08-11 18:07:06,247 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_final_scores_by_gender']
2025-08-11 18:07:06,247 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 599 characters
2025-08-11 18:07:06,248 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/6...
2025-08-11 18:07:07,084 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:07:07,092 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:07:07,092 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 18:07:07,092 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:07:07,093 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'girls vs boys academic performance ITC University'
2025-08-11 18:07:07,093 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-11 18:07:07,093 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-11 18:07:07,553 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.460s]
2025-08-11 18:07:07,553 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 125
2025-08-11 18:07:07,872 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.318s]
2025-08-11 18:07:07,872 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 68
2025-08-11 18:07:08,222 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.349s]
2025-08-11 18:07:08,222 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 15
2025-08-11 18:07:08,446 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.223s]
2025-08-11 18:07:08,447 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 9
2025-08-11 18:07:09,819 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:07:10,477 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.656s]
2025-08-11 18:07:10,478 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 18:07:10,478 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:10,478 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-11 18:07:10,478 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:10,478 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:10,478 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:10,478 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:10,479 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:10,479 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:11.769861+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:10,479 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-11 18:07:10,479 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-11 18:07:10,480 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:10,480 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:26.449015+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_gender'}
2025-08-11 18:07:10,480 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_gender
2025-08-11 18:07:10,480 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:10,480 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:10,480 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-11 18:07:10,480 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:10,480 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:27.661518+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:10,481 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-11 18:07:10,481 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:07:10,481 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:11.769861+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:07:10,481 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-11 18:07:10,481 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-11 18:07:10,481 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-11 18:07:10,481 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_gpa_by_entry_mode
2025-08-11 18:07:10,481 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (2453 chars):
2025-08-11 18:07:10,481 - app.chains.section_writer - INFO -    Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC University, girls have a higher average final score compared to boys. Girls achieved an average final score of 68.72 across 58 assessments, while boys had an average final score of 54.86 from 71 assessments. This indicates that, on average, girls performed better academically than boys in the assessments considered.
Data Tag: average_final_scores_by_gender

Question: How do boys' academic results ...
2025-08-11 18:07:14,841 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:07:14,845 - app.chains.section_writer - INFO - 🤖 AI generated section (424 chars):
2025-08-11 18:07:14,845 - app.chains.section_writer - INFO -    ## 2. Comparative Academic Performance  
### 2.1 Overall Performance  
At ITC University, girls have a higher average final score compared to boys. Girls achieved an average final score of 68.72 across 58 assessments, while boys had an average final score of 54.86 from 71 assessments. This indicates...
2025-08-11 18:07:14,845 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['average_final_scores_by_gender']
2025-08-11 18:07:14,845 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 390 characters
2025-08-11 18:07:14,845 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/6...
2025-08-11 18:07:15,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:07:15,809 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:07:15,810 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 18:07:15,810 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:07:15,810 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Boys program enrollment Bachelor of Business Administration'
2025-08-11 18:07:15,810 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-11 18:07:15,810 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-11 18:07:16,049 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.239s]
2025-08-11 18:07:16,050 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 125
2025-08-11 18:07:16,263 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.213s]
2025-08-11 18:07:16,264 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 68
2025-08-11 18:07:16,512 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.247s]
2025-08-11 18:07:16,512 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 15
2025-08-11 18:07:16,832 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.320s]
2025-08-11 18:07:16,833 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 9
2025-08-11 18:07:18,578 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:07:19,475 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.875s]
2025-08-11 18:07:19,475 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 18:07:19,475 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:19,475 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:19,476 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:19,476 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:32.669602+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:19,476 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:19,476 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:19,476 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-11 18:07:19,476 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-11 18:07:19,476 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-11 18:07:19,476 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-11 18:07:19,477 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:19,477 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:19,477 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:07:19,477 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:19,477 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:32.669602+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:19,477 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:07:19,477 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:19,477 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:19,477 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:07:19,478 - app.chains.section_writer - INFO -   📄 Doc 4: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-11 18:07:19,478 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-11 18:07:19,478 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_performance_in_courses
2025-08-11 18:07:19,478 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-11 18:07:19,478 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-11 18:07:19,478 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_gpa_by_entry_mode
2025-08-11 18:07:19,478 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4669 chars):
2025-08-11 18:07:19,478 - app.chains.section_writer - INFO -    Question: What programs or courses are boys enrolled in at ITC University, and how do their performances vary across these programs?
Answer: At ITC University, boys are enrolled in various programs, primarily in the Bachelor of Business Administration (Banking and Finance) and Bachelor of Arts (Kasem with English Language Education). Their performances across these programs vary significantly. For instance, in the Bachelor of Arts program, a boy scored 60.0 (Grade C) in 'Instructional Media And ...
2025-08-11 18:07:38,320 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:07:38,336 - app.chains.section_writer - INFO - 🤖 AI generated section (1217 chars):
2025-08-11 18:07:38,337 - app.chains.section_writer - INFO -    ## 3. Enrollment and Program Distribution  
### 3.1 Programs Enrolled by Boys  
At ITC University, there is a notable concentration of male enrollment in the Bachelor of Business Administration (Banking and Finance) program. Boys enrolled in this program tend to perform significantly better compared...
2025-08-11 18:07:38,337 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['boys_enrollment_performance_by_program']
2025-08-11 18:07:38,337 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 1227 characters
2025-08-11 18:07:38,337 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/6...
2025-08-11 18:07:39,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:07:39,501 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:07:39,501 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 18:07:39,501 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:07:39,501 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'performance across courses'
2025-08-11 18:07:39,501 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-11 18:07:39,501 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-11 18:07:39,685 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.183s]
2025-08-11 18:07:39,685 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 125
2025-08-11 18:07:39,941 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.256s]
2025-08-11 18:07:39,942 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 68
2025-08-11 18:07:40,124 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.182s]
2025-08-11 18:07:40,124 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 15
2025-08-11 18:07:40,356 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.232s]
2025-08-11 18:07:40,357 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 9
2025-08-11 18:07:41,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:07:41,517 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.461s]
2025-08-11 18:07:41,517 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 18:07:41,517 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:41,518 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:41,518 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-11 18:07:41,518 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-11 18:07:41,518 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:41,518 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:41,518 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:41,518 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:32.669602+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:41,518 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-11 18:07:41,518 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-11 18:07:41,519 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:41,520 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:41,520 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:07:41,520 - app.chains.section_writer - INFO -   📄 Doc 2: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-11 18:07:41,520 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-11 18:07:41,520 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_performance_in_courses
2025-08-11 18:07:41,520 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:41,520 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:41,520 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:07:41,520 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:41,521 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:32.669602+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:41,521 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:07:41,521 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-11 18:07:41,521 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-11 18:07:41,521 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_gpa_by_entry_mode
2025-08-11 18:07:41,521 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4669 chars):
2025-08-11 18:07:41,521 - app.chains.section_writer - INFO -    Question: What programs or courses are boys enrolled in at ITC University, and how do their performances vary across these programs?
Answer: At ITC University, boys are enrolled in various programs, with a notable concentration in the Bachelor of Business Administration (Banking and Finance). Their performance across different courses varies significantly. For instance, the highest final score recorded is 89.0 in the course 'POST INTERNSHIP SEMINAR', which corresponds to an 'A' grade. Other cour...
2025-08-11 18:07:47,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:07:47,637 - app.chains.section_writer - INFO - 🤖 AI generated section (829 chars):
2025-08-11 18:07:47,637 - app.chains.section_writer - INFO -    ## 4. Performance Across Courses  

### 4.1 High-Performing Courses  
The course with the highest final score is 'POST INTERNSHIP SEMINAR', achieving a score of 89.0, which corresponds to a Grade A. Additionally, there are other strong performances across various courses, with several grades of A an...
2025-08-11 18:07:47,637 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['boys_performance_in_courses']
2025-08-11 18:07:47,638 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 798 characters
2025-08-11 18:07:47,638 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/6...
2025-08-11 18:07:49,194 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:07:49,200 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:07:49,200 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 18:07:49,200 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:07:49,200 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Factors Influencing Performance'
2025-08-11 18:07:49,200 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-11 18:07:49,200 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-11 18:07:49,426 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.225s]
2025-08-11 18:07:49,426 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 125
2025-08-11 18:07:49,631 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.205s]
2025-08-11 18:07:49,632 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 68
2025-08-11 18:07:49,869 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.237s]
2025-08-11 18:07:49,869 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 15
2025-08-11 18:07:50,163 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.294s]
2025-08-11 18:07:50,164 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 9
2025-08-11 18:07:51,853 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:07:53,406 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.549s]
2025-08-11 18:07:53,407 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 18:07:53,407 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:53,408 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:53,408 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-11 18:07:53,408 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-11 18:07:53,408 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-11 18:07:53,408 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-11 18:07:53,408 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:53,408 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:53,408 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:53,408 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:32.669602+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   📄 Doc 2: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_performance_in_courses
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   📄 Doc 3: Question: Are there any notable differences in performance based on the entry mode of boys at ITC Un...
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:29.126351+00:00', 'data_returned': True, 'data_tag': 'average_gpa_by_entry_mode'}
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_gpa_by_entry_mode
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:32.669602+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:07:53,409 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:07:53,410 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4669 chars):
2025-08-11 18:07:53,410 - app.chains.section_writer - INFO -    Question: What programs or courses are boys enrolled in at ITC University, and how do their performances vary across these programs?
Answer: At ITC University, boys are enrolled in various programs, with a notable concentration in the Bachelor of Business Administration (Banking and Finance). Their performance across different courses varies significantly. For instance, the highest final score recorded is 89.0 in the course 'POST INTERNSHIP SEMINAR', which corresponds to an 'A' grade. Other cour...
2025-08-11 18:08:03,701 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:08:03,707 - app.chains.section_writer - INFO - 🤖 AI generated section (1750 chars):
2025-08-11 18:08:03,707 - app.chains.section_writer - INFO -    ## 5. Factors Influencing Performance  

The performance of boys at ITC University is influenced by several key factors, including socioeconomic background, engagement in extracurricular activities, support systems, and psychological factors.

1. **Socioeconomic Background**: The socioeconomic statu...
2025-08-11 18:08:03,707 - app.chains.section_writer - INFO - ✅ Found 3 data tag placeholders: ['boys_enrollment_performance_by_program', 'boys_performance_in_courses', 'average_gpa_by_entry_mode']
2025-08-11 18:08:03,708 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 1662 characters
2025-08-11 18:08:03,708 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 6/6...
2025-08-11 18:08:05,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:08:05,052 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:08:05,052 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 18:08:05,052 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:08:05,052 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'boys performance ITC University summary recommendations'
2025-08-11 18:08:05,052 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are boys performing at ITC University?'
2025-08-11 18:08:05,052 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are boys performing at ITC University?'
2025-08-11 18:08:05,263 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.210s]
2025-08-11 18:08:05,263 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 125
2025-08-11 18:08:05,425 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.162s]
2025-08-11 18:08:05,426 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 68
2025-08-11 18:08:05,611 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.184s]
2025-08-11 18:08:05,611 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 15
2025-08-11 18:08:06,131 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.519s]
2025-08-11 18:08:06,131 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 9
2025-08-11 18:08:07,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 18:08:09,225 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.339s]
2025-08-11 18:08:09,225 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 18:08:09,226 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:08:09,226 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:08:09,226 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:08:09,226 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:32.669602+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:08:09,226 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:08:09,226 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:08:09,226 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-11 18:08:09,226 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-11 18:08:09,226 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:08:09,227 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:08:09,227 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:52.471788+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:35:32.669602+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What programs or courses are boys enrolled in at ITC University, and how do their performa...
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T18:40:21.282136+00:00', 'data_returned': True, 'data_tag': 'boys_enrollment_performance_by_program'}
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_enrollment_performance_by_program
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   📄 Doc 4: Question: Are there particular programs or courses at ITC University where boys excel or struggle mo...
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:47:27.701658+00:00', 'data_returned': True, 'data_tag': 'boys_performance_in_courses'}
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   ✅ Added Data Tag: boys_performance_in_courses
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   📄 Doc 5: Question: How do boys' academic results compare to those of girls at ITC University?
Answer: At ITC ...
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are boys performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T18:05:44.968621+00:00', 'data_returned': True, 'data_tag': 'average_final_scores_by_sex'}
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_final_scores_by_sex
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4454 chars):
2025-08-11 18:08:09,228 - app.chains.section_writer - INFO -    Question: What programs or courses are boys enrolled in at ITC University, and how do their performances vary across these programs?
Answer: At ITC University, boys are enrolled in various programs, with a notable concentration in the Bachelor of Business Administration (Banking and Finance). Their performance across different courses varies significantly. For instance, the highest final score recorded is 89.0 in the course 'POST INTERNSHIP SEMINAR', which corresponds to an 'A' grade. Other cour...
2025-08-11 18:08:18,216 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 18:08:18,358 - app.chains.section_writer - INFO - 🤖 AI generated section (1725 chars):
2025-08-11 18:08:18,359 - app.chains.section_writer - INFO -    ## 6. Summary and Recommendations  

The analysis of boys' performance at ITC University reveals a mixed picture. Boys are primarily enrolled in the Bachelor of Business Administration (Banking and Finance) and Bachelor of Arts (Kasem with English Language Education) programs. Their performance vari...
2025-08-11 18:08:18,359 - app.chains.section_writer - INFO - ✅ Found 3 data tag placeholders: ['boys_enrollment_performance_by_program', 'boys_performance_in_courses', 'average_final_scores_by_sex']
2025-08-11 18:08:18,359 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 6 completed and processed: 1676 characters
2025-08-11 18:08:18,360 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 18:08:18,360 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-11 18:08:18,360 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 18:08:18,360 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-11 18:08:18,361 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 7
2025-08-11 18:08:18,361 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 4
2025-08-11 18:08:18,361 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 2
2025-08-11 18:08:18,361 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250811_180253.log
2025-08-11 18:08:18,368 - celery.app.trace - INFO - Task generate_streaming_report[dc7f1de8-c16e-45cd-be5d-5fe8fefed555] succeeded in 325.1071201670002s: {'outline': '# Report on Boys\' Academic Performance at ITC University

## 1. Introduction  
   - This report examines the academic performance of boys at ITC University, highlighting the differences in performance compared to their female peers. Understanding these disparities is crucial for developing targeted interventions to enhance educational outcomes for boys.

## 2. Comparative Academic Performance  
   ### 2.1 Overall Performance  
   - Average final scores of boys vs. girls  
     - Girls: 68.72 (58 results)  
     - Boys: 54.86 (71 results)  
   - Conclusion: Girls outperform boys academically at ITC University  

## 3. Enrollment and Program Distribution  
   ### 3.1 Programs Enrolled by Boys  
   - Overview of programs with significant male enrollment  
     - Notable concentration in Bachelor of Business Administration (Banking and Finance)  

## 4. Performance Across Courses  
   ### 4.1 High-Performing Courses  
   - Course with highest final score: 89.0 in \'POST INTERNSHIP SEMINAR\' (Grade A) ...', ...}
2025-08-11 18:17:43,608 - celery.worker.consumer.consumer - WARNING - consumer: Connection to broker lost. Trying to re-establish the connection...
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 341, in start
    blueprint.start(self)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/bootsteps.py", line 116, in start
    step.start(parent)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 772, in start
    c.loop(*c.loop_args())
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/loops.py", line 97, in asynloop
    next(loop)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/asynchronous/hub.py", line 373, in create_loop
    cb(*cbargs)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 1352, in on_readable
    self.cycle.on_readable(fileno)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 569, in on_readable
    chan.handlers[type]()
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 918, in _receive
    ret.append(self._receive_one(c))
               ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 928, in _receive_one
    response = c.parse_response()
               ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 926, in parse_response
    response = self._execute(conn, try_read)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 902, in _execute
    return conn.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/retry.py", line 65, in call_with_retry
    fail(error)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 904, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 891, in _disconnect_raise_connect
    raise error
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/retry.py", line 62, in call_with_retry
    return do()
           ^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 903, in <lambda>
    lambda: command(*args, **kwargs),
            ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 924, in try_read
    return conn.read_response(disconnect_on_error=False, push_request=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/connection.py", line 613, in read_response
    response = self._parser.read_response(disable_decoding=disable_decoding)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/resp2.py", line 15, in read_response
    result = self._read_response(disable_decoding=disable_decoding)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/resp2.py", line 25, in _read_response
    raw = self._buffer.readline()
          ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/socket.py", line 115, in readline
    self._read_from_socket()
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/socket.py", line 68, in _read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
2025-08-11 18:17:43,615 - py.warnings - WARNING - /Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py:392: CPendingDeprecationWarning: 
In Celery 5.1 we introduced an optional breaking change which
on connection loss cancels all currently executed tasks with late acknowledgement enabled.
These tasks cannot be acknowledged as the connection is gone, and the tasks are automatically redelivered
back to the queue. You can enable this behavior using the worker_cancel_long_running_tasks_on_connection_loss
setting. In Celery 5.1 it is set to False by default. The setting will be set to True by default in Celery 6.0.

  warnings.warn(CANCEL_TASKS_BY_DEFAULT, CPendingDeprecationWarning)

2025-08-11 18:17:43,619 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 2.00 seconds... (1/100)

2025-08-11 18:17:45,633 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 4.00 seconds... (2/100)

2025-08-11 18:17:49,655 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 6.00 seconds... (3/100)

2025-08-11 18:17:55,676 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 8.00 seconds... (4/100)

2025-08-11 18:18:03,708 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 10.00 seconds... (5/100)

2025-08-11 18:18:13,748 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 12.00 seconds... (6/100)


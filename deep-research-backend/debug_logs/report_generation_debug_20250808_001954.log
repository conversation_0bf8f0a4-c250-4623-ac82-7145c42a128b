2025-08-08 00:19:54,326 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:19:54,326 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: test-task-123
2025-08-08 00:19:54,326 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:19:54,326 - REPORT_REQUEST - INFO - 📝 Original Question: 'How many students are there?'
2025-08-08 00:19:54,326 - REPORT_REQUEST - INFO - 🆔 Task ID: test-task-123
2025-08-08 00:19:54,326 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T00:19:54.326428
2025-08-08 00:19:54,507 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.181s]
2025-08-08 00:19:54,507 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 9
2025-08-08 00:19:54,642 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 00:19:54,642 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:19:54,889 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.246s]
2025-08-08 00:19:54,889 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (1):
2025-08-08 00:19:54,889 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (9 docs)
2025-08-08 00:19:54,889 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 00:20:01,374 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:01,401 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 00:20:05,355 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:05,370 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 00:20:08,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:08,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:08,496 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:08,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:08,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:08,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:09,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:09,412 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:09,808 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:10,186 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:10,249 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 00:20:10,249 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:20:10,249 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 00:20:10,249 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:20:10,250 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 9
2025-08-08 00:20:12,746 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:12,833 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:12,969 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:13,094 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:13,174 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:13,233 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:13,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:13,272 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:13,973 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:15,612 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:15,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:16,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:16,312 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:16,444 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:16,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:16,851 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:17,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:17,866 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:18,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:18,349 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:18,890 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:19,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:19,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:20,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:20,733 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:21,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:21,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:23,191 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:23,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:24,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:24,398 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:24,636 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:25,358 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:25,765 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:26,506 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:28,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:30,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:30,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:31,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:31,936 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:32,174 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:32,218 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:32,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:32,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:32,965 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:35,127 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:35,887 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:36,158 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:36,212 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:36,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:37,262 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:38,546 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:38,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:39,984 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:39,986 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:40,136 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:40,701 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:40,737 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:41,218 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:41,279 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:41,409 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:41,503 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:41,720 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:42,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:42,499 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:42,602 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:42,716 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:43,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:43,670 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:43,679 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:43,789 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:44,344 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:44,500 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:44,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:45,608 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:46,036 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:46,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:46,610 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:47,152 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:47,971 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:48,586 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:48,834 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:49,056 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:49,504 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:50,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:52,272 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:52,273 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:52,281 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:52,785 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:53,484 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:53,852 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:54,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:54,639 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:55,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:56,061 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:57,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:58,312 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:58,824 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:58,864 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:59,285 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:59,302 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:20:59,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:00,375 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:01,283 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:02,426 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:02,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:02,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:04,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:04,601 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:05,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:05,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:06,504 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:07,737 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:08,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:12,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:12,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:13,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:15,926 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:16,745 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:18,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:19,209 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:20,123 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:20,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:20,739 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:21,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:24,094 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:24,331 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:26,025 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:27,036 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:27,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:28,624 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:29,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:29,556 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:30,038 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:31,021 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:32,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:33,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:34,219 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:35,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:35,996 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:36,508 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:37,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:38,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:43,340 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:44,188 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:45,895 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:48,233 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:49,796 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:51,026 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:51,279 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:53,202 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:55,145 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:55,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:56,680 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:56,881 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:21:59,137 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:01,132 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:01,230 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:03,848 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:07,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:09,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:11,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:11,506 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:12,172 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:15,213 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:15,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:16,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:17,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:17,877 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:20,744 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:22,792 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:23,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:25,045 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:27,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:32,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:32,715 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:34,212 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:36,070 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:36,456 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:37,538 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:39,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:40,096 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:41,099 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:42,420 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:42,617 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:43,989 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:45,730 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:47,163 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:48,801 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:52,004 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:52,296 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:55,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:55,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:56,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:58,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:59,450 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:22:59,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:00,193 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:02,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:03,136 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:03,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:08,953 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:09,965 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:10,112 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:15,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:15,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:17,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:17,781 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:18,804 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:20,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:21,160 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:21,979 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:22,439 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:24,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:24,845 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:26,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:26,885 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:31,019 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:32,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:34,798 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:37,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:37,610 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:38,828 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:40,107 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:40,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:41,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:41,991 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:44,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:44,642 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:45,743 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:47,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:48,105 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:49,774 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:52,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:53,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:55,953 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:57,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:58,722 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:58,884 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:23:59,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:00,997 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:01,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:03,079 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:03,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:05,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:06,179 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:08,524 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:08,525 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:12,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:13,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:15,334 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:15,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:17,980 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:19,015 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:19,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:19,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:22,188 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:22,504 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:23,726 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:25,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:25,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:27,824 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:57,780 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:24:58,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:01,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:02,836 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:04,316 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:04,378 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:06,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:07,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:08,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:09,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:10,116 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:12,662 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:14,377 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:16,311 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:17,075 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:17,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:18,509 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:20,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:21,830 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:22,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:24,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:24,508 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:25,779 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:29,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:35,443 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:37,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:40,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:41,373 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:44,825 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:51,344 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:54,235 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:57,009 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:25:58,853 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:03,309 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:09,696 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:12,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:14,861 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:16,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:20,485 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:25,079 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:27,309 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:29,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:30,716 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:35,276 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:42,475 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:44,896 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:47,896 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:47,924 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 00:26:47,924 - root - INFO - 'No results'
2025-08-08 00:26:47,924 - root - INFO - 'No results'
2025-08-08 00:26:47,924 - root - INFO - 'No results'
2025-08-08 00:26:47,924 - root - INFO - 'No results'
2025-08-08 00:26:47,924 - root - INFO - 'No results'
2025-08-08 00:26:47,924 - root - INFO - 'No results'
2025-08-08 00:26:47,924 - root - INFO - 'No results'
2025-08-08 00:26:47,924 - root - INFO - 'No results'
2025-08-08 00:26:47,924 - root - INFO - 'No results'
2025-08-08 00:26:47,924 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-08 00:26:47,924 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 9
2025-08-08 00:26:47,924 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 00:26:52,376 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:26:52,381 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 00:27:00,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:00,608 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,608 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 00:27:00,608 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,608 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/9
2025-08-08 00:27:00,608 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,608 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:27:00,608 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,608 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:27:00,608 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:27:00,608 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:27:00,608 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:27:00,608 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contributed to the significant increase in student enrollment from 2021 to...
2025-08-08 00:27:00,608 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:27:00,609 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:27:00,609 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/9
2025-08-08 00:27:00,609 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,609 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:27:00,609 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors typically influence student activity levels, and how might we gather data to assess the...
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:27:00,609 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:27:00,609 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:27:00,609 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/9
2025-08-08 00:27:00,609 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,610 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:27:00,610 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contribute to the popularity of the 'Bachelor of Education (Junior High Sc...
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:27:00,610 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:27:00,610 - REPORT_PIPELINE - INFO - 🔄 Processing interview 4/9
2025-08-08 00:27:00,610 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,610 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:27:00,610 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:27:00,610 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors contribute to the significant differences in enrollment numbers across the various camp...
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:27:00,611 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:27:00,611 - REPORT_PIPELINE - INFO - 🔄 Processing interview 5/9
2025-08-08 00:27:00,611 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,611 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:27:00,611 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What challenges do you think institutions face in collecting and maintaining accurate demographic da...
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:27:00,611 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:27:00,612 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:27:00,612 - REPORT_PIPELINE - INFO - 🔄 Processing interview 6/9
2025-08-08 00:27:00,612 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,612 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:27:00,612 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How many students are part of each entry mode, such as direct entry or transfer?...
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:27:00,612 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:27:00,612 - REPORT_PIPELINE - INFO - 🔄 Processing interview 7/9
2025-08-08 00:27:00,612 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,612 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:27:00,612 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,612 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you believe are driving this dramatic increase in student enrollment since 2021?...
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:27:00,613 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:27:00,613 - REPORT_PIPELINE - INFO - 🔄 Processing interview 8/9
2025-08-08 00:27:00,613 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,613 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:27:00,613 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:27:00,613 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do these eligibility criteria impact the diversity of students receiving financial aid or schola...
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:27:00,614 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:27:00,614 - REPORT_PIPELINE - INFO - 🔄 Processing interview 9/9
2025-08-08 00:27:00,614 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,614 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:27:00,614 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contribute to the higher enrollment in full-time degree programs compared ...
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:27:00,614 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:27:00,615 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:27:00,615 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:27:00,615 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:27:00,615 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:27:00,615 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:27:00,615 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 9
2025-08-08 00:27:00,615 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:00,615 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 00:27:00,615 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 9 documents
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contributed to the significant increase in student enrollment fr...
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:25:17.089287+00:00', 'data_returned': False}
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -     Content: Question: What factors typically influence student activity levels, and how might we gather data to ...
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:21:05.665021+00:00', 'data_returned': False}
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contribute to the popularity of the 'Bachelor of Education (Juni...
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:20:52.288972+00:00', 'data_returned': False}
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -     Content: Question: What factors contribute to the significant differences in enrollment numbers across the va...
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:21:36.522989+00:00', 'data_returned': False}
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -     Content: Question: What challenges do you think institutions face in collecting and maintaining accurate demo...
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:20:49.563061+00:00', 'data_returned': False}
2025-08-08 00:27:00,615 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -     Content: Question: How many students are part of each entry mode, such as direct entry or transfer?
Answer: I...
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:26:47.923365+00:00', 'data_returned': False}
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -     Content: Question: What factors do you believe are driving this dramatic increase in student enrollment since...
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:20:52.305446+00:00', 'data_returned': False}
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -   📄 Doc 8:
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -     Content: Question: How do these eligibility criteria impact the diversity of students receiving financial aid...
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:25:24.439843+00:00', 'data_returned': False}
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -   📄 Doc 9:
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contribute to the higher enrollment in full-time degree programs...
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:20:49.078222+00:00', 'data_returned': False}
2025-08-08 00:27:00,616 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 0/9
2025-08-08 00:27:00,757 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.141s]
2025-08-08 00:27:01,308 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:05,068 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:3.217s]
2025-08-08 00:27:05,070 - UPSERT_DOCS - INFO - ✅ Successfully upserted 9 documents to Elasticsearch
2025-08-08 00:27:05,660 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.589s]
2025-08-08 00:27:05,660 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 18
2025-08-08 00:27:06,020 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.359s]
2025-08-08 00:27:06,020 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:07,231 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:1.211s]
2025-08-08 00:27:07,232 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (1):
2025-08-08 00:27:07,232 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (18 docs)
2025-08-08 00:27:07,232 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 10
2025-08-08 00:27:07,232 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 00:27:07,233 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:07,233 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-08 00:27:07,233 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:07,233 - REPORT_PIPELINE - INFO - ✍️ Writing 10 sections using batch processing...
2025-08-08 00:27:07,871 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:07,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:07,877 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:07,877 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:07,877 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:27:07,877 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:27:07,877 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:07,877 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:07,877 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student population assessment conclusion insights recommendations'
2025-08-08 00:27:07,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:07,878 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Appendices data charts survey instruments'
2025-08-08 00:27:07,878 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:27:07,878 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:27:07,879 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:27:07,879 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:27:07,881 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:07,881 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:27:07,881 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:07,881 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'factors influencing student enrollment'
2025-08-08 00:27:07,881 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:27:07,881 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:27:07,972 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:07,977 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:07,977 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:27:07,977 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:07,977 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'current student population demographics comparison'
2025-08-08 00:27:07,977 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:27:07,977 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:27:08,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:08,008 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:08,008 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:27:08,008 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:08,008 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'References Interview Participants Resources'
2025-08-08 00:27:08,008 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:27:08,008 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:27:08,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:08,054 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:08,055 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:27:08,055 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:08,055 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Interview findings themes data enrollment challenges'
2025-08-08 00:27:08,055 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:27:08,055 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:27:08,067 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.188s]
2025-08-08 00:27:08,068 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 18
2025-08-08 00:27:08,075 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:08,079 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:08,079 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:27:08,079 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:08,079 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Interview analysis trends contrasts implications'
2025-08-08 00:27:08,079 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:27:08,079 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:27:08,127 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:08,135 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:08,135 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:27:08,135 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:08,135 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Student population data implications'
2025-08-08 00:27:08,135 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:27:08,135 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:27:08,146 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.264s]
2025-08-08 00:27:08,146 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.266s]
2025-08-08 00:27:08,147 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 18
2025-08-08 00:27:08,147 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 18
2025-08-08 00:27:08,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:08,182 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:08,183 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:27:08,183 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:08,183 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'current student population demographics trends'
2025-08-08 00:27:08,183 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:27:08,183 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:27:08,288 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.219s]
2025-08-08 00:27:08,288 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:08,293 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.316s]
2025-08-08 00:27:08,294 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.146s]
2025-08-08 00:27:08,294 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:08,294 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 18
2025-08-08 00:27:08,295 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.148s]
2025-08-08 00:27:08,295 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:08,303 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.295s]
2025-08-08 00:27:08,304 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 18
2025-08-08 00:27:08,327 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.272s]
2025-08-08 00:27:08,327 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 18
2025-08-08 00:27:08,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:08,410 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:08,411 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:27:08,411 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:08,411 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology data collection interviews limitations'
2025-08-08 00:27:08,411 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 00:27:08,411 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 00:27:08,448 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.144s]
2025-08-08 00:27:08,448 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:08,461 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.166s]
2025-08-08 00:27:08,461 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 00:27:08,465 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.386s]
2025-08-08 00:27:08,465 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 18
2025-08-08 00:27:08,470 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.143s]
2025-08-08 00:27:08,470 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.176s]
2025-08-08 00:27:08,470 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:08,470 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 00:27:08,543 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.256s]
2025-08-08 00:27:08,544 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 00:27:08,601 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.466s]
2025-08-08 00:27:08,602 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.419s]
2025-08-08 00:27:08,602 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.308s]
2025-08-08 00:27:08,603 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 18
2025-08-08 00:27:08,603 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 18
2025-08-08 00:27:08,603 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:08,605 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.156s]
2025-08-08 00:27:08,605 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 00:27:08,613 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.147s]
2025-08-08 00:27:08,613 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:08,617 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.147s]
2025-08-08 00:27:08,618 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.157s]
2025-08-08 00:27:08,618 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 00:27:08,618 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:27:08,624 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.153s]
2025-08-08 00:27:08,625 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:27:08,792 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.188s]
2025-08-08 00:27:08,793 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:08,795 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.189s]
2025-08-08 00:27:08,795 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.251s]
2025-08-08 00:27:08,796 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.192s]
2025-08-08 00:27:08,796 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.385s]
2025-08-08 00:27:08,796 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:27:08,796 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:27:08,796 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:08,796 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 18
2025-08-08 00:27:08,803 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.198s]
2025-08-08 00:27:08,804 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.191s]
2025-08-08 00:27:08,804 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 00:27:08,804 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.186s]
2025-08-08 00:27:08,804 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 00:27:08,805 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:27:09,004 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:09,213 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:09,218 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.420s]
2025-08-08 00:27:09,218 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.413s]
2025-08-08 00:27:09,218 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 00:27:09,219 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:27:09,224 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.425s]
2025-08-08 00:27:09,224 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:27:09,234 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.429s]
2025-08-08 00:27:09,234 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:09,235 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:27:09,278 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:09,280 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.487s]
2025-08-08 00:27:09,282 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 00:27:09,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:09,593 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:09,622 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.403s]
2025-08-08 00:27:09,623 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.340s]
2025-08-08 00:27:09,623 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:27:09,623 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:27:09,685 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.460s]
2025-08-08 00:27:09,685 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 00:27:09,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:09,950 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.941s]
2025-08-08 00:27:09,950 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:27:09,950 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:27:10,068 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:10,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:10,246 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.561s]
2025-08-08 00:27:10,246 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:27:10,251 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.034s]
2025-08-08 00:27:10,251 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:27:10,251 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:27:10,438 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.156s]
2025-08-08 00:27:10,438 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:27:10,438 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:27:10,456 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.216s]
2025-08-08 00:27:10,456 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:27:10,456 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:27:10,479 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.408s]
2025-08-08 00:27:10,480 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:27:10,480 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:27:10,495 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.208s]
2025-08-08 00:27:10,495 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:27:10,495 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:27:10,514 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.918s]
2025-08-08 00:27:10,515 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.569s]
2025-08-08 00:27:10,515 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:27:10,515 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:27:10,515 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:27:10,516 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:27:10,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:27:10,990 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.765s]
2025-08-08 00:27:10,990 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:27:10,991 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:27:11,212 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.446s]
2025-08-08 00:27:11,212 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:27:11,212 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:27:12,888 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:13,413 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:14,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:15,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:15,112 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:15,254 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:15,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:15,627 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:16,119 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:16,648 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:16,676 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 370 characters
2025-08-08 00:27:16,677 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1357 characters
2025-08-08 00:27:16,677 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1098 characters
2025-08-08 00:27:16,677 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 1263 characters
2025-08-08 00:27:16,677 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1826 characters
2025-08-08 00:27:16,678 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 1210 characters
2025-08-08 00:27:16,678 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 1452 characters
2025-08-08 00:27:16,678 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 1039 characters
2025-08-08 00:27:16,678 - REPORT_PIPELINE - INFO -   ✅ Section 9 completed: 839 characters
2025-08-08 00:27:16,678 - REPORT_PIPELINE - INFO -   ✅ Section 10 completed: 562 characters
2025-08-08 00:27:20,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:27:20,119 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,119 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,120 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,120 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,120 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,120 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,120 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,120 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,120 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,120 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,120 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:27:20,120 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:27:20,120 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-08 00:27:20,120 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:27:20,120 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-08 00:27:20,120 - REPORT_PIPELINE - INFO - 📊 Final report sections: 11
2025-08-08 00:27:20,120 - REPORT_PIPELINE - INFO - 📈 Extended data items: 0
2025-08-08 00:27:20,120 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_001954.log

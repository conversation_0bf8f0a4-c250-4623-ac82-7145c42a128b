2025-08-08 08:19:27,231 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_081927.log
2025-08-08 08:19:27,231 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:19:27,231 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 0355cddf-ffaa-4cb4-9c87-f1dcb81dcf6a
2025-08-08 08:19:27,231 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:19:27,231 - REPORT_REQUEST - INFO - 📝 Original Question: 'string'
2025-08-08 08:19:27,231 - REPORT_REQUEST - INFO - 🆔 Task ID: 0355cddf-ffaa-4cb4-9c87-f1dcb81dcf6a
2025-08-08 08:19:27,231 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T08:19:27.231696
2025-08-08 08:19:27,365 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 08:19:27,365 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 15
2025-08-08 08:19:27,497 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-08 08:19:27,497 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 6
2025-08-08 08:19:27,629 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.131s]
2025-08-08 08:19:27,629 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (2):
2025-08-08 08:19:27,629 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 08:19:27,629 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (7 docs)
2025-08-08 08:19:27,629 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 08:19:34,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:34,114 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 08:19:38,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:38,050 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 08:19:43,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:44,096 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:44,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:45,548 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:45,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:45,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:45,654 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:45,703 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:46,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:46,951 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:47,012 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 08:19:47,013 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:19:47,013 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 08:19:47,013 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:19:47,013 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 5
2025-08-08 08:19:49,623 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:49,815 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:49,854 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:49,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:50,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:52,246 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:52,409 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:52,812 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:53,663 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:53,687 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:53,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:53,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:53,976 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:55,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:55,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:56,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:58,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:58,462 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:58,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:19:58,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:01,117 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:01,141 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What payment plans or financial aid options are available to students at the institution to help manage their fees?', 'sql': 'SELECT DISTINCT type AS financial_aid_type FROM financial_aid_requests UNION SELECT DISTINCT type AS financial_aid_type FROM student_scholarships;', 'correct': True, 'reasoning': "The SQL query retrieves distinct types of financial aid from both the 'financial_aid_requests' and 'student_scholarships' tables. This aligns with the question, which asks for available financial aid options. The use of UNION ensures that all unique types from both sources are included, providing a comprehensive view of the financial aid options available to students at the institution.", 'feedback': 'The SQL query is well-structured for the question. However, it could be improved by adding a WHERE clause to filter results based on the specific institution if that information is available in the schema, ensuring that the results are relevant to the institution in question.'}
2025-08-08 08:20:01,141 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:01,141 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 08:20:03,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:03,975 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:03,987 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific programs or courses at the institution that have higher outstanding fees?', 'sql': 'SELECT p.long_name AS program_name, c.title AS course_title, SUM(sb.balance) AS total_outstanding_fees\nFROM student_balances sb\nJOIN student_bills sb2 ON sb.student_id = sb2.student_id\nJOIN programs p ON sb.student_program_id = p.id\nJOIN courses c ON sb2.bill_id = c.id\nWHERE sb.balance > 0\nGROUP BY p.long_name, c.title\nORDER BY total_outstanding_fees DESC;', 'correct': True, 'reasoning': "The SQL query correctly identifies programs and courses associated with students who have outstanding fees. It aggregates the total outstanding fees by program and course, filtering for balances greater than zero. The use of JOINs ensures that the relevant data from the student_balances, student_bills, programs, and courses tables are combined appropriately. The GROUP BY clause groups the results by program and course, and the ORDER BY clause sorts the results by total outstanding fees in descending order, which aligns with the question's focus on identifying higher outstanding fees.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly mentioning the institution_id in the WHERE clause to ensure that the results are filtered for a specific institution, assuming that the question pertains to a particular institution.'}
2025-08-08 08:20:03,987 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:03,987 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 08:20:04,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:05,139 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:05,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:05,633 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:05,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:05,712 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:06,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:06,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:07,443 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:08,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:08,737 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:08,821 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:09,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:09,727 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:10,443 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:10,974 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:10,993 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Can you elaborate on the eligibility criteria for these financial aid options and how students can apply for them?', 'answerable': False, 'reasoning': "The schema does not contain specific information regarding the eligibility criteria for financial aid options or the application process for students. While there are tables related to financial aid requests (e.g., 'core.financial_aid_requests' and 'core.financial_aid_request_students'), they do not provide detailed descriptions of eligibility criteria or application procedures. Therefore, the question cannot be answered based on the provided schema.", 'feedback': ''}
2025-08-08 08:20:10,994 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:10,994 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:10,994 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'Can you elaborate on the eligibility criteria for these financial aid options and how students can apply for them?', 'answerable': False, 'reasoning': "The schema does not contain specific information regarding the eligibility criteria for financial aid options or the application process for students. While there are tables related to financial aid requests (e.g., 'core.financial_aid_requests' and 'core.financial_aid_request_students'), they do not provide detailed descriptions of eligibility criteria or application procedures. Therefore, the question cannot be answered based on the provided schema.", 'feedback': ''}
2025-08-08 08:20:12,013 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:12,316 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:12,386 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors typically contribute to outstanding fees in educational programs, and how does the institution monitor or address these issues?', 'answerable': False, 'reasoning': "The question asks for factors contributing to outstanding fees in educational programs and how institutions monitor or address these issues. While the schema contains various tables related to students, fees, and transactions (like 'student_bills', 'student_transactions', and 'bills'), it does not provide explicit information about the factors contributing to outstanding fees or the specific monitoring and addressing mechanisms used by the institution. The schema lacks qualitative data or descriptions that would help answer the question comprehensively.", 'feedback': ''}
2025-08-08 08:20:12,386 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:12,386 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:12,386 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors typically contribute to outstanding fees in educational programs, and how does the institution monitor or address these issues?', 'answerable': False, 'reasoning': "The question asks for factors contributing to outstanding fees in educational programs and how institutions monitor or address these issues. While the schema contains various tables related to students, fees, and transactions (like 'student_bills', 'student_transactions', and 'bills'), it does not provide explicit information about the factors contributing to outstanding fees or the specific monitoring and addressing mechanisms used by the institution. The schema lacks qualitative data or descriptions that would help answer the question comprehensively.", 'feedback': ''}
2025-08-08 08:20:13,026 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:13,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:14,477 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:14,494 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:14,611 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:15,241 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:15,251 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Can you elaborate on the eligibility criteria for these financial aid options and how students can apply for them?', 'answerable': False, 'reasoning': 'The question asks for specific details about the eligibility criteria for financial aid options and the application process for students. However, the provided schema does not contain any tables or fields that explicitly describe the eligibility criteria or the application process for financial aid. While there are tables related to financial aid requests and transactions, they do not provide the necessary information to answer the question comprehensively.', 'feedback': ''}
2025-08-08 08:20:15,251 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:15,251 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:15,251 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'Can you elaborate on the eligibility criteria for these financial aid options and how students can apply for them?', 'answerable': False, 'reasoning': 'The question asks for specific details about the eligibility criteria for financial aid options and the application process for students. However, the provided schema does not contain any tables or fields that explicitly describe the eligibility criteria or the application process for financial aid. While there are tables related to financial aid requests and transactions, they do not provide the necessary information to answer the question comprehensively.', 'feedback': ''}
2025-08-08 08:20:15,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:16,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:16,307 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors typically contribute to outstanding fees in educational programs, and how does the institution monitor or address these issues?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to outstanding fees in educational programs and how the institution monitors or addresses these issues. While the schema contains various tables related to students, fees, and transactions, it does not provide explicit information about the factors contributing to outstanding fees or the specific monitoring and addressing mechanisms employed by the institution. The schema lacks qualitative data or descriptions that would help answer the question comprehensively.', 'feedback': ''}
2025-08-08 08:20:16,307 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:16,307 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:16,307 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors typically contribute to outstanding fees in educational programs, and how does the institution monitor or address these issues?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to outstanding fees in educational programs and how the institution monitors or addresses these issues. While the schema contains various tables related to students, fees, and transactions, it does not provide explicit information about the factors contributing to outstanding fees or the specific monitoring and addressing mechanisms employed by the institution. The schema lacks qualitative data or descriptions that would help answer the question comprehensively.', 'feedback': ''}
2025-08-08 08:20:17,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:18,956 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:19,039 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:19,075 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Can you elaborate on the eligibility criteria for these financial aid options and how students can apply for them?', 'answerable': False, 'reasoning': 'The question asks for specific details about the eligibility criteria for financial aid options and the application process for students. However, the provided schema does not contain any tables or fields that explicitly describe the eligibility criteria or the application process for financial aid. While there are tables related to financial aid requests and transactions, they do not provide the necessary information to answer the question comprehensively.', 'feedback': ''}
2025-08-08 08:20:19,075 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:19,075 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:19,075 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'Can you elaborate on the eligibility criteria for these financial aid options and how students can apply for them?', 'answerable': False, 'reasoning': 'The question asks for specific details about the eligibility criteria for financial aid options and the application process for students. However, the provided schema does not contain any tables or fields that explicitly describe the eligibility criteria or the application process for financial aid. While there are tables related to financial aid requests and transactions, they do not provide the necessary information to answer the question comprehensively.', 'feedback': ''}
2025-08-08 08:20:19,086 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:20,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:20,467 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What percentage of students at the institution are currently in debt regarding their fees?', 'sql': 'SELECT (COUNT(CASE WHEN sb.balance < 0 THEN 1 END) * 100.0 / COUNT(*)) AS percentage_in_debt FROM student_balances sb JOIN students s ON sb.student_id = s.id WHERE sb.institution_id = [specific institution ID];', 'correct': True, 'reasoning': "The SQL query correctly calculates the percentage of students who are in debt by counting the number of students with a negative balance and dividing it by the total number of students at the specified institution. The use of a JOIN between the 'student_balances' and 'students' tables ensures that only students from the specified institution are considered. The calculation of the percentage is done correctly by multiplying the count of students in debt by 100.0 to convert it to a percentage format.", 'feedback': "To improve clarity, the placeholder '[specific institution ID]' should be replaced with an actual institution ID or a parameterized query should be used to allow for dynamic input. Additionally, it might be helpful to explicitly state in the question whether the percentage should be rounded to a certain number of decimal places."}
2025-08-08 08:20:20,467 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:20,468 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 08:20:21,174 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:21,360 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:21,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:21,792 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors typically contribute to outstanding fees in educational programs, and how does the institution monitor or address these issues?', 'answerable': False, 'reasoning': 'The question asks for a qualitative analysis of factors contributing to outstanding fees in educational programs and how institutions monitor or address these issues. The schema primarily contains structured data about various entities related to educational institutions, such as students, programs, fees, and transactions. However, it does not provide insights into qualitative factors or institutional policies regarding fee management. Therefore, while the schema can provide data on outstanding fees, it cannot answer the broader question about the contributing factors and monitoring strategies.', 'feedback': ''}
2025-08-08 08:20:21,792 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:21,792 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:21,792 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors typically contribute to outstanding fees in educational programs, and how does the institution monitor or address these issues?', 'answerable': False, 'reasoning': 'The question asks for a qualitative analysis of factors contributing to outstanding fees in educational programs and how institutions monitor or address these issues. The schema primarily contains structured data about various entities related to educational institutions, such as students, programs, fees, and transactions. However, it does not provide insights into qualitative factors or institutional policies regarding fee management. Therefore, while the schema can provide data on outstanding fees, it cannot answer the broader question about the contributing factors and monitoring strategies.', 'feedback': ''}
2025-08-08 08:20:22,116 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:23,018 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:23,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:23,712 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:24,575 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:25,415 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:25,433 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Can you elaborate on the eligibility criteria for these financial aid options and how students can apply for them?', 'answerable': False, 'reasoning': "The schema does not contain specific information regarding the eligibility criteria for financial aid options or the application process for students. While there are tables related to financial aid requests (e.g., 'core.financial_aid_requests' and 'core.financial_aid_request_students'), they do not provide detailed descriptions of eligibility criteria or application procedures. Therefore, the question cannot be answered based on the provided schema.", 'feedback': ''}
2025-08-08 08:20:25,434 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:25,434 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:25,434 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'Can you elaborate on the eligibility criteria for these financial aid options and how students can apply for them?', 'answerable': False, 'reasoning': "The schema does not contain specific information regarding the eligibility criteria for financial aid options or the application process for students. While there are tables related to financial aid requests (e.g., 'core.financial_aid_requests' and 'core.financial_aid_request_students'), they do not provide detailed descriptions of eligibility criteria or application procedures. Therefore, the question cannot be answered based on the provided schema.", 'feedback': ''}
2025-08-08 08:20:26,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:26,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:26,673 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors typically contribute to outstanding fees in educational programs, and how does the institution monitor or address these issues?', 'answerable': False, 'reasoning': 'The question asks for a qualitative analysis of factors contributing to outstanding fees in educational programs and how institutions monitor or address these issues. The schema primarily contains structured data about various entities related to educational institutions, such as students, programs, fees, and transactions. However, it does not provide insights into qualitative factors or institutional policies regarding fee management. Therefore, while the schema can provide data on outstanding fees, it cannot answer the broader question regarding the contributing factors and monitoring strategies.', 'feedback': ''}
2025-08-08 08:20:26,674 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:26,674 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:26,674 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors typically contribute to outstanding fees in educational programs, and how does the institution monitor or address these issues?', 'answerable': False, 'reasoning': 'The question asks for a qualitative analysis of factors contributing to outstanding fees in educational programs and how institutions monitor or address these issues. The schema primarily contains structured data about various entities related to educational institutions, such as students, programs, fees, and transactions. However, it does not provide insights into qualitative factors or institutional policies regarding fee management. Therefore, while the schema can provide data on outstanding fees, it cannot answer the broader question regarding the contributing factors and monitoring strategies.', 'feedback': ''}
2025-08-08 08:20:26,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:27,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:27,035 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific fees are students owing at the institution where students owe the most fees?', 'sql': 'WITH InstitutionFees AS (  SELECT sb.institution_id, SUM(b.total_due) AS total_fees  FROM student_bills sb  JOIN bills b ON sb.bill_id = b.id  GROUP BY sb.institution_id),  MaxInstitution AS (  SELECT institution_id  FROM InstitutionFees  ORDER BY total_fees DESC  LIMIT 1)  SELECT b.id AS bill_id, b.total_due, sb.student_id  FROM student_bills sb  JOIN bills b ON sb.bill_id = b.id  WHERE sb.institution_id = (SELECT institution_id FROM MaxInstitution);', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the highest total fees owed by students by first calculating the total fees for each institution and then selecting the institution with the maximum total fees. It then retrieves the specific fees (bill_id and total_due) owed by students at that institution. The use of Common Table Expressions (CTEs) effectively breaks down the problem into manageable parts, ensuring clarity and correctness in the final selection of fees.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by including additional details in the final SELECT statement, such as the student_program_id or created_at date, to provide more context about the fees.'}
2025-08-08 08:20:27,035 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:27,036 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 08:20:29,090 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:29,116 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What measures does the institution have in place to support students who may be struggling to pay their fees?', 'answerable': False, 'reasoning': 'The question asks about the measures an institution has in place to support students struggling to pay their fees. However, the provided schema does not contain specific information about support measures or programs for students in financial distress. While there are tables related to financial aid requests, student transactions, and billing, there is no direct indication of policies or support measures for students facing financial difficulties. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 08:20:29,117 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:29,117 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:29,117 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What measures does the institution have in place to support students who may be struggling to pay their fees?', 'answerable': False, 'reasoning': 'The question asks about the measures an institution has in place to support students struggling to pay their fees. However, the provided schema does not contain specific information about support measures or programs for students in financial distress. While there are tables related to financial aid requests, student transactions, and billing, there is no direct indication of policies or support measures for students facing financial difficulties. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 08:20:29,187 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:30,494 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:31,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:31,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:33,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:33,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:33,897 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What measures does the institution have in place to support students who may be struggling to pay their fees?', 'answerable': False, 'reasoning': 'The question asks about specific measures that an institution has in place to support students struggling to pay their fees. However, the provided schema does not contain any direct information regarding support measures, financial aid programs, or specific policies related to fee payment assistance. While there are tables related to financial aid requests and student transactions, they do not explicitly detail the measures or support systems in place for students facing financial difficulties. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 08:20:33,897 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:33,897 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:33,898 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What measures does the institution have in place to support students who may be struggling to pay their fees?', 'answerable': False, 'reasoning': 'The question asks about specific measures that an institution has in place to support students struggling to pay their fees. However, the provided schema does not contain any direct information regarding support measures, financial aid programs, or specific policies related to fee payment assistance. While there are tables related to financial aid requests and student transactions, they do not explicitly detail the measures or support systems in place for students facing financial difficulties. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 08:20:35,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:35,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:36,146 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:38,383 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:38,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:38,707 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What measures does the institution have in place to support students who may be struggling to pay their fees?', 'answerable': False, 'reasoning': 'The question asks about the measures an institution has in place to support students struggling to pay their fees. However, the provided schema does not contain specific information about support measures or programs for students in financial distress. While there are tables related to financial aid requests, student transactions, and billing, there is no direct indication of specific support measures or policies. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 08:20:38,707 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:38,707 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:38,707 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What measures does the institution have in place to support students who may be struggling to pay their fees?', 'answerable': False, 'reasoning': 'The question asks about the measures an institution has in place to support students struggling to pay their fees. However, the provided schema does not contain specific information about support measures or programs for students in financial distress. While there are tables related to financial aid requests, student transactions, and billing, there is no direct indication of specific support measures or policies. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 08:20:40,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:41,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:42,096 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:42,137 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the primary reasons or circumstances that have led to such high outstanding fees among these students?', 'answerable': False, 'reasoning': 'The question asks for the primary reasons or circumstances leading to high outstanding fees among students. While the schema contains various tables related to students, fees, and transactions, it does not explicitly provide a direct way to analyze the reasons or circumstances behind outstanding fees. The schema includes tables for student transactions, bills, and balances, but it lacks qualitative data or contextual information that would explain the reasons for outstanding fees, such as student circumstances, financial aid requests, or specific reasons for non-payment. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 08:20:42,137 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:42,138 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:42,138 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are the primary reasons or circumstances that have led to such high outstanding fees among these students?', 'answerable': False, 'reasoning': 'The question asks for the primary reasons or circumstances leading to high outstanding fees among students. While the schema contains various tables related to students, fees, and transactions, it does not explicitly provide a direct way to analyze the reasons or circumstances behind outstanding fees. The schema includes tables for student transactions, bills, and balances, but it lacks qualitative data or contextual information that would explain the reasons for outstanding fees, such as student circumstances, financial aid requests, or specific reasons for non-payment. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 08:20:44,993 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:45,018 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What measures does the institution have in place to support students who may be struggling to pay their fees?', 'answerable': False, 'reasoning': 'The question asks about the measures an institution has in place to support students struggling to pay their fees. However, the provided schema does not contain specific information about support measures or programs for students in financial distress. While there are tables related to financial aid requests, student transactions, and billing, there is no direct indication of policies or measures specifically aimed at supporting students who are struggling to pay their fees. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 08:20:45,018 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:45,018 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:45,018 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What measures does the institution have in place to support students who may be struggling to pay their fees?', 'answerable': False, 'reasoning': 'The question asks about the measures an institution has in place to support students struggling to pay their fees. However, the provided schema does not contain specific information about support measures or programs for students in financial distress. While there are tables related to financial aid requests, student transactions, and billing, there is no direct indication of policies or measures specifically aimed at supporting students who are struggling to pay their fees. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 08:20:45,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:45,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:48,741 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:48,756 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the primary reasons or circumstances that have led to such high outstanding fees among these students?', 'answerable': False, 'reasoning': 'The question asks for the primary reasons or circumstances leading to high outstanding fees among students. While the schema contains various tables related to students, fees, and transactions, it does not explicitly provide a direct way to analyze the reasons behind outstanding fees. The schema includes tables for student transactions, billing periods, and student statuses, but it lacks qualitative data or specific reasons that could explain the circumstances leading to outstanding fees. Therefore, without additional context or data that captures reasons for outstanding fees, the question cannot be answered.', 'feedback': ''}
2025-08-08 08:20:48,756 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:48,757 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:48,757 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are the primary reasons or circumstances that have led to such high outstanding fees among these students?', 'answerable': False, 'reasoning': 'The question asks for the primary reasons or circumstances leading to high outstanding fees among students. While the schema contains various tables related to students, fees, and transactions, it does not explicitly provide a direct way to analyze the reasons behind outstanding fees. The schema includes tables for student transactions, billing periods, and student statuses, but it lacks qualitative data or specific reasons that could explain the circumstances leading to outstanding fees. Therefore, without additional context or data that captures reasons for outstanding fees, the question cannot be answered.', 'feedback': ''}
2025-08-08 08:20:49,063 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:51,762 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:54,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:54,526 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:54,553 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the primary reasons or circumstances that have led to such high outstanding fees among these students?', 'answerable': False, 'reasoning': 'The question asks for the primary reasons or circumstances leading to high outstanding fees among students. However, the provided schema does not contain specific information about the reasons or circumstances behind outstanding fees. While there are tables related to student transactions, bills, and balances, there is no direct link or data that explains the reasons for outstanding fees. To answer this question, additional context or data regarding student financial situations, reasons for non-payment, or related circumstances would be necessary, which is not present in the schema.', 'feedback': ''}
2025-08-08 08:20:54,553 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:54,553 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:54,553 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are the primary reasons or circumstances that have led to such high outstanding fees among these students?', 'answerable': False, 'reasoning': 'The question asks for the primary reasons or circumstances leading to high outstanding fees among students. However, the provided schema does not contain specific information about the reasons or circumstances behind outstanding fees. While there are tables related to student transactions, bills, and balances, there is no direct link or data that explains the reasons for outstanding fees. To answer this question, additional context or data regarding student financial situations, reasons for non-payment, or related circumstances would be necessary, which is not present in the schema.', 'feedback': ''}
2025-08-08 08:20:57,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:58,873 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:59,732 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:20:59,753 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the primary reasons or circumstances that have led to such high outstanding fees among these students?', 'answerable': False, 'reasoning': "The question asks for the primary reasons or circumstances leading to high outstanding fees among students. However, the provided schema does not contain specific information about the reasons or circumstances behind outstanding fees. While there are tables related to student transactions, bills, and balances, there is no direct link to reasons or circumstances that would explain why fees are outstanding. Additional context or data regarding student financial situations, such as financial aid requests, payment history, or specific circumstances affecting students' ability to pay, would be necessary to answer this question.", 'feedback': ''}
2025-08-08 08:20:59,753 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:20:59,753 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:20:59,753 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are the primary reasons or circumstances that have led to such high outstanding fees among these students?', 'answerable': False, 'reasoning': "The question asks for the primary reasons or circumstances leading to high outstanding fees among students. However, the provided schema does not contain specific information about the reasons or circumstances behind outstanding fees. While there are tables related to student transactions, bills, and balances, there is no direct link to reasons or circumstances that would explain why fees are outstanding. Additional context or data regarding student financial situations, such as financial aid requests, payment history, or specific circumstances affecting students' ability to pay, would be necessary to answer this question.", 'feedback': ''}
2025-08-08 08:21:10,834 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:10,839 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the total amount owed by students at the institution compare to previous years?', 'sql': "SELECT ay.start_year, SUM(b.total_due) AS total_amount_owed\nFROM bills b\nJOIN billing_periods bp ON b.billing_period_id = bp.id\nJOIN academic_years ay ON bp.description LIKE CONCAT('%', ay.start_year, '%')\nWHERE ay.status = 'Active'\nGROUP BY ay.start_year\nORDER BY ay.start_year;", 'correct': True, 'reasoning': "The SQL query correctly aggregates the total amount owed by students for each academic year by summing the 'total_due' from the 'bills' table. It joins the 'bills' table with 'billing_periods' to link the bills to their respective periods and then joins with 'academic_years' to filter and group the results by the start year of each academic year. The use of 'LIKE' in the join condition assumes that the billing period description contains the academic year, which is a reasonable approach given the schema. The query also filters for active academic years, which aligns with the question's focus on current comparisons. Overall, it provides a clear comparison of total amounts owed across different years.", 'feedback': "The question could be clarified by specifying whether it seeks a comparison of total amounts owed year-over-year or a trend analysis over multiple years. Additionally, the SQL could be improved by ensuring that the join condition between 'billing_periods' and 'academic_years' is robust, possibly by using a more explicit relationship if available, rather than relying on a 'LIKE' match."}
2025-08-08 08:21:10,839 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:21:10,840 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 08:21:13,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:14,719 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:15,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:16,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:19,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:21,420 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:21,433 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contributed to the dramatic fluctuations in the total amount owed by students over these years?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to fluctuations in the total amount owed by students over the years. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing these fluctuations. The schema lacks historical data trends, contextual information, or specific attributes that could be analyzed to answer the question effectively.', 'feedback': ''}
2025-08-08 08:21:21,433 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:21:21,434 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:21:21,434 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contributed to the dramatic fluctuations in the total amount owed by students over these years?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to fluctuations in the total amount owed by students over the years. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing these fluctuations. The schema lacks historical data trends, contextual information, or specific attributes that could be analyzed to answer the question effectively.', 'feedback': ''}
2025-08-08 08:21:24,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:26,444 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:26,458 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contributed to the dramatic fluctuations in the total amount owed by students over these years?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to fluctuations in the total amount owed by students over the years. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing these fluctuations. The schema lacks historical data trends, contextual information, or specific attributes that could be analyzed to answer the question effectively.', 'feedback': ''}
2025-08-08 08:21:26,458 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:21:26,459 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:21:26,459 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contributed to the dramatic fluctuations in the total amount owed by students over these years?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to fluctuations in the total amount owed by students over the years. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing these fluctuations. The schema lacks historical data trends, contextual information, or specific attributes that could be analyzed to answer the question effectively.', 'feedback': ''}
2025-08-08 08:21:29,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:32,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:32,391 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contributed to the dramatic fluctuations in the total amount owed by students over these years?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to fluctuations in the total amount owed by students over the years. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing these fluctuations. The schema lacks historical data trends, contextual information, or specific financial factors that could be correlated with the amounts owed. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-08 08:21:32,391 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:21:32,391 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:21:32,391 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contributed to the dramatic fluctuations in the total amount owed by students over these years?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to fluctuations in the total amount owed by students over the years. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing these fluctuations. The schema lacks historical data trends, contextual information, or specific financial factors that could be correlated with the amounts owed. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-08 08:21:34,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:37,233 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:37,257 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contributed to the dramatic fluctuations in the total amount owed by students over these years?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to fluctuations in the total amount owed by students over the years. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing these fluctuations. The schema lacks historical data trends, contextual information, or specific financial factors that could be correlated with the amounts owed. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-08 08:21:37,258 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 08:21:37,258 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 08:21:37,258 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contributed to the dramatic fluctuations in the total amount owed by students over these years?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to fluctuations in the total amount owed by students over the years. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing these fluctuations. The schema lacks historical data trends, contextual information, or specific financial factors that could be correlated with the amounts owed. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-08 08:21:37,259 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 08:21:37,260 - root - INFO - [{'bill_id': 6, 'total_due': 10020.0, 'student_id': '0e965ba3-3256-465b-9d22-9484b4fc071f'}, {'bill_id': 7, 'total_due': 10020.0, 'student_id': '0e965ba3-3256-465b-9d22-9484b4fc071f'}, {'bill_id': 7, 'total_due': 10020.0, 'student_id': 'a0b0b8ba-ac03-4d7d-bfbc-47abb40f365d'}, {'bill_id': 8, 'total_due': 3600.0, 'student_id': '688112bb-46dd-4c97-ae1d-f7962de5d6cf'}, {'bill_id': 12, 'total_due': 5000.0, 'student_id': '000248bc-3343-8eaa-29fa-ceadc8813259'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '000248bc-3343-8eaa-29fa-ceadc8813259'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '33619a04-2078-84c2-4450-760d7a3da4e9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'eb145785-7255-0358-2304-8ee7403a8037'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6be0ef4b-326f-7025-762d-5360b6ce0db1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cc3b795d-39f7-802a-2593-5f65cbe7a571'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd83fd770-3f63-5f09-5998-9ce0839a84bc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fc8d2b8d-43dc-a70f-854d-3550d7c41a69'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '45c79e3f-2dcc-73c4-4524-f49fa7d05247'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '188c9605-014b-54f2-204d-a698864a10eb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'be03db91-86b1-12ac-734f-cd7a5dd55439'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'db01dee9-5232-909c-9605-d4b04b5395b5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1da07ca4-72bd-4542-9861-a48d986d31cd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a907a755-2f55-6159-34ea-e0a9f83f73a3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '87d1d0e5-9d6a-6309-936e-0ff8e3b89602'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8e3eb2d2-1a20-19f7-5075-0877efd6785f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9e27be6b-4baa-6b65-5605-f0586d61918a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '14f477f0-822f-2fb2-2e4d-d18bd10f0cbe'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2dfd619e-6d21-0fbf-3b8c-2c405ecb0bcf'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6db4c139-1cf1-22b9-28ab-8e4a32665d66'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '352c885b-0d8b-a598-9fd5-6803eda243f4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bd89b7d1-5ea0-4368-87bd-347f5fdc2ef2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '21541732-81c9-840c-19dc-391affe72cf4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '611351aa-6571-0dcc-7c9b-d1951c459891'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1e8ccac0-2c3a-03a5-2817-30f1ad9512c0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a08194ee-9324-139e-6b58-cbfa3b466b04'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '81a1d828-8c77-5b39-22d1-ba815bb30cb0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '323b73ce-1dcc-8811-5738-4ca23a8813b1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd0060e74-7ebc-282c-4fba-c9a8f0415b60'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '36a5b656-a2f9-943a-623f-05f3f9a58942'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '39ab1074-6907-7af5-601e-1c0918e395b0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd50da43a-2892-0b1b-5ed1-596288689603'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c9cf591e-912f-9894-0cff-cb6e4872051d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c2631455-8cf4-6f28-01fa-46bafad0828c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4cc6d1ec-60b3-2c9a-104e-b62faaa48ff3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c7117ecf-8e9e-3459-597d-b87f73d02c60'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c08bea27-9687-6d41-8100-ee0a16459ee3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'da31ba01-26f9-6002-3347-a8852d5a1549'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1c23cf1c-4ac5-a6fc-769b-01c91efd5ce5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4fddf9ad-0686-60ea-6ee8-5e7015253f47'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1e66c6c4-36cd-4f10-3357-53e6e92c624f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '327c5502-1be8-860c-5d13-85bb1d547fc3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '207aaa95-7868-7019-568d-aa8c839c6a79'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1e3d0b53-4104-01d1-a4aa-dedef5d55e54'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '611182ce-8971-3e4c-95d5-ca4754a6a04c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3bcb4f6c-3fdf-7c6e-0cea-fbcff1a00e14'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '513f88e1-3174-8658-702b-049ed5383194'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a41a97ba-94ee-4a75-6696-a84eeae35397'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a479b44a-3216-9caf-17a8-f19acb9f4f72'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '491b2406-50d9-667c-58a6-d2c044d81d47'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ba218c16-0d70-1a0d-9fb9-3893723205b2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8bdea7c6-27ac-0185-5026-009e77b741b3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '48031dde-62f8-5033-56b1-0ae6fb6100cd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '107b1349-7d26-2d67-24b6-c30c710752fb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7ec6283b-5877-2d26-1a4e-885baf022431'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0b831178-1e8b-8bdf-91f2-164d68ae3e23'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '794062be-8e68-1cef-6a7e-1631c2e7520d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4629d5e7-2dac-561c-8547-fb8a43c68985'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5ba734ad-9cba-4a74-6395-e5a920672a7b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3bd62cb9-1f69-1233-4899-d92203742418'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7a42dda8-1a55-19f0-540a-6499cdba0f5d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '16977c98-80db-19b8-1e74-e5f07fb985a9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5ead574f-35fe-1721-270e-01a53224541f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '318fc6ac-1ee9-7a83-975c-7d28567a144c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '936a1a1c-9220-91d4-52c1-8b2a7c079e07'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c8138258-93e4-5197-3a3b-1e8bd9869604'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5fcd5a5f-9d20-60ba-6220-9ad50c81a42b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '566cf270-6da5-21ca-2d72-9081a9ce68f4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9ce0e2d3-48a1-64cb-6159-c4c504f25f40'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7891ff96-4b07-839a-8a1f-7b87571da365'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '86e92dba-7efb-6e46-75e8-a3a90ca672f7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '33016f6d-0913-8e22-3682-6131eefb09df'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2a746a0e-4a06-270c-4a25-65387273304d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0609ab7d-0a68-a31b-55d0-37b318a30e80'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ef7af3a3-7d44-1c83-9d09-c342456676ce'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f5db58d2-8ccd-7436-300e-982b35495336'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3270f1c8-a31c-1f70-436c-e0af4beb4e6d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1a1f2996-902d-7d2a-48c4-90b35e4f8e98'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '264ade00-66b5-25a5-73d5-5608812d846f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cf01c60d-885e-6c79-23e7-a2e24beda495'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '97ac1588-5092-1694-1f36-8eac2a2e649e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '31c64564-38b4-1114-95a3-509168ff32f3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cc9b80fc-57ab-72a6-0c93-8d53694d2751'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8efa1110-5072-65e2-4c0e-c36ad4fc47e6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5218df03-7bff-4014-4f7c-fd3b3072886c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3dd7cc24-5f17-9d38-5a4f-510cfec5922b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f3e2e958-0691-8d4a-a168-14741535012e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe68428f-32fd-70ab-0975-f0cf4fec8a2d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd77e9907-5a85-a665-425d-74a5a3db59a8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4a0e4953-6c0e-66f2-51a9-13f9a80e01a2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '85ebd148-5328-2cab-85f9-887e01490c83'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8a621fcb-4d5b-54ff-2266-40ea7fada343'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f32334f7-a4ce-5df3-6bcd-1e643511903e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'de4914d5-5805-6830-88fe-5000afc99381'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6f118ccd-4338-310d-7163-aa7bc3390945'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8092f043-5eeb-83cb-0567-61602b62254c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe7484e6-8148-84d4-89da-fc95d4628f53'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f9321517-1e10-9201-8977-be4105296840'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '54a9b86b-2673-a616-96ed-ec8329a82459'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1e489d72-313b-8a07-5fe7-7bc2e3dd3422'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5e6acbae-93cd-53ba-1d01-93ab42a56b36'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5e83828e-3c59-3458-5082-cd116bbe3d18'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '75547f79-0603-1283-8615-0c3376a925e0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2dd394cb-57c5-38a6-8802-51b2fe994dfe'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3cfdc3e2-84c8-932b-0a29-0b2e6bf522e8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a0cbd0e3-a23f-5056-4328-485925974ca7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9b231d96-3af4-a11a-5223-c739f94b8dc0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '390fed05-6d87-88a6-a422-7125e3c20435'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5ad36545-804b-818a-2887-a0eb533a4219'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '800f6bbd-2346-1c75-874a-3f0c9fdd22a7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '812bca28-2213-3c78-6289-63f364d63557'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '54210494-0d2a-944c-7708-0c0f823537b4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f3da8cfc-1fe3-3f5d-3cad-ca075fa26725'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e5670b68-8436-879f-2ac0-85b17b734dd5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cbdbe18f-3b85-42af-152e-5aed61e0595b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a75b8fdb-6518-8942-8fbe-20b7b15434fa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'eebdc83e-4323-21e4-58a9-3984ffd781bf'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2da6f4e7-4668-83f2-01d5-51876f387be3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9a364a15-6e44-5b56-05c5-55d707451cfc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9fb72723-9b50-101b-5820-244a70472c44'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '369f7159-3036-6f8f-7bb7-ad4df93a0a0f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8beb93a8-7538-09fa-173a-4de445365c84'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3ca21109-83e3-a2eb-5a95-8552fe0a9326'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8f727cba-a7a5-6aea-2491-b03b8b975775'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '75215040-983f-8a25-98e2-46812186131b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9b3042e6-763a-3836-4207-093e5b171721'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '335e7e54-2497-a08c-a493-53cbd5332cb4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fd680413-5e4c-274c-923f-d0071ac29f5a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f8ba91c8-51a3-4461-618e-6bf558e45cbd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0fdff8c0-9b01-5b27-153a-5a6847ff07f0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7e397955-6428-10ff-3e90-223c2d551c05'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e6be56c2-63b8-891a-94e6-5ab4213b328c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'eaf42042-a378-606a-66b3-bd02a5c46db1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2f36452d-41aa-156d-a1bf-63e13f8d1a39'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b113d6d0-9eb0-6088-78bd-9b177c553986'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9934e926-4187-1ec9-6348-73f2d19c5490'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '52743e5c-60c7-1c76-1082-83883cd132df'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e053d552-a20f-a572-a60e-7b1369856b2c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4b1b404e-36d8-a3b1-6c7e-dfc17c121463'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f6376fe6-37c5-179d-7647-39fc69337fa9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2478ff0c-8ebb-04f8-6cc9-ca3df4414efa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '08b92dd7-70d3-18fb-9561-c90cba2d8aab'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5273e048-8370-24c9-3497-f5d6717197bd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '72132a16-8fc6-2b52-6c10-7eb6c44d59d8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'eb67b5b2-206d-531b-8755-05579a0831d6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1bd804c2-a4cb-979e-9e8d-671e136f1987'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b2ffd0c8-062f-8e7d-9625-252c69205621'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '061fb097-2688-530f-9e8e-50feb88b5e90'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2c63a07f-2970-7acd-14d2-118abe366d59'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0911a474-2c4f-7229-5315-93839cb6195f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '25fa71a6-3f71-1ccf-4adb-b68577f7122c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c5cb75bc-5b45-8a8f-068a-1dc8c4276e5c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '67d17ddd-69ff-6031-098d-b4e0e2651afd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c42cee94-76f4-2a4e-7e9e-8294ff2b2386'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4a61f5d6-6846-2c26-1a9f-c82f7a7d0e61'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '77601030-7151-5ae0-6f2b-17523bd511fa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9469792a-4c56-1118-7a98-22ef66e79e92'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '849ffafe-7ecb-130c-681a-fa7e05177374'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1ee75750-65f3-1785-1040-882fc3555ae3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0b9cd997-0ec0-2a50-702f-293017c74d56'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b8d6ad1a-381a-4297-1e42-202aec478276'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c37540bc-a4f1-1658-7ba8-41ae1f9109d4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9af17981-9952-a248-0aff-80f26d858052'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '30197397-7396-0691-5086-4567bee22b8e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e7ea02ea-7904-7f08-54c1-0bdce17d68e0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f324264b-8566-1c30-86c4-4e62e69d90d6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '228467af-27cf-4199-94d5-e2451c493495'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '47074658-7909-8a5e-3a18-9b5261a737dc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '618eccfa-57d2-1a1d-44c9-aaa4bea6021b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b796d24b-a797-5785-937f-4d1e13697d2a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '18cc4799-6b47-3fab-99bf-1ca098352b2b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b512897c-22b0-4bed-74d7-363da57056fa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '406d0648-86ca-7f3d-562f-c71e38855133'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3f864573-6f5c-156f-12e8-5708ee615db5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c3d3d9c2-88fb-0329-5585-a9ad016d4318'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '23f9aa78-7af2-35cf-385b-8ab3cf8d8bb1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '62c4e8ec-4e91-88f5-64c9-c9e83af541c1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '23ab09a6-911f-502e-741b-9f66454d8ece'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '39c8d0b6-a615-505b-25f6-1abbf7c03e32'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1165fe63-a5b1-8f99-29d9-f3824b9c494d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'afa4c0c4-959f-9077-7893-5b40418889ac'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '82435985-92ae-5e0f-08c1-e5f30b034e07'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5ca3041a-3c1b-4366-1da3-4fd85ee6a6dc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0bbf6b03-a221-48c0-2d6c-347cddd248dd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b1bf35b9-37d3-4faf-5454-921a9d5e2c47'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7f1d32b5-33f2-4fa5-a657-0f30a9674c6e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4d700bbb-6bb4-67b4-3d92-d5d44579097f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b77fbb57-0200-23c3-4cf3-9615ce433f96'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b700fa89-3c73-1aad-40ea-b21b31a30240'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'adbe7436-6ad6-00b5-7386-6384ac3e9605'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a7bbbfcc-942f-880b-330e-ee64a28c22bf'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5594ddbe-72b1-1974-1149-ae7ad7648656'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2304d8fd-27cd-a72f-65b8-cb5eb37c306a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4b2fb3e5-47e8-19df-6d12-ca79e02730c0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f5150584-4299-6d4e-0b3d-057ca3d21d87'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f9dc09a6-3d13-3e8a-72b0-aa5a252d4631'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3e2d1057-6628-54d8-56ce-9ebf8bf3598a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7742eb6a-05d2-a58b-61b5-885bdd74221d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bd761654-0f90-6920-1832-be14e3669f08'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '993809e6-4863-43e8-59f2-2a88e38f6e50'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8872228b-7f1c-6dbb-56a7-759906db26fc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1903db1a-615f-a5d7-576f-5a9c2a7c3b9f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '238f23e0-a56a-05fb-8a7d-742384053883'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c490b8ba-1704-2ffb-940e-adb6af391543'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9952d41d-17cf-5be4-5bb4-00b5814b73d8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd9122059-43de-001f-9f3a-c4effb586ffe'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1425ea33-365e-5297-6dfb-84f0a29d770f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2808662f-8815-1778-1693-b6d485b80d1b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b1af76db-241b-623f-90be-68b7fdc729e6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '848c946b-948e-3980-5288-644f352e46f3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '206c29a6-2a7c-14d9-8e0e-87bbc7560c45'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3030a9d1-8199-86db-8835-1799c87b6612'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fa4b76cd-57a7-10cd-55ac-474e8ac57305'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3f22683e-8908-46e4-a4be-61d9cdf8ae5b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1f6c73f6-3cb6-9f79-15c8-573f58d44b8f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b281459d-4365-799b-88e0-5b4fc02d7080'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f1ac7a5e-7a8e-9d4d-5d69-2de1df844634'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd769c3ac-20be-7b2f-631b-10aab025696c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6a8435b4-2185-4ff3-6a09-b792e835211e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b8804197-8128-3fa8-0d0f-439e292f11ad'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3606d3bf-164c-4b34-06fd-55bdfd364e9d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '766e4aa2-527c-a512-83b5-4a60c227892a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8a840939-7574-2550-9c4b-e8e138832a4c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e4e88083-3ff9-9a7d-89ce-1d6ea9ce6d2a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4675d79a-7532-7ca6-6fb4-c2960b2d612f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '21cf4c57-24b9-6ca7-6e56-fbcdd15d8ee2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4e9cf2be-55ed-83b3-134d-6631b6b44814'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '43ef313d-1495-8826-4d00-219f3b5e199d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a1d50ed4-849e-583a-8466-a8bd1bfe3b13'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2e23f09b-8037-72db-6922-15fdee019843'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f0e3d2fb-24f4-1382-7cb9-06c532af934c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '86e75922-62e5-68e3-8562-fe4cf28c7e66'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '029343f8-6120-4558-6258-2676cb041343'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'db43e3a2-6bd7-6dbd-3476-4bc986ce6242'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0b93ca2e-2e08-1631-283c-c3ed75439df8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a668eb71-70f1-32aa-1bcf-a3adafc86eb1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '97741a0b-8df3-496d-6f4e-9628105f372e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cc8941a9-2b55-2f0d-34a4-b0ced6bf0142'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bacbc144-6e2e-0533-0f6a-134abe9c74a5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd42e9564-9f26-1d34-7e8b-b071b8190191'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '77f8dca8-828e-1700-73f1-3b5f3da52b1b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '677bb49a-459f-3974-7299-b2268eae5b8e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '179adc9b-4d9a-18e5-25bb-f45a10fe79de'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3052097e-424e-78ce-98a8-695328497d78'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe11d1c4-17b7-2a0e-6b4f-24fe7a2030fd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6d70e03a-95d4-0fdd-1c6b-8fba5e992f89'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '126b86d0-27b9-2003-9aab-651432ad90b6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '67d55c1a-a636-7bfd-145e-27e8fec15091'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a05dd6bb-2072-0400-7c48-3be995af8a82'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7c4b1806-1706-1494-1cd7-d0bd4686a5a8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e606eb58-035a-7824-3c2e-d91fc96c6146'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '918f0a76-781b-6401-02a3-06743fe10e5d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '95d74422-257e-0511-26f7-439dd68b5587'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '07224690-a6d7-4757-9a01-a15be7c95f06'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c653c879-5109-5008-84b8-c25989f6021d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '490d8219-744a-5164-38ac-d78228f9331d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2f8421ed-4c8e-40c2-8356-0bbebee058d1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b3308b22-2e4f-3638-85ce-f86bfc2c20ec'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6e457d06-3a6e-a5db-45b7-2af833434381'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ea0250f0-a28a-9ca6-3c10-8721fd8d6369'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cf2a8462-a361-5acf-9ba4-e8c30a861e90'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'db114026-9f0f-8fee-5c14-56e610b34ddd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ce80c34b-8b2a-a24c-3fcc-725d723c1286'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '308c880a-a36c-a1d5-62ec-c2ce371e6449'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ab056687-3a89-0f59-1581-edb3f86712a2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1af735a9-4367-2b0b-2b28-86a8449d1c60'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e7d93790-5cde-92dc-2691-fbae846c5490'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fb439c03-484b-9837-7b8b-425a34cf809d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ce2b08b9-a4a8-4974-9c71-6e5ce68a0d3a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e22a1fc1-6ea0-7fee-7b45-5aa8f11a277c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '10437aea-9c0b-3651-a5ad-451a54112a17'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ce2454d1-0c4f-405b-4781-f260d61a7522'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '06924ba5-2e40-6adf-67c8-391adc356400'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ded9d785-40a8-968e-4dfd-7b699fe43390'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '40b24a2d-6a7f-42f5-9719-0c31ecee16be'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '64092568-1257-6e6d-6fe3-0a2a32d53cf6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f98e5515-5bd2-996f-6bf3-c4f7b2870ef6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ebd463d6-6094-2a71-135e-19d107808713'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2b5cbe7e-4ef9-4727-0b64-36601d981452'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '66bbae00-4be0-56ef-97a6-779835ea3972'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2e0fe32f-356e-2a5c-33c3-ec6d13e75cd3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b07768fc-7a60-056c-0475-ffb3422c8d58'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7af82fca-3f03-21f6-846d-8de44e822a54'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8a36aae6-5be1-74ba-a4c7-d7b9afb424cc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9a2a9b30-4570-8c8c-4121-7e49f5a40573'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ecdfbd57-0c86-69ce-5319-b4aa47ae721b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '079ec916-657c-6f01-3491-badfca6079df'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b663835f-6e43-8b03-7295-f5fa00df821c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe0efdbc-8c48-61e5-1c23-ce25770a5af4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '352a1246-3904-42cd-992f-4cc7d93990a5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b5f79c82-03fd-6a61-0393-71a97dc660f9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f9314803-8bfd-0ba9-67f7-fb021cf84f01'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a55f43a3-8726-3d68-14c7-979f9e9529aa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2d40f3f1-2a1b-8b5a-4900-1765c230044c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2029bee0-1fda-34af-5b47-f2fdc2ae7d94'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '61ebf10c-442f-87bb-5e6e-b62c117808f0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6af5d2dc-62f1-1810-9bd6-c57c01743c36'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8aa83013-1cb1-23a4-9687-c6c8774b32c4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b422914f-9d4d-99c7-354c-880588dc8bf7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6eeb2b8f-9539-3284-097d-6b5080877841'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b0f696dd-9321-2ec3-0df5-d795f4ae4a61'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9a73fc68-a537-9037-8a48-d21fb063795a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'dd4342a0-12ec-4a91-8cd0-e89df9fb99dc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5b2ade7f-4426-458f-6b20-eabc92d5236c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e297c12d-01d9-7aa5-4870-522f2fc25c50'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '82fc4771-85aa-1c04-96aa-a49eb4ed8333'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '68db1b71-3aaf-a097-89cd-702243d293c3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1530521b-1bd5-81c6-84d3-1267478e59c8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4fec3235-7391-23dc-5b8f-e035b1e42f66'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '629464d1-126b-31a9-354c-112216d10ed9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e73d2365-a715-3d41-5a5a-3d7429ea85d6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8d4d83d6-1e43-1913-a321-098fbe208d5f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0c78269c-7bcb-5d62-1e1d-34c049f7506c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fb73bbf4-396a-51d1-14d2-408f1ce32337'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '21410b5b-0418-99bb-545f-ccc1421f64fc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '22935e27-8f86-991a-7811-e808121e0d1a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9d391879-1e39-21fc-03d4-be32f64d746b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2f2b0203-15f4-309b-3824-310b5b94a3f3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '63e42feb-53b8-2d34-2db6-412c82075b5e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'dc8dedd9-8bf7-2941-9e19-4a34d5d550a4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f4d03a19-5d18-1bf1-7bcb-8366217112d3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b59cf7f5-13f7-9225-89f8-340e94726d02'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe2e5e27-2993-9123-876d-367e11d459d6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a5a95e03-749e-1804-3439-10cfa9e01050'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6933ce59-76c0-7554-42d5-bd54138d0784'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd59d701f-563e-4b5c-2af3-0c9be69890cc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '817984b4-695f-6e99-2aae-de0fcad52706'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2614fb61-09bd-9e74-380e-7ca20d2d285a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c2c9f476-244e-33bd-0b69-2cff899f459c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '01a5632d-6f47-31ca-8a87-056db181995f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6d1bb993-2e68-2b55-a792-a96401914a64'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2953d9c2-1587-75fc-9eb1-2dbad11d51da'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a4018fd8-0665-6149-3eab-efafd1819935'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '16843cd3-0abc-66bb-5e56-9dea4d3b2242'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6bc293c6-765b-8ee1-8226-4bb178c41429'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c759773d-5699-6696-41cc-31c04e907a26'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bbad1b0d-675b-14ad-368e-aada708e4c60'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a9ac1466-34ca-7734-95df-3858464b9c47'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'dd43e172-349e-8f56-14b7-93f490f6819e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '05e3cf1c-1883-5c45-3784-016cc6ea3342'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f1aafdfa-a35a-4b7d-6f93-1f9058a1749a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '774dda71-a77d-3fe6-2500-4eb40f4d6495'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f75f2f53-11af-50ce-6f04-ca22314c04de'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b57e4925-1805-3238-6014-8dbb916e5a29'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1a3f2c47-92a3-60ac-943d-01364a65518f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e2e34ef4-959e-2710-15a7-0eaf198ba34c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f96368dd-7f4f-8595-00bd-bbfddccda1a6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '037aa23f-6365-9f74-9b6c-b23af77552e8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bb8c7c2f-5df6-50cc-9bf8-6f8137543222'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '983a127b-5629-61cc-9fc2-5adcaf566106'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2cbd3100-9fa5-6c3b-4795-7bf181a767bf'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c6ed8c65-9336-2f0a-6043-dc2a7d01422f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '73d17fe2-36a4-409f-4bb0-0539655a7cbd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '30b0fb78-57d6-85ef-0b97-1cef8a4e53dd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0fad3cfa-1a19-7258-698c-d1665c67007c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '919a44fd-11b5-49a1-a3c4-45dd0dde9eef'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5b2eccf6-61f7-7b5e-0d95-fbe3bd54a777'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0af63f8e-9b62-61e0-3c5a-fc9b67f46912'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e0b63582-07b6-73e7-4e9b-db3a324f7567'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8f44d188-4017-0f58-1ba7-51d0a73d4cd3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2b91af61-520f-7c57-13ec-dad186cf3669'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f374435b-293f-4255-91d3-bf08ce187fc6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8e430664-8435-1482-2124-e3f07bbe974d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'eb77c304-0994-6515-66f7-5106f2b285b7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1e4339b4-5796-4371-4770-bc8e6b65530f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '80702547-77f8-9d1b-a67c-dfacea637da2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7d73652b-5849-551d-1baa-1c7f90ae606f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ee5fc432-6791-752b-3763-62d26b414e89'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f7554235-876d-a464-358d-f0e968465b47'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '505b4b70-0ef5-984b-7868-1cce7de99eb5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c3d1d2b9-7b69-86ec-419a-f8a6644f4ea7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cbf2a3a7-02a7-3b7c-95a8-289c401a3eec'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '48fa5881-26db-3066-2b5b-973463ada02d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ce40ab50-2111-6ef8-1c6e-08176c1138ce'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'aafaf1f9-26f3-7b92-81d0-2ad6b515237e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0a5e0965-0fcb-02ba-9353-e685106869c8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '12d17693-14c7-9287-12b0-b1787ed359ee'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'efa17e3b-89ed-29e0-73b6-ae0f17af3d68'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f500d744-21e9-94bf-6cbc-c9fdfcde7987'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '48bb8363-51bd-54a0-45fb-b63256f42cd5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1aa714d8-8a02-a00e-79d6-a10c7a738c3e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e608c293-0452-9748-12aa-f0d722406426'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2c8177a1-9dd7-02d2-30dc-435b511223b0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e60553cd-4301-50d1-1335-fefad3134461'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '858c29bb-4f00-0c22-0099-8316a3ce0d79'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe7fabf1-474b-83d1-49a5-edaec195771a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9f5516fd-558f-399a-3ec0-64b19ddf20e3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ca1625a0-0837-53ad-200a-d2d013322155'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '814cf449-336f-0e59-4bea-909d9ff48915'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'aa70628f-3576-3a17-123a-86dbc72c56f4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8ec7b13f-8fba-700c-75da-3766beb94167'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '50814f6f-8693-9c98-8263-42b196ed0c4b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ccf7180c-3f07-984d-80e5-2051624d0da9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3c061439-7834-191b-111b-a0cb3ba33f7b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5a4e1d2e-2713-7884-97ca-9f38800f2d23'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ad50b162-6c78-289a-4e75-6a7d70135c32'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2f6b6938-3a8d-50bb-5e37-9e46ad7858be'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '402f99af-2a72-4970-2717-1af9491a7e29'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6f6ffe3b-7964-76ce-97ea-a0776769a414'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '43af33a3-1c5c-6360-1f43-69a448dd9bff'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0a6dfc8d-5452-2ef4-7f28-290dca075549'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fb0f6e0d-0179-a5cd-5e37-8be84df42829'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '48730eca-438d-99aa-1c46-27b207126c25'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4c957e9e-0481-7e4b-85b6-42b38f62634c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '00128de8-17c2-0f67-063c-3fdfd63f45e0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd47a5173-4309-85e3-73ba-14d081bf1617'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b87ab736-5ca9-7656-50a6-a18b27e45222'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '847a3ab6-122a-8f3f-6bf4-e55fefeb2ce9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '311e4bb7-958f-4d4b-5ccc-19382c7d7f92'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '700d90b2-08f9-3e41-7d5a-82500f819e89'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6a60cef6-50c4-7448-3acc-de9416c27e37'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '973cc079-0370-47be-0082-61300b5e10f1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8fa452b3-9b69-2f1a-5b1a-482a7f837c41'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '381dcb8f-8afe-6d32-5ad2-920824ce05b7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '77ccc0b2-0fb5-4e37-62f0-a7b38e3b05fd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5e1be9e7-18db-07c7-22e1-82f7239b6dcd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f785e987-4ad7-181c-7ef6-3dfb43157ec6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7c91411e-21e2-9f19-786a-099947974053'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '38a38518-7a5d-2fe1-97ce-2b2083da188b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '56c2f349-57e4-34fc-3fd5-37d6652614f9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '720d7afe-2a06-13bf-7a77-71f024c48e12'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f74ea419-8d88-1ce4-8457-80ffe6001c2f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7d2b0660-2298-9d8b-0e78-b78b511a1c15'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5a0444b6-6af9-8139-5a36-673f8d535bf0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1ae29876-2d7f-3920-517d-123c17403fdb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6565e44c-4e50-5e30-4f17-41937f307fe9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4bdd7e10-3cb4-2d72-0c33-11fe63be39ae'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '88eca14d-1953-2db9-6cd0-9fce91c09b4b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8e1cad13-276a-7b4d-1d71-197054be7cfe'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c7389949-2f9c-0f6f-842c-40cf855f7ef7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'be0d5356-9591-8f59-2b1e-8b5872448ef5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8f3f18ff-92f1-4467-a18d-653aa74466e5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6497b065-8a43-24df-6ffa-746162028db9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9a7e59e3-45f9-01f4-0208-e9e87787070d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a2bc1647-4f37-96c2-1982-a64df2bd713c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '40a5cab1-7f88-228d-6024-b79cc5f448e4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '78697a4a-06f9-2a2f-6f0d-70a197a7532c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6cf95ca0-2740-1cb3-594b-2661dcbd2144'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1ffd7365-1a71-8ad4-2d81-377d9e1d47f8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a83d804e-7ab1-4874-b9ca-1e19c9e355ea'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6f962b88-72c2-20a3-7a45-718c09626cbb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9c6c4e8f-3690-1404-5012-e00000c41982'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd6053689-4b1d-9192-05dc-65042ca352c6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '864fa405-4201-a229-41d3-c11625743a9f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4b43409d-7328-2aba-36fb-57ab2d7d3575'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '075955ea-9608-2f85-3e3d-5bf2d36873a4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3e9caa8f-5f56-0d68-94d5-fcb0822b4085'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f831b86d-7bd0-80ce-21d8-61afa46d878c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e4c0cbe4-2692-1656-7aff-475aa5f04c34'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '96881b0e-a4ad-273a-9bae-28836323a382'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e443fe99-8d48-7bab-4941-4fee0481168f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '50f2d9ef-a77a-4d19-2181-12d89a920b09'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '902c6af1-7579-15f0-243c-e5b08ee42176'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ab90ef0b-a5de-417f-9a85-93dca6e943d6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '45719c2c-21c9-6575-918a-c52c7b61a05f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '033dfb9f-090a-729e-31c5-17b1ce851e75'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f74189aa-87ef-21d1-7db4-bfdc4740684b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9805e696-a19b-4af2-2418-d85db3b26912'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4d0a6ef8-1f1d-2229-18b1-21dd931e02ee'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e40e21b1-9867-037e-7602-dcfdd7a63b44'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e1ad2f0e-6561-7ab2-512d-3d0a1326981f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8e4604ef-1d4d-5720-3fbe-04fda9300397'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1d38d6e1-8a5b-2349-1c89-c5ccbf97615f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6c22e373-59f6-9a40-9d6f-7e57271273a3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '272049bf-003c-20fa-a785-2532453b0aa1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2849cb72-602c-07d9-2530-9d2de5a03a05'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a5b79d98-5e7a-088f-1531-32d644900c7a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c177f12b-2ec1-4324-8070-65eb6c746dbb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a5f47c49-465a-63af-96fb-e16f4b0b35c0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2bbe85b5-8d8c-3dd6-542c-a3a11d079494'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cfebb9e1-65a6-23c5-1bb3-60073f941982'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4c7155f2-780f-2107-01a9-3697351736d2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '69fcd440-3821-6239-3ced-ab1353bf9141'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '048bc8a2-6ef0-5c78-938a-6f4149786965'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5013bf49-89f9-70e1-044a-e836297e628c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6bff5eb9-3b37-8c40-036f-47df859b8247'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2ac3f854-8400-8d66-1a5c-ba88425e8fe8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e7633693-4fad-3109-616e-21441ef4242c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '70d339a6-6d87-7da5-1bca-6afb69888dfe'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '755e7e3c-2df1-5489-81f0-d14b176190e9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8ee4b864-96f0-43a9-5929-9cd445e9970d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fb8b7d01-a411-0f3e-807f-4e077a088833'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe7c4d28-1063-55c1-8cf4-20564b4f6182'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '630c37d3-8e7a-6e64-7bfa-6124d94735e1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2542818c-2afa-a158-9752-c20c344656f0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a7511626-1f53-2166-6b9d-efa2c1d5135c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '28f75371-8ef2-4429-6733-5e6803110983'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3d3a890a-3d06-0a26-165d-b74971514ae4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '34d1c851-746b-4dcc-39e7-15a26b00809b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7bfcee0a-5414-90ec-737e-590a6aa00e10'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '897a7e6f-82d9-57d1-7ce0-087bc6c79c0c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4b2240f3-9d20-6364-4b35-6f24b4318049'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '82815296-5fe5-141c-3380-180f29ba2faf'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd28aba02-9729-8fc6-1ccf-19d2e01e4500'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c469ce87-8743-004c-17ca-d300aba87304'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '968c6dab-116e-3447-270c-70cefe6f6da9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'faa778ae-8868-00a0-2cdf-7731909e20ce'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '950987c6-551e-99b5-47ff-501e54953d43'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '32713096-95b6-7c18-576d-5026681e7564'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0d738c42-9d45-296d-a425-aab6e5482d9d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f0f2fe55-922c-462c-0231-8f1814108ee1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e48630cf-5304-1e98-63d9-c9bca0b01b0f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1696968f-9d45-7f67-053c-44e538ef9616'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '17020f19-3542-4745-4dcc-b72fe6181b3a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '05672bcc-75f3-7c3c-6f24-85b362f9642c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8523776e-4ac1-14ce-8153-baf2cbbd5612'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c2dd6b8d-713a-909d-3b33-6a8017858f37'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ea7f5d69-926d-9349-1877-f97e5d1411c9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a4644949-9517-a2d0-59b4-166e6a2ea42c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ce2d541c-3447-25ce-2886-0c8800bd8380'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b2242a87-5ab8-555c-3ffb-8c43c54e199d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5ddf7603-4f0b-743e-6999-9cd02e222d04'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3fba187a-7829-9df1-a36f-5681f9b01c89'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2a654eef-2dea-5ae0-37da-1260a7a94bc3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '56cef6e1-61d1-492d-a5d0-2376ba3578fa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6698e861-a361-2494-4b60-123853ca3b52'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'dda8639f-4b3c-2afc-8e73-40e62bda30e7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ed7f1e4b-93c6-6b91-1eea-ad5d828f23ac'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'db24e326-136e-538d-a036-2658704654c7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9f7b8ed6-7477-6a04-4a4a-de09a4502b8a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2c096066-543d-6298-3a69-5eed518f0d98'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7c6984e7-7f66-831b-0100-afe0c4450290'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '98906c2b-71b1-a181-0c53-38e03a39138c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '675c68dc-23df-7033-665f-4226b9e09430'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '22b133e6-01f4-220e-6e93-fc7899bd63c0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '05027ef6-a53c-7c86-6182-254572ea6df6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '702b6bb3-7a13-650f-195b-557bd53f97bb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4696cdfc-6e88-8a6d-9142-002f09b86f2c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a8f8a0fd-a509-3e96-63e2-227c09044e92'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bc6bf7be-7057-2e23-5efc-e038651c9844'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b2618c49-22fb-6702-a702-2397217c0ef1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'af564fd8-7030-057f-a1cf-e51d446a19f9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7c26352b-9257-72fb-0d37-22a5dd3f8d43'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c1573eb9-a48f-0d04-3784-8c848bcf1447'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cb7426dd-3025-3ca5-a579-926348c21ce2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '60614313-7c28-14e5-7cf0-706705a04a7c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '62cb0bc8-0b82-2629-1316-5ab3dbc90af4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1aa28bdf-328d-6275-39e0-04791eae7a4a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1f2eb5e9-159a-12c3-3520-eddbf6d38f35'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '19e879b5-14b7-3a9d-9236-7111ea942a0d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fb291ded-9470-67b3-95b6-b94e5cc75759'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '144b4426-5e7d-a34f-07ae-cdfe73a01534'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4ed7cd41-0e5f-67d8-1861-d33305286380'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '41c7dd6c-5dda-4550-2384-e31b87ef1367'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8fc8649c-25bc-5e0d-9742-7158fb360e7a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2504b028-8847-a23c-a389-ba39540618a8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ea609e65-029a-82cb-8be3-9dfb5e5e1dcb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a86f35b4-2da4-67b6-a62e-a743a20a0e6a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f5c92a83-7567-3f88-2adc-ae2f9f6b3522'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cefe9b0b-3955-6c3b-05e3-024548ed2b6f'}, {'bill_id': 15, 'total_due': 4010.0, 'student_id': 'ca5570bc-eaee-48a8-b25f-60d099199785'}, {'bill_id': 18, 'total_due': 200.0, 'student_id': '0e965ba3-3256-465b-9d22-9484b4fc071f'}, {'bill_id': 19, 'total_due': 2948.88, 'student_id': '760a89fa-1b76-5f18-2268-0cd9553e281d'}, {'bill_id': 20, 'total_due': 8000.0, 'student_id': '760a89fa-1b76-5f18-2268-0cd9553e281d'}, {'bill_id': 21, 'total_due': 131.0, 'student_id': 'e1eb26ed-31a8-4738-a6ff-50e99ffbdccb'}, {'bill_id': 23, 'total_due': 506200.0, 'student_id': '1f17f53f-6d18-42de-97f4-0c4a30456888'}, {'bill_id': 28, 'total_due': 12100.0, 'student_id': '0cf68ed0-39b4-a65b-2b86-9ed1fa5fa2e5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c8264ad0-69aa-08db-30b0-121d28492696'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c526e33d-3259-6d70-2c5e-022b5fbe6e2c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '85881e33-378b-0e70-99cf-1f111a0e7f83'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9c0045ab-5d78-0055-7121-a7e04bf8a3ac'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '77532c18-9cee-5f0f-6f5c-6665bfbf7186'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '561664d9-24e2-0f14-6f76-d23611262686'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '54602213-1b71-195a-76ef-9fde8e117847'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '06e087d3-82c2-43c4-5b3d-8ac277160933'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5205c278-620b-a7bb-28e6-433331dd4dbe'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3fe92f8f-3952-2fe0-2019-83cd8d7a2fc8'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '24b25ee8-59cc-8028-a715-2970d68974e4'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '85827b20-5ca0-2e3e-a0d5-a1ecde7b0d51'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4c369e1d-2dc1-016b-1283-1643f72c02b7'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'bbc5c261-a1dd-71e4-54b9-9fb7d7067b13'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3b323b66-4ef5-0586-4e6e-37f6330b19dd'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4182784d-0bb9-9c22-3273-b8d4b4f1921e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4f5a559e-8a7b-8911-2acb-d55cf7b06970'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '06d74679-6d05-4c87-2233-d4f6c3711d8a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c2a87666-1c03-29a7-2611-2458d14023a0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5410bef2-3979-6977-2ed9-0606219992bc'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1de4919f-3970-11c5-579a-8822f7a86f43'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '701a6456-21ed-a79c-2ae1-19066f2c836e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4a007f7a-7df0-49ad-09f3-8668438f15b6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '04943d92-2093-6b01-88b7-4d8d6686481c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3dcabb38-3eb6-52c2-1262-2c437cf90318'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b372f172-6af6-3053-a5e5-f21022622230'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '64e224b2-515f-26ab-15a9-d8a84ab36f4f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '69ed2923-48ec-0a7d-17e9-4691fa826f65'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1bfa8abb-5cb9-0bfe-a726-4377b37f3981'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b8f93449-8b75-997a-73cd-3de804389628'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5ee2e315-6bed-130a-435a-1fd021de7a68'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6b667536-3aff-983b-09dc-45fdd98e3ee8'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '00e8cfa6-041f-349a-8eee-a486a987a589'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2a104d95-5f5b-4bfc-05d9-2a50fbdb6a95'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'aad55961-013f-55f1-683f-93c6c65e8259'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e7892353-9326-2c47-7c9e-638623097ef0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '59c01b26-423c-4dab-8b70-d5f5533078e1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '178f4b56-82d4-7d90-8f25-744e54110e67'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6d69e47d-556c-6a31-452a-590528729126'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd094a185-3fee-8da8-8e76-896d2a0a5b4d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'de4dfea1-166b-67a8-a061-d25a29f75937'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2fbf7ffd-37fa-0140-2887-816b714e76f7'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c31852eb-4b50-7982-63e8-3ceeec9588e1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9081f344-7a34-1bf9-701c-f218ddb89575'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ec214ead-a1f3-27c9-2c81-e6e916635b03'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1c5d5dff-66b6-1e50-360c-899c15f583bb'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '82bb3719-9dec-2777-9458-6393e3cc36b3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '792ce6f8-8c5a-1bce-42f4-64e4318b8827'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5ec5257c-a58f-741a-5b9b-d7b44d1b16de'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '59b67101-92d2-3664-4c29-b97987a7216e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '57a3e583-42ef-4658-066b-1d1267343937'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c3e0122d-041c-3c20-43a0-85bc2f9c5e8f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0ffbaff5-26c7-19d7-51fe-402b1c315798'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '694286d8-55ae-1025-4671-9b632baa5a5e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '39a20a20-2b17-5fe4-3634-52f5f176a09f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2414890d-19da-9df9-72e0-7ed177831fec'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '89737766-470e-34cb-1a35-ec653c069c1a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'cd307533-5b17-1d52-0524-669508b98e99'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ce556b44-2885-79aa-51e8-0f2a44d02478'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3798c462-92f3-1f53-7100-f507e6b600e5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '099c4c00-64e2-71f8-532f-30550fba2c0a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7c228428-977f-2b5f-835c-c490ab948871'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '8091e1d7-3a1f-1385-0fad-4dcc7b67a048'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3f7cd157-2fb3-a02d-8b43-c57832139163'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f99a4825-56cb-068a-169c-72aca66e93e8'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '303546e8-0ebb-947b-48ae-10984bec0d90'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c80bc1c2-23f2-2574-a53d-8aca1efc4a03'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e0e516e2-3835-5729-84fd-5d65c33ba19a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '76d14786-8d13-13b0-21e4-8d8acbad2813'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3e122dd3-76f3-409b-49f6-6325cd154f08'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '703c7a82-1ef4-3ceb-8a6c-7ae26c9d814f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'a100ea90-92e7-445c-1685-d7f6d76c2f9d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '06d434ce-64ad-776e-0a5d-359a1dc65e6c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e3b36403-a3b7-601c-6a9d-0edc047821ad'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '367a8d66-7288-1cc3-2b85-fa17f3740311'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f8a9c514-8f42-a46b-4345-93af3f434d1f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7035e3e2-5578-049b-10c6-9a3c8a404d56'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'df969435-89e5-6db1-a531-6ee051828da0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '43f93924-9737-18a2-7e25-ce852b124e2c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '57a1ac59-a1fd-949e-43d6-382100ae1769'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6248eb6d-89d7-42ad-0418-5b10164c05ce'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c758fccb-9a53-7dca-8bca-86ec7a3a84a6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0d61980d-3f23-4fc3-0b35-682721158973'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e6f41618-95d7-60fd-1114-21668a5c15d1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '91696cc8-3870-9a6a-7129-ba9a6ba36931'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'fb184d2a-0613-8c06-5f3a-982f98c1906f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'db7e8edd-964a-3f39-871d-16a775906778'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '686dacc9-13a1-9e59-54b8-2a91e15766bc'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '098e2ec4-5d3a-5e48-74a7-b6b1768d27ca'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '781c6af0-437f-768c-084f-3b7c223359d8'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '318526e9-7d0c-1d1b-8746-db0113790a75'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e99a3099-5b91-226c-86ec-2f9f387f6896'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '035875b4-6f43-a46e-2701-757f1fd1254a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2d8e43d2-8322-141f-0ba1-40be5367317d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5bf70bac-9b32-6501-91fb-41ebbe8f4513'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '52f8429d-5238-93f2-72b0-2bea9bf57e43'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '430034cd-62f7-0373-4691-a7f8bd074ec5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c0c62ffc-01eb-91d1-0576-41a01c0e2ee8'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6ce966ae-3679-9fb4-7e0c-61f233108b3c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ed552eec-473c-9b60-1bb1-ba58ff221ddb'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4bb6e1db-15ed-7ee0-9d01-52d15d9e59a4'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2ba032c9-0e79-003b-843b-fe8a5f1e1612'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '067695f1-80e3-9067-3273-aa9de1c60499'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e18b75a4-4e29-34ab-61d1-9304de835f33'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '749b7d77-38d1-2383-9bf5-e18e59212e9d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0847d017-0ee8-3e9a-5298-38d6a88b6962'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b19449a4-56dc-5c6c-9076-c91052c55c77'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7c8180a6-30b8-1bac-9751-aa93a9ea527f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '17fca378-8fd3-1feb-171b-889756028d64'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '85c34c25-4c38-2037-0b3c-9ee411f785d2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'dcf9fee5-044d-9a38-441d-a73e440177b0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c43845cb-8769-4fff-4777-596b4a7644d6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5be63658-494a-679e-652a-5eded9e36da2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'a727fddd-68e0-1684-118a-fcabce2548a9'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5456875c-5a13-737a-67fa-d82a03ed689d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '8da4f45b-0321-996a-5757-78714ccf038c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '94b925f9-24cb-3ccd-98d0-3235c683640c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ef48417c-99c7-2ab8-a6b7-ed28e9b99853'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9fb653af-4b40-82d0-5cc3-792d22d126a1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'cab2c4d0-3509-5388-9655-07ee50073f00'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ee979160-6aee-1530-3016-c06e3d9a9551'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9d143923-690e-5fe6-58c6-941dbdc53215'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6a52862d-4c5c-13e4-9933-aadd6bbd405c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ddd5efc1-79e7-2974-8a0a-ce136224828d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '642dd795-5ab6-09cd-5129-0103dbe05cbf'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '85827ab4-148c-5a1c-1366-21e4398e362a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd59dbab4-1345-1021-2770-172cbae81adb'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd382bf86-0ebf-23e6-09f0-0e481a6f3dbe'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd72d772d-943d-7c25-7f28-f7f3a3eb2abd'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '769771e6-0540-935a-67d0-1d477a849d19'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd5fc97aa-78d0-6f2c-66fe-cff80e0d014e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ea031862-4715-349c-015b-9e76f2a800e5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '255cff9a-50ba-7c46-30c9-ff5077eb89ee'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '652c041c-5d7d-9ca8-94fb-ab55e3862bc3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4f62acc1-3a9c-84df-3c07-b1cd7d081b09'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e07b1f28-2039-270b-089d-5da40c5166e9'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '044d3ecd-6262-2d66-2038-a44125a02c2b'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0b87e7ca-5f88-76ac-a19b-9237da433c9b'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '64731f97-5e92-3c3c-9aca-9e1bdad669bb'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3fcf8b96-4fe9-68ec-98e9-866798bc1b02'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b3c1b84c-1d60-872d-3df9-ea20be2b362e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '607a186c-3902-9096-898f-1f79485c8b31'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6303ac1e-02e5-5b93-8393-59990ee9a40e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd9233a2b-9660-18b0-a461-ea9b09794114'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f5d2a1fe-82af-04e4-9946-09e9b8d05767'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ef6ef3b1-4fac-0d02-1173-c1e2325548f7'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2182f797-795c-51e2-8fed-6dc8ac9a6772'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd1a3d330-a7bc-4d53-57e3-27802e0c20d9'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd62ca2eb-7ee1-7460-72c6-6886188075c6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c4ea101c-86cc-7d0f-8a02-833236942c64'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '45d06085-1ab2-1329-8b9e-a63e7c2c0910'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0f2e53cd-845b-825a-702b-e44ff2501b23'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7036f861-2b34-06a0-8f2b-ee59840e6c9c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'cb8342b7-2089-71ba-57df-810b5d624a2c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '94de04e6-6f79-7cb9-9693-84de8be6725e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'a8b34a16-40ea-30b6-a4e7-51c0801c3336'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1ef2d2a2-a016-9cf9-122e-76f4deb45b72'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1521954c-4126-5e4b-2a96-5b6aa162a0c5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ea56a2a3-6ac2-5ec0-62b4-438f35970691'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5232377b-2873-1f24-497b-7d4836170022'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'eca57ebd-2958-1f98-9a15-7323272f7ff1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e3523e62-0456-09bf-1890-7916c0da887c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'fffbc992-5419-23eb-0582-7a99b865798a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'dcbb0631-0ea1-738b-3281-3e2582a6a017'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '53a57913-61c2-3e10-962c-a0dc28c77eb6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '88bce103-a64e-6354-009a-518c49206bb1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2b42f19e-7924-9aeb-30ff-bbb4bc6fa3d3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '328d25dd-882d-634e-30b1-627de6040d2a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '11cf9d8a-6fde-a2e2-654f-066bbf064815'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1033cb6d-03f8-a4df-34a3-e582778e7e88'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd0343321-7172-1e72-768c-6c08bdee5e36'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '57941f4b-526c-1ea8-3a18-ad8eeb7e6457'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '63196a20-33eb-740d-6bd0-3f018b9d6304'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'bf41a50c-4e5f-1b00-3278-d595c9fc1666'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'efc2662e-0fff-90ca-3a12-a75b5b0b526b'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5558492b-64f1-36f2-8b4b-1bda8b0a9472'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6d0f85c2-85c5-37bf-4acf-13ddc2512fe3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '36500e0b-215a-9ffa-964e-021be04c6426'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4f92a7b2-657c-21ff-1f3d-71e9329c299e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '643f20c2-8f4b-92ab-0e09-a467a9393dea'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6ec92e48-9375-988b-1b29-dd2edaf43901'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '39293b2b-01c3-71f0-62e4-8928944c930a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9cd9822d-7691-6599-15df-1867ea788fc5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1b5416f3-1e3e-3272-5023-7351e54c4803'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f8d2eb75-7869-223e-625f-28595f274500'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0f29d39a-5404-57cd-3211-9bfa62297826'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f8e0dba6-356e-8916-2938-54775d56228c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '165b511c-0528-40cb-252e-d211987f09f5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2adaefb3-00b2-5f71-26b9-315f1fb43cf6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2e88991c-0127-24f0-3689-b20b1e2507f3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '21b3c6e1-1a95-7a05-499a-bc9f202a0172'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2a3fae51-1014-4f46-6641-e10fcd0a5ef6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '34496f66-65ee-81b3-6ea7-faf427950f83'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5c122e92-6bbb-70ec-0f97-7eb1e08e7ca0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '606d4292-297f-0b9c-494c-c19370ee45a4'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '8f8ff1e2-0c81-7c88-549c-c9a5335ca00a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ec9dfe99-6b9d-9fbb-720e-8981a04470f0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '54529b9e-57a1-435f-969b-b7adb0275e38'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'fc98e32a-5bea-5010-71c0-252cefb03b79'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '14be8744-56bf-3cc7-5a1a-f0a4aa23a455'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '916378e6-a2af-982c-6271-bac2e9717bb1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '8e9ea401-0d11-8aed-8fe0-644104e46029'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4973e873-791f-379b-1fed-49f7c06f7985'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '02c46aa0-a210-3a3f-8890-a885b539501e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e85738ef-8235-66fa-a738-583a5c620495'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7159f590-042b-00ed-60c6-9563da249861'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c2bb34b0-2b44-8580-83b4-485da0120f88'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7477cc97-3d61-9533-8edb-dff46e5914cd'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6f9cf2fd-8dec-99db-9ac6-418e98655cca'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '885b0f5f-842c-4e73-7229-735dcd2f78eb'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '722c588c-a17d-94d1-41c6-a3a457774fbc'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '21749a04-187a-65bc-3240-d45a65b3972d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'a3946405-4eba-90e8-61ce-518905c43c51'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'efd1a51b-277c-34fe-85d1-091d1baba275'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ff6b22a2-a3b7-4009-6ece-d02654bb93ba'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'fa6be36f-1f8f-990d-90f1-7fed9f9f84ed'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '41aa71e2-75a0-516f-6246-553061955b51'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '60652520-a716-416c-4a3c-b2543fe3518f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '685a05f5-5c84-9574-2aa1-1103791a3da2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '722c588c-a17d-94d1-41c6-a3a457774fbc'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd830591b-6033-5a24-9ac9-f18596950e7f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '21749a04-187a-65bc-3240-d45a65b3972d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'bcf03227-1deb-53ab-9d6b-10761a626524'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'a3946405-4eba-90e8-61ce-518905c43c51'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5d1d83c2-1271-7ed2-81bf-d2bf07ec60d4'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'efd1a51b-277c-34fe-85d1-091d1baba275'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0370f8f1-7e88-433d-2981-058f92ea1184'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ff6b22a2-a3b7-4009-6ece-d02654bb93ba'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '442a643a-2b80-0b3c-903c-e76113262427'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'fa6be36f-1f8f-990d-90f1-7fed9f9f84ed'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'dd9938e7-053a-4320-3e8e-340050144c91'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '41aa71e2-75a0-516f-6246-553061955b51'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c4503a0f-8b25-281a-696c-fc255b9f8f31'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '60652520-a716-416c-4a3c-b2543fe3518f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '685a05f5-5c84-9574-2aa1-1103791a3da2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '71fec68e-2fb0-3521-79de-8ef87cc61e39'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd830591b-6033-5a24-9ac9-f18596950e7f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c69f4b6e-3db1-682e-234c-9eaee1b97f35'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'bcf03227-1deb-53ab-9d6b-10761a626524'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c3ed6407-693c-5304-89fc-4d026e495429'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5d1d83c2-1271-7ed2-81bf-d2bf07ec60d4'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '27ea9a08-1421-18a3-8614-b627efac8372'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0370f8f1-7e88-433d-2981-058f92ea1184'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd4843bc9-9169-7958-6aa8-1c7cf47f8aed'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '442a643a-2b80-0b3c-903c-e76113262427'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'adc3af9f-6d68-918d-a697-d8a97e4e0007'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'df6614a0-0c70-7e9c-9af0-2f7ab15296a1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'dd9938e7-053a-4320-3e8e-340050144c91'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd61eed87-5313-5a31-80c4-f392de6123b1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c4503a0f-8b25-281a-696c-fc255b9f8f31'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7f8dd2be-0b35-1f29-4f8f-82ec7761179f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '71fec68e-2fb0-3521-79de-8ef87cc61e39'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '14ae7e76-9e24-8752-7bd4-382baff24cbe'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c69f4b6e-3db1-682e-234c-9eaee1b97f35'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7f860c43-8702-5870-11da-5c0299b49830'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c3ed6407-693c-5304-89fc-4d026e495429'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9096021c-9098-5994-83ed-b02d89bea3a2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '27ea9a08-1421-18a3-8614-b627efac8372'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e0354eb7-5eed-065f-99ee-9d65f03a675d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd4843bc9-9169-7958-6aa8-1c7cf47f8aed'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'adc3af9f-6d68-918d-a697-d8a97e4e0007'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b1ec6618-0724-79ab-871f-89821f4411a1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'df6614a0-0c70-7e9c-9af0-2f7ab15296a1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0f4d31a4-078e-243b-7d1c-04e496a291ff'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3ef5e09b-39ef-208d-402a-f6e1166a2ee9'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd61eed87-5313-5a31-80c4-f392de6123b1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7f8dd2be-0b35-1f29-4f8f-82ec7761179f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd72215c5-a395-065d-7e4e-2466880527f0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '14ae7e76-9e24-8752-7bd4-382baff24cbe'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e01609dd-8847-921a-95b5-ba34c2823c78'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7f860c43-8702-5870-11da-5c0299b49830'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0a19be2f-2423-2b4c-6aa9-a02fb4713da7'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9096021c-9098-5994-83ed-b02d89bea3a2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ddfba141-0c1d-3e89-882f-c79443352425'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e0354eb7-5eed-065f-99ee-9d65f03a675d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7a4d40af-6bba-5ee1-97ae-767b8f68464a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b1ec6618-0724-79ab-871f-89821f4411a1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '36989fb0-6c34-335d-5b5c-b881f7630375'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0f4d31a4-078e-243b-7d1c-04e496a291ff'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f8320c7d-56b0-4308-593d-59b130cc43e3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3ef5e09b-39ef-208d-402a-f6e1166a2ee9'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd72215c5-a395-065d-7e4e-2466880527f0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e01609dd-8847-921a-95b5-ba34c2823c78'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0a19be2f-2423-2b4c-6aa9-a02fb4713da7'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ddfba141-0c1d-3e89-882f-c79443352425'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7a4d40af-6bba-5ee1-97ae-767b8f68464a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '36989fb0-6c34-335d-5b5c-b881f7630375'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f8320c7d-56b0-4308-593d-59b130cc43e3'}, {'bill_id': 32, 'total_due': 1150.0, 'student_id': '742574f8-647c-18bf-8ca7-1a9ddf343cd4'}, {'bill_id': 32, 'total_due': 1150.0, 'student_id': '69ea01bb-61c4-6879-8f69-929d0a5e68e9'}, {'bill_id': 62, 'total_due': 1000.0, 'student_id': '760a89fa-1b76-5f18-2268-0cd9553e281d'}, {'bill_id': 69, 'total_due': 100.0, 'student_id': '760a89fa-1b76-5f18-2268-0cd9553e281d'}, {'bill_id': 77, 'total_due': 210.0, 'student_id': 'c2d12d80-9f9a-49aa-9dbd-f7a41fbf8b9e'}, {'bill_id': 78, 'total_due': 1050.0, 'student_id': '2567cb4f-66c3-4724-9eb0-9db50defbc42'}, {'bill_id': 79, 'total_due': 4000.0, 'student_id': 'a205b193-049e-46aa-b2c5-0856c1c785c6'}]
2025-08-08 08:21:37,262 - root - INFO - [{'start_year': 2021, 'total_amount_owed': 170865.0}, {'start_year': 2022, 'total_amount_owed': 352830.0}, {'start_year': 2023, 'total_amount_owed': 220950.57}, {'start_year': 2024, 'total_amount_owed': 2999125.95}, {'start_year': 2025, 'total_amount_owed': 2624540.0}, {'start_year': 2026, 'total_amount_owed': 256220.0}]
2025-08-08 08:21:37,262 - root - INFO - [{'financial_aid_type': 'cash'}, {'financial_aid_type': 'draft'}, {'financial_aid_type': 'cheque'}, {'financial_aid_type': 'percentage'}, {'financial_aid_type': 'amount'}]
2025-08-08 08:21:37,262 - root - INFO - 'No results'
2025-08-08 08:21:37,262 - root - INFO - 'No results'
2025-08-08 08:21:37,263 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 3
2025-08-08 08:21:37,263 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 2
2025-08-08 08:21:37,263 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 08:21:48,109 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:48,135 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 08:21:55,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:55,447 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:55,447 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 08:21:55,447 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:55,447 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/5
2025-08-08 08:21:55,448 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:55,448 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 08:21:55,448 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:55,448 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'string'
2025-08-08 08:21:55,448 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 08:21:55,448 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 08:21:55,448 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 08:21:55,448 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 08:21:55,448 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:21:55,448 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The students who owe the most fees at the institution have outstanding bills totaling 506,200.00. Th...
2025-08-08 08:21:55,448 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 08:21:55,450 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'bill_id': 6, 'total_due': 10020.0, 'student_id': '0e965ba3-3256-465b-9d22-9484b4fc071f'}, {'bill_...
2025-08-08 08:21:55,450 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: highest_student_fees_due
2025-08-08 08:21:55,450 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 08:21:55,450 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 08:21:55,451 - celery.redirected - WARNING - [{'bill_id': 6, 'total_due': 10020.0, 'student_id': '0e965ba3-3256-465b-9d22-9484b4fc071f'}, {'bill_id': 7, 'total_due': 10020.0, 'student_id': '0e965ba3-3256-465b-9d22-9484b4fc071f'}, {'bill_id': 7, 'total_due': 10020.0, 'student_id': 'a0b0b8ba-ac03-4d7d-bfbc-47abb40f365d'}, {'bill_id': 8, 'total_due': 3600.0, 'student_id': '688112bb-46dd-4c97-ae1d-f7962de5d6cf'}, {'bill_id': 12, 'total_due': 5000.0, 'student_id': '000248bc-3343-8eaa-29fa-ceadc8813259'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '000248bc-3343-8eaa-29fa-ceadc8813259'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '33619a04-2078-84c2-4450-760d7a3da4e9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'eb145785-7255-0358-2304-8ee7403a8037'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6be0ef4b-326f-7025-762d-5360b6ce0db1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cc3b795d-39f7-802a-2593-5f65cbe7a571'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd83fd770-3f63-5f09-5998-9ce0839a84bc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fc8d2b8d-43dc-a70f-854d-3550d7c41a69'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '45c79e3f-2dcc-73c4-4524-f49fa7d05247'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '188c9605-014b-54f2-204d-a698864a10eb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'be03db91-86b1-12ac-734f-cd7a5dd55439'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'db01dee9-5232-909c-9605-d4b04b5395b5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1da07ca4-72bd-4542-9861-a48d986d31cd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a907a755-2f55-6159-34ea-e0a9f83f73a3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '87d1d0e5-9d6a-6309-936e-0ff8e3b89602'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8e3eb2d2-1a20-19f7-5075-0877efd6785f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9e27be6b-4baa-6b65-5605-f0586d61918a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '14f477f0-822f-2fb2-2e4d-d18bd10f0cbe'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2dfd619e-6d21-0fbf-3b8c-2c405ecb0bcf'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6db4c139-1cf1-22b9-28ab-8e4a32665d66'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '352c885b-0d8b-a598-9fd5-6803eda243f4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bd89b7d1-5ea0-4368-87bd-347f5fdc2ef2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '21541732-81c9-840c-19dc-391affe72cf4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '611351aa-6571-0dcc-7c9b-d1951c459891'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1e8ccac0-2c3a-03a5-2817-30f1ad9512c0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a08194ee-9324-139e-6b58-cbfa3b466b04'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '81a1d828-8c77-5b39-22d1-ba815bb30cb0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '323b73ce-1dcc-8811-5738-4ca23a8813b1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd0060e74-7ebc-282c-4fba-c9a8f0415b60'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '36a5b656-a2f9-943a-623f-05f3f9a58942'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '39ab1074-6907-7af5-601e-1c0918e395b0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd50da43a-2892-0b1b-5ed1-596288689603'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c9cf591e-912f-9894-0cff-cb6e4872051d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c2631455-8cf4-6f28-01fa-46bafad0828c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4cc6d1ec-60b3-2c9a-104e-b62faaa48ff3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c7117ecf-8e9e-3459-597d-b87f73d02c60'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c08bea27-9687-6d41-8100-ee0a16459ee3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'da31ba01-26f9-6002-3347-a8852d5a1549'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1c23cf1c-4ac5-a6fc-769b-01c91efd5ce5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4fddf9ad-0686-60ea-6ee8-5e7015253f47'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1e66c6c4-36cd-4f10-3357-53e6e92c624f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '327c5502-1be8-860c-5d13-85bb1d547fc3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '207aaa95-7868-7019-568d-aa8c839c6a79'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1e3d0b53-4104-01d1-a4aa-dedef5d55e54'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '611182ce-8971-3e4c-95d5-ca4754a6a04c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3bcb4f6c-3fdf-7c6e-0cea-fbcff1a00e14'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '513f88e1-3174-8658-702b-049ed5383194'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a41a97ba-94ee-4a75-6696-a84eeae35397'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a479b44a-3216-9caf-17a8-f19acb9f4f72'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '491b2406-50d9-667c-58a6-d2c044d81d47'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ba218c16-0d70-1a0d-9fb9-3893723205b2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8bdea7c6-27ac-0185-5026-009e77b741b3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '48031dde-62f8-5033-56b1-0ae6fb6100cd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '107b1349-7d26-2d67-24b6-c30c710752fb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7ec6283b-5877-2d26-1a4e-885baf022431'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0b831178-1e8b-8bdf-91f2-164d68ae3e23'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '794062be-8e68-1cef-6a7e-1631c2e7520d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4629d5e7-2dac-561c-8547-fb8a43c68985'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5ba734ad-9cba-4a74-6395-e5a920672a7b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3bd62cb9-1f69-1233-4899-d92203742418'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7a42dda8-1a55-19f0-540a-6499cdba0f5d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '16977c98-80db-19b8-1e74-e5f07fb985a9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5ead574f-35fe-1721-270e-01a53224541f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '318fc6ac-1ee9-7a83-975c-7d28567a144c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '936a1a1c-9220-91d4-52c1-8b2a7c079e07'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c8138258-93e4-5197-3a3b-1e8bd9869604'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5fcd5a5f-9d20-60ba-6220-9ad50c81a42b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '566cf270-6da5-21ca-2d72-9081a9ce68f4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9ce0e2d3-48a1-64cb-6159-c4c504f25f40'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7891ff96-4b07-839a-8a1f-7b87571da365'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '86e92dba-7efb-6e46-75e8-a3a90ca672f7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '33016f6d-0913-8e22-3682-6131eefb09df'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2a746a0e-4a06-270c-4a25-65387273304d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0609ab7d-0a68-a31b-55d0-37b318a30e80'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ef7af3a3-7d44-1c83-9d09-c342456676ce'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f5db58d2-8ccd-7436-300e-982b35495336'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3270f1c8-a31c-1f70-436c-e0af4beb4e6d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1a1f2996-902d-7d2a-48c4-90b35e4f8e98'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '264ade00-66b5-25a5-73d5-5608812d846f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cf01c60d-885e-6c79-23e7-a2e24beda495'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '97ac1588-5092-1694-1f36-8eac2a2e649e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '31c64564-38b4-1114-95a3-509168ff32f3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cc9b80fc-57ab-72a6-0c93-8d53694d2751'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8efa1110-5072-65e2-4c0e-c36ad4fc47e6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5218df03-7bff-4014-4f7c-fd3b3072886c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3dd7cc24-5f17-9d38-5a4f-510cfec5922b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f3e2e958-0691-8d4a-a168-14741535012e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe68428f-32fd-70ab-0975-f0cf4fec8a2d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd77e9907-5a85-a665-425d-74a5a3db59a8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4a0e4953-6c0e-66f2-51a9-13f9a80e01a2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '85ebd148-5328-2cab-85f9-887e01490c83'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8a621fcb-4d5b-54ff-2266-40ea7fada343'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f32334f7-a4ce-5df3-6bcd-1e643511903e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'de4914d5-5805-6830-88fe-5000afc99381'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6f118ccd-4338-310d-7163-aa7bc3390945'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8092f043-5eeb-83cb-0567-61602b62254c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe7484e6-8148-84d4-89da-fc95d4628f53'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f9321517-1e10-9201-8977-be4105296840'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '54a9b86b-2673-a616-96ed-ec8329a82459'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1e489d72-313b-8a07-5fe7-7bc2e3dd3422'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5e6acbae-93cd-53ba-1d01-93ab42a56b36'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5e83828e-3c59-3458-5082-cd116bbe3d18'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '75547f79-0603-1283-8615-0c3376a925e0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2dd394cb-57c5-38a6-8802-51b2fe994dfe'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3cfdc3e2-84c8-932b-0a29-0b2e6bf522e8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a0cbd0e3-a23f-5056-4328-485925974ca7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9b231d96-3af4-a11a-5223-c739f94b8dc0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '390fed05-6d87-88a6-a422-7125e3c20435'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5ad36545-804b-818a-2887-a0eb533a4219'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '800f6bbd-2346-1c75-874a-3f0c9fdd22a7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '812bca28-2213-3c78-6289-63f364d63557'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '54210494-0d2a-944c-7708-0c0f823537b4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f3da8cfc-1fe3-3f5d-3cad-ca075fa26725'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e5670b68-8436-879f-2ac0-85b17b734dd5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cbdbe18f-3b85-42af-152e-5aed61e0595b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a75b8fdb-6518-8942-8fbe-20b7b15434fa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'eebdc83e-4323-21e4-58a9-3984ffd781bf'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2da6f4e7-4668-83f2-01d5-51876f387be3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9a364a15-6e44-5b56-05c5-55d707451cfc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9fb72723-9b50-101b-5820-244a70472c44'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '369f7159-3036-6f8f-7bb7-ad4df93a0a0f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8beb93a8-7538-09fa-173a-4de445365c84'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3ca21109-83e3-a2eb-5a95-8552fe0a9326'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8f727cba-a7a5-6aea-2491-b03b8b975775'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '75215040-983f-8a25-98e2-46812186131b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9b3042e6-763a-3836-4207-093e5b171721'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '335e7e54-2497-a08c-a493-53cbd5332cb4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fd680413-5e4c-274c-923f-d0071ac29f5a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f8ba91c8-51a3-4461-618e-6bf558e45cbd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0fdff8c0-9b01-5b27-153a-5a6847ff07f0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7e397955-6428-10ff-3e90-223c2d551c05'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e6be56c2-63b8-891a-94e6-5ab4213b328c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'eaf42042-a378-606a-66b3-bd02a5c46db1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2f36452d-41aa-156d-a1bf-63e13f8d1a39'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b113d6d0-9eb0-6088-78bd-9b177c553986'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9934e926-4187-1ec9-6348-73f2d19c5490'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '52743e5c-60c7-1c76-1082-83883cd132df'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e053d552-a20f-a572-a60e-7b1369856b2c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4b1b404e-36d8-a3b1-6c7e-dfc17c121463'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f6376fe6-37c5-179d-7647-39fc69337fa9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2478ff0c-8ebb-04f8-6cc9-ca3df4414efa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '08b92dd7-70d3-18fb-9561-c90cba2d8aab'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5273e048-8370-24c9-3497-f5d6717197bd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '72132a16-8fc6-2b52-6c10-7eb6c44d59d8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'eb67b5b2-206d-531b-8755-05579a0831d6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1bd804c2-a4cb-979e-9e8d-671e136f1987'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b2ffd0c8-062f-8e7d-9625-252c69205621'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '061fb097-2688-530f-9e8e-50feb88b5e90'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2c63a07f-2970-7acd-14d2-118abe366d59'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0911a474-2c4f-7229-5315-93839cb6195f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '25fa71a6-3f71-1ccf-4adb-b68577f7122c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c5cb75bc-5b45-8a8f-068a-1dc8c4276e5c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '67d17ddd-69ff-6031-098d-b4e0e2651afd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c42cee94-76f4-2a4e-7e9e-8294ff2b2386'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4a61f5d6-6846-2c26-1a9f-c82f7a7d0e61'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '77601030-7151-5ae0-6f2b-17523bd511fa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9469792a-4c56-1118-7a98-22ef66e79e92'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '849ffafe-7ecb-130c-681a-fa7e05177374'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1ee75750-65f3-1785-1040-882fc3555ae3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0b9cd997-0ec0-2a50-702f-293017c74d56'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b8d6ad1a-381a-4297-1e42-202aec478276'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c37540bc-a4f1-1658-7ba8-41ae1f9109d4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9af17981-9952-a248-0aff-80f26d858052'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '30197397-7396-0691-5086-4567bee22b8e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e7ea02ea-7904-7f08-54c1-0bdce17d68e0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f324264b-8566-1c30-86c4-4e62e69d90d6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '228467af-27cf-4199-94d5-e2451c493495'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '47074658-7909-8a5e-3a18-9b5261a737dc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '618eccfa-57d2-1a1d-44c9-aaa4bea6021b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b796d24b-a797-5785-937f-4d1e13697d2a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '18cc4799-6b47-3fab-99bf-1ca098352b2b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b512897c-22b0-4bed-74d7-363da57056fa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '406d0648-86ca-7f3d-562f-c71e38855133'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3f864573-6f5c-156f-12e8-5708ee615db5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c3d3d9c2-88fb-0329-5585-a9ad016d4318'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '23f9aa78-7af2-35cf-385b-8ab3cf8d8bb1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '62c4e8ec-4e91-88f5-64c9-c9e83af541c1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '23ab09a6-911f-502e-741b-9f66454d8ece'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '39c8d0b6-a615-505b-25f6-1abbf7c03e32'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1165fe63-a5b1-8f99-29d9-f3824b9c494d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'afa4c0c4-959f-9077-7893-5b40418889ac'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '82435985-92ae-5e0f-08c1-e5f30b034e07'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5ca3041a-3c1b-4366-1da3-4fd85ee6a6dc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0bbf6b03-a221-48c0-2d6c-347cddd248dd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b1bf35b9-37d3-4faf-5454-921a9d5e2c47'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7f1d32b5-33f2-4fa5-a657-0f30a9674c6e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4d700bbb-6bb4-67b4-3d92-d5d44579097f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b77fbb57-0200-23c3-4cf3-9615ce433f96'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b700fa89-3c73-1aad-40ea-b21b31a30240'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'adbe7436-6ad6-00b5-7386-6384ac3e9605'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a7bbbfcc-942f-880b-330e-ee64a28c22bf'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5594ddbe-72b1-1974-1149-ae7ad7648656'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2304d8fd-27cd-a72f-65b8-cb5eb37c306a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4b2fb3e5-47e8-19df-6d12-ca79e02730c0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f5150584-4299-6d4e-0b3d-057ca3d21d87'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f9dc09a6-3d13-3e8a-72b0-aa5a252d4631'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3e2d1057-6628-54d8-56ce-9ebf8bf3598a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7742eb6a-05d2-a58b-61b5-885bdd74221d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bd761654-0f90-6920-1832-be14e3669f08'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '993809e6-4863-43e8-59f2-2a88e38f6e50'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8872228b-7f1c-6dbb-56a7-759906db26fc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1903db1a-615f-a5d7-576f-5a9c2a7c3b9f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '238f23e0-a56a-05fb-8a7d-742384053883'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c490b8ba-1704-2ffb-940e-adb6af391543'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9952d41d-17cf-5be4-5bb4-00b5814b73d8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd9122059-43de-001f-9f3a-c4effb586ffe'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1425ea33-365e-5297-6dfb-84f0a29d770f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2808662f-8815-1778-1693-b6d485b80d1b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b1af76db-241b-623f-90be-68b7fdc729e6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '848c946b-948e-3980-5288-644f352e46f3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '206c29a6-2a7c-14d9-8e0e-87bbc7560c45'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3030a9d1-8199-86db-8835-1799c87b6612'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fa4b76cd-57a7-10cd-55ac-474e8ac57305'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3f22683e-8908-46e4-a4be-61d9cdf8ae5b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1f6c73f6-3cb6-9f79-15c8-573f58d44b8f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b281459d-4365-799b-88e0-5b4fc02d7080'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f1ac7a5e-7a8e-9d4d-5d69-2de1df844634'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd769c3ac-20be-7b2f-631b-10aab025696c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6a8435b4-2185-4ff3-6a09-b792e835211e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b8804197-8128-3fa8-0d0f-439e292f11ad'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3606d3bf-164c-4b34-06fd-55bdfd364e9d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '766e4aa2-527c-a512-83b5-4a60c227892a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8a840939-7574-2550-9c4b-e8e138832a4c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e4e88083-3ff9-9a7d-89ce-1d6ea9ce6d2a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4675d79a-7532-7ca6-6fb4-c2960b2d612f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '21cf4c57-24b9-6ca7-6e56-fbcdd15d8ee2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4e9cf2be-55ed-83b3-134d-6631b6b44814'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '43ef313d-1495-8826-4d00-219f3b5e199d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a1d50ed4-849e-583a-8466-a8bd1bfe3b13'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2e23f09b-8037-72db-6922-15fdee019843'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f0e3d2fb-24f4-1382-7cb9-06c532af934c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '86e75922-62e5-68e3-8562-fe4cf28c7e66'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '029343f8-6120-4558-6258-2676cb041343'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'db43e3a2-6bd7-6dbd-3476-4bc986ce6242'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0b93ca2e-2e08-1631-283c-c3ed75439df8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a668eb71-70f1-32aa-1bcf-a3adafc86eb1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '97741a0b-8df3-496d-6f4e-9628105f372e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cc8941a9-2b55-2f0d-34a4-b0ced6bf0142'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bacbc144-6e2e-0533-0f6a-134abe9c74a5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd42e9564-9f26-1d34-7e8b-b071b8190191'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '77f8dca8-828e-1700-73f1-3b5f3da52b1b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '677bb49a-459f-3974-7299-b2268eae5b8e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '179adc9b-4d9a-18e5-25bb-f45a10fe79de'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3052097e-424e-78ce-98a8-695328497d78'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe11d1c4-17b7-2a0e-6b4f-24fe7a2030fd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6d70e03a-95d4-0fdd-1c6b-8fba5e992f89'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '126b86d0-27b9-2003-9aab-651432ad90b6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '67d55c1a-a636-7bfd-145e-27e8fec15091'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a05dd6bb-2072-0400-7c48-3be995af8a82'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7c4b1806-1706-1494-1cd7-d0bd4686a5a8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e606eb58-035a-7824-3c2e-d91fc96c6146'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '918f0a76-781b-6401-02a3-06743fe10e5d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '95d74422-257e-0511-26f7-439dd68b5587'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '07224690-a6d7-4757-9a01-a15be7c95f06'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c653c879-5109-5008-84b8-c25989f6021d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '490d8219-744a-5164-38ac-d78228f9331d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2f8421ed-4c8e-40c2-8356-0bbebee058d1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b3308b22-2e4f-3638-85ce-f86bfc2c20ec'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6e457d06-3a6e-a5db-45b7-2af833434381'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ea0250f0-a28a-9ca6-3c10-8721fd8d6369'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cf2a8462-a361-5acf-9ba4-e8c30a861e90'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'db114026-9f0f-8fee-5c14-56e610b34ddd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ce80c34b-8b2a-a24c-3fcc-725d723c1286'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '308c880a-a36c-a1d5-62ec-c2ce371e6449'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ab056687-3a89-0f59-1581-edb3f86712a2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1af735a9-4367-2b0b-2b28-86a8449d1c60'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e7d93790-5cde-92dc-2691-fbae846c5490'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fb439c03-484b-9837-7b8b-425a34cf809d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ce2b08b9-a4a8-4974-9c71-6e5ce68a0d3a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e22a1fc1-6ea0-7fee-7b45-5aa8f11a277c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '10437aea-9c0b-3651-a5ad-451a54112a17'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ce2454d1-0c4f-405b-4781-f260d61a7522'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '06924ba5-2e40-6adf-67c8-391adc356400'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ded9d785-40a8-968e-4dfd-7b699fe43390'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '40b24a2d-6a7f-42f5-9719-0c31ecee16be'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '64092568-1257-6e6d-6fe3-0a2a32d53cf6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f98e5515-5bd2-996f-6bf3-c4f7b2870ef6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ebd463d6-6094-2a71-135e-19d107808713'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2b5cbe7e-4ef9-4727-0b64-36601d981452'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '66bbae00-4be0-56ef-97a6-779835ea3972'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2e0fe32f-356e-2a5c-33c3-ec6d13e75cd3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b07768fc-7a60-056c-0475-ffb3422c8d58'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7af82fca-3f03-21f6-846d-8de44e822a54'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8a36aae6-5be1-74ba-a4c7-d7b9afb424cc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9a2a9b30-4570-8c8c-4121-7e49f5a40573'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ecdfbd57-0c86-69ce-5319-b4aa47ae721b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '079ec916-657c-6f01-3491-badfca6079df'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b663835f-6e43-8b03-7295-f5fa00df821c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe0efdbc-8c48-61e5-1c23-ce25770a5af4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '352a1246-3904-42cd-992f-4cc7d93990a5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b5f79c82-03fd-6a61-0393-71a97dc660f9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f9314803-8bfd-0ba9-67f7-fb021cf84f01'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a55f43a3-8726-3d68-14c7-979f9e9529aa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2d40f3f1-2a1b-8b5a-4900-1765c230044c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2029bee0-1fda-34af-5b47-f2fdc2ae7d94'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '61ebf10c-442f-87bb-5e6e-b62c117808f0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6af5d2dc-62f1-1810-9bd6-c57c01743c36'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8aa83013-1cb1-23a4-9687-c6c8774b32c4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b422914f-9d4d-99c7-354c-880588dc8bf7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6eeb2b8f-9539-3284-097d-6b5080877841'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b0f696dd-9321-2ec3-0df5-d795f4ae4a61'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9a73fc68-a537-9037-8a48-d21fb063795a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'dd4342a0-12ec-4a91-8cd0-e89df9fb99dc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5b2ade7f-4426-458f-6b20-eabc92d5236c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e297c12d-01d9-7aa5-4870-522f2fc25c50'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '82fc4771-85aa-1c04-96aa-a49eb4ed8333'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '68db1b71-3aaf-a097-89cd-702243d293c3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1530521b-1bd5-81c6-84d3-1267478e59c8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4fec3235-7391-23dc-5b8f-e035b1e42f66'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '629464d1-126b-31a9-354c-112216d10ed9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e73d2365-a715-3d41-5a5a-3d7429ea85d6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8d4d83d6-1e43-1913-a321-098fbe208d5f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0c78269c-7bcb-5d62-1e1d-34c049f7506c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fb73bbf4-396a-51d1-14d2-408f1ce32337'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '21410b5b-0418-99bb-545f-ccc1421f64fc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '22935e27-8f86-991a-7811-e808121e0d1a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9d391879-1e39-21fc-03d4-be32f64d746b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2f2b0203-15f4-309b-3824-310b5b94a3f3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '63e42feb-53b8-2d34-2db6-412c82075b5e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'dc8dedd9-8bf7-2941-9e19-4a34d5d550a4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f4d03a19-5d18-1bf1-7bcb-8366217112d3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b59cf7f5-13f7-9225-89f8-340e94726d02'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe2e5e27-2993-9123-876d-367e11d459d6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a5a95e03-749e-1804-3439-10cfa9e01050'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6933ce59-76c0-7554-42d5-bd54138d0784'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd59d701f-563e-4b5c-2af3-0c9be69890cc'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '817984b4-695f-6e99-2aae-de0fcad52706'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2614fb61-09bd-9e74-380e-7ca20d2d285a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c2c9f476-244e-33bd-0b69-2cff899f459c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '01a5632d-6f47-31ca-8a87-056db181995f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6d1bb993-2e68-2b55-a792-a96401914a64'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2953d9c2-1587-75fc-9eb1-2dbad11d51da'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a4018fd8-0665-6149-3eab-efafd1819935'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '16843cd3-0abc-66bb-5e56-9dea4d3b2242'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6bc293c6-765b-8ee1-8226-4bb178c41429'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c759773d-5699-6696-41cc-31c04e907a26'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bbad1b0d-675b-14ad-368e-aada708e4c60'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a9ac1466-34ca-7734-95df-3858464b9c47'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'dd43e172-349e-8f56-14b7-93f490f6819e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '05e3cf1c-1883-5c45-3784-016cc6ea3342'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f1aafdfa-a35a-4b7d-6f93-1f9058a1749a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '774dda71-a77d-3fe6-2500-4eb40f4d6495'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f75f2f53-11af-50ce-6f04-ca22314c04de'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b57e4925-1805-3238-6014-8dbb916e5a29'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1a3f2c47-92a3-60ac-943d-01364a65518f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e2e34ef4-959e-2710-15a7-0eaf198ba34c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f96368dd-7f4f-8595-00bd-bbfddccda1a6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '037aa23f-6365-9f74-9b6c-b23af77552e8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bb8c7c2f-5df6-50cc-9bf8-6f8137543222'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '983a127b-5629-61cc-9fc2-5adcaf566106'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2cbd3100-9fa5-6c3b-4795-7bf181a767bf'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c6ed8c65-9336-2f0a-6043-dc2a7d01422f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '73d17fe2-36a4-409f-4bb0-0539655a7cbd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '30b0fb78-57d6-85ef-0b97-1cef8a4e53dd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0fad3cfa-1a19-7258-698c-d1665c67007c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '919a44fd-11b5-49a1-a3c4-45dd0dde9eef'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5b2eccf6-61f7-7b5e-0d95-fbe3bd54a777'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0af63f8e-9b62-61e0-3c5a-fc9b67f46912'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e0b63582-07b6-73e7-4e9b-db3a324f7567'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8f44d188-4017-0f58-1ba7-51d0a73d4cd3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2b91af61-520f-7c57-13ec-dad186cf3669'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f374435b-293f-4255-91d3-bf08ce187fc6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8e430664-8435-1482-2124-e3f07bbe974d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'eb77c304-0994-6515-66f7-5106f2b285b7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1e4339b4-5796-4371-4770-bc8e6b65530f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '80702547-77f8-9d1b-a67c-dfacea637da2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7d73652b-5849-551d-1baa-1c7f90ae606f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ee5fc432-6791-752b-3763-62d26b414e89'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f7554235-876d-a464-358d-f0e968465b47'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '505b4b70-0ef5-984b-7868-1cce7de99eb5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c3d1d2b9-7b69-86ec-419a-f8a6644f4ea7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cbf2a3a7-02a7-3b7c-95a8-289c401a3eec'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '48fa5881-26db-3066-2b5b-973463ada02d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ce40ab50-2111-6ef8-1c6e-08176c1138ce'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'aafaf1f9-26f3-7b92-81d0-2ad6b515237e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0a5e0965-0fcb-02ba-9353-e685106869c8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '12d17693-14c7-9287-12b0-b1787ed359ee'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'efa17e3b-89ed-29e0-73b6-ae0f17af3d68'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f500d744-21e9-94bf-6cbc-c9fdfcde7987'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '48bb8363-51bd-54a0-45fb-b63256f42cd5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1aa714d8-8a02-a00e-79d6-a10c7a738c3e'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e608c293-0452-9748-12aa-f0d722406426'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2c8177a1-9dd7-02d2-30dc-435b511223b0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e60553cd-4301-50d1-1335-fefad3134461'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '858c29bb-4f00-0c22-0099-8316a3ce0d79'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe7fabf1-474b-83d1-49a5-edaec195771a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9f5516fd-558f-399a-3ec0-64b19ddf20e3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ca1625a0-0837-53ad-200a-d2d013322155'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '814cf449-336f-0e59-4bea-909d9ff48915'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'aa70628f-3576-3a17-123a-86dbc72c56f4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8ec7b13f-8fba-700c-75da-3766beb94167'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '50814f6f-8693-9c98-8263-42b196ed0c4b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ccf7180c-3f07-984d-80e5-2051624d0da9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3c061439-7834-191b-111b-a0cb3ba33f7b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5a4e1d2e-2713-7884-97ca-9f38800f2d23'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ad50b162-6c78-289a-4e75-6a7d70135c32'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2f6b6938-3a8d-50bb-5e37-9e46ad7858be'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '402f99af-2a72-4970-2717-1af9491a7e29'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6f6ffe3b-7964-76ce-97ea-a0776769a414'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '43af33a3-1c5c-6360-1f43-69a448dd9bff'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0a6dfc8d-5452-2ef4-7f28-290dca075549'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fb0f6e0d-0179-a5cd-5e37-8be84df42829'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '48730eca-438d-99aa-1c46-27b207126c25'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4c957e9e-0481-7e4b-85b6-42b38f62634c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '00128de8-17c2-0f67-063c-3fdfd63f45e0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd47a5173-4309-85e3-73ba-14d081bf1617'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b87ab736-5ca9-7656-50a6-a18b27e45222'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '847a3ab6-122a-8f3f-6bf4-e55fefeb2ce9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '311e4bb7-958f-4d4b-5ccc-19382c7d7f92'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '700d90b2-08f9-3e41-7d5a-82500f819e89'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6a60cef6-50c4-7448-3acc-de9416c27e37'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '973cc079-0370-47be-0082-61300b5e10f1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8fa452b3-9b69-2f1a-5b1a-482a7f837c41'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '381dcb8f-8afe-6d32-5ad2-920824ce05b7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '77ccc0b2-0fb5-4e37-62f0-a7b38e3b05fd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5e1be9e7-18db-07c7-22e1-82f7239b6dcd'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f785e987-4ad7-181c-7ef6-3dfb43157ec6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7c91411e-21e2-9f19-786a-099947974053'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '38a38518-7a5d-2fe1-97ce-2b2083da188b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '56c2f349-57e4-34fc-3fd5-37d6652614f9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '720d7afe-2a06-13bf-7a77-71f024c48e12'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f74ea419-8d88-1ce4-8457-80ffe6001c2f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7d2b0660-2298-9d8b-0e78-b78b511a1c15'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5a0444b6-6af9-8139-5a36-673f8d535bf0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1ae29876-2d7f-3920-517d-123c17403fdb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6565e44c-4e50-5e30-4f17-41937f307fe9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4bdd7e10-3cb4-2d72-0c33-11fe63be39ae'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '88eca14d-1953-2db9-6cd0-9fce91c09b4b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8e1cad13-276a-7b4d-1d71-197054be7cfe'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c7389949-2f9c-0f6f-842c-40cf855f7ef7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'be0d5356-9591-8f59-2b1e-8b5872448ef5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8f3f18ff-92f1-4467-a18d-653aa74466e5'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6497b065-8a43-24df-6ffa-746162028db9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9a7e59e3-45f9-01f4-0208-e9e87787070d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a2bc1647-4f37-96c2-1982-a64df2bd713c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '40a5cab1-7f88-228d-6024-b79cc5f448e4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '78697a4a-06f9-2a2f-6f0d-70a197a7532c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6cf95ca0-2740-1cb3-594b-2661dcbd2144'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1ffd7365-1a71-8ad4-2d81-377d9e1d47f8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a83d804e-7ab1-4874-b9ca-1e19c9e355ea'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6f962b88-72c2-20a3-7a45-718c09626cbb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9c6c4e8f-3690-1404-5012-e00000c41982'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd6053689-4b1d-9192-05dc-65042ca352c6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '864fa405-4201-a229-41d3-c11625743a9f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4b43409d-7328-2aba-36fb-57ab2d7d3575'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '075955ea-9608-2f85-3e3d-5bf2d36873a4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3e9caa8f-5f56-0d68-94d5-fcb0822b4085'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f831b86d-7bd0-80ce-21d8-61afa46d878c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e4c0cbe4-2692-1656-7aff-475aa5f04c34'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '96881b0e-a4ad-273a-9bae-28836323a382'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e443fe99-8d48-7bab-4941-4fee0481168f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '50f2d9ef-a77a-4d19-2181-12d89a920b09'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '902c6af1-7579-15f0-243c-e5b08ee42176'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ab90ef0b-a5de-417f-9a85-93dca6e943d6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '45719c2c-21c9-6575-918a-c52c7b61a05f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '033dfb9f-090a-729e-31c5-17b1ce851e75'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f74189aa-87ef-21d1-7db4-bfdc4740684b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9805e696-a19b-4af2-2418-d85db3b26912'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4d0a6ef8-1f1d-2229-18b1-21dd931e02ee'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e40e21b1-9867-037e-7602-dcfdd7a63b44'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e1ad2f0e-6561-7ab2-512d-3d0a1326981f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8e4604ef-1d4d-5720-3fbe-04fda9300397'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1d38d6e1-8a5b-2349-1c89-c5ccbf97615f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6c22e373-59f6-9a40-9d6f-7e57271273a3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '272049bf-003c-20fa-a785-2532453b0aa1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2849cb72-602c-07d9-2530-9d2de5a03a05'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a5b79d98-5e7a-088f-1531-32d644900c7a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c177f12b-2ec1-4324-8070-65eb6c746dbb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a5f47c49-465a-63af-96fb-e16f4b0b35c0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2bbe85b5-8d8c-3dd6-542c-a3a11d079494'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cfebb9e1-65a6-23c5-1bb3-60073f941982'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4c7155f2-780f-2107-01a9-3697351736d2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '69fcd440-3821-6239-3ced-ab1353bf9141'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '048bc8a2-6ef0-5c78-938a-6f4149786965'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5013bf49-89f9-70e1-044a-e836297e628c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6bff5eb9-3b37-8c40-036f-47df859b8247'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2ac3f854-8400-8d66-1a5c-ba88425e8fe8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e7633693-4fad-3109-616e-21441ef4242c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '70d339a6-6d87-7da5-1bca-6afb69888dfe'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '755e7e3c-2df1-5489-81f0-d14b176190e9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8ee4b864-96f0-43a9-5929-9cd445e9970d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fb8b7d01-a411-0f3e-807f-4e077a088833'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fe7c4d28-1063-55c1-8cf4-20564b4f6182'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '630c37d3-8e7a-6e64-7bfa-6124d94735e1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2542818c-2afa-a158-9752-c20c344656f0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a7511626-1f53-2166-6b9d-efa2c1d5135c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '28f75371-8ef2-4429-6733-5e6803110983'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3d3a890a-3d06-0a26-165d-b74971514ae4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '34d1c851-746b-4dcc-39e7-15a26b00809b'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7bfcee0a-5414-90ec-737e-590a6aa00e10'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '897a7e6f-82d9-57d1-7ce0-087bc6c79c0c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4b2240f3-9d20-6364-4b35-6f24b4318049'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '82815296-5fe5-141c-3380-180f29ba2faf'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'd28aba02-9729-8fc6-1ccf-19d2e01e4500'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c469ce87-8743-004c-17ca-d300aba87304'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '968c6dab-116e-3447-270c-70cefe6f6da9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'faa778ae-8868-00a0-2cdf-7731909e20ce'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '950987c6-551e-99b5-47ff-501e54953d43'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '32713096-95b6-7c18-576d-5026681e7564'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '0d738c42-9d45-296d-a425-aab6e5482d9d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f0f2fe55-922c-462c-0231-8f1814108ee1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'e48630cf-5304-1e98-63d9-c9bca0b01b0f'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1696968f-9d45-7f67-053c-44e538ef9616'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '17020f19-3542-4745-4dcc-b72fe6181b3a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '05672bcc-75f3-7c3c-6f24-85b362f9642c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8523776e-4ac1-14ce-8153-baf2cbbd5612'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c2dd6b8d-713a-909d-3b33-6a8017858f37'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ea7f5d69-926d-9349-1877-f97e5d1411c9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a4644949-9517-a2d0-59b4-166e6a2ea42c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ce2d541c-3447-25ce-2886-0c8800bd8380'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b2242a87-5ab8-555c-3ffb-8c43c54e199d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '5ddf7603-4f0b-743e-6999-9cd02e222d04'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '3fba187a-7829-9df1-a36f-5681f9b01c89'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2a654eef-2dea-5ae0-37da-1260a7a94bc3'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '56cef6e1-61d1-492d-a5d0-2376ba3578fa'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '6698e861-a361-2494-4b60-123853ca3b52'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'dda8639f-4b3c-2afc-8e73-40e62bda30e7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ed7f1e4b-93c6-6b91-1eea-ad5d828f23ac'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'db24e326-136e-538d-a036-2658704654c7'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '9f7b8ed6-7477-6a04-4a4a-de09a4502b8a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2c096066-543d-6298-3a69-5eed518f0d98'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7c6984e7-7f66-831b-0100-afe0c4450290'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '98906c2b-71b1-a181-0c53-38e03a39138c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '675c68dc-23df-7033-665f-4226b9e09430'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '22b133e6-01f4-220e-6e93-fc7899bd63c0'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '05027ef6-a53c-7c86-6182-254572ea6df6'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '702b6bb3-7a13-650f-195b-557bd53f97bb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4696cdfc-6e88-8a6d-9142-002f09b86f2c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a8f8a0fd-a509-3e96-63e2-227c09044e92'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'bc6bf7be-7057-2e23-5efc-e038651c9844'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'b2618c49-22fb-6702-a702-2397217c0ef1'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'af564fd8-7030-057f-a1cf-e51d446a19f9'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '7c26352b-9257-72fb-0d37-22a5dd3f8d43'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'c1573eb9-a48f-0d04-3784-8c848bcf1447'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cb7426dd-3025-3ca5-a579-926348c21ce2'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '60614313-7c28-14e5-7cf0-706705a04a7c'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '62cb0bc8-0b82-2629-1316-5ab3dbc90af4'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1aa28bdf-328d-6275-39e0-04791eae7a4a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '1f2eb5e9-159a-12c3-3520-eddbf6d38f35'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '19e879b5-14b7-3a9d-9236-7111ea942a0d'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'fb291ded-9470-67b3-95b6-b94e5cc75759'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '144b4426-5e7d-a34f-07ae-cdfe73a01534'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '4ed7cd41-0e5f-67d8-1861-d33305286380'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '41c7dd6c-5dda-4550-2384-e31b87ef1367'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '8fc8649c-25bc-5e0d-9742-7158fb360e7a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': '2504b028-8847-a23c-a389-ba39540618a8'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'ea609e65-029a-82cb-8be3-9dfb5e5e1dcb'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'a86f35b4-2da4-67b6-a62e-a743a20a0e6a'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'f5c92a83-7567-3f88-2adc-ae2f9f6b3522'}, {'bill_id': 13, 'total_due': 7000.0, 'student_id': 'cefe9b0b-3955-6c3b-05e3-024548ed2b6f'}, {'bill_id': 15, 'total_due': 4010.0, 'student_id': 'ca5570bc-eaee-48a8-b25f-60d099199785'}, {'bill_id': 18, 'total_due': 200.0, 'student_id': '0e965ba3-3256-465b-9d22-9484b4fc071f'}, {'bill_id': 19, 'total_due': 2948.88, 'student_id': '760a89fa-1b76-5f18-2268-0cd9553e281d'}, {'bill_id': 20, 'total_due': 8000.0, 'student_id': '760a89fa-1b76-5f18-2268-0cd9553e281d'}, {'bill_id': 21, 'total_due': 131.0, 'student_id': 'e1eb26ed-31a8-4738-a6ff-50e99ffbdccb'}, {'bill_id': 23, 'total_due': 506200.0, 'student_id': '1f17f53f-6d18-42de-97f4-0c4a30456888'}, {'bill_id': 28, 'total_due': 12100.0, 'student_id': '0cf68ed0-39b4-a65b-2b86-9ed1fa5fa2e5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c8264ad0-69aa-08db-30b0-121d28492696'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c526e33d-3259-6d70-2c5e-022b5fbe6e2c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '85881e33-378b-0e70-99cf-1f111a0e7f83'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9c0045ab-5d78-0055-7121-a7e04bf8a3ac'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '77532c18-9cee-5f0f-6f5c-6665bfbf7186'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '561664d9-24e2-0f14-6f76-d23611262686'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '54602213-1b71-195a-76ef-9fde8e117847'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '06e087d3-82c2-43c4-5b3d-8ac277160933'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5205c278-620b-a7bb-28e6-433331dd4dbe'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3fe92f8f-3952-2fe0-2019-83cd8d7a2fc8'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '24b25ee8-59cc-8028-a715-2970d68974e4'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '85827b20-5ca0-2e3e-a0d5-a1ecde7b0d51'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4c369e1d-2dc1-016b-1283-1643f72c02b7'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'bbc5c261-a1dd-71e4-54b9-9fb7d7067b13'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3b323b66-4ef5-0586-4e6e-37f6330b19dd'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4182784d-0bb9-9c22-3273-b8d4b4f1921e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4f5a559e-8a7b-8911-2acb-d55cf7b06970'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '06d74679-6d05-4c87-2233-d4f6c3711d8a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c2a87666-1c03-29a7-2611-2458d14023a0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5410bef2-3979-6977-2ed9-0606219992bc'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1de4919f-3970-11c5-579a-8822f7a86f43'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '701a6456-21ed-a79c-2ae1-19066f2c836e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4a007f7a-7df0-49ad-09f3-8668438f15b6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '04943d92-2093-6b01-88b7-4d8d6686481c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3dcabb38-3eb6-52c2-1262-2c437cf90318'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b372f172-6af6-3053-a5e5-f21022622230'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '64e224b2-515f-26ab-15a9-d8a84ab36f4f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '69ed2923-48ec-0a7d-17e9-4691fa826f65'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1bfa8abb-5cb9-0bfe-a726-4377b37f3981'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b8f93449-8b75-997a-73cd-3de804389628'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5ee2e315-6bed-130a-435a-1fd021de7a68'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6b667536-3aff-983b-09dc-45fdd98e3ee8'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '00e8cfa6-041f-349a-8eee-a486a987a589'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2a104d95-5f5b-4bfc-05d9-2a50fbdb6a95'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'aad55961-013f-55f1-683f-93c6c65e8259'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e7892353-9326-2c47-7c9e-638623097ef0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '59c01b26-423c-4dab-8b70-d5f5533078e1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '178f4b56-82d4-7d90-8f25-744e54110e67'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6d69e47d-556c-6a31-452a-590528729126'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd094a185-3fee-8da8-8e76-896d2a0a5b4d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'de4dfea1-166b-67a8-a061-d25a29f75937'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2fbf7ffd-37fa-0140-2887-816b714e76f7'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c31852eb-4b50-7982-63e8-3ceeec9588e1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9081f344-7a34-1bf9-701c-f218ddb89575'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ec214ead-a1f3-27c9-2c81-e6e916635b03'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1c5d5dff-66b6-1e50-360c-899c15f583bb'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '82bb3719-9dec-2777-9458-6393e3cc36b3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '792ce6f8-8c5a-1bce-42f4-64e4318b8827'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5ec5257c-a58f-741a-5b9b-d7b44d1b16de'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '59b67101-92d2-3664-4c29-b97987a7216e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '57a3e583-42ef-4658-066b-1d1267343937'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c3e0122d-041c-3c20-43a0-85bc2f9c5e8f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0ffbaff5-26c7-19d7-51fe-402b1c315798'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '694286d8-55ae-1025-4671-9b632baa5a5e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '39a20a20-2b17-5fe4-3634-52f5f176a09f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2414890d-19da-9df9-72e0-7ed177831fec'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '89737766-470e-34cb-1a35-ec653c069c1a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'cd307533-5b17-1d52-0524-669508b98e99'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ce556b44-2885-79aa-51e8-0f2a44d02478'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3798c462-92f3-1f53-7100-f507e6b600e5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '099c4c00-64e2-71f8-532f-30550fba2c0a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7c228428-977f-2b5f-835c-c490ab948871'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '8091e1d7-3a1f-1385-0fad-4dcc7b67a048'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3f7cd157-2fb3-a02d-8b43-c57832139163'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f99a4825-56cb-068a-169c-72aca66e93e8'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '303546e8-0ebb-947b-48ae-10984bec0d90'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c80bc1c2-23f2-2574-a53d-8aca1efc4a03'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e0e516e2-3835-5729-84fd-5d65c33ba19a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '76d14786-8d13-13b0-21e4-8d8acbad2813'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3e122dd3-76f3-409b-49f6-6325cd154f08'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '703c7a82-1ef4-3ceb-8a6c-7ae26c9d814f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'a100ea90-92e7-445c-1685-d7f6d76c2f9d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '06d434ce-64ad-776e-0a5d-359a1dc65e6c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e3b36403-a3b7-601c-6a9d-0edc047821ad'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '367a8d66-7288-1cc3-2b85-fa17f3740311'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f8a9c514-8f42-a46b-4345-93af3f434d1f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7035e3e2-5578-049b-10c6-9a3c8a404d56'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'df969435-89e5-6db1-a531-6ee051828da0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '43f93924-9737-18a2-7e25-ce852b124e2c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '57a1ac59-a1fd-949e-43d6-382100ae1769'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6248eb6d-89d7-42ad-0418-5b10164c05ce'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c758fccb-9a53-7dca-8bca-86ec7a3a84a6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0d61980d-3f23-4fc3-0b35-682721158973'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e6f41618-95d7-60fd-1114-21668a5c15d1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '91696cc8-3870-9a6a-7129-ba9a6ba36931'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'fb184d2a-0613-8c06-5f3a-982f98c1906f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'db7e8edd-964a-3f39-871d-16a775906778'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '686dacc9-13a1-9e59-54b8-2a91e15766bc'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '098e2ec4-5d3a-5e48-74a7-b6b1768d27ca'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '781c6af0-437f-768c-084f-3b7c223359d8'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '318526e9-7d0c-1d1b-8746-db0113790a75'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e99a3099-5b91-226c-86ec-2f9f387f6896'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '035875b4-6f43-a46e-2701-757f1fd1254a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2d8e43d2-8322-141f-0ba1-40be5367317d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5bf70bac-9b32-6501-91fb-41ebbe8f4513'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '52f8429d-5238-93f2-72b0-2bea9bf57e43'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '430034cd-62f7-0373-4691-a7f8bd074ec5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c0c62ffc-01eb-91d1-0576-41a01c0e2ee8'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6ce966ae-3679-9fb4-7e0c-61f233108b3c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ed552eec-473c-9b60-1bb1-ba58ff221ddb'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4bb6e1db-15ed-7ee0-9d01-52d15d9e59a4'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2ba032c9-0e79-003b-843b-fe8a5f1e1612'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '067695f1-80e3-9067-3273-aa9de1c60499'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e18b75a4-4e29-34ab-61d1-9304de835f33'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '749b7d77-38d1-2383-9bf5-e18e59212e9d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0847d017-0ee8-3e9a-5298-38d6a88b6962'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b19449a4-56dc-5c6c-9076-c91052c55c77'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7c8180a6-30b8-1bac-9751-aa93a9ea527f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '17fca378-8fd3-1feb-171b-889756028d64'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '85c34c25-4c38-2037-0b3c-9ee411f785d2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'dcf9fee5-044d-9a38-441d-a73e440177b0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c43845cb-8769-4fff-4777-596b4a7644d6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5be63658-494a-679e-652a-5eded9e36da2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'a727fddd-68e0-1684-118a-fcabce2548a9'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5456875c-5a13-737a-67fa-d82a03ed689d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '8da4f45b-0321-996a-5757-78714ccf038c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '94b925f9-24cb-3ccd-98d0-3235c683640c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ef48417c-99c7-2ab8-a6b7-ed28e9b99853'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9fb653af-4b40-82d0-5cc3-792d22d126a1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'cab2c4d0-3509-5388-9655-07ee50073f00'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ee979160-6aee-1530-3016-c06e3d9a9551'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9d143923-690e-5fe6-58c6-941dbdc53215'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6a52862d-4c5c-13e4-9933-aadd6bbd405c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ddd5efc1-79e7-2974-8a0a-ce136224828d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '642dd795-5ab6-09cd-5129-0103dbe05cbf'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '85827ab4-148c-5a1c-1366-21e4398e362a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd59dbab4-1345-1021-2770-172cbae81adb'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd382bf86-0ebf-23e6-09f0-0e481a6f3dbe'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd72d772d-943d-7c25-7f28-f7f3a3eb2abd'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '769771e6-0540-935a-67d0-1d477a849d19'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd5fc97aa-78d0-6f2c-66fe-cff80e0d014e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ea031862-4715-349c-015b-9e76f2a800e5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '255cff9a-50ba-7c46-30c9-ff5077eb89ee'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '652c041c-5d7d-9ca8-94fb-ab55e3862bc3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4f62acc1-3a9c-84df-3c07-b1cd7d081b09'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e07b1f28-2039-270b-089d-5da40c5166e9'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '044d3ecd-6262-2d66-2038-a44125a02c2b'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0b87e7ca-5f88-76ac-a19b-9237da433c9b'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '64731f97-5e92-3c3c-9aca-9e1bdad669bb'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3fcf8b96-4fe9-68ec-98e9-866798bc1b02'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b3c1b84c-1d60-872d-3df9-ea20be2b362e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '607a186c-3902-9096-898f-1f79485c8b31'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6303ac1e-02e5-5b93-8393-59990ee9a40e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd9233a2b-9660-18b0-a461-ea9b09794114'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f5d2a1fe-82af-04e4-9946-09e9b8d05767'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ef6ef3b1-4fac-0d02-1173-c1e2325548f7'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2182f797-795c-51e2-8fed-6dc8ac9a6772'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd1a3d330-a7bc-4d53-57e3-27802e0c20d9'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd62ca2eb-7ee1-7460-72c6-6886188075c6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c4ea101c-86cc-7d0f-8a02-833236942c64'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '45d06085-1ab2-1329-8b9e-a63e7c2c0910'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0f2e53cd-845b-825a-702b-e44ff2501b23'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7036f861-2b34-06a0-8f2b-ee59840e6c9c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'cb8342b7-2089-71ba-57df-810b5d624a2c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '94de04e6-6f79-7cb9-9693-84de8be6725e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'a8b34a16-40ea-30b6-a4e7-51c0801c3336'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1ef2d2a2-a016-9cf9-122e-76f4deb45b72'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1521954c-4126-5e4b-2a96-5b6aa162a0c5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ea56a2a3-6ac2-5ec0-62b4-438f35970691'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5232377b-2873-1f24-497b-7d4836170022'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'eca57ebd-2958-1f98-9a15-7323272f7ff1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e3523e62-0456-09bf-1890-7916c0da887c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'fffbc992-5419-23eb-0582-7a99b865798a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'dcbb0631-0ea1-738b-3281-3e2582a6a017'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '53a57913-61c2-3e10-962c-a0dc28c77eb6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '88bce103-a64e-6354-009a-518c49206bb1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2b42f19e-7924-9aeb-30ff-bbb4bc6fa3d3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '328d25dd-882d-634e-30b1-627de6040d2a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '11cf9d8a-6fde-a2e2-654f-066bbf064815'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1033cb6d-03f8-a4df-34a3-e582778e7e88'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd0343321-7172-1e72-768c-6c08bdee5e36'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '57941f4b-526c-1ea8-3a18-ad8eeb7e6457'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '63196a20-33eb-740d-6bd0-3f018b9d6304'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'bf41a50c-4e5f-1b00-3278-d595c9fc1666'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'efc2662e-0fff-90ca-3a12-a75b5b0b526b'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5558492b-64f1-36f2-8b4b-1bda8b0a9472'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6d0f85c2-85c5-37bf-4acf-13ddc2512fe3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '36500e0b-215a-9ffa-964e-021be04c6426'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4f92a7b2-657c-21ff-1f3d-71e9329c299e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '643f20c2-8f4b-92ab-0e09-a467a9393dea'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6ec92e48-9375-988b-1b29-dd2edaf43901'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '39293b2b-01c3-71f0-62e4-8928944c930a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9cd9822d-7691-6599-15df-1867ea788fc5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '1b5416f3-1e3e-3272-5023-7351e54c4803'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f8d2eb75-7869-223e-625f-28595f274500'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0f29d39a-5404-57cd-3211-9bfa62297826'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f8e0dba6-356e-8916-2938-54775d56228c'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '165b511c-0528-40cb-252e-d211987f09f5'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2adaefb3-00b2-5f71-26b9-315f1fb43cf6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2e88991c-0127-24f0-3689-b20b1e2507f3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '21b3c6e1-1a95-7a05-499a-bc9f202a0172'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '2a3fae51-1014-4f46-6641-e10fcd0a5ef6'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '34496f66-65ee-81b3-6ea7-faf427950f83'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5c122e92-6bbb-70ec-0f97-7eb1e08e7ca0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '606d4292-297f-0b9c-494c-c19370ee45a4'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '8f8ff1e2-0c81-7c88-549c-c9a5335ca00a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ec9dfe99-6b9d-9fbb-720e-8981a04470f0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '54529b9e-57a1-435f-969b-b7adb0275e38'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'fc98e32a-5bea-5010-71c0-252cefb03b79'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '14be8744-56bf-3cc7-5a1a-f0a4aa23a455'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '916378e6-a2af-982c-6271-bac2e9717bb1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '8e9ea401-0d11-8aed-8fe0-644104e46029'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '4973e873-791f-379b-1fed-49f7c06f7985'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '02c46aa0-a210-3a3f-8890-a885b539501e'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e85738ef-8235-66fa-a738-583a5c620495'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7159f590-042b-00ed-60c6-9563da249861'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c2bb34b0-2b44-8580-83b4-485da0120f88'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7477cc97-3d61-9533-8edb-dff46e5914cd'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '6f9cf2fd-8dec-99db-9ac6-418e98655cca'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '885b0f5f-842c-4e73-7229-735dcd2f78eb'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '722c588c-a17d-94d1-41c6-a3a457774fbc'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '21749a04-187a-65bc-3240-d45a65b3972d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'a3946405-4eba-90e8-61ce-518905c43c51'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'efd1a51b-277c-34fe-85d1-091d1baba275'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ff6b22a2-a3b7-4009-6ece-d02654bb93ba'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'fa6be36f-1f8f-990d-90f1-7fed9f9f84ed'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '41aa71e2-75a0-516f-6246-553061955b51'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '60652520-a716-416c-4a3c-b2543fe3518f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '685a05f5-5c84-9574-2aa1-1103791a3da2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '722c588c-a17d-94d1-41c6-a3a457774fbc'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd830591b-6033-5a24-9ac9-f18596950e7f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '21749a04-187a-65bc-3240-d45a65b3972d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'bcf03227-1deb-53ab-9d6b-10761a626524'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'a3946405-4eba-90e8-61ce-518905c43c51'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5d1d83c2-1271-7ed2-81bf-d2bf07ec60d4'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'efd1a51b-277c-34fe-85d1-091d1baba275'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0370f8f1-7e88-433d-2981-058f92ea1184'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ff6b22a2-a3b7-4009-6ece-d02654bb93ba'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '442a643a-2b80-0b3c-903c-e76113262427'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'fa6be36f-1f8f-990d-90f1-7fed9f9f84ed'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'dd9938e7-053a-4320-3e8e-340050144c91'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '41aa71e2-75a0-516f-6246-553061955b51'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c4503a0f-8b25-281a-696c-fc255b9f8f31'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '60652520-a716-416c-4a3c-b2543fe3518f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '685a05f5-5c84-9574-2aa1-1103791a3da2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '71fec68e-2fb0-3521-79de-8ef87cc61e39'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd830591b-6033-5a24-9ac9-f18596950e7f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c69f4b6e-3db1-682e-234c-9eaee1b97f35'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'bcf03227-1deb-53ab-9d6b-10761a626524'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c3ed6407-693c-5304-89fc-4d026e495429'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '5d1d83c2-1271-7ed2-81bf-d2bf07ec60d4'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '27ea9a08-1421-18a3-8614-b627efac8372'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0370f8f1-7e88-433d-2981-058f92ea1184'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd4843bc9-9169-7958-6aa8-1c7cf47f8aed'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '442a643a-2b80-0b3c-903c-e76113262427'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'adc3af9f-6d68-918d-a697-d8a97e4e0007'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'df6614a0-0c70-7e9c-9af0-2f7ab15296a1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'dd9938e7-053a-4320-3e8e-340050144c91'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd61eed87-5313-5a31-80c4-f392de6123b1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c4503a0f-8b25-281a-696c-fc255b9f8f31'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7f8dd2be-0b35-1f29-4f8f-82ec7761179f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '71fec68e-2fb0-3521-79de-8ef87cc61e39'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '14ae7e76-9e24-8752-7bd4-382baff24cbe'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c69f4b6e-3db1-682e-234c-9eaee1b97f35'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7f860c43-8702-5870-11da-5c0299b49830'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'c3ed6407-693c-5304-89fc-4d026e495429'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9096021c-9098-5994-83ed-b02d89bea3a2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '27ea9a08-1421-18a3-8614-b627efac8372'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e0354eb7-5eed-065f-99ee-9d65f03a675d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd4843bc9-9169-7958-6aa8-1c7cf47f8aed'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'adc3af9f-6d68-918d-a697-d8a97e4e0007'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b1ec6618-0724-79ab-871f-89821f4411a1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'df6614a0-0c70-7e9c-9af0-2f7ab15296a1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0f4d31a4-078e-243b-7d1c-04e496a291ff'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3ef5e09b-39ef-208d-402a-f6e1166a2ee9'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd61eed87-5313-5a31-80c4-f392de6123b1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7f8dd2be-0b35-1f29-4f8f-82ec7761179f'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd72215c5-a395-065d-7e4e-2466880527f0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '14ae7e76-9e24-8752-7bd4-382baff24cbe'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e01609dd-8847-921a-95b5-ba34c2823c78'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7f860c43-8702-5870-11da-5c0299b49830'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0a19be2f-2423-2b4c-6aa9-a02fb4713da7'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '9096021c-9098-5994-83ed-b02d89bea3a2'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ddfba141-0c1d-3e89-882f-c79443352425'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e0354eb7-5eed-065f-99ee-9d65f03a675d'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7a4d40af-6bba-5ee1-97ae-767b8f68464a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'b1ec6618-0724-79ab-871f-89821f4411a1'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '36989fb0-6c34-335d-5b5c-b881f7630375'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0f4d31a4-078e-243b-7d1c-04e496a291ff'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f8320c7d-56b0-4308-593d-59b130cc43e3'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '3ef5e09b-39ef-208d-402a-f6e1166a2ee9'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'd72215c5-a395-065d-7e4e-2466880527f0'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'e01609dd-8847-921a-95b5-ba34c2823c78'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '0a19be2f-2423-2b4c-6aa9-a02fb4713da7'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'ddfba141-0c1d-3e89-882f-c79443352425'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '7a4d40af-6bba-5ee1-97ae-767b8f68464a'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': '36989fb0-6c34-335d-5b5c-b881f7630375'}, {'bill_id': 31, 'total_due': 2390.0, 'student_id': 'f8320c7d-56b0-4308-593d-59b130cc43e3'}, {'bill_id': 32, 'total_due': 1150.0, 'student_id': '742574f8-647c-18bf-8ca7-1a9ddf343cd4'}, {'bill_id': 32, 'total_due': 1150.0, 'student_id': '69ea01bb-61c4-6879-8f69-929d0a5e68e9'}, {'bill_id': 62, 'total_due': 1000.0, 'student_id': '760a89fa-1b76-5f18-2268-0cd9553e281d'}, {'bill_id': 69, 'total_due': 100.0, 'student_id': '760a89fa-1b76-5f18-2268-0cd9553e281d'}, {'bill_id': 77, 'total_due': 210.0, 'student_id': 'c2d12d80-9f9a-49aa-9dbd-f7a41fbf8b9e'}, {'bill_id': 78, 'total_due': 1050.0, 'student_id': '2567cb4f-66c3-4724-9eb0-9db50defbc42'}, {'bill_id': 79, 'total_due': 4000.0, 'student_id': 'a205b193-049e-46aa-b2c5-0856c1c785c6'}]
2025-08-08 08:21:55,455 - celery.redirected - WARNING - ================================= 
2025-08-08 08:21:55,455 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 08:21:55,455 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 08:21:55,455 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/5
2025-08-08 08:21:55,456 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:55,456 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 08:21:55,456 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'string'
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 08:21:55,456 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the total amount owed by students at the institution compare to previous years?...
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total amount owed by students at the institution has shown significant fluctuations over the yea...
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: graph
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'start_year': 2021, 'total_amount_owed': 170865.0}, {'start_year': 2022, 'total_amount_owed': 3528...
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_amount_owed_by_year
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 08:21:55,456 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 08:21:55,456 - celery.redirected - WARNING - [{'start_year': 2021, 'total_amount_owed': 170865.0}, {'start_year': 2022, 'total_amount_owed': 352830.0}, {'start_year': 2023, 'total_amount_owed': 220950.57}, {'start_year': 2024, 'total_amount_owed': 2999125.95}, {'start_year': 2025, 'total_amount_owed': 2624540.0}, {'start_year': 2026, 'total_amount_owed': 256220.0}]
2025-08-08 08:21:55,456 - celery.redirected - WARNING - ================================= 
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: total_amount_owed_by_year
2025-08-08 08:21:55,456 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 08:21:55,456 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 08:21:55,457 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/5
2025-08-08 08:21:55,457 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:55,457 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 08:21:55,457 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:55,457 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'string'
2025-08-08 08:21:55,457 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 08:21:55,459 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 08:21:55,459 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 08:21:55,459 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 08:21:55,459 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific programs or courses at the institution that have higher outstanding fees?...
2025-08-08 08:21:55,459 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there are currently no specific programs or cours...
2025-08-08 08:21:55,459 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 08:21:55,459 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 08:21:55,459 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: outstanding_fees_by_program
2025-08-08 08:21:55,459 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 08:21:55,459 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 08:21:55,460 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 08:21:55,460 - REPORT_PIPELINE - INFO - 🔄 Processing interview 4/5
2025-08-08 08:21:55,461 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:55,461 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 08:21:55,461 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:55,461 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'string'
2025-08-08 08:21:55,461 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 08:21:55,461 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 08:21:55,461 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 08:21:55,461 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 08:21:55,461 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What percentage of students at the institution are currently in debt regarding their fees?...
2025-08-08 08:21:55,461 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are currently no records available regarding students in debt for their fees a...
2025-08-08 08:21:55,461 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 08:21:55,462 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 08:21:55,462 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: percentage_of_students_in_debt
2025-08-08 08:21:55,462 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 08:21:55,462 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 08:21:55,462 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 08:21:55,462 - REPORT_PIPELINE - INFO - 🔄 Processing interview 5/5
2025-08-08 08:21:55,462 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:55,463 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 08:21:55,463 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'string'
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 08:21:55,463 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What payment plans or financial aid options are available to students at the institution to help man...
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution offers several payment plans and financial aid options to help students manage their...
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'financial_aid_type': 'cash'}, {'financial_aid_type': 'draft'}, {'financial_aid_type': 'cheque'}, ...
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: financial_aid_options
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 08:21:55,463 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 08:21:55,463 - celery.redirected - WARNING - [{'financial_aid_type': 'cash'}, {'financial_aid_type': 'draft'}, {'financial_aid_type': 'cheque'}, {'financial_aid_type': 'percentage'}, {'financial_aid_type': 'amount'}]
2025-08-08 08:21:55,463 - celery.redirected - WARNING - ================================= 
2025-08-08 08:21:55,463 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 08:21:55,463 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 08:21:55,463 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 5
2025-08-08 08:21:55,463 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:55,463 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 08:21:55,463 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:55,463 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 5 documents
2025-08-08 08:21:55,463 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 08:21:55,463 - UPSERT_DOCS - INFO -     Content: Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:21:55,463 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:59.753828+00:00', 'data_returned': True}
2025-08-08 08:21:55,463 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 08:21:55,463 - UPSERT_DOCS - INFO -     Content: Question: How does the total amount owed by students at the institution compare to previous years?
A...
2025-08-08 08:21:55,463 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:21:37.258531+00:00', 'data_returned': True, 'data_tag': 'total_amount_owed_by_year'}
2025-08-08 08:21:55,464 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 08:21:55,464 - UPSERT_DOCS - INFO -     Content: Question: Are there specific programs or courses at the institution that have higher outstanding fee...
2025-08-08 08:21:55,464 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:26.674474+00:00', 'data_returned': False}
2025-08-08 08:21:55,464 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 08:21:55,464 - UPSERT_DOCS - INFO -     Content: Question: What percentage of students at the institution are currently in debt regarding their fees?...
2025-08-08 08:21:55,464 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:45.018767+00:00', 'data_returned': False}
2025-08-08 08:21:55,464 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 08:21:55,464 - UPSERT_DOCS - INFO -     Content: Question: What payment plans or financial aid options are available to students at the institution t...
2025-08-08 08:21:55,464 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:25.434360+00:00', 'data_returned': True}
2025-08-08 08:21:55,464 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 3/5
2025-08-08 08:21:55,593 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.129s]
2025-08-08 08:21:57,744 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 08:21:58,686 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.667s]
2025-08-08 08:21:58,687 - UPSERT_DOCS - INFO - ✅ Successfully upserted 5 documents to Elasticsearch
2025-08-08 08:21:58,816 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 08:21:58,816 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 20
2025-08-08 08:21:58,944 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 08:21:58,944 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:21:59,077 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.132s]
2025-08-08 08:21:59,078 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (3):
2025-08-08 08:21:59,078 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 08:21:59,078 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (7 docs)
2025-08-08 08:21:59,078 - ELASTICSEARCH_STATE - INFO -   - 'string' (5 docs)
2025-08-08 08:21:59,079 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 9
2025-08-08 08:21:59,079 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 08:21:59,080 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:59,080 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-08 08:21:59,080 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:59,080 - REPORT_PIPELINE - INFO - ✍️ Writing 9 sections using batch processing...
2025-08-08 08:21:59,770 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:59,775 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:59,775 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 08:21:59,775 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:59,775 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'string manipulation operations functions performance'
2025-08-08 08:21:59,775 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'string'
2025-08-08 08:21:59,775 - VECTOR_SEARCH - INFO - ❓ Original Question: 'string'
2025-08-08 08:21:59,869 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:59,875 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:59,875 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 08:21:59,876 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:59,876 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'appendices interview transcripts data resources'
2025-08-08 08:21:59,876 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'string'
2025-08-08 08:21:59,876 - VECTOR_SEARCH - INFO - ❓ Original Question: 'string'
2025-08-08 08:21:59,902 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:21:59,903 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 20
2025-08-08 08:21:59,904 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:59,913 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:21:59,917 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:59,917 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 08:21:59,917 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:59,917 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion Summary Future Research'
2025-08-08 08:21:59,917 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'string'
2025-08-08 08:21:59,917 - VECTOR_SEARCH - INFO - ❓ Original Question: 'string'
2025-08-08 08:21:59,933 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:21:59,933 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 08:21:59,933 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:21:59,933 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'string concept programming linguistics'
2025-08-08 08:21:59,933 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'string'
2025-08-08 08:21:59,933 - VECTOR_SEARCH - INFO - ❓ Original Question: 'string'
2025-08-08 08:22:00,031 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:00,035 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-08 08:22:00,035 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:22:00,041 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:22:00,041 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 08:22:00,041 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:22:00,042 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'String definition programming linguistics'
2025-08-08 08:22:00,042 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'string'
2025-08-08 08:22:00,042 - VECTOR_SEARCH - INFO - ❓ Original Question: 'string'
2025-08-08 08:22:00,055 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:00,074 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:22:00,075 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:00,075 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 08:22:00,075 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:22:00,076 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'types of strings'
2025-08-08 08:22:00,076 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'string'
2025-08-08 08:22:00,076 - VECTOR_SEARCH - INFO - ❓ Original Question: 'string'
2025-08-08 08:22:00,089 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:22:00,089 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 08:22:00,089 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:22:00,089 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Challenges and Limitations of Strings'
2025-08-08 08:22:00,089 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'string'
2025-08-08 08:22:00,089 - VECTOR_SEARCH - INFO - ❓ Original Question: 'string'
2025-08-08 08:22:00,134 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.258s]
2025-08-08 08:22:00,135 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 20
2025-08-08 08:22:00,163 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,163 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 5
2025-08-08 08:22:00,174 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.257s]
2025-08-08 08:22:00,175 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 20
2025-08-08 08:22:00,184 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:00,193 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.259s]
2025-08-08 08:22:00,193 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 20
2025-08-08 08:22:00,206 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:22:00,206 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 08:22:00,207 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:22:00,207 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Importance of String'
2025-08-08 08:22:00,207 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'string'
2025-08-08 08:22:00,207 - VECTOR_SEARCH - INFO - ❓ Original Question: 'string'
2025-08-08 08:22:00,262 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,263 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:22:00,297 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-08 08:22:00,298 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.256s]
2025-08-08 08:22:00,298 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-08 08:22:00,299 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 20
2025-08-08 08:22:00,302 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,302 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:22:00,322 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 08:22:00,322 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:22:00,333 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.257s]
2025-08-08 08:22:00,333 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 20
2025-08-08 08:22:00,349 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.260s]
2025-08-08 08:22:00,349 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 20
2025-08-08 08:22:00,391 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,391 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 5
2025-08-08 08:22:00,429 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 08:22:00,430 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,430 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:22:00,430 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 5
2025-08-08 08:22:00,449 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 08:22:00,449 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 5
2025-08-08 08:22:00,459 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-08 08:22:00,459 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:22:00,470 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.263s]
2025-08-08 08:22:00,470 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 20
2025-08-08 08:22:00,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:00,475 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:22:00,475 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 08:22:00,475 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:22:00,475 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Case Studies on Strings in Software Development NLP Data Analysis'
2025-08-08 08:22:00,475 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'string'
2025-08-08 08:22:00,475 - VECTOR_SEARCH - INFO - ❓ Original Question: 'string'
2025-08-08 08:22:00,476 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,476 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:22:00,519 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 08:22:00,520 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-08 08:22:00,557 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,558 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,558 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 5
2025-08-08 08:22:00,558 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-08 08:22:00,577 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,577 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-08 08:22:00,589 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 08:22:00,589 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 5
2025-08-08 08:22:00,594 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.124s]
2025-08-08 08:22:00,594 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:22:00,600 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.124s]
2025-08-08 08:22:00,600 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 20
2025-08-08 08:22:00,601 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-08 08:22:00,602 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 5
2025-08-08 08:22:00,685 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,686 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-08 08:22:00,716 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,717 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-08 08:22:00,720 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 08:22:00,720 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 5
2025-08-08 08:22:00,726 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-08 08:22:00,726 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:22:00,728 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 08:22:00,728 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-08 08:22:00,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 08:22:00,848 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,848 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-08 08:22:00,851 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-08 08:22:00,851 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 5
2025-08-08 08:22:00,887 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.137s]
2025-08-08 08:22:00,887 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-08 08:22:00,887 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What payment plans or financial aid options are available to students at the institution t...
2025-08-08 08:22:00,888 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:25.434360+00:00', 'data_returned': True}
2025-08-08 08:22:00,888 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:22:00,888 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:59.753828+00:00', 'data_returned': True}
2025-08-08 08:22:00,888 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the total amount owed by students at the institution compare to previous years?
A...
2025-08-08 08:22:00,888 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:21:37.258531+00:00', 'data_returned': True, 'data_tag': 'total_amount_owed_by_year'}
2025-08-08 08:22:00,979 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 08:22:00,980 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-08 08:22:01,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 08:22:01,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 08:22:01,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 08:22:01,153 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.136s]
2025-08-08 08:22:01,153 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-08 08:22:01,153 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the total amount owed by students at the institution compare to previous years?
A...
2025-08-08 08:22:01,153 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:21:37.258531+00:00', 'data_returned': True, 'data_tag': 'total_amount_owed_by_year'}
2025-08-08 08:22:01,153 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:22:01,153 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:59.753828+00:00', 'data_returned': True}
2025-08-08 08:22:01,153 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What payment plans or financial aid options are available to students at the institution t...
2025-08-08 08:22:01,154 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:25.434360+00:00', 'data_returned': True}
2025-08-08 08:22:01,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 08:22:01,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 08:22:01,304 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.134s]
2025-08-08 08:22:01,305 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-08 08:22:01,305 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What payment plans or financial aid options are available to students at the institution t...
2025-08-08 08:22:01,305 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:25.434360+00:00', 'data_returned': True}
2025-08-08 08:22:01,305 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:22:01,305 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:59.753828+00:00', 'data_returned': True}
2025-08-08 08:22:01,305 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the total amount owed by students at the institution compare to previous years?
A...
2025-08-08 08:22:01,305 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:21:37.258531+00:00', 'data_returned': True, 'data_tag': 'total_amount_owed_by_year'}
2025-08-08 08:22:01,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 08:22:01,437 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 08:22:01,507 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.383s]
2025-08-08 08:22:01,508 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-08 08:22:01,508 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:22:01,508 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:59.753828+00:00', 'data_returned': True}
2025-08-08 08:22:01,509 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What payment plans or financial aid options are available to students at the institution t...
2025-08-08 08:22:01,509 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:25.434360+00:00', 'data_returned': True}
2025-08-08 08:22:01,509 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the total amount owed by students at the institution compare to previous years?
A...
2025-08-08 08:22:01,509 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:21:37.258531+00:00', 'data_returned': True, 'data_tag': 'total_amount_owed_by_year'}
2025-08-08 08:22:01,536 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.385s]
2025-08-08 08:22:01,537 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-08 08:22:01,537 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:22:01,537 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:59.753828+00:00', 'data_returned': True}
2025-08-08 08:22:01,537 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the total amount owed by students at the institution compare to previous years?
A...
2025-08-08 08:22:01,537 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:21:37.258531+00:00', 'data_returned': True, 'data_tag': 'total_amount_owed_by_year'}
2025-08-08 08:22:01,537 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What payment plans or financial aid options are available to students at the institution t...
2025-08-08 08:22:01,537 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:25.434360+00:00', 'data_returned': True}
2025-08-08 08:22:01,561 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.134s]
2025-08-08 08:22:01,562 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-08 08:22:01,562 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:22:01,562 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:59.753828+00:00', 'data_returned': True}
2025-08-08 08:22:01,562 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the total amount owed by students at the institution compare to previous years?
A...
2025-08-08 08:22:01,562 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:21:37.258531+00:00', 'data_returned': True, 'data_tag': 'total_amount_owed_by_year'}
2025-08-08 08:22:01,562 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What payment plans or financial aid options are available to students at the institution t...
2025-08-08 08:22:01,562 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:25.434360+00:00', 'data_returned': True}
2025-08-08 08:22:01,591 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 08:22:01,634 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.385s]
2025-08-08 08:22:01,635 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-08 08:22:01,635 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:22:01,635 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:59.753828+00:00', 'data_returned': True}
2025-08-08 08:22:01,635 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the total amount owed by students at the institution compare to previous years?
A...
2025-08-08 08:22:01,635 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:21:37.258531+00:00', 'data_returned': True, 'data_tag': 'total_amount_owed_by_year'}
2025-08-08 08:22:01,636 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What payment plans or financial aid options are available to students at the institution t...
2025-08-08 08:22:01,636 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:25.434360+00:00', 'data_returned': True}
2025-08-08 08:22:01,729 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.134s]
2025-08-08 08:22:01,730 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-08 08:22:01,730 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:22:01,730 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:59.753828+00:00', 'data_returned': True}
2025-08-08 08:22:01,730 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the total amount owed by students at the institution compare to previous years?
A...
2025-08-08 08:22:01,730 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:21:37.258531+00:00', 'data_returned': True, 'data_tag': 'total_amount_owed_by_year'}
2025-08-08 08:22:01,730 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What payment plans or financial aid options are available to students at the institution t...
2025-08-08 08:22:01,731 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:25.434360+00:00', 'data_returned': True}
2025-08-08 08:22:01,826 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.386s]
2025-08-08 08:22:01,827 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-08 08:22:01,827 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What payment plans or financial aid options are available to students at the institution t...
2025-08-08 08:22:01,827 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:25.434360+00:00', 'data_returned': True}
2025-08-08 08:22:01,827 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What specific fees are students owing at the institution where students owe the most fees?...
2025-08-08 08:22:01,827 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:20:59.753828+00:00', 'data_returned': True}
2025-08-08 08:22:01,827 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the total amount owed by students at the institution compare to previous years?
A...
2025-08-08 08:22:01,827 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'string', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T08:21:37.258531+00:00', 'data_returned': True, 'data_tag': 'total_amount_owed_by_year'}
2025-08-08 08:22:04,375 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:05,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:05,854 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:06,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:06,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:07,489 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:07,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:09,285 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:09,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:09,981 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 310 characters
2025-08-08 08:22:09,981 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 769 characters
2025-08-08 08:22:09,982 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1406 characters
2025-08-08 08:22:09,982 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 1799 characters
2025-08-08 08:22:09,982 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1829 characters
2025-08-08 08:22:09,982 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 1393 characters
2025-08-08 08:22:09,982 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 1555 characters
2025-08-08 08:22:09,982 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 1180 characters
2025-08-08 08:22:09,983 - REPORT_PIPELINE - INFO -   ✅ Section 9 completed: 1005 characters
2025-08-08 08:22:11,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:22:11,258 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - Replaced tag total_amount_owed_by_year with data object
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - Replaced tag total_amount_owed_by_year with data object
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - Replaced tag total_amount_owed_by_year with data object
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - Replaced tag total_amount_owed_by_year with data object
2025-08-08 08:22:11,259 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,260 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,260 - app.chains.composite.report_pipeline - INFO - Replaced tag total_amount_owed_by_year with data object
2025-08-08 08:22:11,260 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,260 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,260 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,260 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 08:22:11,260 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:22:11,260 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-08 08:22:11,260 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:22:11,260 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-08 08:22:11,260 - REPORT_PIPELINE - INFO - 📊 Final report sections: 20
2025-08-08 08:22:11,260 - REPORT_PIPELINE - INFO - 📈 Extended data items: 5
2025-08-08 08:22:11,260 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_081927.log
2025-08-08 08:22:11,269 - celery.app.trace - INFO - Task generate_report[07c8f1d6-038e-4ce1-a0df-b44bcab81ba8] succeeded in 164.0393960420006s: {'outline': '# Report Outline: Investigation of the Question \'string\'

## 1. Introduction  
   The purpose of this report is to investigate the concept of \'string\' across various contexts, including programming and linguistics. Understanding the definition, importance, and applications of strings is crucial for effective communication and data processing in technology and language.

## 2. Definition of \'String\'  
   - General Definition  
   - Contextual Definitions  
      - In Programming  
      - In Linguistics  

## 3. Importance of \'String\'  
   - Role in Communication  
   - Significance in Programming Languages  
   - Applications in Data Processing  

## 4. Types of Strings  
   - Literal Strings  
   - Variable Strings  
   - Multiline Strings  
   - String Interpolation  

## 5. String Manipulation  
   - Common Operations  
      - Concatenation  
      - Slicing  
   - String Functions in Various Languages  
   - Performance Considerations  

## 6. Case Studies  
   - Use of Strings in Software...', ...}
2025-08-08 08:23:03,449 - celery.worker.strategy - INFO - Task generate_streaming_report[fda7dcfc-845a-4fb8-a245-50a52c65c473] received
2025-08-08 08:23:03,450 - app.tasks.report_task - INFO - starting streaming report generation...
2025-08-08 08:23:03,450 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:23:03,450 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: e672617f-7898-4cd6-a7ee-2791c08c48f2
2025-08-08 08:23:03,451 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:23:03,451 - REPORT_REQUEST - INFO - 📝 Original Question: 'Which institution has the most students?'
2025-08-08 08:23:03,451 - REPORT_REQUEST - INFO - 🆔 Task ID: e672617f-7898-4cd6-a7ee-2791c08c48f2
2025-08-08 08:23:03,451 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T08:23:03.451280
2025-08-08 08:23:03,584 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 08:23:03,585 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 20
2025-08-08 08:23:03,713 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 08:23:03,714 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 9
2025-08-08 08:23:03,848 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.134s]
2025-08-08 08:23:03,848 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (3):
2025-08-08 08:23:03,849 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 08:23:03,849 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (7 docs)
2025-08-08 08:23:03,849 - ELASTICSEARCH_STATE - INFO -   - 'string' (5 docs)
2025-08-08 08:23:03,849 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:23:03,849 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 08:23:03,850 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:23:03,850 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: Which institution has the most students?
2025-08-08 08:23:03,850 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: True
2025-08-08 08:23:03,850 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 08:23:11,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 08:23:11,614 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 08:23:11,615 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 08:23:11,616 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-08 08:23:11,616 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 08:23:11,616 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: "Input to ChatPromptTemplate is missing variables {'question', 'schema_text'}.  Expected: ['question', 'schema_text'] Received: ['original_question', 'uninformed_outline']\nNote: if you intended {question} to be part of the string and not a variable, please escape it with double curly braces like: '{{question}}'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT "
2025-08-08 08:23:11,616 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_081927.log
2025-08-08 08:23:11,617 - celery.app.trace - INFO - Task generate_streaming_report[fda7dcfc-845a-4fb8-a245-50a52c65c473] succeeded in 8.167321958000684s: {'error': 'Error generating streaming report: "Input to ChatPromptTemplate is missing variables {\'question\', \'schema_text\'}.  Expected: [\'question\', \'schema_text\'] Received: [\'original_question\', \'uninformed_outline\']\nNote: if you intended {question} to be part of the string and not a variable, please escape it with double curly braces like: \'{{question}}\'.\nFor troubleshooting, visit: https://python.langchain.com/docs/troubleshooting/errors/INVALID_PROMPT_INPUT "'}

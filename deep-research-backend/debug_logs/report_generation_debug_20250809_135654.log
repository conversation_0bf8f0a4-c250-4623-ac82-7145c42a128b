2025-08-09 13:56:54,839 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250809_135654.log
2025-08-09 13:56:54,839 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:56:54,839 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 2f9b270b-f02c-42d9-a6ca-060a730a056c
2025-08-09 13:56:54,840 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:56:54,840 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are boys performing at ITC University'
2025-08-09 13:56:54,840 - REPORT_REQUEST - INFO - 🆔 Task ID: 2f9b270b-f02c-42d9-a6ca-060a730a056c
2025-08-09 13:56:54,840 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-09T13:56:54.840155
2025-08-09 13:56:54,992 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.152s]
2025-08-09 13:56:54,992 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-09 13:56:55,119 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-09 13:56:55,120 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-09 13:56:55,249 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:0.129s]
2025-08-09 13:56:55,249 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-09 13:56:55,249 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:56:55,249 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-09 13:56:55,249 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:56:55,250 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How are boys performing at ITC University
2025-08-09 13:56:55,250 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-09 13:56:55,250 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-09 13:57:05,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:05,274 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-09 13:57:08,859 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:08,874 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-09 13:57:14,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:14,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:14,837 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:14,877 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:14,995 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:15,188 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:15,210 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:15,413 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:15,425 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:15,828 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:15,891 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-09 13:57:18,216 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:18,247 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:18,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:20,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:20,906 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:21,559 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:22,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:22,283 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:24,079 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:26,435 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:26,940 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:30,936 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:30,943 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the academic subjects or courses where boys are excelling or struggling at ITC University?', 'sql': "SELECT c.title, ar.grade, COUNT(ar.id) AS student_count\nFROM core.courses c\nJOIN core.assessment_results ar ON c.id = ar.course_id\nJOIN core.students s ON ar.student_id = s.id\nWHERE s.sex = 'M'\nGROUP BY c.title, ar.grade\nORDER BY c.title, ar.grade;", 'correct': True, 'reasoning': "The SQL query correctly identifies the courses (academic subjects) by selecting the course title from the 'courses' table. It joins the 'assessment_results' table to get the grades for those courses and counts the number of students (who are boys, as specified by the condition 's.sex = 'M''). The grouping by course title and grade allows for a clear distinction of performance (excelling or struggling) based on the grades. The ordering by title and grade provides a structured output. Therefore, the query effectively answers the question regarding boys' performance in courses at ITC University.", 'feedback': "The question could be clarified by specifying what metrics define 'excelling' or 'struggling' (e.g., specific grade thresholds). Additionally, the SQL could be improved by including a filter for specific semesters or academic years if that information is relevant."}
2025-08-09 13:57:30,943 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:57:30,944 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 13:57:32,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:32,526 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:36,097 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:37,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:37,343 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the performance of boys at ITC University compare to that of girls in the same programs?', 'sql': "SELECT s.sex, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS number_of_students\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.assessment_results ar ON sp.id = ar.student_program_id\nWHERE s.sex IN ('M', 'F') AND s.institution_id = (SELECT id FROM core.institutions WHERE name = 'ITC University')\nGROUP BY s.sex;", 'correct': True, 'reasoning': 'The SQL query correctly retrieves the average final scores of male and female students at ITC University by joining the students, student programs, and assessment results tables. It filters the results to include only male and female students and groups the results by sex, which allows for a direct comparison of their performance. The use of AVG(ar.finalscore) provides the average score for each gender, and COUNT(ar.id) gives the number of students in each group, which is relevant for understanding the performance comparison.', 'feedback': 'The query is well-structured and answers the question effectively. However, it could be improved by explicitly stating the program or course in the SELECT statement to clarify that the comparison is within the same programs. Additionally, ensuring that the institution_id is correctly referenced from the institutions table is crucial for accuracy.'}
2025-08-09 13:57:37,343 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:57:37,344 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 13:57:37,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:38,654 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:38,978 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:39,855 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:40,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:40,604 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:40,911 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:42,914 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:43,038 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:44,208 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:44,223 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:45,971 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:45,992 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance between the subjects where boys excel and those where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between subjects for boys, which requires qualitative insights and potentially external data not present in the schema. The schema primarily contains structured data about students, courses, assessments, and related entities, but does not provide the necessary qualitative factors or insights into gender-specific performance differences.', 'feedback': ''}
2025-08-09 13:57:45,993 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:57:45,993 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:57:45,993 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance between the subjects where boys excel and those where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between subjects for boys, which requires qualitative insights and potentially external data not present in the schema. The schema primarily contains structured data about students, courses, assessments, and related entities, but does not provide the necessary qualitative factors or insights into gender-specific performance differences.', 'feedback': ''}
2025-08-09 13:57:48,183 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:48,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:50,028 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:50,618 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:50,636 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the observed difference in performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between boys and girls at ITC University. However, the provided schema does not contain specific data related to gender performance comparisons or any analytical framework to assess such differences. While there are tables related to students, their grades, and demographic information, there is no direct link or data that would allow for a comprehensive analysis of performance differences based on gender. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 13:57:50,636 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:57:50,636 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:57:50,636 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the observed difference in performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between boys and girls at ITC University. However, the provided schema does not contain specific data related to gender performance comparisons or any analytical framework to assess such differences. While there are tables related to students, their grades, and demographic information, there is no direct link or data that would allow for a comprehensive analysis of performance differences based on gender. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 13:57:50,768 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:50,792 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance between the subjects where boys excel and those where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between subjects for boys, which requires qualitative insights and potentially external data not present in the schema. The schema contains data about students, subjects, and grades, but it does not provide specific factors or qualitative insights that would explain performance differences. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-09 13:57:50,792 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:57:50,792 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:57:50,792 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance between the subjects where boys excel and those where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between subjects for boys, which requires qualitative insights and potentially external data not present in the schema. The schema contains data about students, subjects, and grades, but it does not provide specific factors or qualitative insights that would explain performance differences. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-09 13:57:52,996 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:53,165 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:53,364 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:55,277 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:55,302 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the observed difference in performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between boys and girls at ITC University. However, the provided schema does not contain specific data related to gender performance comparisons or any analytical framework to assess such differences. While there are tables related to students, their grades, and demographic information, there is no direct way to extract or analyze the performance differences based on gender from the schema alone. Additional data or context would be required to answer this question.', 'feedback': ''}
2025-08-09 13:57:55,302 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:57:55,302 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:57:55,302 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the observed difference in performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between boys and girls at ITC University. However, the provided schema does not contain specific data related to gender performance comparisons or any analytical framework to assess such differences. While there are tables related to students, their grades, and demographic information, there is no direct way to extract or analyze the performance differences based on gender from the schema alone. Additional data or context would be required to answer this question.', 'feedback': ''}
2025-08-09 13:57:55,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:55,938 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance between the subjects where boys excel and those where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between subjects for boys, which requires qualitative insights and potentially complex statistical analysis. The provided schema contains data about students, subjects, and their performance (e.g., grades, courses), but it does not include qualitative factors or insights that would explain why boys excel in some subjects and struggle in others. The schema lacks information on teaching methods, student engagement, socio-economic factors, or psychological aspects that could influence performance, making it impossible to answer the question completely.', 'feedback': ''}
2025-08-09 13:57:55,938 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:57:55,938 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:57:55,938 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance between the subjects where boys excel and those where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between subjects for boys, which requires qualitative insights and potentially complex statistical analysis. The provided schema contains data about students, subjects, and their performance (e.g., grades, courses), but it does not include qualitative factors or insights that would explain why boys excel in some subjects and struggle in others. The schema lacks information on teaching methods, student engagement, socio-economic factors, or psychological aspects that could influence performance, making it impossible to answer the question completely.', 'feedback': ''}
2025-08-09 13:57:56,358 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:57,551 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:57,739 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:57:58,141 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:00,468 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:00,492 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance between the subjects where boys excel and those where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between subjects for boys, which requires qualitative insights and potentially external data not present in the schema. The schema primarily contains structured data about students, courses, assessments, and related entities, but does not provide the necessary qualitative context or specific performance metrics that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-09 13:58:00,493 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:58:00,493 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:58:00,493 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance between the subjects where boys excel and those where they struggle?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between subjects for boys, which requires qualitative insights and potentially external data not present in the schema. The schema primarily contains structured data about students, courses, assessments, and related entities, but does not provide the necessary qualitative context or specific performance metrics that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-09 13:58:01,376 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:01,383 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the observed difference in performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between boys and girls at ITC University. However, the provided schema does not contain specific data related to gender-based performance analysis or any metrics that would allow for such an analysis. While there are tables related to students, their grades, and other attributes, there is no direct link or data that would enable a comprehensive answer to the question regarding the observed differences in performance based on gender.', 'feedback': ''}
2025-08-09 13:58:01,383 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:58:01,383 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:58:01,383 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the observed difference in performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between boys and girls at ITC University. However, the provided schema does not contain specific data related to gender-based performance analysis or any metrics that would allow for such an analysis. While there are tables related to students, their grades, and other attributes, there is no direct link or data that would enable a comprehensive answer to the question regarding the observed differences in performance based on gender.', 'feedback': ''}
2025-08-09 13:58:03,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:04,268 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:06,575 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:06,589 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the observed difference in performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between boys and girls at ITC University. However, the provided schema does not contain any specific data related to gender performance comparisons or any analytical framework to assess such differences. While there are tables related to students, grades, and courses, there is no direct reference to gender-specific performance metrics or factors influencing performance. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 13:58:06,589 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:58:06,590 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:58:06,590 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the observed difference in performance between boys and girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences between boys and girls at ITC University. However, the provided schema does not contain any specific data related to gender performance comparisons or any analytical framework to assess such differences. While there are tables related to students, grades, and courses, there is no direct reference to gender-specific performance metrics or factors influencing performance. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 13:58:08,523 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:11,836 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:16,177 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:17,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:22,455 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:28,140 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:31,028 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:34,262 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:35,400 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:39,589 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:46,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:46,084 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "Are there any trends in boys' performance over the past few academic years at ITC University?", 'sql': "SELECT academic_years.start_year, AVG(assessment_results.finalscore) AS average_score, COUNT(assessment_results.id) AS number_of_assessments\nFROM core.students\nJOIN core.assessment_results ON students.id = assessment_results.student_id\nJOIN core.academic_years ON assessment_results.semester_id = academic_years.id\nWHERE students.sex = 'M' AND students.institution_id = 'ITC University'\nGROUP BY academic_years.start_year\nORDER BY academic_years.start_year;", 'correct': True, 'reasoning': "The SQL query correctly identifies the average performance of male students (boys) at ITC University by calculating the average final score from the assessment results, grouped by academic year. It also counts the number of assessments to provide context for the average score. The query filters for male students and ensures that only data from ITC University is included, which aligns with the question's focus on trends in boys' performance over the past academic years.", 'feedback': 'The question could be clarified by specifying what kind of trends are of interest (e.g., increasing or decreasing scores, consistency in performance). Additionally, the SQL could be improved by including a time frame in the WHERE clause to limit the results to a specific range of academic years, if that is a requirement.'}
2025-08-09 13:58:46,084 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:58:46,084 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 13:58:48,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:49,896 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:51,194 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:53,717 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:56,278 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:58:56,292 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded performance data for boys at ITC University. However, the provided schema does not contain any specific data or attributes that would allow for an analysis of factors affecting performance data recording. The schema includes various tables related to institutions, students, courses, and assessments, but it lacks qualitative data or insights into the reasons behind the absence of performance records. Therefore, it is not possible to answer this question based on the schema.', 'feedback': ''}
2025-08-09 13:58:56,292 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:58:56,292 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:58:56,292 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded performance data for boys at ITC University. However, the provided schema does not contain any specific data or attributes that would allow for an analysis of factors affecting performance data recording. The schema includes various tables related to institutions, students, courses, and assessments, but it lacks qualitative data or insights into the reasons behind the absence of performance records. Therefore, it is not possible to answer this question based on the schema.', 'feedback': ''}
2025-08-09 13:58:59,246 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:59:01,417 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:59:01,433 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for boys at ITC University. However, the provided schema does not contain any specific data or attributes related to performance data, gender-specific performance metrics, or any contextual information that could help identify contributing factors. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks qualitative data or insights into performance issues.', 'feedback': ''}
2025-08-09 13:59:01,433 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:59:01,433 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:59:01,433 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for boys at ITC University. However, the provided schema does not contain any specific data or attributes related to performance data, gender-specific performance metrics, or any contextual information that could help identify contributing factors. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks qualitative data or insights into performance issues.', 'feedback': ''}
2025-08-09 13:59:03,729 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:59:06,455 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:59:06,483 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for boys at ITC University. However, the provided schema does not contain any specific data or attributes related to performance data, gender-specific performance analysis, or any contextual information about ITC University. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks the necessary data to analyze or identify factors affecting performance data specifically for boys at a particular institution.', 'feedback': ''}
2025-08-09 13:59:06,483 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:59:06,483 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:59:06,483 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for boys at ITC University. However, the provided schema does not contain any specific data or attributes related to performance data, gender-specific performance analysis, or any contextual information about ITC University. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks the necessary data to analyze or identify factors affecting performance data specifically for boys at a particular institution.', 'feedback': ''}
2025-08-09 13:59:09,034 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:59:11,364 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:59:11,383 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded performance data for boys at ITC University. However, the provided schema does not contain any specific data or attributes related to performance data, gender-specific performance analysis, or any contextual information that could help identify such factors. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks qualitative data or insights into performance issues or gender disparities.', 'feedback': ''}
2025-08-09 13:59:11,384 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 13:59:11,384 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 13:59:11,384 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded performance data for boys at ITC University. However, the provided schema does not contain any specific data or attributes related to performance data, gender-specific performance analysis, or any contextual information that could help identify such factors. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks qualitative data or insights into performance issues or gender disparities.', 'feedback': ''}
2025-08-09 13:59:11,385 - root - INFO - [{'sex': 'F', 'average_score': 68.72, 'number_of_students': 58}, {'sex': 'M', 'average_score': 54.86, 'number_of_students': 71}]
2025-08-09 13:59:11,385 - root - INFO - [{'title': 'Applied Mathematics In Clothing And Textiles', 'grade': 'IC', 'student_count': 3}, {'title': 'Applied Science In Clothing And Textiles', 'grade': 'IC', 'student_count': 3}, {'title': 'BANK FINANCIAL REPORTING AND ANALYSIS', 'grade': 'D', 'student_count': 1}, {'title': 'BANK STRATEGIC INFORMATION MANAGEMENT', 'grade': 'B', 'student_count': 1}, {'title': 'BASIC ACCOUNTING I', 'grade': 'IC', 'student_count': 1}, {'title': 'BASIC ACCOUNTING II', 'grade': 'C', 'student_count': 1}, {'title': 'Biological Science', 'grade': 'IC', 'student_count': 1}, {'title': 'BUSINESS COMMUNICATION SKILLS', 'grade': 'D', 'student_count': 1}, {'title': 'BUSINESS ETHICS AND CORPORATE SOCIAL RESPONSIBILITY', 'grade': 'E', 'student_count': 1}, {'title': 'BUSINESS LAW', 'grade': 'D', 'student_count': 1}, {'title': 'BUSINESS MATHEMATICS AND STATISTICS', 'grade': 'D', 'student_count': 1}, {'title': 'CORPORATE GOVERNANCE', 'grade': 'D', 'student_count': 1}, {'title': 'COST ACCOUNTING', 'grade': 'D', 'student_count': 1}, {'title': 'E-BANKING AND NETWORKING', 'grade': 'F', 'student_count': 1}, {'title': 'ECONOMY OF GHANA', 'grade': 'C+', 'student_count': 1}, {'title': 'Educational Research Methods, Assessment and Statistics', 'grade': 'A', 'student_count': 1}, {'title': 'ENTREPRENEURSHIP AND SMALL BUSINESS MANAGEMENT', 'grade': 'D', 'student_count': 1}, {'title': 'FINANCIAL INSTITUTIONS AND MARKETS', 'grade': 'C', 'student_count': 1}, {'title': 'FINANCIAL MATHEMATICS', 'grade': 'D+', 'student_count': 1}, {'title': 'FINANCIAL MATHEMATICS', 'grade': 'E', 'student_count': 1}, {'title': 'Fraud Money Laundering And International Banking', 'grade': 'D', 'student_count': 1}, {'title': 'FUNCTIONAL FRENCH', 'grade': 'D', 'student_count': 1}, {'title': 'Historical, Philosophical and Sociological Foundations of Education', 'grade': 'D+', 'student_count': 1}, {'title': 'Human', 'grade': 'IC', 'student_count': 1}, {'title': 'INDUSTRIAL INTERNSHIP', 'grade': 'A', 'student_count': 1}, {'title': 'Instructional Media And Technologies For Teaching And Learning', 'grade': 'C', 'student_count': 1}, {'title': 'INTERMEDIATE ACCOUNTING', 'grade': 'D', 'student_count': 1}, {'title': 'INTRODUCTION TO CLOTHING AND TEXTILES', 'grade': 'IC', 'student_count': 1}, {'title': 'INTRODUCTION TO CORPORATE BANKING AND FINANCE', 'grade': 'D', 'student_count': 1}, {'title': 'Introduction To Educational Technology', 'grade': 'IC', 'student_count': 3}, {'title': 'INTRODUCTION TO HOME ECONOMICS', 'grade': 'IC', 'student_count': 3}, {'title': 'Introduction To Human Resource Management', 'grade': 'D+', 'student_count': 1}, {'title': 'INTRODUCTION TO INFORMATION AND COMMUNICATION TECHNOLOGY', 'grade': 'D', 'student_count': 1}, {'title': 'Introduction To Information And Communication Technology', 'grade': 'IC', 'student_count': 2}, {'title': 'Introduction To Literature In English', 'grade': 'IC', 'student_count': 1}, {'title': 'INTRODUCTION TO PROCUREMENT MANAGEMENT', 'grade': 'D', 'student_count': 1}, {'title': 'LEADERSHIP DEVELOPMENT', 'grade': 'D+', 'student_count': 1}, {'title': 'LONG ESSAY/PROJECT WORK', 'grade': 'A', 'student_count': 1}, {'title': 'MANAGEMENT INFORMATION SYSTEMS', 'grade': 'A', 'student_count': 1}, {'title': 'MANAGERIAL ECONOMICS', 'grade': 'D', 'student_count': 1}, {'title': 'MANAGING FINANCIAL RISK', 'grade': 'B+', 'student_count': 1}, {'title': 'MICRO FINANCE AND RURAL BANKING', 'grade': 'C', 'student_count': 1}, {'title': 'MONETARY THEORY AND PRACTICE', 'grade': 'D', 'student_count': 1}, {'title': 'ORGANISATIONAL BEHAVIOUR', 'grade': 'E', 'student_count': 1}, {'title': 'PARTNERSHIP AND COMPANY LAW', 'grade': 'E', 'student_count': 1}, {'title': 'POST INTERNSHIP SEMINAR', 'grade': 'A', 'student_count': 1}, {'title': 'PRE-INDUSTRIAL INTERNSHIP SEMINAR', 'grade': 'D', 'student_count': 1}, {'title': 'PRINCIPLES AND PRACTICE OF TEACHER EDUCATION', 'grade': 'D+', 'student_count': 1}, {'title': 'PRINCIPLES OF BANKING', 'grade': 'C', 'student_count': 1}, {'title': 'PRINCIPLES OF BANKING', 'grade': 'E', 'student_count': 1}, {'title': 'PRINCIPLES OF MACRO ECONOMICS', 'grade': 'D', 'student_count': 1}, {'title': 'PRINCIPLES OF MANAGEMENT', 'grade': 'C', 'student_count': 1}, {'title': 'PRINCIPLES OF MARKETING', 'grade': 'D', 'student_count': 1}, {'title': 'PRINCIPLES OF MARKETING', 'grade': 'E', 'student_count': 1}, {'title': 'PRODUCTION AND OPERATIONS MANAGEMENT', 'grade': 'C', 'student_count': 1}, {'title': 'PROJECT MANAGEMENT', 'grade': 'D', 'student_count': 1}, {'title': 'PSYCHOLOGY OF HUMAN DEVELOPMENT AND LEARNING', 'grade': 'A', 'student_count': 1}, {'title': 'PUBLIC ADMINISTRATION', 'grade': 'E', 'student_count': 1}, {'title': 'QUANTITATIVE TECHNIQUES', 'grade': 'B', 'student_count': 1}, {'title': 'RESEARCH METHODS', 'grade': 'C+', 'student_count': 1}, {'title': 'STRATEGIC MANAGEMENT', 'grade': 'D', 'student_count': 1}, {'title': 'Teaching Assessing Mathematics For Upper Primary', 'grade': 'IC', 'student_count': 1}, {'title': 'TRENDS IN EDUCATION AND SCHOOL MANAGEMENT IN GHANA', 'grade': 'D', 'student_count': 1}, {'title': 'Written Literature Of A Ghanaian Language (dagaare)', 'grade': 'IC', 'student_count': 1}]
2025-08-09 13:59:11,385 - root - INFO - 'No results'
2025-08-09 13:59:11,385 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-09 13:59:18,509 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:59:18,514 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-09 13:59:28,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 13:59:28,866 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:59:28,866 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 13:59:28,866 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:59:28,866 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University'
2025-08-09 13:59:28,866 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 13:59:28,866 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 13:59:28,866 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 13:59:28,866 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 13:59:28,866 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the performance of boys at ITC University compare to that of girls in the same programs?...
2025-08-09 13:59:28,866 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, the performance of girls (F) in the programs is significantly better than that of...
2025-08-09 13:59:28,866 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: bar_chart
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': 'F', 'average_score': 68.72, 'number_of_students': 58}, {'sex': 'M', 'average_score': 54.86...
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_scores_by_sex_at_itc_university
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 13:59:28,867 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 13:59:28,867 - celery.redirected - WARNING - [{'sex': 'F', 'average_score': 68.72, 'number_of_students': 58}, {'sex': 'M', 'average_score': 54.86, 'number_of_students': 71}]
2025-08-09 13:59:28,867 - celery.redirected - WARNING - ================================= 
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: average_scores_by_sex_at_itc_university
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 13:59:28,867 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:59:28,867 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 13:59:28,867 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University'
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 13:59:28,867 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What are the academic subjects or courses where boys are excelling or struggling at ITC University?...
2025-08-09 13:59:28,867 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, boys are excelling in several academic subjects, particularly those where they re...
2025-08-09 13:59:28,868 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 13:59:28,868 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'title': 'Applied Mathematics In Clothing And Textiles', 'grade': 'IC', 'student_count': 3}, {'tit...
2025-08-09 13:59:28,868 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: boys_academic_performance_at_itc_university
2025-08-09 13:59:28,868 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 13:59:28,868 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 13:59:28,868 - celery.redirected - WARNING - [{'title': 'Applied Mathematics In Clothing And Textiles', 'grade': 'IC', 'student_count': 3}, {'title': 'Applied Science In Clothing And Textiles', 'grade': 'IC', 'student_count': 3}, {'title': 'BANK FINANCIAL REPORTING AND ANALYSIS', 'grade': 'D', 'student_count': 1}, {'title': 'BANK STRATEGIC INFORMATION MANAGEMENT', 'grade': 'B', 'student_count': 1}, {'title': 'BASIC ACCOUNTING I', 'grade': 'IC', 'student_count': 1}, {'title': 'BASIC ACCOUNTING II', 'grade': 'C', 'student_count': 1}, {'title': 'Biological Science', 'grade': 'IC', 'student_count': 1}, {'title': 'BUSINESS COMMUNICATION SKILLS', 'grade': 'D', 'student_count': 1}, {'title': 'BUSINESS ETHICS AND CORPORATE SOCIAL RESPONSIBILITY', 'grade': 'E', 'student_count': 1}, {'title': 'BUSINESS LAW', 'grade': 'D', 'student_count': 1}, {'title': 'BUSINESS MATHEMATICS AND STATISTICS', 'grade': 'D', 'student_count': 1}, {'title': 'CORPORATE GOVERNANCE', 'grade': 'D', 'student_count': 1}, {'title': 'COST ACCOUNTING', 'grade': 'D', 'student_count': 1}, {'title': 'E-BANKING AND NETWORKING', 'grade': 'F', 'student_count': 1}, {'title': 'ECONOMY OF GHANA', 'grade': 'C+', 'student_count': 1}, {'title': 'Educational Research Methods, Assessment and Statistics', 'grade': 'A', 'student_count': 1}, {'title': 'ENTREPRENEURSHIP AND SMALL BUSINESS MANAGEMENT', 'grade': 'D', 'student_count': 1}, {'title': 'FINANCIAL INSTITUTIONS AND MARKETS', 'grade': 'C', 'student_count': 1}, {'title': 'FINANCIAL MATHEMATICS', 'grade': 'D+', 'student_count': 1}, {'title': 'FINANCIAL MATHEMATICS', 'grade': 'E', 'student_count': 1}, {'title': 'Fraud Money Laundering And International Banking', 'grade': 'D', 'student_count': 1}, {'title': 'FUNCTIONAL FRENCH', 'grade': 'D', 'student_count': 1}, {'title': 'Historical, Philosophical and Sociological Foundations of Education', 'grade': 'D+', 'student_count': 1}, {'title': 'Human', 'grade': 'IC', 'student_count': 1}, {'title': 'INDUSTRIAL INTERNSHIP', 'grade': 'A', 'student_count': 1}, {'title': 'Instructional Media And Technologies For Teaching And Learning', 'grade': 'C', 'student_count': 1}, {'title': 'INTERMEDIATE ACCOUNTING', 'grade': 'D', 'student_count': 1}, {'title': 'INTRODUCTION TO CLOTHING AND TEXTILES', 'grade': 'IC', 'student_count': 1}, {'title': 'INTRODUCTION TO CORPORATE BANKING AND FINANCE', 'grade': 'D', 'student_count': 1}, {'title': 'Introduction To Educational Technology', 'grade': 'IC', 'student_count': 3}, {'title': 'INTRODUCTION TO HOME ECONOMICS', 'grade': 'IC', 'student_count': 3}, {'title': 'Introduction To Human Resource Management', 'grade': 'D+', 'student_count': 1}, {'title': 'INTRODUCTION TO INFORMATION AND COMMUNICATION TECHNOLOGY', 'grade': 'D', 'student_count': 1}, {'title': 'Introduction To Information And Communication Technology', 'grade': 'IC', 'student_count': 2}, {'title': 'Introduction To Literature In English', 'grade': 'IC', 'student_count': 1}, {'title': 'INTRODUCTION TO PROCUREMENT MANAGEMENT', 'grade': 'D', 'student_count': 1}, {'title': 'LEADERSHIP DEVELOPMENT', 'grade': 'D+', 'student_count': 1}, {'title': 'LONG ESSAY/PROJECT WORK', 'grade': 'A', 'student_count': 1}, {'title': 'MANAGEMENT INFORMATION SYSTEMS', 'grade': 'A', 'student_count': 1}, {'title': 'MANAGERIAL ECONOMICS', 'grade': 'D', 'student_count': 1}, {'title': 'MANAGING FINANCIAL RISK', 'grade': 'B+', 'student_count': 1}, {'title': 'MICRO FINANCE AND RURAL BANKING', 'grade': 'C', 'student_count': 1}, {'title': 'MONETARY THEORY AND PRACTICE', 'grade': 'D', 'student_count': 1}, {'title': 'ORGANISATIONAL BEHAVIOUR', 'grade': 'E', 'student_count': 1}, {'title': 'PARTNERSHIP AND COMPANY LAW', 'grade': 'E', 'student_count': 1}, {'title': 'POST INTERNSHIP SEMINAR', 'grade': 'A', 'student_count': 1}, {'title': 'PRE-INDUSTRIAL INTERNSHIP SEMINAR', 'grade': 'D', 'student_count': 1}, {'title': 'PRINCIPLES AND PRACTICE OF TEACHER EDUCATION', 'grade': 'D+', 'student_count': 1}, {'title': 'PRINCIPLES OF BANKING', 'grade': 'C', 'student_count': 1}, {'title': 'PRINCIPLES OF BANKING', 'grade': 'E', 'student_count': 1}, {'title': 'PRINCIPLES OF MACRO ECONOMICS', 'grade': 'D', 'student_count': 1}, {'title': 'PRINCIPLES OF MANAGEMENT', 'grade': 'C', 'student_count': 1}, {'title': 'PRINCIPLES OF MARKETING', 'grade': 'D', 'student_count': 1}, {'title': 'PRINCIPLES OF MARKETING', 'grade': 'E', 'student_count': 1}, {'title': 'PRODUCTION AND OPERATIONS MANAGEMENT', 'grade': 'C', 'student_count': 1}, {'title': 'PROJECT MANAGEMENT', 'grade': 'D', 'student_count': 1}, {'title': 'PSYCHOLOGY OF HUMAN DEVELOPMENT AND LEARNING', 'grade': 'A', 'student_count': 1}, {'title': 'PUBLIC ADMINISTRATION', 'grade': 'E', 'student_count': 1}, {'title': 'QUANTITATIVE TECHNIQUES', 'grade': 'B', 'student_count': 1}, {'title': 'RESEARCH METHODS', 'grade': 'C+', 'student_count': 1}, {'title': 'STRATEGIC MANAGEMENT', 'grade': 'D', 'student_count': 1}, {'title': 'Teaching Assessing Mathematics For Upper Primary', 'grade': 'IC', 'student_count': 1}, {'title': 'TRENDS IN EDUCATION AND SCHOOL MANAGEMENT IN GHANA', 'grade': 'D', 'student_count': 1}, {'title': 'Written Literature Of A Ghanaian Language (dagaare)', 'grade': 'IC', 'student_count': 1}]
2025-08-09 13:59:28,868 - celery.redirected - WARNING - ================================= 
2025-08-09 13:59:28,868 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 13:59:28,869 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:59:28,869 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 13:59:28,869 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are boys performing at ITC University'
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 13:59:28,869 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any trends in boys' performance over the past few academic years at ITC University?...
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Based on the data available, there are no recorded results regarding boys' performance at ITC Univer...
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: boys_performance_trends_itc_university
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 13:59:28,869 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 13:59:28,869 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 3
2025-08-09 13:59:28,870 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 13:59:28,870 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-09 13:59:28,870 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 3 documents
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO -     Content: Question: How does the performance of boys at ITC University compare to that of girls in the same pr...
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:58:06.590177+00:00', 'data_returned': True, 'data_tag': 'average_scores_by_sex_at_itc_university'}
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO -     Content: Question: What are the academic subjects or courses where boys are excelling or struggling at ITC Un...
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:58:00.493434+00:00', 'data_returned': True}
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO -     Content: Question: Are there any trends in boys' performance over the past few academic years at ITC Universi...
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are boys performing at ITC University', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T13:59:11.384666+00:00', 'data_returned': False}
2025-08-09 13:59:28,870 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 2/3
2025-08-09 13:59:29,100 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:200 duration:0.230s]
2025-08-09 13:59:30,950 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 14:01:31,071 - elastic_transport.transport - INFO - PUT http://54.246.247.31:9200/_bulk?refresh=true [status:N/A duration:120.000s]
2025-08-09 14:01:31,072 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://54.246.247.31:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-09 14:01:31,072 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: Connection timed out
2025-08-09 14:01:31,072 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:01:31,072 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-09 14:01:31,072 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:01:31,073 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: Connection timed out
2025-08-09 14:01:31,073 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250809_135654.log
2025-08-09 14:01:31,076 - celery.app.trace - INFO - Task generate_streaming_report[2b5066c7-108b-4a7a-8939-c78a544c1993] succeeded in 276.2433865410021s: {'error': 'Error generating streaming report: Connection timed out'}
2025-08-09 14:11:02,427 - celery.worker.strategy - INFO - Task generate_streaming_report[ec78fc47-e289-4c4c-bf69-d7fcdcb457c6] received
2025-08-09 14:11:02,428 - app.tasks.report_task - INFO - starting streaming report generation...
2025-08-09 14:11:02,429 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:11:02,429 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 705a01a8-282d-4329-879d-c3f1287b7920
2025-08-09 14:11:02,429 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:11:02,430 - REPORT_REQUEST - INFO - 📝 Original Question: 'Which institutions owe most fees'
2025-08-09 14:11:02,430 - REPORT_REQUEST - INFO - 🆔 Task ID: 705a01a8-282d-4329-879d-c3f1287b7920
2025-08-09 14:11:02,430 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-09T14:11:02.430971
2025-08-09 14:11:20,508 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:18.077s]
2025-08-09 14:11:20,510 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-09 14:12:38,007 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:77.497s]
2025-08-09 14:12:38,008 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-09 14:13:27,079 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:49.071s]
2025-08-09 14:13:27,080 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-09 14:13:27,080 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:13:27,080 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-09 14:13:27,080 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:13:27,080 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: Which institutions owe most fees
2025-08-09 14:13:27,081 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-09 14:13:27,081 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-09 14:13:35,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:35,043 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-09 14:13:39,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:39,750 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-09 14:13:45,513 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:45,695 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:46,127 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:46,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:46,314 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:47,399 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:47,418 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:47,431 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:47,445 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:48,484 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:48,545 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-09 14:13:52,885 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:52,886 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:52,915 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:53,070 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:53,110 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:53,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:53,556 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:55,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:56,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:56,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:56,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:57,317 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:57,749 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:57,904 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:57,912 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:58,325 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:58,346 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:59,022 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:59,023 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:13:59,387 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:02,034 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:02,512 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:03,945 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:04,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:05,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:05,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:07,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:08,041 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:08,046 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific programs or departments within the institution where students owe the most fees that have higher outstanding balances?', 'sql': 'SELECT p.long_name AS program_name, SUM(sb.balance) AS total_outstanding_balance\nFROM student_balances sb\nJOIN student_programs sp ON sb.student_id = sp.student_id\nJOIN programs p ON sp.program_id = p.id\nGROUP BY p.long_name\nORDER BY total_outstanding_balance DESC;', 'correct': True, 'reasoning': "The SQL query correctly identifies the programs by joining the 'student_balances', 'student_programs', and 'programs' tables. It sums the outstanding balances for each program and groups the results by program name. The ordering by total outstanding balance in descending order aligns with the question's request to find programs with the highest outstanding balances.", 'feedback': 'The query is well-structured and answers the question as intended. However, it could be improved by adding a WHERE clause to filter for only those programs with a significant outstanding balance, if a threshold is defined. Additionally, specifying the institution_id in the WHERE clause could ensure that the results are limited to a specific institution, if that context is necessary.'}
2025-08-09 14:14:08,046 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:08,046 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:14:08,277 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:09,383 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:10,504 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:10,509 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How many students are currently enrolled at the institution where students owe the most fees?', 'sql': "SELECT COUNT(DISTINCT s.id) AS enrolled_students\nFROM students s\nJOIN student_balances sb ON s.id = sb.student_id\nJOIN bills b ON sb.billing_period_id = b.billing_period_id\nJOIN institutions i ON s.institution_id = i.id\nWHERE s.status = 'active'\nAND sb.balance > 0\nAND i.id = (\n    SELECT sb2.institution_id\n    FROM student_balances sb2\n    GROUP BY sb2.institution_id\n    ORDER BY SUM(sb2.balance) DESC\n    LIMIT 1\n)", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total fees owed by students by using a subquery that sums the balances for each institution and orders them in descending order. It then counts the distinct active students who owe fees at that institution. The use of 'DISTINCT' ensures that each student is only counted once, and the conditions in the WHERE clause ensure that only currently enrolled students with a positive balance are considered.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating the criteria for 'currently enrolled' in the question to ensure clarity. Additionally, it might be beneficial to include a check for the billing period to ensure that the balances are relevant to the current academic term."}
2025-08-09 14:14:10,509 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:10,510 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:14:10,895 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:10,908 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What percentage of students at the institution where students owe the most fees are currently in arrears?', 'sql': 'WITH max_balance AS (SELECT institution_id, SUM(balance) AS total_balance FROM student_balances GROUP BY institution_id ORDER BY total_balance DESC LIMIT 1), total_students AS (SELECT COUNT(*) AS total FROM students WHERE institution_id = (SELECT institution_id FROM max_balance)), students_in_arrears AS (SELECT COUNT(*) AS in_arrears FROM student_balances sb JOIN students s ON sb.student_id = s.id WHERE sb.balance > 0 AND s.institution_id = (SELECT institution_id FROM max_balance)) SELECT (in_arrears * 100.0 / total) AS percentage_in_arrears FROM total_students, students_in_arrears;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total balance owed by students by summing the balances grouped by institution and ordering them in descending order. It then counts the total number of students in that institution and the number of students in arrears (those with a positive balance). Finally, it calculates the percentage of students in arrears relative to the total number of students at that institution. The logic follows the question's requirements accurately.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding comments to clarify each step of the query for better readability and maintainability.'}
2025-08-09 14:14:10,908 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:10,908 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:14:11,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:13,877 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:13,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:14,068 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:17,132 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:17,599 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:18,289 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:18,630 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:18,703 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:19,106 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:19,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:20,482 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:20,553 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:21,186 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:21,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:21,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:21,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:22,608 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:22,662 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:22,789 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:22,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:23,051 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:24,061 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:24,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:24,954 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:25,088 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:25,106 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What measures or policies has the institution implemented to ensure that students do not fall into arrears?', 'answerable': False, 'reasoning': 'The question asks about specific measures or policies implemented by the institution to prevent students from falling into arrears. However, the provided schema does not contain any tables or fields that explicitly describe policies or measures related to student arrears. While there are tables related to student transactions, billing, and financial aid, none of them provide direct information about institutional policies or measures. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:14:25,107 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:25,107 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:25,107 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What measures or policies has the institution implemented to ensure that students do not fall into arrears?', 'answerable': False, 'reasoning': 'The question asks about specific measures or policies implemented by the institution to prevent students from falling into arrears. However, the provided schema does not contain any tables or fields that explicitly describe policies or measures related to student arrears. While there are tables related to student transactions, billing, and financial aid, none of them provide direct information about institutional policies or measures. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:14:25,449 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:25,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:26,514 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:28,085 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:28,931 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:28,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:28,948 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide direct insights into the specific factors causing high fee debt or the correlation with student enrollment. The schema lacks qualitative data or contextual information that would be necessary to understand the reasons behind fee debt and its effects on enrollment. Therefore, the question cannot be answered completely from the schema.', 'feedback': ''}
2025-08-09 14:14:28,948 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:28,948 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:28,948 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide direct insights into the specific factors causing high fee debt or the correlation with student enrollment. The schema lacks qualitative data or contextual information that would be necessary to understand the reasons behind fee debt and its effects on enrollment. Therefore, the question cannot be answered completely from the schema.', 'feedback': ''}
2025-08-09 14:14:29,340 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:29,442 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:30,428 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:30,437 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total amount of fees owed by students at the institution where students owe the most fees?', 'sql': 'SELECT institution_id, SUM(balance) AS total_fees_owed\nFROM student_balances\nGROUP BY institution_id\nORDER BY total_fees_owed DESC\nLIMIT 1;', 'correct': True, 'reasoning': "The SQL query correctly aggregates the total balance (fees owed) for each institution by summing the 'balance' field in the 'student_balances' table. It groups the results by 'institution_id' and orders them in descending order based on the total fees owed. The use of 'LIMIT 1' ensures that only the institution with the highest total fees owed is returned, which directly answers the question.", 'feedback': 'The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to include a WHERE clause to filter for active students if the schema supports it, to ensure that only relevant balances are considered.'}
2025-08-09 14:14:30,437 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:30,437 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:14:30,633 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:30,658 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What measures or policies has the institution implemented to ensure that students do not fall into arrears?', 'answerable': False, 'reasoning': 'The question asks about specific measures or policies implemented by the institution to prevent students from falling into arrears. However, the provided schema does not contain any tables or fields that explicitly describe policies or measures related to student arrears. While there are tables related to student transactions, billing, and financial aid, none of them provide direct information about institutional policies or measures. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:14:30,658 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:30,658 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:30,659 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What measures or policies has the institution implemented to ensure that students do not fall into arrears?', 'answerable': False, 'reasoning': 'The question asks about specific measures or policies implemented by the institution to prevent students from falling into arrears. However, the provided schema does not contain any tables or fields that explicitly describe policies or measures related to student arrears. While there are tables related to student transactions, billing, and financial aid, none of them provide direct information about institutional policies or measures. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:14:30,821 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:31,783 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:32,045 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:32,063 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high outstanding balances in these specific education programs, and how might the institution address these issues?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high outstanding balances in specific education programs and potential solutions. While the schema contains tables related to student balances, transactions, and programs, it does not provide direct insights into the underlying factors causing these balances or specific strategies for addressing them. The schema lacks qualitative data or contextual information that would be necessary to answer such a question comprehensively.', 'feedback': ''}
2025-08-09 14:14:32,064 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:32,064 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:32,064 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high outstanding balances in these specific education programs, and how might the institution address these issues?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high outstanding balances in specific education programs and potential solutions. While the schema contains tables related to student balances, transactions, and programs, it does not provide direct insights into the underlying factors causing these balances or specific strategies for addressing them. The schema lacks qualitative data or contextual information that would be necessary to answer such a question comprehensively.', 'feedback': ''}
2025-08-09 14:14:33,230 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:33,306 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:33,947 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:33,999 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:34,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:34,063 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and enrollment, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to fee debt or the direct impact on enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:14:34,064 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:34,064 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:34,064 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and enrollment, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to fee debt or the direct impact on enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:14:34,567 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:34,614 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:35,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:35,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:35,814 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What measures or policies has the institution implemented to ensure that students do not fall into arrears?', 'answerable': False, 'reasoning': 'The question asks about specific measures or policies implemented by the institution to prevent students from falling into arrears. However, the provided schema does not contain any tables or fields that explicitly detail policies or measures related to student arrears. While there are tables related to student transactions, billing, and financial aid, none of them provide direct information about institutional policies or measures. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:14:35,814 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:35,814 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:35,814 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What measures or policies has the institution implemented to ensure that students do not fall into arrears?', 'answerable': False, 'reasoning': 'The question asks about specific measures or policies implemented by the institution to prevent students from falling into arrears. However, the provided schema does not contain any tables or fields that explicitly detail policies or measures related to student arrears. While there are tables related to student transactions, billing, and financial aid, none of them provide direct information about institutional policies or measures. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:14:36,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:36,174 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the payment history of students at the institution where students owe the most fees?', 'sql': 'WITH InstitutionBalances AS (  SELECT institution_id, SUM(balance) AS total_balance  FROM student_balances  GROUP BY institution_id), MaxInstitution AS (  SELECT institution_id  FROM InstitutionBalances  ORDER BY total_balance DESC  LIMIT 1) SELECT st.id AS student_id, st.surname, st.othernames, st.email, st.mobile, st.sex, st.status, st.created_at, st.updated_at, stt.transaction_date, stt.transaction_amount, stt.transaction_description, stt.type  FROM students st  JOIN student_transactions stt ON st.id = stt.student_id  WHERE st.institution_id = (SELECT institution_id FROM MaxInstitution) ORDER BY stt.transaction_date;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total balance owed by students by summing the balances in the 'student_balances' table and selecting the institution with the maximum balance. It then retrieves the payment history of students associated with that institution by joining the 'students' and 'student_transactions' tables. The query includes relevant student details and transaction information, which aligns with the question's request for payment history.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating that the balance represents fees owed, as the term 'balance' could be interpreted in different ways. Additionally, including a filter for only relevant transaction types (e.g., payments) might provide a clearer picture of the payment history."}
2025-08-09 14:14:36,175 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:36,175 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:14:36,292 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:36,917 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:37,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:37,134 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high outstanding balances in these specific education programs, and how might the institution address these issues?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high outstanding balances in specific education programs and potential solutions. While the schema contains tables related to student balances, transactions, and programs, it does not provide direct insights into the underlying factors causing these balances or specific strategies for addressing them. The schema lacks qualitative data or contextual information that would be necessary to answer such a question comprehensively.', 'feedback': ''}
2025-08-09 14:14:37,135 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:37,135 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:37,135 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high outstanding balances in these specific education programs, and how might the institution address these issues?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high outstanding balances in specific education programs and potential solutions. While the schema contains tables related to student balances, transactions, and programs, it does not provide direct insights into the underlying factors causing these balances or specific strategies for addressing them. The schema lacks qualitative data or contextual information that would be necessary to answer such a question comprehensively.', 'feedback': ''}
2025-08-09 14:14:37,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:38,356 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:38,450 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:38,918 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:38,934 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide direct insights into the specific factors causing high fee debt or how these factors affect student enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:14:38,935 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:38,935 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:38,935 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide direct insights into the specific factors causing high fee debt or how these factors affect student enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:14:39,750 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:40,281 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:41,435 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:41,454 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What measures or policies has the institution implemented to ensure that students do not fall into arrears?', 'answerable': False, 'reasoning': 'The question asks about specific measures or policies implemented by the institution to prevent students from falling into arrears. However, the provided schema does not contain any tables or fields that explicitly detail policies or measures related to student arrears. While there are tables related to student transactions, billing, and financial aid, none of them provide direct information about institutional policies or measures. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:14:41,454 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:41,454 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:41,454 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What measures or policies has the institution implemented to ensure that students do not fall into arrears?', 'answerable': False, 'reasoning': 'The question asks about specific measures or policies implemented by the institution to prevent students from falling into arrears. However, the provided schema does not contain any tables or fields that explicitly detail policies or measures related to student arrears. While there are tables related to student transactions, billing, and financial aid, none of them provide direct information about institutional policies or measures. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:14:41,505 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:41,527 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative balance in their fee accounts?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative balance in their fee accounts. While the schema contains tables related to student transactions, balances, and billing periods, it does not provide specific information about the factors or reasons behind overpayments or credits. The schema lacks detailed descriptions or attributes that would explain the causes of these financial situations, such as policies, procedures, or specific transaction types that could lead to overpayments or negative balances.', 'feedback': ''}
2025-08-09 14:14:41,527 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:41,527 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:41,527 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative balance in their fee accounts?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative balance in their fee accounts. While the schema contains tables related to student transactions, balances, and billing periods, it does not provide specific information about the factors or reasons behind overpayments or credits. The schema lacks detailed descriptions or attributes that would explain the causes of these financial situations, such as policies, procedures, or specific transaction types that could lead to overpayments or negative balances.', 'feedback': ''}
2025-08-09 14:14:41,559 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:41,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:42,054 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:42,473 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:42,493 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high outstanding balances in these specific education programs, and how might the institution address these issues?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high outstanding balances in specific education programs and potential solutions. While the schema contains tables related to student balances, transactions, and programs, it does not provide direct insights into the underlying factors causing these outstanding balances or specific strategies for addressing them. The schema lacks qualitative data or contextual information that would be necessary to answer such a question comprehensively.', 'feedback': ''}
2025-08-09 14:14:42,494 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:42,494 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:42,494 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high outstanding balances in these specific education programs, and how might the institution address these issues?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high outstanding balances in specific education programs and potential solutions. While the schema contains tables related to student balances, transactions, and programs, it does not provide direct insights into the underlying factors causing these outstanding balances or specific strategies for addressing them. The schema lacks qualitative data or contextual information that would be necessary to answer such a question comprehensively.', 'feedback': ''}
2025-08-09 14:14:42,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:43,379 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:43,811 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:44,495 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:44,509 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and enrollment, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to fee debt or its direct impact on enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:14:44,510 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:44,510 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:44,510 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and enrollment, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to fee debt or its direct impact on enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:14:44,904 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:44,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:45,018 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:47,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:47,466 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:47,486 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high outstanding balances in these specific education programs, and how might the institution address these issues?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high outstanding balances in specific education programs and potential solutions. While the schema contains tables related to student balances, transactions, and programs, it does not provide direct insights into the underlying factors causing these balances or strategies for addressing them. The schema lacks qualitative data or contextual information that would be necessary to answer such a question comprehensively.', 'feedback': ''}
2025-08-09 14:14:47,486 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:47,487 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:47,487 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high outstanding balances in these specific education programs, and how might the institution address these issues?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high outstanding balances in specific education programs and potential solutions. While the schema contains tables related to student balances, transactions, and programs, it does not provide direct insights into the underlying factors causing these balances or strategies for addressing them. The schema lacks qualitative data or contextual information that would be necessary to answer such a question comprehensively.', 'feedback': ''}
2025-08-09 14:14:47,976 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:47,977 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:47,993 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative balance in their fee accounts?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative balance in their fee accounts. While the schema contains tables related to student transactions, balances, and billing, it does not provide specific information about the underlying factors or reasons for overpayments or credits. The schema lacks detailed descriptions or attributes that would allow for a comprehensive analysis of the causes of negative balances. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:14:47,994 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:47,997 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:47,997 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative balance in their fee accounts?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative balance in their fee accounts. While the schema contains tables related to student transactions, balances, and billing, it does not provide specific information about the underlying factors or reasons for overpayments or credits. The schema lacks detailed descriptions or attributes that would allow for a comprehensive analysis of the causes of negative balances. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:14:48,506 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:50,742 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:51,004 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:51,026 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available payment history data for students at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available payment history data for students, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema provides tables related to student transactions, payments, and financial records, but it does not contain any explicit information or attributes that would indicate reasons or factors for the absence of payment history data. To answer this question, one would need insights into operational issues, data integrity, or other contextual factors that are not represented in the schema.', 'feedback': ''}
2025-08-09 14:14:51,026 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:51,026 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:51,026 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available payment history data for students at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available payment history data for students, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema provides tables related to student transactions, payments, and financial records, but it does not contain any explicit information or attributes that would indicate reasons or factors for the absence of payment history data. To answer this question, one would need insights into operational issues, data integrity, or other contextual factors that are not represented in the schema.', 'feedback': ''}
2025-08-09 14:14:53,106 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:53,281 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:53,471 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:53,493 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative balance in their fee accounts?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative balance in their fee accounts. While the schema contains tables related to student transactions, billing periods, and balances, it does not provide specific information about the factors or reasons behind overpayments or credits. The schema lacks detailed descriptions or attributes that would allow for a comprehensive analysis of the causes of negative balances. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:14:53,493 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:53,493 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:53,493 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative balance in their fee accounts?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative balance in their fee accounts. While the schema contains tables related to student transactions, billing periods, and balances, it does not provide specific information about the factors or reasons behind overpayments or credits. The schema lacks detailed descriptions or attributes that would allow for a comprehensive analysis of the causes of negative balances. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:14:53,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:56,067 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:56,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:56,634 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available payment history data for students at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available payment history data for students at the institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of payment history data. The schema includes tables related to students, transactions, and payments, but it does not provide insights into the reasons for missing data or the conditions that might lead to such a situation. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 14:14:56,634 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:56,634 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:56,634 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available payment history data for students at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available payment history data for students at the institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of payment history data. The schema includes tables related to students, transactions, and payments, but it does not provide insights into the reasons for missing data or the conditions that might lead to such a situation. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 14:14:56,845 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:56,853 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:58,750 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:14:58,765 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative balance in their fee accounts?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative balance in their fee accounts. While the schema contains tables related to student transactions, balances, and billing, it does not provide specific information about the factors or reasons behind overpayments or credits. The schema lacks detailed descriptions or attributes that would explain the causes of these financial situations, such as policies, procedures, or specific transaction types that could lead to overpayments or negative balances.', 'feedback': ''}
2025-08-09 14:14:58,765 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:14:58,765 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:14:58,765 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative balance in their fee accounts?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative balance in their fee accounts. While the schema contains tables related to student transactions, balances, and billing, it does not provide specific information about the factors or reasons behind overpayments or credits. The schema lacks detailed descriptions or attributes that would explain the causes of these financial situations, such as policies, procedures, or specific transaction types that could lead to overpayments or negative balances.', 'feedback': ''}
2025-08-09 14:14:59,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:00,251 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:00,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:01,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:01,759 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:02,171 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:02,192 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available payment history data for students at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available payment history data for students, which is a qualitative inquiry rather than a quantitative one. The schema provides tables related to student transactions, payments, and financial records, but it does not contain explicit information about the reasons or factors that might lead to a lack of data. To answer this question, one would need insights into operational issues, data management practices, or other contextual factors that are not represented in the schema.', 'feedback': ''}
2025-08-09 14:15:02,192 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:15:02,192 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:15:02,192 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available payment history data for students at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available payment history data for students, which is a qualitative inquiry rather than a quantitative one. The schema provides tables related to student transactions, payments, and financial records, but it does not contain explicit information about the reasons or factors that might lead to a lack of data. To answer this question, one would need insights into operational issues, data management practices, or other contextual factors that are not represented in the schema.', 'feedback': ''}
2025-08-09 14:15:04,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:05,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:07,216 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:08,046 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:08,060 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available payment history data for students at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available payment history data for students, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema provides tables related to student transactions, payments, and financial records, but it does not contain explicit information about the reasons or factors that might lead to a lack of data. To answer this question, one would need insights into operational issues, data integrity problems, or other contextual factors that are not represented in the schema.', 'feedback': ''}
2025-08-09 14:15:08,060 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:15:08,060 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:15:08,060 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available payment history data for students at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available payment history data for students, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema provides tables related to student transactions, payments, and financial records, but it does not contain explicit information about the reasons or factors that might lead to a lack of data. To answer this question, one would need insights into operational issues, data integrity problems, or other contextual factors that are not represented in the schema.', 'feedback': ''}
2025-08-09 14:15:10,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:10,292 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What types of fees are included in the total amount owed at the institution where students owe the most fees?', 'sql': 'SELECT DISTINCT bit.name\nFROM bills b\nJOIN bill_items bi ON b.id = bi.bill_id\nJOIN bill_item_types bit ON bi.bill_item_type_id = bit.id\nWHERE b.institution_id = (\n    SELECT institution_id\n    FROM bills\n    GROUP BY institution_id\n    ORDER BY SUM(total_due) DESC\n    LIMIT 1\n)', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution where students owe the most fees by using a subquery to find the institution_id with the highest total_due. It then joins the bills, bill_items, and bill_item_types tables to retrieve the distinct names of the fee types associated with that institution. This aligns with the question's requirement to find the types of fees included in the total amount owed at the specified institution.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be beneficial to add a comment or explanation in the SQL code to clarify the purpose of the subquery for future reference.'}
2025-08-09 14:15:10,292 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:15:10,292 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:15:14,296 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:15,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:15,297 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:16,459 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:18,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:19,139 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:22,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:22,087 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do these fees compare to those at other institutions, and what factors contribute to the higher fee structure at this particular institution?', 'answerable': False, 'reasoning': "The question asks for a comparison of fees between institutions and the factors contributing to a higher fee structure at a specific institution. However, the provided schema does not contain any direct information about fee structures or comparisons between different institutions. While there are tables related to billing, such as 'bills', 'bill_categories', and 'billing_periods', they do not provide a comprehensive view of fees across multiple institutions or the factors influencing fee differences. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-09 14:15:22,087 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:15:22,087 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:15:22,087 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do these fees compare to those at other institutions, and what factors contribute to the higher fee structure at this particular institution?', 'answerable': False, 'reasoning': "The question asks for a comparison of fees between institutions and the factors contributing to a higher fee structure at a specific institution. However, the provided schema does not contain any direct information about fee structures or comparisons between different institutions. While there are tables related to billing, such as 'bills', 'bill_categories', and 'billing_periods', they do not provide a comprehensive view of fees across multiple institutions or the factors influencing fee differences. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-09 14:15:22,997 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:24,321 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:24,856 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:27,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:27,463 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do these fees compare to those at other institutions, and what factors contribute to the higher fee structure at this particular institution?', 'answerable': False, 'reasoning': "The question asks for a comparison of fees between institutions and the factors contributing to a higher fee structure at a specific institution. However, the provided schema does not contain any direct information about fee structures or comparisons between different institutions. While there are tables related to billing and financial transactions (e.g., 'bills', 'student_bills', 'billing_periods'), there is no data that allows for a comparative analysis across institutions or insights into the factors influencing fee structures. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-09 14:15:27,463 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:15:27,463 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:15:27,463 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do these fees compare to those at other institutions, and what factors contribute to the higher fee structure at this particular institution?', 'answerable': False, 'reasoning': "The question asks for a comparison of fees between institutions and the factors contributing to a higher fee structure at a specific institution. However, the provided schema does not contain any direct information about fee structures or comparisons between different institutions. While there are tables related to billing and financial transactions (e.g., 'bills', 'student_bills', 'billing_periods'), there is no data that allows for a comparative analysis across institutions or insights into the factors influencing fee structures. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-09 14:15:28,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:30,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:32,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:32,738 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do these fees compare to those at other institutions, and what factors contribute to the higher fee structure at this particular institution?', 'answerable': False, 'reasoning': "The question asks for a comparison of fees between institutions and the factors contributing to a higher fee structure at a specific institution. The schema does not provide direct information about fees at different institutions or the factors influencing fee structures. While there are tables related to institutions and billing (e.g., 'bills', 'billing_periods', 'bill_categories'), there is no comprehensive data that allows for a comparative analysis of fees across institutions or insights into the reasons behind fee differences.", 'feedback': ''}
2025-08-09 14:15:32,738 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:15:32,738 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:15:32,738 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do these fees compare to those at other institutions, and what factors contribute to the higher fee structure at this particular institution?', 'answerable': False, 'reasoning': "The question asks for a comparison of fees between institutions and the factors contributing to a higher fee structure at a specific institution. The schema does not provide direct information about fees at different institutions or the factors influencing fee structures. While there are tables related to institutions and billing (e.g., 'bills', 'billing_periods', 'bill_categories'), there is no comprehensive data that allows for a comparative analysis of fees across institutions or insights into the reasons behind fee differences.", 'feedback': ''}
2025-08-09 14:15:35,005 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:35,010 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average fee amount owed per student at the institution where students owe the most fees?', 'sql': 'SELECT AVG(total_due) AS average_fee_per_student\nFROM student_bills sb\nJOIN bills b ON sb.bill_id = b.id\nWHERE sb.institution_id = (\n    SELECT institution_id\n    FROM student_bills sb2\n    JOIN bills b2 ON sb2.bill_id = b2.id\n    GROUP BY sb2.institution_id\n    ORDER BY SUM(b2.total_due) DESC\n    LIMIT 1\n)', 'correct': False, 'reasoning': "The SQL query attempts to find the average fee amount owed per student at the institution where students owe the most fees. However, it calculates the average of 'total_due' directly from the 'student_bills' table, which does not account for the number of students associated with that institution. To accurately compute the average fee per student, the query should divide the total fees owed by the number of students at that institution. The current query only calculates the average of total fees without considering the student count.", 'feedback': "To improve the SQL query, you should include a count of students in the calculation of the average fee. This can be done by joining the 'students' table to count the number of students at the institution and then dividing the total fees by this count.", 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-09 14:15:35,010 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:15:35,010 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-09 14:15:35,544 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:37,816 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:37,833 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do these fees compare to those at other institutions, and what factors contribute to the higher fee structure at this particular institution?', 'answerable': False, 'reasoning': 'The question asks for a comparison of fees between institutions and the factors contributing to a higher fee structure at a specific institution. However, the provided schema does not contain any direct information about fee structures or comparisons between different institutions. While there are tables related to billing, bills, and financial aid, there is no explicit data that allows for a comparative analysis of fees across institutions or the factors influencing fee structures. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:15:37,834 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:15:37,834 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:15:37,834 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do these fees compare to those at other institutions, and what factors contribute to the higher fee structure at this particular institution?', 'answerable': False, 'reasoning': 'The question asks for a comparison of fees between institutions and the factors contributing to a higher fee structure at a specific institution. However, the provided schema does not contain any direct information about fee structures or comparisons between different institutions. While there are tables related to billing, bills, and financial aid, there is no explicit data that allows for a comparative analysis of fees across institutions or the factors influencing fee structures. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:15:37,966 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:40,660 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:42,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:46,869 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:52,180 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:15:56,232 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:00,295 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:02,004 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:05,968 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:10,407 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:10,412 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average fee amount owed per student at the institution where students owe the most fees?', 'sql': 'SELECT AVG(balance) AS average_fee_per_student\nFROM student_balances\nWHERE institution_id = (\n    SELECT institution_id\n    FROM student_balances\n    GROUP BY institution_id\n    ORDER BY SUM(balance) DESC\n    LIMIT 1\n)', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution where students owe the most fees by summing the balances grouped by institution_id and ordering them in descending order. It then limits the result to the top institution. The outer query calculates the average balance (fee amount owed) for students at that institution. This aligns perfectly with the question asked.', 'feedback': 'The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to add comments in the SQL for clarity, especially in complex queries.'}
2025-08-09 14:16:10,413 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:16:10,413 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:16:12,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:13,892 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:15,116 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:17,779 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:20,259 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:20,276 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. This requires qualitative insights and possibly a combination of various data points, such as student transactions, billing periods, and financial aid requests. However, the schema does not provide direct insights into the qualitative aspects of student financial experiences or the specific factors leading to credit balances. It primarily contains structured data without the necessary context or analytical framework to derive such conclusions.', 'feedback': ''}
2025-08-09 14:16:20,277 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:16:20,277 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:16:20,277 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. This requires qualitative insights and possibly a combination of various data points, such as student transactions, billing periods, and financial aid requests. However, the schema does not provide direct insights into the qualitative aspects of student financial experiences or the specific factors leading to credit balances. It primarily contains structured data without the necessary context or analytical framework to derive such conclusions.', 'feedback': ''}
2025-08-09 14:16:22,677 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:26,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:26,301 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their financial experience. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or how these balances affect students' financial experiences, making it impossible to answer the question completely.", 'feedback': ''}
2025-08-09 14:16:26,302 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:16:26,302 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:16:26,302 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their financial experience. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or how these balances affect students' financial experiences, making it impossible to answer the question completely.", 'feedback': ''}
2025-08-09 14:16:28,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:32,279 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:32,303 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their financial experience. The schema provides various tables related to students, transactions, and financial records, but it does not contain specific analytical data or insights into the reasons behind credit balances or their effects on students' financial experiences. The schema lacks qualitative data or metrics that would allow for a comprehensive answer to this question.", 'feedback': ''}
2025-08-09 14:16:32,303 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:16:32,303 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:16:32,303 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their financial experience. The schema provides various tables related to students, transactions, and financial records, but it does not contain specific analytical data or insights into the reasons behind credit balances or their effects on students' financial experiences. The schema lacks qualitative data or metrics that would allow for a comprehensive answer to this question.", 'feedback': ''}
2025-08-09 14:16:32,304 - root - INFO - [{'institution_id': 1, 'total_fees_owed': -2600.0}]
2025-08-09 14:16:32,304 - root - INFO - [{'enrolled_students': 0}]
2025-08-09 14:16:32,304 - root - INFO - [{'name': 'Tuition Fee'}, {'name': 'Examination Fee'}, {'name': 'Sports Fee'}, {'name': 'Student Council Dues'}, {'name': 'Hall Affiliation Fee'}, {'name': 'Entertainment Fee'}, {'name': 'HOSTEL FEES'}, {'name': 'Academic Facility User Fee'}, {'name': 'Endowment Fund'}, {'name': 'Health Insurance'}, {'name': 'SRC Dues'}, {'name': 'School fees'}, {'name': 'SRC duties'}, {'name': "Lecturers'  Appreciation"}, {'name': 'Test'}, {'name': 'Test4'}, {'name': 'TUITION'}, {'name': 'JCRC'}, {'name': 'Environmental Sanitation Fee'}, {'name': 'Medical exams fees'}, {'name': 'Health Levy'}, {'name': 'ICT FACILITY USER FEE'}, {'name': 'Sport Fees'}, {'name': 'ID Card'}, {'name': 'Furniture Levy'}, {'name': 'ICT Facilities User Fee'}, {'name': 'Development Levy'}, {'name': 'Online Teaching Service'}, {'name': 'Trip Fees'}, {'name': 'Environmental and Sanitation Fee'}, {'name': 'Library'}]
2025-08-09 14:16:32,304 - root - INFO - [{'average_fee_per_student': -1300.0}]
2025-08-09 14:16:32,305 - root - INFO - [{'program_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'total_outstanding_balance': 1434460.78}, {'program_name': 'Diploma In Education', 'total_outstanding_balance': 590428.2}, {'program_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'total_outstanding_balance': 572085.04}, {'program_name': 'BACHELOR OF EDUCATION (EARLY GRADE)', 'total_outstanding_balance': 476761.38}, {'program_name': 'Master Of Arts In Educational Leadership', 'total_outstanding_balance': 402376.2}, {'program_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'total_outstanding_balance': 337275.0}, {'program_name': 'BACHELOR OF ARTS (SOCIAL STUDIES EDUCATION)', 'total_outstanding_balance': 233099.61}, {'program_name': 'Post Diploma Bachelor Of Science (Environmental Health And Sanitation Education)', 'total_outstanding_balance': 181249.0}, {'program_name': 'Master Of Technology Education In Catering And Hospitality', 'total_outstanding_balance': 165740.0}, {'program_name': 'Diploma In Education - KS', 'total_outstanding_balance': 154070.0}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 123673.79}, {'program_name': 'BACHELOR OF ARTS (EWE EDUCATION)', 'total_outstanding_balance': 116490.0}, {'program_name': 'DIPLOMA IN EARLY GRADE', 'total_outstanding_balance': 108654.99}, {'program_name': 'MASTER OF PHILOSOPHY (GHANAIAN LANGUAGE STUDIES)', 'total_outstanding_balance': 107927.0}, {'program_name': 'DIPLOMA (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'total_outstanding_balance': 104546.0}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - S', 'total_outstanding_balance': 103221.81}, {'program_name': 'MASTER OF PHILOSOPHY (SCIENCE EDUCATION)', 'total_outstanding_balance': 103079.5}, {'program_name': 'DIPLOMA IN BASIC EDUCATION', 'total_outstanding_balance': 86068.75}, {'program_name': 'BACHELOR OF ARTS (ENGLISH EDUCATION)', 'total_outstanding_balance': 84409.81}, {'program_name': 'DIPLOMA (ART)', 'total_outstanding_balance': 78452.1}, {'program_name': 'Master Of Science In Information Technology Education', 'total_outstanding_balance': 76074.0}, {'program_name': 'Diploma In Environmental Health And Sanitation Education', 'total_outstanding_balance': 70189.0}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'total_outstanding_balance': 69785.0}, {'program_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'total_outstanding_balance': 68944.3}, {'program_name': 'Master Of Technology Education In Fashion Design And Textile', 'total_outstanding_balance': 65240.0}, {'program_name': 'Master Of Education In Mathematics Education', 'total_outstanding_balance': 64080.0}, {'program_name': 'Master Of Philosophy In Public Health', 'total_outstanding_balance': 63934.2}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'total_outstanding_balance': 63295.0}, {'program_name': 'Master Of Technology In Construction Technology', 'total_outstanding_balance': 61860.0}, {'program_name': 'Master Of Technology In Construction Management', 'total_outstanding_balance': 58918.0}, {'program_name': 'Bachelor Of Science In Occupational Health And Safety', 'total_outstanding_balance': 57154.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'total_outstanding_balance': 55496.0}, {'program_name': 'BACHELOR OF ART (SISAALI WITH ENGLISH EDUCATION)', 'total_outstanding_balance': 55280.0}, {'program_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'total_outstanding_balance': 55074.68}, {'program_name': 'Doctor Of Philosophy In Construction Management', 'total_outstanding_balance': 53522.0}, {'program_name': 'MASTER OF PHILOSOPHY (EARLY CHILDHOOD EDUCATION)', 'total_outstanding_balance': 51836.0}, {'program_name': 'MASTER OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'total_outstanding_balance': 48497.0}, {'program_name': 'Master Of Technology In Mechanical Technology', 'total_outstanding_balance': 47950.0}, {'program_name': 'Master Of Education In Agriculture', 'total_outstanding_balance': 45710.0}, {'program_name': 'BACHELOR OF ARTS (FANTE EDUCATION)', 'total_outstanding_balance': 42689.0}, {'program_name': 'Master Of Philosophy In Information Technology - W', 'total_outstanding_balance': 41284.0}, {'program_name': 'MASTER OF PHILOSOPHY (ENGLISH LANGUAGE)', 'total_outstanding_balance': 39948.0}, {'program_name': 'MASTER OF EDUCATION (MATHEMATICS)', 'total_outstanding_balance': 39644.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (SCIENCE EDUCATION)', 'total_outstanding_balance': 38728.0}, {'program_name': 'Master Of Philosophy In Information Technology', 'total_outstanding_balance': 38680.0}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'total_outstanding_balance': 38347.34}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology Education) - S', 'total_outstanding_balance': 36613.3}, {'program_name': 'MASTER OF PHILOSOPHY (ART EDUCATION)', 'total_outstanding_balance': 36430.0}, {'program_name': 'Master Of Philosophy In Crop Science', 'total_outstanding_balance': 35086.0}, {'program_name': 'Master Of Technology In Wood Technology', 'total_outstanding_balance': 34570.0}, {'program_name': 'Diploma In Education - TM', 'total_outstanding_balance': 33850.0}, {'program_name': 'Post Diploma Bachelor Of Science In Environmental Health And Sanitation Education - S', 'total_outstanding_balance': 31702.0}, {'program_name': 'DIPLOMA (GRAPHIC DESIGN)', 'total_outstanding_balance': 31314.27}, {'program_name': 'BACHELOR ARTS (GA WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 30110.2}, {'program_name': 'Master Of Philosophy In Accounting (Top-Up)', 'total_outstanding_balance': 29830.0}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 29423.78}, {'program_name': 'Master Of Science In Information Technology Education - W', 'total_outstanding_balance': 29389.0}, {'program_name': 'MASTER OF PHILOSOPHY (BASIC EDUCATION)', 'total_outstanding_balance': 29080.0}, {'program_name': 'BACHELOR OF ARTS (TWI WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 27901.0}, {'program_name': 'MASTER OF PHILOSOPHY (STRATEGIC COMMUNICATION)', 'total_outstanding_balance': 27379.0}, {'program_name': 'MASTER OF ARTS (STRATEGIC COMMUNICATION)', 'total_outstanding_balance': 26143.0}, {'program_name': 'MASTER OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'total_outstanding_balance': 26093.0}, {'program_name': 'BACHELOR OF ART (DANGME WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 25931.0}, {'program_name': 'MASTER OF PHILOSOPHY (CURRICULUM & PEDAGOGIC STUDIES)', 'total_outstanding_balance': 25412.2}, {'program_name': 'BACHELOR OF ARTS (DANGME EDUCATION)', 'total_outstanding_balance': 24879.75}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 24670.0}, {'program_name': 'Master Of Technology In Electrical And Electronics Engineering', 'total_outstanding_balance': 24610.0}, {'program_name': 'BACHELOR OF ARTS (EWE WITH GERMAN EDUCATION)', 'total_outstanding_balance': 24369.74}, {'program_name': 'BACHELOR OF ARTS (EWE WITH FRENCH EDUCATION)', 'total_outstanding_balance': 23581.29}, {'program_name': 'DOCTOR OF PHILOSOPHY (SOCIAL STUDIES)', 'total_outstanding_balance': 23355.0}, {'program_name': 'MASTER OF PHILOSOPHY (CHEMISTRY EDUCATION)', 'total_outstanding_balance': 23194.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (COMMUNICATION AND MEDIA)', 'total_outstanding_balance': 21828.0}, {'program_name': 'BACHELOR OF ARTS (DAGAARE WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 21554.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (APPLIED LINGUISTICS)', 'total_outstanding_balance': 20675.0}, {'program_name': 'Bachelor Of Science (Agriculture Engineering, Technology And Innovation Education)', 'total_outstanding_balance': 20569.0}, {'program_name': 'BACHELOR OF ARTS (SISAALI EDUCATION)', 'total_outstanding_balance': 19909.0}, {'program_name': 'MASTER OF PHILOSOPHY (FRENCH)', 'total_outstanding_balance': 19850.5}, {'program_name': 'BACHELOR OF ARTS (DAGBANI WITH LINGUISTIC EDUCATION)', 'total_outstanding_balance': 19309.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'total_outstanding_balance': 19050.0}, {'program_name': 'BACHELOR OF ARTS (RELIGIONS AND MORAL STUDIES EDUCATION)', 'total_outstanding_balance': 18667.0}, {'program_name': 'MASTER OF PHILOSOPHY (INFORMATION AND COMMUNICATION TECHNOLOGY)', 'total_outstanding_balance': 18293.0}, {'program_name': 'BACHELOR OF ARTS (DAGAARE EDUCATION)', 'total_outstanding_balance': 18279.29}, {'program_name': 'BACHELOR OF ARTS (DAGBANI WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 18124.0}, {'program_name': 'BACHELOR OF ARTS (EWE WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 17581.0}, {'program_name': 'DIPLOMA (THEATRE ARTS)', 'total_outstanding_balance': 17014.0}, {'program_name': 'MASTER OF ARTS (COMMUNICATION INSTRUCTION)', 'total_outstanding_balance': 16500.0}, {'program_name': 'MASTER OF PHILOSOPHY (COMMUNICATION INSTRUCTION)', 'total_outstanding_balance': 16500.0}, {'program_name': 'Master Of Philosophy In Agronomy', 'total_outstanding_balance': 16428.0}, {'program_name': 'BACHELOR OF ARTS (GA WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 16335.0}, {'program_name': 'Master Of Technology In Automotive Engineering Technology', 'total_outstanding_balance': 15810.0}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (MANAGEMENT INFORMATION SYSTEMS)', 'total_outstanding_balance': 15743.0}, {'program_name': 'MASTER OF PHILOSOPHY (TEXTILES AND FASHION)', 'total_outstanding_balance': 15425.0}, {'program_name': 'MASTER OF PHILOSOPHY (VISUAL COMMUNICATION)', 'total_outstanding_balance': 15075.0}, {'program_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT FINANCE)', 'total_outstanding_balance': 14750.0}, {'program_name': 'Bachelor Of Science (Mathematics Education) - S', 'total_outstanding_balance': 14268.0}, {'program_name': 'MASTER OF PHILOSOPHY (SPECIAL EDUCATION)', 'total_outstanding_balance': 14125.0}, {'program_name': 'BACHELOR OF ART (DAGBANI WITH GERMAN EDUCATION)', 'total_outstanding_balance': 13956.0}, {'program_name': 'BACHELOR OF ART (DAGAARE WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 13876.0}, {'program_name': 'Diploma In Education (Competency Based Training - CBT)', 'total_outstanding_balance': 13446.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W (Top-Up)', 'total_outstanding_balance': 12962.0}, {'program_name': 'Master Of Philosophy In Accounting (Top-Up) - W', 'total_outstanding_balance': 12880.0}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology Education)', 'total_outstanding_balance': 12707.0}, {'program_name': 'Diploma In Education - TK', 'total_outstanding_balance': 12620.0}, {'program_name': 'Master Of Philosophy In Crop Science (Top-Up)', 'total_outstanding_balance': 12615.0}, {'program_name': 'MASTER OF PHILOSOPHY (FOOD AND NUTRITION)', 'total_outstanding_balance': 12511.0}, {'program_name': 'BACHELOR OF ARTS (DAGBANI EDUCATION)', 'total_outstanding_balance': 12445.0}, {'program_name': 'MASTER OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'total_outstanding_balance': 12081.29}, {'program_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'total_outstanding_balance': 11973.39}, {'program_name': 'Doctor Of Philosophy In Crop Science', 'total_outstanding_balance': 11420.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education) - S', 'total_outstanding_balance': 11410.0}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 11180.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (ARTS AND CULTURE)', 'total_outstanding_balance': 11000.0}, {'program_name': 'MASTER OF PHILOSOPHY (ENTREPRENEURSHIP AND INNOVATION)', 'total_outstanding_balance': 10850.0}, {'program_name': 'BACHELOR OF SCIENCE MIDWIFERY', 'total_outstanding_balance': 10777.0}, {'program_name': 'Doctor Of Philosophy In Construction Technology', 'total_outstanding_balance': 10365.0}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 10312.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W', 'total_outstanding_balance': 10271.0}, {'program_name': 'BACHELOR OF ART (KUSAAL WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 10102.0}, {'program_name': 'Doctor Of Philosophy In Wood Science And Technology', 'total_outstanding_balance': 9964.0}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 9746.0}, {'program_name': 'MASTER OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'total_outstanding_balance': 9400.0}, {'program_name': 'BACHELOR OF ARTS (GA EDUCATION)', 'total_outstanding_balance': 8909.0}, {'program_name': 'MASTER OF PHILOSOPHY (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES)', 'total_outstanding_balance': 8640.5}, {'program_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'total_outstanding_balance': 8469.96}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 8434.23}, {'program_name': 'MASTER OF EDUCATION (SCIENCE)', 'total_outstanding_balance': 8433.0}, {'program_name': 'MASTER OF PHILOSOPHY (THEATRE ARTS)', 'total_outstanding_balance': 8265.0}, {'program_name': 'MASTER OF PHILOSOPHY (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'total_outstanding_balance': 8091.0}, {'program_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT COMMUNICATION)', 'total_outstanding_balance': 7962.0}, {'program_name': 'Doctor Of Philosophy In Soil Fertility And Plant Nutrition', 'total_outstanding_balance': 7680.0}, {'program_name': 'Master Of Philosophy In Information Technology - W (Top-Up)', 'total_outstanding_balance': 7655.0}, {'program_name': 'MASTER OF PHILOSOPHY (HISTORY EDUCATION)', 'total_outstanding_balance': 7170.0}, {'program_name': 'EXECUTIVE MASTERS (CONFLICT HUMAN RIGHTS & PEACE STUDIES)', 'total_outstanding_balance': 7000.0}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 6952.0}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 6640.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education)', 'total_outstanding_balance': 6560.0}, {'program_name': 'MASTER OF PHILOSOPHY (FAMILY LIFE MANAGEMENT)', 'total_outstanding_balance': 6427.0}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 6287.0}, {'program_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education) - S', 'total_outstanding_balance': 6120.96}, {'program_name': 'BACHELOR OF SCIENCE (FAMILY LIFE MANAGEMENT EDUCATION)', 'total_outstanding_balance': 6066.0}, {'program_name': 'Diploma In Welding And Fabrication Engineering Technology', 'total_outstanding_balance': 5910.0}, {'program_name': 'DIPLOMA IN SPORTS COACHING', 'total_outstanding_balance': 5844.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education) -S', 'total_outstanding_balance': 5740.0}, {'program_name': 'Master Of Education In Science Education', 'total_outstanding_balance': 5700.0}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 5689.0}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 5678.0}, {'program_name': 'BACHELOR OF ARTS (KASEM EDUCATION)', 'total_outstanding_balance': 5678.0}, {'program_name': 'MASTER OF PHILOSOPHY (JOURNALISM AND MEDIA ST)', 'total_outstanding_balance': 5500.0}, {'program_name': 'MASTER OF PHILOSOPHY (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'total_outstanding_balance': 5500.0}, {'program_name': 'BACHELOR OF ARTS BUSINESS STUDIES', 'total_outstanding_balance': 5346.0}, {'program_name': 'Master Of Philosophy In Teaching And Learning', 'total_outstanding_balance': 5334.0}, {'program_name': 'Bachelor Of Science (Welding And Fabrication Technology Education', 'total_outstanding_balance': 5318.0}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology Education) - W', 'total_outstanding_balance': 5310.0}, {'program_name': 'BACHELOR OF ARTS (TWI WITH GERMAN EDUCATION)', 'total_outstanding_balance': 5252.0}, {'program_name': 'Master Of Philosophy In Mechanical Engineering Technology', 'total_outstanding_balance': 5110.0}, {'program_name': 'MASTER OF PHILOSOPHY (ECONOMICS)', 'total_outstanding_balance': 4889.0}, {'program_name': 'MASTER OF ARTS (DEVELOPMENT COMMUNICATION)', 'total_outstanding_balance': 4800.0}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH FRENCH EDUCATION)', 'total_outstanding_balance': 4758.0}, {'program_name': 'Doctor Of Philosophy In Animal Science', 'total_outstanding_balance': 4400.0}, {'program_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - S', 'total_outstanding_balance': 4222.0}, {'program_name': 'BACHELOR OF ARTS (TWI WITH FRENCH EDUCATION)', 'total_outstanding_balance': 4123.0}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH FRENCH EDUCATION)', 'total_outstanding_balance': 4113.0}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education) - S', 'total_outstanding_balance': 4110.0}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH FRENCH EDUCATION)', 'total_outstanding_balance': 4107.0}, {'program_name': 'MASTER OF PHILOSOPHY (HUMAN RESOURCE MANAGEMENT)', 'total_outstanding_balance': 4042.0}, {'program_name': 'DIPLOMA IN MUSIC', 'total_outstanding_balance': 4040.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (SPECIAL EDUCATION)', 'total_outstanding_balance': 4000.0}, {'program_name': 'DIPLOMA IN INFORMATION TECHNOLOGY', 'total_outstanding_balance': 3712.0}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 3404.0}, {'program_name': 'BACHELOR OF ARTS (GA WITH GERMAN EDUCATION)', 'total_outstanding_balance': 3384.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S (Top-Up)', 'total_outstanding_balance': 3250.0}, {'program_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'total_outstanding_balance': 3000.0}, {'program_name': 'BACHELOR OF SCIENCE IN PUBLIC HEALTH NURSING', 'total_outstanding_balance': 2997.0}, {'program_name': 'MASTER OF PHILOSOPHY (POLITICAL SCIENCE EDUCATION)', 'total_outstanding_balance': 2985.0}, {'program_name': 'MASTER OF PHILOSOPHY IN SOCIAL STUDIES', 'total_outstanding_balance': 2985.0}, {'program_name': 'Diploma In Education - CP', 'total_outstanding_balance': 2950.0}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH GERMAN EDUCATION)', 'total_outstanding_balance': 2860.0}, {'program_name': 'MASTER OF PHILOSOPHY (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'total_outstanding_balance': 2754.0}, {'program_name': 'BACHELOR  OF ARTS (EWE WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 2700.5}, {'program_name': 'BACHELOR OF ARTS IN COMMUNICATION STUDIES', 'total_outstanding_balance': 2600.0}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH FRENCH EDUCATION)', 'total_outstanding_balance': 2570.0}, {'program_name': 'BACHELOR OF ART (DAGAARE WITH GERMAN EDUCATION)', 'total_outstanding_balance': 2558.0}, {'program_name': 'BACHELOR OF ARTS (GONJA EDUCATION)', 'total_outstanding_balance': 2549.0}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL EDUCATION)', 'total_outstanding_balance': 2548.0}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology Education) - S', 'total_outstanding_balance': 2540.0}, {'program_name': 'BACHELOR OF ARTS (GURUNE EDUCATION)', 'total_outstanding_balance': 2485.0}, {'program_name': 'DIPLOMA (TEXTILES AND FASHION)', 'total_outstanding_balance': 2385.0}, {'program_name': 'BACHELOR OF SCIENCE ECONOMICS WITH MANAGEMENT', 'total_outstanding_balance': 2380.0}, {'program_name': 'BACHELOR OF SCIENCE IN ECONOMICS', 'total_outstanding_balance': 2380.0}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Executive Office Administration)', 'total_outstanding_balance': 2300.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education)', 'total_outstanding_balance': 2173.0}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH FRENCH EDUCATION)', 'total_outstanding_balance': 2172.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education)', 'total_outstanding_balance': 2115.0}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education)', 'total_outstanding_balance': 2059.0}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH GERMAN EDUCATION)', 'total_outstanding_balance': 1900.0}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH FRENCH EDUCATION)', 'total_outstanding_balance': 1856.0}, {'program_name': 'MASTER OF PHILOSOPHY (CLOTHING AND TEXTILES)', 'total_outstanding_balance': 1630.0}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 1628.74}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S', 'total_outstanding_balance': 1450.0}, {'program_name': 'BACHELOR OF ART (LINGUISTICS AND DAGAARE EDUCATION)', 'total_outstanding_balance': 1284.0}, {'program_name': 'BACHELOR OF ARTS (KUSAAL EDUCATION)', 'total_outstanding_balance': 1280.0}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH GERMAN EDUCATION)', 'total_outstanding_balance': 1274.0}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH FRENCH EDUCATION)', 'total_outstanding_balance': 1274.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND GONJA EDUCATION)', 'total_outstanding_balance': 1274.0}, {'program_name': 'MASTER OF PHILOSOPHY (MUSIC EDUCATION)', 'total_outstanding_balance': 985.0}, {'program_name': 'BACHELOR OF ARTS (GA WITH FRENCH EDUCATION)', 'total_outstanding_balance': 900.0}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH GERMAN EDUCATION)', 'total_outstanding_balance': 824.0}, {'program_name': 'Post Diploma Bachelor Of Education (Early Grade) - K', 'total_outstanding_balance': 735.0}, {'program_name': 'Master Of Philosophy In Wood Science And Technology', 'total_outstanding_balance': 663.0}, {'program_name': 'Diploma In Education - M', 'total_outstanding_balance': 610.0}, {'program_name': 'B.SC INFORMATION TECHNOLOGY', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ART (DAGBANI WITH FRENCH EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH GERMAN EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH GERMAN EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH FRENCH EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH GERMAN EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND KASEM EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH GERMAN EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS COMPUTER SCIENCE AND MANAGEMENT', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS IN DEVELOPMENT AND ENVIRONMENTAL STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS IN MUSIC', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS IN RURAL DEVELOPMENT & ECOTOURISM', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF EDUCATION (PRIMARY EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF SCIENCE (HOME ECONOMICS EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF SCIENCE COMMUNITY HEALTH NURSING', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF SCIENCE COMPUTING WITH ACTUARIAL SCIENCE', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF SCIENCE IN ACCOUNTING', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF SCIENCE MANAGEMENT AND COMPUTER STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'BSc. Business Administration', 'total_outstanding_balance': 0.0}, {'program_name': 'BSc. Business Information Technology', 'total_outstanding_balance': 0.0}, {'program_name': 'BSc. Computer Science', 'total_outstanding_balance': 0.0}, {'program_name': 'CERTIFICATE IN BUSINESS STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'CERTIFICATE IN MANAGEMENT AND COMPUTER STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'Certificate in Ministry', 'total_outstanding_balance': 0.0}, {'program_name': 'CERTIFICATE IN TRAVEL AND TOURISM MANAGEMENT', 'total_outstanding_balance': 0.0}, {'program_name': 'COMMONWEALTH LEGISLATIVE DRAFTING COURSE', 'total_outstanding_balance': 0.0}, {'program_name': 'DIPLOMA (FRENCH EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'DIPLOMA (SIGN LANGUAGE)', 'total_outstanding_balance': 0.0}, {'program_name': 'Diploma In Registered Community Nursing', 'total_outstanding_balance': 0.0}, {'program_name': 'DIPLOMA IN REGISTERED PUBLIC HEALTH NURSING', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (EDUCATIONAL LEADERSHIP)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (ENGLISH LANGUAGE)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (FRENCH)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (MUSIC EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (PHD) THEOLOGY)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY IN GHANAIAN LANGUAGES', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF THEOLOGY (AFRICAN CHRISTIANITY) -INTENSIVE', 'total_outstanding_balance': 0.0}, {'program_name': 'DUMMY PROG', 'total_outstanding_balance': 0.0}, {'program_name': 'EXTENDED DIPLOMA STRATEGIC MANAGEMENT AND LEADERSHIP', 'total_outstanding_balance': 0.0}, {'program_name': 'Ghana Legal Systems / Constitutional Law Course', 'total_outstanding_balance': 0.0}, {'program_name': 'M.SC. ENVIRONMENTAL SUSTAINABILITY AND MANAGEMENT', 'total_outstanding_balance': 0.0}, {'program_name': 'MA ADULT EDUCATION', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (ART EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (ARTS AND CULTURE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (AUGUST)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (ENGLISH LANGUAGE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (GHANAIAN LANGUAGES EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (HISTORY EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (THEATRE ARTS)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS BIBLICAL STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS HOLISTIC MISSION AND DEVELOPMENT', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS INTERNATIONAL RELATIONS', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS LEADERSHIP', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS PENTECOSTAL STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS THEOLOGY AND MISSION', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS WORLD CHRISTIANITY OPTION', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGMENT & ORGANIZATION BEHAVIOUR)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (BIOLOGY)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (COMPUTER EDUCATION AND TECHNOLOGY)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (EARLY CHILDHOOD EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION AND MANAGEMENT) BY DISTANCE', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (FOOD AND NUTRITION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (GEOGRAPHY)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (GUIDANCE AND COUNSELLING)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (HOME ECONOMICS)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (POLITICAL SCIENCE EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (SCIENCE EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (SPECIAL EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION IN SCHOOL SUPERVISION AND QUALITY ASSURANCE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF PHILOSOPHY (ARTS AND CULTURE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF PHILOSOPHY (ASSESSMENT, MEASUREMENT AND EVALUATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF PHILOSOPHY (FINANCE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF PHILOSOPHY (HOME ECONOMICS)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF PHILOSOPHY COMMUNICATION AND MEDIA STUDIES   (COMMUNICATION SKILLS)', 'total_outstanding_balance': 0.0}, {'program_name': 'Master Of Philosophy In Electrical Power Systems Engineering', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF SCIENCE (BIOLOGY)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF SCIENCE (DEVELOPMENT FINANCE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF SCIENCE (ECONOMICS EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'Master of Theology', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF THEOLOGY (AFRICAN CHRISTIANITY) - REGULAR', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF THEOLOGY (AFRICAN CHRISTIANITY)-INTENSIVE', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF THEOLOGY (BIBLE TRANSLATION AND INTERPRETATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MSC LOGISTICS AND SUPPLY CHAIN MANAGEMENT', 'total_outstanding_balance': 0.0}, {'program_name': 'NURSING ACCESS COURSE', 'total_outstanding_balance': 0.0}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education) - S', 'total_outstanding_balance': 0.0}, {'program_name': 'POST GRADUATE DIPLOMA IN TEACHING AND LEARNING IN HIGHER EDUCATION', 'total_outstanding_balance': 0.0}, {'program_name': 'Preliminary Law Course', 'total_outstanding_balance': 0.0}, {'program_name': 'Master Of Philosophy In Electrical Power Systems Engineering (Top-Up)', 'total_outstanding_balance': -781.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND NZEMA EDUCATION)', 'total_outstanding_balance': -1274.0}, {'program_name': 'CERTIFICATE (PRE-SCHOOL EDUCATION)', 'total_outstanding_balance': -1274.0}, {'program_name': 'MASTER OF EDUCATION (FRENCH)', 'total_outstanding_balance': -1274.0}, {'program_name': 'MASTER OF FINE ARTS (THEATRE ARTS)', 'total_outstanding_balance': -1274.0}, {'program_name': 'BACHELOR OF ARTS (DEVELOPMENT COMMUNICATION)', 'total_outstanding_balance': -1949.0}, {'program_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'total_outstanding_balance': -2000.0}, {'program_name': 'Master Of Philosophy In Agronomy (Top-up)', 'total_outstanding_balance': -2015.0}, {'program_name': 'MASTER OF PHILOSOPHY (INTEGRATED SCIENCE EDUCATION)', 'total_outstanding_balance': -2370.0}, {'program_name': 'BACHELOR OF ART (LINGUISTIC AND EWE EDUCATION)', 'total_outstanding_balance': -2548.0}, {'program_name': 'Master Of Philosophy In Mathematics Education - W', 'total_outstanding_balance': -2553.0}, {'program_name': 'MASTER OF PHILOSOPHY (ACCOUNTING)', 'total_outstanding_balance': -2562.0}, {'program_name': 'Post Diploma Bachelor Of Science (Agricultural Science Education)', 'total_outstanding_balance': -2747.0}, {'program_name': 'Bachelor Of Science In Administration (Business Information Systems)', 'total_outstanding_balance': -2845.0}, {'program_name': 'Master Of Philosophy In Plant Pathology', 'total_outstanding_balance': -2895.0}, {'program_name': 'MASTER OF PHILOSOPHY (ASSESSMENT IN SPECIAL EDUCATION)', 'total_outstanding_balance': -2960.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND DANGME EDUCATION)', 'total_outstanding_balance': -3172.0}, {'program_name': 'MASTER OF ARTS (FRENCH TRANSLATION)', 'total_outstanding_balance': -3312.0}, {'program_name': 'BACHELOR OF ART (SISAALI EDUCATION WITH FRENCH EDUCATION)', 'total_outstanding_balance': -3378.0}, {'program_name': 'BACHELOR OF SCIENCE (INSURANCE AND RISK MANAGEMENT)', 'total_outstanding_balance': -3383.0}, {'program_name': 'MASTER OF PHILOSOPHY (PHYSICS EDUCATION)', 'total_outstanding_balance': -3417.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND GA EDUCATION)', 'total_outstanding_balance': -3764.0}, {'program_name': 'B.SC. DOC', 'total_outstanding_balance': -4000.0}, {'program_name': 'Diploma In Business Administration (Accounting) - W', 'total_outstanding_balance': -4084.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Business Information Systems)', 'total_outstanding_balance': -4298.0}, {'program_name': 'Master Of Philosophy In Biology Education', 'total_outstanding_balance': -4842.0}, {'program_name': 'BACHELOR OF EDUCATION (SPECIAL NEEDS EDUCATION)', 'total_outstanding_balance': -5096.0}, {'program_name': 'Master Of Philosophy In Construction Technology (Top-Up)', 'total_outstanding_balance': -5135.0}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH ENGLISH EDUCATION)', 'total_outstanding_balance': -5236.0}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education)', 'total_outstanding_balance': -5267.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS EDUCATION)', 'total_outstanding_balance': -5312.0}, {'program_name': 'Bachelor Of Arts (English With French Education)', 'total_outstanding_balance': -5476.0}, {'program_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY) BY DISTANCE', 'total_outstanding_balance': -6000.0}, {'program_name': 'BACHELOR OF SCIENCE (FOOD AND NUTRITION EDUCATION)', 'total_outstanding_balance': -6011.49}, {'program_name': 'Post Diploma Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'total_outstanding_balance': -6061.0}, {'program_name': 'BACHELOR OF SCIENCE IN NURSING', 'total_outstanding_balance': -6075.12}, {'program_name': 'Master Of Philosophy In Wood Science And Technology (Top-Up)', 'total_outstanding_balance': -6434.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) -- R', 'total_outstanding_balance': -6553.0}, {'program_name': 'DIPLOMA (EARLY GRADE)', 'total_outstanding_balance': -6776.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Banking And Finance) - W', 'total_outstanding_balance': -6897.0}, {'program_name': 'Diploma In Management Education', 'total_outstanding_balance': -7040.0}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance) - E', 'total_outstanding_balance': -7247.0}, {'program_name': 'BACHELOR OF ARTS (NZEMA EDUCATION)', 'total_outstanding_balance': -7397.0}, {'program_name': 'Master Of Philosophy In Construction Management (Top Up)', 'total_outstanding_balance': -7861.0}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing And Entrepreneurship) - W', 'total_outstanding_balance': -8013.0}, {'program_name': 'Master Of Philosophy In Soil Science', 'total_outstanding_balance': -8294.0}, {'program_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL SCIENCE EDUCATION)', 'total_outstanding_balance': -8856.0}, {'program_name': 'Diploma In Architecture And Digital Construction', 'total_outstanding_balance': -9330.0}, {'program_name': 'Post Diploma Bachelor Of Science (Management Education)', 'total_outstanding_balance': -9508.0}, {'program_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education)', 'total_outstanding_balance': -9628.0}, {'program_name': 'Diploma In Education (Junior High)', 'total_outstanding_balance': -9855.0}, {'program_name': 'Bachelor Of Arts (English Language Education) - M', 'total_outstanding_balance': -10150.0}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing) - R', 'total_outstanding_balance': -10176.0}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing) - W', 'total_outstanding_balance': -11463.0}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology)', 'total_outstanding_balance': -11748.0}, {'program_name': 'BACHELOR OF MUSIC', 'total_outstanding_balance': -12305.98}, {'program_name': 'Bachelor Of Science (Plumbing, And Gas Technology)', 'total_outstanding_balance': -13159.0}, {'program_name': 'Master Of Philosophy In Educational Leadership (Top-Up)', 'total_outstanding_balance': -13506.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND FANTE EDUCATION)', 'total_outstanding_balance': -14538.0}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology Education)', 'total_outstanding_balance': -14719.0}, {'program_name': 'BACHELOR OF FINE ART (ANIMATION)', 'total_outstanding_balance': -15410.0}, {'program_name': 'Bachelor Of Science (Marketing) - E', 'total_outstanding_balance': -16015.0}, {'program_name': 'Master Of Philosophy In Business Management (Top-Up)', 'total_outstanding_balance': -16920.0}, {'program_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'total_outstanding_balance': -17673.0}, {'program_name': 'BACHELOR OF LAWS', 'total_outstanding_balance': -17700.29}, {'program_name': 'Bachelor Of Science (Civil Engineering)', 'total_outstanding_balance': -18504.0}, {'program_name': 'Master Of Philosophy In Animal Science', 'total_outstanding_balance': -18736.0}, {'program_name': 'Master Of Philosophy In Automotive Engineering Technology', 'total_outstanding_balance': -18780.0}, {'program_name': 'Diploma In Automotive Engineering Technology', 'total_outstanding_balance': -20172.0}, {'program_name': 'Post Diploama Bachelor Of Science (Computerised Accounting)', 'total_outstanding_balance': -22640.0}, {'program_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'total_outstanding_balance': -22756.0}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - E', 'total_outstanding_balance': -26039.0}, {'program_name': 'Master Of Philosophy In Mechanical Engineering Technology (Top-Up)', 'total_outstanding_balance': -28167.0}, {'program_name': 'Bachelor Of Science (Marketing) - R', 'total_outstanding_balance': -28853.0}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BUSINESS INFORMATION SYSTEMS)', 'total_outstanding_balance': -29186.0}, {'program_name': 'Bachelor Of Science (Computerised Accounting)', 'total_outstanding_balance': -29624.0}, {'program_name': 'Bachelor Of Arts (Arabic With English Education)', 'total_outstanding_balance': -30035.0}, {'program_name': 'Bachelor Of Business Administration (Management) - R', 'total_outstanding_balance': -30270.0}, {'program_name': 'Diploma In Mechanical Engineering Technology', 'total_outstanding_balance': -30479.0}, {'program_name': 'Master Of Philosophy In Fashion Design And Textiles (Top-Up)', 'total_outstanding_balance': -30791.0}, {'program_name': 'Master Of Philosophy In Mathematics Education (Top-Up) - W', 'total_outstanding_balance': -32341.0}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology Education)', 'total_outstanding_balance': -32792.0}, {'program_name': 'Bachelor Of Science (Marketing And Entrepreneurship)', 'total_outstanding_balance': -32875.0}, {'program_name': 'Master Of Philosophy In Automotive Engineering Technology (Top-Up)', 'total_outstanding_balance': -33303.0}, {'program_name': 'Bachelor Of Science (Automotive Engineering Technology Education)', 'total_outstanding_balance': -33760.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - E', 'total_outstanding_balance': -33991.0}, {'program_name': 'BACHELOR OF ARTS (CREATIVE ARTS AND DESIGN EDUCATION)', 'total_outstanding_balance': -35032.0}, {'program_name': 'BACHELOR OF SCIENCE (SPORTS COACHING)', 'total_outstanding_balance': -36010.0}, {'program_name': 'Master Of Philosophy In Chemistry Education', 'total_outstanding_balance': -41041.0}, {'program_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education) - S', 'total_outstanding_balance': -41945.54}, {'program_name': 'Bachelor Of Science In Administration (Accounting) - E', 'total_outstanding_balance': -42635.0}, {'program_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education)', 'total_outstanding_balance': -44476.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND FRENCH EDUCATION)', 'total_outstanding_balance': -47952.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'total_outstanding_balance': -55157.0}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Management) - E', 'total_outstanding_balance': -56463.0}, {'program_name': 'Bachelor Of Science (Mechanical Engineering Technology Education)', 'total_outstanding_balance': -58879.0}, {'program_name': 'BACHELOR OF SCIENCE (INTEGRATED HOME ECONOMICS EDUCATION)', 'total_outstanding_balance': -58925.99}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting)', 'total_outstanding_balance': -61245.0}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance)', 'total_outstanding_balance': -62857.0}, {'program_name': 'BACHELOR OF ARTS (STRATEGIC COMMUNICATION)', 'total_outstanding_balance': -66301.4}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance) - W', 'total_outstanding_balance': -66576.0}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - W', 'total_outstanding_balance': -66795.0}, {'program_name': 'Master Of Philosophy In Mathematics Education', 'total_outstanding_balance': -67347.0}, {'program_name': 'Master Of Philosophy In Business Management - W', 'total_outstanding_balance': -72498.0}, {'program_name': 'Bachelor Of Science (Mechanical Technology Education) - S', 'total_outstanding_balance': -72711.08}, {'program_name': 'Diploma In Construction Technology', 'total_outstanding_balance': -77501.0}, {'program_name': 'Bachelor Of Science In Administration (Accounting) - W', 'total_outstanding_balance': -78913.0}, {'program_name': 'Master Of Philosophy In Catering And Hospitality (Top-Up)', 'total_outstanding_balance': -80345.0}, {'program_name': 'Doctor Of Philosophy In Educational Leadership', 'total_outstanding_balance': -80381.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND TWI EDUCATION)', 'total_outstanding_balance': -80988.5}, {'program_name': 'Master Of Philosophy In Catering And Hospitality', 'total_outstanding_balance': -81443.0}, {'program_name': 'BACHELOR OF SCIENCE (MATHEMATICS WITH ECONOMICS EDUCATION)', 'total_outstanding_balance': -84483.48}, {'program_name': 'Diploma In Business Administration (Management) - W', 'total_outstanding_balance': -85268.2}, {'program_name': 'Bachelor Of Business Administration (Executive Office Administration)', 'total_outstanding_balance': -88644.0}, {'program_name': 'Diploma In Catering And Hospitality', 'total_outstanding_balance': -89428.0}, {'program_name': 'Master Of Philosophy In Biology', 'total_outstanding_balance': -92785.0}, {'program_name': 'Diploma In Fashion Design And Textiles', 'total_outstanding_balance': -96557.0}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (MARKETING)', 'total_outstanding_balance': -96863.0}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Management) - W', 'total_outstanding_balance': -100803.0}, {'program_name': 'BACHELOR OF EDUCATION (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'total_outstanding_balance': -102327.0}, {'program_name': 'BACHELOR OF ARTS (THEATRE ARTS)', 'total_outstanding_balance': -102457.0}, {'program_name': 'Master Of Philosophy In Science Education', 'total_outstanding_balance': -102486.0}, {'program_name': 'Bachelor Of Science (Automotive Technology Education) - S', 'total_outstanding_balance': -102946.6}, {'program_name': 'Master Of Philosophy In Environmental And Occupational Health Education', 'total_outstanding_balance': -104822.0}, {'program_name': 'Doctor Of Philosophy In Mathematics Education', 'total_outstanding_balance': -105676.0}, {'program_name': 'Diploma In Economics', 'total_outstanding_balance': -110681.0}, {'program_name': 'Bachelor Of Arts (French With English Education)', 'total_outstanding_balance': -113567.0}, {'program_name': 'Bachelor Of Science (Marketing And Entrepreneurship) - W', 'total_outstanding_balance': -114385.0}, {'program_name': 'Master Of Philosophy In Accounting - W', 'total_outstanding_balance': -114540.0}, {'program_name': 'Bachelor Of Science (Marketing) - W', 'total_outstanding_balance': -117056.0}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (MARKETING AND ENTREPRENEURSHIP)', 'total_outstanding_balance': -118831.28}, {'program_name': 'Master Of Philosophy In Construction Technology', 'total_outstanding_balance': -128801.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'total_outstanding_balance': -130967.0}, {'program_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - R', 'total_outstanding_balance': -133385.0}, {'program_name': 'Diploma In Business Administration (Accounting)', 'total_outstanding_balance': -136014.0}, {'program_name': 'BACHELOR OF ARTS (FASHION DESIGN AND TEXTILE EDUCATION)', 'total_outstanding_balance': -139280.2}, {'program_name': 'Bachelor Of Science (Wood Technology With Education) - S', 'total_outstanding_balance': -141866.0}, {'program_name': 'Bachelor Of Science (Mathematics Education) - M', 'total_outstanding_balance': -142785.0}, {'program_name': 'Bachelor Of Science (Wood Technology Education)', 'total_outstanding_balance': -143739.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'total_outstanding_balance': -144164.0}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - R', 'total_outstanding_balance': -151665.7}, {'program_name': 'Master Of Philosophy In Construction Management', 'total_outstanding_balance': -154701.0}, {'program_name': 'Diploma In Electrical And Electronics Engineering Technology', 'total_outstanding_balance': -166794.0}, {'program_name': 'Master Of Philosophy In Business Management (Top-Up) - W', 'total_outstanding_balance': -169596.0}, {'program_name': 'Master Of Philosophy In Fashion Design And Textiles', 'total_outstanding_balance': -170967.0}, {'program_name': 'BACHELOR OF ART (LINGUISTICS AND ENGLISH EDUCATION)', 'total_outstanding_balance': -173571.6}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - W', 'total_outstanding_balance': -176622.0}, {'program_name': 'Bachelor Of Science (Wood Technology With Education)', 'total_outstanding_balance': -179288.0}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'total_outstanding_balance': -184055.5}, {'program_name': 'BACHELOR OF ARTS (TWI WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': -185909.47}, {'program_name': 'Bachelor Of Business Administration (Human Resource Management) - E', 'total_outstanding_balance': -188171.0}, {'program_name': 'Master Of Philosophy In Educational Leadership', 'total_outstanding_balance': -190984.0}, {'program_name': 'BACHELOR OF SCIENCE (HEALTH ADMINISTRATION EDUCATION)', 'total_outstanding_balance': -192220.92}, {'program_name': 'BACHELOR OF EDUCATION (SPECIAL EDUCATION)', 'total_outstanding_balance': -195855.74}, {'program_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'total_outstanding_balance': -209633.0}, {'program_name': 'BACHELOR OF ARTS (MUSIC EDUCATION)', 'total_outstanding_balance': -210186.8}, {'program_name': 'BACHELOR OF ARTS (TWI EDUCATION)', 'total_outstanding_balance': -214470.89}, {'program_name': 'Bachelor Of Arts (Arabic Education)', 'total_outstanding_balance': -224815.9}, {'program_name': 'Bachelor Of Science (Mechanical Technology Education)', 'total_outstanding_balance': -227857.0}, {'program_name': 'Bachelor Of Business Administration (Secretarial Education)', 'total_outstanding_balance': -229911.0}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'total_outstanding_balance': -247808.0}, {'program_name': 'Master Of Philosophy In Business Management', 'total_outstanding_balance': -253294.0}, {'program_name': 'Bachelor Of Education (Early Grade) - K', 'total_outstanding_balance': -255078.5}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (FINANCE)', 'total_outstanding_balance': -295997.0}, {'program_name': 'Bachelor Of Science (Mathematics Education) - W', 'total_outstanding_balance': -307064.1}, {'program_name': 'Bachelor Of Science (Information Technology)', 'total_outstanding_balance': -326586.8}, {'program_name': 'Bachelor Of Science (Natural Resources Management And Education)', 'total_outstanding_balance': -327682.0}, {'program_name': 'Bachelor Of Science (Physics Education)', 'total_outstanding_balance': -382790.4}, {'program_name': 'Bachelor Of Science (Wood Technology Education) - S', 'total_outstanding_balance': -383020.24}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'total_outstanding_balance': -394610.0}, {'program_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'total_outstanding_balance': -394933.43}, {'program_name': 'Master Of Business Administration (Accounting)', 'total_outstanding_balance': -417306.0}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'total_outstanding_balance': -420024.66}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'total_outstanding_balance': -436644.0}, {'program_name': 'BACHELOR OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'total_outstanding_balance': -437202.15}, {'program_name': 'Bachelor Of Science In Administration (Accounting)', 'total_outstanding_balance': -465804.0}, {'program_name': 'Bachelor Of Theology', 'total_outstanding_balance': -484000.0}, {'program_name': 'BACHELOR OF ARTS (COMMUNICATION STUDIES)', 'total_outstanding_balance': -485626.0}, {'program_name': 'Master Of Philosophy In Accounting', 'total_outstanding_balance': -569404.0}, {'program_name': 'BACHELOR OF SCIENCE (BIOLOGY EDUCATION)', 'total_outstanding_balance': -569535.0}, {'program_name': 'Bachelor Of Science (Agribusiness Management And Entrepreneurship Education)', 'total_outstanding_balance': -570265.0}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'total_outstanding_balance': -573447.7}, {'program_name': 'Diploma In Business Administration (Management)', 'total_outstanding_balance': -600147.5}, {'program_name': 'BACHELOR OF SCIENCE (PHYSICAL EDUCATION)', 'total_outstanding_balance': -601761.18}, {'program_name': 'BACHELOR OF ARTS (HISTORY EDUCATION)', 'total_outstanding_balance': -616154.25}, {'program_name': 'Bachelor Of Business Administration (Management) - W', 'total_outstanding_balance': -623100.0}, {'program_name': 'Bachelor Of Arts (Economics With Social Studies Education)', 'total_outstanding_balance': -660686.0}, {'program_name': 'BACHELOR OF ARTS (GRAPHIC DESIGN)', 'total_outstanding_balance': -751279.0}, {'program_name': 'Bachelor Of Science (Chemistry Education)', 'total_outstanding_balance': -780175.04}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'total_outstanding_balance': -823623.23}, {'program_name': 'Bachelor Of Business Administration (Human Resource Management) - W', 'total_outstanding_balance': -845422.0}, {'program_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'total_outstanding_balance': -972330.9}, {'program_name': 'Master Of Business Administration (Human Resource Management And Organisational Behaviour ) - Weekend', 'total_outstanding_balance': -972686.0}, {'program_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - S', 'total_outstanding_balance': -998550.0}, {'program_name': 'Master Of Divinity', 'total_outstanding_balance': -1107000.0}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - R', 'total_outstanding_balance': -1127030.85}, {'program_name': 'Bachelor Of Business Administration (Management) - E', 'total_outstanding_balance': -1175070.0}, {'program_name': 'Master Of Arts In Ministry', 'total_outstanding_balance': -1344800.0}, {'program_name': 'Bachelor Of Arts (Social Studies With Economics Education)', 'total_outstanding_balance': -1356118.0}, {'program_name': 'Bachelor Of Education (Early Grade) - M', 'total_outstanding_balance': -1397293.0}, {'program_name': 'Bachelor Of Arts (French Education)', 'total_outstanding_balance': -1414263.16}, {'program_name': 'BACHELOR OF ARTS (ART EDUCATION)', 'total_outstanding_balance': -1420622.44}, {'program_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - R', 'total_outstanding_balance': -1509123.5}, {'program_name': 'Bachelor Of Arts (Economics Education)', 'total_outstanding_balance': -1619441.7}, {'program_name': 'Bachelor Of Science (Environmental Health And Sanitation Education)', 'total_outstanding_balance': -1621931.0}, {'program_name': 'Bachelor Of Science (Information Technology Education) - W', 'total_outstanding_balance': -1722763.0}, {'program_name': 'Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'total_outstanding_balance': -1787301.0}, {'program_name': 'BACHELOR OF ARTS (GEOGRAPHY EDUCATION)', 'total_outstanding_balance': -1944012.18}, {'program_name': 'BACHELOR OF ART (SOCIAL STUDIES EDUCATION)', 'total_outstanding_balance': -2073296.4}, {'program_name': 'Bachelor Of Science (Catering And Hospitality Education) - S', 'total_outstanding_balance': -2085830.62}, {'program_name': 'Bachelor Of Science (Biological Sciences Education)', 'total_outstanding_balance': -2143412.76}, {'program_name': 'BACHELOR OF ARTS (POLITICAL SCIENCE EDUCATION)', 'total_outstanding_balance': -2187471.11}, {'program_name': 'Bachelor Of Education (Upper Primary)', 'total_outstanding_balance': -2304386.46}, {'program_name': 'Bachelor Of Science (Agricultural Science Education)', 'total_outstanding_balance': -2312207.44}, {'program_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'total_outstanding_balance': -2512316.0}, {'program_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'total_outstanding_balance': -2587231.24}, {'program_name': 'Bachelor Of Education (Junior High)', 'total_outstanding_balance': -2717689.0}, {'program_name': 'Bachelor Of Science (Integrated Science Education)', 'total_outstanding_balance': -2876327.01}, {'program_name': 'Bachelor Of Science (Catering And Hospitality Education) - R', 'total_outstanding_balance': -2886185.5}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'total_outstanding_balance': -3203971.93}, {'program_name': 'Bachelor Of Science (Accounting Education)', 'total_outstanding_balance': -3237626.2}, {'program_name': 'Bachelor Of Science (Management Education)', 'total_outstanding_balance': -3841907.74}, {'program_name': 'POST CALL LAW COURSE', 'total_outstanding_balance': -4415962.9}, {'program_name': 'Bachelor Of Arts (English Language Education)', 'total_outstanding_balance': -4683021.73}, {'program_name': 'Bachelor Of Science (Mathematics Education)', 'total_outstanding_balance': -5643488.68}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'total_outstanding_balance': -7883860.83}, {'program_name': 'Bachelor Of Science (Information Technology Education)', 'total_outstanding_balance': -********.4}, {'program_name': 'PROFESSIONAL LAW COURSE', 'total_outstanding_balance': -*********.96}]
2025-08-09 14:16:32,307 - root - INFO - [{'percentage_in_arrears': 0.0}]
2025-08-09 14:16:32,307 - root - INFO - 'No results'
2025-08-09 14:16:32,307 - root - INFO - 'No results'
2025-08-09 14:16:32,307 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-09 14:16:42,787 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:42,798 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-09 14:16:55,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:16:55,264 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:16:55,265 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:16:55,265 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:16:55,265 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:16:55,265 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:16:55,265 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:16:55,265 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:16:55,265 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:16:55,265 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total amount of fees owed by students at the institution where students owe the most fee...
2025-08-09 14:16:55,265 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total amount of fees owed by students at the institution where students owe the most fees is -$2...
2025-08-09 14:16:55,265 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:16:55,265 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_id': 1, 'total_fees_owed': -2600.0}]...
2025-08-09 14:16:55,265 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_fees_owed_by_top_institution
2025-08-09 14:16:55,265 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 14:16:55,265 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 14:16:55,266 - celery.redirected - WARNING - [{'institution_id': 1, 'total_fees_owed': -2600.0}]
2025-08-09 14:16:55,266 - celery.redirected - WARNING - ================================= 
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 14:16:55,266 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:16:55,266 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:16:55,266 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:16:55,266 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How many students are currently enrolled at the institution where students owe the most fees?...
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Currently, there are no students enrolled at the institution where students owe the most fees....
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'enrolled_students': 0}]...
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: enrolled_students_at_highest_fee_institution
2025-08-09 14:16:55,266 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 14:16:55,267 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 14:16:55,267 - celery.redirected - WARNING - [{'enrolled_students': 0}]
2025-08-09 14:16:55,267 - celery.redirected - WARNING - ================================= 
2025-08-09 14:16:55,267 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 14:16:55,267 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:16:55,267 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:16:55,267 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:16:55,267 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:16:55,267 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:16:55,267 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:16:55,268 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:16:55,268 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:16:55,268 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What types of fees are included in the total amount owed at the institution where students owe the m...
2025-08-09 14:16:55,268 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total amount owed at the institution where students owe the most fees includes a variety of char...
2025-08-09 14:16:55,268 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:16:55,268 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'name': 'Tuition Fee'}, {'name': 'Examination Fee'}, {'name': 'Sports Fee'}, {'name': 'Student Cou...
2025-08-09 14:16:55,268 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_fees_types_at_highest_debt_institution
2025-08-09 14:16:55,268 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 14:16:55,268 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 14:16:55,268 - celery.redirected - WARNING - [{'name': 'Tuition Fee'}, {'name': 'Examination Fee'}, {'name': 'Sports Fee'}, {'name': 'Student Council Dues'}, {'name': 'Hall Affiliation Fee'}, {'name': 'Entertainment Fee'}, {'name': 'HOSTEL FEES'}, {'name': 'Academic Facility User Fee'}, {'name': 'Endowment Fund'}, {'name': 'Health Insurance'}, {'name': 'SRC Dues'}, {'name': 'School fees'}, {'name': 'SRC duties'}, {'name': "Lecturers'  Appreciation"}, {'name': 'Test'}, {'name': 'Test4'}, {'name': 'TUITION'}, {'name': 'JCRC'}, {'name': 'Environmental Sanitation Fee'}, {'name': 'Medical exams fees'}, {'name': 'Health Levy'}, {'name': 'ICT FACILITY USER FEE'}, {'name': 'Sport Fees'}, {'name': 'ID Card'}, {'name': 'Furniture Levy'}, {'name': 'ICT Facilities User Fee'}, {'name': 'Development Levy'}, {'name': 'Online Teaching Service'}, {'name': 'Trip Fees'}, {'name': 'Environmental and Sanitation Fee'}, {'name': 'Library'}]
2025-08-09 14:16:55,268 - celery.redirected - WARNING - ================================= 
2025-08-09 14:16:55,268 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 14:16:55,269 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:16:55,269 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:16:55,269 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 2
2025-08-09 14:16:55,269 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the average fee amount owed per student at the institution where students owe the most fees?...
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 2:
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the average fee amount owed per student at the institution where students owe the most fees?...
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The average fee amount owed per student at the institution where students owe the most fees is -$130...
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:16:55,269 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'average_fee_per_student': -1300.0}]...
2025-08-09 14:16:55,270 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_fee_owed_per_student_highest
2025-08-09 14:16:55,270 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 14:16:55,270 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 14:16:55,270 - celery.redirected - WARNING - [{'average_fee_per_student': -1300.0}]
2025-08-09 14:16:55,270 - celery.redirected - WARNING - ================================= 
2025-08-09 14:16:55,270 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 2 documents, 1 with data_returned=True
2025-08-09 14:16:55,270 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:16:55,270 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:16:55,270 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:16:55,270 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:16:55,271 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:16:55,271 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:16:55,271 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:16:55,271 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:16:55,271 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific programs or departments within the institution where students owe the most fees t...
2025-08-09 14:16:55,271 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The programs with the highest outstanding balances owed by students are as follows: 
1. **Bachelor o...
2025-08-09 14:16:55,271 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: bar_chart
2025-08-09 14:16:55,272 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'program_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'total_outstanding_balance': 1434460.78...
2025-08-09 14:16:55,272 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_outstanding_balances_by_program
2025-08-09 14:16:55,272 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 14:16:55,272 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 14:16:55,273 - celery.redirected - WARNING - [{'program_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'total_outstanding_balance': 1434460.78}, {'program_name': 'Diploma In Education', 'total_outstanding_balance': 590428.2}, {'program_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'total_outstanding_balance': 572085.04}, {'program_name': 'BACHELOR OF EDUCATION (EARLY GRADE)', 'total_outstanding_balance': 476761.38}, {'program_name': 'Master Of Arts In Educational Leadership', 'total_outstanding_balance': 402376.2}, {'program_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'total_outstanding_balance': 337275.0}, {'program_name': 'BACHELOR OF ARTS (SOCIAL STUDIES EDUCATION)', 'total_outstanding_balance': 233099.61}, {'program_name': 'Post Diploma Bachelor Of Science (Environmental Health And Sanitation Education)', 'total_outstanding_balance': 181249.0}, {'program_name': 'Master Of Technology Education In Catering And Hospitality', 'total_outstanding_balance': 165740.0}, {'program_name': 'Diploma In Education - KS', 'total_outstanding_balance': 154070.0}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 123673.79}, {'program_name': 'BACHELOR OF ARTS (EWE EDUCATION)', 'total_outstanding_balance': 116490.0}, {'program_name': 'DIPLOMA IN EARLY GRADE', 'total_outstanding_balance': 108654.99}, {'program_name': 'MASTER OF PHILOSOPHY (GHANAIAN LANGUAGE STUDIES)', 'total_outstanding_balance': 107927.0}, {'program_name': 'DIPLOMA (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'total_outstanding_balance': 104546.0}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - S', 'total_outstanding_balance': 103221.81}, {'program_name': 'MASTER OF PHILOSOPHY (SCIENCE EDUCATION)', 'total_outstanding_balance': 103079.5}, {'program_name': 'DIPLOMA IN BASIC EDUCATION', 'total_outstanding_balance': 86068.75}, {'program_name': 'BACHELOR OF ARTS (ENGLISH EDUCATION)', 'total_outstanding_balance': 84409.81}, {'program_name': 'DIPLOMA (ART)', 'total_outstanding_balance': 78452.1}, {'program_name': 'Master Of Science In Information Technology Education', 'total_outstanding_balance': 76074.0}, {'program_name': 'Diploma In Environmental Health And Sanitation Education', 'total_outstanding_balance': 70189.0}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'total_outstanding_balance': 69785.0}, {'program_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'total_outstanding_balance': 68944.3}, {'program_name': 'Master Of Technology Education In Fashion Design And Textile', 'total_outstanding_balance': 65240.0}, {'program_name': 'Master Of Education In Mathematics Education', 'total_outstanding_balance': 64080.0}, {'program_name': 'Master Of Philosophy In Public Health', 'total_outstanding_balance': 63934.2}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'total_outstanding_balance': 63295.0}, {'program_name': 'Master Of Technology In Construction Technology', 'total_outstanding_balance': 61860.0}, {'program_name': 'Master Of Technology In Construction Management', 'total_outstanding_balance': 58918.0}, {'program_name': 'Bachelor Of Science In Occupational Health And Safety', 'total_outstanding_balance': 57154.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'total_outstanding_balance': 55496.0}, {'program_name': 'BACHELOR OF ART (SISAALI WITH ENGLISH EDUCATION)', 'total_outstanding_balance': 55280.0}, {'program_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'total_outstanding_balance': 55074.68}, {'program_name': 'Doctor Of Philosophy In Construction Management', 'total_outstanding_balance': 53522.0}, {'program_name': 'MASTER OF PHILOSOPHY (EARLY CHILDHOOD EDUCATION)', 'total_outstanding_balance': 51836.0}, {'program_name': 'MASTER OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'total_outstanding_balance': 48497.0}, {'program_name': 'Master Of Technology In Mechanical Technology', 'total_outstanding_balance': 47950.0}, {'program_name': 'Master Of Education In Agriculture', 'total_outstanding_balance': 45710.0}, {'program_name': 'BACHELOR OF ARTS (FANTE EDUCATION)', 'total_outstanding_balance': 42689.0}, {'program_name': 'Master Of Philosophy In Information Technology - W', 'total_outstanding_balance': 41284.0}, {'program_name': 'MASTER OF PHILOSOPHY (ENGLISH LANGUAGE)', 'total_outstanding_balance': 39948.0}, {'program_name': 'MASTER OF EDUCATION (MATHEMATICS)', 'total_outstanding_balance': 39644.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (SCIENCE EDUCATION)', 'total_outstanding_balance': 38728.0}, {'program_name': 'Master Of Philosophy In Information Technology', 'total_outstanding_balance': 38680.0}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'total_outstanding_balance': 38347.34}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology Education) - S', 'total_outstanding_balance': 36613.3}, {'program_name': 'MASTER OF PHILOSOPHY (ART EDUCATION)', 'total_outstanding_balance': 36430.0}, {'program_name': 'Master Of Philosophy In Crop Science', 'total_outstanding_balance': 35086.0}, {'program_name': 'Master Of Technology In Wood Technology', 'total_outstanding_balance': 34570.0}, {'program_name': 'Diploma In Education - TM', 'total_outstanding_balance': 33850.0}, {'program_name': 'Post Diploma Bachelor Of Science In Environmental Health And Sanitation Education - S', 'total_outstanding_balance': 31702.0}, {'program_name': 'DIPLOMA (GRAPHIC DESIGN)', 'total_outstanding_balance': 31314.27}, {'program_name': 'BACHELOR ARTS (GA WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 30110.2}, {'program_name': 'Master Of Philosophy In Accounting (Top-Up)', 'total_outstanding_balance': 29830.0}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 29423.78}, {'program_name': 'Master Of Science In Information Technology Education - W', 'total_outstanding_balance': 29389.0}, {'program_name': 'MASTER OF PHILOSOPHY (BASIC EDUCATION)', 'total_outstanding_balance': 29080.0}, {'program_name': 'BACHELOR OF ARTS (TWI WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 27901.0}, {'program_name': 'MASTER OF PHILOSOPHY (STRATEGIC COMMUNICATION)', 'total_outstanding_balance': 27379.0}, {'program_name': 'MASTER OF ARTS (STRATEGIC COMMUNICATION)', 'total_outstanding_balance': 26143.0}, {'program_name': 'MASTER OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'total_outstanding_balance': 26093.0}, {'program_name': 'BACHELOR OF ART (DANGME WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 25931.0}, {'program_name': 'MASTER OF PHILOSOPHY (CURRICULUM & PEDAGOGIC STUDIES)', 'total_outstanding_balance': 25412.2}, {'program_name': 'BACHELOR OF ARTS (DANGME EDUCATION)', 'total_outstanding_balance': 24879.75}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 24670.0}, {'program_name': 'Master Of Technology In Electrical And Electronics Engineering', 'total_outstanding_balance': 24610.0}, {'program_name': 'BACHELOR OF ARTS (EWE WITH GERMAN EDUCATION)', 'total_outstanding_balance': 24369.74}, {'program_name': 'BACHELOR OF ARTS (EWE WITH FRENCH EDUCATION)', 'total_outstanding_balance': 23581.29}, {'program_name': 'DOCTOR OF PHILOSOPHY (SOCIAL STUDIES)', 'total_outstanding_balance': 23355.0}, {'program_name': 'MASTER OF PHILOSOPHY (CHEMISTRY EDUCATION)', 'total_outstanding_balance': 23194.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (COMMUNICATION AND MEDIA)', 'total_outstanding_balance': 21828.0}, {'program_name': 'BACHELOR OF ARTS (DAGAARE WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 21554.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (APPLIED LINGUISTICS)', 'total_outstanding_balance': 20675.0}, {'program_name': 'Bachelor Of Science (Agriculture Engineering, Technology And Innovation Education)', 'total_outstanding_balance': 20569.0}, {'program_name': 'BACHELOR OF ARTS (SISAALI EDUCATION)', 'total_outstanding_balance': 19909.0}, {'program_name': 'MASTER OF PHILOSOPHY (FRENCH)', 'total_outstanding_balance': 19850.5}, {'program_name': 'BACHELOR OF ARTS (DAGBANI WITH LINGUISTIC EDUCATION)', 'total_outstanding_balance': 19309.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'total_outstanding_balance': 19050.0}, {'program_name': 'BACHELOR OF ARTS (RELIGIONS AND MORAL STUDIES EDUCATION)', 'total_outstanding_balance': 18667.0}, {'program_name': 'MASTER OF PHILOSOPHY (INFORMATION AND COMMUNICATION TECHNOLOGY)', 'total_outstanding_balance': 18293.0}, {'program_name': 'BACHELOR OF ARTS (DAGAARE EDUCATION)', 'total_outstanding_balance': 18279.29}, {'program_name': 'BACHELOR OF ARTS (DAGBANI WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 18124.0}, {'program_name': 'BACHELOR OF ARTS (EWE WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 17581.0}, {'program_name': 'DIPLOMA (THEATRE ARTS)', 'total_outstanding_balance': 17014.0}, {'program_name': 'MASTER OF ARTS (COMMUNICATION INSTRUCTION)', 'total_outstanding_balance': 16500.0}, {'program_name': 'MASTER OF PHILOSOPHY (COMMUNICATION INSTRUCTION)', 'total_outstanding_balance': 16500.0}, {'program_name': 'Master Of Philosophy In Agronomy', 'total_outstanding_balance': 16428.0}, {'program_name': 'BACHELOR OF ARTS (GA WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 16335.0}, {'program_name': 'Master Of Technology In Automotive Engineering Technology', 'total_outstanding_balance': 15810.0}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (MANAGEMENT INFORMATION SYSTEMS)', 'total_outstanding_balance': 15743.0}, {'program_name': 'MASTER OF PHILOSOPHY (TEXTILES AND FASHION)', 'total_outstanding_balance': 15425.0}, {'program_name': 'MASTER OF PHILOSOPHY (VISUAL COMMUNICATION)', 'total_outstanding_balance': 15075.0}, {'program_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT FINANCE)', 'total_outstanding_balance': 14750.0}, {'program_name': 'Bachelor Of Science (Mathematics Education) - S', 'total_outstanding_balance': 14268.0}, {'program_name': 'MASTER OF PHILOSOPHY (SPECIAL EDUCATION)', 'total_outstanding_balance': 14125.0}, {'program_name': 'BACHELOR OF ART (DAGBANI WITH GERMAN EDUCATION)', 'total_outstanding_balance': 13956.0}, {'program_name': 'BACHELOR OF ART (DAGAARE WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 13876.0}, {'program_name': 'Diploma In Education (Competency Based Training - CBT)', 'total_outstanding_balance': 13446.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W (Top-Up)', 'total_outstanding_balance': 12962.0}, {'program_name': 'Master Of Philosophy In Accounting (Top-Up) - W', 'total_outstanding_balance': 12880.0}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology Education)', 'total_outstanding_balance': 12707.0}, {'program_name': 'Diploma In Education - TK', 'total_outstanding_balance': 12620.0}, {'program_name': 'Master Of Philosophy In Crop Science (Top-Up)', 'total_outstanding_balance': 12615.0}, {'program_name': 'MASTER OF PHILOSOPHY (FOOD AND NUTRITION)', 'total_outstanding_balance': 12511.0}, {'program_name': 'BACHELOR OF ARTS (DAGBANI EDUCATION)', 'total_outstanding_balance': 12445.0}, {'program_name': 'MASTER OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'total_outstanding_balance': 12081.29}, {'program_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'total_outstanding_balance': 11973.39}, {'program_name': 'Doctor Of Philosophy In Crop Science', 'total_outstanding_balance': 11420.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education) - S', 'total_outstanding_balance': 11410.0}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 11180.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (ARTS AND CULTURE)', 'total_outstanding_balance': 11000.0}, {'program_name': 'MASTER OF PHILOSOPHY (ENTREPRENEURSHIP AND INNOVATION)', 'total_outstanding_balance': 10850.0}, {'program_name': 'BACHELOR OF SCIENCE MIDWIFERY', 'total_outstanding_balance': 10777.0}, {'program_name': 'Doctor Of Philosophy In Construction Technology', 'total_outstanding_balance': 10365.0}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 10312.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W', 'total_outstanding_balance': 10271.0}, {'program_name': 'BACHELOR OF ART (KUSAAL WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 10102.0}, {'program_name': 'Doctor Of Philosophy In Wood Science And Technology', 'total_outstanding_balance': 9964.0}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 9746.0}, {'program_name': 'MASTER OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'total_outstanding_balance': 9400.0}, {'program_name': 'BACHELOR OF ARTS (GA EDUCATION)', 'total_outstanding_balance': 8909.0}, {'program_name': 'MASTER OF PHILOSOPHY (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES)', 'total_outstanding_balance': 8640.5}, {'program_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'total_outstanding_balance': 8469.96}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 8434.23}, {'program_name': 'MASTER OF EDUCATION (SCIENCE)', 'total_outstanding_balance': 8433.0}, {'program_name': 'MASTER OF PHILOSOPHY (THEATRE ARTS)', 'total_outstanding_balance': 8265.0}, {'program_name': 'MASTER OF PHILOSOPHY (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'total_outstanding_balance': 8091.0}, {'program_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT COMMUNICATION)', 'total_outstanding_balance': 7962.0}, {'program_name': 'Doctor Of Philosophy In Soil Fertility And Plant Nutrition', 'total_outstanding_balance': 7680.0}, {'program_name': 'Master Of Philosophy In Information Technology - W (Top-Up)', 'total_outstanding_balance': 7655.0}, {'program_name': 'MASTER OF PHILOSOPHY (HISTORY EDUCATION)', 'total_outstanding_balance': 7170.0}, {'program_name': 'EXECUTIVE MASTERS (CONFLICT HUMAN RIGHTS & PEACE STUDIES)', 'total_outstanding_balance': 7000.0}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 6952.0}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 6640.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education)', 'total_outstanding_balance': 6560.0}, {'program_name': 'MASTER OF PHILOSOPHY (FAMILY LIFE MANAGEMENT)', 'total_outstanding_balance': 6427.0}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 6287.0}, {'program_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education) - S', 'total_outstanding_balance': 6120.96}, {'program_name': 'BACHELOR OF SCIENCE (FAMILY LIFE MANAGEMENT EDUCATION)', 'total_outstanding_balance': 6066.0}, {'program_name': 'Diploma In Welding And Fabrication Engineering Technology', 'total_outstanding_balance': 5910.0}, {'program_name': 'DIPLOMA IN SPORTS COACHING', 'total_outstanding_balance': 5844.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education) -S', 'total_outstanding_balance': 5740.0}, {'program_name': 'Master Of Education In Science Education', 'total_outstanding_balance': 5700.0}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 5689.0}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 5678.0}, {'program_name': 'BACHELOR OF ARTS (KASEM EDUCATION)', 'total_outstanding_balance': 5678.0}, {'program_name': 'MASTER OF PHILOSOPHY (JOURNALISM AND MEDIA ST)', 'total_outstanding_balance': 5500.0}, {'program_name': 'MASTER OF PHILOSOPHY (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'total_outstanding_balance': 5500.0}, {'program_name': 'BACHELOR OF ARTS BUSINESS STUDIES', 'total_outstanding_balance': 5346.0}, {'program_name': 'Master Of Philosophy In Teaching And Learning', 'total_outstanding_balance': 5334.0}, {'program_name': 'Bachelor Of Science (Welding And Fabrication Technology Education', 'total_outstanding_balance': 5318.0}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology Education) - W', 'total_outstanding_balance': 5310.0}, {'program_name': 'BACHELOR OF ARTS (TWI WITH GERMAN EDUCATION)', 'total_outstanding_balance': 5252.0}, {'program_name': 'Master Of Philosophy In Mechanical Engineering Technology', 'total_outstanding_balance': 5110.0}, {'program_name': 'MASTER OF PHILOSOPHY (ECONOMICS)', 'total_outstanding_balance': 4889.0}, {'program_name': 'MASTER OF ARTS (DEVELOPMENT COMMUNICATION)', 'total_outstanding_balance': 4800.0}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH FRENCH EDUCATION)', 'total_outstanding_balance': 4758.0}, {'program_name': 'Doctor Of Philosophy In Animal Science', 'total_outstanding_balance': 4400.0}, {'program_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - S', 'total_outstanding_balance': 4222.0}, {'program_name': 'BACHELOR OF ARTS (TWI WITH FRENCH EDUCATION)', 'total_outstanding_balance': 4123.0}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH FRENCH EDUCATION)', 'total_outstanding_balance': 4113.0}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education) - S', 'total_outstanding_balance': 4110.0}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH FRENCH EDUCATION)', 'total_outstanding_balance': 4107.0}, {'program_name': 'MASTER OF PHILOSOPHY (HUMAN RESOURCE MANAGEMENT)', 'total_outstanding_balance': 4042.0}, {'program_name': 'DIPLOMA IN MUSIC', 'total_outstanding_balance': 4040.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (SPECIAL EDUCATION)', 'total_outstanding_balance': 4000.0}, {'program_name': 'DIPLOMA IN INFORMATION TECHNOLOGY', 'total_outstanding_balance': 3712.0}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 3404.0}, {'program_name': 'BACHELOR OF ARTS (GA WITH GERMAN EDUCATION)', 'total_outstanding_balance': 3384.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S (Top-Up)', 'total_outstanding_balance': 3250.0}, {'program_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'total_outstanding_balance': 3000.0}, {'program_name': 'BACHELOR OF SCIENCE IN PUBLIC HEALTH NURSING', 'total_outstanding_balance': 2997.0}, {'program_name': 'MASTER OF PHILOSOPHY (POLITICAL SCIENCE EDUCATION)', 'total_outstanding_balance': 2985.0}, {'program_name': 'MASTER OF PHILOSOPHY IN SOCIAL STUDIES', 'total_outstanding_balance': 2985.0}, {'program_name': 'Diploma In Education - CP', 'total_outstanding_balance': 2950.0}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH GERMAN EDUCATION)', 'total_outstanding_balance': 2860.0}, {'program_name': 'MASTER OF PHILOSOPHY (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'total_outstanding_balance': 2754.0}, {'program_name': 'BACHELOR  OF ARTS (EWE WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': 2700.5}, {'program_name': 'BACHELOR OF ARTS IN COMMUNICATION STUDIES', 'total_outstanding_balance': 2600.0}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH FRENCH EDUCATION)', 'total_outstanding_balance': 2570.0}, {'program_name': 'BACHELOR OF ART (DAGAARE WITH GERMAN EDUCATION)', 'total_outstanding_balance': 2558.0}, {'program_name': 'BACHELOR OF ARTS (GONJA EDUCATION)', 'total_outstanding_balance': 2549.0}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL EDUCATION)', 'total_outstanding_balance': 2548.0}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology Education) - S', 'total_outstanding_balance': 2540.0}, {'program_name': 'BACHELOR OF ARTS (GURUNE EDUCATION)', 'total_outstanding_balance': 2485.0}, {'program_name': 'DIPLOMA (TEXTILES AND FASHION)', 'total_outstanding_balance': 2385.0}, {'program_name': 'BACHELOR OF SCIENCE ECONOMICS WITH MANAGEMENT', 'total_outstanding_balance': 2380.0}, {'program_name': 'BACHELOR OF SCIENCE IN ECONOMICS', 'total_outstanding_balance': 2380.0}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Executive Office Administration)', 'total_outstanding_balance': 2300.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education)', 'total_outstanding_balance': 2173.0}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH FRENCH EDUCATION)', 'total_outstanding_balance': 2172.0}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education)', 'total_outstanding_balance': 2115.0}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education)', 'total_outstanding_balance': 2059.0}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH GERMAN EDUCATION)', 'total_outstanding_balance': 1900.0}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH FRENCH EDUCATION)', 'total_outstanding_balance': 1856.0}, {'program_name': 'MASTER OF PHILOSOPHY (CLOTHING AND TEXTILES)', 'total_outstanding_balance': 1630.0}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH LINGUISTICS EDUCATION)', 'total_outstanding_balance': 1628.74}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S', 'total_outstanding_balance': 1450.0}, {'program_name': 'BACHELOR OF ART (LINGUISTICS AND DAGAARE EDUCATION)', 'total_outstanding_balance': 1284.0}, {'program_name': 'BACHELOR OF ARTS (KUSAAL EDUCATION)', 'total_outstanding_balance': 1280.0}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH GERMAN EDUCATION)', 'total_outstanding_balance': 1274.0}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH FRENCH EDUCATION)', 'total_outstanding_balance': 1274.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND GONJA EDUCATION)', 'total_outstanding_balance': 1274.0}, {'program_name': 'MASTER OF PHILOSOPHY (MUSIC EDUCATION)', 'total_outstanding_balance': 985.0}, {'program_name': 'BACHELOR OF ARTS (GA WITH FRENCH EDUCATION)', 'total_outstanding_balance': 900.0}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH GERMAN EDUCATION)', 'total_outstanding_balance': 824.0}, {'program_name': 'Post Diploma Bachelor Of Education (Early Grade) - K', 'total_outstanding_balance': 735.0}, {'program_name': 'Master Of Philosophy In Wood Science And Technology', 'total_outstanding_balance': 663.0}, {'program_name': 'Diploma In Education - M', 'total_outstanding_balance': 610.0}, {'program_name': 'B.SC INFORMATION TECHNOLOGY', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ART (DAGBANI WITH FRENCH EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH GERMAN EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH GERMAN EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH FRENCH EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH GERMAN EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND KASEM EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH GERMAN EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS COMPUTER SCIENCE AND MANAGEMENT', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS IN DEVELOPMENT AND ENVIRONMENTAL STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS IN MUSIC', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF ARTS IN RURAL DEVELOPMENT & ECOTOURISM', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF EDUCATION (PRIMARY EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF SCIENCE (HOME ECONOMICS EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF SCIENCE COMMUNITY HEALTH NURSING', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF SCIENCE COMPUTING WITH ACTUARIAL SCIENCE', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF SCIENCE IN ACCOUNTING', 'total_outstanding_balance': 0.0}, {'program_name': 'BACHELOR OF SCIENCE MANAGEMENT AND COMPUTER STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'BSc. Business Administration', 'total_outstanding_balance': 0.0}, {'program_name': 'BSc. Business Information Technology', 'total_outstanding_balance': 0.0}, {'program_name': 'BSc. Computer Science', 'total_outstanding_balance': 0.0}, {'program_name': 'CERTIFICATE IN BUSINESS STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'CERTIFICATE IN MANAGEMENT AND COMPUTER STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'Certificate in Ministry', 'total_outstanding_balance': 0.0}, {'program_name': 'CERTIFICATE IN TRAVEL AND TOURISM MANAGEMENT', 'total_outstanding_balance': 0.0}, {'program_name': 'COMMONWEALTH LEGISLATIVE DRAFTING COURSE', 'total_outstanding_balance': 0.0}, {'program_name': 'DIPLOMA (FRENCH EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'DIPLOMA (SIGN LANGUAGE)', 'total_outstanding_balance': 0.0}, {'program_name': 'Diploma In Registered Community Nursing', 'total_outstanding_balance': 0.0}, {'program_name': 'DIPLOMA IN REGISTERED PUBLIC HEALTH NURSING', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (EDUCATIONAL LEADERSHIP)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (ENGLISH LANGUAGE)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (FRENCH)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (MUSIC EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY (PHD) THEOLOGY)', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF PHILOSOPHY IN GHANAIAN LANGUAGES', 'total_outstanding_balance': 0.0}, {'program_name': 'DOCTOR OF THEOLOGY (AFRICAN CHRISTIANITY) -INTENSIVE', 'total_outstanding_balance': 0.0}, {'program_name': 'DUMMY PROG', 'total_outstanding_balance': 0.0}, {'program_name': 'EXTENDED DIPLOMA STRATEGIC MANAGEMENT AND LEADERSHIP', 'total_outstanding_balance': 0.0}, {'program_name': 'Ghana Legal Systems / Constitutional Law Course', 'total_outstanding_balance': 0.0}, {'program_name': 'M.SC. ENVIRONMENTAL SUSTAINABILITY AND MANAGEMENT', 'total_outstanding_balance': 0.0}, {'program_name': 'MA ADULT EDUCATION', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (ART EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (ARTS AND CULTURE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (AUGUST)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (ENGLISH LANGUAGE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (GHANAIAN LANGUAGES EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (HISTORY EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS (THEATRE ARTS)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS BIBLICAL STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS HOLISTIC MISSION AND DEVELOPMENT', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS INTERNATIONAL RELATIONS', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS LEADERSHIP', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS PENTECOSTAL STUDIES', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS THEOLOGY AND MISSION', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF ARTS WORLD CHRISTIANITY OPTION', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGMENT & ORGANIZATION BEHAVIOUR)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (BIOLOGY)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (COMPUTER EDUCATION AND TECHNOLOGY)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (EARLY CHILDHOOD EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION AND MANAGEMENT) BY DISTANCE', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (FOOD AND NUTRITION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (GEOGRAPHY)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (GUIDANCE AND COUNSELLING)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (HOME ECONOMICS)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (POLITICAL SCIENCE EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (SCIENCE EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (SPECIAL EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF EDUCATION IN SCHOOL SUPERVISION AND QUALITY ASSURANCE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF PHILOSOPHY (ARTS AND CULTURE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF PHILOSOPHY (ASSESSMENT, MEASUREMENT AND EVALUATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF PHILOSOPHY (FINANCE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF PHILOSOPHY (HOME ECONOMICS)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF PHILOSOPHY COMMUNICATION AND MEDIA STUDIES   (COMMUNICATION SKILLS)', 'total_outstanding_balance': 0.0}, {'program_name': 'Master Of Philosophy In Electrical Power Systems Engineering', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF SCIENCE (BIOLOGY)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF SCIENCE (DEVELOPMENT FINANCE)', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF SCIENCE (ECONOMICS EDUCATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'Master of Theology', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF THEOLOGY (AFRICAN CHRISTIANITY) - REGULAR', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF THEOLOGY (AFRICAN CHRISTIANITY)-INTENSIVE', 'total_outstanding_balance': 0.0}, {'program_name': 'MASTER OF THEOLOGY (BIBLE TRANSLATION AND INTERPRETATION)', 'total_outstanding_balance': 0.0}, {'program_name': 'MSC LOGISTICS AND SUPPLY CHAIN MANAGEMENT', 'total_outstanding_balance': 0.0}, {'program_name': 'NURSING ACCESS COURSE', 'total_outstanding_balance': 0.0}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education) - S', 'total_outstanding_balance': 0.0}, {'program_name': 'POST GRADUATE DIPLOMA IN TEACHING AND LEARNING IN HIGHER EDUCATION', 'total_outstanding_balance': 0.0}, {'program_name': 'Preliminary Law Course', 'total_outstanding_balance': 0.0}, {'program_name': 'Master Of Philosophy In Electrical Power Systems Engineering (Top-Up)', 'total_outstanding_balance': -781.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND NZEMA EDUCATION)', 'total_outstanding_balance': -1274.0}, {'program_name': 'CERTIFICATE (PRE-SCHOOL EDUCATION)', 'total_outstanding_balance': -1274.0}, {'program_name': 'MASTER OF EDUCATION (FRENCH)', 'total_outstanding_balance': -1274.0}, {'program_name': 'MASTER OF FINE ARTS (THEATRE ARTS)', 'total_outstanding_balance': -1274.0}, {'program_name': 'BACHELOR OF ARTS (DEVELOPMENT COMMUNICATION)', 'total_outstanding_balance': -1949.0}, {'program_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'total_outstanding_balance': -2000.0}, {'program_name': 'Master Of Philosophy In Agronomy (Top-up)', 'total_outstanding_balance': -2015.0}, {'program_name': 'MASTER OF PHILOSOPHY (INTEGRATED SCIENCE EDUCATION)', 'total_outstanding_balance': -2370.0}, {'program_name': 'BACHELOR OF ART (LINGUISTIC AND EWE EDUCATION)', 'total_outstanding_balance': -2548.0}, {'program_name': 'Master Of Philosophy In Mathematics Education - W', 'total_outstanding_balance': -2553.0}, {'program_name': 'MASTER OF PHILOSOPHY (ACCOUNTING)', 'total_outstanding_balance': -2562.0}, {'program_name': 'Post Diploma Bachelor Of Science (Agricultural Science Education)', 'total_outstanding_balance': -2747.0}, {'program_name': 'Bachelor Of Science In Administration (Business Information Systems)', 'total_outstanding_balance': -2845.0}, {'program_name': 'Master Of Philosophy In Plant Pathology', 'total_outstanding_balance': -2895.0}, {'program_name': 'MASTER OF PHILOSOPHY (ASSESSMENT IN SPECIAL EDUCATION)', 'total_outstanding_balance': -2960.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND DANGME EDUCATION)', 'total_outstanding_balance': -3172.0}, {'program_name': 'MASTER OF ARTS (FRENCH TRANSLATION)', 'total_outstanding_balance': -3312.0}, {'program_name': 'BACHELOR OF ART (SISAALI EDUCATION WITH FRENCH EDUCATION)', 'total_outstanding_balance': -3378.0}, {'program_name': 'BACHELOR OF SCIENCE (INSURANCE AND RISK MANAGEMENT)', 'total_outstanding_balance': -3383.0}, {'program_name': 'MASTER OF PHILOSOPHY (PHYSICS EDUCATION)', 'total_outstanding_balance': -3417.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND GA EDUCATION)', 'total_outstanding_balance': -3764.0}, {'program_name': 'B.SC. DOC', 'total_outstanding_balance': -4000.0}, {'program_name': 'Diploma In Business Administration (Accounting) - W', 'total_outstanding_balance': -4084.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Business Information Systems)', 'total_outstanding_balance': -4298.0}, {'program_name': 'Master Of Philosophy In Biology Education', 'total_outstanding_balance': -4842.0}, {'program_name': 'BACHELOR OF EDUCATION (SPECIAL NEEDS EDUCATION)', 'total_outstanding_balance': -5096.0}, {'program_name': 'Master Of Philosophy In Construction Technology (Top-Up)', 'total_outstanding_balance': -5135.0}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH ENGLISH EDUCATION)', 'total_outstanding_balance': -5236.0}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education)', 'total_outstanding_balance': -5267.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS EDUCATION)', 'total_outstanding_balance': -5312.0}, {'program_name': 'Bachelor Of Arts (English With French Education)', 'total_outstanding_balance': -5476.0}, {'program_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY) BY DISTANCE', 'total_outstanding_balance': -6000.0}, {'program_name': 'BACHELOR OF SCIENCE (FOOD AND NUTRITION EDUCATION)', 'total_outstanding_balance': -6011.49}, {'program_name': 'Post Diploma Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'total_outstanding_balance': -6061.0}, {'program_name': 'BACHELOR OF SCIENCE IN NURSING', 'total_outstanding_balance': -6075.12}, {'program_name': 'Master Of Philosophy In Wood Science And Technology (Top-Up)', 'total_outstanding_balance': -6434.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) -- R', 'total_outstanding_balance': -6553.0}, {'program_name': 'DIPLOMA (EARLY GRADE)', 'total_outstanding_balance': -6776.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Banking And Finance) - W', 'total_outstanding_balance': -6897.0}, {'program_name': 'Diploma In Management Education', 'total_outstanding_balance': -7040.0}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance) - E', 'total_outstanding_balance': -7247.0}, {'program_name': 'BACHELOR OF ARTS (NZEMA EDUCATION)', 'total_outstanding_balance': -7397.0}, {'program_name': 'Master Of Philosophy In Construction Management (Top Up)', 'total_outstanding_balance': -7861.0}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing And Entrepreneurship) - W', 'total_outstanding_balance': -8013.0}, {'program_name': 'Master Of Philosophy In Soil Science', 'total_outstanding_balance': -8294.0}, {'program_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL SCIENCE EDUCATION)', 'total_outstanding_balance': -8856.0}, {'program_name': 'Diploma In Architecture And Digital Construction', 'total_outstanding_balance': -9330.0}, {'program_name': 'Post Diploma Bachelor Of Science (Management Education)', 'total_outstanding_balance': -9508.0}, {'program_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education)', 'total_outstanding_balance': -9628.0}, {'program_name': 'Diploma In Education (Junior High)', 'total_outstanding_balance': -9855.0}, {'program_name': 'Bachelor Of Arts (English Language Education) - M', 'total_outstanding_balance': -10150.0}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing) - R', 'total_outstanding_balance': -10176.0}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing) - W', 'total_outstanding_balance': -11463.0}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology)', 'total_outstanding_balance': -11748.0}, {'program_name': 'BACHELOR OF MUSIC', 'total_outstanding_balance': -12305.98}, {'program_name': 'Bachelor Of Science (Plumbing, And Gas Technology)', 'total_outstanding_balance': -13159.0}, {'program_name': 'Master Of Philosophy In Educational Leadership (Top-Up)', 'total_outstanding_balance': -13506.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND FANTE EDUCATION)', 'total_outstanding_balance': -14538.0}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology Education)', 'total_outstanding_balance': -14719.0}, {'program_name': 'BACHELOR OF FINE ART (ANIMATION)', 'total_outstanding_balance': -15410.0}, {'program_name': 'Bachelor Of Science (Marketing) - E', 'total_outstanding_balance': -16015.0}, {'program_name': 'Master Of Philosophy In Business Management (Top-Up)', 'total_outstanding_balance': -16920.0}, {'program_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'total_outstanding_balance': -17673.0}, {'program_name': 'BACHELOR OF LAWS', 'total_outstanding_balance': -17700.29}, {'program_name': 'Bachelor Of Science (Civil Engineering)', 'total_outstanding_balance': -18504.0}, {'program_name': 'Master Of Philosophy In Animal Science', 'total_outstanding_balance': -18736.0}, {'program_name': 'Master Of Philosophy In Automotive Engineering Technology', 'total_outstanding_balance': -18780.0}, {'program_name': 'Diploma In Automotive Engineering Technology', 'total_outstanding_balance': -20172.0}, {'program_name': 'Post Diploama Bachelor Of Science (Computerised Accounting)', 'total_outstanding_balance': -22640.0}, {'program_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'total_outstanding_balance': -22756.0}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - E', 'total_outstanding_balance': -26039.0}, {'program_name': 'Master Of Philosophy In Mechanical Engineering Technology (Top-Up)', 'total_outstanding_balance': -28167.0}, {'program_name': 'Bachelor Of Science (Marketing) - R', 'total_outstanding_balance': -28853.0}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BUSINESS INFORMATION SYSTEMS)', 'total_outstanding_balance': -29186.0}, {'program_name': 'Bachelor Of Science (Computerised Accounting)', 'total_outstanding_balance': -29624.0}, {'program_name': 'Bachelor Of Arts (Arabic With English Education)', 'total_outstanding_balance': -30035.0}, {'program_name': 'Bachelor Of Business Administration (Management) - R', 'total_outstanding_balance': -30270.0}, {'program_name': 'Diploma In Mechanical Engineering Technology', 'total_outstanding_balance': -30479.0}, {'program_name': 'Master Of Philosophy In Fashion Design And Textiles (Top-Up)', 'total_outstanding_balance': -30791.0}, {'program_name': 'Master Of Philosophy In Mathematics Education (Top-Up) - W', 'total_outstanding_balance': -32341.0}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology Education)', 'total_outstanding_balance': -32792.0}, {'program_name': 'Bachelor Of Science (Marketing And Entrepreneurship)', 'total_outstanding_balance': -32875.0}, {'program_name': 'Master Of Philosophy In Automotive Engineering Technology (Top-Up)', 'total_outstanding_balance': -33303.0}, {'program_name': 'Bachelor Of Science (Automotive Engineering Technology Education)', 'total_outstanding_balance': -33760.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - E', 'total_outstanding_balance': -33991.0}, {'program_name': 'BACHELOR OF ARTS (CREATIVE ARTS AND DESIGN EDUCATION)', 'total_outstanding_balance': -35032.0}, {'program_name': 'BACHELOR OF SCIENCE (SPORTS COACHING)', 'total_outstanding_balance': -36010.0}, {'program_name': 'Master Of Philosophy In Chemistry Education', 'total_outstanding_balance': -41041.0}, {'program_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education) - S', 'total_outstanding_balance': -41945.54}, {'program_name': 'Bachelor Of Science In Administration (Accounting) - E', 'total_outstanding_balance': -42635.0}, {'program_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education)', 'total_outstanding_balance': -44476.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND FRENCH EDUCATION)', 'total_outstanding_balance': -47952.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'total_outstanding_balance': -55157.0}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Management) - E', 'total_outstanding_balance': -56463.0}, {'program_name': 'Bachelor Of Science (Mechanical Engineering Technology Education)', 'total_outstanding_balance': -58879.0}, {'program_name': 'BACHELOR OF SCIENCE (INTEGRATED HOME ECONOMICS EDUCATION)', 'total_outstanding_balance': -58925.99}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting)', 'total_outstanding_balance': -61245.0}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance)', 'total_outstanding_balance': -62857.0}, {'program_name': 'BACHELOR OF ARTS (STRATEGIC COMMUNICATION)', 'total_outstanding_balance': -66301.4}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance) - W', 'total_outstanding_balance': -66576.0}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - W', 'total_outstanding_balance': -66795.0}, {'program_name': 'Master Of Philosophy In Mathematics Education', 'total_outstanding_balance': -67347.0}, {'program_name': 'Master Of Philosophy In Business Management - W', 'total_outstanding_balance': -72498.0}, {'program_name': 'Bachelor Of Science (Mechanical Technology Education) - S', 'total_outstanding_balance': -72711.08}, {'program_name': 'Diploma In Construction Technology', 'total_outstanding_balance': -77501.0}, {'program_name': 'Bachelor Of Science In Administration (Accounting) - W', 'total_outstanding_balance': -78913.0}, {'program_name': 'Master Of Philosophy In Catering And Hospitality (Top-Up)', 'total_outstanding_balance': -80345.0}, {'program_name': 'Doctor Of Philosophy In Educational Leadership', 'total_outstanding_balance': -80381.0}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND TWI EDUCATION)', 'total_outstanding_balance': -80988.5}, {'program_name': 'Master Of Philosophy In Catering And Hospitality', 'total_outstanding_balance': -81443.0}, {'program_name': 'BACHELOR OF SCIENCE (MATHEMATICS WITH ECONOMICS EDUCATION)', 'total_outstanding_balance': -84483.48}, {'program_name': 'Diploma In Business Administration (Management) - W', 'total_outstanding_balance': -85268.2}, {'program_name': 'Bachelor Of Business Administration (Executive Office Administration)', 'total_outstanding_balance': -88644.0}, {'program_name': 'Diploma In Catering And Hospitality', 'total_outstanding_balance': -89428.0}, {'program_name': 'Master Of Philosophy In Biology', 'total_outstanding_balance': -92785.0}, {'program_name': 'Diploma In Fashion Design And Textiles', 'total_outstanding_balance': -96557.0}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (MARKETING)', 'total_outstanding_balance': -96863.0}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Management) - W', 'total_outstanding_balance': -100803.0}, {'program_name': 'BACHELOR OF EDUCATION (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'total_outstanding_balance': -102327.0}, {'program_name': 'BACHELOR OF ARTS (THEATRE ARTS)', 'total_outstanding_balance': -102457.0}, {'program_name': 'Master Of Philosophy In Science Education', 'total_outstanding_balance': -102486.0}, {'program_name': 'Bachelor Of Science (Automotive Technology Education) - S', 'total_outstanding_balance': -102946.6}, {'program_name': 'Master Of Philosophy In Environmental And Occupational Health Education', 'total_outstanding_balance': -104822.0}, {'program_name': 'Doctor Of Philosophy In Mathematics Education', 'total_outstanding_balance': -105676.0}, {'program_name': 'Diploma In Economics', 'total_outstanding_balance': -110681.0}, {'program_name': 'Bachelor Of Arts (French With English Education)', 'total_outstanding_balance': -113567.0}, {'program_name': 'Bachelor Of Science (Marketing And Entrepreneurship) - W', 'total_outstanding_balance': -114385.0}, {'program_name': 'Master Of Philosophy In Accounting - W', 'total_outstanding_balance': -114540.0}, {'program_name': 'Bachelor Of Science (Marketing) - W', 'total_outstanding_balance': -117056.0}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (MARKETING AND ENTREPRENEURSHIP)', 'total_outstanding_balance': -118831.28}, {'program_name': 'Master Of Philosophy In Construction Technology', 'total_outstanding_balance': -128801.0}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'total_outstanding_balance': -130967.0}, {'program_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - R', 'total_outstanding_balance': -133385.0}, {'program_name': 'Diploma In Business Administration (Accounting)', 'total_outstanding_balance': -136014.0}, {'program_name': 'BACHELOR OF ARTS (FASHION DESIGN AND TEXTILE EDUCATION)', 'total_outstanding_balance': -139280.2}, {'program_name': 'Bachelor Of Science (Wood Technology With Education) - S', 'total_outstanding_balance': -141866.0}, {'program_name': 'Bachelor Of Science (Mathematics Education) - M', 'total_outstanding_balance': -142785.0}, {'program_name': 'Bachelor Of Science (Wood Technology Education)', 'total_outstanding_balance': -143739.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'total_outstanding_balance': -144164.0}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - R', 'total_outstanding_balance': -151665.7}, {'program_name': 'Master Of Philosophy In Construction Management', 'total_outstanding_balance': -154701.0}, {'program_name': 'Diploma In Electrical And Electronics Engineering Technology', 'total_outstanding_balance': -166794.0}, {'program_name': 'Master Of Philosophy In Business Management (Top-Up) - W', 'total_outstanding_balance': -169596.0}, {'program_name': 'Master Of Philosophy In Fashion Design And Textiles', 'total_outstanding_balance': -170967.0}, {'program_name': 'BACHELOR OF ART (LINGUISTICS AND ENGLISH EDUCATION)', 'total_outstanding_balance': -173571.6}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - W', 'total_outstanding_balance': -176622.0}, {'program_name': 'Bachelor Of Science (Wood Technology With Education)', 'total_outstanding_balance': -179288.0}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'total_outstanding_balance': -184055.5}, {'program_name': 'BACHELOR OF ARTS (TWI WITH ENGLISH LANGUAGE EDUCATION)', 'total_outstanding_balance': -185909.47}, {'program_name': 'Bachelor Of Business Administration (Human Resource Management) - E', 'total_outstanding_balance': -188171.0}, {'program_name': 'Master Of Philosophy In Educational Leadership', 'total_outstanding_balance': -190984.0}, {'program_name': 'BACHELOR OF SCIENCE (HEALTH ADMINISTRATION EDUCATION)', 'total_outstanding_balance': -192220.92}, {'program_name': 'BACHELOR OF EDUCATION (SPECIAL EDUCATION)', 'total_outstanding_balance': -195855.74}, {'program_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'total_outstanding_balance': -209633.0}, {'program_name': 'BACHELOR OF ARTS (MUSIC EDUCATION)', 'total_outstanding_balance': -210186.8}, {'program_name': 'BACHELOR OF ARTS (TWI EDUCATION)', 'total_outstanding_balance': -214470.89}, {'program_name': 'Bachelor Of Arts (Arabic Education)', 'total_outstanding_balance': -224815.9}, {'program_name': 'Bachelor Of Science (Mechanical Technology Education)', 'total_outstanding_balance': -227857.0}, {'program_name': 'Bachelor Of Business Administration (Secretarial Education)', 'total_outstanding_balance': -229911.0}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'total_outstanding_balance': -247808.0}, {'program_name': 'Master Of Philosophy In Business Management', 'total_outstanding_balance': -253294.0}, {'program_name': 'Bachelor Of Education (Early Grade) - K', 'total_outstanding_balance': -255078.5}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (FINANCE)', 'total_outstanding_balance': -295997.0}, {'program_name': 'Bachelor Of Science (Mathematics Education) - W', 'total_outstanding_balance': -307064.1}, {'program_name': 'Bachelor Of Science (Information Technology)', 'total_outstanding_balance': -326586.8}, {'program_name': 'Bachelor Of Science (Natural Resources Management And Education)', 'total_outstanding_balance': -327682.0}, {'program_name': 'Bachelor Of Science (Physics Education)', 'total_outstanding_balance': -382790.4}, {'program_name': 'Bachelor Of Science (Wood Technology Education) - S', 'total_outstanding_balance': -383020.24}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'total_outstanding_balance': -394610.0}, {'program_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'total_outstanding_balance': -394933.43}, {'program_name': 'Master Of Business Administration (Accounting)', 'total_outstanding_balance': -417306.0}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'total_outstanding_balance': -420024.66}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'total_outstanding_balance': -436644.0}, {'program_name': 'BACHELOR OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'total_outstanding_balance': -437202.15}, {'program_name': 'Bachelor Of Science In Administration (Accounting)', 'total_outstanding_balance': -465804.0}, {'program_name': 'Bachelor Of Theology', 'total_outstanding_balance': -484000.0}, {'program_name': 'BACHELOR OF ARTS (COMMUNICATION STUDIES)', 'total_outstanding_balance': -485626.0}, {'program_name': 'Master Of Philosophy In Accounting', 'total_outstanding_balance': -569404.0}, {'program_name': 'BACHELOR OF SCIENCE (BIOLOGY EDUCATION)', 'total_outstanding_balance': -569535.0}, {'program_name': 'Bachelor Of Science (Agribusiness Management And Entrepreneurship Education)', 'total_outstanding_balance': -570265.0}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'total_outstanding_balance': -573447.7}, {'program_name': 'Diploma In Business Administration (Management)', 'total_outstanding_balance': -600147.5}, {'program_name': 'BACHELOR OF SCIENCE (PHYSICAL EDUCATION)', 'total_outstanding_balance': -601761.18}, {'program_name': 'BACHELOR OF ARTS (HISTORY EDUCATION)', 'total_outstanding_balance': -616154.25}, {'program_name': 'Bachelor Of Business Administration (Management) - W', 'total_outstanding_balance': -623100.0}, {'program_name': 'Bachelor Of Arts (Economics With Social Studies Education)', 'total_outstanding_balance': -660686.0}, {'program_name': 'BACHELOR OF ARTS (GRAPHIC DESIGN)', 'total_outstanding_balance': -751279.0}, {'program_name': 'Bachelor Of Science (Chemistry Education)', 'total_outstanding_balance': -780175.04}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'total_outstanding_balance': -823623.23}, {'program_name': 'Bachelor Of Business Administration (Human Resource Management) - W', 'total_outstanding_balance': -845422.0}, {'program_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'total_outstanding_balance': -972330.9}, {'program_name': 'Master Of Business Administration (Human Resource Management And Organisational Behaviour ) - Weekend', 'total_outstanding_balance': -972686.0}, {'program_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - S', 'total_outstanding_balance': -998550.0}, {'program_name': 'Master Of Divinity', 'total_outstanding_balance': -1107000.0}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - R', 'total_outstanding_balance': -1127030.85}, {'program_name': 'Bachelor Of Business Administration (Management) - E', 'total_outstanding_balance': -1175070.0}, {'program_name': 'Master Of Arts In Ministry', 'total_outstanding_balance': -1344800.0}, {'program_name': 'Bachelor Of Arts (Social Studies With Economics Education)', 'total_outstanding_balance': -1356118.0}, {'program_name': 'Bachelor Of Education (Early Grade) - M', 'total_outstanding_balance': -1397293.0}, {'program_name': 'Bachelor Of Arts (French Education)', 'total_outstanding_balance': -1414263.16}, {'program_name': 'BACHELOR OF ARTS (ART EDUCATION)', 'total_outstanding_balance': -1420622.44}, {'program_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - R', 'total_outstanding_balance': -1509123.5}, {'program_name': 'Bachelor Of Arts (Economics Education)', 'total_outstanding_balance': -1619441.7}, {'program_name': 'Bachelor Of Science (Environmental Health And Sanitation Education)', 'total_outstanding_balance': -1621931.0}, {'program_name': 'Bachelor Of Science (Information Technology Education) - W', 'total_outstanding_balance': -1722763.0}, {'program_name': 'Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'total_outstanding_balance': -1787301.0}, {'program_name': 'BACHELOR OF ARTS (GEOGRAPHY EDUCATION)', 'total_outstanding_balance': -1944012.18}, {'program_name': 'BACHELOR OF ART (SOCIAL STUDIES EDUCATION)', 'total_outstanding_balance': -2073296.4}, {'program_name': 'Bachelor Of Science (Catering And Hospitality Education) - S', 'total_outstanding_balance': -2085830.62}, {'program_name': 'Bachelor Of Science (Biological Sciences Education)', 'total_outstanding_balance': -2143412.76}, {'program_name': 'BACHELOR OF ARTS (POLITICAL SCIENCE EDUCATION)', 'total_outstanding_balance': -2187471.11}, {'program_name': 'Bachelor Of Education (Upper Primary)', 'total_outstanding_balance': -2304386.46}, {'program_name': 'Bachelor Of Science (Agricultural Science Education)', 'total_outstanding_balance': -2312207.44}, {'program_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'total_outstanding_balance': -2512316.0}, {'program_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'total_outstanding_balance': -2587231.24}, {'program_name': 'Bachelor Of Education (Junior High)', 'total_outstanding_balance': -2717689.0}, {'program_name': 'Bachelor Of Science (Integrated Science Education)', 'total_outstanding_balance': -2876327.01}, {'program_name': 'Bachelor Of Science (Catering And Hospitality Education) - R', 'total_outstanding_balance': -2886185.5}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'total_outstanding_balance': -3203971.93}, {'program_name': 'Bachelor Of Science (Accounting Education)', 'total_outstanding_balance': -3237626.2}, {'program_name': 'Bachelor Of Science (Management Education)', 'total_outstanding_balance': -3841907.74}, {'program_name': 'POST CALL LAW COURSE', 'total_outstanding_balance': -4415962.9}, {'program_name': 'Bachelor Of Arts (English Language Education)', 'total_outstanding_balance': -4683021.73}, {'program_name': 'Bachelor Of Science (Mathematics Education)', 'total_outstanding_balance': -5643488.68}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'total_outstanding_balance': -7883860.83}, {'program_name': 'Bachelor Of Science (Information Technology Education)', 'total_outstanding_balance': -********.4}, {'program_name': 'PROFESSIONAL LAW COURSE', 'total_outstanding_balance': -*********.96}]
2025-08-09 14:16:55,275 - celery.redirected - WARNING - ================================= 
2025-08-09 14:16:55,275 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: total_outstanding_balances_by_program
2025-08-09 14:16:55,275 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 14:16:55,275 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:16:55,275 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:16:55,275 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:16:55,275 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:16:55,275 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:16:55,275 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:16:55,275 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:16:55,275 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:16:55,276 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What percentage of students at the institution where students owe the most fees are currently in arr...
2025-08-09 14:16:55,276 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At the institution where students owe the most fees, 0.0% of students are currently in arrears. This...
2025-08-09 14:16:55,276 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:16:55,276 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'percentage_in_arrears': 0.0}]...
2025-08-09 14:16:55,276 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: percentage_of_students_in_arrears_at_top_fee_institution
2025-08-09 14:16:55,277 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 14:16:55,277 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 14:16:55,277 - celery.redirected - WARNING - [{'percentage_in_arrears': 0.0}]
2025-08-09 14:16:55,277 - celery.redirected - WARNING - ================================= 
2025-08-09 14:16:55,278 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 14:16:55,278 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:16:55,278 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:16:55,278 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:16:55,278 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:16:55,278 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:16:55,278 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:16:55,279 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:16:55,279 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:16:55,279 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the payment history of students at the institution where students owe the most fees?...
2025-08-09 14:16:55,279 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no records available regarding the payment history of students at the inst...
2025-08-09 14:16:55,279 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:16:55,279 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 14:16:55,279 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: payment_history_no_data
2025-08-09 14:16:55,279 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 14:16:55,279 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 14:16:55,279 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 8
2025-08-09 14:16:55,279 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:16:55,279 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-09 14:16:55,279 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:16:55,279 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 8 documents
2025-08-09 14:16:55,279 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-09 14:16:55,279 - UPSERT_DOCS - INFO -     Content: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:16:55,279 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:14:58.765760+00:00', 'data_returned': True}
2025-08-09 14:16:55,279 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-09 14:16:55,279 - UPSERT_DOCS - INFO -     Content: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:16:55,279 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:14:44.510425+00:00', 'data_returned': True}
2025-08-09 14:16:55,279 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -     Content: Question: What types of fees are included in the total amount owed at the institution where students...
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:15:37.834817+00:00', 'data_returned': True}
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -     Content: Question: What is the average fee amount owed per student at the institution where students owe the ...
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:16:32.304003+00:00', 'data_returned': False}
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -     Content: Question: What is the average fee amount owed per student at the institution where students owe the ...
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-09T14:16:32.304003+00:00', 'data_returned': True}
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -     Content: Question: Are there specific programs or departments within the institution where students owe the m...
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:14:47.487169+00:00', 'data_returned': True, 'data_tag': 'total_outstanding_balances_by_program'}
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -     Content: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 14:16:55,280 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:14:41.454604+00:00', 'data_returned': True}
2025-08-09 14:16:55,281 - UPSERT_DOCS - INFO -   📄 Doc 8:
2025-08-09 14:16:55,281 - UPSERT_DOCS - INFO -     Content: Question: What is the payment history of students at the institution where students owe the most fee...
2025-08-09 14:16:55,281 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:15:08.060545+00:00', 'data_returned': False}
2025-08-09 14:16:55,281 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 6/8
2025-08-09 14:16:55,281 - elastic_transport.node_pool - INFO - Resurrected node <Urllib3HttpNode(http://54.246.247.31:9200)> (force=False)
2025-08-09 14:16:55,409 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:N/A duration:0.127s]
2025-08-09 14:16:55,409 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://54.246.247.31:9200)> has failed for 2 times in a row, putting on 2 second timeout
2025-08-09 14:16:55,409 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 0 of 3)
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/connection.py", line 85, in create_connection
    raise err
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_node/_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
               ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/util.py", line 39, in reraise
    raise value
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 493, in _make_request
    conn.request(
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 445, in request
    self.endheaders()
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 1331, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 1091, in _send_output
    self.send(msg)
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 1035, in send
    self.connect()
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 276, in connect
    self.sock = self._new_conn()
                ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x10e214bc0>: Failed to establish a new connection: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_transport.py", line 342, in perform_request
    resp = node.perform_request(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_node/_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x10e214bc0>: Failed to establish a new connection: [Errno 61] Connection refused)
2025-08-09 14:16:55,545 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:N/A duration:0.128s]
2025-08-09 14:16:55,545 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://54.246.247.31:9200)> has failed for 3 times in a row, putting on 4 second timeout
2025-08-09 14:16:55,545 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 1 of 3)
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/connection.py", line 85, in create_connection
    raise err
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_node/_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
               ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/util.py", line 39, in reraise
    raise value
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 493, in _make_request
    conn.request(
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 445, in request
    self.endheaders()
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 1331, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 1091, in _send_output
    self.send(msg)
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 1035, in send
    self.connect()
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 276, in connect
    self.sock = self._new_conn()
                ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x10e214f20>: Failed to establish a new connection: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_transport.py", line 342, in perform_request
    resp = node.perform_request(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_node/_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x10e214f20>: Failed to establish a new connection: [Errno 61] Connection refused)
2025-08-09 14:16:55,673 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:N/A duration:0.126s]
2025-08-09 14:16:55,674 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://54.246.247.31:9200)> has failed for 4 times in a row, putting on 8 second timeout
2025-08-09 14:16:55,674 - elastic_transport.transport - WARNING - Retrying request after failure (attempt 2 of 3)
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 198, in _new_conn
    sock = connection.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/connection.py", line 85, in create_connection
    raise err
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/connection.py", line 73, in create_connection
    sock.connect(sa)
ConnectionRefusedError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_node/_http_urllib3.py", line 167, in perform_request
    response = self.pool.urlopen(
               ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
              ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/retry.py", line 449, in increment
    raise reraise(type(error), error, _stacktrace)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/util/util.py", line 39, in reraise
    raise value
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
               ^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py", line 493, in _make_request
    conn.request(
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 445, in request
    self.endheaders()
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 1331, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 1091, in _send_output
    self.send(msg)
  File "/opt/anaconda3/lib/python3.12/http/client.py", line 1035, in send
    self.connect()
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 276, in connect
    self.sock = self._new_conn()
                ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/urllib3/connection.py", line 213, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPConnection object at 0x10e216780>: Failed to establish a new connection: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_transport.py", line 342, in perform_request
    resp = node.perform_request(
           ^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/elastic_transport/_node/_http_urllib3.py", line 202, in perform_request
    raise err from e
elastic_transport.ConnectionError: Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x10e216780>: Failed to establish a new connection: [Errno 61] Connection refused)
2025-08-09 14:16:55,810 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:N/A duration:0.133s]
2025-08-09 14:16:55,810 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://54.246.247.31:9200)> has failed for 5 times in a row, putting on 16 second timeout
2025-08-09 14:16:55,812 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: Connection error caused by: ConnectionError(Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x10e214bc0>: Failed to establish a new connection: [Errno 61] Connection refused))
2025-08-09 14:16:55,812 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:16:55,812 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-09 14:16:55,812 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:16:55,813 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: Connection error caused by: ConnectionError(Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x10e214bc0>: Failed to establish a new connection: [Errno 61] Connection refused))
2025-08-09 14:16:55,813 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250809_135654.log
2025-08-09 14:16:55,814 - celery.app.trace - INFO - Task generate_streaming_report[ec78fc47-e289-4c4c-bf69-d7fcdcb457c6] succeeded in 353.39229220799825s: {'error': 'Error generating streaming report: Connection error caused by: ConnectionError(Connection error caused by: NewConnectionError(<urllib3.connection.HTTPConnection object at 0x10e214bc0>: Failed to establish a new connection: [Errno 61] Connection refused))'}

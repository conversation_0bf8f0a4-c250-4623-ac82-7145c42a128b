2025-08-08 01:20:23,503 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_012023.log
2025-08-08 01:20:23,503 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:20:23,504 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 6c28e074-279a-41ad-8db1-a0649ff7220d
2025-08-08 01:20:23,504 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:20:23,504 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:20:23,504 - REPORT_REQUEST - INFO - 🆔 Task ID: 6c28e074-279a-41ad-8db1-a0649ff7220d
2025-08-08 01:20:23,504 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T01:20:23.504123
2025-08-08 01:20:23,765 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.261s]
2025-08-08 01:20:23,765 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 29
2025-08-08 01:20:24,165 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.400s]
2025-08-08 01:20:24,165 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:20:24,497 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.332s]
2025-08-08 01:20:24,497 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (4):
2025-08-08 01:20:24,497 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (18 docs)
2025-08-08 01:20:24,497 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 01:20:24,497 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (3 docs)
2025-08-08 01:20:24,497 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing in ITC University?' (1 docs)
2025-08-08 01:20:24,497 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 01:20:39,039 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:39,060 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 01:20:43,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:43,829 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 01:20:46,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:50,404 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:50,592 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:50,787 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:50,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:50,976 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:51,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:51,462 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:51,837 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:52,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:52,352 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 01:20:52,352 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:20:52,352 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 01:20:52,352 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:20:52,353 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 4
2025-08-08 01:20:58,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:58,165 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:58,466 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:20:58,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:00,848 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:01,314 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:01,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:02,702 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:03,009 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:03,181 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:03,714 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:05,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:07,945 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:08,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:11,088 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:12,569 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:13,601 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:13,605 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic results of girls compare to those of boys at ITC University?', 'sql': "SELECT sex, AVG(assessment_total) AS average_assessment_total, AVG(exam_total) AS average_exam_total, AVG(finalscore) AS average_finalscore, COUNT(*) AS total_students FROM assessment_results ar JOIN students s ON ar.student_id = s.id WHERE s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University') GROUP BY sex;", 'correct': True, 'reasoning': 'The SQL query correctly retrieves the average academic results (assessment_total, exam_total, finalscore) for both girls and boys at ITC University by grouping the results by sex. It also counts the total number of students in each group, which provides a comparative analysis of the academic performance between genders. The use of a subquery to filter by institution ensures that only results from ITC University are considered.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be enhanced by including additional metrics or context, such as the distribution of grades or a comparison of the highest and lowest scores, to provide a more comprehensive view of the academic performance differences between genders.'}
2025-08-08 01:21:13,606 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:21:13,606 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 01:21:16,413 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:16,419 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any notable trends in the performance of girls at ITC University over the past few academic years?', 'sql': "SELECT academic_years.start_year, AVG(assessment_results.finalscore) AS average_score, COUNT(assessment_results.id) AS total_assessments\nFROM assessment_results\nJOIN students ON assessment_results.student_id = students.id\nJOIN academic_years ON assessment_results.semester_id = academic_years.id\nWHERE students.sex = 'F' AND academic_years.status = 'Active'\nGROUP BY academic_years.start_year\nORDER BY academic_years.start_year;", 'correct': True, 'reasoning': "The SQL query correctly identifies the average performance of female students (girls) at ITC University by calculating the average final score from the assessment results. It filters the data to include only female students and considers only active academic years. The grouping by start year allows for the analysis of trends over multiple years, which aligns with the question's request for notable trends in performance. The inclusion of the total number of assessments also provides context for the average score, which is relevant for understanding the significance of the trends.", 'feedback': "The question could be clarified by specifying what is meant by 'notable trends'—for example, whether it refers to increases or decreases in performance, or specific thresholds for what constitutes a notable trend. Additionally, the SQL could be improved by including a comparison with male students or overall performance to provide a more comprehensive view of trends."}
2025-08-08 01:21:16,419 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:21:16,419 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 01:21:17,034 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:17,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:18,357 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:19,017 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:19,485 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:19,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:19,493 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University enrolled in, and how do their performances vary across these programs?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(ar.finalscore) AS average_score, COUNT(s.id) AS number_of_students\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.assessment_results ar ON sp.id = ar.student_program_id\nJOIN core.courses c ON ar.course_id = c.id\nWHERE s.sex = 'F'\nGROUP BY p.long_name, c.title\nORDER BY p.long_name, c.title;", 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses that female students (girls) at ITC University are enrolled in. It joins the necessary tables: students, student_programs, programs, assessment_results, and courses. The query filters for female students and groups the results by program name and course title, calculating the average score and the number of students for each group. This aligns with the question's request for information on enrollment and performance variation.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly mentioning 'ITC University' in the WHERE clause if there is a specific institution_id for ITC University in the schema, to ensure that the results are limited to that institution."}
2025-08-08 01:21:19,494 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:21:19,494 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 01:21:20,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:20,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:21,430 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:22,074 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:23,055 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:23,990 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:24,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:24,681 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:26,037 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:26,050 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and academic results, there are no fields that directly address gender differences in performance or the factors influencing them. Therefore, without additional data or analysis capabilities, this question cannot be answered from the schema.', 'feedback': ''}
2025-08-08 01:21:26,050 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:21:26,050 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:21:26,050 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and academic results, there are no fields that directly address gender differences in performance or the factors influencing them. Therefore, without additional data or analysis capabilities, this question cannot be answered from the schema.', 'feedback': ''}
2025-08-08 01:21:26,663 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:26,677 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could be taken to improve data collection on the performance of girls at ITC University, and how might this data be utilized to enhance their academic experience?', 'answerable': False, 'reasoning': 'The question is focused on improving data collection and utilizing that data to enhance the academic experience of girls at ITC University. However, the provided schema does not contain specific information or tables related to gender-specific performance metrics, data collection strategies, or academic experience enhancement initiatives. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks the necessary context or data points to address the qualitative aspects of the question.', 'feedback': ''}
2025-08-08 01:21:26,678 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:21:26,678 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:21:26,678 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could be taken to improve data collection on the performance of girls at ITC University, and how might this data be utilized to enhance their academic experience?', 'answerable': False, 'reasoning': 'The question is focused on improving data collection and utilizing that data to enhance the academic experience of girls at ITC University. However, the provided schema does not contain specific information or tables related to gender-specific performance metrics, data collection strategies, or academic experience enhancement initiatives. The schema primarily consists of tables related to institutions, students, courses, and various administrative aspects, but lacks the necessary context or data points to address the qualitative aspects of the question.', 'feedback': ''}
2025-08-08 01:21:28,189 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:29,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:29,215 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:30,443 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:33,104 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:35,344 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:35,363 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the graduation rates for girls at ITC University compared to their male counterparts?', 'sql': "SELECT sex, COUNT(*) AS graduation_count\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.graduation_clearance gc ON sp.id = gc.student_program_id\nWHERE gc.status = 'cleared' AND s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY sex;", 'correct': True, 'reasoning': "The SQL query correctly identifies the graduation rates by counting the number of students who have a 'cleared' status in the graduation clearance table, grouped by their sex. It also filters the results to only include students from 'ITC University' by using a subquery to find the institution's ID. This directly addresses the question of comparing graduation rates between girls and boys at the specified university.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly labeling the output columns for clarity, such as renaming 'sex' to 'gender' and 'graduation_count' to 'number_of_graduates'. Additionally, including the total number of students by gender for a more comprehensive comparison could enhance the analysis."}
2025-08-08 01:21:35,363 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:21:35,363 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 01:21:35,411 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:35,422 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance among girls across these various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences among girls across various programs at ITC University. However, the schema does not provide specific data on performance factors or demographic breakdowns by gender for the programs. While there are tables related to students, programs, and performance metrics (like grades), there is no direct link or data that would allow for a comprehensive analysis of the factors affecting performance specifically for girls across different programs. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 01:21:35,423 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:21:35,423 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:21:35,423 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in performance among girls across these various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to performance differences among girls across various programs at ITC University. However, the schema does not provide specific data on performance factors or demographic breakdowns by gender for the programs. While there are tables related to students, programs, and performance metrics (like grades), there is no direct link or data that would allow for a comprehensive analysis of the factors affecting performance specifically for girls across different programs. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 01:21:38,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:39,378 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:40,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:42,782 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:45,084 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:45,099 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the equal graduation rates for both genders at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to equal graduation rates for both genders at ITC University. However, the provided schema does not contain specific data or metrics related to graduation rates, gender comparisons, or any factors influencing these rates. While there are tables related to students, programs, and graduation, there is no direct information or analytical capability in the schema to assess or compare graduation rates by gender or to identify contributing factors.', 'feedback': ''}
2025-08-08 01:21:45,100 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:21:45,100 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:21:45,100 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the equal graduation rates for both genders at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to equal graduation rates for both genders at ITC University. However, the provided schema does not contain specific data or metrics related to graduation rates, gender comparisons, or any factors influencing these rates. While there are tables related to students, programs, and graduation, there is no direct information or analytical capability in the schema to assess or compare graduation rates by gender or to identify contributing factors.', 'feedback': ''}
2025-08-08 01:21:45,100 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 01:21:45,100 - root - INFO - 'No results'
2025-08-08 01:21:45,101 - root - INFO - 'No results'
2025-08-08 01:21:45,101 - root - INFO - 'No results'
2025-08-08 01:21:45,101 - root - INFO - 'No results'
2025-08-08 01:21:45,101 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-08 01:21:45,101 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 4
2025-08-08 01:21:45,101 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 01:21:54,095 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:21:54,100 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 01:22:07,045 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:07,052 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:07,052 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 01:22:07,052 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:07,052 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/4
2025-08-08 01:22:07,052 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:07,052 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:22:07,052 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:07,052 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 01:22:07,052 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:22:07,052 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:22:07,052 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 01:22:07,052 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:22:07,052 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you believe contribute to the higher academic performance of girls compared to boys ...
2025-08-08 01:22:07,052 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:22:07,053 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:22:07,053 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/4
2025-08-08 01:22:07,053 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:07,053 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:22:07,053 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:22:07,053 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you believe contribute to the differences in performance among girls across these va...
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:22:07,053 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:22:07,054 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:22:07,054 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/4
2025-08-08 01:22:07,054 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:07,054 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:22:07,054 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:07,054 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 01:22:07,054 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:22:07,054 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:22:07,054 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 01:22:07,054 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:22:07,054 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What steps could be taken to improve data collection on the performance of girls at ITC University, ...
2025-08-08 01:22:07,054 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:22:07,054 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:22:07,055 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:22:07,055 - REPORT_PIPELINE - INFO - 🔄 Processing interview 4/4
2025-08-08 01:22:07,055 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:07,055 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:22:07,055 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:22:07,055 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you believe contribute to the equal graduation rates for both genders at ITC Univers...
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:22:07,055 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:22:07,056 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:22:07,056 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:22:07,056 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:22:07,056 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 4
2025-08-08 01:22:07,056 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:07,056 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 01:22:07,056 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 4 documents
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -     Content: Question: What factors do you believe contribute to the higher academic performance of girls compare...
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:21:26.050741+00:00', 'data_returned': False}
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -     Content: Question: What factors do you believe contribute to the differences in performance among girls acros...
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:21:35.423660+00:00', 'data_returned': False}
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -     Content: Question: What steps could be taken to improve data collection on the performance of girls at ITC Un...
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:21:26.678490+00:00', 'data_returned': False}
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -     Content: Question: What factors do you believe contribute to the equal graduation rates for both genders at I...
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:21:45.100367+00:00', 'data_returned': False}
2025-08-08 01:22:07,056 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 0/4
2025-08-08 01:22:07,191 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.135s]
2025-08-08 01:22:08,022 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:22:12,526 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:4.438s]
2025-08-08 01:22:12,527 - UPSERT_DOCS - INFO - ✅ Successfully upserted 4 documents to Elasticsearch
2025-08-08 01:22:13,552 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.024s]
2025-08-08 01:22:13,552 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 33
2025-08-08 01:22:13,800 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.247s]
2025-08-08 01:22:13,800 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:22:14,267 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.466s]
2025-08-08 01:22:14,268 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (4):
2025-08-08 01:22:14,268 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (18 docs)
2025-08-08 01:22:14,268 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (7 docs)
2025-08-08 01:22:14,268 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 01:22:14,268 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing in ITC University?' (1 docs)
2025-08-08 01:22:14,269 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 8
2025-08-08 01:22:14,269 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 01:22:14,270 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:14,270 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-08 01:22:14,270 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:14,270 - REPORT_PIPELINE - INFO - ✍️ Writing 8 sections using batch processing...
2025-08-08 01:22:14,984 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:14,988 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:14,988 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:22:14,988 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:14,988 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'improving support for female students'
2025-08-08 01:22:14,988 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 01:22:14,989 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:22:14,992 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:15,001 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:15,001 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:22:15,002 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:15,002 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'gender performance education conclusion'
2025-08-08 01:22:15,002 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,002 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,051 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:15,056 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:15,059 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:15,060 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:22:15,060 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:15,060 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'girls academic performance ITC University'
2025-08-08 01:22:15,060 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,060 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,065 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:15,065 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:22:15,065 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:15,065 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'interview methodology data collection criteria'
2025-08-08 01:22:15,065 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,065 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,097 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:15,107 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:15,108 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:15,109 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:22:15,109 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:15,109 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Comparative analysis of gender experiences and performance at ITC University'
2025-08-08 01:22:15,109 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,109 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,119 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:15,120 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:22:15,120 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:15,120 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Appendices interview transcripts data resources'
2025-08-08 01:22:15,120 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,120 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:15,170 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.181s]
2025-08-08 01:22:15,170 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:22:15,172 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:15,172 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:22:15,173 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:15,173 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'sources and literature review'
2025-08-08 01:22:15,173 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,173 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:15,261 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:15,261 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:22:15,261 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:15,261 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'academic performance female students ITC University'
2025-08-08 01:22:15,261 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,262 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:22:15,355 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.289s]
2025-08-08 01:22:15,355 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:22:15,356 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.186s]
2025-08-08 01:22:15,357 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:22:15,358 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.356s]
2025-08-08 01:22:15,358 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:22:15,458 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.398s]
2025-08-08 01:22:15,458 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:22:15,596 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.241s]
2025-08-08 01:22:15,597 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:22:15,627 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.269s]
2025-08-08 01:22:15,627 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:22:15,633 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.524s]
2025-08-08 01:22:15,633 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:22:15,959 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.839s]
2025-08-08 01:22:15,960 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:22:16,168 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.535s]
2025-08-08 01:22:16,169 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:22:16,499 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.237s]
2025-08-08 01:22:16,499 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:22:16,608 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.435s]
2025-08-08 01:22:16,609 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:22:16,829 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.660s]
2025-08-08 01:22:16,830 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 01:22:16,887 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.927s]
2025-08-08 01:22:16,887 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:22:16,897 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.540s]
2025-08-08 01:22:16,897 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 01:22:17,040 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.581s]
2025-08-08 01:22:17,040 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:22:17,164 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.664s]
2025-08-08 01:22:17,164 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:22:17,169 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.560s]
2025-08-08 01:22:17,169 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:22:17,278 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.390s]
2025-08-08 01:22:17,278 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 01:22:17,546 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.505s]
2025-08-08 01:22:17,546 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 01:22:17,555 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.390s]
2025-08-08 01:22:17,555 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 01:22:17,557 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.960s]
2025-08-08 01:22:17,557 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 01:22:17,568 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.398s]
2025-08-08 01:22:17,568 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 01:22:17,642 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:2.014s]
2025-08-08 01:22:17,642 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 01:22:17,982 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.703s]
2025-08-08 01:22:17,982 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:22:18,221 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.674s]
2025-08-08 01:22:18,221 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:22:18,221 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.666s]
2025-08-08 01:22:18,222 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:22:18,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:22:18,437 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.868s]
2025-08-08 01:22:18,437 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.794s]
2025-08-08 01:22:18,437 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:22:18,437 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:22:18,460 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.903s]
2025-08-08 01:22:18,461 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.631s]
2025-08-08 01:22:18,461 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:1.563s]
2025-08-08 01:22:18,461 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:22:18,461 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:22:18,461 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:22:18,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:22:18,777 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:22:18,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:22:18,877 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:22:18,994 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:22:19,023 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:22:19,151 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:22:21,291 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:2.884s]
2025-08-08 01:22:21,292 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:22:21,292 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:22:21,843 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:3.166s]
2025-08-08 01:22:21,844 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:22:21,844 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:22:21,949 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:3.167s]
2025-08-08 01:22:21,949 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:22:21,949 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:22:22,560 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:3.677s]
2025-08-08 01:22:22,560 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:22:22,561 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:22:22,761 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:3.881s]
2025-08-08 01:22:22,762 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:22:22,762 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:22:23,834 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:4.836s]
2025-08-08 01:22:23,835 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:22:23,835 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:22:23,836 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:4.810s]
2025-08-08 01:22:23,837 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:22:23,837 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:22:23,989 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:4.834s]
2025-08-08 01:22:23,990 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 01:22:23,990 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 01:22:25,429 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:26,109 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:26,973 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:27,375 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:28,296 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:28,401 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:28,604 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:28,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:28,688 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 390 characters
2025-08-08 01:22:28,689 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1697 characters
2025-08-08 01:22:28,689 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1737 characters
2025-08-08 01:22:28,690 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 1540 characters
2025-08-08 01:22:28,690 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1591 characters
2025-08-08 01:22:28,690 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 957 characters
2025-08-08 01:22:28,691 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 555 characters
2025-08-08 01:22:28,691 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 616 characters
2025-08-08 01:22:31,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:22:31,068 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:22:31,068 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:22:31,068 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:22:31,068 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:22:31,068 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:22:31,068 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:22:31,068 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:22:31,068 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:22:31,068 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 01:22:31,068 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:22:31,068 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-08 01:22:31,069 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:22:31,069 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-08 01:22:31,069 - REPORT_PIPELINE - INFO - 📊 Final report sections: 9
2025-08-08 01:22:31,069 - REPORT_PIPELINE - INFO - 📈 Extended data items: 0
2025-08-08 01:22:31,069 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_012023.log
2025-08-08 01:22:31,072 - celery.app.trace - INFO - Task generate_report[4c188de9-019b-4d02-80ef-c94c23081d62] succeeded in 127.57294725000247s: {'outline': '# Report on Girls\' Performance at ITC University

## Introduction  
The purpose of this report is to evaluate the academic performance of female students at ITC University, focusing on their achievements, challenges, and the support systems available to them. The key finding indicates that while girls are performing well academically, they face significant challenges that impact their overall experience and participation in IT programs.

## Methodology  
- Description of the interview process  
- Criteria for selecting interview participants  
- Overview of data collection methods, including surveys and academic records analysis  
- Sample size and demographics  
- Limitations of the study  

## Key Themes and Insights  
### Academic Performance  
- Overview of girls\' academic achievements at ITC University  
- Comparison of grades and GPA with male counterparts  
- Course completion rates  

### Participation in IT Programs  
- Enrollment statistics for girls in IT programs  
- Trends in...', , ...}

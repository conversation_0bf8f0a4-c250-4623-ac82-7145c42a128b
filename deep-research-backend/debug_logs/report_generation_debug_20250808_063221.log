2025-08-08 06:32:21,321 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_063221.log
2025-08-08 06:32:21,321 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:32:21,321 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 037137bd-f499-4edc-8407-56fcad839d71
2025-08-08 06:32:21,321 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:32:21,321 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:32:21,321 - REPORT_REQUEST - INFO - 🆔 Task ID: 037137bd-f499-4edc-8407-56fcad839d71
2025-08-08 06:32:21,321 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T06:32:21.321850
2025-08-08 06:32:21,547 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.226s]
2025-08-08 06:32:21,547 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-08 06:32:21,683 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 06:32:21,683 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:32:21,907 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.223s]
2025-08-08 06:32:21,907 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-08 06:32:21,908 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 06:32:31,242 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:31,265 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 06:32:35,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:35,563 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 06:32:42,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:43,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:43,188 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:43,290 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:43,413 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:43,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:43,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:43,780 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:44,232 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:44,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:44,413 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 06:32:44,414 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:32:44,414 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 06:32:44,414 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:32:44,414 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 4
2025-08-08 06:32:46,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:46,691 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:46,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:47,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:48,702 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:48,725 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': 'The schema does not contain specific data regarding graduation rates, particularly broken down by gender or institution. While there are tables related to students, graduation bills, and programs, there is no direct reference to graduation rates or a mechanism to calculate them based on the provided schema. To answer the question, we would need data that explicitly tracks graduation outcomes by gender and institution, which is not present.', 'feedback': ''}
2025-08-08 06:32:48,725 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:32:48,725 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:32:48,725 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': 'The schema does not contain specific data regarding graduation rates, particularly broken down by gender or institution. While there are tables related to students, graduation bills, and programs, there is no direct reference to graduation rates or a mechanism to calculate them based on the provided schema. To answer the question, we would need data that explicitly tracks graduation outcomes by gender and institution, which is not present.', 'feedback': ''}
2025-08-08 06:32:50,003 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:50,164 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:50,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:50,915 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:51,765 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:51,816 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:51,899 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:53,320 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:55,251 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:55,266 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': 'Too much uncertainty as to whether or not it can be answered', 'feedback': ''}
2025-08-08 06:32:55,266 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:32:55,266 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:32:55,266 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': 'Too much uncertainty as to whether or not it can be answered', 'feedback': ''}
2025-08-08 06:32:56,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:56,516 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:56,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:57,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:59,183 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:32:59,198 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': 'The schema does not contain specific data regarding graduation rates, particularly broken down by gender or institution. While there are tables related to students, graduation bills, and programs, there is no direct reference to graduation rates or a way to calculate them based on the provided schema. To answer the question, we would need data that explicitly tracks graduation outcomes by gender and institution, which is not present in the schema.', 'feedback': ''}
2025-08-08 06:32:59,198 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:32:59,198 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:32:59,198 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': 'The schema does not contain specific data regarding graduation rates, particularly broken down by gender or institution. While there are tables related to students, graduation bills, and programs, there is no direct reference to graduation rates or a way to calculate them based on the provided schema. To answer the question, we would need data that explicitly tracks graduation outcomes by gender and institution, which is not present in the schema.', 'feedback': ''}
2025-08-08 06:33:01,054 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:02,081 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:02,093 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any trends in the performance of girls at ITC University over the past few years?', 'sql': "SELECT s.sex, sem.start_year, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS total_assessments\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN semesters sem ON ar.semester_id = sem.id\nWHERE s.sex = 'F' AND sem.status = 'Ended'\nGROUP BY s.sex, sem.start_year\nORDER BY sem.start_year;", 'correct': True, 'reasoning': "The SQL query correctly identifies the performance trends of female students ('F') at ITC University by calculating the average final score and the total number of assessments for each academic year (start_year) where the semester status is 'Ended'. The grouping by sex and start_year allows for a clear view of trends over the years, which aligns with the question's focus on performance trends over time.", 'feedback': 'The question could be clarified by specifying what kind of trends are being sought (e.g., improvement, decline, consistency) or by asking for specific metrics beyond average scores, such as grade distributions or comparisons with male students. The SQL could be improved by including a filter for the institution_id to ensure the data is specific to ITC University.'}
2025-08-08 06:33:02,094 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:02,094 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:33:02,138 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:02,156 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in the most?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(ar.finalscore) AS average_score\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.assessment_results ar ON sp.id = ar.student_program_id\nJOIN core.courses c ON ar.course_id = c.id\nWHERE s.sex = 'F' AND s.institution_id = 'ITC University'\nGROUP BY p.long_name, c.title\nORDER BY average_score DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses that female students at ITC University are excelling in by calculating the average score of assessment results for each program and course combination. It filters the students by sex and institution, groups the results by program and course, and orders them by average score in descending order, which aligns with the question's requirement to find out where girls are excelling.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be beneficial to clarify whether 'excelling' refers strictly to the highest average scores or if there are other metrics of success to consider. Additionally, ensuring that the institution_id is correctly referenced as a string or integer based on the actual schema would be important."}
2025-08-08 06:33:02,157 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:02,157 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:33:02,389 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:02,394 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic performance levels of girls compare to those of boys at ITC University?', 'sql': "SELECT s.sex, AVG(sg.gpa) AS average_gpa, COUNT(sg.id) AS total_students\nFROM core.students s\nJOIN core.student_semester_gpas sg ON s.id = sg.student_id\nJOIN core.student_programs sp ON s.id = sp.student_id\nWHERE sp.institution_id = 'ITC University' AND sg.status = 'active'\nGROUP BY s.sex;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average GPA and total number of students for each sex (girls and boys) at ITC University. It joins the necessary tables: 'students' for student information, 'student_semester_gpas' for GPA data, and 'student_programs' to filter by institution. The WHERE clause ensures that only active students from ITC University are considered, and the GROUP BY clause groups the results by sex, which is essential for comparing the academic performance levels of girls and boys.", 'feedback': 'The question could be clarified by specifying whether it is looking for a comparison of average GPAs or other performance metrics. Additionally, the SQL could be improved by including a specific selection of semesters if the analysis is intended to focus on a particular academic period.'}
2025-08-08 06:33:02,395 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:02,395 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:33:02,980 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:02,996 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': 'The schema does not contain specific data regarding graduation rates, particularly broken down by gender or institution. While there are tables related to students, graduation bills, and programs, there is no direct reference to graduation rates or a mechanism to calculate them based on the provided schema. To answer the question, we would need data that explicitly tracks graduation outcomes by gender and institution, which is not present in the schema.', 'feedback': ''}
2025-08-08 06:33:02,996 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:02,996 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:02,996 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': 'The schema does not contain specific data regarding graduation rates, particularly broken down by gender or institution. While there are tables related to students, graduation bills, and programs, there is no direct reference to graduation rates or a mechanism to calculate them based on the provided schema. To answer the question, we would need data that explicitly tracks graduation outcomes by gender and institution, which is not present in the schema.', 'feedback': ''}
2025-08-08 06:33:05,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:06,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:06,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:06,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:06,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:07,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:07,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:07,532 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': 'The schema does not contain specific data regarding graduation rates, particularly broken down by gender or institution. While there are tables related to students, graduation bills, and programs, there is no direct reference to graduation rates or a way to calculate them based on the provided schema. To answer the question, we would need data that explicitly tracks graduation outcomes by gender and institution, which is not present in the schema.', 'feedback': ''}
2025-08-08 06:33:07,532 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:07,532 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:07,532 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'answerable': False, 'reasoning': 'The schema does not contain specific data regarding graduation rates, particularly broken down by gender or institution. While there are tables related to students, graduation bills, and programs, there is no direct reference to graduation rates or a way to calculate them based on the provided schema. To answer the question, we would need data that explicitly tracks graduation outcomes by gender and institution, which is not present in the schema.', 'feedback': ''}
2025-08-08 06:33:07,607 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:07,721 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:07,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:08,798 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:09,764 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:10,156 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:11,313 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:12,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:12,140 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:33:12,140 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:12,140 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:12,140 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:33:12,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:12,710 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify contributing factors to the lack of data on girls' performance specifically. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-08 06:33:12,710 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:12,710 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:12,710 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify contributing factors to the lack of data on girls' performance specifically. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-08 06:33:14,230 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:15,295 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:16,556 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:16,573 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question. Therefore, it cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 06:33:16,573 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:16,573 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:16,573 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question. Therefore, it cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 06:33:17,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:17,063 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. However, the provided schema does not contain any specific information or guidelines on how to improve data collection or analysis processes. It primarily consists of tables related to institutions, students, academic years, and various related entities, but lacks any procedural or strategic insights. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 06:33:17,064 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:17,064 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:17,064 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. However, the provided schema does not contain any specific information or guidelines on how to improve data collection or analysis processes. It primarily consists of tables related to institutions, students, academic years, and various related entities, but lacks any procedural or strategic insights. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 06:33:17,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:17,877 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of data gaps or specific factors affecting academic performance. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify reasons for the lack of data on girls' performance. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-08 06:33:17,877 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:17,877 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:17,877 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of data gaps or specific factors affecting academic performance. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify reasons for the lack of data on girls' performance. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-08 06:33:18,966 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:19,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:20,173 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:21,058 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:21,072 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:33:21,073 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:21,073 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:21,073 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:33:21,686 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:21,701 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection processes, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis practices. Therefore, while the schema can provide data on student performance and gender, it does not answer the question about steps for improvement.', 'feedback': ''}
2025-08-08 06:33:21,701 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:21,701 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:21,701 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection processes, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide direct insights or recommendations on how to improve data collection and analysis practices. Therefore, while the schema can provide data on student performance and gender, it does not answer the question about steps for improvement.', 'feedback': ''}
2025-08-08 06:33:22,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:22,365 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. While the schema contains various tables related to students, programs, and academic performance, it does not provide direct insights or qualitative data regarding the reasons for the lack of data on girls' performance. The schema lacks specific fields or tables that would capture qualitative factors or insights into data availability or the reasons behind it. Therefore, the question cannot be answered based on the provided schema.", 'feedback': ''}
2025-08-08 06:33:22,365 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:22,365 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:22,365 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. While the schema contains various tables related to students, programs, and academic performance, it does not provide direct insights or qualitative data regarding the reasons for the lack of data on girls' performance. The schema lacks specific fields or tables that would capture qualitative factors or insights into data availability or the reasons behind it. Therefore, the question cannot be answered based on the provided schema.", 'feedback': ''}
2025-08-08 06:33:23,132 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:23,846 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:24,443 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:24,777 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:24,809 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:33:24,810 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:24,810 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:24,810 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:33:26,158 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:26,177 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide specific guidance or actionable steps for improving data collection and analysis processes. Therefore, while the schema contains relevant data, it does not directly answer the question.', 'feedback': ''}
2025-08-08 06:33:26,177 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:26,177 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:26,177 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection practices, analysis methodologies, and possibly recommendations for improvements. The provided schema contains various tables related to students, their academic performance, and demographic information, but it does not provide specific guidance or actionable steps for improving data collection and analysis processes. Therefore, while the schema contains relevant data, it does not directly answer the question.', 'feedback': ''}
2025-08-08 06:33:26,849 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:26,871 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.", 'feedback': ''}
2025-08-08 06:33:26,872 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:26,872 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:26,872 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.", 'feedback': ''}
2025-08-08 06:33:28,408 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:31,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:31,222 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection methods, analysis techniques, and possibly recommendations for changes in processes or systems. The provided schema contains data structures related to students, their academic performance, and demographic information, but it does not provide any direct information or guidance on how to improve data collection or analysis practices. Therefore, while the schema can support data retrieval for analysis, it does not answer the question of what steps to take for improvement.', 'feedback': ''}
2025-08-08 06:33:31,222 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:33:31,222 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:33:31,222 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection methods, analysis techniques, and possibly recommendations for changes in processes or systems. The provided schema contains data structures related to students, their academic performance, and demographic information, but it does not provide any direct information or guidance on how to improve data collection or analysis practices. Therefore, while the schema can support data retrieval for analysis, it does not answer the question of what steps to take for improvement.', 'feedback': ''}
2025-08-08 06:33:31,223 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 06:33:31,223 - root - INFO - 'No results'
2025-08-08 06:33:31,223 - root - INFO - 'No results'
2025-08-08 06:33:31,223 - root - INFO - 'No results'
2025-08-08 06:33:31,223 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-08 06:33:31,223 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 3
2025-08-08 06:33:31,224 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 06:33:38,134 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:38,145 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 06:33:47,852 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:47,865 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:47,865 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 06:33:47,865 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:47,865 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/3
2025-08-08 06:33:47,865 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:47,865 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:33:47,865 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:47,865 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 06:33:47,865 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:33:47,865 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:33:47,865 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the academic performance levels of girls compare to those of boys at ITC University?...
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, which indicates that there may not be sufficient data avai...
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: academic_performance_comparison_by_gender
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 06:33:47,866 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:33:47,866 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/3
2025-08-08 06:33:47,866 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:47,866 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:33:47,866 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:33:47,866 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are girls at ITC University excelling in the most?...
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are currently no available data or results indicating which programs or course...
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_programs_performance_itc_university
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 06:33:47,866 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:33:47,866 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/3
2025-08-08 06:33:47,866 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:47,866 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:33:47,866 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:33:47,866 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:33:47,866 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any trends in the performance of girls at ITC University over the past few years?...
2025-08-08 06:33:47,867 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Based on the data available, there are no recorded results regarding the performance of girls at ITC...
2025-08-08 06:33:47,867 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:33:47,867 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 06:33:47,867 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_performance_trends_itc_university
2025-08-08 06:33:47,867 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 06:33:47,867 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 06:33:47,867 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:33:47,867 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 3
2025-08-08 06:33:47,867 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:47,867 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 06:33:47,867 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 3 documents
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO -     Content: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:31.222985+00:00', 'data_returned': False}
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:26.872414+00:00', 'data_returned': False}
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO -     Content: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:24.810283+00:00', 'data_returned': False}
2025-08-08 06:33:47,867 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 0/3
2025-08-08 06:33:48,001 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.134s]
2025-08-08 06:33:49,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:50,580 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.982s]
2025-08-08 06:33:50,580 - UPSERT_DOCS - INFO - ✅ Successfully upserted 3 documents to Elasticsearch
2025-08-08 06:33:50,720 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.139s]
2025-08-08 06:33:50,720 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 3
2025-08-08 06:33:50,857 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.137s]
2025-08-08 06:33:50,857 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:33:51,017 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.160s]
2025-08-08 06:33:51,017 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (1):
2025-08-08 06:33:51,018 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (3 docs)
2025-08-08 06:33:51,018 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 8
2025-08-08 06:33:51,018 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 06:33:51,018 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:51,018 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-08 06:33:51,018 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:51,018 - REPORT_PIPELINE - INFO - ✍️ Writing 8 sections using batch processing...
2025-08-08 06:33:51,579 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:51,585 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:51,585 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:33:51,585 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:51,585 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'trends patterns performance improvement'
2025-08-08 06:33:51,585 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:33:51,585 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:33:51,715 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:33:51,715 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-08 06:33:51,811 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:51,815 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:51,816 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:33:51,816 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:51,816 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Faculty support for gender equality'
2025-08-08 06:33:51,816 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:33:51,816 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:33:51,846 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 06:33:51,847 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:33:51,847 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:51,851 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:51,855 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:51,860 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:51,861 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:33:51,861 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:51,863 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'appendices interview transcripts data statistics survey references'
2025-08-08 06:33:51,863 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:33:51,863 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:33:51,865 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:51,865 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:33:51,865 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:51,865 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Academic Performance Metrics'
2025-08-08 06:33:51,866 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:33:51,866 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:33:51,870 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:51,870 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:33:51,870 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:51,870 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'academic performance female students ITC University'
2025-08-08 06:33:51,870 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:33:51,870 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:33:51,977 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:33:51,977 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 06:33:52,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:52,020 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:52,020 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:33:52,020 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:52,021 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion ITC University findings implications recommendations'
2025-08-08 06:33:52,021 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:33:52,021 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:33:52,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:52,094 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:52,094 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:33:52,094 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:52,094 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Key themes female students extracurricular activities challenges support systems'
2025-08-08 06:33:52,094 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:33:52,094 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:33:52,098 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.282s]
2025-08-08 06:33:52,098 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-08 06:33:52,119 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.142s]
2025-08-08 06:33:52,120 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 06:33:52,120 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.257s]
2025-08-08 06:33:52,121 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-08 06:33:52,123 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.258s]
2025-08-08 06:33:52,123 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-08 06:33:52,127 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.257s]
2025-08-08 06:33:52,127 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-08 06:33:52,228 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:33:52,228 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:33:52,251 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:33:52,252 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:33:52,252 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 06:33:52,253 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:33:52,259 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 06:33:52,259 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:33:52,275 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.254s]
2025-08-08 06:33:52,276 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-08 06:33:52,348 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.253s]
2025-08-08 06:33:52,349 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-08 06:33:52,356 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:33:52,356 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 06:33:52,382 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:33:52,382 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:33:52,382 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 06:33:52,383 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 06:33:52,389 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:33:52,390 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 06:33:52,403 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:33:52,404 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:33:52,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:52,472 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:33:52,472 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:33:52,472 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:33:52,472 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology Data Collection Interview Process Participants'
2025-08-08 06:33:52,472 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:33:52,472 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:33:52,477 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:33:52,477 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:33:52,490 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 06:33:52,490 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 06:33:52,512 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:33:52,512 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:33:52,512 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 06:33:52,512 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 06:33:52,519 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:33:52,519 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 06:33:52,531 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:33:52,531 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 06:33:52,600 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:33:52,600 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 3
2025-08-08 06:33:52,603 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:33:52,603 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 06:33:52,658 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:33:52,658 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 06:33:52,726 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:33:52,727 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:33:52,729 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:33:52,729 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 06:33:52,855 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:33:52,855 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 3
2025-08-08 06:33:52,864 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:52,984 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:33:52,985 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 06:33:52,993 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:53,020 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:53,035 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:53,044 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.177s]
2025-08-08 06:33:53,044 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 06:33:53,044 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 06:33:53,133 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:53,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:53,274 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.136s]
2025-08-08 06:33:53,275 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 06:33:53,275 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 06:33:53,374 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.378s]
2025-08-08 06:33:53,374 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 06:33:53,374 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 06:33:53,398 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:53,406 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.383s]
2025-08-08 06:33:53,406 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 06:33:53,406 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 06:33:53,415 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.377s]
2025-08-08 06:33:53,416 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 06:33:53,416 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 06:33:53,534 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.133s]
2025-08-08 06:33:53,535 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 06:33:53,535 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 06:33:53,604 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.389s]
2025-08-08 06:33:53,604 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 06:33:53,605 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 06:33:53,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:53,804 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:53,805 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:53,809 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:53,867 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.222s]
2025-08-08 06:33:53,868 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 3 documents
2025-08-08 06:33:53,868 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 06:33:53,868 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:31.222985+00:00', 'data_returned': False}
2025-08-08 06:33:53,869 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 06:33:53,869 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:24.810283+00:00', 'data_returned': False}
2025-08-08 06:33:53,869 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 06:33:53,869 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:26.872414+00:00', 'data_returned': False}
2025-08-08 06:33:53,954 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.146s]
2025-08-08 06:33:53,955 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.143s]
2025-08-08 06:33:53,955 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 3 documents
2025-08-08 06:33:53,956 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 3 documents
2025-08-08 06:33:53,956 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 06:33:53,956 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 06:33:53,956 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:26.872414+00:00', 'data_returned': False}
2025-08-08 06:33:53,957 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:31.222985+00:00', 'data_returned': False}
2025-08-08 06:33:53,957 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 06:33:53,957 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 06:33:53,957 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:24.810283+00:00', 'data_returned': False}
2025-08-08 06:33:53,957 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:24.810283+00:00', 'data_returned': False}
2025-08-08 06:33:53,957 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 06:33:53,957 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 06:33:53,957 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:31.222985+00:00', 'data_returned': False}
2025-08-08 06:33:53,957 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:26.872414+00:00', 'data_returned': False}
2025-08-08 06:33:54,073 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.259s]
2025-08-08 06:33:54,074 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 06:33:54,074 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 06:33:54,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:54,225 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.142s]
2025-08-08 06:33:54,226 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 3 documents
2025-08-08 06:33:54,226 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 06:33:54,226 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:24.810283+00:00', 'data_returned': False}
2025-08-08 06:33:54,226 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 06:33:54,226 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:26.872414+00:00', 'data_returned': False}
2025-08-08 06:33:54,226 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 06:33:54,226 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:31.222985+00:00', 'data_returned': False}
2025-08-08 06:33:54,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:54,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:54,416 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.141s]
2025-08-08 06:33:54,417 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 3 documents
2025-08-08 06:33:54,417 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 06:33:54,417 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:31.222985+00:00', 'data_returned': False}
2025-08-08 06:33:54,417 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 06:33:54,417 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:24.810283+00:00', 'data_returned': False}
2025-08-08 06:33:54,417 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 06:33:54,418 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:26.872414+00:00', 'data_returned': False}
2025-08-08 06:33:54,471 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.139s]
2025-08-08 06:33:54,472 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 3 documents
2025-08-08 06:33:54,472 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 06:33:54,472 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:24.810283+00:00', 'data_returned': False}
2025-08-08 06:33:54,472 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 06:33:54,472 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:26.872414+00:00', 'data_returned': False}
2025-08-08 06:33:54,472 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 06:33:54,472 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:31.222985+00:00', 'data_returned': False}
2025-08-08 06:33:54,481 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:54,627 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.142s]
2025-08-08 06:33:54,627 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 3 documents
2025-08-08 06:33:54,628 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 06:33:54,628 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:31.222985+00:00', 'data_returned': False}
2025-08-08 06:33:54,628 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 06:33:54,628 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:24.810283+00:00', 'data_returned': False}
2025-08-08 06:33:54,628 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 06:33:54,628 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:26.872414+00:00', 'data_returned': False}
2025-08-08 06:33:55,523 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:33:55,665 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.138s]
2025-08-08 06:33:55,666 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 3 documents
2025-08-08 06:33:55,667 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 06:33:55,667 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:24.810283+00:00', 'data_returned': False}
2025-08-08 06:33:55,667 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 06:33:55,667 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:31.222985+00:00', 'data_returned': False}
2025-08-08 06:33:55,667 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 06:33:55,667 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:33:26.872414+00:00', 'data_returned': False}
2025-08-08 06:33:58,594 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:59,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:33:59,158 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:00,220 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:00,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:00,630 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:01,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:02,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:02,245 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 843 characters
2025-08-08 06:34:02,246 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1521 characters
2025-08-08 06:34:02,246 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1399 characters
2025-08-08 06:34:02,246 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 1380 characters
2025-08-08 06:34:02,247 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 927 characters
2025-08-08 06:34:02,247 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 1135 characters
2025-08-08 06:34:02,247 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 1601 characters
2025-08-08 06:34:02,247 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 1626 characters
2025-08-08 06:34:04,677 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:04,687 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,688 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,688 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,688 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,688 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,688 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,688 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,688 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,688 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,688 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,688 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:34:04,689 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:34:04,689 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-08 06:34:04,689 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:34:04,689 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-08 06:34:04,689 - REPORT_PIPELINE - INFO - 📊 Final report sections: 11
2025-08-08 06:34:04,689 - REPORT_PIPELINE - INFO - 📈 Extended data items: 3
2025-08-08 06:34:04,689 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_063221.log
2025-08-08 06:34:04,693 - celery.app.trace - INFO - Task generate_report[f86c67ff-49f9-4308-abd9-effdf58b05c3] succeeded in 103.37404525000011s: {'outline': '# Report on Girls\' Performance at ITC University

## I. Introduction  
   The purpose of this report is to analyze the academic performance of female students at ITC University, focusing on their achievements, challenges, and the support systems available to them. The key finding indicates that while female students are performing well academically, they face unique challenges that require targeted support and resources to enhance their educational experience.

## II. Methodology  
   - Overview of Data Collection  
   - Description of Interview Process  
   - Participants Overview  
       - Sample Size and Demographics  
       - Limitations of the Study  

## III. Academic Performance Metrics  
   - Comparison of Grades  
   - Course Completion Rates  
   - Course Selection Trends  
   - Comparison with Male Peers  

## IV. Key Themes and Insights  
   - Participation in Extracurricular Activities  
       - Clubs and Organizations  
       - Leadership Roles  
   - Challenges Faced by Female...', ...}
2025-08-08 06:34:15,405 - celery.worker.strategy - INFO - Task generate_report[3d45f6fb-6232-41db-809c-a7d6a5bdd464] received
2025-08-08 06:34:15,406 - app.tasks.report_task - INFO - starting...
2025-08-08 06:34:15,406 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:34:15,406 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 707ff891-b5bd-4620-9bc4-072644f83c45
2025-08-08 06:34:15,406 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:34:15,406 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:34:15,406 - REPORT_REQUEST - INFO - 🆔 Task ID: 707ff891-b5bd-4620-9bc4-072644f83c45
2025-08-08 06:34:15,406 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T06:34:15.406924
2025-08-08 06:34:15,534 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:34:15,534 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 3
2025-08-08 06:34:15,664 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:34:15,664 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 06:34:15,796 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.132s]
2025-08-08 06:34:15,796 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (1):
2025-08-08 06:34:15,796 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (3 docs)
2025-08-08 06:34:15,797 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 06:34:23,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:23,103 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 06:34:27,550 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:27,568 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 06:34:29,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:29,404 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:29,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:29,516 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:29,579 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:29,728 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:29,760 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:29,869 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:30,006 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:30,183 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:30,248 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 06:34:30,248 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:34:30,248 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 06:34:30,248 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:34:30,248 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 4
2025-08-08 06:34:32,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:32,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:32,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:32,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:34,149 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:34,654 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:34,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:34,663 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:35,347 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:35,779 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:35,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:35,915 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:39,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:40,638 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:40,640 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:40,669 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:45,679 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:47,511 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:47,516 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any trends in the performance of girls at ITC University over the past few academic years?', 'sql': "SELECT academic_years.start_year, AVG(assessment_results.finalscore) AS average_score, COUNT(assessment_results.id) AS total_assessments\nFROM assessment_results\nJOIN students ON assessment_results.student_id = students.id\nJOIN academic_years ON assessment_results.semester_id = academic_years.id\nWHERE students.sex = 'F' AND academic_years.status = 'Active'\nGROUP BY academic_years.start_year\nORDER BY academic_years.start_year;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the average performance of female students (girls) at ITC University by calculating the average final score from the assessment results. It filters the results to include only those students who are female and only considers active academic years. The grouping by start year allows for the analysis of trends over the years, and the ordering by start year ensures that the results are presented chronologically. Therefore, the query effectively answers the question about trends in performance over the past academic years.', 'feedback': "The SQL query is well-structured and addresses the question appropriately. However, it could be enhanced by including additional metrics or visualizations to better illustrate trends, such as year-over-year changes in average scores or a comparison with male students' performance."}
2025-08-08 06:34:47,516 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:34:47,517 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:34:47,665 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:47,787 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:49,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:49,623 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:49,871 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:50,091 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:50,107 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:51,116 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:51,293 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:52,437 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:53,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:53,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:54,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:55,233 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:56,260 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:56,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:56,457 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:34:56,483 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the absence of such data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 06:34:56,483 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:34:56,483 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:34:56,483 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the absence of such data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to the question. Therefore, it cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 06:34:58,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:00,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:00,468 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:35:00,468 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:00,468 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:35:00,469 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:35:00,785 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:02,664 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:03,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:03,927 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic performance levels of girls compare to those of boys at ITC University?', 'sql': "SELECT s.sex, AVG(sg.gpa) AS average_gpa, COUNT(s.id) AS student_count\nFROM students s\nJOIN student_programs sp ON s.id = sp.student_id\nJOIN student_semester_gpas sg ON s.id = sg.student_id\nWHERE sp.institution_id = 1 AND s.status IN ('active', 'defered', 'deferred')\nGROUP BY s.sex;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average GPA and student count for each sex (girls and boys) at ITC University by joining the relevant tables: 'students', 'student_programs', and 'student_semester_gpas'. It filters for students at the specified institution and only includes those with active or deferred statuses. The use of GROUP BY on 's.sex' allows for a comparison between the academic performance levels of girls and boys, which directly addresses the question.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by ensuring that the status values are correctly spelled (e.g., 'defered' should be 'deferred'). Additionally, it might be beneficial to clarify the institution_id in the WHERE clause to ensure it corresponds to ITC University, as the current query uses a hardcoded value."}
2025-08-08 06:35:03,927 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:03,927 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:35:04,575 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:04,717 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:04,732 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:35:04,733 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:04,733 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:35:04,733 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the lack of data. The schema includes various tables related to institutions, students, programs, and performance metrics, but it lacks qualitative data or insights that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:35:06,749 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:06,913 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:07,247 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:08,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:11,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:11,512 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the absence of such data. The schema includes various tables related to institutions, students, programs, and performance records, but it lacks qualitative insights or contextual information that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:35:11,513 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:11,513 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:35:11,513 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded performance data for girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of recorded performance data for girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender performance analysis or the reasons behind the absence of such data. The schema includes various tables related to institutions, students, programs, and performance records, but it lacks qualitative insights or contextual information that would allow for a comprehensive answer to this question.', 'feedback': ''}
2025-08-08 06:35:13,402 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:15,098 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:15,639 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:23,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:23,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:23,344 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection methods, analysis techniques, and possibly recommendations for changes in processes or systems. The provided schema contains tables related to student data, academic performance, and demographics, but it does not provide specific guidance or actionable steps for improving data collection and analysis. Therefore, while the schema contains relevant data, it does not directly answer the question.', 'feedback': ''}
2025-08-08 06:35:23,344 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:23,344 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:35:23,344 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection methods, analysis techniques, and possibly recommendations for changes in processes or systems. The provided schema contains tables related to student data, academic performance, and demographics, but it does not provide specific guidance or actionable steps for improving data collection and analysis. Therefore, while the schema contains relevant data, it does not directly answer the question.', 'feedback': ''}
2025-08-08 06:35:24,114 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:25,281 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:26,232 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:26,564 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:27,500 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:27,588 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:27,620 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection methods, analysis techniques, and possibly recommendations for changes in processes or systems. The provided schema contains tables related to student information, academic performance, and demographics, but it does not provide specific guidance or actionable steps for improving data collection and analysis. Therefore, while the schema contains relevant data, it does not directly answer the question.', 'feedback': ''}
2025-08-08 06:35:27,621 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:27,621 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:35:27,621 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection methods, analysis techniques, and possibly recommendations for changes in processes or systems. The provided schema contains tables related to student information, academic performance, and demographics, but it does not provide specific guidance or actionable steps for improving data collection and analysis. Therefore, while the schema contains relevant data, it does not directly answer the question.', 'feedback': ''}
2025-08-08 06:35:27,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:31,914 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:32,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:34,313 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:34,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:34,527 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection methods, analysis techniques, and possibly recommendations for changes in processes or systems. The provided schema contains data structures related to students, their academic performance, and demographic information, but it does not provide any direct guidance or actionable steps for improving data collection and analysis. Therefore, while the schema can support data retrieval for analysis, it does not answer the question of what steps to take.', 'feedback': ''}
2025-08-08 06:35:34,527 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:34,527 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:35:34,527 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection methods, analysis techniques, and possibly recommendations for changes in processes or systems. The provided schema contains data structures related to students, their academic performance, and demographic information, but it does not provide any direct guidance or actionable steps for improving data collection and analysis. Therefore, while the schema can support data retrieval for analysis, it does not answer the question of what steps to take.', 'feedback': ''}
2025-08-08 06:35:36,550 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:37,460 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:39,669 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:39,694 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection methods, analysis techniques, and possibly recommendations for changes in processes or systems. The provided schema contains tables related to student data, academic performance, and demographics, but it does not provide specific guidance or actionable steps for improving data collection and analysis. Therefore, while the schema contains relevant data, it does not directly answer the question.', 'feedback': ''}
2025-08-08 06:35:39,694 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:39,695 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:35:39,695 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the academic performance of students by gender?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to improve data collection and analysis regarding the academic performance of students by gender. This is a strategic and operational question that requires insights into data collection methods, analysis techniques, and possibly recommendations for changes in processes or systems. The provided schema contains tables related to student data, academic performance, and demographics, but it does not provide specific guidance or actionable steps for improving data collection and analysis. Therefore, while the schema contains relevant data, it does not directly answer the question.', 'feedback': ''}
2025-08-08 06:35:40,009 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:41,702 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:41,726 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the graduation rates of girls at ITC University compare to the overall graduation rates?', 'sql': "WITH total_students AS (SELECT COUNT(*) AS total FROM students WHERE institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')), total_graduated AS (SELECT COUNT(*) AS graduated FROM graduation_batches gb JOIN student_programs sp ON gb.id = sp.program_id JOIN students s ON sp.student_id = s.id WHERE gb.status = 'active' AND s.sex = 'F' AND s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')), overall_graduated AS (SELECT COUNT(*) AS overall_graduated FROM graduation_batches gb JOIN student_programs sp ON gb.id = sp.program_id JOIN students s ON sp.student_id = s.id WHERE gb.status = 'active' AND s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')) SELECT (SELECT graduated FROM total_graduated) * 100.0 / (SELECT total FROM total_students) AS female_graduation_rate, (SELECT overall_graduated FROM overall_graduated) * 100.0 / (SELECT total FROM total_students) AS overall_graduation_rate;", 'correct': True, 'reasoning': 'The SQL query correctly calculates the graduation rates for female students at ITC University and compares it to the overall graduation rates. It first counts the total number of students and the number of graduated female students, then calculates the graduation rates as a percentage of the total students. The use of Common Table Expressions (CTEs) helps in organizing the query and making it clear. The query also ensures that it only considers students from ITC University, which is essential for answering the question accurately.', 'feedback': "The SQL query is well-structured and answers the question as intended. However, it could be improved by explicitly naming the output columns in the final SELECT statement for clarity, such as 'female_graduation_rate' and 'overall_graduation_rate'. Additionally, it might be beneficial to include a comparison in the output, such as the difference between the two rates, to provide a more direct answer to the comparison aspect of the question."}
2025-08-08 06:35:41,727 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:41,727 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:35:42,821 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:43,985 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:46,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:46,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:47,982 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:49,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:50,054 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:52,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:52,038 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the lack of graduation rates at ITC University, particularly for girls?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of graduation rates at ITC University, specifically for girls. While the schema contains various tables related to students, programs, and graduation statuses, it does not provide direct data or metrics that would allow for a comprehensive analysis of graduation rates or the specific factors affecting them. The schema lacks qualitative data, such as reasons for dropout or specific challenges faced by female students, which are necessary to answer the question fully.', 'feedback': ''}
2025-08-08 06:35:52,038 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:52,038 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:35:52,038 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the lack of graduation rates at ITC University, particularly for girls?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of graduation rates at ITC University, specifically for girls. While the schema contains various tables related to students, programs, and graduation statuses, it does not provide direct data or metrics that would allow for a comprehensive analysis of graduation rates or the specific factors affecting them. The schema lacks qualitative data, such as reasons for dropout or specific challenges faced by female students, which are necessary to answer the question fully.', 'feedback': ''}
2025-08-08 06:35:54,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:56,588 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:56,600 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the lack of graduation rates at ITC University, particularly for girls?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of graduation rates at ITC University, specifically for girls. While the schema contains various tables related to students, programs, and graduation statuses, it does not provide direct data or metrics that would allow for a comprehensive analysis of graduation rates or the specific factors affecting them. The schema lacks qualitative data, such as reasons for dropout or specific challenges faced by female students, which are necessary to answer the question fully.', 'feedback': ''}
2025-08-08 06:35:56,600 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:35:56,600 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:35:56,600 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the lack of graduation rates at ITC University, particularly for girls?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of graduation rates at ITC University, specifically for girls. While the schema contains various tables related to students, programs, and graduation statuses, it does not provide direct data or metrics that would allow for a comprehensive analysis of graduation rates or the specific factors affecting them. The schema lacks qualitative data, such as reasons for dropout or specific challenges faced by female students, which are necessary to answer the question fully.', 'feedback': ''}
2025-08-08 06:35:57,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:35:58,662 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:00,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:00,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:00,849 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the lack of graduation rates at ITC University, particularly for girls?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of graduation rates at ITC University, specifically for girls. While the schema contains various tables related to students, programs, and academic performance, it does not provide direct data or metrics regarding graduation rates or specific factors affecting them. Additionally, the schema lacks qualitative data or insights that would be necessary to understand the reasons behind graduation rates, such as social, economic, or cultural factors. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-08 06:36:00,849 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:36:00,849 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:36:00,849 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the lack of graduation rates at ITC University, particularly for girls?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of graduation rates at ITC University, specifically for girls. While the schema contains various tables related to students, programs, and academic performance, it does not provide direct data or metrics regarding graduation rates or specific factors affecting them. Additionally, the schema lacks qualitative data or insights that would be necessary to understand the reasons behind graduation rates, such as social, economic, or cultural factors. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-08 06:36:02,991 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:03,505 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:04,704 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:04,977 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:05,007 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the lack of graduation rates at ITC University, particularly for girls?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of graduation rates at ITC University, specifically for girls. While the schema contains various tables related to students, programs, and graduation statuses, it does not provide direct data or metrics that would allow for a comprehensive analysis of graduation rates or the specific factors affecting them. The schema lacks qualitative data, such as reasons for dropout or specific challenges faced by female students, which are necessary to answer the question fully.', 'feedback': ''}
2025-08-08 06:36:05,007 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:36:05,007 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:36:05,007 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the lack of graduation rates at ITC University, particularly for girls?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of graduation rates at ITC University, specifically for girls. While the schema contains various tables related to students, programs, and graduation statuses, it does not provide direct data or metrics that would allow for a comprehensive analysis of graduation rates or the specific factors affecting them. The schema lacks qualitative data, such as reasons for dropout or specific challenges faced by female students, which are necessary to answer the question fully.', 'feedback': ''}
2025-08-08 06:36:10,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:17,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:17,265 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in the most?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(g.gpa) AS average_gpa\nFROM student_semester_gpas g\nJOIN students s ON g.student_id = s.id\nJOIN programs p ON g.student_program_id = p.id\nJOIN courses c ON g.semester_id = c.id\nWHERE s.sex = 'F' AND g.status = 'active'\nGROUP BY p.long_name, c.title\nORDER BY average_gpa DESC;", 'correct': False, 'reasoning': "The SQL query attempts to find the average GPA of female students at ITC University by joining the relevant tables. However, it incorrectly joins the 'courses' table using 'g.semester_id = c.id', which does not accurately reflect the relationship between students' GPAs and the courses they are enrolled in. The correct join should likely involve a relationship that connects students' GPAs to the specific courses they are taking in a given semester, which is not established in the current query. Therefore, the query does not correctly answer the question about which programs or courses girls are excelling in.", 'feedback': "To improve the SQL query, ensure that the join between 'student_semester_gpas' and 'courses' accurately reflects the relationship between students' GPAs and the courses they are enrolled in. You may need to include a table that links students to their courses directly, such as a 'student_courses' table, if it exists. Additionally, clarify the question to specify whether it is asking for programs, courses, or both, and ensure the SQL reflects that.", 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-08 06:36:17,266 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:36:17,266 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-08 06:36:19,721 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:21,917 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:23,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:28,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:33,908 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:33,920 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in the most?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(ar.finalscore) AS average_score\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.assessment_results ar ON sp.id = ar.student_program_id\nJOIN core.courses c ON ar.course_id = c.id\nWHERE s.sex = 'F' AND s.institution_id = 'ITC University'\nGROUP BY p.long_name, c.title\nORDER BY average_score DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses that female students at ITC University are excelling in by calculating the average score of assessment results for each program and course combination. It filters the students by sex and institution, groups the results by program and course, and orders them by average score in descending order, which aligns with the question's request for identifying areas of excellence.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be beneficial to clarify whether 'excelling' refers strictly to the highest average scores or if other metrics should be considered. Additionally, ensuring that the institution_id is correctly referenced as a string or integer based on the actual schema would be important."}
2025-08-08 06:36:33,921 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:36:33,921 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:36:35,695 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:36,173 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:37,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:39,084 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:41,877 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:41,899 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might influence the academic performance of girls in various programs at ITC University?', 'answerable': False, 'reasoning': "The question asks for factors influencing academic performance specifically for girls in various programs at ITC University. However, the provided schema does not contain specific data or attributes related to gender-specific factors influencing academic performance. While there are tables related to academic performance (like assessment results, grades, etc.), there is no direct link to factors that could influence performance, such as social, economic, or psychological factors. Additionally, the schema does not specify any data related to 'ITC University' specifically, as it only references institutions generically. Therefore, the question cannot be answered with the available schema.", 'feedback': ''}
2025-08-08 06:36:41,899 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:36:41,899 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:36:41,899 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might influence the academic performance of girls in various programs at ITC University?', 'answerable': False, 'reasoning': "The question asks for factors influencing academic performance specifically for girls in various programs at ITC University. However, the provided schema does not contain specific data or attributes related to gender-specific factors influencing academic performance. While there are tables related to academic performance (like assessment results, grades, etc.), there is no direct link to factors that could influence performance, such as social, economic, or psychological factors. Additionally, the schema does not specify any data related to 'ITC University' specifically, as it only references institutions generically. Therefore, the question cannot be answered with the available schema.", 'feedback': ''}
2025-08-08 06:36:44,173 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:46,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:46,373 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might influence the academic performance of girls in various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance specifically for girls in various programs at ITC University. However, the provided schema does not contain any direct information about gender-specific factors affecting academic performance. While there are tables related to students, programs, and academic results, there is no explicit data or attributes that would allow for an analysis of factors influencing performance based on gender. Additionally, the schema lacks qualitative data or metrics that could be used to assess such influences.', 'feedback': ''}
2025-08-08 06:36:46,373 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:36:46,373 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:36:46,373 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might influence the academic performance of girls in various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance specifically for girls in various programs at ITC University. However, the provided schema does not contain any direct information about gender-specific factors affecting academic performance. While there are tables related to students, programs, and academic results, there is no explicit data or attributes that would allow for an analysis of factors influencing performance based on gender. Additionally, the schema lacks qualitative data or metrics that could be used to assess such influences.', 'feedback': ''}
2025-08-08 06:36:48,597 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:50,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:50,690 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might influence the academic performance of girls in various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance specifically for girls in various programs at ITC University. However, the provided schema does not contain any direct information about gender-specific factors affecting academic performance. While there are tables related to students, programs, and academic results, there is no explicit data or attributes that would allow for an analysis of factors influencing performance based on gender. Additionally, the schema lacks qualitative data or metrics that could be used to assess such influences.', 'feedback': ''}
2025-08-08 06:36:50,690 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:36:50,690 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:36:50,690 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might influence the academic performance of girls in various programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance specifically for girls in various programs at ITC University. However, the provided schema does not contain any direct information about gender-specific factors affecting academic performance. While there are tables related to students, programs, and academic results, there is no explicit data or attributes that would allow for an analysis of factors influencing performance based on gender. Additionally, the schema lacks qualitative data or metrics that could be used to assess such influences.', 'feedback': ''}
2025-08-08 06:36:50,691 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 06:36:50,691 - root - INFO - [{'female_graduation_rate': 0.0, 'overall_graduation_rate': 0.0}]
2025-08-08 06:36:50,691 - root - INFO - 'No results'
2025-08-08 06:36:50,691 - root - INFO - 'No results'
2025-08-08 06:36:50,691 - root - INFO - 'No results'
2025-08-08 06:36:50,691 - root - INFO - 'No results'
2025-08-08 06:36:50,691 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 1
2025-08-08 06:36:50,691 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 4
2025-08-08 06:36:50,691 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 06:36:56,833 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:36:56,837 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 06:37:07,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:07,598 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:07,599 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 06:37:07,599 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:07,599 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/4
2025-08-08 06:37:07,599 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:07,599 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:37:07,599 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:37:07,599 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the academic performance levels of girls compare to those of boys at ITC University?...
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, which indicates that there may not be sufficient data avai...
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: academic_performance_comparison_by_gender
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 06:37:07,599 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 06:37:07,600 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:37:07,600 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/4
2025-08-08 06:37:07,600 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:07,600 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:37:07,600 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 2
2025-08-08 06:37:07,600 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are girls at ITC University excelling in the most?...
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 06:37:07,600 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 2:
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are girls at ITC University excelling in the most?...
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are currently no available data or results indicating which programs or course...
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_programs_performance_itc_university
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 2 documents, 0 with data_returned=True
2025-08-08 06:37:07,601 - REPORT_PIPELINE - INFO -   📄 Generated 2 documents from this interview
2025-08-08 06:37:07,601 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/4
2025-08-08 06:37:07,601 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:07,601 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:37:07,601 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:37:07,601 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any trends in the performance of girls at ITC University over the past few academic years?...
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Based on the data available, there are no recorded results regarding the performance of girls at ITC...
2025-08-08 06:37:07,601 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_performance_trends_itc_university
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 06:37:07,602 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:37:07,602 - REPORT_PIPELINE - INFO - 🔄 Processing interview 4/4
2025-08-08 06:37:07,602 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:07,602 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:37:07,602 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:37:07,602 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the graduation rates of girls at ITC University compare to the overall graduation rates?...
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The graduation rate for girls at ITC University is 0.0%, which is the same as the overall graduation...
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'female_graduation_rate': 0.0, 'overall_graduation_rate': 0.0}]...
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: female_graduation_rate_vs_overall
2025-08-08 06:37:07,602 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 06:37:07,602 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 06:37:07,603 - celery.redirected - WARNING - [{'female_graduation_rate': 0.0, 'overall_graduation_rate': 0.0}]
2025-08-08 06:37:07,603 - celery.redirected - WARNING - ================================= 
2025-08-08 06:37:07,603 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 06:37:07,603 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:37:07,603 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 5
2025-08-08 06:37:07,603 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:07,603 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 06:37:07,603 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 5 documents
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -     Content: Question: How do the academic performance levels of girls compare to those of boys at ITC University...
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:35:39.695200+00:00', 'data_returned': False}
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: I'm so...
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:50.690735+00:00', 'data_returned': False}
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:50.690735+00:00', 'data_returned': False}
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -     Content: Question: Are there any trends in the performance of girls at ITC University over the past few acade...
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:35:11.513348+00:00', 'data_returned': False}
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 06:37:07,603 - UPSERT_DOCS - INFO -     Content: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 06:37:07,604 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:05.007802+00:00', 'data_returned': True}
2025-08-08 06:37:07,604 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 1/5
2025-08-08 06:37:07,731 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.126s]
2025-08-08 06:37:08,230 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:37:09,424 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.695s]
2025-08-08 06:37:09,426 - UPSERT_DOCS - INFO - ✅ Successfully upserted 5 documents to Elasticsearch
2025-08-08 06:37:09,555 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:37:09,555 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 8
2025-08-08 06:37:09,683 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:37:09,684 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 06:37:09,823 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.139s]
2025-08-08 06:37:09,824 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (1):
2025-08-08 06:37:09,824 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 06:37:09,824 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 8
2025-08-08 06:37:09,825 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 06:37:09,825 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:09,826 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-08 06:37:09,826 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:09,826 - REPORT_PIPELINE - INFO - ✍️ Writing 8 sections using batch processing...
2025-08-08 06:37:10,408 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:10,413 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:10,414 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:37:10,414 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:10,414 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Graduation rate analysis'
2025-08-08 06:37:10,414 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,414 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:10,470 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:10,470 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:37:10,470 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:10,470 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Faculty support gender inclusivity programs'
2025-08-08 06:37:10,470 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,470 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,477 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:10,485 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:10,486 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:37:10,486 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:10,486 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'factors influencing performance'
2025-08-08 06:37:10,486 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,486 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,544 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 06:37:10,544 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 8
2025-08-08 06:37:10,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:10,568 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:10,568 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:37:10,568 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:10,568 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Graduation rates for girls'
2025-08-08 06:37:10,568 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,568 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,599 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:37:10,599 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 8
2025-08-08 06:37:10,620 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-08 06:37:10,620 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 8
2025-08-08 06:37:10,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:10,673 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:37:10,674 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 06:37:10,674 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:10,675 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:37:10,675 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:10,675 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University mission values gender representation history'
2025-08-08 06:37:10,675 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,675 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,686 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:10,698 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:10,699 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:37:10,699 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:10,699 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'References Interview Summaries Resources'
2025-08-08 06:37:10,699 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,699 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,700 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-08 06:37:10,701 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 8
2025-08-08 06:37:10,716 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:10,732 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:10,732 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:37:10,733 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:10,733 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 06:37:10,733 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion key insights recommendations graduation rates girls support systems'
2025-08-08 06:37:10,733 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 06:37:10,733 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,734 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:10,749 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:37:10,749 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 06:37:10,753 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:10,753 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:37:10,753 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:10,753 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'female students academic performance ITC University graduation rates'
2025-08-08 06:37:10,753 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,754 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:37:10,805 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 06:37:10,805 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-08 06:37:10,806 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 06:37:10,806 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 8
2025-08-08 06:37:10,826 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:37:10,827 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 8
2025-08-08 06:37:10,830 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:37:10,830 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 06:37:10,860 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:37:10,860 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:37:10,860 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-08 06:37:10,860 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 8
2025-08-08 06:37:10,876 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:37:10,876 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-08 06:37:10,939 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-08 06:37:10,940 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-08 06:37:10,940 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-08 06:37:10,941 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 06:37:10,953 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:37:10,953 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 06:37:10,957 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:37:10,957 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-08 06:37:10,988 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:37:10,989 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:37:10,989 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 06:37:10,989 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-08 06:37:11,002 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.248s]
2025-08-08 06:37:11,002 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 8
2025-08-08 06:37:11,009 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 06:37:11,010 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-08 06:37:11,070 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:37:11,071 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-08 06:37:11,079 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-08 06:37:11,079 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-08 06:37:11,084 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:37:11,085 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-08 06:37:11,118 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:37:11,120 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-08 06:37:11,128 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:37:11,129 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 06:37:11,197 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:37:11,198 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-08 06:37:11,205 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-08 06:37:11,205 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-08 06:37:11,246 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-08 06:37:11,246 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-08 06:37:11,253 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.124s]
2025-08-08 06:37:11,253 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-08 06:37:11,385 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-08 06:37:11,386 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-08 06:37:11,456 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:37:11,495 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:37:11,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:37:11,546 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:37:11,601 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.141s]
2025-08-08 06:37:11,602 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-08 06:37:11,602 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 06:37:11,602 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:05.007802+00:00', 'data_returned': True}
2025-08-08 06:37:11,755 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.256s]
2025-08-08 06:37:11,756 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-08 06:37:11,756 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 06:37:11,756 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:05.007802+00:00', 'data_returned': True}
2025-08-08 06:37:11,767 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:37:11,779 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:37:11,799 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.255s]
2025-08-08 06:37:11,800 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-08 06:37:11,800 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 06:37:11,800 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:05.007802+00:00', 'data_returned': True}
2025-08-08 06:37:11,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:37:11,814 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.266s]
2025-08-08 06:37:11,815 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-08 06:37:11,815 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 06:37:11,815 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:05.007802+00:00', 'data_returned': True}
2025-08-08 06:37:11,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:37:11,904 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.133s]
2025-08-08 06:37:11,905 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-08 06:37:11,905 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 06:37:11,905 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:05.007802+00:00', 'data_returned': True}
2025-08-08 06:37:11,918 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.134s]
2025-08-08 06:37:11,918 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-08 06:37:11,918 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 06:37:11,918 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:05.007802+00:00', 'data_returned': True}
2025-08-08 06:37:11,939 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.133s]
2025-08-08 06:37:11,940 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-08 06:37:11,940 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 06:37:11,940 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:05.007802+00:00', 'data_returned': True}
2025-08-08 06:37:11,983 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.138s]
2025-08-08 06:37:11,983 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-08 06:37:11,983 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the graduation rates of girls at ITC University compare to the overall graduation r...
2025-08-08 06:37:11,983 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:36:05.007802+00:00', 'data_returned': True}
2025-08-08 06:37:13,359 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:14,779 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:15,839 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:16,054 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:16,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:16,450 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:17,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:18,556 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:18,569 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 331 characters
2025-08-08 06:37:18,569 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1017 characters
2025-08-08 06:37:18,570 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 937 characters
2025-08-08 06:37:18,570 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 1463 characters
2025-08-08 06:37:18,571 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1295 characters
2025-08-08 06:37:18,571 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 1256 characters
2025-08-08 06:37:18,571 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 1123 characters
2025-08-08 06:37:18,571 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 433 characters
2025-08-08 06:37:20,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:37:20,341 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:37:20,341 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:37:20,341 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:37:20,341 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:37:20,341 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:37:20,341 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:37:20,341 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:37:20,342 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:37:20,342 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:37:20,342 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:37:20,342 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-08 06:37:20,342 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:37:20,342 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-08 06:37:20,342 - REPORT_PIPELINE - INFO - 📊 Final report sections: 9
2025-08-08 06:37:20,342 - REPORT_PIPELINE - INFO - 📈 Extended data items: 4
2025-08-08 06:37:20,342 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_063221.log
2025-08-08 06:37:20,344 - celery.app.trace - INFO - Task generate_report[3d45f6fb-6232-41db-809c-a7d6a5bdd464] succeeded in 184.9396059999999s: {'outline': '# Report on Girls\' Performance at ITC University

## Introduction  
The purpose of this report is to analyze the academic performance of female students at ITC University, focusing on graduation rates and factors influencing these outcomes. The key finding indicates that the current graduation rate for girls is 0.0%, highlighting significant challenges in their academic journey.

## 1. Background of ITC University  
- Overview of the institution\'s mission and values  
- Historical context of gender representation in higher education at ITC University  

## 2. Overview of Graduation Rates  
- 2.1 Current Graduation Rates for Girls  
    - 2.1.1 Reported Graduation Rate: 0.0%  
    - 2.1.2 Comparison to Overall Graduation Rate: 0.0%  
- 2.2 Implications of Graduation Rates  
    - 2.2.1 Lack of Graduates  
    - 2.2.2 Potential Factors Contributing to Low Rates  

## 3. Analysis of Findings  
- 3.1 Interpretation of 0.0% Graduation Rate  
    - 3.1.1 Significance of No Graduates  
    - 3.1.2...', ...}
2025-08-08 06:47:24,864 - celery.worker.strategy - INFO - Task generate_report[067ccc1c-489c-4753-b7c3-f88793b235f4] received
2025-08-08 06:47:24,866 - app.tasks.report_task - INFO - starting...
2025-08-08 06:47:24,866 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:47:24,866 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: de81daf6-7e2c-47a8-b1f4-afa21744e7dd
2025-08-08 06:47:24,866 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:47:24,866 - REPORT_REQUEST - INFO - 📝 Original Question: 'Which institution has the most students?'
2025-08-08 06:47:24,866 - REPORT_REQUEST - INFO - 🆔 Task ID: de81daf6-7e2c-47a8-b1f4-afa21744e7dd
2025-08-08 06:47:24,867 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T06:47:24.867072
2025-08-08 06:47:25,002 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 06:47:25,002 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 8
2025-08-08 06:47:25,132 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 06:47:25,133 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 06:47:25,270 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.136s]
2025-08-08 06:47:25,270 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (1):
2025-08-08 06:47:25,271 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 06:47:25,271 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 06:47:31,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:31,362 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 06:47:35,672 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:35,695 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 06:47:38,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:38,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:38,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:38,552 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:38,691 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:38,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:39,310 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:39,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:39,899 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:43,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:43,397 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 06:47:43,398 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:47:43,398 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 06:47:43,398 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:47:43,398 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 7
2025-08-08 06:47:45,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:45,841 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:45,850 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:45,856 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:46,152 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:46,173 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:46,628 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:48,197 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:48,325 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:48,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:48,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:48,805 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:49,777 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:49,780 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:49,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:50,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:50,186 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:50,471 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:51,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:51,850 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:51,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:53,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:54,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:54,581 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:55,536 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:55,812 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:56,334 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:57,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:59,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:47:59,446 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the demographic breakdown of students at the institution with the most students?', 'sql': 'SELECT s.sex, COUNT(*) AS student_count\nFROM students s\nJOIN institutions i ON s.institution_id = i.id\nWHERE i.id = (SELECT institution_id FROM students GROUP BY institution_id ORDER BY COUNT(*) DESC LIMIT 1)\nGROUP BY s.sex;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by using a subquery that groups students by institution_id and orders them by the count of students in descending order, limiting the result to the top institution. It then joins the students table with the institutions table to ensure that the demographic breakdown is specifically for the institution with the highest student count. Finally, it groups the results by sex, providing the demographic breakdown as requested.', 'feedback': 'The query is well-structured and effectively answers the question. However, it could be enhanced by including additional demographic factors, such as nationality or age, if those details are relevant to the breakdown. Additionally, consider adding a LIMIT clause to the main query to ensure it only returns results for the top institution, although the subquery already limits to one institution.'}
2025-08-08 06:47:59,446 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:47:59,446 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:47:59,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:00,477 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:01,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:01,938 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:02,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:02,360 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the retention rate of students at the institution with the most students?', 'sql': "WITH student_counts AS (  SELECT institution_id, COUNT(*) AS total_students  FROM core.students  GROUP BY institution_id), max_institution AS (  SELECT institution_id  FROM student_counts  ORDER BY total_students DESC  LIMIT 1), retained_students AS (  SELECT COUNT(DISTINCT s.id) AS retained_count  FROM core.students s  JOIN core.student_statuses ss ON s.id = ss.student_id  WHERE ss.student_status_type_id IN (SELECT id FROM core.student_status_types WHERE status = 'active') AND s.institution_id = (SELECT institution_id FROM max_institution)) SELECT retained_count, (retained_count::float / total_students) * 100 AS retention_rate FROM retained_students, student_counts WHERE institution_id = (SELECT institution_id FROM max_institution);", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the total number of students per institution and selecting the one with the maximum count. It then calculates the number of retained students by counting those with an 'active' status from the student_statuses table. Finally, it computes the retention rate as the ratio of retained students to total students for that institution. The logic follows the requirements of the question accurately.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly naming the final output columns for clarity, such as 'retention_rate' and 'total_students'. Additionally, consider adding error handling or checks for cases where there might be no students at any institution."}
2025-08-08 06:48:02,360 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:02,360 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:48:02,687 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:02,925 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:03,783 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:04,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:04,294 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any specific admission criteria that the institution with the most students has implemented?', 'sql': 'SELECT af.description, af.is_program_configured, af.show_education, af.show_employment, af.show_results, COUNT(a.id) AS student_count\nFROM admission_forms af\nJOIN applicants a ON af.id = a.form_id\nJOIN (SELECT institution_id, COUNT(*) AS total_students\n      FROM applicants\n      GROUP BY institution_id\n      ORDER BY total_students DESC\n      LIMIT 1) AS top_institution ON a.institution_id = top_institution.institution_id\nGROUP BY af.id;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of applicants per institution and selecting the one with the highest count. It then retrieves the admission criteria from the 'admission_forms' table, including the description and various flags indicating whether certain criteria are configured (like program configuration, education, employment, and results). The use of GROUP BY ensures that the results are aggregated correctly for the admission forms associated with the top institution.", 'feedback': "The question could be clarified by specifying what is meant by 'specific admission criteria'—whether it refers to all criteria or just certain types. Additionally, the SQL could be improved by explicitly stating that it is looking for criteria related to the top institution, which is already implied but could enhance clarity."}
2025-08-08 06:48:04,294 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:04,294 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:48:04,573 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:05,039 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:05,702 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:05,725 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:06,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:06,536 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:06,636 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:07,399 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:07,436 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:07,906 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:08,178 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:08,260 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:08,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:09,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:09,908 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:10,134 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:10,714 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:10,764 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:11,441 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:11,770 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:12,030 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:12,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:12,594 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about various entities related to institutions, students, programs, and assessments, but it does not include qualitative factors or insights that would explain retention rates. The schema lacks data on student experiences, institutional policies, or other qualitative metrics that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 06:48:12,594 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:12,594 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:12,594 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about various entities related to institutions, students, programs, and assessments, but it does not include qualitative factors or insights that would explain retention rates. The schema lacks data on student experiences, institutional policies, or other qualitative metrics that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 06:48:12,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:13,150 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:13,804 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:13,822 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do these admission criteria impact the diversity and academic performance of the student body at the institution?', 'answerable': False, 'reasoning': 'The question asks about the impact of admission criteria on diversity and academic performance, which requires analysis and interpretation of data that is not directly available in the schema. While the schema contains tables related to admissions, applicants, and academic performance, it does not provide specific admission criteria or a direct way to measure diversity and academic performance in relation to those criteria. To answer this question, one would need to perform a detailed analysis that combines various data points, which is not feasible with the current schema alone.', 'feedback': ''}
2025-08-08 06:48:13,822 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:13,822 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:13,822 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do these admission criteria impact the diversity and academic performance of the student body at the institution?', 'answerable': False, 'reasoning': 'The question asks about the impact of admission criteria on diversity and academic performance, which requires analysis and interpretation of data that is not directly available in the schema. While the schema contains tables related to admissions, applicants, and academic performance, it does not provide specific admission criteria or a direct way to measure diversity and academic performance in relation to those criteria. To answer this question, one would need to perform a detailed analysis that combines various data points, which is not feasible with the current schema alone.', 'feedback': ''}
2025-08-08 06:48:14,621 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:15,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:16,091 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:16,112 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:16,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:16,527 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total number of students enrolled at the institution with the most students?', 'sql': 'SELECT COUNT(*) AS total_students\nFROM students\nWHERE institution_id = (\n    SELECT institution_id\n    FROM students\n    GROUP BY institution_id\n    ORDER BY COUNT(*) DESC\n    LIMIT 1\n);', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by grouping the records in the 'students' table by 'institution_id', counting the number of students for each institution, and ordering the results in descending order. The subquery retrieves the 'institution_id' of the institution with the highest count of students. The outer query then counts the total number of students for that specific institution, which directly answers the question about the total number of students enrolled at the institution with the most students.", 'feedback': 'The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to add a comment in the SQL to clarify the purpose of the subquery for future reference.'}
2025-08-08 06:48:16,528 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:16,528 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:48:16,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:16,762 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about institutions, students, programs, and various academic and administrative records, but it does not include qualitative insights or analyses regarding retention rates or the factors influencing them. Therefore, this question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 06:48:16,762 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:16,762 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:16,762 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about institutions, students, programs, and various academic and administrative records, but it does not include qualitative insights or analyses regarding retention rates or the factors influencing them. Therefore, this question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 06:48:17,992 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:17,999 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are most popular at the institution with the most students?', 'sql': 'SELECT p.long_name, COUNT(sp.student_id) AS student_count\nFROM student_programs sp\nJOIN programs p ON sp.program_id = p.id\nWHERE sp.institution_id = (\n    SELECT institution_id\n    FROM students\n    GROUP BY institution_id\n    ORDER BY COUNT(id) DESC\n    LIMIT 1\n)\nGROUP BY p.long_name\nORDER BY student_count DESC;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and selecting the one with the highest count. It then joins the 'student_programs' table with the 'programs' table to count how many students are enrolled in each program at that institution. The results are grouped by program name and ordered by the number of students enrolled, which aligns with the question's request for the most popular programs.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by adding a LIMIT clause to the final result set to return only the top N most popular programs, if that is a requirement. Additionally, clarifying whether 'most popular' refers strictly to the number of students or if other factors should be considered could enhance the question."}
2025-08-08 06:48:17,999 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:17,999 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:48:18,212 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:18,229 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do these admission criteria impact the diversity and academic performance of the student body at the institution?', 'answerable': False, 'reasoning': 'The question asks about the impact of admission criteria on diversity and academic performance, which requires analysis and interpretation of data that is not directly available in the schema. While the schema contains tables related to admissions, applicants, and academic performance (like grades and programs), it does not provide specific admission criteria or a direct way to measure diversity and performance outcomes in relation to those criteria. Therefore, the question cannot be answered based solely on the provided schema.', 'feedback': ''}
2025-08-08 06:48:18,229 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:18,229 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:18,229 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do these admission criteria impact the diversity and academic performance of the student body at the institution?', 'answerable': False, 'reasoning': 'The question asks about the impact of admission criteria on diversity and academic performance, which requires analysis and interpretation of data that is not directly available in the schema. While the schema contains tables related to admissions, applicants, and academic performance (like grades and programs), it does not provide specific admission criteria or a direct way to measure diversity and performance outcomes in relation to those criteria. Therefore, the question cannot be answered based solely on the provided schema.', 'feedback': ''}
2025-08-08 06:48:18,323 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:18,338 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 06:48:18,338 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:18,338 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:18,338 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 06:48:18,348 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:18,828 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:19,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:19,728 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:19,732 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the student population of the institution with the most students compare to other institutions?', 'sql': "WITH StudentCounts AS (  SELECT institution_id, COUNT(*) AS student_count  FROM core.students  GROUP BY institution_id), MaxInstitution AS (  SELECT institution_id, student_count  FROM StudentCounts  WHERE student_count = (SELECT MAX(student_count) FROM StudentCounts)) SELECT i.name AS institution_name, sc.student_count, CASE WHEN sc.institution_id = mi.institution_id THEN 'Most Students' ELSE 'Other' END AS comparison FROM StudentCounts sc JOIN auth.institutions i ON sc.institution_id = i.id JOIN MaxInstitution mi ON 1=1;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and then comparing it to the maximum count. It uses a Common Table Expression (CTE) to first calculate the student counts and then determine which institution has the maximum count. The final SELECT statement retrieves the names of the institutions along with their student counts and labels them as 'Most Students' or 'Other' based on the comparison. This directly addresses the question of how the student population of the institution with the most students compares to others.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating the comparison in a more detailed manner, such as including the percentage difference or a more descriptive comparison metric. Additionally, ensure that the schema references are consistent (e.g., 'core.students' vs. 'auth.institutions')."}
2025-08-08 06:48:19,732 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:19,732 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:48:20,227 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:20,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:21,002 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:21,038 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about institutions, students, programs, and various academic and administrative records, but it does not include qualitative insights or analyses regarding retention rates or the factors influencing them. Therefore, this question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 06:48:21,038 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:21,038 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:21,038 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about institutions, students, programs, and various academic and administrative records, but it does not include qualitative insights or analyses regarding retention rates or the factors influencing them. Therefore, this question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 06:48:21,261 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:21,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:21,839 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How has the student enrollment at the institution with the most students changed over the past few years?', 'sql': "WITH InstitutionStudentCounts AS (  SELECT institution_id, COUNT(*) AS student_count  FROM core.students  GROUP BY institution_id), MostStudentsInstitution AS (  SELECT institution_id  FROM InstitutionStudentCounts  ORDER BY student_count DESC  LIMIT 1) SELECT s.created_at, COUNT(sp.student_id) AS enrollment_count  FROM core.student_programs sp  JOIN core.students s ON sp.student_id = s.id  WHERE s.institution_id = (SELECT institution_id FROM MostStudentsInstitution)  AND s.created_at >= DATE_TRUNC('year', CURRENT_DATE) - INTERVAL '5 years'  GROUP BY s.created_at ORDER BY s.created_at;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and selecting the one with the highest count. It then retrieves the enrollment counts for that institution over the past five years by filtering the student records based on their creation date. The use of 'DATE_TRUNC' ensures that the query captures the enrollment data accurately by year. The final output groups the results by the creation date, which aligns with the question's requirement to analyze changes in enrollment over time.", 'feedback': 'The question could be clarified by specifying what kind of changes are being looked for (e.g., total enrollment numbers, trends, etc.). Additionally, the SQL could be improved by including a more explicit time frame in the output, such as year or month, to make the changes in enrollment clearer.'}
2025-08-08 06:48:21,839 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:21,839 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:48:22,608 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:22,718 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:22,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:22,776 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do these admission criteria impact the diversity and academic performance of the student body at the institution?', 'answerable': False, 'reasoning': 'The question asks about the impact of admission criteria on diversity and academic performance, which requires qualitative analysis and data interpretation beyond the raw data available in the schema. The schema provides tables related to admissions, applicants, and academic results, but it does not contain specific metrics or analyses that directly link admission criteria to diversity or academic performance outcomes. Therefore, while some data may be indirectly related, the question cannot be answered completely or correctly using the schema alone.', 'feedback': ''}
2025-08-08 06:48:22,776 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:22,776 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:22,776 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do these admission criteria impact the diversity and academic performance of the student body at the institution?', 'answerable': False, 'reasoning': 'The question asks about the impact of admission criteria on diversity and academic performance, which requires qualitative analysis and data interpretation beyond the raw data available in the schema. The schema provides tables related to admissions, applicants, and academic results, but it does not contain specific metrics or analyses that directly link admission criteria to diversity or academic performance outcomes. Therefore, while some data may be indirectly related, the question cannot be answered completely or correctly using the schema alone.', 'feedback': ''}
2025-08-08 06:48:22,958 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:23,132 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:23,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:23,491 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 06:48:23,491 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:23,491 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:23,491 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 06:48:23,586 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:23,835 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:24,346 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 503 Service Unavailable"
2025-08-08 06:48:24,347 - openai._base_client - INFO - Retrying request to /chat/completions in 0.388908 seconds
2025-08-08 06:48:24,364 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:24,389 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:24,933 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:25,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:25,051 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about institutions, students, programs, and various academic and administrative records, but it does not include qualitative insights, opinions, or specific retention strategies that would be necessary to answer the question. Therefore, it cannot be answered using the schema.', 'feedback': ''}
2025-08-08 06:48:25,051 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:25,051 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:25,051 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about institutions, students, programs, and various academic and administrative records, but it does not include qualitative insights, opinions, or specific retention strategies that would be necessary to answer the question. Therefore, it cannot be answered using the schema.', 'feedback': ''}
2025-08-08 06:48:25,167 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:25,450 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:25,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:25,521 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or explicitly represented in the provided schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not provide specific metrics or qualitative data that would allow for an analysis of factors influencing enrollment numbers. To answer this question, one would need additional context or data, such as marketing strategies, student satisfaction surveys, or demographic trends, none of which are present in the schema.', 'feedback': ''}
2025-08-08 06:48:25,521 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:25,522 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:25,522 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or explicitly represented in the provided schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not provide specific metrics or qualitative data that would allow for an analysis of factors influencing enrollment numbers. To answer this question, one would need additional context or data, such as marketing strategies, student satisfaction surveys, or demographic trends, none of which are present in the schema.', 'feedback': ''}
2025-08-08 06:48:25,683 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:26,046 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:26,853 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:27,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:27,091 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do these admission criteria impact the diversity and academic performance of the student body at the institution?', 'answerable': False, 'reasoning': 'The question asks about the impact of admission criteria on diversity and academic performance, which requires qualitative analysis and data interpretation beyond the raw data available in the schema. The schema provides information about applicants, admission forms, and related entities, but it does not contain specific metrics or analyses regarding diversity or academic performance outcomes. To answer this question, one would need to analyze trends, statistics, and possibly qualitative data that are not present in the schema.', 'feedback': ''}
2025-08-08 06:48:27,091 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:27,091 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:27,091 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do these admission criteria impact the diversity and academic performance of the student body at the institution?', 'answerable': False, 'reasoning': 'The question asks about the impact of admission criteria on diversity and academic performance, which requires qualitative analysis and data interpretation beyond the raw data available in the schema. The schema provides information about applicants, admission forms, and related entities, but it does not contain specific metrics or analyses regarding diversity or academic performance outcomes. To answer this question, one would need to analyze trends, statistics, and possibly qualitative data that are not present in the schema.', 'feedback': ''}
2025-08-08 06:48:27,265 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:27,609 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:27,758 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:27,844 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:29,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:29,094 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers in these education programs, and how do they reflect current trends in the job market for educators?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high enrollment numbers in education programs and their reflection on job market trends for educators. This requires qualitative insights and external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, students, and various related entities, but it does not include qualitative factors or trends analysis that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 06:48:29,094 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:29,095 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:29,095 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers in these education programs, and how do they reflect current trends in the job market for educators?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high enrollment numbers in education programs and their reflection on job market trends for educators. This requires qualitative insights and external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, students, and various related entities, but it does not include qualitative factors or trends analysis that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 06:48:29,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:29,387 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 06:48:29,387 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:29,387 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:29,387 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 06:48:29,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:29,561 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various administrative aspects, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the reasons behind student attraction and retention. Therefore, this question cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 06:48:29,562 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:29,562 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:29,562 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various administrative aspects, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the reasons behind student attraction and retention. Therefore, this question cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 06:48:29,762 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:29,788 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a query that can be answered with specific data from the provided schema. The schema contains tables related to student information, programs, admissions, and other operational data, but it does not provide insights or qualitative factors that could influence enrollment trends. Therefore, it cannot be answered directly or indirectly using the schema.', 'feedback': ''}
2025-08-08 06:48:29,789 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:29,789 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:29,789 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a query that can be answered with specific data from the provided schema. The schema contains tables related to student information, programs, admissions, and other operational data, but it does not provide insights or qualitative factors that could influence enrollment trends. Therefore, it cannot be answered directly or indirectly using the schema.', 'feedback': ''}
2025-08-08 06:48:29,859 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:29,873 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with a single SQL statement. The schema provides data about institutions, applicants, and their statuses, but it does not contain specific metrics or factors that directly correlate to enrollment numbers. To answer this question, one would need to analyze various data points such as admission rates, applicant demographics, program offerings, and possibly external factors, none of which are explicitly detailed in the schema.', 'feedback': ''}
2025-08-08 06:48:29,873 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:29,873 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:29,873 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with a single SQL statement. The schema provides data about institutions, applicants, and their statuses, but it does not contain specific metrics or factors that directly correlate to enrollment numbers. To answer this question, one would need to analyze various data points such as admission rates, applicant demographics, program offerings, and possibly external factors, none of which are explicitly detailed in the schema.', 'feedback': ''}
2025-08-08 06:48:31,170 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:31,749 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:31,820 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:31,849 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:32,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:33,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:33,398 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers in these education programs, and how do they reflect current trends in the job market for educators?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high enrollment numbers in education programs and their reflection on job market trends for educators. This requires qualitative insights and external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, students, and related entities, but it does not include qualitative factors or trends analysis that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 06:48:33,398 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:33,398 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:33,398 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers in these education programs, and how do they reflect current trends in the job market for educators?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high enrollment numbers in education programs and their reflection on job market trends for educators. This requires qualitative insights and external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, students, and related entities, but it does not include qualitative factors or trends analysis that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 06:48:33,965 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:33,980 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various administrative aspects, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the university's attractiveness. Therefore, this question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-08 06:48:33,980 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:33,980 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:33,980 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various administrative aspects, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the university's attractiveness. Therefore, this question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-08 06:48:34,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:34,180 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 06:48:34,181 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:34,181 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:34,181 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education, particularly regarding gender and other demographics such as ethnicity?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education, specifically regarding gender and ethnicity. However, the provided schema does not contain any tables or fields that explicitly store national demographic trends or comparative data. While there are fields related to student demographics (such as gender and nationality), there is no data available in the schema that would allow for a comparison to national trends. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 06:48:34,185 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:34,196 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a quantitative one. The provided schema contains tables related to student data, institutions, programs, and various administrative aspects, but it does not include any specific data or metrics that would allow for an analysis of trends or factors influencing enrollment. The schema lacks qualitative insights or external factors that could affect enrollment trends, such as economic conditions, demographic changes, or institutional reputation.', 'feedback': ''}
2025-08-08 06:48:34,196 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:34,196 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:34,196 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a quantitative one. The provided schema contains tables related to student data, institutions, programs, and various administrative aspects, but it does not include any specific data or metrics that would allow for an analysis of trends or factors influencing enrollment. The schema lacks qualitative insights or external factors that could affect enrollment trends, such as economic conditions, demographic changes, or institutional reputation.', 'feedback': ''}
2025-08-08 06:48:34,533 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:34,554 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with a specific SQL query. The schema provides data about institutions, applicants, and related entities, but it does not contain direct metrics or factors that would explain enrollment numbers, such as marketing strategies, community engagement, or other qualitative factors. Therefore, while the schema contains relevant data, it does not provide a complete answer to the question.', 'feedback': ''}
2025-08-08 06:48:34,554 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:34,555 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:34,555 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with a specific SQL query. The schema provides data about institutions, applicants, and related entities, but it does not contain direct metrics or factors that would explain enrollment numbers, such as marketing strategies, community engagement, or other qualitative factors. Therefore, while the schema contains relevant data, it does not provide a complete answer to the question.', 'feedback': ''}
2025-08-08 06:48:35,188 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:35,838 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:35,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:36,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:37,414 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:37,433 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers in these education programs, and how do they reflect current trends in the job market for educators?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high enrollment numbers in education programs and their reflection on job market trends for educators. This requires qualitative insights and external data that are not present in the provided database schema, which primarily contains structured data about institutions, students, programs, and related entities. The schema does not include any information about job market trends or qualitative factors influencing enrollment.', 'feedback': ''}
2025-08-08 06:48:37,434 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:37,434 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:37,434 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers in these education programs, and how do they reflect current trends in the job market for educators?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high enrollment numbers in education programs and their reflection on job market trends for educators. This requires qualitative insights and external data that are not present in the provided database schema, which primarily contains structured data about institutions, students, programs, and related entities. The schema does not include any information about job market trends or qualitative factors influencing enrollment.', 'feedback': ''}
2025-08-08 06:48:37,888 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:37,908 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various administrative aspects, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the university's attractiveness. Therefore, the question cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 06:48:37,909 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:37,909 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:37,909 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various administrative aspects, but it does not include qualitative insights, opinions, or specific marketing strategies that would explain the university's attractiveness. Therefore, the question cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 06:48:38,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:38,464 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a quantitative one. The provided schema contains a wealth of data related to institutions, students, programs, and various administrative aspects, but it does not include qualitative insights or external factors that could influence enrollment trends, such as economic conditions, societal trends, or institutional reputation. Therefore, while the schema can provide data on enrollment numbers and related metrics, it cannot answer the question regarding the influencing factors.', 'feedback': ''}
2025-08-08 06:48:38,464 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:38,464 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:38,464 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a quantitative one. The provided schema contains a wealth of data related to institutions, students, programs, and various administrative aspects, but it does not include qualitative insights or external factors that could influence enrollment trends, such as economic conditions, societal trends, or institutional reputation. Therefore, while the schema can provide data on enrollment numbers and related metrics, it cannot answer the question regarding the influencing factors.', 'feedback': ''}
2025-08-08 06:48:39,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:39,038 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with a single SQL statement. The schema provides data about institutions, applicants, and various related entities, but it does not contain specific metrics or factors that directly correlate to enrollment numbers. To answer this question, one would need to analyze multiple data points, such as admission rates, applicant demographics, program offerings, and possibly external factors, none of which are explicitly defined in the schema.', 'feedback': ''}
2025-08-08 06:48:39,038 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:39,038 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:39,038 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with a single SQL statement. The schema provides data about institutions, applicants, and various related entities, but it does not contain specific metrics or factors that directly correlate to enrollment numbers. To answer this question, one would need to analyze multiple data points, such as admission rates, applicant demographics, program offerings, and possibly external factors, none of which are explicitly defined in the schema.', 'feedback': ''}
2025-08-08 06:48:39,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:39,866 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:40,655 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:41,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:41,603 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers in these education programs, and how do they reflect current trends in the job market for educators?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high enrollment numbers in education programs and their reflection on job market trends for educators. This requires qualitative insights and external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, students, and related entities, but it does not include qualitative factors or trends analysis that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 06:48:41,604 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:41,604 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:41,604 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers in these education programs, and how do they reflect current trends in the job market for educators?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high enrollment numbers in education programs and their reflection on job market trends for educators. This requires qualitative insights and external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, students, and related entities, but it does not include qualitative factors or trends analysis that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 06:48:42,026 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:42,051 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative insights, opinions, or specific factors that would explain the university's attractiveness or student retention. Such factors would typically require analysis of external data, surveys, or qualitative research, which are not represented in the schema.", 'feedback': ''}
2025-08-08 06:48:42,051 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:42,051 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:42,051 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to other institutions. The provided database schema contains structured data about institutions, students, programs, and various related entities, but it does not include qualitative insights, opinions, or specific factors that would explain the university's attractiveness or student retention. Such factors would typically require analysis of external data, surveys, or qualitative research, which are not represented in the schema.", 'feedback': ''}
2025-08-08 06:48:43,138 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:43,158 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a query that can be answered with specific data from the provided schema. The schema contains tables related to student data, institutions, programs, and various administrative aspects, but it does not provide insights or qualitative factors that could influence enrollment trends. Therefore, it cannot be answered directly or indirectly using the schema.', 'feedback': ''}
2025-08-08 06:48:43,159 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:48:43,159 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:48:43,159 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could influence student enrollment trends at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for factors influencing student enrollment trends at large institutions, which is a qualitative analysis rather than a query that can be answered with specific data from the provided schema. The schema contains tables related to student data, institutions, programs, and various administrative aspects, but it does not provide insights or qualitative factors that could influence enrollment trends. Therefore, it cannot be answered directly or indirectly using the schema.', 'feedback': ''}
2025-08-08 06:48:43,160 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 06:48:43,160 - root - INFO - [{'total_students': 192627}]
2025-08-08 06:48:43,160 - root - INFO - [{'institution_name': 'ITC University', 'student_count': 49153, 'comparison': 'Other'}, {'institution_name': 'Akrofi-Christaller Institute of Theology Mission and Culture', 'student_count': 114, 'comparison': 'Other'}, {'institution_name': 'Accra Medical', 'student_count': 13012, 'comparison': 'Other'}, {'institution_name': 'Ghana School of Law', 'student_count': 262, 'comparison': 'Other'}, {'institution_name': 'Koforidua Technical University', 'student_count': 17758, 'comparison': 'Other'}, {'institution_name': 'Presbyterian University College Ghana', 'student_count': 32094, 'comparison': 'Other'}, {'institution_name': 'Methodist University College Ghana', 'student_count': 1, 'comparison': 'Other'}, {'institution_name': 'Wisconsin International University College', 'student_count': 1202, 'comparison': 'Other'}, {'institution_name': 'ITC University', 'student_count': 192627, 'comparison': 'Most Students'}, {'institution_name': 'Kofi Annan International Peacekeeping Training Centre', 'student_count': 52, 'comparison': 'Other'}, {'institution_name': 'Klintaps University College of Health and Allied Sciences', 'student_count': 1135, 'comparison': 'Other'}]
2025-08-08 06:48:43,161 - root - INFO - [{'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'student_count': 23927}, {'long_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'student_count': 10732}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY)', 'student_count': 9618}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'student_count': 8905}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'student_count': 7662}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE)', 'student_count': 7606}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'student_count': 5826}, {'long_name': 'Bachelor Of Science (Mathematics Education)', 'student_count': 5492}, {'long_name': 'BACHELOR OF ARTS (POLITICAL SCIENCE EDUCATION)', 'student_count': 5305}, {'long_name': 'Bachelor Of Science (Information Technology Education)', 'student_count': 4481}, {'long_name': 'BACHELOR OF ART (SOCIAL STUDIES EDUCATION)', 'student_count': 4255}, {'long_name': 'BACHELOR OF ARTS (ENGLISH LANGUAGE EDUCATION)', 'student_count': 3826}, {'long_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'student_count': 3519}, {'long_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'student_count': 3348}, {'long_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'student_count': 3330}, {'long_name': 'BACHELOR OF ARTS (SOCIAL STUDIES EDUCATION)', 'student_count': 2989}, {'long_name': 'BACHELOR OF ARTS (GEOGRAPHY EDUCATION)', 'student_count': 2947}, {'long_name': 'Diploma In Education', 'student_count': 2821}, {'long_name': 'BACHELOR OF SCIENCE (MANAGEMENT EDUCATION)', 'student_count': 2750}, {'long_name': 'Bachelor Of Science (Accounting Education)', 'student_count': 2577}, {'long_name': 'BACHELOR OF ARTS (ART EDUCATION)', 'student_count': 2503}, {'long_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'student_count': 2381}, {'long_name': 'DIPLOMA IN EARLY GRADE', 'student_count': 1994}, {'long_name': 'Bachelor Of Science (Agricultural Science Education)', 'student_count': 1988}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED SCIENCE EDUCATION)', 'student_count': 1974}, {'long_name': 'BACHELOR OF ARTS (ECONOMICS EDUCATION)', 'student_count': 1921}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICAL EDUCATION)', 'student_count': 1678}, {'long_name': 'DIPLOMA IN BASIC EDUCATION', 'student_count': 1580}, {'long_name': 'BACHELOR OF ARTS (FRENCH EDUCATION)', 'student_count': 1574}, {'long_name': 'BACHELOR OF ARTS (GRAPHIC DESIGN)', 'student_count': 1558}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - R', 'student_count': 1508}, {'long_name': 'BACHELOR OF ARTS (EWE EDUCATION)', 'student_count': 1397}, {'long_name': 'BACHELOR OF SCIENCE (FOOD AND NUTRITION EDUCATION)', 'student_count': 1359}, {'long_name': 'BACHELOR OF ARTS (ENGLISH EDUCATION)', 'student_count': 1358}, {'long_name': 'Bachelor Of Science (Biological Sciences Education)', 'student_count': 1193}, {'long_name': 'Bachelor Of Science (Environmental Health And Sanitation Education)', 'student_count': 1175}, {'long_name': 'BACHELOR OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'student_count': 1173}, {'long_name': 'Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'student_count': 1171}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - R', 'student_count': 1160}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - S', 'student_count': 1137}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'student_count': 1089}, {'long_name': 'BACHELOR OF ARTS (TWI EDUCATION)', 'student_count': 1051}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION AND MANAGEMENT) BY DISTANCE', 'student_count': 1029}, {'long_name': 'BACHELOR OF ARTS (HISTORY EDUCATION)', 'student_count': 989}, {'long_name': 'BACHELOR OF SCIENCE (HEALTH ADMINISTRATION EDUCATION)', 'student_count': 942}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL EDUCATION)', 'student_count': 929}, {'long_name': 'Bachelor Of Arts (Social Studies With Economics Education)', 'student_count': 880}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED HOME ECONOMICS EDUCATION)', 'student_count': 870}, {'long_name': 'BACHELOR OF EDUCATION (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'student_count': 858}, {'long_name': 'Bachelor Of Education (Early Grade) - M', 'student_count': 816}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - R', 'student_count': 811}, {'long_name': 'BACHELOR OF ARTS (MUSIC EDUCATION)', 'student_count': 751}, {'long_name': 'Bachelor Of Education (Junior High)', 'student_count': 707}, {'long_name': 'BACHELOR OF SCIENCE (BIOLOGY EDUCATION)', 'student_count': 704}, {'long_name': 'DIPLOMA (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'student_count': 690}, {'long_name': 'BACHELOR OF ARTS (FASHION DESIGN AND TEXTILE EDUCATION)', 'student_count': 684}, {'long_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'student_count': 663}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 649}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - S', 'student_count': 627}, {'long_name': 'Master Of Arts In Educational Leadership', 'student_count': 542}, {'long_name': 'MASTER OF PHILOSOPHY (GHANAIAN LANGUAGE STUDIES)', 'student_count': 517}, {'long_name': 'Diploma In Education - KS', 'student_count': 509}, {'long_name': 'BACHELOR OF SCIENCE (HOME ECONOMICS EDUCATION)', 'student_count': 501}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'student_count': 481}, {'long_name': 'BACHELOR OF ARTS (TWI WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 466}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'student_count': 444}, {'long_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'student_count': 444}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'student_count': 439}, {'long_name': 'MASTER OF PHILOSOPHY (ENGLISH LANGUAGE)', 'student_count': 400}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICS EDUCATION)', 'student_count': 393}, {'long_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'student_count': 392}, {'long_name': 'MASTER OF PHILOSOPHY (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'student_count': 379}, {'long_name': 'BACHELOR  OF ARTS (EWE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 366}, {'long_name': 'MASTER OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'student_count': 364}, {'long_name': 'Bachelor Of Arts (Economics With Social Studies Education)', 'student_count': 351}, {'long_name': 'BACHELOR OF MUSIC', 'student_count': 351}, {'long_name': 'Bachelor Of Business Administration (Management) - E', 'student_count': 348}, {'long_name': 'MASTER OF PHILOSOPHY (SCIENCE EDUCATION)', 'student_count': 342}, {'long_name': 'MASTER OF PHILOSOPHY (EARLY CHILDHOOD EDUCATION)', 'student_count': 340}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND ENGLISH EDUCATION)', 'student_count': 339}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 325}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY) BY DISTANCE', 'student_count': 323}, {'long_name': 'BACHELOR OF ARTS (FANTE EDUCATION)', 'student_count': 316}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - S', 'student_count': 315}, {'long_name': 'Bachelor Of Science (Information Technology Education) - W', 'student_count': 310}, {'long_name': 'Diploma In Business Administration (Management)', 'student_count': 309}, {'long_name': 'MASTER OF PHILOSOPHY (ART EDUCATION)', 'student_count': 308}, {'long_name': 'BACHELOR OF ARTS (COMMUNICATION STUDIES)', 'student_count': 306}, {'long_name': 'DIPLOMA (GRAPHIC DESIGN)', 'student_count': 298}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (MARKETING AND ENTREPRENEURSHIP)', 'student_count': 296}, {'long_name': 'DIPLOMA (ART)', 'student_count': 295}, {'long_name': 'Bachelor Of Science (Agribusiness Management And Entrepreneurship Education)', 'student_count': 291}, {'long_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'student_count': 283}, {'long_name': 'MASTER OF PHILOSOPHY IN SOCIAL STUDIES', 'student_count': 283}, {'long_name': 'BACHELOR OF ARTS (THEATRE ARTS)', 'student_count': 269}, {'long_name': 'MASTER OF PHILOSOPHY (SPECIAL EDUCATION)', 'student_count': 265}, {'long_name': 'MASTER OF PHILOSOPHY (BASIC EDUCATION)', 'student_count': 254}, {'long_name': 'DIPLOMA IN MANAGEMENT STUDIES', 'student_count': 251}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES)', 'student_count': 243}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'student_count': 232}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS WITH ECONOMICS EDUCATION)', 'student_count': 226}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - W', 'student_count': 224}, {'long_name': 'DIPLOMA IN MUSIC', 'student_count': 208}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education) - S', 'student_count': 207}, {'long_name': 'MASTER OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'student_count': 202}, {'long_name': 'Bachelor Of Science (Information Technology)', 'student_count': 193}, {'long_name': 'Bachelor Of Science (Natural Resources Management And Education)', 'student_count': 192}, {'long_name': 'Bachelor Of Science (Wood Technology Education) - S', 'student_count': 192}, {'long_name': 'MASTER OF PHILOSOPHY (FRENCH)', 'student_count': 189}, {'long_name': 'MASTER OF EDUCATION (EARLY CHILDHOOD EDUCATION)', 'student_count': 188}, {'long_name': 'DOCTOR OF PHILOSOPHY (SCIENCE EDUCATION)', 'student_count': 187}, {'long_name': 'BACHELOR OF ARTS (CREATIVE ARTS AND DESIGN EDUCATION)', 'student_count': 182}, {'long_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'student_count': 181}, {'long_name': 'MASTER OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'student_count': 178}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'student_count': 175}, {'long_name': 'Master Of Technology Education In Catering And Hospitality', 'student_count': 175}, {'long_name': 'Bachelor Of Science In Administration (Accounting)', 'student_count': 173}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND TWI EDUCATION)', 'student_count': 171}, {'long_name': 'MASTER OF PHILOSOPHY (INFORMATION AND COMMUNICATION TECHNOLOGY)', 'student_count': 169}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'student_count': 167}, {'long_name': 'MASTER OF PHILOSOPHY (POLITICAL SCIENCE EDUCATION)', 'student_count': 167}, {'long_name': 'Bachelor Of Education (Early Grade) - K', 'student_count': 166}, {'long_name': 'Bachelor Of Business Administration (Management) - W', 'student_count': 163}, {'long_name': 'Bachelor Of Arts (Arabic Education)', 'student_count': 158}, {'long_name': 'BACHELOR OF ARTS (STRATEGIC COMMUNICATION)', 'student_count': 156}, {'long_name': 'MASTER OF PHILOSOPHY (FOOD AND NUTRITION)', 'student_count': 155}, {'long_name': 'MASTER OF PHILOSOPHY (MUSIC EDUCATION)', 'student_count': 154}, {'long_name': 'BACHELOR OF ARTS (RELIGIONS AND MORAL STUDIES EDUCATION)', 'student_count': 153}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'student_count': 150}, {'long_name': 'Master Of Business Administration (Human Resource Management And Organisational Behaviour ) - Weekend', 'student_count': 149}, {'long_name': 'DIPLOMA (THEATRE ARTS)', 'student_count': 144}, {'long_name': 'DIPLOMA (BASIC EDUCATION)', 'student_count': 143}, {'long_name': 'Bachelor Of Business Administration (Secretarial Education)', 'student_count': 129}, {'long_name': 'Diploma In Education - TM', 'student_count': 128}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - R', 'student_count': 127}, {'long_name': 'MASTER OF EDUCATION (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'student_count': 124}, {'long_name': 'BACHELOR ARTS (GA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 123}, {'long_name': 'DIPLOMA (EARLY GRADE)', 'student_count': 121}, {'long_name': 'MASTER OF ARTS (ENGLISH LANGUAGE)', 'student_count': 119}, {'long_name': 'Master Of Business Administration (Accounting)', 'student_count': 119}, {'long_name': 'MASTER OF EDUCATION (SCIENCE)', 'student_count': 119}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'student_count': 118}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS)', 'student_count': 118}, {'long_name': 'MASTER OF EDUCATION (HOME ECONOMICS)', 'student_count': 115}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education)', 'student_count': 114}, {'long_name': 'DIPLOMA (TEXTILES AND FASHION)', 'student_count': 114}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - S', 'student_count': 114}, {'long_name': 'Bachelor Of Science (Mathematics Education) - M', 'student_count': 112}, {'long_name': 'Master Of Philosophy In Catering And Hospitality', 'student_count': 112}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (FINANCE)', 'student_count': 111}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'student_count': 110}, {'long_name': 'MASTER OF ARTS (STRATEGIC COMMUNICATION)', 'student_count': 108}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FRENCH EDUCATION)', 'student_count': 107}, {'long_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'student_count': 106}, {'long_name': 'BACHELOR OF SCIENCE (FAMILY LIFE MANAGEMENT EDUCATION)', 'student_count': 106}, {'long_name': 'DOCTOR OF PHILOSOPHY (ARTS AND CULTURE)', 'student_count': 105}, {'long_name': 'BACHELOR OF ARTS (DANGME EDUCATION)', 'student_count': 104}, {'long_name': 'BACHELOR OF ARTS (TWI WITH LINGUISTICS EDUCATION)', 'student_count': 104}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BUSINESS INFORMATION SYSTEMS)', 'student_count': 104}, {'long_name': 'Post Diploma Bachelor Of Science (Environmental Health And Sanitation Education)', 'student_count': 102}, {'long_name': 'CERTIFICATE (PRE-SCHOOL EDUCATION)', 'student_count': 100}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education) - S', 'student_count': 99}, {'long_name': 'MASTER OF PHILOSOPHY (CURRICULUM & PEDAGOGIC STUDIES)', 'student_count': 99}, {'long_name': 'DIPLOMA (FRENCH EDUCATION)', 'student_count': 98}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology Education) - S', 'student_count': 98}, {'long_name': 'Bachelor Of Science (Wood Technology Education)', 'student_count': 96}, {'long_name': 'MASTER OF PHILOSOPHY (CHEMISTRY EDUCATION)', 'student_count': 96}, {'long_name': 'DIPLOMA IN ACCOUNTING', 'student_count': 95}, {'long_name': 'DOCTOR OF PHILOSOPHY (APPLIED LINGUISTICS)', 'student_count': 95}, {'long_name': 'MASTER OF ARTS (GHANAIAN LANGUAGES EDUCATION)', 'student_count': 95}, {'long_name': 'Master Of Philosophy In Construction Management', 'student_count': 95}, {'long_name': 'BACHELOR OF SCIENCE (SPORTS COACHING)', 'student_count': 94}, {'long_name': 'BACHELOR OF ART (SISAALI WITH ENGLISH EDUCATION)', 'student_count': 93}, {'long_name': 'BACHELOR OF ARTS (DAGBANI EDUCATION)', 'student_count': 93}, {'long_name': 'BACHELOR OF ARTS (DAGAARE EDUCATION)', 'student_count': 90}, {'long_name': 'Master Of Philosophy In Construction Technology', 'student_count': 90}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (FINANCE)', 'student_count': 89}, {'long_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'student_count': 86}, {'long_name': 'MASTER OF PHILOSOPHY (ECONOMICS)', 'student_count': 86}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT COMMUNICATION)', 'student_count': 84}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH ENGLISH EDUCATION)', 'student_count': 82}, {'long_name': 'POST GRADUATE DIPLOMA IN TEACHING AND LEARNING IN HIGHER EDUCATION', 'student_count': 82}, {'long_name': 'EXECUTIVE MASTERS (CONFLICT HUMAN RIGHTS & PEACE STUDIES)', 'student_count': 81}, {'long_name': 'Bachelor Of Science (Automotive Technology Education) - S', 'student_count': 80}, {'long_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'student_count': 80}, {'long_name': 'Diploma In Electrical And Electronics Engineering Technology', 'student_count': 79}, {'long_name': 'Diploma In Environmental Health And Sanitation Education', 'student_count': 79}, {'long_name': 'MASTER OF EDUCATION (INSTITUTIONAL MENTORSHIP AND SUPERVISION)', 'student_count': 79}, {'long_name': 'Master Of Philosophy In Mathematics Education', 'student_count': 79}, {'long_name': 'MASTER OF PHILOSOPHY (VISUAL COMMUNICATION)', 'student_count': 77}, {'long_name': 'DOCTOR OF PHILOSOPHY (SOCIAL STUDIES)', 'student_count': 75}, {'long_name': 'MASTER OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'student_count': 75}, {'long_name': 'MASTER OF PHILOSOPHY (STRATEGIC COMMUNICATION)', 'student_count': 75}, {'long_name': 'BACHELOR OF ART (DANGME WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 74}, {'long_name': 'Diploma In Business Administration (Accounting)', 'student_count': 74}, {'long_name': 'Master Of Science In Information Technology Education', 'student_count': 74}, {'long_name': 'Master Of Technology In Construction Technology', 'student_count': 74}, {'long_name': 'Post Diploma Bachelor Of Science In Environmental Health And Sanitation Education - S', 'student_count': 74}, {'long_name': 'BACHELOR OF ARTS (EWE WITH LINGUISTICS EDUCATION)', 'student_count': 72}, {'long_name': 'BACHELOR OF ARTS (EWE WITH FRENCH EDUCATION)', 'student_count': 71}, {'long_name': 'BACHELOR OF FINE ART (ANIMATION)', 'student_count': 70}, {'long_name': 'Diploma In Catering And Hospitality', 'student_count': 68}, {'long_name': 'Master Of Philosophy In Educational Leadership', 'student_count': 67}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'student_count': 67}, {'long_name': 'DOCTOR OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'student_count': 66}, {'long_name': 'Master Of Philosophy In Environmental And Occupational Health Education', 'student_count': 66}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - R', 'student_count': 65}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'student_count': 65}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles', 'student_count': 65}, {'long_name': 'Bachelor Of Arts (French With English Education)', 'student_count': 64}, {'long_name': 'Bachelor Of Science (Mathematics Education) - W', 'student_count': 64}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH LINGUISTIC EDUCATION)', 'student_count': 63}, {'long_name': 'DOCTOR OF PHILOSOPHY (EDUCATIONAL LEADERSHIP)', 'student_count': 63}, {'long_name': 'Master Of Philosophy In Accounting', 'student_count': 62}, {'long_name': 'Diploma In Economics', 'student_count': 60}, {'long_name': 'BACHELOR OF ARTS (DAGAARE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 59}, {'long_name': 'DIPLOMA IN SPORTS COACHING', 'student_count': 59}, {'long_name': 'MASTER OF EDUCATION (GEOGRAPHY)', 'student_count': 59}, {'long_name': 'Master Of Philosophy In Mathematics Education - W', 'student_count': 59}, {'long_name': 'DOCTOR OF PHILOSOPHY (COMMUNICATION AND MEDIA)', 'student_count': 58}, {'long_name': 'Master Of Technology In Construction Management', 'student_count': 58}, {'long_name': 'MASTER OF PHILOSOPHY (ASSESSMENT, MEASUREMENT AND EVALUATION)', 'student_count': 57}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH LINGUISTICS EDUCATION)', 'student_count': 55}, {'long_name': 'BACHELOR OF EDUCATION (PSYCHOLOGY)', 'student_count': 55}, {'long_name': 'Bachelor Of Science (Wood Technology With Education)', 'student_count': 55}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH LINGUISTICS EDUCATION)', 'student_count': 54}, {'long_name': 'Master Of Philosophy In Business Management', 'student_count': 54}, {'long_name': 'Master Of Philosophy In Catering And Hospitality (Top-Up)', 'student_count': 54}, {'long_name': 'Diploma In Education - TK', 'student_count': 53}, {'long_name': 'DOCTOR OF PHILOSOPHY IN GHANAIAN LANGUAGES', 'student_count': 53}, {'long_name': 'MASTER OF PHILOSOPHY (ACCOUNTING)', 'student_count': 52}, {'long_name': 'Bachelor Of Science (Wood Technology With Education) - S', 'student_count': 51}, {'long_name': 'Diploma In Education - CP', 'student_count': 51}, {'long_name': 'Diploma In Education - M', 'student_count': 51}, {'long_name': 'MASTER OF ARTS (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES', 'student_count': 51}, {'long_name': 'MASTER OF EDUCATION (FRENCH)', 'student_count': 51}, {'long_name': 'MASTER OF EDUCATION (SPECIAL EDUCATION)', 'student_count': 49}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH FRENCH EDUCATION)', 'student_count': 48}, {'long_name': 'BACHELOR OF ARTS (DEVELOPMENT COMMUNICATION)', 'student_count': 48}, {'long_name': 'BACHELOR OF ARTS (EWE WITH GERMAN EDUCATION)', 'student_count': 48}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - E', 'student_count': 48}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (LOGISTICS AND TRANSPORT MANAGEMENT)', 'student_count': 48}, {'long_name': 'Diploma In Construction Technology', 'student_count': 48}, {'long_name': 'Diploma In Fashion Design And Textiles', 'student_count': 48}, {'long_name': 'Master Of Education In Agriculture', 'student_count': 48}, {'long_name': 'BACHELOR OF ARTS (TWI WITH GERMAN EDUCATION)', 'student_count': 47}, {'long_name': 'Bachelor Of Science In Occupational Health And Safety', 'student_count': 47}, {'long_name': 'MASTER OF EDUCATION (GUIDANCE AND COUNSELLING)', 'student_count': 47}, {'long_name': 'Master Of Philosophy In Construction Management (Top Up)', 'student_count': 47}, {'long_name': 'Master Of Philosophy In Mechanical Engineering Technology', 'student_count': 47}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 46}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 46}, {'long_name': 'BACHELOR OF ARTS (GA EDUCATION)', 'student_count': 45}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - W', 'student_count': 45}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - W', 'student_count': 45}, {'long_name': 'Master Of Philosophy In Science Education', 'student_count': 42}, {'long_name': 'Master Of Technology Education In Fashion Design And Textile', 'student_count': 42}, {'long_name': 'MASTER OF PHILOSOPHY (CLOTHING AND TEXTILES)', 'student_count': 41}, {'long_name': 'Master Of Philosophy In Wood Science And Technology', 'student_count': 41}, {'long_name': 'BACHELOR OF ART (KUSAAL WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 40}, {'long_name': 'MASTER OF PHILOSOPHY (INTEGRATED SCIENCE EDUCATION)', 'student_count': 40}, {'long_name': 'MASTER OF PHILOSOPHY (TEXTILES AND FASHION)', 'student_count': 40}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH GERMAN EDUCATION)', 'student_count': 39}, {'long_name': 'DOCTOR OF PHILOSOPHY (ENGLISH LANGUAGE)', 'student_count': 39}, {'long_name': 'BACHELOR OF ARTS (GA WITH LINGUISTICS EDUCATION)', 'student_count': 38}, {'long_name': 'Bachelor Of Science (Marketing) - W', 'student_count': 37}, {'long_name': 'Master Of Philosophy In Biology', 'student_count': 37}, {'long_name': 'Master Of Technology In Electrical And Electronics Engineering', 'student_count': 37}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH LINGUISTICS EDUCATION)', 'student_count': 36}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance)', 'student_count': 36}, {'long_name': 'DOCTOR OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'student_count': 36}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education)', 'student_count': 36}, {'long_name': 'Doctor Of Philosophy In Educational Leadership', 'student_count': 35}, {'long_name': 'Bachelor Of Business Administration (Executive Office Administration)', 'student_count': 34}, {'long_name': 'Diploma In Business Administration (Management) - W', 'student_count': 34}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MARKETING)', 'student_count': 34}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS) BY DISTANCE', 'student_count': 34}, {'long_name': 'MASTER OF PHILOSOPHY (HISTORY EDUCATION)', 'student_count': 34}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH LINGUISTICS EDUCATION)', 'student_count': 33}, {'long_name': 'DOCTOR OF PHILOSOPHY (SPECIAL EDUCATION)', 'student_count': 33}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGMENT & ORGANIZATION BEHAVIOUR)', 'student_count': 33}, {'long_name': 'MASTER OF EDUCATION IN SCHOOL SUPERVISION AND QUALITY ASSURANCE)', 'student_count': 33}, {'long_name': 'MASTER OF ARTS (COMMUNICATION INSTRUCTION)', 'student_count': 32}, {'long_name': 'Master Of Philosophy In Business Management - W', 'student_count': 32}, {'long_name': 'Master Of Philosophy In Crop Science', 'student_count': 32}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education) - S', 'student_count': 32}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 31}, {'long_name': 'BACHELOR OF ARTS (NZEMA EDUCATION)', 'student_count': 31}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RESOURCE MANAGEMENT)', 'student_count': 31}, {'long_name': 'MASTER OF PHILOSOPHY (JOURNALISM AND MEDIA ST)', 'student_count': 31}, {'long_name': 'Diploma In Education (Competency Based Training - CBT)', 'student_count': 30}, {'long_name': 'MASTER OF EDUCATION (FOOD AND NUTRITION)', 'student_count': 30}, {'long_name': 'Master Of Philosophy In Agronomy', 'student_count': 30}, {'long_name': 'BACHELOR OF ARTS (GONJA EDUCATION)', 'student_count': 29}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship) - W', 'student_count': 29}, {'long_name': 'MASTER OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'student_count': 29}, {'long_name': 'MASTER OF EDUCATION (ENGLISH LANGUAGE) BY DISTANCE', 'student_count': 29}, {'long_name': 'Master Of Education In Mathematics Education', 'student_count': 29}, {'long_name': 'MASTER OF PHILOSOPHY (ARTS AND CULTURE)', 'student_count': 29}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles (Top-Up)', 'student_count': 29}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 28}, {'long_name': 'Bachelor Of Arts (English Language Education) - M', 'student_count': 28}, {'long_name': 'Bachelor Of Science (Mathematics Education) - S', 'student_count': 28}, {'long_name': 'MASTER OF PHILOSOPHY (COMMUNICATION INSTRUCTION)', 'student_count': 28}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'student_count': 28}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FANTE EDUCATION)', 'student_count': 27}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH GERMAN EDUCATION)', 'student_count': 26}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 26}, {'long_name': 'Diploma In Education (Junior High)', 'student_count': 26}, {'long_name': 'DOCTOR OF PHILOSOPHY (FRENCH)', 'student_count': 26}, {'long_name': 'MASTER OF PHILOSOPHY (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 26}, {'long_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL SCIENCE EDUCATION)', 'student_count': 25}, {'long_name': 'DOCTOR OF PHILOSOPHY (MUSIC EDUCATION)', 'student_count': 25}, {'long_name': 'MASTER OF ARTS (DEVELOPMENT COMMUNICATION)', 'student_count': 25}, {'long_name': 'MASTER OF SCIENCE (ECONOMICS EDUCATION)', 'student_count': 25}, {'long_name': 'Master Of Science In Information Technology Education - W', 'student_count': 25}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology Education)', 'student_count': 25}, {'long_name': 'BACHELOR OF SCIENCE (INSURANCE AND RISK MANAGEMENT)', 'student_count': 24}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - E', 'student_count': 24}, {'long_name': 'Bachelor Of Business Administration (Management) - R', 'student_count': 23}, {'long_name': 'MASTER OF EDUCATION (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'student_count': 23}, {'long_name': 'Master Of Education In Science Education', 'student_count': 23}, {'long_name': 'Master Of Philosophy In Construction Technology (Top-Up)', 'student_count': 23}, {'long_name': 'Master Of Technology In Mechanical Technology', 'student_count': 23}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH LINGUISTICS EDUCATION)', 'student_count': 22}, {'long_name': 'Bachelor Of Science (Civil Engineering)', 'student_count': 22}, {'long_name': 'Diploma In Automotive Engineering Technology', 'student_count': 22}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MANAGEMENT INFORMATION SYSTEMS)', 'student_count': 22}, {'long_name': 'MASTER OF PHILOSOPHY (ENVIRONMENTAL SCIENCE)', 'student_count': 22}, {'long_name': 'MASTER OF PHILOSOPHY (FINANCE)', 'student_count': 22}, {'long_name': 'Master Of Philosophy In Automotive Engineering Technology', 'student_count': 22}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 21}, {'long_name': 'BACHELOR OF ARTS (SISAALI EDUCATION)', 'student_count': 21}, {'long_name': 'MASTER OF EDUCATION (COMPUTER EDUCATION AND TECHNOLOGY)', 'student_count': 21}, {'long_name': 'MASTER OF PHILOSOPHY (FAMILY LIFE MANAGEMENT)', 'student_count': 21}, {'long_name': 'MASTER OF PHILOSOPHY (THEATRE ARTS)', 'student_count': 21}, {'long_name': 'Master Of Philosophy In Public Health', 'student_count': 21}, {'long_name': 'Post Diploma Bachelor Of Science (Management Education)', 'student_count': 21}, {'long_name': 'BACHELOR OF ARTS (KASEM EDUCATION)', 'student_count': 20}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship)', 'student_count': 20}, {'long_name': 'MASTER OF ARTS (ART EDUCATION)', 'student_count': 20}, {'long_name': 'MASTER OF EDUCATION (SCIENCE EDUCATION)', 'student_count': 20}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up)', 'student_count': 20}, {'long_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education) - S', 'student_count': 20}, {'long_name': 'BACHELOR OF ARTS (KUSAAL EDUCATION)', 'student_count': 19}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH LINGUISTICS EDUCATION)', 'student_count': 19}, {'long_name': 'Master Of Philosophy In Educational Leadership (Top-Up)', 'student_count': 19}, {'long_name': 'MASTER OF SCIENCE (DEVELOPMENT FINANCE)', 'student_count': 19}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - W', 'student_count': 19}, {'long_name': 'Bachelor Of Arts (Arabic With English Education)', 'student_count': 18}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH LINGUISTICS EDUCATION)', 'student_count': 18}, {'long_name': 'Bachelor Of Science (Mechanical Engineering Technology Education)', 'student_count': 18}, {'long_name': 'MASTER OF EDUCATION (POLITICAL SCIENCE EDUCATION)', 'student_count': 18}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICS EDUCATION)', 'student_count': 18}, {'long_name': 'Master Of Philosophy In Chemistry Education', 'student_count': 18}, {'long_name': 'Master Of Technology In Automotive Engineering Technology', 'student_count': 18}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'student_count': 18}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH LINGUISTICS EDUCATION)', 'student_count': 17}, {'long_name': 'BACHELOR OF ARTS (GURUNE EDUCATION)', 'student_count': 17}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH LINGUISTICS EDUCATION)', 'student_count': 17}, {'long_name': 'Doctor Of Philosophy In Construction Management', 'student_count': 17}, {'long_name': 'Master Of Philosophy In Mechanical Engineering Technology (Top-Up)', 'student_count': 17}, {'long_name': 'Master Of Philosophy In Accounting - W', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering (Top-Up)', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Information Technology', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Mathematics Education (Top-Up) - W', 'student_count': 16}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - W', 'student_count': 16}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'student_count': 16}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH GERMAN EDUCATION)', 'student_count': 15}, {'long_name': 'BACHELOR OF ARTS (TWI WITH FRENCH EDUCATION)', 'student_count': 15}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - W', 'student_count': 15}, {'long_name': 'DIPLOMA (EDUCATION)', 'student_count': 15}, {'long_name': 'Diploma In Architecture And Digital Construction', 'student_count': 15}, {'long_name': 'Diploma In Mechanical Engineering Technology', 'student_count': 15}, {'long_name': 'MASTER OF PHILOSOPHY (ENTREPRENEURSHIP AND INNOVATION)', 'student_count': 15}, {'long_name': 'Master Of Philosophy In Animal Science', 'student_count': 15}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education) - S', 'student_count': 15}, {'long_name': 'Bachelor Of Science (Plumbing, And Gas Technology)', 'student_count': 14}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - E', 'student_count': 14}, {'long_name': 'Doctor Of Philosophy In Construction Technology', 'student_count': 14}, {'long_name': 'MASTER OF EDUCATION (BIOLOGY)', 'student_count': 14}, {'long_name': 'MASTER OF PHILOSOPHY (INSTRUCTIONAL DESIGN AND TECHNOLOGY)', 'student_count': 14}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology Education)', 'student_count': 14}, {'long_name': 'Bachelor Of Science (Agriculture Engineering, Technology And Innovation Education)', 'student_count': 13}, {'long_name': 'MASTER OF ARTS (HISTORY EDUCATION)', 'student_count': 13}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up)', 'student_count': 13}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering', 'student_count': 13}, {'long_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'student_count': 13}, {'long_name': 'Bachelor Of Science (Automotive Engineering Technology Education)', 'student_count': 12}, {'long_name': 'Bachelor Of Science (Computerised Accounting)', 'student_count': 12}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education)', 'student_count': 12}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology)', 'student_count': 12}, {'long_name': 'Doctor Of Philosophy In Wood Science And Technology', 'student_count': 11}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT FINANCE)', 'student_count': 11}, {'long_name': 'Master Of Technology In Wood Technology', 'student_count': 11}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting)', 'student_count': 11}, {'long_name': 'BACHELOR OF ARTS (GA WITH GERMAN EDUCATION)', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL EDUCATION)', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS EDUCATION)', 'student_count': 10}, {'long_name': 'Bachelor Of Science (Marketing) - E', 'student_count': 10}, {'long_name': 'Master Of Philosophy In Information Technology - W', 'student_count': 10}, {'long_name': 'Master Of Philosophy In Soil Science', 'student_count': 10}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education)', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH FRENCH EDUCATION)', 'student_count': 9}, {'long_name': 'BACHELOR OF ARTS (GA WITH FRENCH EDUCATION)', 'student_count': 9}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH LINGUISTICS EDUCATION)', 'student_count': 9}, {'long_name': 'Doctor Of Philosophy In Mathematics Education', 'student_count': 9}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up) - W', 'student_count': 9}, {'long_name': 'Master Of Philosophy In Information Technology - W (Top-Up)', 'student_count': 9}, {'long_name': 'Post Diploma Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'student_count': 9}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology Education) - S', 'student_count': 9}, {'long_name': 'Bachelor Of Science (Marketing) - R', 'student_count': 8}, {'long_name': 'Diploma In Business Administration (Accounting) - W', 'student_count': 8}, {'long_name': 'Master Of Philosophy In Biology Education', 'student_count': 8}, {'long_name': 'Master Of Philosophy In Teaching And Learning', 'student_count': 8}, {'long_name': 'Master Of Philosophy In Wood Science And Technology (Top-Up)', 'student_count': 8}, {'long_name': 'MASTER OF SCIENCE (BIOLOGY)', 'student_count': 8}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND DAGAARE EDUCATION)', 'student_count': 7}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH FRENCH EDUCATION)', 'student_count': 7}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH FRENCH EDUCATION)', 'student_count': 7}, {'long_name': 'Bachelor Of Science In Administration (Business Information Systems)', 'student_count': 7}, {'long_name': 'Master Of Philosophy In Crop Science (Top-Up)', 'student_count': 7}, {'long_name': 'Post Diploama Bachelor Of Science (Computerised Accounting)', 'student_count': 7}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - E', 'student_count': 7}, {'long_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education)', 'student_count': 7}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH FRENCH EDUCATION)', 'student_count': 6}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH FRENCH EDUCATION)', 'student_count': 6}, {'long_name': 'Master Of Philosophy In Automotive Engineering Technology (Top-Up)', 'student_count': 6}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - E', 'student_count': 6}, {'long_name': 'BACHELOR OF ART (SISAALI EDUCATION WITH FRENCH EDUCATION)', 'student_count': 5}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND DANGME EDUCATION)', 'student_count': 5}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH FRENCH EDUCATION)', 'student_count': 5}, {'long_name': 'Doctor Of Philosophy In Crop Science', 'student_count': 5}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Agricultural Science Education)', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology Education) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing And Entrepreneurship) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education) -S', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology Education)', 'student_count': 5}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH GERMAN EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH GERMAN EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GA EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL NEEDS EDUCATION)', 'student_count': 4}, {'long_name': 'Diploma In Management Education', 'student_count': 4}, {'long_name': 'MASTER OF EDUCATION (ENGLISH EDUCATION)', 'student_count': 4}, {'long_name': 'MASTER OF PHILOSOPHY (HOME ECONOMICS)', 'student_count': 4}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - W', 'student_count': 4}, {'long_name': 'BACHELOR OF ART (LINGUISTIC AND EWE EDUCATION)', 'student_count': 3}, {'long_name': 'Bachelor Of Arts (English With French Education)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH FRENCH EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'DOCTOR OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'student_count': 3}, {'long_name': 'MASTER OF ARTS (FRENCH TRANSLATION)', 'student_count': 3}, {'long_name': 'MASTER OF FINE ARTS (THEATRE ARTS)', 'student_count': 3}, {'long_name': 'Master Of Philosophy In Agronomy (Top-up)', 'student_count': 3}, {'long_name': 'Master Of Philosophy In Plant Pathology', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - R', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S (Top-Up)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W (Top-Up)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) -- R', 'student_count': 3}, {'long_name': 'B.SC. DOC', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH GERMAN EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH FRENCH EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH GERMAN EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH FRENCH EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GONJA EDUCATION)', 'student_count': 2}, {'long_name': 'Bachelor Of Science (Welding And Fabrication Technology Education', 'student_count': 2}, {'long_name': 'Doctor Of Philosophy In Animal Science', 'student_count': 2}, {'long_name': 'Doctor Of Philosophy In Soil Fertility And Plant Nutrition', 'student_count': 2}, {'long_name': 'MASTER OF ARTS (ARTS AND CULTURE)', 'student_count': 2}, {'long_name': 'MASTER OF ARTS (THEATRE ARTS)', 'student_count': 2}, {'long_name': 'MASTER OF PHILOSOPHY COMMUNICATION AND MEDIA STUDIES   (COMMUNICATION SKILLS)', 'student_count': 2}, {'long_name': 'MASTER OF PHILOSOPHY IN AGRICULTURAL EXTENSION AND RURAL ENTREPRENEURSHIP', 'student_count': 2}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Executive Office Administration)', 'student_count': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GERMAN EDUCATION)', 'student_count': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND KASEM EDUCATION)', 'student_count': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND NZEMA EDUCATION)', 'student_count': 1}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - E', 'student_count': 1}, {'long_name': 'DIPLOMA (SIGN LANGUAGE)', 'student_count': 1}, {'long_name': 'Diploma In Welding And Fabrication Engineering Technology', 'student_count': 1}, {'long_name': 'DOCTOR OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'student_count': 1}, {'long_name': 'MASTER OF EDUCATION (ASSESSMENT IN SPECIAL EDUCATION)', 'student_count': 1}, {'long_name': 'MASTER OF PHILOSOPHY (ASSESSMENT IN SPECIAL EDUCATION)', 'student_count': 1}, {'long_name': 'Master Of Philosphy In Water And Environmental Management', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Education (Early Grade) - K', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education)', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education) - S', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Banking And Finance) - W', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Business Information Systems)', 'student_count': 1}]
2025-08-08 06:48:43,162 - root - INFO - [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]
2025-08-08 06:48:43,162 - root - INFO - [{'description': 'UEW UNDERGRADUATE APPLICATION FORM 2022/2023', 'is_program_configured': 'Yes', 'show_education': 'Yes', 'show_employment': 'No', 'show_results': 'No', 'student_count': 26130}, {'description': 'UEW Postgraduate forms', 'is_program_configured': 'Yes', 'show_education': 'Yes', 'show_employment': 'Yes', 'show_results': 'Yes', 'student_count': 1}]
2025-08-08 06:48:43,162 - root - INFO - 'No results'
2025-08-08 06:48:43,162 - root - INFO - 'No results'
2025-08-08 06:48:43,162 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 5
2025-08-08 06:48:43,162 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 2
2025-08-08 06:48:43,162 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 06:48:51,445 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:51,450 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 06:48:59,805 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:48:59,811 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:48:59,811 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 06:48:59,811 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:48:59,811 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/7
2025-08-08 06:48:59,811 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:48:59,811 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:48:59,811 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:48:59,811 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 06:48:59,811 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:48:59,811 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:48:59,811 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:48:59,811 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:48:59,812 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total number of students enrolled at the institution with the most students?...
2025-08-08 06:48:59,812 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total number of students enrolled at the institution with the most students is 192,627....
2025-08-08 06:48:59,812 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:48:59,812 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'total_students': 192627}]...
2025-08-08 06:48:59,812 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_students_at_largest_institution
2025-08-08 06:48:59,812 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 06:48:59,812 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 06:48:59,812 - celery.redirected - WARNING - [{'total_students': 192627}]
2025-08-08 06:48:59,812 - celery.redirected - WARNING - ================================= 
2025-08-08 06:48:59,812 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 06:48:59,812 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:48:59,812 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/7
2025-08-08 06:48:59,812 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:48:59,812 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:48:59,812 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:48:59,812 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 06:48:59,812 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:48:59,812 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:48:59,813 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:48:59,813 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:48:59,813 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the student population of the institution with the most students compare to other instituti...
2025-08-08 06:48:59,813 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the most students is ITC University, which has a total student population of 19...
2025-08-08 06:48:59,813 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: graph
2025-08-08 06:48:59,813 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_name': 'ITC University', 'student_count': 49153, 'comparison': 'Other'}, {'institutio...
2025-08-08 06:48:59,813 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_population_comparison
2025-08-08 06:48:59,813 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 06:48:59,813 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 06:48:59,813 - celery.redirected - WARNING - [{'institution_name': 'ITC University', 'student_count': 49153, 'comparison': 'Other'}, {'institution_name': 'Akrofi-Christaller Institute of Theology Mission and Culture', 'student_count': 114, 'comparison': 'Other'}, {'institution_name': 'Accra Medical', 'student_count': 13012, 'comparison': 'Other'}, {'institution_name': 'Ghana School of Law', 'student_count': 262, 'comparison': 'Other'}, {'institution_name': 'Koforidua Technical University', 'student_count': 17758, 'comparison': 'Other'}, {'institution_name': 'Presbyterian University College Ghana', 'student_count': 32094, 'comparison': 'Other'}, {'institution_name': 'Methodist University College Ghana', 'student_count': 1, 'comparison': 'Other'}, {'institution_name': 'Wisconsin International University College', 'student_count': 1202, 'comparison': 'Other'}, {'institution_name': 'ITC University', 'student_count': 192627, 'comparison': 'Most Students'}, {'institution_name': 'Kofi Annan International Peacekeeping Training Centre', 'student_count': 52, 'comparison': 'Other'}, {'institution_name': 'Klintaps University College of Health and Allied Sciences', 'student_count': 1135, 'comparison': 'Other'}]
2025-08-08 06:48:59,813 - celery.redirected - WARNING - ================================= 
2025-08-08 06:48:59,813 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_population_comparison
2025-08-08 06:48:59,813 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 06:48:59,813 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:48:59,813 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/7
2025-08-08 06:48:59,814 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:48:59,814 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:48:59,814 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:48:59,814 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 06:48:59,814 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:48:59,814 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:48:59,814 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:48:59,814 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:48:59,814 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are most popular at the institution with the most students?...
2025-08-08 06:48:59,814 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The most popular programs at the institution with the highest student enrollment are as follows: 1) ...
2025-08-08 06:48:59,814 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-08 06:48:59,815 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'student_count': 23927}, {'long_name': ...
2025-08-08 06:48:59,815 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: popular_courses_by_student_count
2025-08-08 06:48:59,815 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 06:48:59,815 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 06:48:59,815 - celery.redirected - WARNING - [{'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'student_count': 23927}, {'long_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'student_count': 10732}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY)', 'student_count': 9618}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'student_count': 8905}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'student_count': 7662}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE)', 'student_count': 7606}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'student_count': 5826}, {'long_name': 'Bachelor Of Science (Mathematics Education)', 'student_count': 5492}, {'long_name': 'BACHELOR OF ARTS (POLITICAL SCIENCE EDUCATION)', 'student_count': 5305}, {'long_name': 'Bachelor Of Science (Information Technology Education)', 'student_count': 4481}, {'long_name': 'BACHELOR OF ART (SOCIAL STUDIES EDUCATION)', 'student_count': 4255}, {'long_name': 'BACHELOR OF ARTS (ENGLISH LANGUAGE EDUCATION)', 'student_count': 3826}, {'long_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'student_count': 3519}, {'long_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'student_count': 3348}, {'long_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'student_count': 3330}, {'long_name': 'BACHELOR OF ARTS (SOCIAL STUDIES EDUCATION)', 'student_count': 2989}, {'long_name': 'BACHELOR OF ARTS (GEOGRAPHY EDUCATION)', 'student_count': 2947}, {'long_name': 'Diploma In Education', 'student_count': 2821}, {'long_name': 'BACHELOR OF SCIENCE (MANAGEMENT EDUCATION)', 'student_count': 2750}, {'long_name': 'Bachelor Of Science (Accounting Education)', 'student_count': 2577}, {'long_name': 'BACHELOR OF ARTS (ART EDUCATION)', 'student_count': 2503}, {'long_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'student_count': 2381}, {'long_name': 'DIPLOMA IN EARLY GRADE', 'student_count': 1994}, {'long_name': 'Bachelor Of Science (Agricultural Science Education)', 'student_count': 1988}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED SCIENCE EDUCATION)', 'student_count': 1974}, {'long_name': 'BACHELOR OF ARTS (ECONOMICS EDUCATION)', 'student_count': 1921}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICAL EDUCATION)', 'student_count': 1678}, {'long_name': 'DIPLOMA IN BASIC EDUCATION', 'student_count': 1580}, {'long_name': 'BACHELOR OF ARTS (FRENCH EDUCATION)', 'student_count': 1574}, {'long_name': 'BACHELOR OF ARTS (GRAPHIC DESIGN)', 'student_count': 1558}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - R', 'student_count': 1508}, {'long_name': 'BACHELOR OF ARTS (EWE EDUCATION)', 'student_count': 1397}, {'long_name': 'BACHELOR OF SCIENCE (FOOD AND NUTRITION EDUCATION)', 'student_count': 1359}, {'long_name': 'BACHELOR OF ARTS (ENGLISH EDUCATION)', 'student_count': 1358}, {'long_name': 'Bachelor Of Science (Biological Sciences Education)', 'student_count': 1193}, {'long_name': 'Bachelor Of Science (Environmental Health And Sanitation Education)', 'student_count': 1175}, {'long_name': 'BACHELOR OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'student_count': 1173}, {'long_name': 'Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'student_count': 1171}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - R', 'student_count': 1160}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - S', 'student_count': 1137}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'student_count': 1089}, {'long_name': 'BACHELOR OF ARTS (TWI EDUCATION)', 'student_count': 1051}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION AND MANAGEMENT) BY DISTANCE', 'student_count': 1029}, {'long_name': 'BACHELOR OF ARTS (HISTORY EDUCATION)', 'student_count': 989}, {'long_name': 'BACHELOR OF SCIENCE (HEALTH ADMINISTRATION EDUCATION)', 'student_count': 942}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL EDUCATION)', 'student_count': 929}, {'long_name': 'Bachelor Of Arts (Social Studies With Economics Education)', 'student_count': 880}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED HOME ECONOMICS EDUCATION)', 'student_count': 870}, {'long_name': 'BACHELOR OF EDUCATION (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'student_count': 858}, {'long_name': 'Bachelor Of Education (Early Grade) - M', 'student_count': 816}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - R', 'student_count': 811}, {'long_name': 'BACHELOR OF ARTS (MUSIC EDUCATION)', 'student_count': 751}, {'long_name': 'Bachelor Of Education (Junior High)', 'student_count': 707}, {'long_name': 'BACHELOR OF SCIENCE (BIOLOGY EDUCATION)', 'student_count': 704}, {'long_name': 'DIPLOMA (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'student_count': 690}, {'long_name': 'BACHELOR OF ARTS (FASHION DESIGN AND TEXTILE EDUCATION)', 'student_count': 684}, {'long_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'student_count': 663}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 649}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - S', 'student_count': 627}, {'long_name': 'Master Of Arts In Educational Leadership', 'student_count': 542}, {'long_name': 'MASTER OF PHILOSOPHY (GHANAIAN LANGUAGE STUDIES)', 'student_count': 517}, {'long_name': 'Diploma In Education - KS', 'student_count': 509}, {'long_name': 'BACHELOR OF SCIENCE (HOME ECONOMICS EDUCATION)', 'student_count': 501}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'student_count': 481}, {'long_name': 'BACHELOR OF ARTS (TWI WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 466}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'student_count': 444}, {'long_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'student_count': 444}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'student_count': 439}, {'long_name': 'MASTER OF PHILOSOPHY (ENGLISH LANGUAGE)', 'student_count': 400}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICS EDUCATION)', 'student_count': 393}, {'long_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'student_count': 392}, {'long_name': 'MASTER OF PHILOSOPHY (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'student_count': 379}, {'long_name': 'BACHELOR  OF ARTS (EWE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 366}, {'long_name': 'MASTER OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'student_count': 364}, {'long_name': 'Bachelor Of Arts (Economics With Social Studies Education)', 'student_count': 351}, {'long_name': 'BACHELOR OF MUSIC', 'student_count': 351}, {'long_name': 'Bachelor Of Business Administration (Management) - E', 'student_count': 348}, {'long_name': 'MASTER OF PHILOSOPHY (SCIENCE EDUCATION)', 'student_count': 342}, {'long_name': 'MASTER OF PHILOSOPHY (EARLY CHILDHOOD EDUCATION)', 'student_count': 340}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND ENGLISH EDUCATION)', 'student_count': 339}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 325}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY) BY DISTANCE', 'student_count': 323}, {'long_name': 'BACHELOR OF ARTS (FANTE EDUCATION)', 'student_count': 316}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - S', 'student_count': 315}, {'long_name': 'Bachelor Of Science (Information Technology Education) - W', 'student_count': 310}, {'long_name': 'Diploma In Business Administration (Management)', 'student_count': 309}, {'long_name': 'MASTER OF PHILOSOPHY (ART EDUCATION)', 'student_count': 308}, {'long_name': 'BACHELOR OF ARTS (COMMUNICATION STUDIES)', 'student_count': 306}, {'long_name': 'DIPLOMA (GRAPHIC DESIGN)', 'student_count': 298}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (MARKETING AND ENTREPRENEURSHIP)', 'student_count': 296}, {'long_name': 'DIPLOMA (ART)', 'student_count': 295}, {'long_name': 'Bachelor Of Science (Agribusiness Management And Entrepreneurship Education)', 'student_count': 291}, {'long_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'student_count': 283}, {'long_name': 'MASTER OF PHILOSOPHY IN SOCIAL STUDIES', 'student_count': 283}, {'long_name': 'BACHELOR OF ARTS (THEATRE ARTS)', 'student_count': 269}, {'long_name': 'MASTER OF PHILOSOPHY (SPECIAL EDUCATION)', 'student_count': 265}, {'long_name': 'MASTER OF PHILOSOPHY (BASIC EDUCATION)', 'student_count': 254}, {'long_name': 'DIPLOMA IN MANAGEMENT STUDIES', 'student_count': 251}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES)', 'student_count': 243}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'student_count': 232}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS WITH ECONOMICS EDUCATION)', 'student_count': 226}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - W', 'student_count': 224}, {'long_name': 'DIPLOMA IN MUSIC', 'student_count': 208}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education) - S', 'student_count': 207}, {'long_name': 'MASTER OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'student_count': 202}, {'long_name': 'Bachelor Of Science (Information Technology)', 'student_count': 193}, {'long_name': 'Bachelor Of Science (Natural Resources Management And Education)', 'student_count': 192}, {'long_name': 'Bachelor Of Science (Wood Technology Education) - S', 'student_count': 192}, {'long_name': 'MASTER OF PHILOSOPHY (FRENCH)', 'student_count': 189}, {'long_name': 'MASTER OF EDUCATION (EARLY CHILDHOOD EDUCATION)', 'student_count': 188}, {'long_name': 'DOCTOR OF PHILOSOPHY (SCIENCE EDUCATION)', 'student_count': 187}, {'long_name': 'BACHELOR OF ARTS (CREATIVE ARTS AND DESIGN EDUCATION)', 'student_count': 182}, {'long_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'student_count': 181}, {'long_name': 'MASTER OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'student_count': 178}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'student_count': 175}, {'long_name': 'Master Of Technology Education In Catering And Hospitality', 'student_count': 175}, {'long_name': 'Bachelor Of Science In Administration (Accounting)', 'student_count': 173}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND TWI EDUCATION)', 'student_count': 171}, {'long_name': 'MASTER OF PHILOSOPHY (INFORMATION AND COMMUNICATION TECHNOLOGY)', 'student_count': 169}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'student_count': 167}, {'long_name': 'MASTER OF PHILOSOPHY (POLITICAL SCIENCE EDUCATION)', 'student_count': 167}, {'long_name': 'Bachelor Of Education (Early Grade) - K', 'student_count': 166}, {'long_name': 'Bachelor Of Business Administration (Management) - W', 'student_count': 163}, {'long_name': 'Bachelor Of Arts (Arabic Education)', 'student_count': 158}, {'long_name': 'BACHELOR OF ARTS (STRATEGIC COMMUNICATION)', 'student_count': 156}, {'long_name': 'MASTER OF PHILOSOPHY (FOOD AND NUTRITION)', 'student_count': 155}, {'long_name': 'MASTER OF PHILOSOPHY (MUSIC EDUCATION)', 'student_count': 154}, {'long_name': 'BACHELOR OF ARTS (RELIGIONS AND MORAL STUDIES EDUCATION)', 'student_count': 153}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'student_count': 150}, {'long_name': 'Master Of Business Administration (Human Resource Management And Organisational Behaviour ) - Weekend', 'student_count': 149}, {'long_name': 'DIPLOMA (THEATRE ARTS)', 'student_count': 144}, {'long_name': 'DIPLOMA (BASIC EDUCATION)', 'student_count': 143}, {'long_name': 'Bachelor Of Business Administration (Secretarial Education)', 'student_count': 129}, {'long_name': 'Diploma In Education - TM', 'student_count': 128}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - R', 'student_count': 127}, {'long_name': 'MASTER OF EDUCATION (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'student_count': 124}, {'long_name': 'BACHELOR ARTS (GA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 123}, {'long_name': 'DIPLOMA (EARLY GRADE)', 'student_count': 121}, {'long_name': 'MASTER OF ARTS (ENGLISH LANGUAGE)', 'student_count': 119}, {'long_name': 'Master Of Business Administration (Accounting)', 'student_count': 119}, {'long_name': 'MASTER OF EDUCATION (SCIENCE)', 'student_count': 119}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'student_count': 118}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS)', 'student_count': 118}, {'long_name': 'MASTER OF EDUCATION (HOME ECONOMICS)', 'student_count': 115}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education)', 'student_count': 114}, {'long_name': 'DIPLOMA (TEXTILES AND FASHION)', 'student_count': 114}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - S', 'student_count': 114}, {'long_name': 'Bachelor Of Science (Mathematics Education) - M', 'student_count': 112}, {'long_name': 'Master Of Philosophy In Catering And Hospitality', 'student_count': 112}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (FINANCE)', 'student_count': 111}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'student_count': 110}, {'long_name': 'MASTER OF ARTS (STRATEGIC COMMUNICATION)', 'student_count': 108}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FRENCH EDUCATION)', 'student_count': 107}, {'long_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'student_count': 106}, {'long_name': 'BACHELOR OF SCIENCE (FAMILY LIFE MANAGEMENT EDUCATION)', 'student_count': 106}, {'long_name': 'DOCTOR OF PHILOSOPHY (ARTS AND CULTURE)', 'student_count': 105}, {'long_name': 'BACHELOR OF ARTS (DANGME EDUCATION)', 'student_count': 104}, {'long_name': 'BACHELOR OF ARTS (TWI WITH LINGUISTICS EDUCATION)', 'student_count': 104}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BUSINESS INFORMATION SYSTEMS)', 'student_count': 104}, {'long_name': 'Post Diploma Bachelor Of Science (Environmental Health And Sanitation Education)', 'student_count': 102}, {'long_name': 'CERTIFICATE (PRE-SCHOOL EDUCATION)', 'student_count': 100}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education) - S', 'student_count': 99}, {'long_name': 'MASTER OF PHILOSOPHY (CURRICULUM & PEDAGOGIC STUDIES)', 'student_count': 99}, {'long_name': 'DIPLOMA (FRENCH EDUCATION)', 'student_count': 98}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology Education) - S', 'student_count': 98}, {'long_name': 'Bachelor Of Science (Wood Technology Education)', 'student_count': 96}, {'long_name': 'MASTER OF PHILOSOPHY (CHEMISTRY EDUCATION)', 'student_count': 96}, {'long_name': 'DIPLOMA IN ACCOUNTING', 'student_count': 95}, {'long_name': 'DOCTOR OF PHILOSOPHY (APPLIED LINGUISTICS)', 'student_count': 95}, {'long_name': 'MASTER OF ARTS (GHANAIAN LANGUAGES EDUCATION)', 'student_count': 95}, {'long_name': 'Master Of Philosophy In Construction Management', 'student_count': 95}, {'long_name': 'BACHELOR OF SCIENCE (SPORTS COACHING)', 'student_count': 94}, {'long_name': 'BACHELOR OF ART (SISAALI WITH ENGLISH EDUCATION)', 'student_count': 93}, {'long_name': 'BACHELOR OF ARTS (DAGBANI EDUCATION)', 'student_count': 93}, {'long_name': 'BACHELOR OF ARTS (DAGAARE EDUCATION)', 'student_count': 90}, {'long_name': 'Master Of Philosophy In Construction Technology', 'student_count': 90}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (FINANCE)', 'student_count': 89}, {'long_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'student_count': 86}, {'long_name': 'MASTER OF PHILOSOPHY (ECONOMICS)', 'student_count': 86}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT COMMUNICATION)', 'student_count': 84}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH ENGLISH EDUCATION)', 'student_count': 82}, {'long_name': 'POST GRADUATE DIPLOMA IN TEACHING AND LEARNING IN HIGHER EDUCATION', 'student_count': 82}, {'long_name': 'EXECUTIVE MASTERS (CONFLICT HUMAN RIGHTS & PEACE STUDIES)', 'student_count': 81}, {'long_name': 'Bachelor Of Science (Automotive Technology Education) - S', 'student_count': 80}, {'long_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'student_count': 80}, {'long_name': 'Diploma In Electrical And Electronics Engineering Technology', 'student_count': 79}, {'long_name': 'Diploma In Environmental Health And Sanitation Education', 'student_count': 79}, {'long_name': 'MASTER OF EDUCATION (INSTITUTIONAL MENTORSHIP AND SUPERVISION)', 'student_count': 79}, {'long_name': 'Master Of Philosophy In Mathematics Education', 'student_count': 79}, {'long_name': 'MASTER OF PHILOSOPHY (VISUAL COMMUNICATION)', 'student_count': 77}, {'long_name': 'DOCTOR OF PHILOSOPHY (SOCIAL STUDIES)', 'student_count': 75}, {'long_name': 'MASTER OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'student_count': 75}, {'long_name': 'MASTER OF PHILOSOPHY (STRATEGIC COMMUNICATION)', 'student_count': 75}, {'long_name': 'BACHELOR OF ART (DANGME WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 74}, {'long_name': 'Diploma In Business Administration (Accounting)', 'student_count': 74}, {'long_name': 'Master Of Science In Information Technology Education', 'student_count': 74}, {'long_name': 'Master Of Technology In Construction Technology', 'student_count': 74}, {'long_name': 'Post Diploma Bachelor Of Science In Environmental Health And Sanitation Education - S', 'student_count': 74}, {'long_name': 'BACHELOR OF ARTS (EWE WITH LINGUISTICS EDUCATION)', 'student_count': 72}, {'long_name': 'BACHELOR OF ARTS (EWE WITH FRENCH EDUCATION)', 'student_count': 71}, {'long_name': 'BACHELOR OF FINE ART (ANIMATION)', 'student_count': 70}, {'long_name': 'Diploma In Catering And Hospitality', 'student_count': 68}, {'long_name': 'Master Of Philosophy In Educational Leadership', 'student_count': 67}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'student_count': 67}, {'long_name': 'DOCTOR OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'student_count': 66}, {'long_name': 'Master Of Philosophy In Environmental And Occupational Health Education', 'student_count': 66}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - R', 'student_count': 65}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'student_count': 65}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles', 'student_count': 65}, {'long_name': 'Bachelor Of Arts (French With English Education)', 'student_count': 64}, {'long_name': 'Bachelor Of Science (Mathematics Education) - W', 'student_count': 64}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH LINGUISTIC EDUCATION)', 'student_count': 63}, {'long_name': 'DOCTOR OF PHILOSOPHY (EDUCATIONAL LEADERSHIP)', 'student_count': 63}, {'long_name': 'Master Of Philosophy In Accounting', 'student_count': 62}, {'long_name': 'Diploma In Economics', 'student_count': 60}, {'long_name': 'BACHELOR OF ARTS (DAGAARE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 59}, {'long_name': 'DIPLOMA IN SPORTS COACHING', 'student_count': 59}, {'long_name': 'MASTER OF EDUCATION (GEOGRAPHY)', 'student_count': 59}, {'long_name': 'Master Of Philosophy In Mathematics Education - W', 'student_count': 59}, {'long_name': 'DOCTOR OF PHILOSOPHY (COMMUNICATION AND MEDIA)', 'student_count': 58}, {'long_name': 'Master Of Technology In Construction Management', 'student_count': 58}, {'long_name': 'MASTER OF PHILOSOPHY (ASSESSMENT, MEASUREMENT AND EVALUATION)', 'student_count': 57}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH LINGUISTICS EDUCATION)', 'student_count': 55}, {'long_name': 'BACHELOR OF EDUCATION (PSYCHOLOGY)', 'student_count': 55}, {'long_name': 'Bachelor Of Science (Wood Technology With Education)', 'student_count': 55}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH LINGUISTICS EDUCATION)', 'student_count': 54}, {'long_name': 'Master Of Philosophy In Business Management', 'student_count': 54}, {'long_name': 'Master Of Philosophy In Catering And Hospitality (Top-Up)', 'student_count': 54}, {'long_name': 'Diploma In Education - TK', 'student_count': 53}, {'long_name': 'DOCTOR OF PHILOSOPHY IN GHANAIAN LANGUAGES', 'student_count': 53}, {'long_name': 'MASTER OF PHILOSOPHY (ACCOUNTING)', 'student_count': 52}, {'long_name': 'Bachelor Of Science (Wood Technology With Education) - S', 'student_count': 51}, {'long_name': 'Diploma In Education - CP', 'student_count': 51}, {'long_name': 'Diploma In Education - M', 'student_count': 51}, {'long_name': 'MASTER OF ARTS (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES', 'student_count': 51}, {'long_name': 'MASTER OF EDUCATION (FRENCH)', 'student_count': 51}, {'long_name': 'MASTER OF EDUCATION (SPECIAL EDUCATION)', 'student_count': 49}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH FRENCH EDUCATION)', 'student_count': 48}, {'long_name': 'BACHELOR OF ARTS (DEVELOPMENT COMMUNICATION)', 'student_count': 48}, {'long_name': 'BACHELOR OF ARTS (EWE WITH GERMAN EDUCATION)', 'student_count': 48}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - E', 'student_count': 48}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (LOGISTICS AND TRANSPORT MANAGEMENT)', 'student_count': 48}, {'long_name': 'Diploma In Construction Technology', 'student_count': 48}, {'long_name': 'Diploma In Fashion Design And Textiles', 'student_count': 48}, {'long_name': 'Master Of Education In Agriculture', 'student_count': 48}, {'long_name': 'BACHELOR OF ARTS (TWI WITH GERMAN EDUCATION)', 'student_count': 47}, {'long_name': 'Bachelor Of Science In Occupational Health And Safety', 'student_count': 47}, {'long_name': 'MASTER OF EDUCATION (GUIDANCE AND COUNSELLING)', 'student_count': 47}, {'long_name': 'Master Of Philosophy In Construction Management (Top Up)', 'student_count': 47}, {'long_name': 'Master Of Philosophy In Mechanical Engineering Technology', 'student_count': 47}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 46}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 46}, {'long_name': 'BACHELOR OF ARTS (GA EDUCATION)', 'student_count': 45}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - W', 'student_count': 45}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - W', 'student_count': 45}, {'long_name': 'Master Of Philosophy In Science Education', 'student_count': 42}, {'long_name': 'Master Of Technology Education In Fashion Design And Textile', 'student_count': 42}, {'long_name': 'MASTER OF PHILOSOPHY (CLOTHING AND TEXTILES)', 'student_count': 41}, {'long_name': 'Master Of Philosophy In Wood Science And Technology', 'student_count': 41}, {'long_name': 'BACHELOR OF ART (KUSAAL WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 40}, {'long_name': 'MASTER OF PHILOSOPHY (INTEGRATED SCIENCE EDUCATION)', 'student_count': 40}, {'long_name': 'MASTER OF PHILOSOPHY (TEXTILES AND FASHION)', 'student_count': 40}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH GERMAN EDUCATION)', 'student_count': 39}, {'long_name': 'DOCTOR OF PHILOSOPHY (ENGLISH LANGUAGE)', 'student_count': 39}, {'long_name': 'BACHELOR OF ARTS (GA WITH LINGUISTICS EDUCATION)', 'student_count': 38}, {'long_name': 'Bachelor Of Science (Marketing) - W', 'student_count': 37}, {'long_name': 'Master Of Philosophy In Biology', 'student_count': 37}, {'long_name': 'Master Of Technology In Electrical And Electronics Engineering', 'student_count': 37}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH LINGUISTICS EDUCATION)', 'student_count': 36}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance)', 'student_count': 36}, {'long_name': 'DOCTOR OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'student_count': 36}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education)', 'student_count': 36}, {'long_name': 'Doctor Of Philosophy In Educational Leadership', 'student_count': 35}, {'long_name': 'Bachelor Of Business Administration (Executive Office Administration)', 'student_count': 34}, {'long_name': 'Diploma In Business Administration (Management) - W', 'student_count': 34}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MARKETING)', 'student_count': 34}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS) BY DISTANCE', 'student_count': 34}, {'long_name': 'MASTER OF PHILOSOPHY (HISTORY EDUCATION)', 'student_count': 34}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH LINGUISTICS EDUCATION)', 'student_count': 33}, {'long_name': 'DOCTOR OF PHILOSOPHY (SPECIAL EDUCATION)', 'student_count': 33}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGMENT & ORGANIZATION BEHAVIOUR)', 'student_count': 33}, {'long_name': 'MASTER OF EDUCATION IN SCHOOL SUPERVISION AND QUALITY ASSURANCE)', 'student_count': 33}, {'long_name': 'MASTER OF ARTS (COMMUNICATION INSTRUCTION)', 'student_count': 32}, {'long_name': 'Master Of Philosophy In Business Management - W', 'student_count': 32}, {'long_name': 'Master Of Philosophy In Crop Science', 'student_count': 32}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education) - S', 'student_count': 32}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 31}, {'long_name': 'BACHELOR OF ARTS (NZEMA EDUCATION)', 'student_count': 31}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RESOURCE MANAGEMENT)', 'student_count': 31}, {'long_name': 'MASTER OF PHILOSOPHY (JOURNALISM AND MEDIA ST)', 'student_count': 31}, {'long_name': 'Diploma In Education (Competency Based Training - CBT)', 'student_count': 30}, {'long_name': 'MASTER OF EDUCATION (FOOD AND NUTRITION)', 'student_count': 30}, {'long_name': 'Master Of Philosophy In Agronomy', 'student_count': 30}, {'long_name': 'BACHELOR OF ARTS (GONJA EDUCATION)', 'student_count': 29}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship) - W', 'student_count': 29}, {'long_name': 'MASTER OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'student_count': 29}, {'long_name': 'MASTER OF EDUCATION (ENGLISH LANGUAGE) BY DISTANCE', 'student_count': 29}, {'long_name': 'Master Of Education In Mathematics Education', 'student_count': 29}, {'long_name': 'MASTER OF PHILOSOPHY (ARTS AND CULTURE)', 'student_count': 29}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles (Top-Up)', 'student_count': 29}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 28}, {'long_name': 'Bachelor Of Arts (English Language Education) - M', 'student_count': 28}, {'long_name': 'Bachelor Of Science (Mathematics Education) - S', 'student_count': 28}, {'long_name': 'MASTER OF PHILOSOPHY (COMMUNICATION INSTRUCTION)', 'student_count': 28}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'student_count': 28}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FANTE EDUCATION)', 'student_count': 27}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH GERMAN EDUCATION)', 'student_count': 26}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 26}, {'long_name': 'Diploma In Education (Junior High)', 'student_count': 26}, {'long_name': 'DOCTOR OF PHILOSOPHY (FRENCH)', 'student_count': 26}, {'long_name': 'MASTER OF PHILOSOPHY (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'student_count': 26}, {'long_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL SCIENCE EDUCATION)', 'student_count': 25}, {'long_name': 'DOCTOR OF PHILOSOPHY (MUSIC EDUCATION)', 'student_count': 25}, {'long_name': 'MASTER OF ARTS (DEVELOPMENT COMMUNICATION)', 'student_count': 25}, {'long_name': 'MASTER OF SCIENCE (ECONOMICS EDUCATION)', 'student_count': 25}, {'long_name': 'Master Of Science In Information Technology Education - W', 'student_count': 25}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology Education)', 'student_count': 25}, {'long_name': 'BACHELOR OF SCIENCE (INSURANCE AND RISK MANAGEMENT)', 'student_count': 24}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - E', 'student_count': 24}, {'long_name': 'Bachelor Of Business Administration (Management) - R', 'student_count': 23}, {'long_name': 'MASTER OF EDUCATION (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'student_count': 23}, {'long_name': 'Master Of Education In Science Education', 'student_count': 23}, {'long_name': 'Master Of Philosophy In Construction Technology (Top-Up)', 'student_count': 23}, {'long_name': 'Master Of Technology In Mechanical Technology', 'student_count': 23}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH LINGUISTICS EDUCATION)', 'student_count': 22}, {'long_name': 'Bachelor Of Science (Civil Engineering)', 'student_count': 22}, {'long_name': 'Diploma In Automotive Engineering Technology', 'student_count': 22}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MANAGEMENT INFORMATION SYSTEMS)', 'student_count': 22}, {'long_name': 'MASTER OF PHILOSOPHY (ENVIRONMENTAL SCIENCE)', 'student_count': 22}, {'long_name': 'MASTER OF PHILOSOPHY (FINANCE)', 'student_count': 22}, {'long_name': 'Master Of Philosophy In Automotive Engineering Technology', 'student_count': 22}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH ENGLISH LANGUAGE EDUCATION)', 'student_count': 21}, {'long_name': 'BACHELOR OF ARTS (SISAALI EDUCATION)', 'student_count': 21}, {'long_name': 'MASTER OF EDUCATION (COMPUTER EDUCATION AND TECHNOLOGY)', 'student_count': 21}, {'long_name': 'MASTER OF PHILOSOPHY (FAMILY LIFE MANAGEMENT)', 'student_count': 21}, {'long_name': 'MASTER OF PHILOSOPHY (THEATRE ARTS)', 'student_count': 21}, {'long_name': 'Master Of Philosophy In Public Health', 'student_count': 21}, {'long_name': 'Post Diploma Bachelor Of Science (Management Education)', 'student_count': 21}, {'long_name': 'BACHELOR OF ARTS (KASEM EDUCATION)', 'student_count': 20}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship)', 'student_count': 20}, {'long_name': 'MASTER OF ARTS (ART EDUCATION)', 'student_count': 20}, {'long_name': 'MASTER OF EDUCATION (SCIENCE EDUCATION)', 'student_count': 20}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up)', 'student_count': 20}, {'long_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education) - S', 'student_count': 20}, {'long_name': 'BACHELOR OF ARTS (KUSAAL EDUCATION)', 'student_count': 19}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH LINGUISTICS EDUCATION)', 'student_count': 19}, {'long_name': 'Master Of Philosophy In Educational Leadership (Top-Up)', 'student_count': 19}, {'long_name': 'MASTER OF SCIENCE (DEVELOPMENT FINANCE)', 'student_count': 19}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - W', 'student_count': 19}, {'long_name': 'Bachelor Of Arts (Arabic With English Education)', 'student_count': 18}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH LINGUISTICS EDUCATION)', 'student_count': 18}, {'long_name': 'Bachelor Of Science (Mechanical Engineering Technology Education)', 'student_count': 18}, {'long_name': 'MASTER OF EDUCATION (POLITICAL SCIENCE EDUCATION)', 'student_count': 18}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICS EDUCATION)', 'student_count': 18}, {'long_name': 'Master Of Philosophy In Chemistry Education', 'student_count': 18}, {'long_name': 'Master Of Technology In Automotive Engineering Technology', 'student_count': 18}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'student_count': 18}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH LINGUISTICS EDUCATION)', 'student_count': 17}, {'long_name': 'BACHELOR OF ARTS (GURUNE EDUCATION)', 'student_count': 17}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH LINGUISTICS EDUCATION)', 'student_count': 17}, {'long_name': 'Doctor Of Philosophy In Construction Management', 'student_count': 17}, {'long_name': 'Master Of Philosophy In Mechanical Engineering Technology (Top-Up)', 'student_count': 17}, {'long_name': 'Master Of Philosophy In Accounting - W', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering (Top-Up)', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Information Technology', 'student_count': 16}, {'long_name': 'Master Of Philosophy In Mathematics Education (Top-Up) - W', 'student_count': 16}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - W', 'student_count': 16}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'student_count': 16}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH GERMAN EDUCATION)', 'student_count': 15}, {'long_name': 'BACHELOR OF ARTS (TWI WITH FRENCH EDUCATION)', 'student_count': 15}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - W', 'student_count': 15}, {'long_name': 'DIPLOMA (EDUCATION)', 'student_count': 15}, {'long_name': 'Diploma In Architecture And Digital Construction', 'student_count': 15}, {'long_name': 'Diploma In Mechanical Engineering Technology', 'student_count': 15}, {'long_name': 'MASTER OF PHILOSOPHY (ENTREPRENEURSHIP AND INNOVATION)', 'student_count': 15}, {'long_name': 'Master Of Philosophy In Animal Science', 'student_count': 15}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education) - S', 'student_count': 15}, {'long_name': 'Bachelor Of Science (Plumbing, And Gas Technology)', 'student_count': 14}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - E', 'student_count': 14}, {'long_name': 'Doctor Of Philosophy In Construction Technology', 'student_count': 14}, {'long_name': 'MASTER OF EDUCATION (BIOLOGY)', 'student_count': 14}, {'long_name': 'MASTER OF PHILOSOPHY (INSTRUCTIONAL DESIGN AND TECHNOLOGY)', 'student_count': 14}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology Education)', 'student_count': 14}, {'long_name': 'Bachelor Of Science (Agriculture Engineering, Technology And Innovation Education)', 'student_count': 13}, {'long_name': 'MASTER OF ARTS (HISTORY EDUCATION)', 'student_count': 13}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up)', 'student_count': 13}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering', 'student_count': 13}, {'long_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'student_count': 13}, {'long_name': 'Bachelor Of Science (Automotive Engineering Technology Education)', 'student_count': 12}, {'long_name': 'Bachelor Of Science (Computerised Accounting)', 'student_count': 12}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education)', 'student_count': 12}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology)', 'student_count': 12}, {'long_name': 'Doctor Of Philosophy In Wood Science And Technology', 'student_count': 11}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT FINANCE)', 'student_count': 11}, {'long_name': 'Master Of Technology In Wood Technology', 'student_count': 11}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting)', 'student_count': 11}, {'long_name': 'BACHELOR OF ARTS (GA WITH GERMAN EDUCATION)', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL EDUCATION)', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS EDUCATION)', 'student_count': 10}, {'long_name': 'Bachelor Of Science (Marketing) - E', 'student_count': 10}, {'long_name': 'Master Of Philosophy In Information Technology - W', 'student_count': 10}, {'long_name': 'Master Of Philosophy In Soil Science', 'student_count': 10}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education)', 'student_count': 10}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH FRENCH EDUCATION)', 'student_count': 9}, {'long_name': 'BACHELOR OF ARTS (GA WITH FRENCH EDUCATION)', 'student_count': 9}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH LINGUISTICS EDUCATION)', 'student_count': 9}, {'long_name': 'Doctor Of Philosophy In Mathematics Education', 'student_count': 9}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up) - W', 'student_count': 9}, {'long_name': 'Master Of Philosophy In Information Technology - W (Top-Up)', 'student_count': 9}, {'long_name': 'Post Diploma Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'student_count': 9}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology Education) - S', 'student_count': 9}, {'long_name': 'Bachelor Of Science (Marketing) - R', 'student_count': 8}, {'long_name': 'Diploma In Business Administration (Accounting) - W', 'student_count': 8}, {'long_name': 'Master Of Philosophy In Biology Education', 'student_count': 8}, {'long_name': 'Master Of Philosophy In Teaching And Learning', 'student_count': 8}, {'long_name': 'Master Of Philosophy In Wood Science And Technology (Top-Up)', 'student_count': 8}, {'long_name': 'MASTER OF SCIENCE (BIOLOGY)', 'student_count': 8}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND DAGAARE EDUCATION)', 'student_count': 7}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH FRENCH EDUCATION)', 'student_count': 7}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH FRENCH EDUCATION)', 'student_count': 7}, {'long_name': 'Bachelor Of Science In Administration (Business Information Systems)', 'student_count': 7}, {'long_name': 'Master Of Philosophy In Crop Science (Top-Up)', 'student_count': 7}, {'long_name': 'Post Diploama Bachelor Of Science (Computerised Accounting)', 'student_count': 7}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - E', 'student_count': 7}, {'long_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education)', 'student_count': 7}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH FRENCH EDUCATION)', 'student_count': 6}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH FRENCH EDUCATION)', 'student_count': 6}, {'long_name': 'Master Of Philosophy In Automotive Engineering Technology (Top-Up)', 'student_count': 6}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - E', 'student_count': 6}, {'long_name': 'BACHELOR OF ART (SISAALI EDUCATION WITH FRENCH EDUCATION)', 'student_count': 5}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND DANGME EDUCATION)', 'student_count': 5}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH FRENCH EDUCATION)', 'student_count': 5}, {'long_name': 'Doctor Of Philosophy In Crop Science', 'student_count': 5}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Agricultural Science Education)', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology Education) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing And Entrepreneurship) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education) -S', 'student_count': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology Education)', 'student_count': 5}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH GERMAN EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH GERMAN EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GA EDUCATION)', 'student_count': 4}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL NEEDS EDUCATION)', 'student_count': 4}, {'long_name': 'Diploma In Management Education', 'student_count': 4}, {'long_name': 'MASTER OF EDUCATION (ENGLISH EDUCATION)', 'student_count': 4}, {'long_name': 'MASTER OF PHILOSOPHY (HOME ECONOMICS)', 'student_count': 4}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - W', 'student_count': 4}, {'long_name': 'BACHELOR OF ART (LINGUISTIC AND EWE EDUCATION)', 'student_count': 3}, {'long_name': 'Bachelor Of Arts (English With French Education)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH FRENCH EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH GERMAN EDUCATION)', 'student_count': 3}, {'long_name': 'DOCTOR OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'student_count': 3}, {'long_name': 'MASTER OF ARTS (FRENCH TRANSLATION)', 'student_count': 3}, {'long_name': 'MASTER OF FINE ARTS (THEATRE ARTS)', 'student_count': 3}, {'long_name': 'Master Of Philosophy In Agronomy (Top-up)', 'student_count': 3}, {'long_name': 'Master Of Philosophy In Plant Pathology', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - R', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S (Top-Up)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W (Top-Up)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education)', 'student_count': 3}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) -- R', 'student_count': 3}, {'long_name': 'B.SC. DOC', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH GERMAN EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH FRENCH EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH GERMAN EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH FRENCH EDUCATION)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GONJA EDUCATION)', 'student_count': 2}, {'long_name': 'Bachelor Of Science (Welding And Fabrication Technology Education', 'student_count': 2}, {'long_name': 'Doctor Of Philosophy In Animal Science', 'student_count': 2}, {'long_name': 'Doctor Of Philosophy In Soil Fertility And Plant Nutrition', 'student_count': 2}, {'long_name': 'MASTER OF ARTS (ARTS AND CULTURE)', 'student_count': 2}, {'long_name': 'MASTER OF ARTS (THEATRE ARTS)', 'student_count': 2}, {'long_name': 'MASTER OF PHILOSOPHY COMMUNICATION AND MEDIA STUDIES   (COMMUNICATION SKILLS)', 'student_count': 2}, {'long_name': 'MASTER OF PHILOSOPHY IN AGRICULTURAL EXTENSION AND RURAL ENTREPRENEURSHIP', 'student_count': 2}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Executive Office Administration)', 'student_count': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education)', 'student_count': 2}, {'long_name': 'BACHELOR OF ARTS (GERMAN EDUCATION)', 'student_count': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND KASEM EDUCATION)', 'student_count': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND NZEMA EDUCATION)', 'student_count': 1}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - E', 'student_count': 1}, {'long_name': 'DIPLOMA (SIGN LANGUAGE)', 'student_count': 1}, {'long_name': 'Diploma In Welding And Fabrication Engineering Technology', 'student_count': 1}, {'long_name': 'DOCTOR OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'student_count': 1}, {'long_name': 'MASTER OF EDUCATION (ASSESSMENT IN SPECIAL EDUCATION)', 'student_count': 1}, {'long_name': 'MASTER OF PHILOSOPHY (ASSESSMENT IN SPECIAL EDUCATION)', 'student_count': 1}, {'long_name': 'Master Of Philosphy In Water And Environmental Management', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Education (Early Grade) - K', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education)', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education) - S', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Banking And Finance) - W', 'student_count': 1}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Business Information Systems)', 'student_count': 1}]
2025-08-08 06:48:59,819 - celery.redirected - WARNING - ================================= 
2025-08-08 06:48:59,819 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: popular_courses_by_student_count
2025-08-08 06:48:59,819 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 06:48:59,819 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:48:59,819 - REPORT_PIPELINE - INFO - 🔄 Processing interview 4/7
2025-08-08 06:48:59,820 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:48:59,820 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:48:59,820 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:48:59,820 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 06:48:59,820 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:48:59,820 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:48:59,820 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:48:59,820 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:48:59,820 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the demographic breakdown of students at the institution with the most students?...
2025-08-08 06:48:59,820 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the most students has a total of 164,627 students, broken down by gender as fol...
2025-08-08 06:48:59,820 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-08 06:48:59,820 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_cou...
2025-08-08 06:48:59,820 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_demographics_by_gender
2025-08-08 06:48:59,820 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 06:48:59,820 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 06:48:59,820 - celery.redirected - WARNING - [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]
2025-08-08 06:48:59,820 - celery.redirected - WARNING - ================================= 
2025-08-08 06:48:59,821 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_demographics_by_gender
2025-08-08 06:48:59,821 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 06:48:59,821 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:48:59,821 - REPORT_PIPELINE - INFO - 🔄 Processing interview 5/7
2025-08-08 06:48:59,821 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:48:59,821 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:48:59,821 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:48:59,821 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 06:48:59,821 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:48:59,821 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:48:59,822 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:48:59,822 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:48:59,822 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How has the student enrollment at the institution with the most students changed over the past few y...
2025-08-08 06:48:59,822 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no available results regarding the changes in student enrollment at the in...
2025-08-08 06:48:59,822 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:48:59,822 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 06:48:59,822 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_enrollment_trends_most_populous_institution
2025-08-08 06:48:59,823 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 06:48:59,823 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 06:48:59,823 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:48:59,824 - REPORT_PIPELINE - INFO - 🔄 Processing interview 6/7
2025-08-08 06:48:59,824 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:48:59,824 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:48:59,824 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:48:59,824 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 06:48:59,824 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:48:59,824 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:48:59,824 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:48:59,824 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:48:59,824 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any specific admission criteria that the institution with the most students has implemente...
2025-08-08 06:48:59,824 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the most students, which has a total of 26,130 enrolled, has implemented specif...
2025-08-08 06:48:59,824 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:48:59,824 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'description': 'UEW UNDERGRADUATE APPLICATION FORM 2022/2023', 'is_program_configured': 'Yes', 'sh...
2025-08-08 06:48:59,824 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: admission_criteria_for_largest_institution
2025-08-08 06:48:59,824 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 06:48:59,825 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 06:48:59,825 - celery.redirected - WARNING - [{'description': 'UEW UNDERGRADUATE APPLICATION FORM 2022/2023', 'is_program_configured': 'Yes', 'show_education': 'Yes', 'show_employment': 'No', 'show_results': 'No', 'student_count': 26130}, {'description': 'UEW Postgraduate forms', 'is_program_configured': 'Yes', 'show_education': 'Yes', 'show_employment': 'Yes', 'show_results': 'Yes', 'student_count': 1}]
2025-08-08 06:48:59,825 - celery.redirected - WARNING - ================================= 
2025-08-08 06:48:59,825 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 06:48:59,825 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:48:59,825 - REPORT_PIPELINE - INFO - 🔄 Processing interview 7/7
2025-08-08 06:48:59,825 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:48:59,825 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:48:59,825 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:48:59,825 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 06:48:59,825 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:48:59,826 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:48:59,826 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:48:59,826 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:48:59,826 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the retention rate of students at the institution with the most students?...
2025-08-08 06:48:59,826 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no results available for the retention rate of students at the institution...
2025-08-08 06:48:59,826 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:48:59,826 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 06:48:59,826 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_retention_rate_most_enrolled_institution
2025-08-08 06:48:59,826 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 06:48:59,826 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 06:48:59,826 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:48:59,826 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 7
2025-08-08 06:48:59,826 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:48:59,826 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 06:48:59,826 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:48:59,826 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 7 documents
2025-08-08 06:48:59,826 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 06:48:59,826 - UPSERT_DOCS - INFO -     Content: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 06:48:59,826 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:39.038464+00:00', 'data_returned': True}
2025-08-08 06:48:59,826 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 06:48:59,826 - UPSERT_DOCS - INFO -     Content: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 06:48:59,826 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:42.051783+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 06:48:59,826 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:41.604319+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -     Content: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:34.181369+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -     Content: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:43.159593+00:00', 'data_returned': False}
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -     Content: Question: Are there any specific admission criteria that the institution with the most students has ...
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:27.091568+00:00', 'data_returned': True}
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -     Content: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:25.051653+00:00', 'data_returned': False}
2025-08-08 06:48:59,827 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 5/7
2025-08-08 06:48:59,956 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.128s]
2025-08-08 06:49:01,569 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:49:02,705 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.697s]
2025-08-08 06:49:02,706 - UPSERT_DOCS - INFO - ✅ Successfully upserted 7 documents to Elasticsearch
2025-08-08 06:49:02,836 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 06:49:02,836 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 15
2025-08-08 06:49:02,964 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:49:02,964 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 6
2025-08-08 06:49:03,100 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.136s]
2025-08-08 06:49:03,100 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (2):
2025-08-08 06:49:03,100 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 06:49:03,100 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (7 docs)
2025-08-08 06:49:03,101 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 6
2025-08-08 06:49:03,101 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 06:49:03,101 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:49:03,102 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-08 06:49:03,102 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:49:03,102 - REPORT_PIPELINE - INFO - ✍️ Writing 6 sections using batch processing...
2025-08-08 06:49:03,574 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:03,581 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:49:03,582 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:49:03,582 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:49:03,582 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University student enrollment'
2025-08-08 06:49:03,582 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institution has the most students?'
2025-08-08 06:49:03,582 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institution has the most students?'
2025-08-08 06:49:03,711 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:49:03,711 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 15
2025-08-08 06:49:03,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:03,835 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:49:03,835 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:49:03,835 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:49:03,835 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University admission criteria'
2025-08-08 06:49:03,835 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institution has the most students?'
2025-08-08 06:49:03,835 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institution has the most students?'
2025-08-08 06:49:03,844 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 06:49:03,844 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 6
2025-08-08 06:49:03,914 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:03,931 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:49:03,932 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:49:03,932 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:49:03,932 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student gender distribution'
2025-08-08 06:49:03,932 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institution has the most students?'
2025-08-08 06:49:03,932 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institution has the most students?'
2025-08-08 06:49:03,956 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:03,963 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:49:03,963 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 15
2025-08-08 06:49:03,967 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:49:03,967 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:49:03,967 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:49:03,967 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'institution with most students significance enrollment trends'
2025-08-08 06:49:03,967 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institution has the most students?'
2025-08-08 06:49:03,967 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institution has the most students?'
2025-08-08 06:49:03,970 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:49:03,971 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 06:49:04,068 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-08 06:49:04,068 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 15
2025-08-08 06:49:04,092 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:49:04,093 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 6
2025-08-08 06:49:04,093 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:49:04,094 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 15
2025-08-08 06:49:04,104 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 06:49:04,104 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 06:49:04,198 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:04,201 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:49:04,201 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:49:04,201 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:49:04,201 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'largest university enrollment'
2025-08-08 06:49:04,201 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institution has the most students?'
2025-08-08 06:49:04,201 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institution has the most students?'
2025-08-08 06:49:04,202 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 06:49:04,202 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 6
2025-08-08 06:49:04,221 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:49:04,222 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:49:04,222 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 06:49:04,222 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 6
2025-08-08 06:49:04,334 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-08 06:49:04,334 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 06:49:04,334 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 06:49:04,335 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 15
2025-08-08 06:49:04,355 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-08 06:49:04,355 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 06:49:04,355 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-08 06:49:04,355 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 06:49:04,462 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 06:49:04,463 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:49:04,463 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 6
2025-08-08 06:49:04,463 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 06:49:04,484 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:49:04,484 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 06:49:04,499 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:49:04,590 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:49:04,590 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 06:49:04,639 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.138s]
2025-08-08 06:49:04,639 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 06:49:04,639 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 06:49:04,639 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:42.051783+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 06:49:04,639 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there any specific admission criteria that the institution with the most students has ...
2025-08-08 06:49:04,639 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:27.091568+00:00', 'data_returned': True}
2025-08-08 06:49:04,640 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 06:49:04,640 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:41.604319+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 06:49:04,640 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 06:49:04,640 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:39.038464+00:00', 'data_returned': True}
2025-08-08 06:49:04,640 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 06:49:04,640 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:34.181369+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 06:49:04,718 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 06:49:04,718 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 06:49:04,821 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:49:04,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:49:04,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:04,931 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:49:04,931 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 06:49:04,931 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:49:04,931 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University popular programs education'
2025-08-08 06:49:04,931 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institution has the most students?'
2025-08-08 06:49:04,931 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institution has the most students?'
2025-08-08 06:49:04,962 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.138s]
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Are there any specific admission criteria that the institution with the most students has ...
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:27.091568+00:00', 'data_returned': True}
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:42.051783+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:41.604319+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:34.181369+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 06:49:04,962 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:39.038464+00:00', 'data_returned': True}
2025-08-08 06:49:05,057 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:49:05,057 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 15
2025-08-08 06:49:05,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:49:05,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:49:05,182 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.258s]
2025-08-08 06:49:05,183 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 06:49:05,183 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 06:49:05,183 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:39.038464+00:00', 'data_returned': True}
2025-08-08 06:49:05,183 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 06:49:05,183 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:41.604319+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 06:49:05,184 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 06:49:05,184 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there any specific admission criteria that the institution with the most students has ...
2025-08-08 06:49:05,184 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 6
2025-08-08 06:49:05,184 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:27.091568+00:00', 'data_returned': True}
2025-08-08 06:49:05,185 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 06:49:05,185 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:34.181369+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 06:49:05,185 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 06:49:05,185 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:42.051783+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 06:49:05,216 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.137s]
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:34.181369+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:41.604319+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there any specific admission criteria that the institution with the most students has ...
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:27.091568+00:00', 'data_returned': True}
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:39.038464+00:00', 'data_returned': True}
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 06:49:05,217 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:42.051783+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 06:49:05,314 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 06:49:05,314 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 06:49:05,383 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.272s]
2025-08-08 06:49:05,383 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 06:49:05,383 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 06:49:05,384 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:39.038464+00:00', 'data_returned': True}
2025-08-08 06:49:05,384 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 06:49:05,384 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:42.051783+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 06:49:05,384 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there any specific admission criteria that the institution with the most students has ...
2025-08-08 06:49:05,384 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:27.091568+00:00', 'data_returned': True}
2025-08-08 06:49:05,384 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 06:49:05,384 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:34.181369+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 06:49:05,384 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 06:49:05,384 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:41.604319+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 06:49:05,444 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 06:49:05,445 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 06:49:05,782 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:49:05,925 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.139s]
2025-08-08 06:49:05,925 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 06:49:05,925 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 06:49:05,925 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:42.051783+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 06:49:05,925 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 06:49:05,925 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:41.604319+00:00', 'data_returned': True, 'data_tag': 'popular_courses_by_student_count'}
2025-08-08 06:49:05,925 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there any specific admission criteria that the institution with the most students has ...
2025-08-08 06:49:05,925 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:27.091568+00:00', 'data_returned': True}
2025-08-08 06:49:05,925 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 06:49:05,926 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:39.038464+00:00', 'data_returned': True}
2025-08-08 06:49:05,926 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 06:49:05,926 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:48:34.181369+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 06:49:08,110 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:08,229 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:09,623 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:09,771 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:09,958 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:11,866 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:11,891 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 668 characters
2025-08-08 06:49:11,891 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 603 characters
2025-08-08 06:49:11,891 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 873 characters
2025-08-08 06:49:11,892 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 767 characters
2025-08-08 06:49:11,892 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1420 characters
2025-08-08 06:49:11,892 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 997 characters
2025-08-08 06:49:13,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:49:13,681 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - Replaced tag student_population_comparison with data object
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - Replaced tag popular_courses_by_student_count with data object
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - Replaced tag student_demographics_by_gender with data object
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,682 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 06:49:13,683 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:49:13,683 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-08 06:49:13,683 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:49:13,683 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-08 06:49:13,683 - REPORT_PIPELINE - INFO - 📊 Final report sections: 17
2025-08-08 06:49:13,683 - REPORT_PIPELINE - INFO - 📈 Extended data items: 7
2025-08-08 06:49:13,683 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_063221.log
2025-08-08 06:49:13,690 - celery.app.trace - INFO - Task generate_report[067ccc1c-489c-4753-b7c3-f88793b235f4] succeeded in 108.82439858399994s: {'outline': '# Report on Student Enrollment Across Institutions

## Introduction  
This report investigates which institution has the most students, focusing on ITC University, which boasts a total enrollment of 192,627 students. Understanding student enrollment trends is crucial for analyzing the dynamics of higher education.

## 1. Institution with the Most Students  
- **1.1 ITC University**  
  - Total student enrollment: 192,627  
  - Comparison with other institutions:  
    - Presbyterian University College Ghana: 32,094 students  
    - Koforidua Technical University: 17,758 students  
    - Accra Medical: 13,012 students  
  - Significance of ITC University\'s enrollment in the context of higher education.

## 2. Popular Programs at ITC University  
- **2.1 Most Popular Programs**  
  - Bachelor of Education (Junior High School): 23,927 students  
  - Bachelor of Education (Basic Education): 10,732 students  
  - Bachelor of Education (Upper Primary): 9,618 students  
- Analysis of trends in program...', ...}

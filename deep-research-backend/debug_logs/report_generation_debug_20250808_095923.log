2025-08-08 09:59:23,371 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_095923.log
2025-08-08 09:59:23,371 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 09:59:23,371 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 3eff9145-d8ae-430f-b3ba-366fa3db2f48
2025-08-08 09:59:23,371 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 09:59:23,371 - REPORT_REQUEST - INFO - 📝 Original Question: 'How many girls are at ITC University?'
2025-08-08 09:59:23,371 - REPORT_REQUEST - INFO - 🆔 Task ID: 3eff9145-d8ae-430f-b3ba-366fa3db2f48
2025-08-08 09:59:23,371 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T09:59:23.371848
2025-08-08 09:59:23,504 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-08 09:59:23,504 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 55
2025-08-08 09:59:23,632 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 09:59:23,632 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 31
2025-08-08 09:59:23,761 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.129s]
2025-08-08 09:59:23,762 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (5):
2025-08-08 09:59:23,762 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (20 docs)
2025-08-08 09:59:23,762 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (15 docs)
2025-08-08 09:59:23,762 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (8 docs)
2025-08-08 09:59:23,762 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (7 docs)
2025-08-08 09:59:23,762 - ELASTICSEARCH_STATE - INFO -   - 'string' (5 docs)
2025-08-08 09:59:23,762 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 09:59:23,762 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 09:59:23,762 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 09:59:23,762 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How many girls are at ITC University?
2025-08-08 09:59:23,762 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-08 09:59:23,762 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 09:59:34,246 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:34,267 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 09:59:40,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:40,051 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-08 09:59:44,273 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:45,929 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:46,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:46,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:46,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:46,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:46,537 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:46,746 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:47,334 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:47,917 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:47,979 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-08 09:59:51,034 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:51,491 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:51,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:51,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:51,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:52,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:52,628 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:52,918 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:54,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:55,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:55,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:55,874 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:56,313 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:56,430 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:56,662 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:56,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:56,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:57,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:57,156 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:57,310 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:57,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:57,988 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:58,820 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 09:59:58,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:01,510 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:01,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:02,665 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:02,940 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:03,468 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:05,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:05,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:05,225 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total enrollment of students at ITC University?', 'sql': "SELECT COUNT(*) AS total_enrollment FROM students WHERE institution_id = (SELECT id FROM institutions WHERE name = 'ITC University');", 'correct': True, 'reasoning': "The SQL query correctly counts the number of students enrolled at 'ITC University' by first retrieving the institution's ID from the 'institutions' table where the name matches 'ITC University'. It then uses this ID to filter the 'students' table and count the total number of students associated with that institution. This directly answers the question about total enrollment.", 'feedback': 'The SQL query is well-structured and accurately answers the question. No improvements are necessary for the current query.'}
2025-08-08 10:00:05,226 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:05,226 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 10:00:05,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:05,978 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:05,982 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the number of girls at ITC University compare to the number of boys?', 'sql': "SELECT sex, COUNT(*) AS number_of_students FROM students WHERE institution_id = (SELECT id FROM institutions WHERE name = 'ITC University') GROUP BY sex;", 'correct': True, 'reasoning': "The SQL query correctly counts the number of students grouped by their sex (male or female) at ITC University. It filters the students based on the institution_id that corresponds to 'ITC University', ensuring that only relevant data is considered. The use of GROUP BY sex allows for a direct comparison between the number of girls and boys, which is exactly what the question asks for.", 'feedback': "The SQL query is well-structured and answers the question effectively. However, it could be improved by explicitly labeling the counts as 'number_of_girls' and 'number_of_boys' for clarity in the results."}
2025-08-08 10:00:05,982 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:05,982 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 10:00:06,461 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:07,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:08,260 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:08,273 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What percentage of the total student population at ITC University are girls?', 'sql': "SELECT (COUNT(CASE WHEN sex = 'F' THEN 1 END) * 100.0 / COUNT(*)) AS percentage_girls FROM core.students WHERE institution_id = (SELECT id FROM auth.institutions WHERE name = 'ITC University');", 'correct': True, 'reasoning': "The SQL query correctly calculates the percentage of female students at ITC University. It counts the number of students with 'F' in the 'sex' column and divides it by the total number of students at the institution, multiplying by 100 to get a percentage. The subquery correctly retrieves the institution ID for 'ITC University'.", 'feedback': 'The SQL query is well-structured and accurately answers the question. However, it could be improved by ensuring that the subquery for the institution ID is robust against cases where the institution name might not exist, potentially using a LEFT JOIN instead.'}
2025-08-08 10:00:08,273 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:08,273 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 10:00:08,314 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:09,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:10,376 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:10,393 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:10,998 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:11,013 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:11,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:11,377 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the trend in female enrollment at ITC University over the past few years?', 'sql': "SELECT academic_years.start_year, COUNT(students.id) AS female_enrollment\nFROM students\nJOIN admission_batches ON students.id = admission_batches.parent_id\nJOIN academic_years ON admission_batches.academic_year_id = academic_years.id\nWHERE students.sex = 'F' AND students.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY academic_years.start_year\nORDER BY academic_years.start_year;", 'correct': True, 'reasoning': "The SQL query correctly identifies the trend in female enrollment by counting the number of female students (where sex = 'F') enrolled at ITC University over the years. It joins the necessary tables: 'students', 'admission_batches', and 'academic_years' to correlate student data with their respective academic years. The use of GROUP BY on 'academic_years.start_year' allows for aggregation of counts per year, and the ORDER BY clause ensures the results are presented chronologically.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be beneficial to clarify the definition of 'trend' in the question. For instance, specifying whether the trend should be visualized or if specific percentage changes should be calculated could enhance the analysis."}
2025-08-08 10:00:11,377 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:11,377 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 10:00:11,597 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:11,653 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:11,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:12,289 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:12,856 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:13,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:14,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:14,010 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the retention rate of girls at ITC University compared to boys?', 'sql': "SELECT sex, COUNT(DISTINCT s.id) AS total_students, COUNT(DISTINCT sp.student_id) AS retained_students, (COUNT(DISTINCT sp.student_id) * 100.0 / COUNT(DISTINCT s.id)) AS retention_rate FROM core.students s JOIN core.student_programs sp ON s.id = sp.student_id JOIN auth.institutions i ON s.institution_id = i.id WHERE i.name = 'ITC University' AND sp.current = TRUE GROUP BY sex;", 'correct': True, 'reasoning': "The SQL query correctly identifies the retention rate of students based on their sex (girls vs boys) at ITC University. It counts the total number of students and the number of retained students (those who are currently enrolled) for each sex. The calculation of the retention rate is done by dividing the number of retained students by the total number of students and multiplying by 100 to get a percentage. The query also correctly filters for the institution name 'ITC University' and groups the results by sex, which aligns with the question's requirements.", 'feedback': "The SQL query is well-structured and answers the question accurately. However, it could be improved by explicitly labeling the output columns for clarity, such as renaming 'sex' to 'gender' or adding aliases to the retention rate for better readability in the results."}
2025-08-08 10:00:14,011 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:14,011 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 10:00:14,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:14,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:14,830 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:14,915 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:15,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:15,553 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:15,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:16,185 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:16,393 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:16,712 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:16,977 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:17,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:17,705 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:17,793 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:17,815 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at ITC University, which is a qualitative analysis rather than a quantitative one. The schema provides data about institutions, applicants, programs, and related entities, but it does not contain specific qualitative factors or insights that would explain enrollment trends. To answer this question, one would need access to external data sources, surveys, or analyses that provide insights into the reasons behind enrollment numbers, which are not present in the schema.', 'feedback': ''}
2025-08-08 10:00:17,815 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:17,815 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:17,816 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at ITC University, which is a qualitative analysis rather than a quantitative one. The schema provides data about institutions, applicants, programs, and related entities, but it does not contain specific qualitative factors or insights that would explain enrollment trends. To answer this question, one would need access to external data sources, surveys, or analyses that provide insights into the reasons behind enrollment numbers, which are not present in the schema.', 'feedback': ''}
2025-08-08 10:00:18,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:18,259 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps is ITC University taking to improve data collection and transparency regarding gender distribution among its student population?', 'answerable': False, 'reasoning': 'The question asks about specific steps taken by ITC University to improve data collection and transparency regarding gender distribution among its student population. However, the provided schema does not contain any tables or fields that explicitly detail the actions or initiatives of the university regarding data collection or transparency measures. The schema includes various tables related to students, programs, and institutions, but it lacks any direct reference to policies, initiatives, or steps taken by the institution in this context.', 'feedback': ''}
2025-08-08 10:00:18,260 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:18,260 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:18,260 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps is ITC University taking to improve data collection and transparency regarding gender distribution among its student population?', 'answerable': False, 'reasoning': 'The question asks about specific steps taken by ITC University to improve data collection and transparency regarding gender distribution among its student population. However, the provided schema does not contain any tables or fields that explicitly detail the actions or initiatives of the university regarding data collection or transparency measures. The schema includes various tables related to students, programs, and institutions, but it lacks any direct reference to policies, initiatives, or steps taken by the institution in this context.', 'feedback': ''}
2025-08-08 10:00:20,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:20,686 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:20,701 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the higher enrollment of boys compared to girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the higher enrollment of boys compared to girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender enrollment statistics or factors influencing enrollment decisions. While there are tables related to students and their demographics, there is no direct information or metrics that would allow for a comprehensive analysis of the reasons behind gender disparities in enrollment. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 10:00:20,701 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:20,701 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:20,701 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the higher enrollment of boys compared to girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the higher enrollment of boys compared to girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender enrollment statistics or factors influencing enrollment decisions. While there are tables related to students and their demographics, there is no direct information or metrics that would allow for a comprehensive analysis of the reasons behind gender disparities in enrollment. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 10:00:20,954 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:21,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:21,910 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:22,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:22,254 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:22,400 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:23,882 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:24,453 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:24,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:24,475 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers at ITC University?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at ITC University. The provided schema contains various tables related to institutions, students, programs, and admissions, but it does not include qualitative data or insights that would explain the reasons behind enrollment numbers. The schema lacks information on marketing strategies, student satisfaction, or external factors influencing enrollment, which are necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 10:00:24,475 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:24,475 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:24,475 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers at ITC University?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at ITC University. The provided schema contains various tables related to institutions, students, programs, and admissions, but it does not include qualitative data or insights that would explain the reasons behind enrollment numbers. The schema lacks information on marketing strategies, student satisfaction, or external factors influencing enrollment, which are necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 10:00:24,480 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe have contributed to this decline in female enrollment at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a decline in female enrollment at ITC University. However, the provided schema does not contain specific data related to enrollment trends, gender statistics, or any factors that could be analyzed to answer this question. The schema includes various tables related to institutions, students, programs, and admissions, but it lacks the necessary historical enrollment data or demographic analysis that would be required to identify and explain the reasons for a decline in female enrollment.', 'feedback': ''}
2025-08-08 10:00:24,481 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:24,481 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:24,481 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe have contributed to this decline in female enrollment at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a decline in female enrollment at ITC University. However, the provided schema does not contain specific data related to enrollment trends, gender statistics, or any factors that could be analyzed to answer this question. The schema includes various tables related to institutions, students, programs, and admissions, but it lacks the necessary historical enrollment data or demographic analysis that would be required to identify and explain the reasons for a decline in female enrollment.', 'feedback': ''}
2025-08-08 10:00:25,307 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:25,327 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps is ITC University taking to improve data collection and transparency regarding gender distribution among its student population?', 'answerable': False, 'reasoning': 'The question asks about specific steps taken by ITC University to improve data collection and transparency regarding gender distribution among its student population. However, the provided schema does not contain any tables or fields that explicitly detail the actions or initiatives of the university regarding data collection or transparency measures. The schema includes various tables related to students, their demographics, and academic records, but it lacks any information on institutional policies or steps taken to improve data collection practices. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 10:00:25,327 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:25,327 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:25,327 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps is ITC University taking to improve data collection and transparency regarding gender distribution among its student population?', 'answerable': False, 'reasoning': 'The question asks about specific steps taken by ITC University to improve data collection and transparency regarding gender distribution among its student population. However, the provided schema does not contain any tables or fields that explicitly detail the actions or initiatives of the university regarding data collection or transparency measures. The schema includes various tables related to students, their demographics, and academic records, but it lacks any information on institutional policies or steps taken to improve data collection practices. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 10:00:26,971 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:26,986 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the higher enrollment of boys compared to girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the higher enrollment of boys compared to girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender enrollment statistics or factors influencing enrollment decisions. While there are tables related to students and their demographics, there is no direct information or metrics that would allow for a comprehensive analysis of the reasons behind gender disparities in enrollment. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 10:00:26,986 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:26,987 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:26,987 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the higher enrollment of boys compared to girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the higher enrollment of boys compared to girls at ITC University. However, the provided schema does not contain any specific data or attributes related to gender enrollment statistics or factors influencing enrollment decisions. While there are tables related to students and their demographics, there is no direct information or metrics that would allow for a comprehensive analysis of the reasons behind gender disparities in enrollment. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 10:00:27,430 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:27,662 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:27,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:27,872 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the demographics of the girls enrolled at ITC University, such as age and nationality?', 'sql': "SELECT EXTRACT(YEAR FROM AGE(dob)) AS age, n.name AS nationality\nFROM core.students s\nJOIN core.nationalities n ON s.nationality_id = n.id\nJOIN auth.institutions i ON s.institution_id = i.id\nWHERE s.sex = 'F' AND i.name = 'ITC University';", 'correct': True, 'reasoning': "The SQL query correctly extracts the age of female students enrolled at ITC University by calculating the age from the date of birth (dob) and retrieves their nationality from the nationalities table. It uses appropriate JOINs to connect the students, nationalities, and institutions tables based on their relationships. The WHERE clause filters for female students and specifies the institution as ITC University, which aligns with the question's requirements.", 'feedback': 'The SQL query is well-structured and accurately addresses the question. However, it could be beneficial to include additional demographic information if available, such as the total number of girls or their distribution across different nationalities, to provide a more comprehensive demographic overview.'}
2025-08-08 10:00:27,873 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:27,873 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 10:00:27,907 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:28,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:29,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:30,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:30,545 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at ITC University, which is a qualitative analysis rather than a quantitative query that can be directly answered using the provided database schema. The schema contains data about institutions, students, programs, and related entities, but it does not provide specific insights or qualitative factors that would explain enrollment trends. To answer this question, one would need additional context or data such as marketing strategies, student satisfaction surveys, or external factors influencing enrollment, none of which are present in the schema.', 'feedback': ''}
2025-08-08 10:00:30,545 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:30,545 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:30,546 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at ITC University, which is a qualitative analysis rather than a quantitative query that can be directly answered using the provided database schema. The schema contains data about institutions, students, programs, and related entities, but it does not provide specific insights or qualitative factors that would explain enrollment trends. To answer this question, one would need additional context or data such as marketing strategies, student satisfaction surveys, or external factors influencing enrollment, none of which are present in the schema.', 'feedback': ''}
2025-08-08 10:00:30,638 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:31,110 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:31,132 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe have contributed to this decline in female enrollment at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a decline in female enrollment at ITC University. However, the provided schema does not contain specific data related to enrollment trends, gender demographics, or any factors that could be analyzed to answer this question. The schema includes various tables related to institutions, students, programs, and admissions, but it lacks the necessary historical enrollment data or qualitative factors that would allow for a comprehensive analysis of the decline in female enrollment.', 'feedback': ''}
2025-08-08 10:00:31,132 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:31,132 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:31,132 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe have contributed to this decline in female enrollment at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a decline in female enrollment at ITC University. However, the provided schema does not contain specific data related to enrollment trends, gender demographics, or any factors that could be analyzed to answer this question. The schema includes various tables related to institutions, students, programs, and admissions, but it lacks the necessary historical enrollment data or qualitative factors that would allow for a comprehensive analysis of the decline in female enrollment.', 'feedback': ''}
2025-08-08 10:00:31,151 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:31,167 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps is ITC University taking to improve data collection and transparency regarding gender distribution among its student population?', 'answerable': False, 'reasoning': 'The question asks about specific steps taken by ITC University to improve data collection and transparency regarding gender distribution among its student population. However, the provided schema does not contain any tables or fields that explicitly detail the actions or initiatives of the university regarding data collection or transparency measures. The schema includes various tables related to students, their demographics, and academic records, but it lacks information on institutional policies or steps taken to improve data collection practices. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 10:00:31,167 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:31,167 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:31,167 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps is ITC University taking to improve data collection and transparency regarding gender distribution among its student population?', 'answerable': False, 'reasoning': 'The question asks about specific steps taken by ITC University to improve data collection and transparency regarding gender distribution among its student population. However, the provided schema does not contain any tables or fields that explicitly detail the actions or initiatives of the university regarding data collection or transparency measures. The schema includes various tables related to students, their demographics, and academic records, but it lacks information on institutional policies or steps taken to improve data collection practices. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 10:00:31,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:31,556 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:31,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:32,203 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:32,217 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the higher enrollment of boys compared to girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the higher enrollment of boys compared to girls at ITC University. However, the provided schema does not contain any data or tables that specifically address gender enrollment statistics or the factors influencing them. While there are tables related to students, programs, and admissions, none of them provide insights into the reasons behind gender disparities in enrollment. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 10:00:32,217 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:32,217 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:32,217 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the higher enrollment of boys compared to girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the higher enrollment of boys compared to girls at ITC University. However, the provided schema does not contain any data or tables that specifically address gender enrollment statistics or the factors influencing them. While there are tables related to students, programs, and admissions, none of them provide insights into the reasons behind gender disparities in enrollment. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 10:00:32,883 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:33,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:33,594 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:33,942 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:34,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:34,389 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:34,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:35,544 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:35,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:35,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:35,982 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:36,002 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers at ITC University?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at ITC University. The provided schema contains various tables related to institutions, students, programs, and admissions, but it does not include qualitative data or insights that would explain the reasons behind enrollment numbers. The schema lacks information on marketing strategies, student satisfaction, or external factors influencing enrollment, which are necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 10:00:36,003 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:36,003 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:36,003 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment numbers at ITC University?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at ITC University. The provided schema contains various tables related to institutions, students, programs, and admissions, but it does not include qualitative data or insights that would explain the reasons behind enrollment numbers. The schema lacks information on marketing strategies, student satisfaction, or external factors influencing enrollment, which are necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-08 10:00:36,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:36,574 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:36,612 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe have contributed to this decline in female enrollment at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a decline in female enrollment at ITC University. However, the provided schema does not contain specific data related to enrollment trends, gender statistics, or any factors that could be analyzed to answer this question. The schema includes various tables related to institutions, students, programs, and admissions, but it lacks the necessary data to assess the reasons behind enrollment changes, particularly by gender.', 'feedback': ''}
2025-08-08 10:00:36,612 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:36,612 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:36,612 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe have contributed to this decline in female enrollment at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a decline in female enrollment at ITC University. However, the provided schema does not contain specific data related to enrollment trends, gender statistics, or any factors that could be analyzed to answer this question. The schema includes various tables related to institutions, students, programs, and admissions, but it lacks the necessary data to assess the reasons behind enrollment changes, particularly by gender.', 'feedback': ''}
2025-08-08 10:00:37,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:37,351 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps is ITC University taking to improve data collection and transparency regarding gender distribution among its student population?', 'answerable': False, 'reasoning': 'The question asks about specific steps taken by ITC University to improve data collection and transparency regarding gender distribution among its student population. However, the provided schema does not contain any tables or fields that explicitly detail the actions or initiatives of the university regarding data collection or transparency measures. The schema includes various tables related to students, programs, and other institutional data, but it lacks any direct reference to policies, initiatives, or steps taken by the institution in this context.', 'feedback': ''}
2025-08-08 10:00:37,351 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:37,351 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:37,351 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps is ITC University taking to improve data collection and transparency regarding gender distribution among its student population?', 'answerable': False, 'reasoning': 'The question asks about specific steps taken by ITC University to improve data collection and transparency regarding gender distribution among its student population. However, the provided schema does not contain any tables or fields that explicitly detail the actions or initiatives of the university regarding data collection or transparency measures. The schema includes various tables related to students, programs, and other institutional data, but it lacks any direct reference to policies, initiatives, or steps taken by the institution in this context.', 'feedback': ''}
2025-08-08 10:00:38,320 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:38,338 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the higher enrollment of boys compared to girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the higher enrollment of boys compared to girls at ITC University. However, the provided schema does not contain any data or tables that specifically address gender enrollment statistics or the factors influencing them. While there are tables related to students, programs, and admissions, none of them provide insights into the reasons behind gender disparities in enrollment. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 10:00:38,339 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:38,339 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:38,339 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the higher enrollment of boys compared to girls at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the higher enrollment of boys compared to girls at ITC University. However, the provided schema does not contain any data or tables that specifically address gender enrollment statistics or the factors influencing them. While there are tables related to students, programs, and admissions, none of them provide insights into the reasons behind gender disparities in enrollment. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 10:00:38,760 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:39,095 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:39,125 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What initiatives or programs does ITC University have in place to encourage the enrollment of girls, and how might these impact future demographic data?', 'answerable': False, 'reasoning': 'The question asks about specific initiatives or programs at ITC University aimed at encouraging the enrollment of girls, as well as the potential impact of these initiatives on future demographic data. However, the provided schema does not contain any tables or fields that directly reference initiatives, programs, or demographic data related to gender enrollment. While there are tables related to institutions, students, and programs, none of them provide the necessary information to answer the question about specific initiatives or their impacts on demographics.', 'feedback': ''}
2025-08-08 10:00:39,125 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:39,125 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:39,125 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What initiatives or programs does ITC University have in place to encourage the enrollment of girls, and how might these impact future demographic data?', 'answerable': False, 'reasoning': 'The question asks about specific initiatives or programs at ITC University aimed at encouraging the enrollment of girls, as well as the potential impact of these initiatives on future demographic data. However, the provided schema does not contain any tables or fields that directly reference initiatives, programs, or demographic data related to gender enrollment. While there are tables related to institutions, students, and programs, none of them provide the necessary information to answer the question about specific initiatives or their impacts on demographics.', 'feedback': ''}
2025-08-08 10:00:39,565 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:39,667 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:41,542 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:41,557 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the 100% retention rate for both genders at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to a 100% retention rate for both genders at ITC University. However, the provided schema does not contain specific data or metrics related to retention rates, gender-specific statistics, or factors influencing retention. While there are tables related to students, programs, and institutions, none of them directly provide insights into retention rates or the factors affecting them. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 10:00:41,557 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:41,557 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:41,557 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the 100% retention rate for both genders at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to a 100% retention rate for both genders at ITC University. However, the provided schema does not contain specific data or metrics related to retention rates, gender-specific statistics, or factors influencing retention. While there are tables related to students, programs, and institutions, none of them directly provide insights into retention rates or the factors affecting them. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 10:00:42,018 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:42,042 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:42,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:42,630 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe have contributed to this decline in female enrollment at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a decline in female enrollment at ITC University. However, the provided schema does not contain specific data related to enrollment trends, gender demographics, or any factors that could be analyzed to answer this question. The schema includes various tables related to institutions, students, programs, and admissions, but it lacks the necessary historical enrollment data or qualitative factors that would allow for a comprehensive analysis of the decline in female enrollment.', 'feedback': ''}
2025-08-08 10:00:42,630 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:42,630 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:42,630 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe have contributed to this decline in female enrollment at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a decline in female enrollment at ITC University. However, the provided schema does not contain specific data related to enrollment trends, gender demographics, or any factors that could be analyzed to answer this question. The schema includes various tables related to institutions, students, programs, and admissions, but it lacks the necessary historical enrollment data or qualitative factors that would allow for a comprehensive analysis of the decline in female enrollment.', 'feedback': ''}
2025-08-08 10:00:43,246 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:44,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:45,376 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:45,384 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What initiatives or programs does ITC University have in place to encourage the enrollment of girls, and how might these impact future demographic data?', 'answerable': False, 'reasoning': 'The question asks about specific initiatives or programs at ITC University aimed at encouraging the enrollment of girls, as well as the potential impact of these initiatives on future demographic data. However, the provided schema does not contain any tables or fields that directly reference initiatives, programs, or demographic data related to gender enrollment. While there are tables related to institutions, students, and programs, none of them provide the necessary information to answer the question about specific initiatives or their impacts on demographics.', 'feedback': ''}
2025-08-08 10:00:45,384 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:45,384 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:45,384 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What initiatives or programs does ITC University have in place to encourage the enrollment of girls, and how might these impact future demographic data?', 'answerable': False, 'reasoning': 'The question asks about specific initiatives or programs at ITC University aimed at encouraging the enrollment of girls, as well as the potential impact of these initiatives on future demographic data. However, the provided schema does not contain any tables or fields that directly reference initiatives, programs, or demographic data related to gender enrollment. While there are tables related to institutions, students, and programs, none of them provide the necessary information to answer the question about specific initiatives or their impacts on demographics.', 'feedback': ''}
2025-08-08 10:00:46,373 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:47,348 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:47,369 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the 100% retention rate for both genders at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to a 100% retention rate for both genders at ITC University. However, the provided schema does not contain specific data or metrics related to retention rates or the factors influencing them. While there are tables related to students, programs, and institutions, none of them explicitly provide insights into retention rates or the reasons behind them. Therefore, without additional data or context, this question cannot be answered from the schema.', 'feedback': ''}
2025-08-08 10:00:47,369 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:47,369 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:47,370 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the 100% retention rate for both genders at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to a 100% retention rate for both genders at ITC University. However, the provided schema does not contain specific data or metrics related to retention rates or the factors influencing them. While there are tables related to students, programs, and institutions, none of them explicitly provide insights into retention rates or the reasons behind them. Therefore, without additional data or context, this question cannot be answered from the schema.', 'feedback': ''}
2025-08-08 10:00:47,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:48,095 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:49,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:50,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:51,244 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:51,273 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What initiatives or programs does ITC University have in place to encourage the enrollment of girls, and how might these impact future demographic data?', 'answerable': False, 'reasoning': 'The question asks about specific initiatives or programs at ITC University aimed at encouraging the enrollment of girls, as well as the potential impact of these initiatives on future demographic data. However, the provided schema does not contain any tables or fields that directly relate to initiatives or programs specifically targeting gender enrollment. While there are tables related to institutions, students, and programs, there is no explicit information regarding initiatives or their effects on demographic data. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 10:00:51,273 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:51,273 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:51,273 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What initiatives or programs does ITC University have in place to encourage the enrollment of girls, and how might these impact future demographic data?', 'answerable': False, 'reasoning': 'The question asks about specific initiatives or programs at ITC University aimed at encouraging the enrollment of girls, as well as the potential impact of these initiatives on future demographic data. However, the provided schema does not contain any tables or fields that directly relate to initiatives or programs specifically targeting gender enrollment. While there are tables related to institutions, students, and programs, there is no explicit information regarding initiatives or their effects on demographic data. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 10:00:53,086 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:53,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:53,841 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the 100% retention rate for both genders at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to a 100% retention rate for both genders at ITC University. However, the provided schema does not contain specific data or metrics related to retention rates, gender-specific statistics, or factors influencing retention. While there are tables related to students, programs, and institutions, none of them provide direct insights into retention rates or the factors affecting them. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 10:00:53,841 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:53,842 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:53,842 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the 100% retention rate for both genders at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to a 100% retention rate for both genders at ITC University. However, the provided schema does not contain specific data or metrics related to retention rates, gender-specific statistics, or factors influencing retention. While there are tables related to students, programs, and institutions, none of them provide direct insights into retention rates or the factors affecting them. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 10:00:54,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:55,299 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:56,503 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:56,801 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:57,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:57,658 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What initiatives or programs does ITC University have in place to encourage the enrollment of girls, and how might these impact future demographic data?', 'answerable': False, 'reasoning': 'The question asks about specific initiatives or programs at ITC University aimed at encouraging the enrollment of girls, as well as the potential impact of these initiatives on future demographic data. However, the provided schema does not contain any tables or fields that directly reference initiatives, programs, or demographic data related to gender enrollment. While there are tables related to institutions, students, and programs, none of them provide the necessary information to answer the question about specific initiatives or their impacts on demographics.', 'feedback': ''}
2025-08-08 10:00:57,659 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:57,659 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:57,659 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What initiatives or programs does ITC University have in place to encourage the enrollment of girls, and how might these impact future demographic data?', 'answerable': False, 'reasoning': 'The question asks about specific initiatives or programs at ITC University aimed at encouraging the enrollment of girls, as well as the potential impact of these initiatives on future demographic data. However, the provided schema does not contain any tables or fields that directly reference initiatives, programs, or demographic data related to gender enrollment. While there are tables related to institutions, students, and programs, none of them provide the necessary information to answer the question about specific initiatives or their impacts on demographics.', 'feedback': ''}
2025-08-08 10:00:58,271 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:59,612 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:00:59,673 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the 100% retention rate for both genders at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to a 100% retention rate for both genders at ITC University. However, the provided schema does not contain any specific data or metrics related to retention rates, gender-specific statistics, or factors influencing retention. While there are tables related to students, programs, and institutions, none of them provide insights into the reasons or factors behind retention rates. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 10:00:59,674 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:00:59,674 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:00:59,674 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the 100% retention rate for both genders at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to a 100% retention rate for both genders at ITC University. However, the provided schema does not contain any specific data or metrics related to retention rates, gender-specific statistics, or factors influencing retention. While there are tables related to students, programs, and institutions, none of them provide insights into the reasons or factors behind retention rates. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 10:01:02,595 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:03,695 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:06,416 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:11,598 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:11,728 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:13,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:15,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:19,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:20,002 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:21,491 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:25,672 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:29,505 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:30,429 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:34,722 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:36,631 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:37,488 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:37,513 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific scholarships or initiatives at ITC University aimed at increasing female enrollment?', 'sql': "SELECT DISTINCT s.id AS student_id, s.surname, s.othernames, ss.type, ss.value, sa.name AS award_name \nFROM student_scholarships ss \nJOIN students s ON ss.student_id = s.id \nJOIN student_award_types sa ON ss.type = sa.id \nWHERE s.sex = 'F' AND ss.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University') \nAND (sa.name LIKE '%female%' OR sa.name LIKE '%women%');", 'correct': True, 'reasoning': "The SQL query correctly identifies scholarships or initiatives at ITC University that are specifically aimed at female students. It filters students by sex ('F' for female) and checks for the institution's name. Additionally, it looks for award names that contain 'female' or 'women', which aligns with the question's focus on initiatives aimed at increasing female enrollment.", 'feedback': "The SQL query is well-structured and effectively addresses the question. However, it could be improved by explicitly stating that the query is looking for scholarships or initiatives that are specifically designed to increase female enrollment, rather than just filtering by the presence of 'female' or 'women' in the award names. This could involve adding a more specific condition or context in the query."}
2025-08-08 10:01:37,513 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:01:37,513 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 10:01:40,283 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:41,559 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:43,056 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:46,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:47,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:52,060 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:52,075 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What do you think are the potential barriers that might prevent female students from enrolling at ITC University, and how could the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for an analysis of potential barriers preventing female students from enrolling at ITC University and suggestions for addressing these challenges. The provided database schema contains various tables related to institutions, students, programs, and admissions, but it does not include qualitative data or insights into social, cultural, or economic factors that could affect female student enrollment. Additionally, there are no tables that provide information on barriers or challenges faced by students, nor any mechanisms for addressing such issues. Therefore, the question cannot be answered using the existing schema.', 'feedback': ''}
2025-08-08 10:01:52,075 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:01:52,075 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:01:52,076 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What do you think are the potential barriers that might prevent female students from enrolling at ITC University, and how could the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for an analysis of potential barriers preventing female students from enrolling at ITC University and suggestions for addressing these challenges. The provided database schema contains various tables related to institutions, students, programs, and admissions, but it does not include qualitative data or insights into social, cultural, or economic factors that could affect female student enrollment. Additionally, there are no tables that provide information on barriers or challenges faced by students, nor any mechanisms for addressing such issues. Therefore, the question cannot be answered using the existing schema.', 'feedback': ''}
2025-08-08 10:01:54,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:54,648 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses have the highest enrollment of girls at ITC University?', 'sql': "SELECT 'Program' AS type, p.long_name AS name, COUNT(sp.student_id) AS female_enrollment\nFROM core.programs p\nJOIN core.student_programs sp ON p.id = sp.program_id\nJOIN core.students s ON sp.student_id = s.id\nWHERE s.sex = 'F'\nGROUP BY p.id, p.long_name\nORDER BY female_enrollment DESC\nLIMIT 1\nUNION ALL\nSELECT 'Course' AS type, c.title AS name, COUNT(sp.student_id) AS female_enrollment\nFROM core.courses c\nJOIN core.student_programs sp ON c.unit_id = sp.unit_id\nJOIN core.students s ON sp.student_id = s.id\nWHERE s.sex = 'F'\nGROUP BY c.id, c.title\nORDER BY female_enrollment DESC\nLIMIT 1;", 'correct': False, 'reasoning': "The SQL query attempts to retrieve the programs and courses with the highest enrollment of female students. However, it does not filter the results specifically for 'ITC University'. The query should include a condition to filter by the institution_id for both programs and courses to ensure that only data from ITC University is considered. Without this filter, the results may include programs and courses from other institutions, which does not accurately answer the question.", 'feedback': "To improve the SQL query, add a condition in both SELECT statements to filter by institution_id, specifically for ITC University. For example, include 'WHERE p.institution_id = <ITC_University_ID>' for programs and 'WHERE c.institution_id = <ITC_University_ID>' for courses, replacing <ITC_University_ID> with the actual ID for ITC University.", 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-08 10:01:54,648 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:01:54,648 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-08 10:01:55,721 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:59,106 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:01:59,122 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What do you think are the potential barriers that might prevent female students from enrolling at ITC University, and how could the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for an analysis of potential barriers preventing female students from enrolling at ITC University and suggestions for addressing these challenges. The provided database schema contains various tables related to institutions, students, programs, and other academic-related data, but it does not include qualitative data or insights into social, cultural, or economic factors that could affect female student enrollment. Additionally, there are no tables that specifically address barriers to enrollment or strategies for overcoming them. Therefore, this question cannot be answered using the existing schema.', 'feedback': ''}
2025-08-08 10:01:59,122 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:01:59,122 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:01:59,122 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What do you think are the potential barriers that might prevent female students from enrolling at ITC University, and how could the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for an analysis of potential barriers preventing female students from enrolling at ITC University and suggestions for addressing these challenges. The provided database schema contains various tables related to institutions, students, programs, and other academic-related data, but it does not include qualitative data or insights into social, cultural, or economic factors that could affect female student enrollment. Additionally, there are no tables that specifically address barriers to enrollment or strategies for overcoming them. Therefore, this question cannot be answered using the existing schema.', 'feedback': ''}
2025-08-08 10:01:59,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:02,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:03,177 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:05,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:05,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:05,777 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What do you think are the potential barriers that might prevent female students from enrolling at ITC University, and how could the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for an analysis of potential barriers preventing female students from enrolling at ITC University and suggestions for addressing these challenges. The provided database schema contains various tables related to institutions, students, programs, and admissions, but it does not include qualitative data or insights into social, cultural, or economic factors that could be barriers to enrollment. Additionally, there are no tables that provide information on student experiences, feedback, or institutional policies that could inform how the university might address these challenges. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 10:02:05,778 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:02:05,778 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:02:05,778 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What do you think are the potential barriers that might prevent female students from enrolling at ITC University, and how could the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for an analysis of potential barriers preventing female students from enrolling at ITC University and suggestions for addressing these challenges. The provided database schema contains various tables related to institutions, students, programs, and admissions, but it does not include qualitative data or insights into social, cultural, or economic factors that could be barriers to enrollment. Additionally, there are no tables that provide information on student experiences, feedback, or institutional policies that could inform how the university might address these challenges. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 10:02:08,838 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:12,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:12,343 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What do you think are the potential barriers that might prevent female students from enrolling at ITC University, and how could the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for an analysis of potential barriers preventing female students from enrolling at ITC University and suggestions for addressing these challenges. The provided database schema contains information about institutions, students, programs, and various related entities, but it does not include qualitative data or insights into social, cultural, or economic factors that could be barriers to enrollment. Additionally, there are no fields that would allow for the collection of opinions or feedback from students regarding their enrollment challenges. Therefore, this question cannot be answered using the existing schema.', 'feedback': ''}
2025-08-08 10:02:12,343 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:02:12,343 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:02:12,343 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What do you think are the potential barriers that might prevent female students from enrolling at ITC University, and how could the university address these challenges?', 'answerable': False, 'reasoning': 'The question asks for an analysis of potential barriers preventing female students from enrolling at ITC University and suggestions for addressing these challenges. The provided database schema contains information about institutions, students, programs, and various related entities, but it does not include qualitative data or insights into social, cultural, or economic factors that could be barriers to enrollment. Additionally, there are no fields that would allow for the collection of opinions or feedback from students regarding their enrollment challenges. Therefore, this question cannot be answered using the existing schema.', 'feedback': ''}
2025-08-08 10:02:14,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:21,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:29,277 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:33,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:35,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:47,938 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:02:58,864 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:03,025 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:06,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:08,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:18,745 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:24,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:28,164 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:31,607 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:33,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:42,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:53,106 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:56,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:03:59,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:01,136 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:06,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:14,127 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:14,148 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses have the highest enrollment of girls at ITC University?', 'sql': "SELECT p.long_name AS program_name, COUNT(sp.student_id) AS female_enrollment\nFROM student_programs sp\nJOIN students s ON sp.student_id = s.id\nJOIN programs p ON sp.program_id = p.id\nWHERE s.sex = 'F' AND sp.institution_id = 'ITC University'\nGROUP BY p.long_name\nORDER BY female_enrollment DESC;", 'correct': False, 'reasoning': 'The SQL query focuses on counting the number of female students enrolled in various programs at ITC University, which is a part of the question. However, the question also asks about courses, and the SQL query does not include any information about courses. The schema indicates that courses are a separate entity from programs, and the query does not account for enrollment in courses, which is necessary to fully answer the question.', 'feedback': 'To improve the SQL query, it should also include a join with the courses table to count female enrollments in courses as well. Additionally, the question could be clarified by specifying whether it is asking for programs, courses, or both, and if both are needed, the SQL should reflect that by including both entities.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-08 10:04:14,148 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:04:14,148 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-08 10:04:17,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:20,915 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:22,180 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:28,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:35,877 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:35,881 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses have the highest enrollment of girls at ITC University?', 'sql': "SELECT p.long_name, COUNT(sp.student_id) AS girl_enrollment\nFROM core.programs p\nJOIN core.student_programs sp ON p.id = sp.program_id\nJOIN core.students s ON sp.student_id = s.id\nWHERE s.sex = 'F' AND s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY p.long_name\nORDER BY girl_enrollment DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the programs with the highest enrollment of female students at ITC University. It joins the 'programs', 'student_programs', and 'students' tables to gather the necessary data. The WHERE clause filters for female students and ensures they are enrolled at ITC University. The COUNT function aggregates the number of female students per program, and the results are grouped by program name and ordered by enrollment count in descending order, which aligns with the question's requirements.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be beneficial to clarify the term 'highest enrollment' in the question to specify whether it refers to the total number of enrollments or the percentage of female students in each program. Additionally, consider adding a LIMIT clause to the SQL query if only the top N programs are needed."}
2025-08-08 10:04:35,881 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:04:35,881 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 10:04:46,402 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:47,655 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:49,149 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:51,759 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:55,216 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:04:55,243 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment of girls in these specific education programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the high enrollment of girls in specific education programs at ITC University. However, the provided schema does not contain any direct information about gender enrollment trends, societal factors, or specific educational programs that would allow for an analysis of these factors. The schema includes data about institutions, students, programs, and various related entities, but it lacks qualitative data or insights into the reasons behind enrollment patterns.', 'feedback': ''}
2025-08-08 10:04:55,243 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:04:55,243 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:04:55,243 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment of girls in these specific education programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the high enrollment of girls in specific education programs at ITC University. However, the provided schema does not contain any direct information about gender enrollment trends, societal factors, or specific educational programs that would allow for an analysis of these factors. The schema includes data about institutions, students, programs, and various related entities, but it lacks qualitative data or insights into the reasons behind enrollment patterns.', 'feedback': ''}
2025-08-08 10:04:57,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:00,494 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:00,531 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment of girls in these specific education programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the high enrollment of girls in specific education programs at ITC University. However, the provided schema does not contain any direct information about gender enrollment statistics, specific educational programs, or factors influencing enrollment. While there are tables related to students, programs, and admissions, none of them provide qualitative data or insights into the reasons behind enrollment trends. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 10:05:00,532 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 10:05:00,532 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 10:05:00,532 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high enrollment of girls in these specific education programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the high enrollment of girls in specific education programs at ITC University. However, the provided schema does not contain any direct information about gender enrollment statistics, specific educational programs, or factors influencing enrollment. While there are tables related to students, programs, and admissions, none of them provide qualitative data or insights into the reasons behind enrollment trends. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 10:05:00,533 - root - INFO - [{'total_enrollment': 192627}]
2025-08-08 10:05:00,533 - root - INFO - [{'sex': '', 'number_of_students': 8205}, {'sex': 'M', 'number_of_students': 96457}, {'sex': 'F', 'number_of_students': 87965}]
2025-08-08 10:05:00,533 - root - INFO - [{'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'girl_enrollment': 9242}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE)', 'girl_enrollment': 6867}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY)', 'girl_enrollment': 6378}, {'long_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'girl_enrollment': 5947}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'girl_enrollment': 4587}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'girl_enrollment': 4055}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'girl_enrollment': 4002}, {'long_name': 'BACHELOR OF ARTS (ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 2125}, {'long_name': 'BACHELOR OF ART (SOCIAL STUDIES EDUCATION)', 'girl_enrollment': 2054}, {'long_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'girl_enrollment': 1977}, {'long_name': 'BACHELOR OF ARTS (POLITICAL SCIENCE EDUCATION)', 'girl_enrollment': 1713}, {'long_name': 'DIPLOMA IN EARLY GRADE', 'girl_enrollment': 1555}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - R', 'girl_enrollment': 1437}, {'long_name': 'BACHELOR OF SCIENCE (FOOD AND NUTRITION EDUCATION)', 'girl_enrollment': 1250}, {'long_name': 'BACHELOR OF SCIENCE (MANAGEMENT EDUCATION)', 'girl_enrollment': 1238}, {'long_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'girl_enrollment': 1204}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - S', 'girl_enrollment': 1112}, {'long_name': 'Diploma In Education', 'girl_enrollment': 1086}, {'long_name': 'BACHELOR OF ARTS (SOCIAL STUDIES EDUCATION)', 'girl_enrollment': 1025}, {'long_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'girl_enrollment': 957}, {'long_name': 'BACHELOR OF ARTS (FRENCH EDUCATION)', 'girl_enrollment': 917}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS EDUCATION)', 'girl_enrollment': 872}, {'long_name': 'DIPLOMA IN BASIC EDUCATION', 'girl_enrollment': 843}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED HOME ECONOMICS EDUCATION)', 'girl_enrollment': 777}, {'long_name': 'Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'girl_enrollment': 774}, {'long_name': 'BACHELOR OF ARTS (GEOGRAPHY EDUCATION)', 'girl_enrollment': 770}, {'long_name': 'BACHELOR OF ARTS (ENGLISH EDUCATION)', 'girl_enrollment': 736}, {'long_name': 'BACHELOR OF ARTS (EWE EDUCATION)', 'girl_enrollment': 726}, {'long_name': 'BACHELOR OF SCIENCE (ACCOUNTING EDUCATION)', 'girl_enrollment': 715}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - R', 'girl_enrollment': 663}, {'long_name': 'Bachelor Of Education (Early Grade) - M', 'girl_enrollment': 662}, {'long_name': 'Bachelor Of Science (Information Technology Education)', 'girl_enrollment': 610}, {'long_name': 'BACHELOR OF SCIENCE (HEALTH ADMINISTRATION EDUCATION)', 'girl_enrollment': 604}, {'long_name': 'BACHELOR OF ARTS (ART EDUCATION)', 'girl_enrollment': 585}, {'long_name': 'BACHELOR OF ARTS (FASHION DESIGN AND TEXTILE EDUCATION)', 'girl_enrollment': 582}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION AND MANAGEMENT) BY DISTANCE', 'girl_enrollment': 576}, {'long_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL HEALTH AND SANITATION EDUCATION)', 'girl_enrollment': 555}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'girl_enrollment': 552}, {'long_name': 'BACHELOR OF EDUCATION (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'girl_enrollment': 548}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - S', 'girl_enrollment': 534}, {'long_name': 'BACHELOR OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'girl_enrollment': 534}, {'long_name': 'BACHELOR OF ARTS (TWI EDUCATION)', 'girl_enrollment': 493}, {'long_name': 'BACHELOR OF SCIENCE (HOME ECONOMICS EDUCATION)', 'girl_enrollment': 465}, {'long_name': 'Bachelor Of Arts (Economics Education)', 'girl_enrollment': 461}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL EDUCATION)', 'girl_enrollment': 443}, {'long_name': 'DIPLOMA (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'girl_enrollment': 437}, {'long_name': 'Bachelor Of Arts (Social Studies With Economics Education)', 'girl_enrollment': 426}, {'long_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'girl_enrollment': 392}, {'long_name': 'Bachelor Of Science (Biological Sciences Education)', 'girl_enrollment': 385}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED SCIENCE EDUCATION)', 'girl_enrollment': 367}, {'long_name': 'BACHELOR OF ARTS (HISTORY EDUCATION)', 'girl_enrollment': 358}, {'long_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'girl_enrollment': 358}, {'long_name': 'Master Of Arts In Educational Leadership', 'girl_enrollment': 321}, {'long_name': 'BACHELOR OF SCIENCE (AGRICULTURAL SCIENCE EDUCATION)', 'girl_enrollment': 296}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'girl_enrollment': 295}, {'long_name': 'BACHELOR OF ARTS (GRAPHIC DESIGN)', 'girl_enrollment': 289}, {'long_name': 'Bachelor Of Education (Junior High)', 'girl_enrollment': 281}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICAL EDUCATION)', 'girl_enrollment': 280}, {'long_name': 'BACHELOR OF ARTS (TWI WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 255}, {'long_name': 'MASTER OF PHILOSOPHY (EARLY CHILDHOOD EDUCATION)', 'girl_enrollment': 229}, {'long_name': 'BACHELOR  OF ARTS (EWE WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 227}, {'long_name': 'BACHELOR OF SCIENCE (BIOLOGY EDUCATION)', 'girl_enrollment': 220}, {'long_name': 'Bachelor Of Business Administration (Management) - E', 'girl_enrollment': 213}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND ENGLISH EDUCATION)', 'girl_enrollment': 210}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY) BY DISTANCE', 'girl_enrollment': 207}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education) - S', 'girl_enrollment': 204}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 202}, {'long_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'girl_enrollment': 191}, {'long_name': 'MASTER OF PHILOSOPHY (ENGLISH LANGUAGE)', 'girl_enrollment': 190}, {'long_name': 'Diploma In Business Administration (Management)', 'girl_enrollment': 188}, {'long_name': 'MASTER OF PHILOSOPHY (GHANAIAN LANGUAGE STUDIES)', 'girl_enrollment': 184}, {'long_name': 'BACHELOR OF ARTS (COMMUNICATION STUDIES)', 'girl_enrollment': 183}, {'long_name': 'Master Of Technology Education In Catering And Hospitality', 'girl_enrollment': 172}, {'long_name': 'MASTER OF PHILOSOPHY (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'girl_enrollment': 159}, {'long_name': 'BACHELOR OF ARTS (FANTE EDUCATION)', 'girl_enrollment': 154}, {'long_name': 'BACHELOR OF ARTS (THEATRE ARTS)', 'girl_enrollment': 151}, {'long_name': 'MASTER OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'girl_enrollment': 149}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'girl_enrollment': 144}, {'long_name': 'MASTER OF EDUCATION (EARLY CHILDHOOD EDUCATION)', 'girl_enrollment': 134}, {'long_name': 'DIPLOMA IN MANAGEMENT STUDIES', 'girl_enrollment': 134}, {'long_name': 'MASTER OF PHILOSOPHY (FOOD AND NUTRITION)', 'girl_enrollment': 133}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - W', 'girl_enrollment': 133}, {'long_name': 'Bachelor Of Education (Early Grade) - K', 'girl_enrollment': 130}, {'long_name': 'Bachelor Of Arts (Economics With Social Studies Education)', 'girl_enrollment': 128}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'girl_enrollment': 125}, {'long_name': 'BACHELOR OF ARTS (MUSIC EDUCATION)', 'girl_enrollment': 121}, {'long_name': 'Master Of Philosophy In Catering And Hospitality', 'girl_enrollment': 109}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (MARKETING AND ENTREPRENEURSHIP)', 'girl_enrollment': 108}, {'long_name': 'Bachelor Of Business Administration (Management) - W', 'girl_enrollment': 105}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - R', 'girl_enrollment': 104}, {'long_name': 'DIPLOMA (TEXTILES AND FASHION)', 'girl_enrollment': 104}, {'long_name': 'DIPLOMA (EARLY GRADE)', 'girl_enrollment': 102}, {'long_name': 'MASTER OF PHILOSOPHY (BASIC EDUCATION)', 'girl_enrollment': 102}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - S', 'girl_enrollment': 102}, {'long_name': 'MASTER OF EDUCATION (HOME ECONOMICS)', 'girl_enrollment': 100}, {'long_name': 'MASTER OF PHILOSOPHY (SPECIAL EDUCATION)', 'girl_enrollment': 98}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'girl_enrollment': 98}, {'long_name': 'DIPLOMA (ART)', 'girl_enrollment': 96}, {'long_name': 'BACHELOR OF ARTS (STRATEGIC COMMUNICATION)', 'girl_enrollment': 96}, {'long_name': 'MASTER OF PHILOSOPHY IN SOCIAL STUDIES', 'girl_enrollment': 95}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND TWI EDUCATION)', 'girl_enrollment': 94}, {'long_name': 'DIPLOMA (BASIC EDUCATION)', 'girl_enrollment': 94}, {'long_name': 'BACHELOR OF SCIENCE (FAMILY LIFE MANAGEMENT EDUCATION)', 'girl_enrollment': 93}, {'long_name': 'Master Of Business Administration (Human Resource Management And Organisational Behaviour ) - Weekend', 'girl_enrollment': 92}, {'long_name': 'MASTER OF PHILOSOPHY (SCIENCE EDUCATION)', 'girl_enrollment': 92}, {'long_name': 'Bachelor Of Science (Chemistry Education)', 'girl_enrollment': 88}, {'long_name': 'MASTER OF PHILOSOPHY (ART EDUCATION)', 'girl_enrollment': 88}, {'long_name': 'Bachelor Of Business Administration (Secretarial Education)', 'girl_enrollment': 86}, {'long_name': 'CERTIFICATE (PRE-SCHOOL EDUCATION)', 'girl_enrollment': 84}, {'long_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'girl_enrollment': 77}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FRENCH EDUCATION)', 'girl_enrollment': 77}, {'long_name': 'Bachelor Of Science (Agribusiness Management And Entrepreneurship Education)', 'girl_enrollment': 77}, {'long_name': 'DIPLOMA (THEATRE ARTS)', 'girl_enrollment': 76}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'girl_enrollment': 76}, {'long_name': 'MASTER OF PHILOSOPHY (FRENCH)', 'girl_enrollment': 74}, {'long_name': 'MASTER OF ARTS (ENGLISH LANGUAGE)', 'girl_enrollment': 71}, {'long_name': 'Bachelor Of Science In Administration (Accounting)', 'girl_enrollment': 70}, {'long_name': 'Diploma In Catering And Hospitality', 'girl_enrollment': 66}, {'long_name': 'Bachelor Of Science (Information Technology Education) - W', 'girl_enrollment': 63}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES)', 'girl_enrollment': 61}, {'long_name': 'BACHELOR OF ARTS (RELIGIONS AND MORAL STUDIES EDUCATION)', 'girl_enrollment': 58}, {'long_name': 'MASTER OF EDUCATION (INSTITUTIONAL MENTORSHIP AND SUPERVISION)', 'girl_enrollment': 57}, {'long_name': 'BACHELOR ARTS (GA WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 57}, {'long_name': 'DIPLOMA (FRENCH EDUCATION)', 'girl_enrollment': 56}, {'long_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'girl_enrollment': 56}, {'long_name': 'BACHELOR OF ARTS (TWI WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 54}, {'long_name': 'MASTER OF EDUCATION (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'girl_enrollment': 53}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles', 'girl_enrollment': 53}, {'long_name': 'Master Of Business Administration (Accounting)', 'girl_enrollment': 52}, {'long_name': 'BACHELOR OF MUSIC', 'girl_enrollment': 51}, {'long_name': 'Post Diploma Bachelor Of Science (Environmental Health And Sanitation Education)', 'girl_enrollment': 50}, {'long_name': 'Master Of Philosophy In Catering And Hospitality (Top-Up)', 'girl_enrollment': 49}, {'long_name': 'MASTER OF ARTS (STRATEGIC COMMUNICATION)', 'girl_enrollment': 48}, {'long_name': 'DOCTOR OF PHILOSOPHY (SCIENCE EDUCATION)', 'girl_enrollment': 48}, {'long_name': 'MASTER OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'girl_enrollment': 47}, {'long_name': 'BACHELOR OF ARTS (EWE WITH FRENCH EDUCATION)', 'girl_enrollment': 46}, {'long_name': 'Bachelor Of Arts (Arabic Education)', 'girl_enrollment': 44}, {'long_name': 'DIPLOMA (GRAPHIC DESIGN)', 'girl_enrollment': 43}, {'long_name': 'BACHELOR OF ART (DANGME WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 43}, {'long_name': 'BACHELOR OF ARTS (CREATIVE ARTS AND DESIGN EDUCATION)', 'girl_enrollment': 43}, {'long_name': 'BACHELOR OF ARTS (DANGME EDUCATION)', 'girl_enrollment': 42}, {'long_name': 'DOCTOR OF PHILOSOPHY (APPLIED LINGUISTICS)', 'girl_enrollment': 42}, {'long_name': 'Bachelor Of Science (Natural Resources Management And Education)', 'girl_enrollment': 42}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS WITH ECONOMICS EDUCATION)', 'girl_enrollment': 41}, {'long_name': 'Diploma In Education - KS', 'girl_enrollment': 41}, {'long_name': 'Bachelor Of Arts (French With English Education)', 'girl_enrollment': 41}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - R', 'girl_enrollment': 40}, {'long_name': 'Master Of Technology Education In Fashion Design And Textile', 'girl_enrollment': 39}, {'long_name': 'MASTER OF ARTS (GHANAIAN LANGUAGES EDUCATION)', 'girl_enrollment': 39}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (FINANCE)', 'girl_enrollment': 39}, {'long_name': 'DIPLOMA IN ACCOUNTING', 'girl_enrollment': 38}, {'long_name': 'BACHELOR OF ARTS (EWE WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 38}, {'long_name': 'Diploma In Environmental Health And Sanitation Education', 'girl_enrollment': 38}, {'long_name': 'MASTER OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'girl_enrollment': 37}, {'long_name': 'Diploma In Fashion Design And Textiles', 'girl_enrollment': 37}, {'long_name': 'Post Diploma Bachelor Of Science In Environmental Health And Sanitation Education - S', 'girl_enrollment': 36}, {'long_name': 'DIPLOMA IN MUSIC', 'girl_enrollment': 35}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - R', 'girl_enrollment': 34}, {'long_name': 'Master Of Philosophy In Educational Leadership', 'girl_enrollment': 34}, {'long_name': 'MASTER OF PHILOSOPHY (CLOTHING AND TEXTILES)', 'girl_enrollment': 33}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education)', 'girl_enrollment': 33}, {'long_name': 'Bachelor Of Business Administration (Executive Office Administration)', 'girl_enrollment': 33}, {'long_name': 'MASTER OF PHILOSOPHY (STRATEGIC COMMUNICATION)', 'girl_enrollment': 32}, {'long_name': 'DOCTOR OF PHILOSOPHY (ARTS AND CULTURE)', 'girl_enrollment': 32}, {'long_name': 'BACHELOR OF EDUCATION (PSYCHOLOGY)', 'girl_enrollment': 32}, {'long_name': 'DOCTOR OF PHILOSOPHY (EDUCATIONAL LEADERSHIP)', 'girl_enrollment': 31}, {'long_name': 'MASTER OF PHILOSOPHY (POLITICAL SCIENCE EDUCATION)', 'girl_enrollment': 31}, {'long_name': 'MASTER OF EDUCATION (GUIDANCE AND COUNSELLING)', 'girl_enrollment': 31}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT COMMUNICATION)', 'girl_enrollment': 31}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (FINANCE)', 'girl_enrollment': 31}, {'long_name': 'MASTER OF EDUCATION (SCIENCE)', 'girl_enrollment': 30}, {'long_name': 'MASTER OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'girl_enrollment': 30}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'girl_enrollment': 30}, {'long_name': 'Bachelor Of Science In Occupational Health And Safety', 'girl_enrollment': 30}, {'long_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'girl_enrollment': 29}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - E', 'girl_enrollment': 29}, {'long_name': 'BACHELOR OF ARTS (DAGAARE EDUCATION)', 'girl_enrollment': 28}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 28}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'girl_enrollment': 28}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICS EDUCATION)', 'girl_enrollment': 27}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BUSINESS INFORMATION SYSTEMS)', 'girl_enrollment': 27}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'girl_enrollment': 27}, {'long_name': 'MASTER OF EDUCATION IN SCHOOL SUPERVISION AND QUALITY ASSURANCE)', 'girl_enrollment': 26}, {'long_name': 'BACHELOR OF ARTS (TWI WITH GERMAN EDUCATION)', 'girl_enrollment': 26}, {'long_name': 'BACHELOR OF ARTS (EWE WITH GERMAN EDUCATION)', 'girl_enrollment': 26}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles (Top-Up)', 'girl_enrollment': 26}, {'long_name': 'MASTER OF PHILOSOPHY (TEXTILES AND FASHION)', 'girl_enrollment': 26}, {'long_name': 'DOCTOR OF PHILOSOPHY (SOCIAL STUDIES)', 'girl_enrollment': 25}, {'long_name': 'MASTER OF EDUCATION (FOOD AND NUTRITION)', 'girl_enrollment': 25}, {'long_name': 'EXECUTIVE MASTERS (CONFLICT HUMAN RIGHTS & PEACE STUDIES)', 'girl_enrollment': 25}, {'long_name': 'BACHELOR OF ARTS (DEVELOPMENT COMMUNICATION)', 'girl_enrollment': 25}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - W', 'girl_enrollment': 25}, {'long_name': 'MASTER OF EDUCATION (FRENCH)', 'girl_enrollment': 24}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'girl_enrollment': 24}, {'long_name': 'MASTER OF PHILOSOPHY (CURRICULUM & PEDAGOGIC STUDIES)', 'girl_enrollment': 24}, {'long_name': 'Diploma In Business Administration (Accounting)', 'girl_enrollment': 24}, {'long_name': 'Bachelor Of Science (Information Technology)', 'girl_enrollment': 24}, {'long_name': 'BACHELOR OF ARTS (GA EDUCATION)', 'girl_enrollment': 23}, {'long_name': 'MASTER OF PHILOSOPHY (MUSIC EDUCATION)', 'girl_enrollment': 23}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH FRENCH EDUCATION)', 'girl_enrollment': 23}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS)', 'girl_enrollment': 22}, {'long_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'girl_enrollment': 22}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH GERMAN EDUCATION)', 'girl_enrollment': 22}, {'long_name': 'BACHELOR OF ART (SISAALI WITH ENGLISH EDUCATION)', 'girl_enrollment': 22}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 21}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'girl_enrollment': 21}, {'long_name': 'MASTER OF EDUCATION (SPECIAL EDUCATION)', 'girl_enrollment': 20}, {'long_name': 'BACHELOR OF ARTS (DAGBANI EDUCATION)', 'girl_enrollment': 20}, {'long_name': 'Diploma In Business Administration (Management) - W', 'girl_enrollment': 20}, {'long_name': 'Diploma In Economics', 'girl_enrollment': 20}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (LOGISTICS AND TRANSPORT MANAGEMENT)', 'girl_enrollment': 20}, {'long_name': 'POST GRADUATE DIPLOMA IN TEACHING AND LEARNING IN HIGHER EDUCATION', 'girl_enrollment': 19}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 19}, {'long_name': 'BACHELOR OF ARTS (GA WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 19}, {'long_name': 'DOCTOR OF PHILOSOPHY IN GHANAIAN LANGUAGES', 'girl_enrollment': 19}, {'long_name': 'MASTER OF PHILOSOPHY (FAMILY LIFE MANAGEMENT)', 'girl_enrollment': 19}, {'long_name': 'MASTER OF ARTS (COMMUNICATION INSTRUCTION)', 'girl_enrollment': 18}, {'long_name': 'DOCTOR OF PHILOSOPHY (COMMUNICATION AND MEDIA)', 'girl_enrollment': 18}, {'long_name': 'Diploma In Education - M', 'girl_enrollment': 18}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH GERMAN EDUCATION)', 'girl_enrollment': 17}, {'long_name': 'Diploma In Electrical And Electronics Engineering Technology', 'girl_enrollment': 17}, {'long_name': 'Master Of Philosophy In Business Management', 'girl_enrollment': 17}, {'long_name': 'DOCTOR OF PHILOSOPHY (ENGLISH LANGUAGE)', 'girl_enrollment': 16}, {'long_name': 'MASTER OF ARTS (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES', 'girl_enrollment': 16}, {'long_name': 'Bachelor Of Business Administration (Management) - R', 'girl_enrollment': 16}, {'long_name': 'Bachelor Of Science (Mathematics Education) - W', 'girl_enrollment': 16}, {'long_name': 'MASTER OF PHILOSOPHY (VISUAL COMMUNICATION)', 'girl_enrollment': 15}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 15}, {'long_name': 'DIPLOMA IN SPORTS COACHING', 'girl_enrollment': 15}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FANTE EDUCATION)', 'girl_enrollment': 15}, {'long_name': 'MASTER OF PHILOSOPHY (INFORMATION AND COMMUNICATION TECHNOLOGY)', 'girl_enrollment': 15}, {'long_name': 'DOCTOR OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'girl_enrollment': 15}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGMENT & ORGANIZATION BEHAVIOUR)', 'girl_enrollment': 15}, {'long_name': 'BACHELOR OF ART (KUSAAL WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 15}, {'long_name': 'MASTER OF EDUCATION (ENGLISH LANGUAGE) BY DISTANCE', 'girl_enrollment': 15}, {'long_name': 'Diploma In Education (Junior High)', 'girl_enrollment': 15}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship) - W', 'girl_enrollment': 15}, {'long_name': 'MASTER OF EDUCATION (GEOGRAPHY)', 'girl_enrollment': 14}, {'long_name': 'MASTER OF PHILOSOPHY (ECONOMICS)', 'girl_enrollment': 14}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 14}, {'long_name': 'MASTER OF PHILOSOPHY (CHEMISTRY EDUCATION)', 'girl_enrollment': 14}, {'long_name': 'DOCTOR OF PHILOSOPHY (SPECIAL EDUCATION)', 'girl_enrollment': 14}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 14}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance)', 'girl_enrollment': 14}, {'long_name': 'BACHELOR OF ARTS (GONJA EDUCATION)', 'girl_enrollment': 13}, {'long_name': 'BACHELOR OF SCIENCE (INSURANCE AND RISK MANAGEMENT)', 'girl_enrollment': 13}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH ENGLISH EDUCATION)', 'girl_enrollment': 13}, {'long_name': 'Bachelor Of Arts (English Language Education) - M', 'girl_enrollment': 13}, {'long_name': 'Master Of Science In Information Technology Education', 'girl_enrollment': 13}, {'long_name': 'Master Of Philosophy In Environmental And Occupational Health Education', 'girl_enrollment': 13}, {'long_name': 'BACHELOR OF ARTS (TWI WITH FRENCH EDUCATION)', 'girl_enrollment': 12}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH LINGUISTIC EDUCATION)', 'girl_enrollment': 12}, {'long_name': 'BACHELOR OF ARTS (NZEMA EDUCATION)', 'girl_enrollment': 12}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MARKETING)', 'girl_enrollment': 12}, {'long_name': 'MASTER OF ARTS (DEVELOPMENT COMMUNICATION)', 'girl_enrollment': 12}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RESOURCE MANAGEMENT)', 'girl_enrollment': 12}, {'long_name': 'Diploma In Education (Competency Based Training - CBT)', 'girl_enrollment': 12}, {'long_name': 'Bachelor Of Science (Marketing) - W', 'girl_enrollment': 12}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - W', 'girl_enrollment': 12}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'girl_enrollment': 12}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - W', 'girl_enrollment': 12}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - W', 'girl_enrollment': 12}, {'long_name': 'Doctor Of Philosophy In Educational Leadership', 'girl_enrollment': 12}, {'long_name': 'BACHELOR OF ARTS (DAGAARE WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 11}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - W', 'girl_enrollment': 11}, {'long_name': 'Bachelor Of Science (Mathematics Education) - M', 'girl_enrollment': 11}, {'long_name': 'Master Of Philosophy In Accounting', 'girl_enrollment': 11}, {'long_name': 'BACHELOR OF ARTS (GURUNE EDUCATION)', 'girl_enrollment': 10}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 10}, {'long_name': 'BACHELOR OF SCIENCE (SPORTS COACHING)', 'girl_enrollment': 10}, {'long_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL SCIENCE EDUCATION)', 'girl_enrollment': 10}, {'long_name': 'Diploma In Education - TM', 'girl_enrollment': 10}, {'long_name': 'Diploma In Automotive Engineering Technology', 'girl_enrollment': 10}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - E', 'girl_enrollment': 10}, {'long_name': 'Bachelor Of Science (Civil Engineering)', 'girl_enrollment': 10}, {'long_name': 'Master Of Philosophy In Crop Science', 'girl_enrollment': 10}, {'long_name': 'MASTER OF PHILOSOPHY (ASSESSMENT, MEASUREMENT AND EVALUATION)', 'girl_enrollment': 10}, {'long_name': 'Bachelor Of Science (Wood Technology Education)', 'girl_enrollment': 9}, {'long_name': 'MASTER OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'girl_enrollment': 9}, {'long_name': 'BACHELOR OF ARTS (KASEM EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'Post Diploma Bachelor Of Science (Management Education)', 'girl_enrollment': 9}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'BACHELOR OF ARTS (GA WITH FRENCH EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'MASTER OF PHILOSOPHY (HISTORY EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'MASTER OF PHILOSOPHY (COMMUNICATION INSTRUCTION)', 'girl_enrollment': 9}, {'long_name': 'DOCTOR OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'Master Of Philosophy In Mathematics Education', 'girl_enrollment': 9}, {'long_name': 'MASTER OF PHILOSOPHY (INTEGRATED SCIENCE EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 8}, {'long_name': 'MASTER OF PHILOSOPHY (ARTS AND CULTURE)', 'girl_enrollment': 8}, {'long_name': 'DIPLOMA (EDUCATION)', 'girl_enrollment': 8}, {'long_name': 'MASTER OF PHILOSOPHY (THEATRE ARTS)', 'girl_enrollment': 8}, {'long_name': 'BACHELOR OF ARTS (SISAALI EDUCATION)', 'girl_enrollment': 8}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'girl_enrollment': 8}, {'long_name': 'Bachelor Of Science (Wood Technology Education) - S', 'girl_enrollment': 8}, {'long_name': 'Master Of Education In Science Education', 'girl_enrollment': 8}, {'long_name': 'Master Of Philosophy In Business Management - W', 'girl_enrollment': 8}, {'long_name': 'MASTER OF PHILOSOPHY (ENVIRONMENTAL SCIENCE)', 'girl_enrollment': 8}, {'long_name': 'MASTER OF SCIENCE (ECONOMICS EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'MASTER OF PHILOSOPHY (JOURNALISM AND MEDIA ST)', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (KUSAAL EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (GA WITH GERMAN EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'MASTER OF PHILOSOPHY (ACCOUNTING)', 'girl_enrollment': 7}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education)', 'girl_enrollment': 7}, {'long_name': 'Bachelor Of Science (Mathematics Education) - S', 'girl_enrollment': 7}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship)', 'girl_enrollment': 7}, {'long_name': 'Master Of Education In Mathematics Education', 'girl_enrollment': 7}, {'long_name': 'Master Of Science In Information Technology Education - W', 'girl_enrollment': 7}, {'long_name': 'Master Of Philosophy In Construction Technology', 'girl_enrollment': 7}, {'long_name': 'Master Of Philosophy In Educational Leadership (Top-Up)', 'girl_enrollment': 7}, {'long_name': 'DOCTOR OF PHILOSOPHY (FRENCH)', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH FRENCH EDUCATION)', 'girl_enrollment': 6}, {'long_name': 'Bachelor Of Science (Computerised Accounting)', 'girl_enrollment': 6}, {'long_name': 'BACHELOR OF FINE ART (ANIMATION)', 'girl_enrollment': 6}, {'long_name': 'Master Of Education In Agriculture', 'girl_enrollment': 6}, {'long_name': 'Master Of Philosophy In Agronomy', 'girl_enrollment': 6}, {'long_name': 'Master Of Philosophy In Science Education', 'girl_enrollment': 6}, {'long_name': 'Master Of Philosophy In Biology', 'girl_enrollment': 6}, {'long_name': 'Bachelor Of Arts (Arabic With English Education)', 'girl_enrollment': 5}, {'long_name': 'Master Of Technology In Construction Management', 'girl_enrollment': 5}, {'long_name': 'MASTER OF ARTS (ART EDUCATION)', 'girl_enrollment': 5}, {'long_name': 'MASTER OF EDUCATION (POLITICAL SCIENCE EDUCATION)', 'girl_enrollment': 5}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH FRENCH EDUCATION)', 'girl_enrollment': 5}, {'long_name': 'MASTER OF EDUCATION (BIOLOGY)', 'girl_enrollment': 5}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS) BY DISTANCE', 'girl_enrollment': 5}, {'long_name': 'MASTER OF EDUCATION (SCIENCE EDUCATION)', 'girl_enrollment': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology Education)', 'girl_enrollment': 5}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'girl_enrollment': 5}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - E', 'girl_enrollment': 5}, {'long_name': 'Master Of Philosophy In Construction Management (Top Up)', 'girl_enrollment': 5}, {'long_name': 'Master Of Philosophy In Wood Science And Technology', 'girl_enrollment': 5}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up)', 'girl_enrollment': 5}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up)', 'girl_enrollment': 5}, {'long_name': 'Master Of Philosophy In Information Technology - W (Top-Up)', 'girl_enrollment': 5}, {'long_name': 'MASTER OF PHILOSOPHY (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'girl_enrollment': 5}, {'long_name': 'Master Of Technology In Construction Technology', 'girl_enrollment': 4}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH FRENCH EDUCATION)', 'girl_enrollment': 4}, {'long_name': 'MASTER OF ARTS (HISTORY EDUCATION)', 'girl_enrollment': 4}, {'long_name': 'Master Of Philosophy In Construction Technology (Top-Up)', 'girl_enrollment': 4}, {'long_name': 'MASTER OF PHILOSOPHY (HOME ECONOMICS)', 'girl_enrollment': 4}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 4}, {'long_name': 'DOCTOR OF PHILOSOPHY (MUSIC EDUCATION)', 'girl_enrollment': 4}, {'long_name': 'MASTER OF PHILOSOPHY (ENTREPRENEURSHIP AND INNOVATION)', 'girl_enrollment': 4}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 4}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'girl_enrollment': 4}, {'long_name': 'Bachelor Of Science (Marketing) - E', 'girl_enrollment': 4}, {'long_name': 'Master Of Technology In Electrical And Electronics Engineering', 'girl_enrollment': 4}, {'long_name': 'Master Of Technology In Mechanical Technology', 'girl_enrollment': 4}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MANAGEMENT INFORMATION SYSTEMS)', 'girl_enrollment': 4}, {'long_name': 'Master Of Philosophy In Information Technology', 'girl_enrollment': 4}, {'long_name': 'MASTER OF EDUCATION (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'girl_enrollment': 3}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH GERMAN EDUCATION)', 'girl_enrollment': 3}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND DAGAARE EDUCATION)', 'girl_enrollment': 3}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT FINANCE)', 'girl_enrollment': 3}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL NEEDS EDUCATION)', 'girl_enrollment': 3}, {'long_name': 'MASTER OF PHILOSOPHY (FINANCE)', 'girl_enrollment': 3}, {'long_name': 'Diploma In Education - CP', 'girl_enrollment': 3}, {'long_name': 'Diploma In Mechanical Engineering Technology', 'girl_enrollment': 3}, {'long_name': 'Diploma In Construction Technology', 'girl_enrollment': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'girl_enrollment': 3}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - S', 'girl_enrollment': 3}, {'long_name': 'Bachelor Of Science In Administration (Business Information Systems)', 'girl_enrollment': 3}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education) - S', 'girl_enrollment': 3}, {'long_name': 'Bachelor Of Arts (English With French Education)', 'girl_enrollment': 3}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering', 'girl_enrollment': 3}, {'long_name': 'Master Of Philosophy In Mathematics Education - W', 'girl_enrollment': 3}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up) - W', 'girl_enrollment': 3}, {'long_name': 'Master Of Philosophy In Biology Education', 'girl_enrollment': 3}, {'long_name': 'Master Of Philosophy In Teaching And Learning', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH GERMAN EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'Doctor Of Philosophy In Construction Technology', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND DANGME EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'MASTER OF FINE ARTS (THEATRE ARTS)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ART (LINGUISTIC AND EWE EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'MASTER OF SCIENCE (DEVELOPMENT FINANCE)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH FRENCH EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'Diploma In Business Administration (Accounting) - W', 'girl_enrollment': 2}, {'long_name': 'Diploma In Management Education', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology Education) - S', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Executive Office Administration)', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Agricultural Science Education)', 'girl_enrollment': 2}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - E', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - E', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing And Entrepreneurship) - W', 'girl_enrollment': 2}, {'long_name': 'Post Diploama Bachelor Of Science (Computerised Accounting)', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'girl_enrollment': 2}, {'long_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'girl_enrollment': 2}, {'long_name': 'Bachelor Of Science (Marketing) - R', 'girl_enrollment': 2}, {'long_name': 'Bachelor Of Science (Agriculture Engineering, Technology And Innovation Education)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH GERMAN EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH FRENCH EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GA EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'Master Of Philosophy In Wood Science And Technology (Top-Up)', 'girl_enrollment': 2}, {'long_name': 'Master Of Philosophy In Mathematics Education (Top-Up) - W', 'girl_enrollment': 2}, {'long_name': 'Master Of Philosophy In Accounting - W', 'girl_enrollment': 2}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up) - W', 'girl_enrollment': 2}, {'long_name': 'Master Of Philosophy In Information Technology - W', 'girl_enrollment': 2}, {'long_name': 'MASTER OF SCIENCE (BIOLOGY)', 'girl_enrollment': 2}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICS EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'Doctor Of Philosophy In Construction Management', 'girl_enrollment': 2}, {'long_name': 'Doctor Of Philosophy In Crop Science', 'girl_enrollment': 2}, {'long_name': 'Doctor Of Philosophy In Mathematics Education', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH FRENCH EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'MASTER OF PHILOSOPHY COMMUNICATION AND MEDIA STUDIES   (COMMUNICATION SKILLS)', 'girl_enrollment': 1}, {'long_name': 'Doctor Of Philosophy In Wood Science And Technology', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH GERMAN EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GONJA EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND KASEM EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH FRENCH EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'DIPLOMA (SIGN LANGUAGE)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ART (SISAALI EDUCATION WITH FRENCH EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'MASTER OF EDUCATION (ASSESSMENT IN SPECIAL EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'MASTER OF EDUCATION (COMPUTER EDUCATION AND TECHNOLOGY)', 'girl_enrollment': 1}, {'long_name': 'Diploma In Education - TK', 'girl_enrollment': 1}, {'long_name': 'Diploma In Architecture And Digital Construction', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education)', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Education (Early Grade) - K', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - R', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - W', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) -- R', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Banking And Finance) - W', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S (Top-Up)', 'girl_enrollment': 1}, {'long_name': 'Bachelor Of Science (Mechanical Engineering Technology Education)', 'girl_enrollment': 1}, {'long_name': 'Bachelor Of Science (Wood Technology With Education)', 'girl_enrollment': 1}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - E', 'girl_enrollment': 1}, {'long_name': 'Bachelor Of Science (Plumbing, And Gas Technology)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH GERMAN EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH FRENCH EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH GERMAN EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (GERMAN EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'MASTER OF EDUCATION (ENGLISH EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'Master Of Philosophy In Automotive Engineering Technology', 'girl_enrollment': 1}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering (Top-Up)', 'girl_enrollment': 1}, {'long_name': 'Master Of Philosophy In Mechanical Engineering Technology (Top-Up)', 'girl_enrollment': 1}, {'long_name': 'Master Of Philosophy In Chemistry Education', 'girl_enrollment': 1}]
2025-08-08 10:05:00,535 - root - INFO - [{'start_year': 2022, 'female_enrollment': 2312}, {'start_year': 2024, 'female_enrollment': 120}, {'start_year': 2025, 'female_enrollment': 122}]
2025-08-08 10:05:00,535 - root - INFO - [{'sex': '', 'total_students': 8205, 'retained_students': 8205, 'retention_rate': 100.0}, {'sex': 'M', 'total_students': 118671, 'retained_students': 118671, 'retention_rate': 100.0}, {'sex': 'F', 'total_students': 114258, 'retained_students': 114258, 'retention_rate': 100.0}]
2025-08-08 10:05:00,535 - root - INFO - 'No results'
2025-08-08 10:05:00,535 - root - INFO - 'No results'
2025-08-08 10:05:00,535 - root - INFO - 'No results'
2025-08-08 10:05:00,536 - root - INFO - 'No results'
2025-08-08 10:05:00,536 - root - INFO - 'No results'
2025-08-08 10:05:00,536 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 10:05:11,485 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:11,490 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 10:05:23,569 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:23,575 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:23,575 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 10:05:23,576 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:23,576 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many girls are at ITC University?'
2025-08-08 10:05:23,576 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 10:05:23,576 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 10:05:23,576 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 10:05:23,576 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 10:05:23,576 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total enrollment of students at ITC University?...
2025-08-08 10:05:23,576 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total enrollment of students at ITC University is 192,627....
2025-08-08 10:05:23,576 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 10:05:23,576 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'total_enrollment': 192627}]...
2025-08-08 10:05:23,576 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_student_enrollment
2025-08-08 10:05:23,576 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 10:05:23,576 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 10:05:23,576 - celery.redirected - WARNING - [{'total_enrollment': 192627}]
2025-08-08 10:05:23,576 - celery.redirected - WARNING - ================================= 
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 10:05:23,577 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:23,577 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 10:05:23,577 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many girls are at ITC University?'
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 10:05:23,577 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What percentage of the total student population at ITC University are girls?...
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no available results for the percentage of girls in the total student popu...
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: percentage_of_girls_in_student_population
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 10:05:23,577 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 10:05:23,577 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:23,578 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 10:05:23,578 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:23,578 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many girls are at ITC University?'
2025-08-08 10:05:23,578 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 10:05:23,578 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 10:05:23,578 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 10:05:23,578 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 10:05:23,578 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the number of girls at ITC University compare to the number of boys?...
2025-08-08 10:05:23,578 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, there are 87,965 girls and 96,457 boys enrolled. This indicates that the number o...
2025-08-08 10:05:23,578 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 10:05:23,578 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': '', 'number_of_students': 8205}, {'sex': 'M', 'number_of_students': 96457}, {'sex': 'F', 'n...
2025-08-08 10:05:23,578 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_gender_distribution_itc_university
2025-08-08 10:05:23,579 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 10:05:23,579 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 10:05:23,579 - celery.redirected - WARNING - [{'sex': '', 'number_of_students': 8205}, {'sex': 'M', 'number_of_students': 96457}, {'sex': 'F', 'number_of_students': 87965}]
2025-08-08 10:05:23,579 - celery.redirected - WARNING - ================================= 
2025-08-08 10:05:23,579 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 10:05:23,579 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:23,579 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 10:05:23,579 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:23,579 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many girls are at ITC University?'
2025-08-08 10:05:23,579 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 10:05:23,579 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 3
2025-08-08 10:05:23,579 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 10:05:23,579 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 10:05:23,579 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses have the highest enrollment of girls at ITC University?...
2025-08-08 10:05:23,579 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-08 10:05:23,579 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 10:05:23,579 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 2:
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses have the highest enrollment of girls at ITC University?...
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 3:
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses have the highest enrollment of girls at ITC University?...
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The programs with the highest enrollment of girls at ITC University are as follows: 1) Bachelor of E...
2025-08-08 10:05:23,580 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-08 10:05:23,581 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'girl_enrollment': 9242}, {'long_name':...
2025-08-08 10:05:23,581 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: highest_girl_enrollment_programs
2025-08-08 10:05:23,581 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 10:05:23,581 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 10:05:23,582 - celery.redirected - WARNING - [{'long_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'girl_enrollment': 9242}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE)', 'girl_enrollment': 6867}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY)', 'girl_enrollment': 6378}, {'long_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'girl_enrollment': 5947}, {'long_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'girl_enrollment': 4587}, {'long_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'girl_enrollment': 4055}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'girl_enrollment': 4002}, {'long_name': 'BACHELOR OF ARTS (ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 2125}, {'long_name': 'BACHELOR OF ART (SOCIAL STUDIES EDUCATION)', 'girl_enrollment': 2054}, {'long_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'girl_enrollment': 1977}, {'long_name': 'BACHELOR OF ARTS (POLITICAL SCIENCE EDUCATION)', 'girl_enrollment': 1713}, {'long_name': 'DIPLOMA IN EARLY GRADE', 'girl_enrollment': 1555}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - R', 'girl_enrollment': 1437}, {'long_name': 'BACHELOR OF SCIENCE (FOOD AND NUTRITION EDUCATION)', 'girl_enrollment': 1250}, {'long_name': 'BACHELOR OF SCIENCE (MANAGEMENT EDUCATION)', 'girl_enrollment': 1238}, {'long_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'girl_enrollment': 1204}, {'long_name': 'Bachelor Of Science (Catering And Hospitality Education) - S', 'girl_enrollment': 1112}, {'long_name': 'Diploma In Education', 'girl_enrollment': 1086}, {'long_name': 'BACHELOR OF ARTS (SOCIAL STUDIES EDUCATION)', 'girl_enrollment': 1025}, {'long_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'girl_enrollment': 957}, {'long_name': 'BACHELOR OF ARTS (FRENCH EDUCATION)', 'girl_enrollment': 917}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS EDUCATION)', 'girl_enrollment': 872}, {'long_name': 'DIPLOMA IN BASIC EDUCATION', 'girl_enrollment': 843}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED HOME ECONOMICS EDUCATION)', 'girl_enrollment': 777}, {'long_name': 'Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'girl_enrollment': 774}, {'long_name': 'BACHELOR OF ARTS (GEOGRAPHY EDUCATION)', 'girl_enrollment': 770}, {'long_name': 'BACHELOR OF ARTS (ENGLISH EDUCATION)', 'girl_enrollment': 736}, {'long_name': 'BACHELOR OF ARTS (EWE EDUCATION)', 'girl_enrollment': 726}, {'long_name': 'BACHELOR OF SCIENCE (ACCOUNTING EDUCATION)', 'girl_enrollment': 715}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - R', 'girl_enrollment': 663}, {'long_name': 'Bachelor Of Education (Early Grade) - M', 'girl_enrollment': 662}, {'long_name': 'Bachelor Of Science (Information Technology Education)', 'girl_enrollment': 610}, {'long_name': 'BACHELOR OF SCIENCE (HEALTH ADMINISTRATION EDUCATION)', 'girl_enrollment': 604}, {'long_name': 'BACHELOR OF ARTS (ART EDUCATION)', 'girl_enrollment': 585}, {'long_name': 'BACHELOR OF ARTS (FASHION DESIGN AND TEXTILE EDUCATION)', 'girl_enrollment': 582}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION AND MANAGEMENT) BY DISTANCE', 'girl_enrollment': 576}, {'long_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL HEALTH AND SANITATION EDUCATION)', 'girl_enrollment': 555}, {'long_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'girl_enrollment': 552}, {'long_name': 'BACHELOR OF EDUCATION (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'girl_enrollment': 548}, {'long_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - S', 'girl_enrollment': 534}, {'long_name': 'BACHELOR OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'girl_enrollment': 534}, {'long_name': 'BACHELOR OF ARTS (TWI EDUCATION)', 'girl_enrollment': 493}, {'long_name': 'BACHELOR OF SCIENCE (HOME ECONOMICS EDUCATION)', 'girl_enrollment': 465}, {'long_name': 'Bachelor Of Arts (Economics Education)', 'girl_enrollment': 461}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL EDUCATION)', 'girl_enrollment': 443}, {'long_name': 'DIPLOMA (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'girl_enrollment': 437}, {'long_name': 'Bachelor Of Arts (Social Studies With Economics Education)', 'girl_enrollment': 426}, {'long_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'girl_enrollment': 392}, {'long_name': 'Bachelor Of Science (Biological Sciences Education)', 'girl_enrollment': 385}, {'long_name': 'BACHELOR OF SCIENCE (INTEGRATED SCIENCE EDUCATION)', 'girl_enrollment': 367}, {'long_name': 'BACHELOR OF ARTS (HISTORY EDUCATION)', 'girl_enrollment': 358}, {'long_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'girl_enrollment': 358}, {'long_name': 'Master Of Arts In Educational Leadership', 'girl_enrollment': 321}, {'long_name': 'BACHELOR OF SCIENCE (AGRICULTURAL SCIENCE EDUCATION)', 'girl_enrollment': 296}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'girl_enrollment': 295}, {'long_name': 'BACHELOR OF ARTS (GRAPHIC DESIGN)', 'girl_enrollment': 289}, {'long_name': 'Bachelor Of Education (Junior High)', 'girl_enrollment': 281}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICAL EDUCATION)', 'girl_enrollment': 280}, {'long_name': 'BACHELOR OF ARTS (TWI WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 255}, {'long_name': 'MASTER OF PHILOSOPHY (EARLY CHILDHOOD EDUCATION)', 'girl_enrollment': 229}, {'long_name': 'BACHELOR  OF ARTS (EWE WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 227}, {'long_name': 'BACHELOR OF SCIENCE (BIOLOGY EDUCATION)', 'girl_enrollment': 220}, {'long_name': 'Bachelor Of Business Administration (Management) - E', 'girl_enrollment': 213}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND ENGLISH EDUCATION)', 'girl_enrollment': 210}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY) BY DISTANCE', 'girl_enrollment': 207}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education) - S', 'girl_enrollment': 204}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 202}, {'long_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'girl_enrollment': 191}, {'long_name': 'MASTER OF PHILOSOPHY (ENGLISH LANGUAGE)', 'girl_enrollment': 190}, {'long_name': 'Diploma In Business Administration (Management)', 'girl_enrollment': 188}, {'long_name': 'MASTER OF PHILOSOPHY (GHANAIAN LANGUAGE STUDIES)', 'girl_enrollment': 184}, {'long_name': 'BACHELOR OF ARTS (COMMUNICATION STUDIES)', 'girl_enrollment': 183}, {'long_name': 'Master Of Technology Education In Catering And Hospitality', 'girl_enrollment': 172}, {'long_name': 'MASTER OF PHILOSOPHY (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'girl_enrollment': 159}, {'long_name': 'BACHELOR OF ARTS (FANTE EDUCATION)', 'girl_enrollment': 154}, {'long_name': 'BACHELOR OF ARTS (THEATRE ARTS)', 'girl_enrollment': 151}, {'long_name': 'MASTER OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'girl_enrollment': 149}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'girl_enrollment': 144}, {'long_name': 'MASTER OF EDUCATION (EARLY CHILDHOOD EDUCATION)', 'girl_enrollment': 134}, {'long_name': 'DIPLOMA IN MANAGEMENT STUDIES', 'girl_enrollment': 134}, {'long_name': 'MASTER OF PHILOSOPHY (FOOD AND NUTRITION)', 'girl_enrollment': 133}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - W', 'girl_enrollment': 133}, {'long_name': 'Bachelor Of Education (Early Grade) - K', 'girl_enrollment': 130}, {'long_name': 'Bachelor Of Arts (Economics With Social Studies Education)', 'girl_enrollment': 128}, {'long_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'girl_enrollment': 125}, {'long_name': 'BACHELOR OF ARTS (MUSIC EDUCATION)', 'girl_enrollment': 121}, {'long_name': 'Master Of Philosophy In Catering And Hospitality', 'girl_enrollment': 109}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (MARKETING AND ENTREPRENEURSHIP)', 'girl_enrollment': 108}, {'long_name': 'Bachelor Of Business Administration (Management) - W', 'girl_enrollment': 105}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - R', 'girl_enrollment': 104}, {'long_name': 'DIPLOMA (TEXTILES AND FASHION)', 'girl_enrollment': 104}, {'long_name': 'DIPLOMA (EARLY GRADE)', 'girl_enrollment': 102}, {'long_name': 'MASTER OF PHILOSOPHY (BASIC EDUCATION)', 'girl_enrollment': 102}, {'long_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - S', 'girl_enrollment': 102}, {'long_name': 'MASTER OF EDUCATION (HOME ECONOMICS)', 'girl_enrollment': 100}, {'long_name': 'MASTER OF PHILOSOPHY (SPECIAL EDUCATION)', 'girl_enrollment': 98}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'girl_enrollment': 98}, {'long_name': 'DIPLOMA (ART)', 'girl_enrollment': 96}, {'long_name': 'BACHELOR OF ARTS (STRATEGIC COMMUNICATION)', 'girl_enrollment': 96}, {'long_name': 'MASTER OF PHILOSOPHY IN SOCIAL STUDIES', 'girl_enrollment': 95}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND TWI EDUCATION)', 'girl_enrollment': 94}, {'long_name': 'DIPLOMA (BASIC EDUCATION)', 'girl_enrollment': 94}, {'long_name': 'BACHELOR OF SCIENCE (FAMILY LIFE MANAGEMENT EDUCATION)', 'girl_enrollment': 93}, {'long_name': 'Master Of Business Administration (Human Resource Management And Organisational Behaviour ) - Weekend', 'girl_enrollment': 92}, {'long_name': 'MASTER OF PHILOSOPHY (SCIENCE EDUCATION)', 'girl_enrollment': 92}, {'long_name': 'Bachelor Of Science (Chemistry Education)', 'girl_enrollment': 88}, {'long_name': 'MASTER OF PHILOSOPHY (ART EDUCATION)', 'girl_enrollment': 88}, {'long_name': 'Bachelor Of Business Administration (Secretarial Education)', 'girl_enrollment': 86}, {'long_name': 'CERTIFICATE (PRE-SCHOOL EDUCATION)', 'girl_enrollment': 84}, {'long_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'girl_enrollment': 77}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FRENCH EDUCATION)', 'girl_enrollment': 77}, {'long_name': 'Bachelor Of Science (Agribusiness Management And Entrepreneurship Education)', 'girl_enrollment': 77}, {'long_name': 'DIPLOMA (THEATRE ARTS)', 'girl_enrollment': 76}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'girl_enrollment': 76}, {'long_name': 'MASTER OF PHILOSOPHY (FRENCH)', 'girl_enrollment': 74}, {'long_name': 'MASTER OF ARTS (ENGLISH LANGUAGE)', 'girl_enrollment': 71}, {'long_name': 'Bachelor Of Science In Administration (Accounting)', 'girl_enrollment': 70}, {'long_name': 'Diploma In Catering And Hospitality', 'girl_enrollment': 66}, {'long_name': 'Bachelor Of Science (Information Technology Education) - W', 'girl_enrollment': 63}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES)', 'girl_enrollment': 61}, {'long_name': 'BACHELOR OF ARTS (RELIGIONS AND MORAL STUDIES EDUCATION)', 'girl_enrollment': 58}, {'long_name': 'MASTER OF EDUCATION (INSTITUTIONAL MENTORSHIP AND SUPERVISION)', 'girl_enrollment': 57}, {'long_name': 'BACHELOR ARTS (GA WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 57}, {'long_name': 'DIPLOMA (FRENCH EDUCATION)', 'girl_enrollment': 56}, {'long_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'girl_enrollment': 56}, {'long_name': 'BACHELOR OF ARTS (TWI WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 54}, {'long_name': 'MASTER OF EDUCATION (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'girl_enrollment': 53}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles', 'girl_enrollment': 53}, {'long_name': 'Master Of Business Administration (Accounting)', 'girl_enrollment': 52}, {'long_name': 'BACHELOR OF MUSIC', 'girl_enrollment': 51}, {'long_name': 'Post Diploma Bachelor Of Science (Environmental Health And Sanitation Education)', 'girl_enrollment': 50}, {'long_name': 'Master Of Philosophy In Catering And Hospitality (Top-Up)', 'girl_enrollment': 49}, {'long_name': 'MASTER OF ARTS (STRATEGIC COMMUNICATION)', 'girl_enrollment': 48}, {'long_name': 'DOCTOR OF PHILOSOPHY (SCIENCE EDUCATION)', 'girl_enrollment': 48}, {'long_name': 'MASTER OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'girl_enrollment': 47}, {'long_name': 'BACHELOR OF ARTS (EWE WITH FRENCH EDUCATION)', 'girl_enrollment': 46}, {'long_name': 'Bachelor Of Arts (Arabic Education)', 'girl_enrollment': 44}, {'long_name': 'DIPLOMA (GRAPHIC DESIGN)', 'girl_enrollment': 43}, {'long_name': 'BACHELOR OF ART (DANGME WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 43}, {'long_name': 'BACHELOR OF ARTS (CREATIVE ARTS AND DESIGN EDUCATION)', 'girl_enrollment': 43}, {'long_name': 'BACHELOR OF ARTS (DANGME EDUCATION)', 'girl_enrollment': 42}, {'long_name': 'DOCTOR OF PHILOSOPHY (APPLIED LINGUISTICS)', 'girl_enrollment': 42}, {'long_name': 'Bachelor Of Science (Natural Resources Management And Education)', 'girl_enrollment': 42}, {'long_name': 'BACHELOR OF SCIENCE (MATHEMATICS WITH ECONOMICS EDUCATION)', 'girl_enrollment': 41}, {'long_name': 'Diploma In Education - KS', 'girl_enrollment': 41}, {'long_name': 'Bachelor Of Arts (French With English Education)', 'girl_enrollment': 41}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - R', 'girl_enrollment': 40}, {'long_name': 'Master Of Technology Education In Fashion Design And Textile', 'girl_enrollment': 39}, {'long_name': 'MASTER OF ARTS (GHANAIAN LANGUAGES EDUCATION)', 'girl_enrollment': 39}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (FINANCE)', 'girl_enrollment': 39}, {'long_name': 'DIPLOMA IN ACCOUNTING', 'girl_enrollment': 38}, {'long_name': 'BACHELOR OF ARTS (EWE WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 38}, {'long_name': 'Diploma In Environmental Health And Sanitation Education', 'girl_enrollment': 38}, {'long_name': 'MASTER OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'girl_enrollment': 37}, {'long_name': 'Diploma In Fashion Design And Textiles', 'girl_enrollment': 37}, {'long_name': 'Post Diploma Bachelor Of Science In Environmental Health And Sanitation Education - S', 'girl_enrollment': 36}, {'long_name': 'DIPLOMA IN MUSIC', 'girl_enrollment': 35}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - R', 'girl_enrollment': 34}, {'long_name': 'Master Of Philosophy In Educational Leadership', 'girl_enrollment': 34}, {'long_name': 'MASTER OF PHILOSOPHY (CLOTHING AND TEXTILES)', 'girl_enrollment': 33}, {'long_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education)', 'girl_enrollment': 33}, {'long_name': 'Bachelor Of Business Administration (Executive Office Administration)', 'girl_enrollment': 33}, {'long_name': 'MASTER OF PHILOSOPHY (STRATEGIC COMMUNICATION)', 'girl_enrollment': 32}, {'long_name': 'DOCTOR OF PHILOSOPHY (ARTS AND CULTURE)', 'girl_enrollment': 32}, {'long_name': 'BACHELOR OF EDUCATION (PSYCHOLOGY)', 'girl_enrollment': 32}, {'long_name': 'DOCTOR OF PHILOSOPHY (EDUCATIONAL LEADERSHIP)', 'girl_enrollment': 31}, {'long_name': 'MASTER OF PHILOSOPHY (POLITICAL SCIENCE EDUCATION)', 'girl_enrollment': 31}, {'long_name': 'MASTER OF EDUCATION (GUIDANCE AND COUNSELLING)', 'girl_enrollment': 31}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT COMMUNICATION)', 'girl_enrollment': 31}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (FINANCE)', 'girl_enrollment': 31}, {'long_name': 'MASTER OF EDUCATION (SCIENCE)', 'girl_enrollment': 30}, {'long_name': 'MASTER OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'girl_enrollment': 30}, {'long_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'girl_enrollment': 30}, {'long_name': 'Bachelor Of Science In Occupational Health And Safety', 'girl_enrollment': 30}, {'long_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'girl_enrollment': 29}, {'long_name': 'Bachelor Of Business Administration (Human Resource Management) - E', 'girl_enrollment': 29}, {'long_name': 'BACHELOR OF ARTS (DAGAARE EDUCATION)', 'girl_enrollment': 28}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 28}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'girl_enrollment': 28}, {'long_name': 'BACHELOR OF SCIENCE (PHYSICS EDUCATION)', 'girl_enrollment': 27}, {'long_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BUSINESS INFORMATION SYSTEMS)', 'girl_enrollment': 27}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'girl_enrollment': 27}, {'long_name': 'MASTER OF EDUCATION IN SCHOOL SUPERVISION AND QUALITY ASSURANCE)', 'girl_enrollment': 26}, {'long_name': 'BACHELOR OF ARTS (TWI WITH GERMAN EDUCATION)', 'girl_enrollment': 26}, {'long_name': 'BACHELOR OF ARTS (EWE WITH GERMAN EDUCATION)', 'girl_enrollment': 26}, {'long_name': 'Master Of Philosophy In Fashion Design And Textiles (Top-Up)', 'girl_enrollment': 26}, {'long_name': 'MASTER OF PHILOSOPHY (TEXTILES AND FASHION)', 'girl_enrollment': 26}, {'long_name': 'DOCTOR OF PHILOSOPHY (SOCIAL STUDIES)', 'girl_enrollment': 25}, {'long_name': 'MASTER OF EDUCATION (FOOD AND NUTRITION)', 'girl_enrollment': 25}, {'long_name': 'EXECUTIVE MASTERS (CONFLICT HUMAN RIGHTS & PEACE STUDIES)', 'girl_enrollment': 25}, {'long_name': 'BACHELOR OF ARTS (DEVELOPMENT COMMUNICATION)', 'girl_enrollment': 25}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - W', 'girl_enrollment': 25}, {'long_name': 'MASTER OF EDUCATION (FRENCH)', 'girl_enrollment': 24}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'girl_enrollment': 24}, {'long_name': 'MASTER OF PHILOSOPHY (CURRICULUM & PEDAGOGIC STUDIES)', 'girl_enrollment': 24}, {'long_name': 'Diploma In Business Administration (Accounting)', 'girl_enrollment': 24}, {'long_name': 'Bachelor Of Science (Information Technology)', 'girl_enrollment': 24}, {'long_name': 'BACHELOR OF ARTS (GA EDUCATION)', 'girl_enrollment': 23}, {'long_name': 'MASTER OF PHILOSOPHY (MUSIC EDUCATION)', 'girl_enrollment': 23}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH FRENCH EDUCATION)', 'girl_enrollment': 23}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS)', 'girl_enrollment': 22}, {'long_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'girl_enrollment': 22}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH GERMAN EDUCATION)', 'girl_enrollment': 22}, {'long_name': 'BACHELOR OF ART (SISAALI WITH ENGLISH EDUCATION)', 'girl_enrollment': 22}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 21}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'girl_enrollment': 21}, {'long_name': 'MASTER OF EDUCATION (SPECIAL EDUCATION)', 'girl_enrollment': 20}, {'long_name': 'BACHELOR OF ARTS (DAGBANI EDUCATION)', 'girl_enrollment': 20}, {'long_name': 'Diploma In Business Administration (Management) - W', 'girl_enrollment': 20}, {'long_name': 'Diploma In Economics', 'girl_enrollment': 20}, {'long_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (LOGISTICS AND TRANSPORT MANAGEMENT)', 'girl_enrollment': 20}, {'long_name': 'POST GRADUATE DIPLOMA IN TEACHING AND LEARNING IN HIGHER EDUCATION', 'girl_enrollment': 19}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 19}, {'long_name': 'BACHELOR OF ARTS (GA WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 19}, {'long_name': 'DOCTOR OF PHILOSOPHY IN GHANAIAN LANGUAGES', 'girl_enrollment': 19}, {'long_name': 'MASTER OF PHILOSOPHY (FAMILY LIFE MANAGEMENT)', 'girl_enrollment': 19}, {'long_name': 'MASTER OF ARTS (COMMUNICATION INSTRUCTION)', 'girl_enrollment': 18}, {'long_name': 'DOCTOR OF PHILOSOPHY (COMMUNICATION AND MEDIA)', 'girl_enrollment': 18}, {'long_name': 'Diploma In Education - M', 'girl_enrollment': 18}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH GERMAN EDUCATION)', 'girl_enrollment': 17}, {'long_name': 'Diploma In Electrical And Electronics Engineering Technology', 'girl_enrollment': 17}, {'long_name': 'Master Of Philosophy In Business Management', 'girl_enrollment': 17}, {'long_name': 'DOCTOR OF PHILOSOPHY (ENGLISH LANGUAGE)', 'girl_enrollment': 16}, {'long_name': 'MASTER OF ARTS (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES', 'girl_enrollment': 16}, {'long_name': 'Bachelor Of Business Administration (Management) - R', 'girl_enrollment': 16}, {'long_name': 'Bachelor Of Science (Mathematics Education) - W', 'girl_enrollment': 16}, {'long_name': 'MASTER OF PHILOSOPHY (VISUAL COMMUNICATION)', 'girl_enrollment': 15}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 15}, {'long_name': 'DIPLOMA IN SPORTS COACHING', 'girl_enrollment': 15}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND FANTE EDUCATION)', 'girl_enrollment': 15}, {'long_name': 'MASTER OF PHILOSOPHY (INFORMATION AND COMMUNICATION TECHNOLOGY)', 'girl_enrollment': 15}, {'long_name': 'DOCTOR OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'girl_enrollment': 15}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGMENT & ORGANIZATION BEHAVIOUR)', 'girl_enrollment': 15}, {'long_name': 'BACHELOR OF ART (KUSAAL WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 15}, {'long_name': 'MASTER OF EDUCATION (ENGLISH LANGUAGE) BY DISTANCE', 'girl_enrollment': 15}, {'long_name': 'Diploma In Education (Junior High)', 'girl_enrollment': 15}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship) - W', 'girl_enrollment': 15}, {'long_name': 'MASTER OF EDUCATION (GEOGRAPHY)', 'girl_enrollment': 14}, {'long_name': 'MASTER OF PHILOSOPHY (ECONOMICS)', 'girl_enrollment': 14}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 14}, {'long_name': 'MASTER OF PHILOSOPHY (CHEMISTRY EDUCATION)', 'girl_enrollment': 14}, {'long_name': 'DOCTOR OF PHILOSOPHY (SPECIAL EDUCATION)', 'girl_enrollment': 14}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 14}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance)', 'girl_enrollment': 14}, {'long_name': 'BACHELOR OF ARTS (GONJA EDUCATION)', 'girl_enrollment': 13}, {'long_name': 'BACHELOR OF SCIENCE (INSURANCE AND RISK MANAGEMENT)', 'girl_enrollment': 13}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH ENGLISH EDUCATION)', 'girl_enrollment': 13}, {'long_name': 'Bachelor Of Arts (English Language Education) - M', 'girl_enrollment': 13}, {'long_name': 'Master Of Science In Information Technology Education', 'girl_enrollment': 13}, {'long_name': 'Master Of Philosophy In Environmental And Occupational Health Education', 'girl_enrollment': 13}, {'long_name': 'BACHELOR OF ARTS (TWI WITH FRENCH EDUCATION)', 'girl_enrollment': 12}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH LINGUISTIC EDUCATION)', 'girl_enrollment': 12}, {'long_name': 'BACHELOR OF ARTS (NZEMA EDUCATION)', 'girl_enrollment': 12}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MARKETING)', 'girl_enrollment': 12}, {'long_name': 'MASTER OF ARTS (DEVELOPMENT COMMUNICATION)', 'girl_enrollment': 12}, {'long_name': 'MASTER OF PHILOSOPHY (HUMAN RESOURCE MANAGEMENT)', 'girl_enrollment': 12}, {'long_name': 'Diploma In Education (Competency Based Training - CBT)', 'girl_enrollment': 12}, {'long_name': 'Bachelor Of Science (Marketing) - W', 'girl_enrollment': 12}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - W', 'girl_enrollment': 12}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'girl_enrollment': 12}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - W', 'girl_enrollment': 12}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - W', 'girl_enrollment': 12}, {'long_name': 'Doctor Of Philosophy In Educational Leadership', 'girl_enrollment': 12}, {'long_name': 'BACHELOR OF ARTS (DAGAARE WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 11}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - W', 'girl_enrollment': 11}, {'long_name': 'Bachelor Of Science (Mathematics Education) - M', 'girl_enrollment': 11}, {'long_name': 'Master Of Philosophy In Accounting', 'girl_enrollment': 11}, {'long_name': 'BACHELOR OF ARTS (GURUNE EDUCATION)', 'girl_enrollment': 10}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 10}, {'long_name': 'BACHELOR OF SCIENCE (SPORTS COACHING)', 'girl_enrollment': 10}, {'long_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL SCIENCE EDUCATION)', 'girl_enrollment': 10}, {'long_name': 'Diploma In Education - TM', 'girl_enrollment': 10}, {'long_name': 'Diploma In Automotive Engineering Technology', 'girl_enrollment': 10}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Management) - E', 'girl_enrollment': 10}, {'long_name': 'Bachelor Of Science (Civil Engineering)', 'girl_enrollment': 10}, {'long_name': 'Master Of Philosophy In Crop Science', 'girl_enrollment': 10}, {'long_name': 'MASTER OF PHILOSOPHY (ASSESSMENT, MEASUREMENT AND EVALUATION)', 'girl_enrollment': 10}, {'long_name': 'Bachelor Of Science (Wood Technology Education)', 'girl_enrollment': 9}, {'long_name': 'MASTER OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'girl_enrollment': 9}, {'long_name': 'BACHELOR OF ARTS (KASEM EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'Post Diploma Bachelor Of Science (Management Education)', 'girl_enrollment': 9}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'BACHELOR OF ARTS (GA WITH FRENCH EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'MASTER OF PHILOSOPHY (HISTORY EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'MASTER OF PHILOSOPHY (COMMUNICATION INSTRUCTION)', 'girl_enrollment': 9}, {'long_name': 'DOCTOR OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'Master Of Philosophy In Mathematics Education', 'girl_enrollment': 9}, {'long_name': 'MASTER OF PHILOSOPHY (INTEGRATED SCIENCE EDUCATION)', 'girl_enrollment': 9}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 8}, {'long_name': 'MASTER OF PHILOSOPHY (ARTS AND CULTURE)', 'girl_enrollment': 8}, {'long_name': 'DIPLOMA (EDUCATION)', 'girl_enrollment': 8}, {'long_name': 'MASTER OF PHILOSOPHY (THEATRE ARTS)', 'girl_enrollment': 8}, {'long_name': 'BACHELOR OF ARTS (SISAALI EDUCATION)', 'girl_enrollment': 8}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'girl_enrollment': 8}, {'long_name': 'Bachelor Of Science (Wood Technology Education) - S', 'girl_enrollment': 8}, {'long_name': 'Master Of Education In Science Education', 'girl_enrollment': 8}, {'long_name': 'Master Of Philosophy In Business Management - W', 'girl_enrollment': 8}, {'long_name': 'MASTER OF PHILOSOPHY (ENVIRONMENTAL SCIENCE)', 'girl_enrollment': 8}, {'long_name': 'MASTER OF SCIENCE (ECONOMICS EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'MASTER OF PHILOSOPHY (JOURNALISM AND MEDIA ST)', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (KUSAAL EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (GONJA WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (GA WITH GERMAN EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 7}, {'long_name': 'MASTER OF PHILOSOPHY (ACCOUNTING)', 'girl_enrollment': 7}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education)', 'girl_enrollment': 7}, {'long_name': 'Bachelor Of Science (Mathematics Education) - S', 'girl_enrollment': 7}, {'long_name': 'Bachelor Of Science (Marketing And Entrepreneurship)', 'girl_enrollment': 7}, {'long_name': 'Master Of Education In Mathematics Education', 'girl_enrollment': 7}, {'long_name': 'Master Of Science In Information Technology Education - W', 'girl_enrollment': 7}, {'long_name': 'Master Of Philosophy In Construction Technology', 'girl_enrollment': 7}, {'long_name': 'Master Of Philosophy In Educational Leadership (Top-Up)', 'girl_enrollment': 7}, {'long_name': 'DOCTOR OF PHILOSOPHY (FRENCH)', 'girl_enrollment': 7}, {'long_name': 'BACHELOR OF ARTS (FANTE WITH FRENCH EDUCATION)', 'girl_enrollment': 6}, {'long_name': 'Bachelor Of Science (Computerised Accounting)', 'girl_enrollment': 6}, {'long_name': 'BACHELOR OF FINE ART (ANIMATION)', 'girl_enrollment': 6}, {'long_name': 'Master Of Education In Agriculture', 'girl_enrollment': 6}, {'long_name': 'Master Of Philosophy In Agronomy', 'girl_enrollment': 6}, {'long_name': 'Master Of Philosophy In Science Education', 'girl_enrollment': 6}, {'long_name': 'Master Of Philosophy In Biology', 'girl_enrollment': 6}, {'long_name': 'Bachelor Of Arts (Arabic With English Education)', 'girl_enrollment': 5}, {'long_name': 'Master Of Technology In Construction Management', 'girl_enrollment': 5}, {'long_name': 'MASTER OF ARTS (ART EDUCATION)', 'girl_enrollment': 5}, {'long_name': 'MASTER OF EDUCATION (POLITICAL SCIENCE EDUCATION)', 'girl_enrollment': 5}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH FRENCH EDUCATION)', 'girl_enrollment': 5}, {'long_name': 'MASTER OF EDUCATION (BIOLOGY)', 'girl_enrollment': 5}, {'long_name': 'MASTER OF EDUCATION (MATHEMATICS) BY DISTANCE', 'girl_enrollment': 5}, {'long_name': 'MASTER OF EDUCATION (SCIENCE EDUCATION)', 'girl_enrollment': 5}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology Education)', 'girl_enrollment': 5}, {'long_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'girl_enrollment': 5}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - E', 'girl_enrollment': 5}, {'long_name': 'Master Of Philosophy In Construction Management (Top Up)', 'girl_enrollment': 5}, {'long_name': 'Master Of Philosophy In Wood Science And Technology', 'girl_enrollment': 5}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up)', 'girl_enrollment': 5}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up)', 'girl_enrollment': 5}, {'long_name': 'Master Of Philosophy In Information Technology - W (Top-Up)', 'girl_enrollment': 5}, {'long_name': 'MASTER OF PHILOSOPHY (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'girl_enrollment': 5}, {'long_name': 'Master Of Technology In Construction Technology', 'girl_enrollment': 4}, {'long_name': 'BACHELOR OF ART (DAGBANI WITH FRENCH EDUCATION)', 'girl_enrollment': 4}, {'long_name': 'MASTER OF ARTS (HISTORY EDUCATION)', 'girl_enrollment': 4}, {'long_name': 'Master Of Philosophy In Construction Technology (Top-Up)', 'girl_enrollment': 4}, {'long_name': 'MASTER OF PHILOSOPHY (HOME ECONOMICS)', 'girl_enrollment': 4}, {'long_name': 'BACHELOR OF ARTS (DAGBANI WITH ENGLISH LANGUAGE EDUCATION)', 'girl_enrollment': 4}, {'long_name': 'DOCTOR OF PHILOSOPHY (MUSIC EDUCATION)', 'girl_enrollment': 4}, {'long_name': 'MASTER OF PHILOSOPHY (ENTREPRENEURSHIP AND INNOVATION)', 'girl_enrollment': 4}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 4}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'girl_enrollment': 4}, {'long_name': 'Bachelor Of Science (Marketing) - E', 'girl_enrollment': 4}, {'long_name': 'Master Of Technology In Electrical And Electronics Engineering', 'girl_enrollment': 4}, {'long_name': 'Master Of Technology In Mechanical Technology', 'girl_enrollment': 4}, {'long_name': 'MASTER OF BUSINESS ADMINISTRATION (MANAGEMENT INFORMATION SYSTEMS)', 'girl_enrollment': 4}, {'long_name': 'Master Of Philosophy In Information Technology', 'girl_enrollment': 4}, {'long_name': 'MASTER OF EDUCATION (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'girl_enrollment': 3}, {'long_name': 'BACHELOR OF ARTS (DANGME WITH GERMAN EDUCATION)', 'girl_enrollment': 3}, {'long_name': 'BACHELOR OF ART (LINGUISTICS AND DAGAARE EDUCATION)', 'girl_enrollment': 3}, {'long_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT FINANCE)', 'girl_enrollment': 3}, {'long_name': 'BACHELOR OF EDUCATION (SPECIAL NEEDS EDUCATION)', 'girl_enrollment': 3}, {'long_name': 'MASTER OF PHILOSOPHY (FINANCE)', 'girl_enrollment': 3}, {'long_name': 'Diploma In Education - CP', 'girl_enrollment': 3}, {'long_name': 'Diploma In Mechanical Engineering Technology', 'girl_enrollment': 3}, {'long_name': 'Diploma In Construction Technology', 'girl_enrollment': 3}, {'long_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'girl_enrollment': 3}, {'long_name': 'Bachelor Of Science (Construction Technology Education) - S', 'girl_enrollment': 3}, {'long_name': 'Bachelor Of Science In Administration (Business Information Systems)', 'girl_enrollment': 3}, {'long_name': 'Bachelor Of Science (Mechanical Technology Education) - S', 'girl_enrollment': 3}, {'long_name': 'Bachelor Of Arts (English With French Education)', 'girl_enrollment': 3}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering', 'girl_enrollment': 3}, {'long_name': 'Master Of Philosophy In Mathematics Education - W', 'girl_enrollment': 3}, {'long_name': 'Master Of Philosophy In Business Management (Top-Up) - W', 'girl_enrollment': 3}, {'long_name': 'Master Of Philosophy In Biology Education', 'girl_enrollment': 3}, {'long_name': 'Master Of Philosophy In Teaching And Learning', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH GERMAN EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'Doctor Of Philosophy In Construction Technology', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND DANGME EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH LINGUISTICS EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'MASTER OF FINE ARTS (THEATRE ARTS)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ART (LINGUISTIC AND EWE EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'MASTER OF SCIENCE (DEVELOPMENT FINANCE)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH FRENCH EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'Diploma In Business Administration (Accounting) - W', 'girl_enrollment': 2}, {'long_name': 'Diploma In Management Education', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Construction Technology Education) - S', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Business Administration (Executive Office Administration)', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Agricultural Science Education)', 'girl_enrollment': 2}, {'long_name': 'Bachelor Of Science In Administration (Accounting) - E', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - E', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing And Entrepreneurship) - W', 'girl_enrollment': 2}, {'long_name': 'Post Diploama Bachelor Of Science (Computerised Accounting)', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'girl_enrollment': 2}, {'long_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'girl_enrollment': 2}, {'long_name': 'Bachelor Of Science (Marketing) - R', 'girl_enrollment': 2}, {'long_name': 'Bachelor Of Science (Agriculture Engineering, Technology And Innovation Education)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (GURUNE WITH GERMAN EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH FRENCH EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GA EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'Master Of Philosophy In Wood Science And Technology (Top-Up)', 'girl_enrollment': 2}, {'long_name': 'Master Of Philosophy In Mathematics Education (Top-Up) - W', 'girl_enrollment': 2}, {'long_name': 'Master Of Philosophy In Accounting - W', 'girl_enrollment': 2}, {'long_name': 'Master Of Philosophy In Accounting (Top-Up) - W', 'girl_enrollment': 2}, {'long_name': 'Master Of Philosophy In Information Technology - W', 'girl_enrollment': 2}, {'long_name': 'MASTER OF SCIENCE (BIOLOGY)', 'girl_enrollment': 2}, {'long_name': 'MASTER OF PHILOSOPHY (PHYSICS EDUCATION)', 'girl_enrollment': 2}, {'long_name': 'Doctor Of Philosophy In Construction Management', 'girl_enrollment': 2}, {'long_name': 'Doctor Of Philosophy In Crop Science', 'girl_enrollment': 2}, {'long_name': 'Doctor Of Philosophy In Mathematics Education', 'girl_enrollment': 2}, {'long_name': 'Post Diploma Bachelor Of Science (Information Technology)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (NZEMA WITH FRENCH EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'MASTER OF PHILOSOPHY COMMUNICATION AND MEDIA STUDIES   (COMMUNICATION SKILLS)', 'girl_enrollment': 1}, {'long_name': 'Doctor Of Philosophy In Wood Science And Technology', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ART (DAGAARE WITH GERMAN EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND GONJA EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (LINGUISTICS AND KASEM EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (KUSAAL WITH FRENCH EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'DIPLOMA (SIGN LANGUAGE)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ART (SISAALI EDUCATION WITH FRENCH EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'MASTER OF EDUCATION (ASSESSMENT IN SPECIAL EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'MASTER OF EDUCATION (COMPUTER EDUCATION AND TECHNOLOGY)', 'girl_enrollment': 1}, {'long_name': 'Diploma In Education - TK', 'girl_enrollment': 1}, {'long_name': 'Diploma In Architecture And Digital Construction', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education)', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Education (Early Grade) - K', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - R', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Marketing) - W', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) -- R', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science In Administration (Banking And Finance) - W', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W', 'girl_enrollment': 1}, {'long_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S (Top-Up)', 'girl_enrollment': 1}, {'long_name': 'Bachelor Of Science (Mechanical Engineering Technology Education)', 'girl_enrollment': 1}, {'long_name': 'Bachelor Of Science (Wood Technology With Education)', 'girl_enrollment': 1}, {'long_name': 'Bachelor Of Science In Administration (Banking And Finance) - E', 'girl_enrollment': 1}, {'long_name': 'Bachelor Of Science (Plumbing, And Gas Technology)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (KASEM WITH GERMAN EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH FRENCH EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (SISAALI WITH GERMAN EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'BACHELOR OF ARTS (GERMAN EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'MASTER OF EDUCATION (ENGLISH EDUCATION)', 'girl_enrollment': 1}, {'long_name': 'Master Of Philosophy In Automotive Engineering Technology', 'girl_enrollment': 1}, {'long_name': 'Master Of Philosophy In Electrical Power Systems Engineering (Top-Up)', 'girl_enrollment': 1}, {'long_name': 'Master Of Philosophy In Mechanical Engineering Technology (Top-Up)', 'girl_enrollment': 1}, {'long_name': 'Master Of Philosophy In Chemistry Education', 'girl_enrollment': 1}]
2025-08-08 10:05:23,584 - celery.redirected - WARNING - ================================= 
2025-08-08 10:05:23,584 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: highest_girl_enrollment_programs
2025-08-08 10:05:23,584 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 3 documents, 1 with data_returned=True
2025-08-08 10:05:23,585 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:23,585 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 10:05:23,585 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:23,585 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many girls are at ITC University?'
2025-08-08 10:05:23,585 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 10:05:23,585 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 10:05:23,585 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 10:05:23,585 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 10:05:23,585 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the trend in female enrollment at ITC University over the past few years?...
2025-08-08 10:05:23,585 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The trend in female enrollment at ITC University shows a significant decline over the past few years...
2025-08-08 10:05:23,585 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: graph
2025-08-08 10:05:23,585 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'start_year': 2022, 'female_enrollment': 2312}, {'start_year': 2024, 'female_enrollment': 120}, {'...
2025-08-08 10:05:23,585 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: female_enrollment_trend_itc_university
2025-08-08 10:05:23,586 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 10:05:23,586 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 10:05:23,586 - celery.redirected - WARNING - [{'start_year': 2022, 'female_enrollment': 2312}, {'start_year': 2024, 'female_enrollment': 120}, {'start_year': 2025, 'female_enrollment': 122}]
2025-08-08 10:05:23,587 - celery.redirected - WARNING - ================================= 
2025-08-08 10:05:23,587 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: female_enrollment_trend_itc_university
2025-08-08 10:05:23,587 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 10:05:23,588 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:23,588 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 10:05:23,588 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:23,588 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many girls are at ITC University?'
2025-08-08 10:05:23,588 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 10:05:23,588 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 10:05:23,589 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 10:05:23,589 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 10:05:23,589 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific scholarships or initiatives at ITC University aimed at increasing female enrollme...
2025-08-08 10:05:23,589 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Based on the available data, there are currently no specific scholarships or initiatives at ITC Univ...
2025-08-08 10:05:23,589 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 10:05:23,589 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 10:05:23,589 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: female_enrollment_scholarships_initiatives
2025-08-08 10:05:23,589 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 10:05:23,590 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 10:05:23,590 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:23,590 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 10:05:23,590 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:23,590 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many girls are at ITC University?'
2025-08-08 10:05:23,590 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 10:05:23,590 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 10:05:23,590 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 10:05:23,590 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 10:05:23,590 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What are the demographics of the girls enrolled at ITC University, such as age and nationality?...
2025-08-08 10:05:23,591 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: There are currently no records of girls enrolled at ITC University, which means we do not have any d...
2025-08-08 10:05:23,591 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 10:05:23,591 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 10:05:23,591 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_enrollment_demographics_itc_university
2025-08-08 10:05:23,591 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 10:05:23,591 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 10:05:23,591 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:23,591 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 10:05:23,591 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:23,591 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many girls are at ITC University?'
2025-08-08 10:05:23,591 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 10:05:23,591 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 10:05:23,592 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 10:05:23,592 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 10:05:23,592 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the retention rate of girls at ITC University compared to boys?...
2025-08-08 10:05:23,592 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, the retention rate for both girls and boys is 100%. This means that all students,...
2025-08-08 10:05:23,592 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 10:05:23,592 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': '', 'total_students': 8205, 'retained_students': 8205, 'retention_rate': 100.0}, {'sex': 'M...
2025-08-08 10:05:23,592 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: retention_rate_by_gender
2025-08-08 10:05:23,592 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 10:05:23,592 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 10:05:23,592 - celery.redirected - WARNING - [{'sex': '', 'total_students': 8205, 'retained_students': 8205, 'retention_rate': 100.0}, {'sex': 'M', 'total_students': 118671, 'retained_students': 118671, 'retention_rate': 100.0}, {'sex': 'F', 'total_students': 114258, 'retained_students': 114258, 'retention_rate': 100.0}]
2025-08-08 10:05:23,592 - celery.redirected - WARNING - ================================= 
2025-08-08 10:05:23,592 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 10:05:23,592 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 10
2025-08-08 10:05:23,592 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:23,592 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 10:05:23,592 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:23,592 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 10 documents
2025-08-08 10:05:23,592 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 10:05:23,592 - UPSERT_DOCS - INFO -     Content: Question: What is the total enrollment of students at ITC University?
Answer: The total enrollment o...
2025-08-08 10:05:23,592 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:36.003364+00:00', 'data_returned': True}
2025-08-08 10:05:23,592 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Content: Question: What percentage of the total student population at ITC University are girls?
Answer: It ap...
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:37.351553+00:00', 'data_returned': False}
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Content: Question: How does the number of girls at ITC University compare to the number of boys?
Answer: At I...
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:38.339355+00:00', 'data_returned': True}
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: I...
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': False}
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: I...
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': False}
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: T...
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': True, 'data_tag': 'highest_girl_enrollment_programs'}
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Content: Question: What is the trend in female enrollment at ITC University over the past few years?
Answer: ...
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:42.630832+00:00', 'data_returned': True, 'data_tag': 'female_enrollment_trend_itc_university'}
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -   📄 Doc 8:
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Content: Question: Are there specific scholarships or initiatives at ITC University aimed at increasing femal...
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:02:12.343836+00:00', 'data_returned': False}
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -   📄 Doc 9:
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Content: Question: What are the demographics of the girls enrolled at ITC University, such as age and nationa...
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:57.659330+00:00', 'data_returned': False}
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -   📄 Doc 10:
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Content: Question: What is the retention rate of girls at ITC University compared to boys?
Answer: At ITC Uni...
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:59.674310+00:00', 'data_returned': True}
2025-08-08 10:05:23,593 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 5/10
2025-08-08 10:05:23,723 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.129s]
2025-08-08 10:05:24,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 10:05:25,772 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.786s]
2025-08-08 10:05:25,773 - UPSERT_DOCS - INFO - ✅ Successfully upserted 10 documents to Elasticsearch
2025-08-08 10:05:25,773 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 8
2025-08-08 10:05:25,774 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 10:05:25,774 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:25,774 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-08 10:05:25,774 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:25,775 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/8...
2025-08-08 10:05:26,768 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:26,791 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:26,791 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 10:05:26,791 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:26,792 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University female enrollment statistics'
2025-08-08 10:05:26,792 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many girls are at ITC University?'
2025-08-08 10:05:26,792 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many girls are at ITC University?'
2025-08-08 10:05:26,922 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 10:05:26,922 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 65
2025-08-08 10:05:27,051 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 10:05:27,052 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 36
2025-08-08 10:05:27,179 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 10:05:27,179 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 10
2025-08-08 10:05:27,309 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 10:05:27,310 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 10:05:27,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 10:05:27,867 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.141s]
2025-08-08 10:05:27,868 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 10:05:27,868 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the trend in female enrollment at ITC University over the past few years?
Answer: ...
2025-08-08 10:05:27,868 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:42.630832+00:00', 'data_returned': True, 'data_tag': 'female_enrollment_trend_itc_university'}
2025-08-08 10:05:27,868 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: T...
2025-08-08 10:05:27,869 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': True, 'data_tag': 'highest_girl_enrollment_programs'}
2025-08-08 10:05:27,869 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the number of girls at ITC University compare to the number of boys?
Answer: At I...
2025-08-08 10:05:27,869 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:38.339355+00:00', 'data_returned': True}
2025-08-08 10:05:27,869 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total enrollment of students at ITC University?
Answer: The total enrollment o...
2025-08-08 10:05:27,869 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:36.003364+00:00', 'data_returned': True}
2025-08-08 10:05:27,869 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the retention rate of girls at ITC University compared to boys?
Answer: At ITC Uni...
2025-08-08 10:05:27,869 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:59.674310+00:00', 'data_returned': True}
2025-08-08 10:05:30,849 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:30,866 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 377 characters
2025-08-08 10:05:30,867 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/8...
2025-08-08 10:05:31,609 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:31,612 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:31,612 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 10:05:31,612 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:31,612 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University total enrollment statistics'
2025-08-08 10:05:31,612 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many girls are at ITC University?'
2025-08-08 10:05:31,612 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many girls are at ITC University?'
2025-08-08 10:05:31,739 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 10:05:31,739 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 65
2025-08-08 10:05:31,866 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 10:05:31,867 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 36
2025-08-08 10:05:31,994 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 10:05:31,995 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 10
2025-08-08 10:05:32,121 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 10:05:32,121 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 10:05:32,474 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 10:05:32,615 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.137s]
2025-08-08 10:05:32,615 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 10:05:32,615 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the total enrollment of students at ITC University?
Answer: The total enrollment o...
2025-08-08 10:05:32,616 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:36.003364+00:00', 'data_returned': True}
2025-08-08 10:05:32,616 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the trend in female enrollment at ITC University over the past few years?
Answer: ...
2025-08-08 10:05:32,616 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:42.630832+00:00', 'data_returned': True, 'data_tag': 'female_enrollment_trend_itc_university'}
2025-08-08 10:05:32,616 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the number of girls at ITC University compare to the number of boys?
Answer: At I...
2025-08-08 10:05:32,616 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:38.339355+00:00', 'data_returned': True}
2025-08-08 10:05:32,616 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: T...
2025-08-08 10:05:32,616 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': True, 'data_tag': 'highest_girl_enrollment_programs'}
2025-08-08 10:05:32,616 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the retention rate of girls at ITC University compared to boys?
Answer: At ITC Uni...
2025-08-08 10:05:32,616 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:59.674310+00:00', 'data_returned': True}
2025-08-08 10:05:34,174 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:34,182 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 251 characters
2025-08-08 10:05:34,183 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/8...
2025-08-08 10:05:34,960 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:34,966 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:34,966 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 10:05:34,966 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:34,966 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'highest female enrollment programs'
2025-08-08 10:05:34,966 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many girls are at ITC University?'
2025-08-08 10:05:34,966 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many girls are at ITC University?'
2025-08-08 10:05:35,094 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 10:05:35,095 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 65
2025-08-08 10:05:35,220 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-08 10:05:35,221 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 36
2025-08-08 10:05:35,347 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-08 10:05:35,347 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 10
2025-08-08 10:05:35,477 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 10:05:35,478 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 10:05:36,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 10:05:36,167 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.135s]
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: T...
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': True, 'data_tag': 'highest_girl_enrollment_programs'}
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the trend in female enrollment at ITC University over the past few years?
Answer: ...
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:42.630832+00:00', 'data_returned': True, 'data_tag': 'female_enrollment_trend_itc_university'}
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the number of girls at ITC University compare to the number of boys?
Answer: At I...
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:38.339355+00:00', 'data_returned': True}
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total enrollment of students at ITC University?
Answer: The total enrollment o...
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:36.003364+00:00', 'data_returned': True}
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the retention rate of girls at ITC University compared to boys?
Answer: At ITC Uni...
2025-08-08 10:05:36,168 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:59.674310+00:00', 'data_returned': True}
2025-08-08 10:05:40,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:40,261 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 808 characters
2025-08-08 10:05:40,262 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/8...
2025-08-08 10:05:40,993 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:40,996 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:40,997 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 10:05:40,997 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:40,997 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'female enrollment trends over time'
2025-08-08 10:05:40,997 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many girls are at ITC University?'
2025-08-08 10:05:40,997 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many girls are at ITC University?'
2025-08-08 10:05:41,126 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 10:05:41,127 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 65
2025-08-08 10:05:41,255 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 10:05:41,256 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 36
2025-08-08 10:05:41,386 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 10:05:41,386 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 10
2025-08-08 10:05:41,517 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-08 10:05:41,518 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 10:05:42,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 10:05:42,382 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.133s]
2025-08-08 10:05:42,382 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 10:05:42,382 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the trend in female enrollment at ITC University over the past few years?
Answer: ...
2025-08-08 10:05:42,382 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:42.630832+00:00', 'data_returned': True, 'data_tag': 'female_enrollment_trend_itc_university'}
2025-08-08 10:05:42,382 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: T...
2025-08-08 10:05:42,383 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': True, 'data_tag': 'highest_girl_enrollment_programs'}
2025-08-08 10:05:42,383 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the number of girls at ITC University compare to the number of boys?
Answer: At I...
2025-08-08 10:05:42,383 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:38.339355+00:00', 'data_returned': True}
2025-08-08 10:05:42,383 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the retention rate of girls at ITC University compared to boys?
Answer: At ITC Uni...
2025-08-08 10:05:42,383 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:59.674310+00:00', 'data_returned': True}
2025-08-08 10:05:42,383 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total enrollment of students at ITC University?
Answer: The total enrollment o...
2025-08-08 10:05:42,383 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:36.003364+00:00', 'data_returned': True}
2025-08-08 10:05:47,787 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:47,799 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 1136 characters
2025-08-08 10:05:47,799 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/8...
2025-08-08 10:05:49,114 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:49,118 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:49,118 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 10:05:49,118 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:49,118 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University retention rates by gender'
2025-08-08 10:05:49,118 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many girls are at ITC University?'
2025-08-08 10:05:49,118 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many girls are at ITC University?'
2025-08-08 10:05:49,245 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 10:05:49,246 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 65
2025-08-08 10:05:49,374 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 10:05:49,374 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 36
2025-08-08 10:05:49,504 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 10:05:49,505 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 10
2025-08-08 10:05:49,634 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 10:05:49,635 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 10:05:50,106 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 10:05:50,253 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.143s]
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the retention rate of girls at ITC University compared to boys?
Answer: At ITC Uni...
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:59.674310+00:00', 'data_returned': True}
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the trend in female enrollment at ITC University over the past few years?
Answer: ...
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:42.630832+00:00', 'data_returned': True, 'data_tag': 'female_enrollment_trend_itc_university'}
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the number of girls at ITC University compare to the number of boys?
Answer: At I...
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:38.339355+00:00', 'data_returned': True}
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: T...
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': True, 'data_tag': 'highest_girl_enrollment_programs'}
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total enrollment of students at ITC University?
Answer: The total enrollment o...
2025-08-08 10:05:50,254 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:36.003364+00:00', 'data_returned': True}
2025-08-08 10:05:55,953 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:55,959 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 772 characters
2025-08-08 10:05:55,959 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 6/8...
2025-08-08 10:05:56,823 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:05:56,827 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:05:56,827 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 10:05:56,827 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:05:56,828 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'gender enrollment university policies implications'
2025-08-08 10:05:56,828 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many girls are at ITC University?'
2025-08-08 10:05:56,828 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many girls are at ITC University?'
2025-08-08 10:05:56,962 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 10:05:56,963 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 65
2025-08-08 10:05:57,093 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-08 10:05:57,094 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 36
2025-08-08 10:05:57,222 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 10:05:57,223 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 10
2025-08-08 10:05:57,353 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 10:05:57,353 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 10:05:57,793 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 10:05:57,933 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.136s]
2025-08-08 10:05:57,934 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 10:05:57,934 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the trend in female enrollment at ITC University over the past few years?
Answer: ...
2025-08-08 10:05:57,934 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:42.630832+00:00', 'data_returned': True, 'data_tag': 'female_enrollment_trend_itc_university'}
2025-08-08 10:05:57,934 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: T...
2025-08-08 10:05:57,934 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': True, 'data_tag': 'highest_girl_enrollment_programs'}
2025-08-08 10:05:57,934 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the number of girls at ITC University compare to the number of boys?
Answer: At I...
2025-08-08 10:05:57,935 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:38.339355+00:00', 'data_returned': True}
2025-08-08 10:05:57,935 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the retention rate of girls at ITC University compared to boys?
Answer: At ITC Uni...
2025-08-08 10:05:57,935 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:59.674310+00:00', 'data_returned': True}
2025-08-08 10:05:57,935 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total enrollment of students at ITC University?
Answer: The total enrollment o...
2025-08-08 10:05:57,935 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:36.003364+00:00', 'data_returned': True}
2025-08-08 10:06:02,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:06:02,733 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 6 completed and processed: 1235 characters
2025-08-08 10:06:02,734 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 7/8...
2025-08-08 10:06:03,568 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:06:03,578 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:06:03,578 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 10:06:03,578 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:06:03,578 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'female enrollment ITC University findings implications'
2025-08-08 10:06:03,578 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many girls are at ITC University?'
2025-08-08 10:06:03,578 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many girls are at ITC University?'
2025-08-08 10:06:03,705 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 10:06:03,705 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 65
2025-08-08 10:06:03,841 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 10:06:03,841 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 36
2025-08-08 10:06:03,968 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 10:06:03,968 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 10
2025-08-08 10:06:04,095 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 10:06:04,095 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 10:06:04,588 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 10:06:04,728 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.137s]
2025-08-08 10:06:04,728 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 10:06:04,728 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the trend in female enrollment at ITC University over the past few years?
Answer: ...
2025-08-08 10:06:04,728 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:42.630832+00:00', 'data_returned': True, 'data_tag': 'female_enrollment_trend_itc_university'}
2025-08-08 10:06:04,728 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: T...
2025-08-08 10:06:04,728 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': True, 'data_tag': 'highest_girl_enrollment_programs'}
2025-08-08 10:06:04,728 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the number of girls at ITC University compare to the number of boys?
Answer: At I...
2025-08-08 10:06:04,728 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:38.339355+00:00', 'data_returned': True}
2025-08-08 10:06:04,728 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the retention rate of girls at ITC University compared to boys?
Answer: At ITC Uni...
2025-08-08 10:06:04,729 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:59.674310+00:00', 'data_returned': True}
2025-08-08 10:06:04,729 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total enrollment of students at ITC University?
Answer: The total enrollment o...
2025-08-08 10:06:04,729 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:36.003364+00:00', 'data_returned': True}
2025-08-08 10:06:13,995 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:06:14,053 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 7 completed and processed: 1543 characters
2025-08-08 10:06:14,053 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 8/8...
2025-08-08 10:06:14,992 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:06:15,013 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:06:15,013 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 10:06:15,013 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:06:15,013 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'interview transcripts sources'
2025-08-08 10:06:15,013 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many girls are at ITC University?'
2025-08-08 10:06:15,013 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many girls are at ITC University?'
2025-08-08 10:06:15,150 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-08 10:06:15,150 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 65
2025-08-08 10:06:15,279 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-08 10:06:15,280 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 36
2025-08-08 10:06:15,408 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 10:06:15,408 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 10
2025-08-08 10:06:15,535 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-08 10:06:15,536 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 5
2025-08-08 10:06:16,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 10:06:16,837 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.134s]
2025-08-08 10:06:16,838 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-08 10:06:16,838 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the retention rate of girls at ITC University compared to boys?
Answer: At ITC Uni...
2025-08-08 10:06:16,838 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:59.674310+00:00', 'data_returned': True}
2025-08-08 10:06:16,838 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total enrollment of students at ITC University?
Answer: The total enrollment o...
2025-08-08 10:06:16,838 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:36.003364+00:00', 'data_returned': True}
2025-08-08 10:06:16,838 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the trend in female enrollment at ITC University over the past few years?
Answer: ...
2025-08-08 10:06:16,839 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:42.630832+00:00', 'data_returned': True, 'data_tag': 'female_enrollment_trend_itc_university'}
2025-08-08 10:06:16,839 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What programs or courses have the highest enrollment of girls at ITC University?
Answer: T...
2025-08-08 10:06:16,839 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-08T10:05:00.532529+00:00', 'data_returned': True, 'data_tag': 'highest_girl_enrollment_programs'}
2025-08-08 10:06:16,839 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How does the number of girls at ITC University compare to the number of boys?
Answer: At I...
2025-08-08 10:06:16,839 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many girls are at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T10:00:38.339355+00:00', 'data_returned': True}
2025-08-08 10:06:21,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:06:21,607 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 8 completed and processed: 1054 characters
2025-08-08 10:06:21,608 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:06:21,608 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-08 10:06:21,608 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:06:21,608 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-08 10:06:21,609 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 8
2025-08-08 10:06:21,609 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 8
2025-08-08 10:06:21,609 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 0
2025-08-08 10:06:21,609 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_095923.log
2025-08-08 10:06:21,617 - celery.app.trace - INFO - Task generate_streaming_report[c3599842-8d9a-4383-bc12-947d953523bd] succeeded in 418.24392679099947s: {'outline': '# Report on Female Enrollment at ITC University

## 1. Introduction  
   The purpose of this report is to investigate the number of girls enrolled at ITC University, providing a comprehensive overview of gender demographics within the institution. The key finding reveals that there are currently 87,965 female students enrolled at ITC University, which indicates a significant gender disparity in the overall student population.

## 2. Total Enrollment Statistics  
   - Total student enrollment at ITC University  
     - Total: 192,627 students  
   - Gender breakdown of enrollment  
     - Girls: 87,965  
     - Boys: 96,457  
     - Comparison: Boys outnumber girls by 8,492 students

## 3. Programs with Highest Female Enrollment  
   - Overview of programs with significant female participation  
     - Bachelor of Education (Junior High School): 9,242 girls  
     - Bachelor of Education (Early Grade): 6,867 girls  
     - Bachelor of Education (Upper Primary): 6,378 girls  
   - Analysis of...', , ...}
2025-08-08 10:59:35,292 - celery.worker.strategy - INFO - Task generate_streaming_report[fc8e3d21-3e8a-4457-8e84-b6081894aeb8] received
2025-08-08 10:59:35,293 - app.tasks.report_task - INFO - starting streaming report generation...
2025-08-08 10:59:35,294 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:59:35,294 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 2305263d-0e7d-4bcf-abee-b68f8701783a
2025-08-08 10:59:35,294 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:59:35,294 - REPORT_REQUEST - INFO - 📝 Original Question: 'What institution has the most students?'
2025-08-08 10:59:35,294 - REPORT_REQUEST - INFO - 🆔 Task ID: 2305263d-0e7d-4bcf-abee-b68f8701783a
2025-08-08 10:59:35,295 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T10:59:35.295005
2025-08-08 10:59:45,298 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:N/A duration:10.003s]
2025-08-08 10:59:45,299 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://*************:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-08 10:59:45,299 - ELASTICSEARCH_STATE - ERROR - ❌ Error checking Elasticsearch state: Connection timed out
2025-08-08 10:59:45,300 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 10:59:45,300 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 10:59:45,300 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 10:59:45,300 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: What institution has the most students?
2025-08-08 10:59:45,300 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-08 10:59:45,300 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 10:59:53,722 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:59:53,742 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 10:59:58,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 10:59:58,263 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-08 11:00:02,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:03,730 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:06,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:06,994 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:07,055 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:07,526 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:07,912 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:08,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:09,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:09,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:09,646 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-08 11:00:11,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:12,204 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:12,379 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:12,966 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:13,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:13,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:14,487 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:15,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:15,171 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:16,082 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:16,275 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:16,415 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:16,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:16,576 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:16,577 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:17,429 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:18,197 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:19,074 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:20,271 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:20,858 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:22,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:23,099 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:24,307 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:25,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:26,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:26,368 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total number of students enrolled at the institution with the most students?', 'sql': 'SELECT COUNT(s.id) AS total_students\nFROM students s\nJOIN institutions i ON s.institution_id = i.id\nGROUP BY s.institution_id\nORDER BY total_students DESC\nLIMIT 1;', 'correct': True, 'reasoning': 'The SQL query correctly counts the number of students for each institution by grouping the results by institution_id. It then orders the results in descending order based on the count of students, ensuring that the institution with the most students appears first. The use of LIMIT 1 ensures that only the top result (the institution with the most students) is returned, which aligns perfectly with the question asked.', 'feedback': 'The SQL query is well-structured and effectively answers the question. No changes are necessary, but it could be beneficial to add a comment in the SQL to clarify the purpose of the query for future reference.'}
2025-08-08 11:00:26,369 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:26,369 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:00:27,009 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:27,013 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the demographic breakdown of students at the institution with the most students?', 'sql': 'SELECT s.sex, COUNT(*) AS student_count\nFROM core.students s\nJOIN core.institutions i ON s.institution_id = i.id\nWHERE i.id = (SELECT institution_id FROM core.students GROUP BY institution_id ORDER BY COUNT(*) DESC LIMIT 1)\nGROUP BY s.sex;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by using a subquery that groups students by institution_id and orders them by the count of students in descending order, limiting the result to the top institution. It then joins the students table with the institutions table to ensure that the demographic breakdown is specific to that institution. Finally, it groups the results by sex and counts the number of students for each sex, which aligns with the request for a demographic breakdown.', 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be enhanced by including additional demographic factors such as age or nationality if those attributes are relevant to the breakdown. Additionally, clarifying the term 'demographic breakdown' in the question could help specify which demographic factors are of interest."}
2025-08-08 11:00:27,013 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:27,014 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:00:29,962 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:31,189 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:31,200 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:32,283 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:32,376 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:33,095 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:33,100 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the retention rate of students at the institution with the most students?', 'sql': "WITH student_counts AS (SELECT institution_id, COUNT(*) AS total_students FROM core.students GROUP BY institution_id), max_institution AS (SELECT institution_id FROM student_counts ORDER BY total_students DESC LIMIT 1), retained_students AS (SELECT COUNT(*) AS retained FROM core.student_programs sp JOIN max_institution mi ON sp.institution_id = mi.institution_id WHERE sp.student_program_status_id = (SELECT id FROM core.student_status_types WHERE status = 'active')) SELECT (SELECT retained FROM retained_students) * 1.0 / (SELECT total_students FROM student_counts WHERE institution_id = (SELECT institution_id FROM max_institution)) AS retention_rate;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the total number of students per institution and selecting the one with the maximum count. It then calculates the number of retained students by counting those with an 'active' status in the student_programs table. Finally, it computes the retention rate by dividing the number of retained students by the total number of students at that institution. The logic follows the question's requirements accurately.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by adding comments for clarity, especially for complex parts like the CTEs (Common Table Expressions). Additionally, ensure that the status 'active' is the correct definition of retention for the context of this question."}
2025-08-08 11:00:33,100 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:33,100 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:00:33,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:33,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:34,772 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:34,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:35,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:35,678 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:35,688 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How has the student enrollment at the institution with the most students changed over the past few years?', 'sql': 'SELECT academic_years.start_year, COUNT(students.id) AS total_enrollment\nFROM students\nJOIN student_programs ON students.id = student_programs.student_id\nJOIN institutions ON student_programs.institution_id = institutions.id\nJOIN academic_years ON student_programs.semester_id = academic_years.id\nWHERE institutions.id = (SELECT institution_id FROM students GROUP BY institution_id ORDER BY COUNT(*) DESC LIMIT 1)\nGROUP BY academic_years.start_year\nORDER BY academic_years.start_year;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by using a subquery that counts the number of students per institution and orders them in descending order. It then joins the relevant tables to count the total enrollment for that institution grouped by academic years. The use of 'academic_years.start_year' in the SELECT and GROUP BY clauses allows for tracking changes in enrollment over the years, which directly answers the question about how enrollment has changed over time.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by ensuring that the 'academic_years' table is correctly linked to the relevant semester or academic period in the 'student_programs' table, as the relationship should reflect the actual enrollment periods. Additionally, clarifying the definition of 'changed' in the question could help in understanding whether it refers to increases, decreases, or overall trends."}
2025-08-08 11:00:35,688 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:35,688 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:00:35,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:36,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:36,534 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:36,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:37,602 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:38,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:38,173 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and various related entities, but it does not provide specific metrics or qualitative factors that would directly explain enrollment numbers. To answer this question, one would need to analyze trends, marketing strategies, student satisfaction, and other external factors that are not captured in the schema.', 'feedback': ''}
2025-08-08 11:00:38,173 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:38,173 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:38,174 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and various related entities, but it does not provide specific metrics or qualitative factors that would directly explain enrollment numbers. To answer this question, one would need to analyze trends, marketing strategies, student satisfaction, and other external factors that are not captured in the schema.', 'feedback': ''}
2025-08-08 11:00:38,312 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:38,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:38,945 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:39,129 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:39,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:40,046 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:40,623 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:41,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:41,796 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:41,819 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the challenges in obtaining retention rate data for large institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to challenges in obtaining retention rate data for large institutions. This is a qualitative inquiry that requires insights, opinions, or contextual understanding rather than quantitative data that can be extracted from the provided database schema. The schema contains tables related to institutions, students, programs, and various academic records, but it does not provide any direct information or analysis regarding the challenges of obtaining retention rate data.', 'feedback': ''}
2025-08-08 11:00:41,820 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:41,820 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:41,820 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the challenges in obtaining retention rate data for large institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to challenges in obtaining retention rate data for large institutions. This is a qualitative inquiry that requires insights, opinions, or contextual understanding rather than quantitative data that can be extracted from the provided database schema. The schema contains tables related to institutions, students, programs, and various academic records, but it does not provide any direct information or analysis regarding the challenges of obtaining retention rate data.', 'feedback': ''}
2025-08-08 11:00:42,247 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:42,473 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:43,150 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:43,171 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and various related entities, but it does not provide specific metrics or qualitative factors that would directly explain enrollment numbers. To answer this question, one would need to analyze trends, marketing strategies, student satisfaction, and other external factors that are not captured in the schema.', 'feedback': ''}
2025-08-08 11:00:43,171 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:43,172 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:43,172 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and various related entities, but it does not provide specific metrics or qualitative factors that would directly explain enrollment numbers. To answer this question, one would need to analyze trends, marketing strategies, student satisfaction, and other external factors that are not captured in the schema.', 'feedback': ''}
2025-08-08 11:00:43,244 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:43,265 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:44,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:45,190 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:45,210 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education regarding gender and other demographics?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education regarding gender and other demographics. However, the provided schema does not contain any tables or fields that specifically store national demographic trends or comparative data. While there are tables related to students that include gender and possibly other demographic information (like nationality), there is no reference to national trends or a way to aggregate or compare this data against a national standard. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:00:45,210 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:45,211 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:45,211 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education regarding gender and other demographics?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education regarding gender and other demographics. However, the provided schema does not contain any tables or fields that specifically store national demographic trends or comparative data. While there are tables related to students that include gender and possibly other demographic information (like nationality), there is no reference to national trends or a way to aggregate or compare this data against a national standard. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:00:46,053 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:46,056 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:46,080 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on student enrollment at such a large institution?', 'answerable': False, 'reasoning': 'The question is asking for qualitative insights into the factors contributing to a lack of available data on student enrollment, which is not something that can be directly answered using the provided database schema. The schema contains tables related to student enrollment, but it does not provide any information or context regarding the reasons or factors that could lead to a lack of data. To answer this question, one would need qualitative data, institutional policies, or external factors that are not represented in the schema.', 'feedback': ''}
2025-08-08 11:00:46,081 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:46,081 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:46,081 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on student enrollment at such a large institution?', 'answerable': False, 'reasoning': 'The question is asking for qualitative insights into the factors contributing to a lack of available data on student enrollment, which is not something that can be directly answered using the provided database schema. The schema contains tables related to student enrollment, but it does not provide any information or context regarding the reasons or factors that could lead to a lack of data. To answer this question, one would need qualitative data, institutional policies, or external factors that are not represented in the schema.', 'feedback': ''}
2025-08-08 11:00:46,662 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:46,677 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the challenges in obtaining retention rate data for large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the challenges of obtaining retention rate data for large institutions, which is not something that can be derived from the provided database schema. The schema contains tables related to institutions, students, programs, and various administrative data, but it does not include any information or metrics that would directly address the factors contributing to challenges in data collection or retention rates. Therefore, this question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 11:00:46,677 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:46,678 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:46,678 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the challenges in obtaining retention rate data for large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into the challenges of obtaining retention rate data for large institutions, which is not something that can be derived from the provided database schema. The schema contains tables related to institutions, students, programs, and various administrative data, but it does not include any information or metrics that would directly address the factors contributing to challenges in data collection or retention rates. Therefore, this question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 11:00:47,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:48,460 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:48,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:48,667 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and related entities, but it does not provide specific metrics or qualitative factors that would directly explain enrollment numbers. To answer this question, one would need to analyze various data points such as admission rates, applicant demographics, program offerings, and possibly external factors like market trends, none of which are explicitly detailed in the schema.', 'feedback': ''}
2025-08-08 11:00:48,668 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:48,668 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:48,668 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and related entities, but it does not provide specific metrics or qualitative factors that would directly explain enrollment numbers. To answer this question, one would need to analyze various data points such as admission rates, applicant demographics, program offerings, and possibly external factors like market trends, none of which are explicitly detailed in the schema.', 'feedback': ''}
2025-08-08 11:00:49,503 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:50,311 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:50,318 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the student population of the institution with the most students compare to other institutions?', 'sql': "WITH student_counts AS (  SELECT institution_id, COUNT(*) AS student_count  FROM core.students  GROUP BY institution_id), max_student_count AS (  SELECT MAX(student_count) AS max_count  FROM student_counts) SELECT i.name, sc.student_count, CASE WHEN sc.student_count = m.max_count THEN 'Most Students' ELSE 'Less Students' END AS comparison FROM student_counts sc JOIN auth.institutions i ON sc.institution_id = i.id JOIN max_student_count m ON 1=1;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by first counting the number of students per institution and then determining the maximum count. It then compares each institution's student count to this maximum, labeling them as either having 'Most Students' or 'Less Students'. This directly addresses the question of how the student population of the institution with the most students compares to others.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly including the institution with the most students in the results, rather than just labeling others. Additionally, consider renaming the 'comparison' column to something more descriptive, like 'student_population_comparison'."}
2025-08-08 11:00:50,318 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:50,319 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:00:50,534 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:50,827 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:50,844 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education regarding gender and other demographics?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education regarding gender and other demographics. However, the provided schema does not contain any tables or fields that specifically store national demographic trends or comparative data. While there are tables related to students that include gender and possibly other demographic information (like nationality), there is no reference to national trends or a way to aggregate or compare this data against a national standard. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:00:50,845 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:50,845 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:50,845 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education regarding gender and other demographics?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education regarding gender and other demographics. However, the provided schema does not contain any tables or fields that specifically store national demographic trends or comparative data. While there are tables related to students that include gender and possibly other demographic information (like nationality), there is no reference to national trends or a way to aggregate or compare this data against a national standard. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:00:51,291 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:51,310 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on student enrollment at such a large institution?', 'answerable': False, 'reasoning': 'The question is asking for an analysis of factors contributing to the lack of available data on student enrollment, which is a qualitative inquiry rather than a quantitative one. The provided schema contains tables related to student enrollment, but it does not include any data or fields that would allow for an analysis of the reasons behind the lack of available data. The schema does not provide insights into data collection processes, data quality issues, or institutional policies that could affect data availability.', 'feedback': ''}
2025-08-08 11:00:51,310 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:51,310 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:51,310 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on student enrollment at such a large institution?', 'answerable': False, 'reasoning': 'The question is asking for an analysis of factors contributing to the lack of available data on student enrollment, which is a qualitative inquiry rather than a quantitative one. The provided schema contains tables related to student enrollment, but it does not include any data or fields that would allow for an analysis of the reasons behind the lack of available data. The schema does not provide insights into data collection processes, data quality issues, or institutional policies that could affect data availability.', 'feedback': ''}
2025-08-08 11:00:51,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:51,683 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the challenges in obtaining retention rate data for large institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to challenges in obtaining retention rate data for large institutions. This is a qualitative inquiry that requires insights, opinions, or contextual understanding rather than quantitative data that can be extracted from the provided database schema. The schema contains tables related to institutions, students, programs, and various academic records, but it does not include any information or metrics that directly address the challenges or factors affecting retention rates.', 'feedback': ''}
2025-08-08 11:00:51,683 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:51,683 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:51,683 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the challenges in obtaining retention rate data for large institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to challenges in obtaining retention rate data for large institutions. This is a qualitative inquiry that requires insights, opinions, or contextual understanding rather than quantitative data that can be extracted from the provided database schema. The schema contains tables related to institutions, students, programs, and various academic records, but it does not include any information or metrics that directly address the challenges or factors affecting retention rates.', 'feedback': ''}
2025-08-08 11:00:51,691 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:53,204 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:53,614 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:53,754 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:54,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:54,096 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:54,536 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:54,551 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and enrollment processes, but it does not provide specific metrics or factors that would directly explain high enrollment numbers. To answer this question, one would need to analyze various data points such as marketing efforts, program offerings, applicant demographics, and external factors influencing enrollment, none of which are explicitly detailed in the schema.', 'feedback': ''}
2025-08-08 11:00:54,551 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:54,551 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:54,551 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a straightforward query that can be answered with the existing schema. The schema contains data about institutions, applicants, and enrollment processes, but it does not provide specific metrics or factors that would directly explain high enrollment numbers. To answer this question, one would need to analyze various data points such as marketing efforts, program offerings, applicant demographics, and external factors influencing enrollment, none of which are explicitly detailed in the schema.', 'feedback': ''}
2025-08-08 11:00:55,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:55,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:55,990 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:56,007 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on student enrollment at such a large institution?', 'answerable': False, 'reasoning': 'The question is asking for qualitative insights into the factors contributing to a lack of available data on student enrollment, which is not something that can be directly answered using the provided database schema. The schema contains tables related to student enrollment, but it does not provide any information or context regarding the reasons or factors that could lead to a lack of data. To answer this question, one would need qualitative data or insights that are not present in the schema.', 'feedback': ''}
2025-08-08 11:00:56,007 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:56,007 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:56,007 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on student enrollment at such a large institution?', 'answerable': False, 'reasoning': 'The question is asking for qualitative insights into the factors contributing to a lack of available data on student enrollment, which is not something that can be directly answered using the provided database schema. The schema contains tables related to student enrollment, but it does not provide any information or context regarding the reasons or factors that could lead to a lack of data. To answer this question, one would need qualitative data or insights that are not present in the schema.', 'feedback': ''}
2025-08-08 11:00:56,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:56,102 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the challenges in obtaining retention rate data for large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights regarding the challenges in obtaining retention rate data for large institutions. This type of inquiry requires subjective analysis and interpretation of various factors that are not represented in the database schema. The schema primarily contains structured data about institutions, students, programs, and related entities, but it does not provide any qualitative insights or factors that would contribute to challenges in data retention. Therefore, it cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:00:56,102 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:56,102 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:56,102 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the challenges in obtaining retention rate data for large institutions?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights regarding the challenges in obtaining retention rate data for large institutions. This type of inquiry requires subjective analysis and interpretation of various factors that are not represented in the database schema. The schema primarily contains structured data about institutions, students, programs, and related entities, but it does not provide any qualitative insights or factors that would contribute to challenges in data retention. Therefore, it cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:00:56,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:56,588 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:56,610 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education regarding gender and other demographics?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education regarding gender and other demographics. However, the provided schema does not contain any tables or fields that specifically store national demographic trends or comparative data. While there are tables related to students that include gender and possibly other demographic information (like nationality), there is no reference to national trends or a way to aggregate or compare this data against a national standard. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:00:56,610 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:00:56,610 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:00:56,610 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education regarding gender and other demographics?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education regarding gender and other demographics. However, the provided schema does not contain any tables or fields that specifically store national demographic trends or comparative data. While there are tables related to students that include gender and possibly other demographic information (like nationality), there is no reference to national trends or a way to aggregate or compare this data against a national standard. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:00:57,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:57,450 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:59,017 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:00:59,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:00,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:02,035 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:02,054 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on student enrollment at such a large institution?', 'answerable': False, 'reasoning': 'The question is asking for qualitative insights into the factors contributing to a lack of available data on student enrollment, which is not something that can be directly answered by querying the database schema. The schema provides structured data about various entities related to institutions, students, and their programs, but it does not contain any qualitative analysis or commentary on data availability issues. Therefore, this question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 11:01:02,055 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:01:02,055 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:01:02,055 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of available data on student enrollment at such a large institution?', 'answerable': False, 'reasoning': 'The question is asking for qualitative insights into the factors contributing to a lack of available data on student enrollment, which is not something that can be directly answered by querying the database schema. The schema provides structured data about various entities related to institutions, students, and their programs, but it does not contain any qualitative analysis or commentary on data availability issues. Therefore, this question cannot be answered using the schema.', 'feedback': ''}
2025-08-08 11:01:02,608 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:02,623 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education regarding gender and other demographics?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education regarding gender and other demographics. However, the provided schema does not contain any tables or fields that specifically store national demographic trends or comparative data. While there are tables related to students that include gender and possibly other demographic information (like nationality), there is no reference to national trends or a way to aggregate or compare this data against a national standard. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:01:02,623 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:01:02,623 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:01:02,623 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national trends in higher education regarding gender and other demographics?', 'answerable': False, 'reasoning': 'The question asks for a comparison of the demographic breakdown of an institution to national trends in higher education regarding gender and other demographics. However, the provided schema does not contain any tables or fields that specifically store national demographic trends or comparative data. While there are tables related to students that include gender and possibly other demographic information (like nationality), there is no reference to national trends or a way to aggregate or compare this data against a national standard. Therefore, the question cannot be answered with the current schema.', 'feedback': ''}
2025-08-08 11:01:03,399 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:03,418 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any specific data or attributes that directly relate to the reasons or factors influencing student population sizes, such as demographic data, marketing strategies, or institutional policies. The schema primarily contains tables related to student records, courses, and administrative data, but lacks qualitative insights or comparative metrics that would allow for a comprehensive answer to this question.", 'feedback': ''}
2025-08-08 11:01:03,419 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:01:03,419 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:01:03,419 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any specific data or attributes that directly relate to the reasons or factors influencing student population sizes, such as demographic data, marketing strategies, or institutional policies. The schema primarily contains tables related to student records, courses, and administrative data, but lacks qualitative insights or comparative metrics that would allow for a comprehensive answer to this question.", 'feedback': ''}
2025-08-08 11:01:03,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:05,690 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:08,155 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:08,170 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any specific data or attributes that would allow for a direct comparison of student populations across different institutions or the factors influencing those populations. The schema includes tables related to students, institutions, and various attributes, but it lacks qualitative data or metrics that would help identify or analyze the reasons behind the differences in student populations.", 'feedback': ''}
2025-08-08 11:01:08,170 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:01:08,171 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:01:08,171 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any specific data or attributes that would allow for a direct comparison of student populations across different institutions or the factors influencing those populations. The schema includes tables related to students, institutions, and various attributes, but it lacks qualitative data or metrics that would help identify or analyze the reasons behind the differences in student populations.", 'feedback': ''}
2025-08-08 11:01:10,606 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:10,942 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:13,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:13,700 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any data or attributes that directly relate to the reasons or factors influencing student population sizes, such as demographic data, marketing strategies, or institutional policies. The schema primarily contains tables related to student records, courses, and administrative data, but lacks qualitative data or comparative metrics that would allow for a comprehensive analysis of the factors affecting student enrollment.", 'feedback': ''}
2025-08-08 11:01:13,700 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:01:13,700 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:01:13,700 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any data or attributes that directly relate to the reasons or factors influencing student population sizes, such as demographic data, marketing strategies, or institutional policies. The schema primarily contains tables related to student records, courses, and administrative data, but lacks qualitative data or comparative metrics that would allow for a comprehensive analysis of the factors affecting student enrollment.", 'feedback': ''}
2025-08-08 11:01:14,487 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:16,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:17,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:18,495 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:18,954 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:18,989 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any specific data or attributes that would allow for a direct comparison of student populations across different institutions or the factors influencing those populations. The schema includes tables related to student information, institutions, and various academic records, but it lacks qualitative data or metrics that would help identify or analyze the reasons behind the differences in student populations.", 'feedback': ''}
2025-08-08 11:01:18,989 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:01:18,989 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:01:18,989 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any specific data or attributes that would allow for a direct comparison of student populations across different institutions or the factors influencing those populations. The schema includes tables related to student information, institutions, and various academic records, but it lacks qualitative data or metrics that would help identify or analyze the reasons behind the differences in student populations.", 'feedback': ''}
2025-08-08 11:01:25,472 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:31,297 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:34,182 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:37,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:40,970 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:01:51,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:02,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:02,344 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are most popular at the institution with the most students?', 'sql': "WITH StudentCounts AS (SELECT institution_id, COUNT(*) AS student_count FROM core.students GROUP BY institution_id ORDER BY student_count DESC LIMIT 1), PopularPrograms AS (SELECT p.long_name AS program_name, COUNT(s.id) AS student_count FROM core.programs p JOIN core.students s ON p.institution_id = s.institution_id WHERE p.institution_id IN (SELECT institution_id FROM StudentCounts) GROUP BY p.long_name), PopularCourses AS (SELECT c.title AS course_title, COUNT(s.id) AS student_count FROM core.courses c JOIN core.students s ON c.institution_id = s.institution_id WHERE c.institution_id IN (SELECT institution_id FROM StudentCounts) GROUP BY c.title) SELECT 'Program' AS type, program_name AS name, student_count FROM PopularPrograms UNION ALL SELECT 'Course' AS type, course_title AS name, student_count FROM PopularCourses ORDER BY student_count DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and selecting the one with the highest count. It then retrieves the most popular programs and courses at that institution by counting the number of students enrolled in each program and course. The use of CTEs (Common Table Expressions) allows for clear separation of logic, and the final UNION ALL combines the results for both programs and courses, which aligns with the question's request for both types of offerings.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating the criteria for 'most popular' (e.g., by number of students enrolled) in the question to ensure clarity. Additionally, consider adding a limit to the final result set to return only the top N programs and courses if needed."}
2025-08-08 11:02:02,344 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:02:02,345 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 11:02:33,474 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:34,067 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:35,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:38,265 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:41,340 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:41,372 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights, opinions, or external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include any qualitative analysis, opinions, or external factors that could explain popularity. Therefore, it cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:02:41,372 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:02:41,372 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:02:41,372 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights, opinions, or external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include any qualitative analysis, opinions, or external factors that could explain popularity. Therefore, it cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 11:02:44,143 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:46,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:46,639 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights, opinions, or external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include any qualitative analysis, surveys, or metrics that would help answer the question about popularity factors.', 'feedback': ''}
2025-08-08 11:02:46,639 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:02:46,640 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:02:46,640 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights, opinions, or external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include any qualitative analysis, surveys, or metrics that would help answer the question about popularity factors.', 'feedback': ''}
2025-08-08 11:02:49,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:52,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:52,616 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights, opinions, or external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include any qualitative factors or insights that would help answer the question about popularity. Therefore, it cannot be answered from the schema.', 'feedback': ''}
2025-08-08 11:02:52,616 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:02:52,616 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:02:52,616 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights, opinions, or external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include any qualitative factors or insights that would help answer the question about popularity. Therefore, it cannot be answered from the schema.', 'feedback': ''}
2025-08-08 11:02:55,574 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:58,335 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:02:58,350 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided schema contains data about various entities related to institutions, programs, courses, and students, but it does not include any qualitative data or insights that would allow for an analysis of popularity factors. The schema lacks information on student preferences, market trends, or external influences that could affect program popularity.', 'feedback': ''}
2025-08-08 11:02:58,350 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 11:02:58,350 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 11:02:58,350 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided schema contains data about various entities related to institutions, programs, courses, and students, but it does not include any qualitative data or insights that would allow for an analysis of popularity factors. The schema lacks information on student preferences, market trends, or external influences that could affect program popularity.', 'feedback': ''}
2025-08-08 11:02:58,351 - root - INFO - [{'total_students': 192627}]
2025-08-08 11:02:58,351 - root - INFO - [{'name': 'ITC University', 'student_count': 49153, 'comparison': 'Less Students'}, {'name': 'Akrofi-Christaller Institute of Theology Mission and Culture', 'student_count': 114, 'comparison': 'Less Students'}, {'name': 'Accra Medical', 'student_count': 13012, 'comparison': 'Less Students'}, {'name': 'Ghana School of Law', 'student_count': 262, 'comparison': 'Less Students'}, {'name': 'Koforidua Technical University', 'student_count': 17758, 'comparison': 'Less Students'}, {'name': 'Presbyterian University College Ghana', 'student_count': 32094, 'comparison': 'Less Students'}, {'name': 'Methodist University College Ghana', 'student_count': 1, 'comparison': 'Less Students'}, {'name': 'Wisconsin International University College', 'student_count': 1202, 'comparison': 'Less Students'}, {'name': 'ITC University', 'student_count': 192627, 'comparison': 'Most Students'}, {'name': 'Kofi Annan International Peacekeeping Training Centre', 'student_count': 52, 'comparison': 'Less Students'}, {'name': 'Klintaps University College of Health and Allied Sciences', 'student_count': 1135, 'comparison': 'Less Students'}]
2025-08-08 11:02:58,351 - root - INFO - [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]
2025-08-08 11:02:58,351 - root - INFO - 'No results'
2025-08-08 11:02:58,351 - root - INFO - 'No results'
2025-08-08 11:02:58,351 - root - INFO - 'No results'
2025-08-08 11:02:58,351 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 11:03:06,702 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:03:06,708 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 11:03:15,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 11:03:15,509 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:03:15,509 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:03:15,509 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:03:15,509 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:03:15,509 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:03:15,509 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:03:15,509 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:03:15,509 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:03:15,509 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total number of students enrolled at the institution with the most students?...
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total number of students enrolled at the institution with the most students is 192,627....
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'total_students': 192627}]...
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_students_at_largest_institution
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 11:03:15,510 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 11:03:15,510 - celery.redirected - WARNING - [{'total_students': 192627}]
2025-08-08 11:03:15,510 - celery.redirected - WARNING - ================================= 
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 11:03:15,510 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:03:15,510 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:03:15,510 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:03:15,510 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:03:15,510 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the student population of the institution with the most students compare to other instituti...
2025-08-08 11:03:15,511 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the most students is ITC University, which has a total student population of 19...
2025-08-08 11:03:15,511 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: graph
2025-08-08 11:03:15,511 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'name': 'ITC University', 'student_count': 49153, 'comparison': 'Less Students'}, {'name': 'Akrofi...
2025-08-08 11:03:15,511 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_population_comparison
2025-08-08 11:03:15,511 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 11:03:15,511 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 11:03:15,511 - celery.redirected - WARNING - [{'name': 'ITC University', 'student_count': 49153, 'comparison': 'Less Students'}, {'name': 'Akrofi-Christaller Institute of Theology Mission and Culture', 'student_count': 114, 'comparison': 'Less Students'}, {'name': 'Accra Medical', 'student_count': 13012, 'comparison': 'Less Students'}, {'name': 'Ghana School of Law', 'student_count': 262, 'comparison': 'Less Students'}, {'name': 'Koforidua Technical University', 'student_count': 17758, 'comparison': 'Less Students'}, {'name': 'Presbyterian University College Ghana', 'student_count': 32094, 'comparison': 'Less Students'}, {'name': 'Methodist University College Ghana', 'student_count': 1, 'comparison': 'Less Students'}, {'name': 'Wisconsin International University College', 'student_count': 1202, 'comparison': 'Less Students'}, {'name': 'ITC University', 'student_count': 192627, 'comparison': 'Most Students'}, {'name': 'Kofi Annan International Peacekeeping Training Centre', 'student_count': 52, 'comparison': 'Less Students'}, {'name': 'Klintaps University College of Health and Allied Sciences', 'student_count': 1135, 'comparison': 'Less Students'}]
2025-08-08 11:03:15,511 - celery.redirected - WARNING - ================================= 
2025-08-08 11:03:15,511 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_population_comparison
2025-08-08 11:03:15,511 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 11:03:15,511 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:03:15,511 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:03:15,511 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:03:15,511 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:03:15,511 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:03:15,512 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are most popular at the institution with the most students?...
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no results available for the programs or courses at the institution with t...
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: popular_courses_at_largest_institution
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 11:03:15,512 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:03:15,512 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:03:15,512 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:03:15,512 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:03:15,512 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the demographic breakdown of students at the institution with the most students?...
2025-08-08 11:03:15,513 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the most students has a total of 164,622 students, broken down by gender as fol...
2025-08-08 11:03:15,513 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-08 11:03:15,513 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_cou...
2025-08-08 11:03:15,513 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_demographics_by_gender
2025-08-08 11:03:15,513 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 11:03:15,513 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 11:03:15,513 - celery.redirected - WARNING - [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]
2025-08-08 11:03:15,513 - celery.redirected - WARNING - ================================= 
2025-08-08 11:03:15,513 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_demographics_by_gender
2025-08-08 11:03:15,513 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 11:03:15,513 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:03:15,513 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:03:15,513 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:03:15,514 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:03:15,514 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:03:15,514 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:03:15,514 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:03:15,514 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:03:15,514 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How has the student enrollment at the institution with the most students changed over the past few y...
2025-08-08 11:03:15,514 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no available records or data regarding the changes in student enrollment a...
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_enrollment_trends_most_populous_institution
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 11:03:15,515 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:03:15,515 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 11:03:15,515 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What institution has the most students?'
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 11:03:15,515 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the retention rate of students at the institution with the most students?...
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no results available to determine the retention rate of students at the in...
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 11:03:15,515 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: retention_rate_no_data
2025-08-08 11:03:15,516 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 11:03:15,516 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 11:03:15,516 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 6
2025-08-08 11:03:15,516 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:03:15,516 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 11:03:15,516 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 6 documents
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Content: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:00:54.551786+00:00', 'data_returned': True}
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Content: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:01:18.989961+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:02:58.350707+00:00', 'data_returned': False}
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Content: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:01:02.624038+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_gender'}
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Content: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:01:02.055382+00:00', 'data_returned': False}
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-08 11:03:15,516 - UPSERT_DOCS - INFO -     Content: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 11:03:15,517 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T11:00:56.102726+00:00', 'data_returned': False}
2025-08-08 11:03:15,517 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 3/6
2025-08-08 11:03:25,519 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:N/A duration:10.002s]
2025-08-08 11:03:25,520 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://*************:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-08 11:03:25,520 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: Connection timed out
2025-08-08 11:03:25,520 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 11:03:25,521 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-08 11:03:25,521 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 11:03:25,521 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: Connection timed out
2025-08-08 11:03:25,521 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_095923.log
2025-08-08 11:03:25,524 - celery.app.trace - INFO - Task generate_streaming_report[fc8e3d21-3e8a-4457-8e84-b6081894aeb8] succeeded in 230.23250341699895s: {'error': 'Error generating streaming report: Connection timed out'}

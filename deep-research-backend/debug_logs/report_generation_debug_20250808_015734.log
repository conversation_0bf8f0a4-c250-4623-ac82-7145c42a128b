2025-08-08 01:57:34,529 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_015734.log
2025-08-08 01:57:34,530 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:57:34,530 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 5224971c-58b7-44d4-be57-3f0dd2771a49
2025-08-08 01:57:34,530 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:57:34,530 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-08 01:57:34,530 - REPORT_REQUEST - INFO - 🆔 Task ID: 5224971c-58b7-44d4-be57-3f0dd2771a49
2025-08-08 01:57:34,530 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T01:57:34.530623
2025-08-08 01:57:34,941 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.411s]
2025-08-08 01:57:34,942 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 47
2025-08-08 01:57:35,918 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.975s]
2025-08-08 01:57:35,918 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 1
2025-08-08 01:57:42,984 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:7.065s]
2025-08-08 01:57:42,984 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (4):
2025-08-08 01:57:42,984 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (28 docs)
2025-08-08 01:57:42,984 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (11 docs)
2025-08-08 01:57:42,984 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 01:57:42,984 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing in ITC University?' (1 docs)
2025-08-08 01:57:42,985 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 01:57:54,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:57:54,888 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 01:57:58,758 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:57:58,778 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 01:58:01,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:01,067 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:01,081 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:01,184 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:01,234 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:01,268 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:01,552 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:01,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:01,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:02,043 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:02,112 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 01:58:02,113 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:58:02,113 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 01:58:02,113 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:58:02,113 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 2
2025-08-08 01:58:04,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:04,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:07,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:07,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:08,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:09,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:14,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:15,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:20,207 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:20,223 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic results of girls compare to those of boys at ITC University?', 'sql': "SELECT sex, AVG(assessment_total) AS average_assessment_total, AVG(exam_total) AS average_exam_total, AVG(finalscore) AS average_finalscore, COUNT(*) AS total_students\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nWHERE s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY sex;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average academic results (assessment_total, exam_total, finalscore) for both girls and boys at ITC University by grouping the results based on the 'sex' of the students. It also counts the total number of students in each group, which is relevant for comparison. The use of a subquery to filter by institution ensures that only results from ITC University are considered.", 'feedback': 'The question could be clarified by specifying which specific academic results are of interest (e.g., average scores, pass rates) or if any additional metrics should be included. The SQL query is well-structured, but it could also include a comparison metric or visualization if needed.'}
2025-08-08 01:58:20,224 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:58:20,224 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 01:58:22,818 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:24,457 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:25,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:26,504 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:26,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:28,757 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:31,538 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:31,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:32,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:33,978 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:33,997 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significant academic performance gap between girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance gap between girls and boys at ITC University. However, the provided schema does not contain specific data or attributes related to gender-based performance analysis or any direct metrics that would allow for such an analysis. While there are tables related to students, courses, and academic results, there is no indication of gender-specific performance data or factors influencing performance gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 01:58:33,997 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:58:33,997 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:58:33,997 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significant academic performance gap between girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance gap between girls and boys at ITC University. However, the provided schema does not contain specific data or attributes related to gender-based performance analysis or any direct metrics that would allow for such an analysis. While there are tables related to students, courses, and academic results, there is no indication of gender-specific performance data or factors influencing performance gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 01:58:37,647 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:37,779 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:41,065 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:41,086 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significant academic performance gap between girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance gap between girls and boys at ITC University. However, the provided schema does not contain specific data or attributes that directly relate to gender-based performance analysis or the factors influencing academic performance. While there are tables related to students, courses, and assessments, there is no explicit data on gender differences in performance or the factors contributing to such gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 01:58:41,087 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:58:41,087 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:58:41,087 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significant academic performance gap between girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance gap between girls and boys at ITC University. However, the provided schema does not contain specific data or attributes that directly relate to gender-based performance analysis or the factors influencing academic performance. While there are tables related to students, courses, and assessments, there is no explicit data on gender differences in performance or the factors contributing to such gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 01:58:43,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:43,714 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in?', 'sql': "SELECT cp.title AS course_title, sp.program_id, AVG(ar.finalscore) AS average_score\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.assessment_results ar ON sp.id = ar.student_program_id\nJOIN core.courses cp ON ar.course_id = cp.id\nWHERE s.sex = 'F' AND sp.institution_id = 'ITC University'\nGROUP BY cp.title, sp.program_id\nHAVING AVG(ar.finalscore) > 75\nORDER BY average_score DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the courses and programs that female students at ITC University are excelling in by filtering for female students, joining the necessary tables to gather course titles and program IDs, and calculating the average final score for each course and program. The use of HAVING to filter for an average score greater than 75 aligns with the notion of 'excelling'. The query also orders the results by average score in descending order, which is appropriate for identifying the top-performing courses.", 'feedback': "The question could be clarified by specifying what 'excelling' means in terms of score thresholds, but the SQL query already assumes a threshold of 75, which is reasonable. Additionally, it might be beneficial to include the program title in the SELECT statement for more clarity on which programs the courses belong to."}
2025-08-08 01:58:43,714 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:58:43,715 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 01:58:43,903 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:46,473 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:47,017 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:47,035 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significant academic performance gap between girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance gap between girls and boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and academic results, there is no direct reference to gender-specific performance data or any analytical framework to assess the reasons behind performance gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 01:58:47,035 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:58:47,035 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:58:47,035 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significant academic performance gap between girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance gap between girls and boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and academic results, there is no direct reference to gender-specific performance data or any analytical framework to assess the reasons behind performance gaps. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-08 01:58:47,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:48,733 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:49,540 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:53,026 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:57,325 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:58:57,340 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significant academic performance gap between girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance gap between girls and boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing academic performance would typically require qualitative data, statistical analysis, and possibly external research, none of which are present in the schema.', 'feedback': ''}
2025-08-08 01:58:57,340 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:58:57,340 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:58:57,340 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significant academic performance gap between girls and boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance gap between girls and boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing academic performance would typically require qualitative data, statistical analysis, and possibly external research, none of which are present in the schema.', 'feedback': ''}
2025-08-08 01:59:02,140 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:59:02,154 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of data or notable achievements for girls at ITC University in their programs or courses?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights regarding the factors contributing to the lack of data or notable achievements for girls at ITC University in their programs or courses. The provided schema contains structured data about institutions, students, programs, and various academic records, but it does not include qualitative data or insights that would allow for an analysis of the factors affecting the achievements of girls specifically. The schema lacks information on social, cultural, or institutional factors that could influence these outcomes, making it impossible to answer the question based solely on the available data.', 'feedback': ''}
2025-08-08 01:59:02,154 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:59:02,154 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:59:02,154 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of data or notable achievements for girls at ITC University in their programs or courses?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights regarding the factors contributing to the lack of data or notable achievements for girls at ITC University in their programs or courses. The provided schema contains structured data about institutions, students, programs, and various academic records, but it does not include qualitative data or insights that would allow for an analysis of the factors affecting the achievements of girls specifically. The schema lacks information on social, cultural, or institutional factors that could influence these outcomes, making it impossible to answer the question based solely on the available data.', 'feedback': ''}
2025-08-08 01:59:08,032 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:59:11,046 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:59:11,054 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of data or notable achievements for girls at ITC University in their programs or courses?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights regarding the factors contributing to the lack of data or notable achievements for girls at ITC University in their programs or courses. The provided schema contains structured data about institutions, students, programs, and various academic records, but it does not include qualitative data or insights that would help answer such a question. The schema lacks information on social, cultural, or institutional factors that could influence the achievements of female students, which are necessary to provide a comprehensive answer.', 'feedback': ''}
2025-08-08 01:59:11,054 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:59:11,054 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:59:11,054 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of data or notable achievements for girls at ITC University in their programs or courses?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights regarding the factors contributing to the lack of data or notable achievements for girls at ITC University in their programs or courses. The provided schema contains structured data about institutions, students, programs, and various academic records, but it does not include qualitative data or insights that would help answer such a question. The schema lacks information on social, cultural, or institutional factors that could influence the achievements of female students, which are necessary to provide a comprehensive answer.', 'feedback': ''}
2025-08-08 01:59:14,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:59:17,297 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:59:17,310 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of data or notable achievements for girls at ITC University in their programs or courses?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights regarding the factors contributing to the lack of data or notable achievements for girls at ITC University in their programs or courses. The provided schema contains structured data about institutions, students, programs, and various academic records, but it does not include qualitative data or insights that would help answer such a question. The schema lacks information on social, cultural, or institutional factors that could influence the achievements of female students, making it impossible to derive a complete answer from the available data.', 'feedback': ''}
2025-08-08 01:59:17,310 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:59:17,311 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:59:17,311 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of data or notable achievements for girls at ITC University in their programs or courses?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights regarding the factors contributing to the lack of data or notable achievements for girls at ITC University in their programs or courses. The provided schema contains structured data about institutions, students, programs, and various academic records, but it does not include qualitative data or insights that would help answer such a question. The schema lacks information on social, cultural, or institutional factors that could influence the achievements of female students, making it impossible to derive a complete answer from the available data.', 'feedback': ''}
2025-08-08 01:59:20,061 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:59:22,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:59:22,841 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of data or notable achievements for girls at ITC University in their programs or courses?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights regarding the factors contributing to the lack of data or notable achievements for girls at ITC University in their programs or courses. The provided schema contains structured data about institutions, students, programs, and various academic records, but it does not include qualitative data or insights that would help answer such a subjective question. The schema lacks information on social, cultural, or institutional factors that could influence the achievements of female students, making it impossible to provide a complete answer.', 'feedback': ''}
2025-08-08 01:59:22,841 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 01:59:22,841 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 01:59:22,841 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might contribute to the lack of data or notable achievements for girls at ITC University in their programs or courses?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights regarding the factors contributing to the lack of data or notable achievements for girls at ITC University in their programs or courses. The provided schema contains structured data about institutions, students, programs, and various academic records, but it does not include qualitative data or insights that would help answer such a subjective question. The schema lacks information on social, cultural, or institutional factors that could influence the achievements of female students, making it impossible to provide a complete answer.', 'feedback': ''}
2025-08-08 01:59:22,841 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 01:59:22,842 - root - INFO - [{'sex': 'F', 'average_assessment_total': 36.26, 'average_exam_total': 36.87, 'average_finalscore': 68.72, 'total_students': 58}, {'sex': 'M', 'average_assessment_total': 10.92, 'average_exam_total': 13.44, 'average_finalscore': 54.86, 'total_students': 71}]
2025-08-08 01:59:22,842 - root - INFO - 'No results'
2025-08-08 01:59:22,842 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 1
2025-08-08 01:59:22,842 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 1
2025-08-08 01:59:22,842 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 01:59:33,049 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:59:33,056 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 01:59:42,146 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:59:42,155 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:59:42,156 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 01:59:42,156 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:59:42,156 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/2
2025-08-08 01:59:42,156 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:59:42,156 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:59:42,156 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:59:42,156 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 01:59:42,156 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:59:42,156 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:59:42,156 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 01:59:42,156 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:59:42,156 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the academic results of girls compare to those of boys at ITC University?...
2025-08-08 01:59:42,156 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, the academic results show a significant difference between girls and boys. Girls ...
2025-08-08 01:59:42,157 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: graph
2025-08-08 01:59:42,157 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': 'F', 'average_assessment_total': 36.26, 'average_exam_total': 36.87, 'average_finalscore': ...
2025-08-08 01:59:42,157 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: academic_performance_by_gender
2025-08-08 01:59:42,157 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 01:59:42,157 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 01:59:42,157 - celery.redirected - WARNING - [{'sex': 'F', 'average_assessment_total': 36.26, 'average_exam_total': 36.87, 'average_finalscore': 68.72, 'total_students': 58}, {'sex': 'M', 'average_assessment_total': 10.92, 'average_exam_total': 13.44, 'average_finalscore': 54.86, 'total_students': 71}]
2025-08-08 01:59:42,157 - celery.redirected - WARNING - ================================= 
2025-08-08 01:59:42,157 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: academic_performance_by_gender
2025-08-08 01:59:42,157 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 01:59:42,157 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:59:42,157 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/2
2025-08-08 01:59:42,157 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:59:42,157 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:59:42,157 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:59:42,157 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 01:59:42,157 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:59:42,158 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:59:42,158 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 01:59:42,158 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:59:42,158 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are girls at ITC University excelling in?...
2025-08-08 01:59:42,158 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are currently no available data or results indicating specific programs or cou...
2025-08-08 01:59:42,158 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:59:42,158 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:59:42,158 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_programs_performance_itc_university
2025-08-08 01:59:42,158 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:59:42,158 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:59:42,158 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:59:42,158 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 2
2025-08-08 01:59:42,159 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:59:42,159 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 01:59:42,159 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:59:42,159 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 2 documents
2025-08-08 01:59:42,159 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 01:59:42,159 - UPSERT_DOCS - INFO -     Content: Question: How do the academic results of girls compare to those of boys at ITC University?
Answer: A...
2025-08-08 01:59:42,159 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:58:57.340679+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-08 01:59:42,159 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 01:59:42,159 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are girls at ITC University excelling in?
Answer: It appears that...
2025-08-08 01:59:42,159 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:59:22.841502+00:00', 'data_returned': False}
2025-08-08 01:59:42,159 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 1/2
2025-08-08 01:59:42,347 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:200 duration:0.188s]
2025-08-08 01:59:43,742 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:59:53,806 - elastic_transport.transport - INFO - PUT http://54.246.247.31:9200/_bulk?refresh=true [status:N/A duration:10.002s]
2025-08-08 01:59:53,807 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://54.246.247.31:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-08 01:59:53,807 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: Connection timed out
2025-08-08 01:59:53,807 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:59:53,808 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION ERROR
2025-08-08 01:59:53,808 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:59:53,808 - REPORT_PIPELINE_ERROR - ERROR - ❌ Report generation failed: Error generating report: Connection timed out
2025-08-08 01:59:53,808 - REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_015734.log
2025-08-08 01:59:53,811 - celery.app.trace - INFO - Task generate_report[97edf0c8-d804-42a6-8a68-ed01ed0e5019] succeeded in 139.281847708s: {'error': 'Error generating report: Connection timed out'}

2025-08-07 16:40:46,373 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250807_164046.log
2025-08-07 16:40:46,374 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:40:46,374 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 537b218a-e535-40e8-b03f-74ea76ab9ead
2025-08-07 16:40:46,374 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:40:46,374 - REPORT_REQUEST - INFO - 📝 Original Question: 'What is the institution with the most students?'
2025-08-07 16:40:46,374 - REPORT_REQUEST - INFO - 🆔 Task ID: 537b218a-e535-40e8-b03f-74ea76ab9ead
2025-08-07 16:40:46,374 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-07T16:40:46.374314
2025-08-07 16:40:46,508 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-07 16:40:46,508 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-07 16:40:46,673 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.165s]
2025-08-07 16:40:46,673 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:40:47,036 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.362s]
2025-08-07 16:40:47,036 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-07 16:40:47,036 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-07 16:40:56,413 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:40:56,433 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-07 16:41:00,423 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:00,437 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-07 16:41:02,499 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:02,518 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-07 16:41:02,518 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:41:02,518 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-07 16:41:02,518 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:41:02,518 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 0
2025-08-07 16:41:02,518 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-07 16:41:02,518 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-07 16:41:02,518 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 0
2025-08-07 16:41:02,518 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-07 16:41:08,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:08,963 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-07 16:41:20,309 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:20,316 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:41:20,316 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-07 16:41:20,316 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:41:20,316 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 0
2025-08-07 16:41:20,316 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:41:20,316 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-07 16:41:20,316 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:41:20,316 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 0 documents
2025-08-07 16:41:20,317 - UPSERT_DOCS - WARNING - ⚠️ No documents to upsert!
2025-08-07 16:41:20,463 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.146s]
2025-08-07 16:41:20,463 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 0
2025-08-07 16:41:20,611 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.147s]
2025-08-07 16:41:20,612 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:41:20,753 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.140s]
2025-08-07 16:41:20,753 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (0):
2025-08-07 16:41:20,754 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 9
2025-08-07 16:41:20,754 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-07 16:41:20,754 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:41:20,755 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-07 16:41:20,755 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:41:20,755 - REPORT_PIPELINE - INFO - ✍️ Writing section 1/9: ## I. Introduction  
The purpose of this report is to identify the institution with the highest stud...
2025-08-07 16:41:21,679 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:21,709 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:41:21,709 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 16:41:21,709 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:41:21,710 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'highest student enrollment institution analysis'
2025-08-07 16:41:21,710 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 16:41:21,710 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 16:41:21,842 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-07 16:41:21,842 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 16:41:21,977 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-07 16:41:21,978 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:41:22,112 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-07 16:41:22,113 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 16:41:22,423 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.310s]
2025-08-07 16:41:22,424 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 16:41:23,566 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 16:41:23,898 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.269s]
2025-08-07 16:41:23,899 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 16:41:23,899 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 16:41:27,595 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:27,631 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 335 characters
2025-08-07 16:41:27,632 - REPORT_PIPELINE - INFO - ✍️ Writing section 2/9: ## II. Definition of Terms  
- Institution  
- Student Enrollment  
- Types of Institutions (e.g., u...
2025-08-07 16:41:28,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:28,914 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:41:28,914 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 16:41:28,914 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:41:28,914 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'definition of terms institution student enrollment types of institutions'
2025-08-07 16:41:28,914 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 16:41:28,914 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 16:41:29,061 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.147s]
2025-08-07 16:41:29,062 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 16:41:29,240 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.179s]
2025-08-07 16:41:29,241 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:41:29,384 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.143s]
2025-08-07 16:41:29,384 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 16:41:29,569 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.185s]
2025-08-07 16:41:29,569 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 16:41:31,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 16:41:31,803 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.163s]
2025-08-07 16:41:31,803 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 16:41:31,803 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 16:41:36,857 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:36,861 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1364 characters
2025-08-07 16:41:36,862 - REPORT_PIPELINE - INFO - ✍️ Writing section 3/9: ## III. Methodology  
- Overview of Data Collection  
- Description of Interview Process  
- Limitat...
2025-08-07 16:41:37,881 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:37,886 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:41:37,886 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 16:41:37,886 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:41:37,886 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology Data Collection Interview Process Limitations'
2025-08-07 16:41:37,886 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 16:41:37,886 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 16:41:38,024 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.138s]
2025-08-07 16:41:38,025 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 16:41:38,164 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.139s]
2025-08-07 16:41:38,165 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:41:38,339 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.173s]
2025-08-07 16:41:38,339 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 16:41:38,473 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-07 16:41:38,474 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 16:41:39,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 16:41:39,606 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.163s]
2025-08-07 16:41:39,606 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 16:41:39,607 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 16:41:44,383 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:44,407 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1388 characters
2025-08-07 16:41:44,407 - REPORT_PIPELINE - INFO - ✍️ Writing section 4/9: ## IV. Findings  
- Summary of Interview Insights  
- Key Themes Identified  
  - Institutional Size...
2025-08-07 16:41:45,150 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:45,155 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:41:45,155 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 16:41:45,155 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:41:45,155 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Interview findings themes institutional size enrollment trends comparison'
2025-08-07 16:41:45,155 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 16:41:45,155 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 16:41:45,298 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.143s]
2025-08-07 16:41:45,298 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 16:41:45,434 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-07 16:41:45,434 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:41:45,602 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.167s]
2025-08-07 16:41:45,602 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 16:41:45,731 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-07 16:41:45,731 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 16:41:46,890 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 16:41:47,137 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.144s]
2025-08-07 16:41:47,137 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 16:41:47,137 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 16:41:50,893 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:50,897 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 1247 characters
2025-08-07 16:41:50,897 - REPORT_PIPELINE - INFO - ✍️ Writing section 5/9: ## V. Analysis  
- Trends Across Institutions  
  - Growth in Enrollment  
  - Factors Influencing S...
2025-08-07 16:41:51,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:41:51,945 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:41:51,945 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 16:41:51,945 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:41:51,945 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Enrollment trends institutions analysis'
2025-08-07 16:41:51,945 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 16:41:51,946 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 16:41:52,079 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-07 16:41:52,080 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 16:41:52,208 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-07 16:41:52,209 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:41:52,377 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.168s]
2025-08-07 16:41:52,377 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 16:41:52,513 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-07 16:41:52,513 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 16:41:54,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 16:41:54,749 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.133s]
2025-08-07 16:41:54,750 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 16:41:54,750 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 16:42:00,819 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:42:00,824 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1842 characters
2025-08-07 16:42:00,825 - REPORT_PIPELINE - INFO - ✍️ Writing section 6/9: ## VI. Factors Influencing Enrollment  
- Institutional Reputation  
- Program Offerings  
- Geograp...
2025-08-07 16:42:02,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:42:02,210 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:42:02,210 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 16:42:02,210 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:42:02,210 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Factors Influencing Enrollment'
2025-08-07 16:42:02,210 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 16:42:02,210 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 16:42:02,345 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-07 16:42:02,345 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 16:42:02,476 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-07 16:42:02,477 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:42:02,610 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-07 16:42:02,611 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 16:42:02,798 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.187s]
2025-08-07 16:42:02,798 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 16:42:03,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 16:42:03,434 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.190s]
2025-08-07 16:42:03,434 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 16:42:03,434 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 16:42:09,419 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:42:09,424 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 1751 characters
2025-08-07 16:42:09,424 - REPORT_PIPELINE - INFO - ✍️ Writing section 7/9: ## VII. Conclusion  
- Summary of Key Findings  
- Implications of Findings  
- Recommendations for ...
2025-08-07 16:42:10,294 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:42:10,299 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:42:10,299 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 16:42:10,299 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:42:10,299 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion Key Findings Implications Recommendations'
2025-08-07 16:42:10,299 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 16:42:10,299 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 16:42:10,487 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.187s]
2025-08-07 16:42:10,487 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 16:42:10,641 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.153s]
2025-08-07 16:42:10,641 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:42:10,779 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.137s]
2025-08-07 16:42:10,780 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 16:42:10,996 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.216s]
2025-08-07 16:42:10,996 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 16:42:11,784 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 16:42:11,947 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.160s]
2025-08-07 16:42:11,948 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 16:42:11,948 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 16:42:16,063 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:42:16,078 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 1140 characters
2025-08-07 16:42:16,079 - REPORT_PIPELINE - INFO - ✍️ Writing section 8/9: ## VIII. References  
- List of Interviewees  
- Additional Resources and Literature...
2025-08-07 16:42:17,251 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:42:17,309 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:42:17,310 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 16:42:17,310 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:42:17,310 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'References Interviewees Resources Literature'
2025-08-07 16:42:17,310 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 16:42:17,310 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 16:42:17,447 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.137s]
2025-08-07 16:42:17,448 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 16:42:17,624 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.176s]
2025-08-07 16:42:17,625 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:42:17,801 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.176s]
2025-08-07 16:42:17,802 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 16:42:17,934 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-07 16:42:17,934 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 16:42:19,047 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 16:42:19,205 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.154s]
2025-08-07 16:42:19,206 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 16:42:19,206 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 16:42:23,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:42:23,786 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 682 characters
2025-08-07 16:42:23,787 - REPORT_PIPELINE - INFO - ✍️ Writing section 9/9: ## IX. Appendices  
- Interview Transcripts/Summaries  
- Data Tables and Charts...
2025-08-07 16:42:24,586 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:42:24,596 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:42:24,596 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-07 16:42:24,596 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:42:24,597 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'appendices interview transcripts data tables charts'
2025-08-07 16:42:24,597 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-07 16:42:24,597 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-07 16:42:24,747 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.150s]
2025-08-07 16:42:24,748 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 0
2025-08-07 16:42:24,918 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.170s]
2025-08-07 16:42:24,918 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-07 16:42:25,093 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.174s]
2025-08-07 16:42:25,094 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 0
2025-08-07 16:42:25,220 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-07 16:42:25,221 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-07 16:42:25,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-07 16:42:25,776 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.137s]
2025-08-07 16:42:25,776 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-07 16:42:25,777 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-07 16:42:28,418 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:42:28,504 - REPORT_PIPELINE - INFO -   ✅ Section 9 completed: 602 characters
2025-08-07 16:42:29,243 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-07 16:42:29,246 - app.chains.composite.report_pipeline - INFO - Match not found
2025-08-07 16:42:29,247 - app.chains.composite.report_pipeline - INFO - Match not found
2025-08-07 16:42:29,247 - app.chains.composite.report_pipeline - INFO - Match not found
2025-08-07 16:42:29,247 - app.chains.composite.report_pipeline - INFO - Match not found
2025-08-07 16:42:29,247 - app.chains.composite.report_pipeline - INFO - Match not found
2025-08-07 16:42:29,247 - app.chains.composite.report_pipeline - INFO - Match not found
2025-08-07 16:42:29,247 - app.chains.composite.report_pipeline - INFO - Match not found
2025-08-07 16:42:29,247 - app.chains.composite.report_pipeline - INFO - Match not found
2025-08-07 16:42:29,247 - app.chains.composite.report_pipeline - INFO - Match not found
2025-08-07 16:42:29,247 - app.chains.composite.report_pipeline - INFO - Match not found
2025-08-07 16:42:29,247 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-07 16:42:29,247 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-07 16:42:29,247 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-07 16:42:29,247 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-07 16:42:29,248 - REPORT_PIPELINE - INFO - 📊 Final report sections: 10
2025-08-07 16:42:29,248 - REPORT_PIPELINE - INFO - 📈 Extended data items: 0
2025-08-07 16:42:29,248 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250807_164046.log
2025-08-07 16:42:29,250 - celery.app.trace - INFO - Task generate_report[8463d18d-970f-4852-953e-486cec8df4e9] succeeded in 102.87962462499854s: {'outline': '# Report Outline: Investigating the Institution with the Most Students

## I. Introduction  
The purpose of this report is to identify the institution with the highest student enrollment and to analyze the factors influencing these numbers. The key finding indicates that the institution with the most students is a large public university, which has seen significant growth in enrollment over recent years.

## II. Definition of Terms  
- Institution  
- Student Enrollment  
- Types of Institutions (e.g., universities, colleges, online institutions)  

## III. Methodology  
- Overview of Data Collection  
- Description of Interview Process  
- Limitations of the Study  

## IV. Findings  
- Summary of Interview Insights  
- Key Themes Identified  
  - Institutional Size  
  - Enrollment Trends  
  - Comparison of Institutions  

## V. Analysis  
- Trends Across Institutions  
  - Growth in Enrollment  
  - Factors Influencing Student Numbers  
- Contrasts Between Institutions  
  - Public vs....', 's...', ...}

2025-08-08 14:53:22,385 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_145322.log
2025-08-08 14:53:22,385 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 14:53:22,385 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 13d1213c-6e55-4432-9212-2f0ea1d068a8
2025-08-08 14:53:22,385 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 14:53:22,385 - REPORT_REQUEST - INFO - 📝 Original Question: 'Which institution has the most students?'
2025-08-08 14:53:22,385 - REPORT_REQUEST - INFO - 🆔 Task ID: 13d1213c-6e55-4432-9212-2f0ea1d068a8
2025-08-08 14:53:22,385 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T14:53:22.385801
2025-08-08 14:53:32,389 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:N/A duration:10.004s]
2025-08-08 14:53:32,390 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://54.246.247.31:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-08 14:53:32,390 - ELASTICSEARCH_STATE - ERROR - ❌ Error checking Elasticsearch state: Connection timed out
2025-08-08 14:53:32,391 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 14:53:32,391 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-08 14:53:32,391 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 14:53:32,391 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: Which institution has the most students?
2025-08-08 14:53:32,391 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-08 14:53:32,391 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-08 14:53:41,749 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:41,773 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 14:53:46,920 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:46,937 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-08 14:53:49,744 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:49,828 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:49,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:49,855 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:50,053 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:50,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:50,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:50,251 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:50,646 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:51,556 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:51,626 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-08 14:53:54,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:54,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:55,273 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:55,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:55,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:55,671 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:57,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:58,054 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:58,347 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:58,397 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:58,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:58,877 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:58,947 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:59,218 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:59,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:59,854 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:53:59,934 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:00,088 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:03,894 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:04,696 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:05,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:06,043 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:06,060 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:06,449 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:08,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:08,399 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the demographic breakdown of students at the institution with the most students?', 'sql': 'SELECT sex, COUNT(*) AS student_count FROM students WHERE institution_id = (SELECT institution_id FROM students GROUP BY institution_id ORDER BY COUNT(*) DESC LIMIT 1) GROUP BY sex;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by using a subquery that groups students by institution_id, counts them, and orders the results in descending order to get the top institution. It then counts the number of students by sex for that institution, which provides the demographic breakdown as requested in the question.', 'feedback': 'The SQL query is well-structured and accurately answers the question. However, it could be enhanced by including additional demographic factors, such as age or nationality, if those were relevant to the breakdown.'}
2025-08-08 14:54:08,399 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:08,399 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 14:54:11,202 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:11,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:11,858 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:12,503 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:12,514 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the retention rate of students at the institution with the most students?', 'sql': 'WITH student_counts AS (SELECT institution_id, COUNT(*) AS total_students FROM core.students GROUP BY institution_id), retained_students AS (SELECT institution_id, COUNT(*) AS retained FROM core.student_programs WHERE current = TRUE GROUP BY institution_id) SELECT sc.institution_id, (COALESCE(rs.retained, 0) * 1.0 / sc.total_students) * 100 AS retention_rate FROM student_counts sc LEFT JOIN retained_students rs ON sc.institution_id = rs.institution_id WHERE sc.total_students = (SELECT MAX(total_students) FROM student_counts);', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by counting the total number of students per institution and then calculating the retention rate for that institution. It uses a common table expression (CTE) to first gather the total number of students and the number of retained students (those who are currently enrolled). The final selection computes the retention rate as a percentage of retained students over total students for the institution with the maximum student count. The logic aligns with the question asked.', 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly naming the institution in the final output for clarity, such as including the institution's name from the institutions table."}
2025-08-08 14:54:12,515 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:12,515 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 14:54:13,772 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:15,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:15,586 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:17,618 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:17,874 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:18,400 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:19,489 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:19,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:19,753 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:20,557 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:20,667 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:20,901 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:21,974 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:22,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:22,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:23,639 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:24,912 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:24,982 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:25,193 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:25,544 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:26,104 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:27,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:28,669 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:28,692 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national averages in terms of gender and other demographics such as ethnicity or socioeconomic status?', 'answerable': False, 'reasoning': "The question asks for a demographic breakdown of an institution compared to national averages in terms of gender, ethnicity, and socioeconomic status. However, the provided schema does not contain any tables or fields that specifically capture demographic data such as ethnicity or socioeconomic status. While there are fields related to gender (e.g., in the 'students' table), there is no comprehensive data on ethnicity or socioeconomic status available in the schema. Therefore, the question cannot be answered completely.", 'feedback': ''}
2025-08-08 14:54:28,692 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:28,692 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:28,693 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national averages in terms of gender and other demographics such as ethnicity or socioeconomic status?', 'answerable': False, 'reasoning': "The question asks for a demographic breakdown of an institution compared to national averages in terms of gender, ethnicity, and socioeconomic status. However, the provided schema does not contain any tables or fields that specifically capture demographic data such as ethnicity or socioeconomic status. While there are fields related to gender (e.g., in the 'students' table), there is no comprehensive data on ethnicity or socioeconomic status available in the schema. Therefore, the question cannot be answered completely.", 'feedback': ''}
2025-08-08 14:54:29,047 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:29,057 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total number of students enrolled at the institution with the most students?', 'sql': 'SELECT COUNT(*) AS total_students\nFROM students\nWHERE institution_id = (\n    SELECT institution_id\n    FROM students\n    GROUP BY institution_id\n    ORDER BY COUNT(*) DESC\n    LIMIT 1\n);', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by grouping the students by institution_id, counting the number of students in each group, and ordering the results in descending order. The subquery retrieves the institution_id of the institution with the highest count of students, and the outer query counts the total number of students for that institution. Therefore, the query accurately answers the question posed.', 'feedback': 'The SQL query is well-structured and effectively answers the question. No changes are necessary, but it could be beneficial to add a comment in the SQL to clarify the purpose of the subquery for future reference.'}
2025-08-08 14:54:29,057 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:29,057 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 14:54:29,531 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:29,791 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:30,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:31,574 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:31,780 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:32,127 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:32,163 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:32,183 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 14:54:32,183 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:32,183 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:32,183 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 14:54:32,857 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:33,142 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:34,482 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:35,773 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:35,801 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national averages in terms of gender and other demographics such as ethnicity or socioeconomic status?', 'answerable': False, 'reasoning': "The schema does not contain sufficient information to directly answer the question regarding the demographic breakdown of the institution compared to national averages. While there are tables related to students, such as 'students' and 'nationalities', which may provide some demographic data, there is no explicit data on ethnicity or socioeconomic status. Additionally, there is no reference to national averages for comparison. Therefore, without additional data or tables that provide national demographic averages, this question cannot be answered.", 'feedback': ''}
2025-08-08 14:54:35,801 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:35,801 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:35,801 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national averages in terms of gender and other demographics such as ethnicity or socioeconomic status?', 'answerable': False, 'reasoning': "The schema does not contain sufficient information to directly answer the question regarding the demographic breakdown of the institution compared to national averages. While there are tables related to students, such as 'students' and 'nationalities', which may provide some demographic data, there is no explicit data on ethnicity or socioeconomic status. Additionally, there is no reference to national averages for comparison. Therefore, without additional data or tables that provide national demographic averages, this question cannot be answered.", 'feedback': ''}
2025-08-08 14:54:35,910 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:36,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:36,268 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How has the student enrollment at the institution with the most students changed over the past few years?', 'sql': 'SELECT ay.start_year, COUNT(s.id) AS student_count\nFROM students s\nJOIN institutions i ON s.institution_id = i.id\nJOIN academic_years ay ON ay.institution_id = i.id\nWHERE i.id = (\n    SELECT institution_id\n    FROM students\n    GROUP BY institution_id\n    ORDER BY COUNT(id) DESC\n    LIMIT 1\n)\nGROUP BY ay.start_year\nORDER BY ay.start_year;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and ordering them in descending order. It then retrieves the enrollment data for that institution over the years by joining the 'students', 'institutions', and 'academic_years' tables. The query groups the results by the start year of the academic years, which allows it to show how student enrollment has changed over the years for the institution with the most students.", 'feedback': "The question could be clarified by specifying what is meant by 'changed'—whether it refers to increases, decreases, or overall trends. Additionally, the SQL could be improved by ensuring that the academic years considered are relevant to the current context, perhaps by adding a filter for recent years."}
2025-08-08 14:54:36,268 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:36,268 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 14:54:36,571 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:36,845 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:36,858 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 14:54:36,858 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:36,858 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:36,858 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 14:54:37,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:37,662 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the student population of the institution with the most students compare to other institutions?', 'sql': "WITH student_counts AS (\n    SELECT institution_id, COUNT(*) AS student_count\n    FROM core.students\n    GROUP BY institution_id\n), max_institution AS (\n    SELECT institution_id, student_count\n    FROM student_counts\n    ORDER BY student_count DESC\n    LIMIT 1\n)\nSELECT \n    i.name AS institution_name,\n    sc.student_count,\n    CASE \n        WHEN sc.student_count = mi.student_count THEN 'Most Students'\n        ELSE 'Other'\n    END AS comparison\nFROM student_counts sc\nJOIN max_institution mi ON 1=1\nJOIN auth.institutions i ON sc.institution_id = i.id\nORDER BY sc.student_count DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and then selecting the one with the highest count. It also compares the student counts of all institutions against this maximum, labeling them as 'Most Students' or 'Other'. The use of a common table expression (CTE) to first calculate student counts and then find the maximum institution is a valid approach. The final selection includes the institution name and the comparison, which directly addresses the question.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating the comparison in a more detailed manner, such as including the percentage difference or a more descriptive comparison metric. Additionally, ensure that the schema references are correct (e.g., 'core.students' vs. 'students') to avoid confusion."}
2025-08-08 14:54:37,663 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:37,663 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 14:54:38,579 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:38,592 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific data or metrics that would allow for an analysis of factors influencing enrollment numbers, such as marketing strategies, community engagement, or student satisfaction. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 14:54:38,592 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:38,592 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:38,592 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific data or metrics that would allow for an analysis of factors influencing enrollment numbers, such as marketing strategies, community engagement, or student satisfaction. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-08 14:54:38,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:39,357 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:39,398 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:40,082 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:40,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:40,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:41,057 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:41,110 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:41,125 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national averages in terms of gender and other demographics such as ethnicity or socioeconomic status?', 'answerable': False, 'reasoning': "The question asks for a demographic breakdown of an institution compared to national averages in terms of gender, ethnicity, and socioeconomic status. However, the provided schema does not contain any tables or fields that specifically store demographic data such as ethnicity or socioeconomic status. While there are fields related to gender (e.g., in the 'students' table), there is no comprehensive data on ethnicity or socioeconomic status. Therefore, the question cannot be answered completely from the schema.", 'feedback': ''}
2025-08-08 14:54:41,125 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:41,125 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:41,125 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national averages in terms of gender and other demographics such as ethnicity or socioeconomic status?', 'answerable': False, 'reasoning': "The question asks for a demographic breakdown of an institution compared to national averages in terms of gender, ethnicity, and socioeconomic status. However, the provided schema does not contain any tables or fields that specifically store demographic data such as ethnicity or socioeconomic status. While there are fields related to gender (e.g., in the 'students' table), there is no comprehensive data on ethnicity or socioeconomic status. Therefore, the question cannot be answered completely from the schema.", 'feedback': ''}
2025-08-08 14:54:41,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:41,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:41,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:41,716 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:41,742 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 14:54:41,743 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:41,743 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:41,743 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 14:54:42,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:43,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:43,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:43,701 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific qualitative data or analysis regarding the reasons for enrollment numbers. To answer this question, one would need insights from external sources, surveys, or qualitative research, which are not captured in the schema.', 'feedback': ''}
2025-08-08 14:54:43,701 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:43,701 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:43,702 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific qualitative data or analysis regarding the reasons for enrollment numbers. To answer this question, one would need insights from external sources, surveys, or qualitative research, which are not captured in the schema.', 'feedback': ''}
2025-08-08 14:54:44,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:44,428 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:44,772 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:46,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:46,474 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:46,710 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:46,725 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe have contributed to the stability in enrollment numbers at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to stability in enrollment numbers at an institution. However, the provided schema does not contain any specific data or metrics related to enrollment trends, stability, or contributing factors. While there are tables related to students, programs, and admissions, they do not provide insights into the reasons behind enrollment stability. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 14:54:46,725 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:46,726 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:46,726 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe have contributed to the stability in enrollment numbers at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to stability in enrollment numbers at an institution. However, the provided schema does not contain any specific data or metrics related to enrollment trends, stability, or contributing factors. While there are tables related to students, programs, and admissions, they do not provide insights into the reasons behind enrollment stability. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-08 14:54:47,247 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:47,269 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 14:54:47,269 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:47,269 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:47,269 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What specific strategies or programs does this institution implement to achieve such a high retention rate?', 'answerable': False, 'reasoning': 'The question asks for specific strategies or programs related to retention rates at an institution. However, the provided schema does not contain any tables or fields that explicitly detail retention strategies or programs. While there are tables related to students, programs, and various institutional activities, none of them seem to directly address retention strategies or programs. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-08 14:54:47,392 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:48,096 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:48,111 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national averages in terms of gender and other demographics such as ethnicity or socioeconomic status?', 'answerable': False, 'reasoning': "The question asks for a demographic breakdown of an institution compared to national averages in terms of gender, ethnicity, and socioeconomic status. However, the provided schema does not contain any tables or fields that specifically store demographic data such as ethnicity or socioeconomic status. While there are fields related to gender (e.g., in the 'students' table), there is no comprehensive data on ethnicity or socioeconomic status. Therefore, the question cannot be answered completely from the schema.", 'feedback': ''}
2025-08-08 14:54:48,112 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:48,112 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:48,112 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How does the demographic breakdown of this institution compare to national averages in terms of gender and other demographics such as ethnicity or socioeconomic status?', 'answerable': False, 'reasoning': "The question asks for a demographic breakdown of an institution compared to national averages in terms of gender, ethnicity, and socioeconomic status. However, the provided schema does not contain any tables or fields that specifically store demographic data such as ethnicity or socioeconomic status. While there are fields related to gender (e.g., in the 'students' table), there is no comprehensive data on ethnicity or socioeconomic status. Therefore, the question cannot be answered completely from the schema.", 'feedback': ''}
2025-08-08 14:54:48,972 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:48,992 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any data or attributes that directly relate to the reasons or factors influencing student population sizes, such as demographic data, marketing strategies, or institutional policies. The schema primarily contains tables related to student records, courses, and administrative data, but lacks qualitative insights or comparative metrics that would allow for a comprehensive answer to the question.", 'feedback': ''}
2025-08-08 14:54:48,993 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:48,993 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:48,993 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any data or attributes that directly relate to the reasons or factors influencing student population sizes, such as demographic data, marketing strategies, or institutional policies. The schema primarily contains tables related to student records, courses, and administrative data, but lacks qualitative insights or comparative metrics that would allow for a comprehensive answer to the question.", 'feedback': ''}
2025-08-08 14:54:49,748 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:50,197 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:50,221 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or available in the provided schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific qualitative data or analysis regarding the reasons for enrollment numbers. To answer this question, one would need insights or data that are not present in the schema, such as surveys, marketing strategies, or external factors influencing enrollment.', 'feedback': ''}
2025-08-08 14:54:50,221 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:50,221 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:50,221 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which is not directly quantifiable or available in the provided schema. The schema contains various tables related to institutions, students, programs, and admissions, but it does not include specific qualitative data or analysis regarding the reasons for enrollment numbers. To answer this question, one would need insights or data that are not present in the schema, such as surveys, marketing strategies, or external factors influencing enrollment.', 'feedback': ''}
2025-08-08 14:54:51,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:52,766 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:52,780 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe have contributed to the stability in enrollment numbers at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to stability in enrollment numbers, which is a qualitative assessment. The provided schema contains data about institutions, students, programs, and various related entities, but it does not include any specific metrics, historical trends, or qualitative data that would allow for an analysis of enrollment stability. To answer this question, one would need access to historical enrollment data, external factors affecting enrollment, and possibly qualitative insights from institutional reports or surveys, none of which are present in the schema.', 'feedback': ''}
2025-08-08 14:54:52,781 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:52,781 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:52,781 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe have contributed to the stability in enrollment numbers at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to stability in enrollment numbers, which is a qualitative assessment. The provided schema contains data about institutions, students, programs, and various related entities, but it does not include any specific metrics, historical trends, or qualitative data that would allow for an analysis of enrollment stability. To answer this question, one would need access to historical enrollment data, external factors affecting enrollment, and possibly qualitative insights from institutional reports or surveys, none of which are present in the schema.', 'feedback': ''}
2025-08-08 14:54:53,190 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:53,416 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:53,428 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are most popular at the institution with the most students?', 'sql': "WITH InstitutionCounts AS (  SELECT institution_id, COUNT(id) AS student_count  FROM students  WHERE status = 'active'  GROUP BY institution_id  ORDER BY student_count DESC  LIMIT 1) SELECT p.long_name AS program_name, COUNT(s.id) AS student_count FROM students s JOIN programs p ON s.institution_id = p.institution_id WHERE s.status = 'active' AND s.institution_id = (SELECT institution_id FROM InstitutionCounts) GROUP BY p.id ORDER BY student_count DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most active students by counting the number of students per institution and selecting the one with the highest count. It then retrieves the programs offered by that institution and counts the number of active students enrolled in each program. The results are grouped by program and ordered by the number of students, which aligns with the question's request for the most popular programs.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating the institution's name or ID in the final output for clarity, as the question implies an interest in the context of the institution as well."}
2025-08-08 14:54:53,429 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:53,429 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 14:54:55,396 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:55,663 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:55,698 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which cannot be directly answered using the provided database schema. The schema contains structured data about institutions, students, programs, and related entities, but it does not include qualitative insights or analysis about enrollment factors. To answer this question, one would need to analyze external data or conduct surveys, which are not represented in the schema.', 'feedback': ''}
2025-08-08 14:54:55,698 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:55,698 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:55,699 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to high enrollment numbers at an institution, which cannot be directly answered using the provided database schema. The schema contains structured data about institutions, students, programs, and related entities, but it does not include qualitative insights or analysis about enrollment factors. To answer this question, one would need to analyze external data or conduct surveys, which are not represented in the schema.', 'feedback': ''}
2025-08-08 14:54:57,595 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:57,611 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any data or attributes that would allow for a comparative analysis of student populations or the factors influencing them. The schema includes tables related to institutions, students, and various attributes, but it lacks qualitative data or metrics that would help identify or analyze the reasons behind differences in student populations.", 'feedback': ''}
2025-08-08 14:54:57,611 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:57,612 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:57,612 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. However, the provided schema does not contain any data or attributes that would allow for a comparative analysis of student populations or the factors influencing them. The schema includes tables related to institutions, students, and various attributes, but it lacks qualitative data or metrics that would help identify or analyze the reasons behind differences in student populations.", 'feedback': ''}
2025-08-08 14:54:57,917 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:54:57,934 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe have contributed to the stability in enrollment numbers at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to stability in enrollment numbers, which is a qualitative assessment. The provided schema contains data about institutions, students, programs, and various related entities, but it does not include any specific metrics, historical trends, or qualitative factors that would allow for a comprehensive analysis of enrollment stability. The schema lacks data on external factors, institutional policies, or historical enrollment trends that would be necessary to answer the question.', 'feedback': ''}
2025-08-08 14:54:57,934 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:54:57,934 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:54:57,934 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe have contributed to the stability in enrollment numbers at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to stability in enrollment numbers, which is a qualitative assessment. The provided schema contains data about institutions, students, programs, and various related entities, but it does not include any specific metrics, historical trends, or qualitative factors that would allow for a comprehensive analysis of enrollment stability. The schema lacks data on external factors, institutional policies, or historical enrollment trends that would be necessary to answer the question.', 'feedback': ''}
2025-08-08 14:54:59,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:00,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:02,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:02,479 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. This requires qualitative insights and contextual information that is not present in the database schema. The schema contains data about institutions and students, but it does not provide any direct metrics or qualitative factors that would explain the differences in student populations, such as marketing strategies, academic offerings, or community engagement. Therefore, the question cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 14:55:02,479 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:55:02,479 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:55:02,479 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. This requires qualitative insights and contextual information that is not present in the database schema. The schema contains data about institutions and students, but it does not provide any direct metrics or qualitative factors that would explain the differences in student populations, such as marketing strategies, academic offerings, or community engagement. Therefore, the question cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 14:55:04,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:04,119 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe have contributed to the stability in enrollment numbers at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to stability in enrollment numbers, which is a qualitative assessment rather than a quantitative query that can be answered directly from the provided database schema. The schema contains tables related to institutions, students, programs, and various administrative aspects, but it does not include specific data or metrics that would allow for an analysis of enrollment stability. Factors such as market trends, student satisfaction, or external economic conditions are not captured in the schema.', 'feedback': ''}
2025-08-08 14:55:04,119 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:55:04,120 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:55:04,120 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe have contributed to the stability in enrollment numbers at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to stability in enrollment numbers, which is a qualitative assessment rather than a quantitative query that can be answered directly from the provided database schema. The schema contains tables related to institutions, students, programs, and various administrative aspects, but it does not include specific data or metrics that would allow for an analysis of enrollment stability. Factors such as market trends, student satisfaction, or external economic conditions are not captured in the schema.', 'feedback': ''}
2025-08-08 14:55:05,272 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:07,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:07,989 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. This requires qualitative insights and contextual information that is not present in the database schema. The schema contains data about institutions and students, but it does not provide any direct metrics or qualitative factors that would explain the differences in student populations, such as marketing strategies, academic offerings, or community engagement. Therefore, the question cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 14:55:07,989 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:55:07,989 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:55:07,990 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's significantly larger student population compared to these other institutions?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to ITC University's larger student population compared to other institutions. This requires qualitative insights and contextual information that is not present in the database schema. The schema contains data about institutions and students, but it does not provide any direct metrics or qualitative factors that would explain the differences in student populations, such as marketing strategies, academic offerings, or community engagement. Therefore, the question cannot be answered using the available schema.", 'feedback': ''}
2025-08-08 14:55:25,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:26,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:27,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:31,213 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:33,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:33,948 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided schema contains data about various entities related to institutions, programs, courses, and students, but it does not include any qualitative data or insights that would allow for an analysis of popularity factors. The schema lacks information on student preferences, market trends, or external influences that could affect program popularity.', 'feedback': ''}
2025-08-08 14:55:33,948 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:55:33,949 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:55:33,949 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided schema contains data about various entities related to institutions, programs, courses, and students, but it does not include any qualitative data or insights that would allow for an analysis of popularity factors. The schema lacks information on student preferences, market trends, or external influences that could affect program popularity.', 'feedback': ''}
2025-08-08 14:55:37,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:40,142 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:40,164 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided schema contains data about various entities related to institutions, programs, courses, and students, but it does not include any qualitative data or insights that would allow for an analysis of popularity factors. Additionally, the schema lacks any direct metrics or indicators of popularity, such as enrollment numbers, student satisfaction ratings, or market demand data.', 'feedback': ''}
2025-08-08 14:55:40,164 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:55:40,164 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:55:40,164 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided schema contains data about various entities related to institutions, programs, courses, and students, but it does not include any qualitative data or insights that would allow for an analysis of popularity factors. Additionally, the schema lacks any direct metrics or indicators of popularity, such as enrollment numbers, student satisfaction ratings, or market demand data.', 'feedback': ''}
2025-08-08 14:55:43,244 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:46,156 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:46,171 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative inquiry that requires insights, opinions, or external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include any qualitative factors or insights that would help answer the question about popularity. Therefore, it cannot be answered from the schema.', 'feedback': ''}
2025-08-08 14:55:46,171 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:55:46,171 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:55:46,171 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative inquiry that requires insights, opinions, or external data that are not present in the provided database schema. The schema contains structured data about institutions, programs, courses, and related entities, but it does not include any qualitative factors or insights that would help answer the question about popularity. Therefore, it cannot be answered from the schema.', 'feedback': ''}
2025-08-08 14:55:48,968 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:51,697 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:55:51,711 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided schema contains data about various entities related to institutions, programs, courses, and students, but it does not include any qualitative data or insights that would allow for an analysis of popularity factors. The schema lacks information on student preferences, market trends, or external influences that could affect program popularity.', 'feedback': ''}
2025-08-08 14:55:51,712 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 14:55:51,712 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 14:55:51,712 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions, which is a qualitative inquiry rather than a quantitative one. The provided schema contains data about various entities related to institutions, programs, courses, and students, but it does not include any qualitative data or insights that would allow for an analysis of popularity factors. The schema lacks information on student preferences, market trends, or external influences that could affect program popularity.', 'feedback': ''}
2025-08-08 14:55:51,713 - root - INFO - [{'total_students': 192627}]
2025-08-08 14:55:51,713 - root - INFO - [{'institution_name': 'ITC University', 'student_count': 192627, 'comparison': 'Most Students'}, {'institution_name': 'ITC University', 'student_count': 49153, 'comparison': 'Other'}, {'institution_name': 'Presbyterian University College Ghana', 'student_count': 32094, 'comparison': 'Other'}, {'institution_name': 'Koforidua Technical University', 'student_count': 17758, 'comparison': 'Other'}, {'institution_name': 'Accra Medical', 'student_count': 13012, 'comparison': 'Other'}, {'institution_name': 'Wisconsin International University College', 'student_count': 1202, 'comparison': 'Other'}, {'institution_name': 'Klintaps University College of Health and Allied Sciences', 'student_count': 1135, 'comparison': 'Other'}, {'institution_name': 'Ghana School of Law', 'student_count': 262, 'comparison': 'Other'}, {'institution_name': 'Akrofi-Christaller Institute of Theology Mission and Culture', 'student_count': 114, 'comparison': 'Other'}, {'institution_name': 'Kofi Annan International Peacekeeping Training Centre', 'student_count': 52, 'comparison': 'Other'}, {'institution_name': 'Methodist University College Ghana', 'student_count': 1, 'comparison': 'Other'}]
2025-08-08 14:55:51,713 - root - INFO - [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]
2025-08-08 14:55:51,713 - root - INFO - [{'start_year': 2019, 'student_count': 192627}, {'start_year': 2021, 'student_count': 192627}, {'start_year': 2022, 'student_count': 192627}, {'start_year': 2023, 'student_count': 192627}, {'start_year': 2024, 'student_count': 192627}, {'start_year': 2025, 'student_count': 192627}, {'start_year': 2026, 'student_count': 192627}, {'start_year': 2027, 'student_count': 192627}]
2025-08-08 14:55:51,713 - root - INFO - [{'institution_id': 24, 'retention_rate': 100.5}]
2025-08-08 14:55:51,713 - root - INFO - 'No results'
2025-08-08 14:55:51,713 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 14:56:04,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:56:04,206 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 14:56:16,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 14:56:16,421 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 14:56:16,421 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 14:56:16,421 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 14:56:16,422 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total number of students enrolled at the institution with the most students?...
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total number of students enrolled at the institution with the most students is 192,627....
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'total_students': 192627}]...
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_students_at_largest_institution
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 14:56:16,422 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 14:56:16,422 - celery.redirected - WARNING - [{'total_students': 192627}]
2025-08-08 14:56:16,422 - celery.redirected - WARNING - ================================= 
2025-08-08 14:56:16,422 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 14:56:16,422 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 14:56:16,423 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 14:56:16,423 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 14:56:16,423 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the student population of the institution with the most students compare to other instituti...
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the most students is ITC University, which has a total student population of 19...
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: graph
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_name': 'ITC University', 'student_count': 192627, 'comparison': 'Most Students'}, {'i...
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_population_comparison
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 14:56:16,423 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 14:56:16,423 - celery.redirected - WARNING - [{'institution_name': 'ITC University', 'student_count': 192627, 'comparison': 'Most Students'}, {'institution_name': 'ITC University', 'student_count': 49153, 'comparison': 'Other'}, {'institution_name': 'Presbyterian University College Ghana', 'student_count': 32094, 'comparison': 'Other'}, {'institution_name': 'Koforidua Technical University', 'student_count': 17758, 'comparison': 'Other'}, {'institution_name': 'Accra Medical', 'student_count': 13012, 'comparison': 'Other'}, {'institution_name': 'Wisconsin International University College', 'student_count': 1202, 'comparison': 'Other'}, {'institution_name': 'Klintaps University College of Health and Allied Sciences', 'student_count': 1135, 'comparison': 'Other'}, {'institution_name': 'Ghana School of Law', 'student_count': 262, 'comparison': 'Other'}, {'institution_name': 'Akrofi-Christaller Institute of Theology Mission and Culture', 'student_count': 114, 'comparison': 'Other'}, {'institution_name': 'Kofi Annan International Peacekeeping Training Centre', 'student_count': 52, 'comparison': 'Other'}, {'institution_name': 'Methodist University College Ghana', 'student_count': 1, 'comparison': 'Other'}]
2025-08-08 14:56:16,423 - celery.redirected - WARNING - ================================= 
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_population_comparison
2025-08-08 14:56:16,423 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 14:56:16,424 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 14:56:16,424 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 14:56:16,424 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 14:56:16,424 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are most popular at the institution with the most students?...
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no results available for the programs or courses at the institution with t...
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: popular_courses_at_largest_institution
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 14:56:16,424 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 14:56:16,424 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 14:56:16,424 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 14:56:16,424 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 14:56:16,425 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the demographic breakdown of students at the institution with the most students?...
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the most students has a total student population of 184,627. The demographic br...
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_cou...
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_demographics_by_sex
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 14:56:16,425 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 14:56:16,425 - celery.redirected - WARNING - [{'sex': '', 'student_count': 8205}, {'sex': 'M', 'student_count': 96457}, {'sex': 'F', 'student_count': 87965}]
2025-08-08 14:56:16,425 - celery.redirected - WARNING - ================================= 
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_demographics_by_sex
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 14:56:16,425 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 14:56:16,425 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 14:56:16,425 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 14:56:16,425 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How has the student enrollment at the institution with the most students changed over the past few y...
2025-08-08 14:56:16,425 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The student enrollment at the institution with the most students has remained constant at 192,627 fr...
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'start_year': 2019, 'student_count': 192627}, {'start_year': 2021, 'student_count': 192627}, {'sta...
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_enrollment_trend_most_students
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 14:56:16,426 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 14:56:16,426 - celery.redirected - WARNING - [{'start_year': 2019, 'student_count': 192627}, {'start_year': 2021, 'student_count': 192627}, {'start_year': 2022, 'student_count': 192627}, {'start_year': 2023, 'student_count': 192627}, {'start_year': 2024, 'student_count': 192627}, {'start_year': 2025, 'student_count': 192627}, {'start_year': 2026, 'student_count': 192627}, {'start_year': 2027, 'student_count': 192627}]
2025-08-08 14:56:16,426 - celery.redirected - WARNING - ================================= 
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 14:56:16,426 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 14:56:16,426 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 14:56:16,426 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institution has the most students?'
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 14:56:16,426 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the retention rate of students at the institution with the most students?...
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The retention rate of students at the institution with the most students is 100.5%. This indicates t...
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_id': 24, 'retention_rate': 100.5}]...
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: retention_rate_highest_institution
2025-08-08 14:56:16,426 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 14:56:16,427 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 14:56:16,427 - celery.redirected - WARNING - [{'institution_id': 24, 'retention_rate': 100.5}]
2025-08-08 14:56:16,427 - celery.redirected - WARNING - ================================= 
2025-08-08 14:56:16,427 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 14:56:16,427 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 6
2025-08-08 14:56:16,427 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 14:56:16,427 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 14:56:16,427 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 14:56:16,427 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 6 documents
2025-08-08 14:56:16,427 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 14:56:16,427 - UPSERT_DOCS - INFO -     Content: Question: What is the total number of students enrolled at the institution with the most students?
A...
2025-08-08 14:56:16,427 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T14:54:55.699306+00:00', 'data_returned': True}
2025-08-08 14:56:16,427 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 14:56:16,427 - UPSERT_DOCS - INFO -     Content: Question: How does the student population of the institution with the most students compare to other...
2025-08-08 14:56:16,427 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T14:55:07.990096+00:00', 'data_returned': True, 'data_tag': 'student_population_comparison'}
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are most popular at the institution with the most students?
Answe...
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T14:55:51.712440+00:00', 'data_returned': False}
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -     Content: Question: What is the demographic breakdown of students at the institution with the most students?
A...
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T14:54:48.112485+00:00', 'data_returned': True, 'data_tag': 'student_demographics_by_sex'}
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -     Content: Question: How has the student enrollment at the institution with the most students changed over the ...
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T14:55:04.120135+00:00', 'data_returned': True}
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -     Content: Question: What is the retention rate of students at the institution with the most students?
Answer: ...
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institution has the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T14:54:47.270052+00:00', 'data_returned': True}
2025-08-08 14:56:16,428 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 5/6
2025-08-08 14:56:26,431 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:N/A duration:10.002s]
2025-08-08 14:56:26,431 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://54.246.247.31:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-08 14:56:26,432 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: Connection timed out
2025-08-08 14:56:26,432 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 14:56:26,432 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-08 14:56:26,432 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 14:56:26,433 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: Connection timed out
2025-08-08 14:56:26,433 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_145322.log
2025-08-08 14:56:26,436 - celery.app.trace - INFO - Task generate_streaming_report[5f3884f5-1864-4e69-9c9b-067c23343027] succeeded in 184.04705224999998s: {'error': 'Error generating streaming report: Connection timed out'}

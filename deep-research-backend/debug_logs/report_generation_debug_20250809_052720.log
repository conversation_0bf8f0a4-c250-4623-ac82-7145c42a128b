2025-08-09 05:27:20,342 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250809_052720.log
2025-08-09 05:27:20,342 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:27:20,342 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: faa011f4-fd6d-49a8-a984-af3f3ef53bda
2025-08-09 05:27:20,342 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:27:20,342 - REPORT_REQUEST - INFO - 📝 Original Question: 'From which institution do students owe the most fees?'
2025-08-09 05:27:20,342 - REPORT_REQUEST - INFO - 🆔 Task ID: faa011f4-fd6d-49a8-a984-af3f3ef53bda
2025-08-09 05:27:20,342 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-09T05:27:20.342966
2025-08-09 05:27:20,500 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.157s]
2025-08-09 05:27:20,500 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 14
2025-08-09 05:27:20,693 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.193s]
2025-08-09 05:27:20,693 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 7
2025-08-09 05:27:20,863 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.170s]
2025-08-09 05:27:20,864 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (2):
2025-08-09 05:27:20,864 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (7 docs)
2025-08-09 05:27:20,864 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (7 docs)
2025-08-09 05:27:20,865 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:27:20,865 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-09 05:27:20,865 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:27:20,865 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: From which institution do students owe the most fees?
2025-08-09 05:27:20,865 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-09 05:27:20,865 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-09 05:27:29,718 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:29,737 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-09 05:27:34,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:34,887 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-09 05:27:37,934 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:38,015 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:38,154 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:38,197 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:38,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:38,460 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:38,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:38,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:38,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:39,212 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:39,252 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-09 05:27:41,559 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:41,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:41,968 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:42,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:42,230 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:42,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:42,561 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:44,179 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:44,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:44,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:44,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:44,603 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:44,866 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:45,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:45,477 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:45,749 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:46,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:46,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:46,127 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:46,241 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:46,537 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:49,299 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:50,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:50,508 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:50,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:51,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:52,074 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:52,247 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:53,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:53,627 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total amount of fees owed by students at the institution where students owe the most fees?', 'sql': 'SELECT institution_id, SUM(balance) AS total_fees_owed\nFROM student_balances\nGROUP BY institution_id\nORDER BY total_fees_owed DESC\nLIMIT 1;', 'correct': True, 'reasoning': "The SQL query correctly aggregates the total balance (fees owed) for each institution by summing the 'balance' field in the 'student_balances' table. It groups the results by 'institution_id' and orders them in descending order based on the total fees owed. The use of 'LIMIT 1' ensures that only the institution with the highest total fees owed is returned, which directly answers the question.", 'feedback': "The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to include a 'SELECT' clause that explicitly states the total fees owed for clarity."}
2025-08-09 05:27:53,628 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:27:53,628 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:27:54,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:54,279 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average fee debt per student at the institution where students owe the most fees?', 'sql': 'SELECT AVG(balance) AS average_fee_debt\nFROM student_balances\nWHERE institution_id = (\n    SELECT institution_id\n    FROM student_balances\n    GROUP BY institution_id\n    ORDER BY SUM(balance) DESC\n    LIMIT 1\n)', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution where students owe the most fees by summing the balances grouped by institution_id and ordering them in descending order. It then limits the result to the top institution. The outer query calculates the average balance (fee debt) for that specific institution, which directly answers the question about the average fee debt per student at the institution with the highest total debt.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly joining with the students table to ensure that the average is calculated per student rather than just the average of all balances, in case there are multiple students with varying balances.'}
2025-08-09 05:27:54,279 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:27:54,279 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:27:55,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:56,858 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:56,930 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:58,255 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:58,833 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:59,149 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:59,325 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:59,543 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:59,615 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:59,678 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:27:59,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:00,605 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:00,618 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:01,015 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:01,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:02,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:02,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:02,559 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:02,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:02,776 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:02,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:03,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:03,713 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:03,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:05,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:05,112 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:05,132 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or relationships that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or contextual information that would be necessary to answer such a qualitative question.', 'feedback': ''}
2025-08-09 05:28:05,135 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:05,135 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:05,135 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or relationships that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or contextual information that would be necessary to answer such a qualitative question.', 'feedback': ''}
2025-08-09 05:28:05,521 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:05,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:05,673 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students having a credit balance at this institution, and how does this compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students having a credit balance at the institution and a comparison with other institutions. While the schema contains tables related to student transactions, balances, and institutions, it does not provide specific factors or reasons for credit balances. Additionally, the schema lacks comparative data across different institutions that would allow for a meaningful comparison. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 05:28:05,673 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:05,673 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:05,673 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students having a credit balance at this institution, and how does this compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students having a credit balance at the institution and a comparison with other institutions. While the schema contains tables related to student transactions, balances, and institutions, it does not provide specific factors or reasons for credit balances. Additionally, the schema lacks comparative data across different institutions that would allow for a meaningful comparison. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 05:28:06,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:06,981 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:07,741 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:07,855 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:07,928 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:07,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:08,695 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:09,617 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:09,631 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or relationships that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or contextual information that would be necessary to answer such a qualitative question.', 'feedback': ''}
2025-08-09 05:28:09,631 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:09,631 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:09,631 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or relationships that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or contextual information that would be necessary to answer such a qualitative question.', 'feedback': ''}
2025-08-09 05:28:09,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:09,729 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students having a credit balance at this institution, and how does this compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students having a credit balance at the institution and a comparison with other institutions. While the schema contains tables related to student transactions, balances, and institutions, it does not provide specific factors or reasons for credit balances. Additionally, the schema lacks comparative data across different institutions that would allow for a meaningful comparison. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 05:28:09,729 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:09,730 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:09,730 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students having a credit balance at this institution, and how does this compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students having a credit balance at the institution and a comparison with other institutions. While the schema contains tables related to student transactions, balances, and institutions, it does not provide specific factors or reasons for credit balances. Additionally, the schema lacks comparative data across different institutions that would allow for a meaningful comparison. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 05:28:11,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:11,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:11,639 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:13,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:13,406 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:13,423 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:13,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:13,437 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students having a credit balance at this institution, and how does this compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students having a credit balance at the institution and a comparison with other institutions. While the schema contains tables related to student transactions, balances, and institutions, it does not provide specific factors or reasons for credit balances. Additionally, the schema lacks comparative data across different institutions that would allow for a meaningful comparison. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 05:28:13,438 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:13,438 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:13,438 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students having a credit balance at this institution, and how does this compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students having a credit balance at the institution and a comparison with other institutions. While the schema contains tables related to student transactions, balances, and institutions, it does not provide specific factors or reasons for credit balances. Additionally, the schema lacks comparative data across different institutions that would allow for a meaningful comparison. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 05:28:13,440 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or relationships that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or contextual information that would be necessary to answer such a qualitative question.', 'feedback': ''}
2025-08-09 05:28:13,440 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:13,440 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:13,440 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or relationships that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or contextual information that would be necessary to answer such a qualitative question.', 'feedback': ''}
2025-08-09 05:28:13,938 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:15,268 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:15,378 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:15,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:16,261 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:16,272 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the fee debt at the institution where students owe the most fees compare to other institutions?', 'sql': 'WITH InstitutionDebts AS (  SELECT institution_id, SUM(balance) AS total_debt  FROM student_balances  GROUP BY institution_id), MaxDebt AS (  SELECT institution_id, total_debt  FROM InstitutionDebts  WHERE total_debt = (SELECT MAX(total_debt) FROM InstitutionDebts)) SELECT id AS institution_id, total_debt, (total_debt - (SELECT total_debt FROM MaxDebt)) AS debt_difference FROM InstitutionDebts ORDER BY total_debt DESC;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the highest total debt by summing the balances for each institution and then finding the maximum total debt. It also calculates the difference in debt between each institution and the institution with the highest debt, which directly addresses the comparison aspect of the question. The use of Common Table Expressions (CTEs) helps in organizing the query logically, making it clear how the data is being processed to answer the question.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly including the name of the institutions in the final output for better clarity, as the question is about comparing debts at different institutions.'}
2025-08-09 05:28:16,273 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:16,273 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:28:16,551 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:16,719 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:16,726 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the demographic breakdown of students who owe fees at the institution where students owe the most fees?', 'sql': 'WITH FeeOwed AS (SELECT student_id, SUM(balance) AS total_balance FROM student_balances GROUP BY student_id HAVING SUM(balance) > 0), MaxOwedInstitution AS (SELECT institution_id FROM student_balances GROUP BY institution_id ORDER BY SUM(balance) DESC LIMIT 1) SELECT s.sex, s.nationality_id, COUNT(s.id) AS student_count FROM students s JOIN FeeOwed fo ON s.id = fo.student_id WHERE s.institution_id = (SELECT institution_id FROM MaxOwedInstitution) GROUP BY s.sex, s.nationality_id;', 'correct': True, 'reasoning': "The SQL query correctly identifies students who owe fees by summing their balances and filtering for those with a positive balance. It then determines the institution with the highest total fees owed and retrieves the demographic breakdown (sex and nationality) of students from that institution who owe fees. The use of CTEs (Common Table Expressions) is appropriate for structuring the query, and the final selection groups the results by sex and nationality, which aligns with the question's request for a demographic breakdown.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly including the institution_id in the final output for clarity, as the question implies a focus on the specific institution where the fees are highest.'}
2025-08-09 05:28:16,726 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:16,726 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:28:17,597 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:17,613 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or historical data that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 05:28:17,613 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:17,613 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:17,614 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or historical data that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 05:28:17,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:17,633 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students having a credit balance at this institution, and how does this compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students having a credit balance at the institution and a comparison with other institutions. While the schema contains tables related to student transactions, balances, and institutions, it does not provide specific factors or reasons for credit balances. Additionally, the schema lacks comparative data across different institutions that would allow for a meaningful comparison. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 05:28:17,634 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:17,634 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:17,634 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students having a credit balance at this institution, and how does this compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students having a credit balance at the institution and a comparison with other institutions. While the schema contains tables related to student transactions, balances, and institutions, it does not provide specific factors or reasons for credit balances. Additionally, the schema lacks comparative data across different institutions that would allow for a meaningful comparison. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 05:28:18,314 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:18,384 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:19,445 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:19,813 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:20,041 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:20,577 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:20,586 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What percentage of students at the institution where students owe the most fees are currently in debt?', 'sql': 'WITH debt_info AS (  SELECT sb.institution_id, SUM(sb.balance) AS total_debt, COUNT(DISTINCT sb.student_id) AS total_students  FROM student_balances sb  GROUP BY sb.institution_id),  max_debt AS (  SELECT institution_id  FROM debt_info  WHERE total_debt = (SELECT MAX(total_debt) FROM debt_info)  LIMIT 1),  students_in_debt AS (  SELECT COUNT(DISTINCT s.id) AS students_in_debt  FROM students s  JOIN student_balances sb ON s.id = sb.student_id  WHERE sb.balance < 0 AND s.institution_id = (SELECT institution_id FROM max_debt) )  SELECT (students_in_debt.students_in_debt * 100.0 / (SELECT COUNT(DISTINCT id) FROM students WHERE institution_id = (SELECT institution_id FROM max_debt))) AS percentage_in_debt  FROM students_in_debt;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total debt by summing the balances in the 'student_balances' table and grouping by institution. It then finds the institution with the maximum debt and counts the number of students in debt (those with a negative balance) at that institution. Finally, it calculates the percentage of students in debt relative to the total number of students at that institution. This aligns perfectly with the question asked.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding comments to clarify each step of the query for better readability and maintainability.'}
2025-08-09 05:28:20,587 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:20,587 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:28:20,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:21,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:22,713 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:23,158 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:24,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:24,467 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:24,477 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:24,654 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:24,669 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of data on fee debts at this institution, and how could this impact our understanding of student financial challenges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore factors contributing to a lack of data on fee debts and its implications on understanding student financial challenges. The provided schema contains tables related to student transactions, bills, and financial aid, but it does not include any qualitative data or analysis regarding the reasons for data gaps or the broader implications of such gaps. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-09 05:28:24,670 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:24,670 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:24,670 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of data on fee debts at this institution, and how could this impact our understanding of student financial challenges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore factors contributing to a lack of data on fee debts and its implications on understanding student financial challenges. The provided schema contains tables related to student transactions, bills, and financial aid, but it does not include any qualitative data or analysis regarding the reasons for data gaps or the broader implications of such gaps. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-09 05:28:24,915 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:25,213 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:25,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:26,991 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:27,261 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:27,732 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:29,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:29,202 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:29,220 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of data on fee debts at this institution, and how could this impact our understanding of student financial challenges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore potential factors contributing to a lack of data on fee debts at an institution, as well as the implications of this lack of data on understanding student financial challenges. The provided schema contains tables related to student transactions, bills, and financial aid, but it does not provide direct insights into qualitative factors or the broader implications of missing data. Therefore, while the schema may contain some relevant data points, it does not support a comprehensive answer to the question.', 'feedback': ''}
2025-08-09 05:28:29,221 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:29,221 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:29,221 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of data on fee debts at this institution, and how could this impact our understanding of student financial challenges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore potential factors contributing to a lack of data on fee debts at an institution, as well as the implications of this lack of data on understanding student financial challenges. The provided schema contains tables related to student transactions, bills, and financial aid, but it does not provide direct insights into qualitative factors or the broader implications of missing data. Therefore, while the schema may contain some relevant data points, it does not support a comprehensive answer to the question.', 'feedback': ''}
2025-08-09 05:28:30,276 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:30,308 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the absence of students owing fees at this institution, and how does this compare to other institutions with significant fee debt?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the absence of students owing fees at a specific institution and a comparison with other institutions regarding fee debt. The schema provides various tables related to students, fees, and institutions, but it does not contain specific data or attributes that would allow for a comprehensive analysis of the factors contributing to fee absences or a comparative analysis of fee debts across institutions. The schema lacks qualitative data or insights into the reasons behind fee debts, which are necessary to answer the question.', 'feedback': ''}
2025-08-09 05:28:30,308 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:30,308 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:30,308 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the absence of students owing fees at this institution, and how does this compare to other institutions with significant fee debt?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the absence of students owing fees at a specific institution and a comparison with other institutions regarding fee debt. The schema provides various tables related to students, fees, and institutions, but it does not contain specific data or attributes that would allow for a comprehensive analysis of the factors contributing to fee absences or a comparative analysis of fee debts across institutions. The schema lacks qualitative data or insights into the reasons behind fee debts, which are necessary to answer the question.', 'feedback': ''}
2025-08-09 05:28:31,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:31,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:31,664 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of debt among students at this institution?', 'answerable': False, 'reasoning': "The question asks for qualitative factors contributing to the absence of debt among students at the institution. The provided schema contains various tables related to student information, financial transactions, and academic records, but it does not include any qualitative data or analysis regarding the reasons behind students' financial situations. To answer this question, one would need insights into student behavior, financial literacy, institutional policies, or external economic factors, none of which are represented in the schema.", 'feedback': ''}
2025-08-09 05:28:31,665 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:31,665 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:31,665 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of debt among students at this institution?', 'answerable': False, 'reasoning': "The question asks for qualitative factors contributing to the absence of debt among students at the institution. The provided schema contains various tables related to student information, financial transactions, and academic records, but it does not include any qualitative data or analysis regarding the reasons behind students' financial situations. To answer this question, one would need insights into student behavior, financial literacy, institutional policies, or external economic factors, none of which are represented in the schema.", 'feedback': ''}
2025-08-09 05:28:32,247 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:32,950 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:33,915 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:34,295 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:34,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:35,011 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:35,025 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of data on fee debts at this institution, and how could this impact our understanding of student financial challenges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore potential factors contributing to a lack of data on fee debts at an institution, as well as the implications of this lack of data on understanding student financial challenges. The provided schema contains tables related to student transactions, bills, and financial aid, but it does not provide insights into qualitative factors or the reasoning behind data gaps. Additionally, the schema does not include any direct information about the reasons for missing data or the broader implications of such gaps. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 05:28:35,026 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:35,026 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:35,026 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of data on fee debts at this institution, and how could this impact our understanding of student financial challenges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore potential factors contributing to a lack of data on fee debts at an institution, as well as the implications of this lack of data on understanding student financial challenges. The provided schema contains tables related to student transactions, bills, and financial aid, but it does not provide insights into qualitative factors or the reasoning behind data gaps. Additionally, the schema does not include any direct information about the reasons for missing data or the broader implications of such gaps. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 05:28:35,421 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:35,434 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the absence of students owing fees at this institution, and how does this compare to other institutions with significant fee debt?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the absence of students owing fees at a specific institution and a comparison with other institutions regarding fee debt. The schema provides various tables related to students, fees, and institutions, but it does not contain specific data or attributes that would allow for a comprehensive analysis of the factors contributing to fee absences or a comparative analysis of fee debts across institutions. The schema lacks qualitative data or insights into the reasons behind fee debts, which are necessary to answer the question.', 'feedback': ''}
2025-08-09 05:28:35,434 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:35,434 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:35,434 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the absence of students owing fees at this institution, and how does this compare to other institutions with significant fee debt?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the absence of students owing fees at a specific institution and a comparison with other institutions regarding fee debt. The schema provides various tables related to students, fees, and institutions, but it does not contain specific data or attributes that would allow for a comprehensive analysis of the factors contributing to fee absences or a comparative analysis of fee debts across institutions. The schema lacks qualitative data or insights into the reasons behind fee debts, which are necessary to answer the question.', 'feedback': ''}
2025-08-09 05:28:35,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:35,933 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of debt among students at this institution?', 'answerable': False, 'reasoning': "The question asks for qualitative factors contributing to the absence of debt among students at the institution. The provided schema contains various tables related to student information, financial transactions, and academic records, but it does not include any qualitative data or analysis regarding the reasons behind students' financial situations. The schema lacks specific fields or tables that would allow for an analysis of factors contributing to the absence of debt, such as student financial behavior, socioeconomic background, or institutional policies on financial aid and debt management.", 'feedback': ''}
2025-08-09 05:28:35,933 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:35,933 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:35,933 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of debt among students at this institution?', 'answerable': False, 'reasoning': "The question asks for qualitative factors contributing to the absence of debt among students at the institution. The provided schema contains various tables related to student information, financial transactions, and academic records, but it does not include any qualitative data or analysis regarding the reasons behind students' financial situations. The schema lacks specific fields or tables that would allow for an analysis of factors contributing to the absence of debt, such as student financial behavior, socioeconomic background, or institutional policies on financial aid and debt management.", 'feedback': ''}
2025-08-09 05:28:36,398 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:38,128 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:38,202 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:38,254 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:38,591 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:39,279 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:39,542 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:40,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:40,425 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the absence of students owing fees at this institution, and how does this compare to other institutions with significant fee debt?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the absence of students owing fees at a specific institution and a comparison with other institutions regarding fee debt. The schema provides various tables related to students, fees, and institutions, but it does not contain specific data or attributes that would allow for a comprehensive analysis of the factors contributing to fee absences or comparisons of fee debts across institutions. The schema lacks qualitative data or insights into the reasons behind fee debts, which are necessary to answer the question.', 'feedback': ''}
2025-08-09 05:28:40,425 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:40,425 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:40,425 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the absence of students owing fees at this institution, and how does this compare to other institutions with significant fee debt?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the absence of students owing fees at a specific institution and a comparison with other institutions regarding fee debt. The schema provides various tables related to students, fees, and institutions, but it does not contain specific data or attributes that would allow for a comprehensive analysis of the factors contributing to fee absences or comparisons of fee debts across institutions. The schema lacks qualitative data or insights into the reasons behind fee debts, which are necessary to answer the question.', 'feedback': ''}
2025-08-09 05:28:40,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:40,660 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of data on fee debts at this institution, and how could this impact our understanding of student financial challenges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore potential factors contributing to a lack of data on fee debts at an institution, as well as the implications of this lack of data on understanding student financial challenges. The provided schema contains tables related to student transactions, bills, and financial aid, but it does not include any qualitative data or insights into the reasons behind data gaps or the broader implications of such gaps. Therefore, while the schema can provide quantitative data on fees and transactions, it cannot address the qualitative aspects of the question.', 'feedback': ''}
2025-08-09 05:28:40,660 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:40,660 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:40,660 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of data on fee debts at this institution, and how could this impact our understanding of student financial challenges?', 'answerable': False, 'reasoning': 'The question is qualitative and seeks to explore potential factors contributing to a lack of data on fee debts at an institution, as well as the implications of this lack of data on understanding student financial challenges. The provided schema contains tables related to student transactions, bills, and financial aid, but it does not include any qualitative data or insights into the reasons behind data gaps or the broader implications of such gaps. Therefore, while the schema can provide quantitative data on fees and transactions, it cannot address the qualitative aspects of the question.', 'feedback': ''}
2025-08-09 05:28:40,720 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:40,740 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of debt among students at this institution?', 'answerable': False, 'reasoning': "The question asks for qualitative factors contributing to the absence of debt among students at the institution. The provided schema primarily contains structured data related to students, programs, financial transactions, and institutional details, but it does not include qualitative insights or analyses that would explain the reasons behind students' financial situations. To answer this question, one would need qualitative data, such as surveys or studies on student financial behavior, which are not present in the schema.", 'feedback': ''}
2025-08-09 05:28:40,741 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:40,741 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:40,741 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of debt among students at this institution?', 'answerable': False, 'reasoning': "The question asks for qualitative factors contributing to the absence of debt among students at the institution. The provided schema primarily contains structured data related to students, programs, financial transactions, and institutional details, but it does not include qualitative insights or analyses that would explain the reasons behind students' financial situations. To answer this question, one would need qualitative data, such as surveys or studies on student financial behavior, which are not present in the schema.", 'feedback': ''}
2025-08-09 05:28:40,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:42,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:42,672 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:44,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:44,544 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:44,554 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of debt among students at this institution?', 'answerable': False, 'reasoning': "The question asks for qualitative factors contributing to the absence of debt among students at the institution. The provided schema primarily contains structured data about students, programs, financial transactions, and institutional details, but it does not include qualitative insights or analyses that would explain the reasons behind students' financial situations. To answer this question, one would need qualitative data, surveys, or studies that are not present in the schema.", 'feedback': ''}
2025-08-09 05:28:44,554 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:44,554 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:44,554 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of debt among students at this institution?', 'answerable': False, 'reasoning': "The question asks for qualitative factors contributing to the absence of debt among students at the institution. The provided schema primarily contains structured data about students, programs, financial transactions, and institutional details, but it does not include qualitative insights or analyses that would explain the reasons behind students' financial situations. To answer this question, one would need qualitative data, surveys, or studies that are not present in the schema.", 'feedback': ''}
2025-08-09 05:28:44,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:44,871 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:44,887 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the absence of students owing fees at this institution, and how does this compare to other institutions with significant fee debt?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the absence of students owing fees at a specific institution and a comparison with other institutions regarding fee debt. The schema provides various tables related to students, fees, and institutions, but it does not contain specific data or attributes that would allow for a comprehensive analysis of the factors contributing to fee absences or a comparative analysis of fee debts across institutions. The schema lacks qualitative data or insights into the reasons behind fee debts, which are necessary to answer the question.', 'feedback': ''}
2025-08-09 05:28:44,887 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:28:44,887 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:28:44,887 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the absence of students owing fees at this institution, and how does this compare to other institutions with significant fee debt?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the absence of students owing fees at a specific institution and a comparison with other institutions regarding fee debt. The schema provides various tables related to students, fees, and institutions, but it does not contain specific data or attributes that would allow for a comprehensive analysis of the factors contributing to fee absences or a comparative analysis of fee debts across institutions. The schema lacks qualitative data or insights into the reasons behind fee debts, which are necessary to answer the question.', 'feedback': ''}
2025-08-09 05:28:49,825 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:49,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:51,917 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:53,432 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:54,091 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:55,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:55,910 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:28:57,177 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:00,988 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:03,172 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:05,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:05,019 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What payment plans or financial aid options are available to students at the institution where students owe the most fees?', 'sql': 'SELECT DISTINCT fa.type, fa.description, fa.status FROM financial_aid_requests fa WHERE fa.institution_id = (SELECT institution_id FROM student_balances sb GROUP BY institution_id ORDER BY SUM(balance) DESC LIMIT 1);', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total student balances by summing the balances in the student_balances table and ordering them in descending order. It then retrieves distinct types, descriptions, and statuses of financial aid requests associated with that institution from the financial_aid_requests table. This aligns with the question's requirement to find available financial aid options at the institution where students owe the most fees.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding a comment to clarify the purpose of the subquery for future reference.'}
2025-08-09 05:29:05,019 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:29:05,019 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:29:07,509 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:08,392 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:09,931 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:09,943 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific programs or courses at the institution where students owe the most fees that contribute to higher debts?', 'sql': 'SELECT p.long_name AS program_name, c.title AS course_title, SUM(b.total_due) AS total_debt\nFROM student_bills sb\nJOIN bills b ON sb.bill_id = b.id\nJOIN core.student_programs sp ON sb.student_program_id = sp.id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.courses c ON sp.unit_id = c.unit_id\nGROUP BY p.long_name, c.title\nORDER BY total_debt DESC\nLIMIT 10;', 'correct': False, 'reasoning': "The SQL query attempts to aggregate the total debts associated with programs and courses, but it incorrectly joins the courses table. The join condition 'JOIN core.courses c ON sp.unit_id = c.unit_id' does not accurately reflect the relationship between student programs and courses, as it assumes that each program is directly linked to a course through the unit_id, which may not be the case. Additionally, the question asks for programs or courses contributing to higher debts, but the query does not filter or specify which debts are considered 'higher' beyond the aggregation. The query also lacks a direct link to the institution, which is implied in the question but not explicitly handled in the SQL.", 'feedback': 'Clarify the relationship between programs and courses in the schema to ensure accurate joins. Additionally, consider adding a filter for the institution_id to focus on a specific institution. The question could be improved by specifying whether to consider only certain types of fees or all fees, and the SQL could be enhanced by ensuring the correct relationships are used and possibly including a WHERE clause to filter by institution.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-09 05:29:09,943 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:29:09,943 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-09 05:29:10,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:11,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:12,561 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:14,002 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:14,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:14,635 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons for the absence of payment plans or financial aid options at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of payment plans or financial aid options at an institution, as well as the impact on student enrollment and retention. However, the provided schema does not contain any direct information regarding the reasons for the absence of such options or their effects on enrollment and retention. The schema includes tables related to financial aid requests, student transactions, and student programs, but it lacks qualitative data or insights into institutional policies or student experiences that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-09 05:29:14,635 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:29:14,635 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:29:14,635 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons for the absence of payment plans or financial aid options at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of payment plans or financial aid options at an institution, as well as the impact on student enrollment and retention. However, the provided schema does not contain any direct information regarding the reasons for the absence of such options or their effects on enrollment and retention. The schema includes tables related to financial aid requests, student transactions, and student programs, but it lacks qualitative data or insights into institutional policies or student experiences that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-09 05:29:15,285 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:16,913 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:18,992 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:19,006 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons for the absence of payment plans or financial aid options at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of payment plans or financial aid options at an institution, as well as the impact of this absence on student enrollment and retention. The schema provided does not contain any direct information regarding the reasons for the absence of such options, nor does it provide insights into the effects on student enrollment and retention. While there are tables related to financial aid requests and student transactions, they do not explicitly address the reasons for the absence of payment plans or financial aid options. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 05:29:19,006 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:29:19,006 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:29:19,006 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons for the absence of payment plans or financial aid options at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of payment plans or financial aid options at an institution, as well as the impact of this absence on student enrollment and retention. The schema provided does not contain any direct information regarding the reasons for the absence of such options, nor does it provide insights into the effects on student enrollment and retention. While there are tables related to financial aid requests and student transactions, they do not explicitly address the reasons for the absence of payment plans or financial aid options. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 05:29:21,101 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:22,357 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:24,207 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:24,225 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons for the absence of payment plans or financial aid options at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of payment plans or financial aid options at an institution, as well as the impact on student enrollment and retention. However, the provided schema does not contain any direct information regarding the reasons for the absence of such options or their effects on enrollment and retention. The schema includes tables related to financial aid requests, student transactions, and billing, but it lacks qualitative data or insights into institutional policies or student experiences that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-09 05:29:24,226 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:29:24,226 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:29:24,226 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons for the absence of payment plans or financial aid options at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of payment plans or financial aid options at an institution, as well as the impact on student enrollment and retention. However, the provided schema does not contain any direct information regarding the reasons for the absence of such options or their effects on enrollment and retention. The schema includes tables related to financial aid requests, student transactions, and billing, but it lacks qualitative data or insights into institutional policies or student experiences that would be necessary to answer the question comprehensively.', 'feedback': ''}
2025-08-09 05:29:26,394 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:29,259 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:29,285 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons for the absence of payment plans or financial aid options at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of payment plans or financial aid options at an institution, as well as the impact of this absence on student enrollment and retention. The schema provided does not contain any direct information regarding the reasons for the absence of such options or the qualitative impacts on student enrollment and retention. While there are tables related to financial aid requests and student transactions, they do not provide insights into the reasons for the absence of payment plans or financial aid options. Additionally, the schema lacks any data that would allow for an analysis of the impact on student enrollment and retention.', 'feedback': ''}
2025-08-09 05:29:29,286 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:29:29,286 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:29:29,286 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons for the absence of payment plans or financial aid options at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of payment plans or financial aid options at an institution, as well as the impact of this absence on student enrollment and retention. The schema provided does not contain any direct information regarding the reasons for the absence of such options or the qualitative impacts on student enrollment and retention. While there are tables related to financial aid requests and student transactions, they do not provide insights into the reasons for the absence of payment plans or financial aid options. Additionally, the schema lacks any data that would allow for an analysis of the impact on student enrollment and retention.', 'feedback': ''}
2025-08-09 05:29:29,693 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:32,662 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:36,146 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:37,680 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:42,754 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:49,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:49,657 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific programs or courses at the institution where students owe the most fees that contribute to higher debts?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, SUM(st.transaction_amount) AS total_debt\nFROM core.student_transactions st\nJOIN core.student_programs sp ON st.student_program_id = sp.id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.courses c ON c.unit_id = sp.unit_id\nWHERE st.type = 'debit'\nGROUP BY p.long_name, c.title\nORDER BY total_debt DESC;", 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses associated with student transactions that are classified as 'debit', which indicates fees owed. It aggregates the total debt for each program and course combination, allowing for the identification of those with the highest debts. The use of GROUP BY and ORDER BY ensures that the results are organized by the total debt in descending order, which directly addresses the question about specific programs or courses contributing to higher debts.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding a filter for a specific institution if the schema supports it, to ensure the results are relevant to the institution in question. Additionally, clarifying whether the question seeks to identify only the top programs or courses or all of them could refine the query further.'}
2025-08-09 05:29:49,658 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:29:49,658 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:29:52,494 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:53,283 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:54,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:57,497 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:59,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:29:59,888 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might influence the overall debt levels of students, even if specific programs or courses are not identified as contributing to higher fees?', 'answerable': False, 'reasoning': 'The question asks for factors influencing overall debt levels of students without specifying programs or courses. The schema primarily contains data about students, their programs, financial transactions, and related entities. However, it does not provide direct insights into broader factors that might influence debt levels, such as economic conditions, personal financial management, or external financial aid sources. The schema lacks qualitative data or external factors that could be analyzed to answer the question comprehensively.', 'feedback': ''}
2025-08-09 05:29:59,888 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:29:59,888 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:29:59,888 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might influence the overall debt levels of students, even if specific programs or courses are not identified as contributing to higher fees?', 'answerable': False, 'reasoning': 'The question asks for factors influencing overall debt levels of students without specifying programs or courses. The schema primarily contains data about students, their programs, financial transactions, and related entities. However, it does not provide direct insights into broader factors that might influence debt levels, such as economic conditions, personal financial management, or external financial aid sources. The schema lacks qualitative data or external factors that could be analyzed to answer the question comprehensively.', 'feedback': ''}
2025-08-09 05:30:02,639 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:30:05,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:30:05,242 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might influence the overall debt levels of students, even if specific programs or courses are not identified as contributing to higher fees?', 'answerable': False, 'reasoning': 'The question asks about the factors influencing overall debt levels of students without specifying programs or courses. The schema provided contains various tables related to students, their financial transactions, and academic details, but it does not explicitly define or categorize factors that influence debt levels. While there are tables related to student transactions, bills, and financial aid, the schema lacks a comprehensive analysis or categorization of factors affecting debt levels. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-09 05:30:05,242 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:30:05,243 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:30:05,243 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might influence the overall debt levels of students, even if specific programs or courses are not identified as contributing to higher fees?', 'answerable': False, 'reasoning': 'The question asks about the factors influencing overall debt levels of students without specifying programs or courses. The schema provided contains various tables related to students, their financial transactions, and academic details, but it does not explicitly define or categorize factors that influence debt levels. While there are tables related to student transactions, bills, and financial aid, the schema lacks a comprehensive analysis or categorization of factors affecting debt levels. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-09 05:30:07,784 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:30:10,015 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:30:10,029 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might influence the overall debt levels of students, even if specific programs or courses are not identified as contributing to higher fees?', 'answerable': False, 'reasoning': 'The question asks for factors influencing overall debt levels of students without specifying programs or courses. The schema primarily contains data about students, their programs, financial transactions, and related entities. However, it does not provide direct insights into broader factors that might influence debt levels, such as economic conditions, personal financial management, or external financial aid sources. The schema lacks the necessary contextual data to analyze these influences comprehensively.', 'feedback': ''}
2025-08-09 05:30:10,029 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:30:10,029 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:30:10,030 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might influence the overall debt levels of students, even if specific programs or courses are not identified as contributing to higher fees?', 'answerable': False, 'reasoning': 'The question asks for factors influencing overall debt levels of students without specifying programs or courses. The schema primarily contains data about students, their programs, financial transactions, and related entities. However, it does not provide direct insights into broader factors that might influence debt levels, such as economic conditions, personal financial management, or external financial aid sources. The schema lacks the necessary contextual data to analyze these influences comprehensively.', 'feedback': ''}
2025-08-09 05:30:10,031 - root - INFO - [{'institution_id': 1, 'total_fees_owed': -2600.0}]
2025-08-09 05:30:10,031 - root - INFO - [{'average_fee_debt': -1300.0}]
2025-08-09 05:30:10,031 - root - INFO - [{'percentage_in_debt': 0.0}]
2025-08-09 05:30:10,031 - root - INFO - 'No results'
2025-08-09 05:30:10,031 - root - INFO - 'No results'
2025-08-09 05:30:10,031 - root - INFO - 'No results'
2025-08-09 05:30:10,031 - root - INFO - 'No results'
2025-08-09 05:30:10,031 - root - INFO - 'No results'
2025-08-09 05:30:10,031 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-09 05:30:16,901 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:30:16,915 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-09 05:30:22,789 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:30:22,802 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:22,802 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 05:30:22,802 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:22,802 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which institution do students owe the most fees?'
2025-08-09 05:30:22,802 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 05:30:22,802 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 05:30:22,802 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 05:30:22,803 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 05:30:22,803 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total amount of fees owed by students at the institution where students owe the most fee...
2025-08-09 05:30:22,803 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total amount of fees owed by students at the institution where students owe the most fees is -$2...
2025-08-09 05:30:22,803 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 05:30:22,803 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_id': 1, 'total_fees_owed': -2600.0}]...
2025-08-09 05:30:22,803 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_fees_owed_by_students_at_top_institution
2025-08-09 05:30:22,803 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 05:30:22,803 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 05:30:22,803 - celery.redirected - WARNING - [{'institution_id': 1, 'total_fees_owed': -2600.0}]
2025-08-09 05:30:22,803 - celery.redirected - WARNING - ================================= 
2025-08-09 05:30:22,803 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 05:30:22,804 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:22,804 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 05:30:22,804 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which institution do students owe the most fees?'
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 05:30:22,804 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the fee debt at the institution where students owe the most fees compare to other instituti...
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there may not be any data available regarding fee...
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: fee_debt_comparison_results
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 05:30:22,804 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:22,804 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 05:30:22,804 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:22,804 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which institution do students owe the most fees?'
2025-08-09 05:30:22,805 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 05:30:22,805 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 05:30:22,805 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 05:30:22,805 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 05:30:22,805 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the average fee debt per student at the institution where students owe the most fees?...
2025-08-09 05:30:22,805 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The average fee debt per student at the institution where students owe the most fees is -1300.0. Thi...
2025-08-09 05:30:22,805 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 05:30:22,805 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'average_fee_debt': -1300.0}]...
2025-08-09 05:30:22,805 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_fee_debt_at_top_institution
2025-08-09 05:30:22,805 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 05:30:22,805 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 05:30:22,805 - celery.redirected - WARNING - [{'average_fee_debt': -1300.0}]
2025-08-09 05:30:22,805 - celery.redirected - WARNING - ================================= 
2025-08-09 05:30:22,805 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 05:30:22,806 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:22,806 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 05:30:22,806 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which institution do students owe the most fees?'
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 2
2025-08-09 05:30:22,806 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific programs or courses at the institution where students owe the most fees that cont...
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 2:
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific programs or courses at the institution where students owe the most fees that cont...
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there are currently no specific programs or cours...
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 05:30:22,806 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: programs_with_high_student_debts
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 2 documents, 0 with data_returned=True
2025-08-09 05:30:22,807 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:22,807 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 05:30:22,807 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which institution do students owe the most fees?'
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 05:30:22,807 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What percentage of students at the institution where students owe the most fees are currently in deb...
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At the institution where students owe the most fees, 0.0% of students are currently in debt. This in...
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'percentage_in_debt': 0.0}]...
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: percentage_of_students_in_debt_at_high_fee_institution
2025-08-09 05:30:22,807 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 05:30:22,807 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 05:30:22,807 - celery.redirected - WARNING - [{'percentage_in_debt': 0.0}]
2025-08-09 05:30:22,807 - celery.redirected - WARNING - ================================= 
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 05:30:22,808 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:22,808 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 05:30:22,808 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which institution do students owe the most fees?'
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 05:30:22,808 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What payment plans or financial aid options are available to students at the institution where stude...
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are currently no available payment plans or financial aid options listed for s...
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: financial_aid_options_for_highest_fee_institution
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 05:30:22,808 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:22,808 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 05:30:22,808 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:22,808 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which institution do students owe the most fees?'
2025-08-09 05:30:22,809 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 05:30:22,809 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 05:30:22,809 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 05:30:22,809 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 05:30:22,809 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the demographic breakdown of students who owe fees at the institution where students owe the...
2025-08-09 05:30:22,809 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no students who owe fees at the institution where students owe the most fe...
2025-08-09 05:30:22,809 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 05:30:22,809 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 05:30:22,809 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: demographic_breakdown_of_students_owing_fees
2025-08-09 05:30:22,809 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 05:30:22,809 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 05:30:22,809 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 8
2025-08-09 05:30:22,809 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:22,809 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-09 05:30:22,809 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:22,809 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 8 documents
2025-08-09 05:30:22,809 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-09 05:30:22,809 - UPSERT_DOCS - INFO -     Content: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:30:22,809 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:30:22,809 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Content: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:40.661012+00:00', 'data_returned': False}
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Content: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Content: Question: Are there specific programs or courses at the institution where students owe the most fees...
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:30:10.030245+00:00', 'data_returned': False}
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Content: Question: Are there specific programs or courses at the institution where students owe the most fees...
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-09T05:30:10.030245+00:00', 'data_returned': False}
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Content: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Content: Question: What payment plans or financial aid options are available to students at the institution w...
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:29:29.286451+00:00', 'data_returned': False}
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -   📄 Doc 8:
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Content: Question: What is the demographic breakdown of students who owe fees at the institution where studen...
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.888031+00:00', 'data_returned': False}
2025-08-09 05:30:22,810 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 3/8
2025-08-09 05:30:22,960 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.148s]
2025-08-09 05:30:25,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 05:30:27,481 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:1.547s]
2025-08-09 05:30:27,483 - UPSERT_DOCS - INFO - ✅ Successfully upserted 8 documents to Elasticsearch
2025-08-09 05:30:27,483 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 5
2025-08-09 05:30:27,483 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-09 05:30:27,484 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:27,485 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-09 05:30:27,485 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:27,485 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/5...
2025-08-09 05:30:28,028 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:30:28,033 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:28,033 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 05:30:28,033 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:28,034 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student fee debt higher education report'
2025-08-09 05:30:28,034 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which institution do students owe the most fees?'
2025-08-09 05:30:28,034 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which institution do students owe the most fees?'
2025-08-09 05:30:28,194 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.160s]
2025-08-09 05:30:28,194 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 22
2025-08-09 05:30:28,380 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.186s]
2025-08-09 05:30:28,381 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 10
2025-08-09 05:30:28,544 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.163s]
2025-08-09 05:30:28,545 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-09 05:30:28,682 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-09 05:30:28,682 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-09 05:30:29,705 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 05:30:29,870 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.161s]
2025-08-09 05:30:29,871 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-09 05:30:29,871 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:30:29,871 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:30:29,871 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:30:29,871 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:30:29,872 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:30:29,872 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:30:29,873 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:30:29,873 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:30:29,873 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:30:29,873 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:30:29,873 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:30:29,873 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:30:29,873 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1079 chars):
2025-08-09 05:30:29,873 - app.chains.section_writer - INFO -    Question: What is the average fee debt per student at the institution where students owe the most fees?
Answer: The average fee debt per student at the institution where students owe the most fees is -1300.0. This negative value suggests that, on average, students at this institution have a credit balance rather than a debt, indicating they may have overpaid their fees or received financial aid that exceeds their charges.

Question: What is the total amount of fees owed by students at the instit...
2025-08-09 05:30:32,439 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:30:32,451 - app.chains.section_writer - INFO - 🤖 AI generated section (733 chars):
2025-08-09 05:30:32,451 - app.chains.section_writer - INFO -    This report investigates the institution from which students owe the most fees, highlighting the significance of understanding student fee debt in higher education. The key finding indicates that students at the institution analyzed do not owe fees but instead have credit balances. Specifically, the...
2025-08-09 05:30:32,451 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-09 05:30:32,451 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 733 characters
2025-08-09 05:30:32,452 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/5...
2025-08-09 05:30:33,202 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:30:33,214 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:30:33,214 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 05:30:33,214 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:30:33,214 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'data collection interviews analysis themes'
2025-08-09 05:30:33,214 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which institution do students owe the most fees?'
2025-08-09 05:30:33,214 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which institution do students owe the most fees?'
2025-08-09 05:30:33,362 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.148s]
2025-08-09 05:30:33,363 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 22
2025-08-09 05:30:33,498 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-09 05:30:33,498 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 10
2025-08-09 05:30:33,631 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-09 05:30:33,632 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-09 05:33:27,724 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:174.092s]
2025-08-09 05:33:27,727 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-09 05:33:29,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 05:33:29,400 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.155s]
2025-08-09 05:33:29,401 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-09 05:33:29,402 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:33:29,402 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:33:29,402 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:33:29,402 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:33:29,402 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:33:29,402 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:33:29,403 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:33:29,403 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:33:29,403 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:33:29,403 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:33:29,403 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:33:29,403 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:33:29,403 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1079 chars):
2025-08-09 05:33:29,403 - app.chains.section_writer - INFO -    Question: What is the average fee debt per student at the institution where students owe the most fees?
Answer: The average fee debt per student at the institution where students owe the most fees is -1300.0. This negative value suggests that, on average, students at this institution have a credit balance rather than a debt, indicating they may have overpaid their fees or received financial aid that exceeds their charges.

Question: What is the total amount of fees owed by students at the instit...
2025-08-09 05:33:33,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:33:33,265 - app.chains.section_writer - INFO - 🤖 AI generated section (1258 chars):
2025-08-09 05:33:33,266 - app.chains.section_writer - INFO -    ## Methodology  

Data collection was conducted through a series of structured interviews with stakeholders, including students, faculty, and administrative staff. These interviews aimed to gather qualitative insights regarding the financial experiences of students, particularly in relation to fee p...
2025-08-09 05:33:33,266 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-09 05:33:33,266 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 1258 characters
2025-08-09 05:33:33,267 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/5...
2025-08-09 05:33:34,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:33:34,386 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:33:34,387 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 05:33:34,387 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:33:34,387 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'total fees owed average debt percentage of students in debt'
2025-08-09 05:33:34,387 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which institution do students owe the most fees?'
2025-08-09 05:33:34,387 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which institution do students owe the most fees?'
2025-08-09 05:33:34,517 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-09 05:33:34,517 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 22
2025-08-09 05:33:34,647 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-09 05:33:34,648 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 10
2025-08-09 05:33:34,814 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.166s]
2025-08-09 05:33:34,814 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-09 05:33:34,946 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-09 05:33:34,947 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-09 05:33:58,970 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 05:33:59,137 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.162s]
2025-08-09 05:33:59,138 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-09 05:33:59,138 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:33:59,138 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:33:59,138 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:33:59,138 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:33:59,139 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:33:59,139 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:33:59,139 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:33:59,140 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:33:59,140 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:33:59,140 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:33:59,140 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:33:59,140 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:33:59,140 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1079 chars):
2025-08-09 05:33:59,140 - app.chains.section_writer - INFO -    Question: What is the average fee debt per student at the institution where students owe the most fees?
Answer: The average fee debt per student at the institution where students owe the most fees is -1300.0. This negative value suggests that, on average, students at this institution have a credit balance rather than a debt, indicating they may have overpaid their fees or received financial aid that exceeds their charges.

Question: What percentage of students at the institution where students o...
2025-08-09 05:34:01,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:01,830 - app.chains.section_writer - INFO - 🤖 AI generated section (640 chars):
2025-08-09 05:34:01,830 - app.chains.section_writer - INFO -    ## Findings  

### Total Fees Owed  
- **Institution with the Most Fees**  
  - Total amount of fees owed: -$2,600  
  - Interpretation: Negative value indicates a credit balance rather than outstanding fees.

### Average Fee Debt per Student  
- **Average Debt Analysis**  
  - Average fee debt per ...
2025-08-09 05:34:01,831 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-09 05:34:01,831 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 640 characters
2025-08-09 05:34:01,831 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/5...
2025-08-09 05:34:02,602 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:02,610 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:34:02,611 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 05:34:02,611 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:34:02,611 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'students credit balances financial management'
2025-08-09 05:34:02,611 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which institution do students owe the most fees?'
2025-08-09 05:34:02,611 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which institution do students owe the most fees?'
2025-08-09 05:34:02,760 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.149s]
2025-08-09 05:34:02,761 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 22
2025-08-09 05:34:02,889 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-09 05:34:02,889 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 10
2025-08-09 05:34:03,019 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 05:34:03,019 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-09 05:34:03,149 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 05:34:03,150 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-09 05:34:03,669 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 05:34:03,826 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.153s]
2025-08-09 05:34:03,826 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-09 05:34:03,827 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:34:03,827 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:34:03,827 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:34:03,827 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:34:03,827 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:34:03,827 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:34:03,828 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:34:03,828 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:34:03,828 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:34:03,828 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:34:03,828 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:34:03,828 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:34:03,828 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1079 chars):
2025-08-09 05:34:03,828 - app.chains.section_writer - INFO -    Question: What is the total amount of fees owed by students at the institution where students owe the most fees?
Answer: The total amount of fees owed by students at the institution where students owe the most fees is -$2,600. This negative value may indicate that students at this institution have a credit balance rather than owing fees.

Question: What is the average fee debt per student at the institution where students owe the most fees?
Answer: The average fee debt per student at the institu...
2025-08-09 05:34:07,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:07,344 - app.chains.section_writer - INFO - 🤖 AI generated section (1038 chars):
2025-08-09 05:34:07,344 - app.chains.section_writer - INFO -    ## Discussion  

The analysis reveals that all indicators suggest students at the institution do not owe fees but rather have credit balances. Specifically, the total amount of fees owed by students is -$2,600, indicating a collective credit balance. Additionally, the average fee debt per student is...
2025-08-09 05:34:07,344 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-09 05:34:07,344 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 1038 characters
2025-08-09 05:34:07,345 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/5...
2025-08-09 05:34:08,136 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:08,146 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:34:08,146 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 05:34:08,147 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:34:08,147 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student fee debt conclusion'
2025-08-09 05:34:08,147 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which institution do students owe the most fees?'
2025-08-09 05:34:08,147 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which institution do students owe the most fees?'
2025-08-09 05:34:08,285 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.138s]
2025-08-09 05:34:08,285 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 22
2025-08-09 05:34:08,412 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-09 05:34:08,412 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 10
2025-08-09 05:34:08,540 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-09 05:34:08,540 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 8
2025-08-09 05:34:08,666 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-09 05:34:08,667 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 3
2025-08-09 05:34:09,198 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 05:34:09,357 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.155s]
2025-08-09 05:34:09,357 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 3 documents
2025-08-09 05:34:09,357 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:34:09,358 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:34:09,358 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:34:09,358 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:34:09,358 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:34:09,358 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:34:09,359 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 05:34:09,359 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.614193+00:00', 'data_returned': True}
2025-08-09 05:34:09,359 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 05:34:09,359 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:17.634295+00:00', 'data_returned': True}
2025-08-09 05:34:09,359 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 05:34:09,359 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which institution do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:28:44.554741+00:00', 'data_returned': True}
2025-08-09 05:34:09,359 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1079 chars):
2025-08-09 05:34:09,359 - app.chains.section_writer - INFO -    Question: What is the average fee debt per student at the institution where students owe the most fees?
Answer: The average fee debt per student at the institution where students owe the most fees is -1300.0. This negative value suggests that, on average, students at this institution have a credit balance rather than a debt, indicating they may have overpaid their fees or received financial aid that exceeds their charges.

Question: What is the total amount of fees owed by students at the instit...
2025-08-09 05:34:12,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:12,330 - app.chains.section_writer - INFO - 🤖 AI generated section (882 chars):
2025-08-09 05:34:12,330 - app.chains.section_writer - INFO -    In conclusion, the analysis of student fee debt at the institution reveals that, on average, students have a credit balance of -$1,300. This suggests that students may have overpaid their fees or received financial aid exceeding their charges. Furthermore, the total amount of fees owed by students i...
2025-08-09 05:34:12,330 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-09 05:34:12,330 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 882 characters
2025-08-09 05:34:12,331 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:34:12,331 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-09 05:34:12,331 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:34:12,331 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-09 05:34:12,331 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 5
2025-08-09 05:34:12,331 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 7
2025-08-09 05:34:12,331 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 0
2025-08-09 05:34:12,331 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250809_052720.log
2025-08-09 05:34:12,337 - celery.app.trace - INFO - Task generate_streaming_report[168dd02d-6a19-4d29-a411-6a613502a58f] succeeded in 298.62869212499936s: {'outline': '# Report on Student Fee Debt by Institution

## Introduction  
- This report investigates the institution from which students owe the most fees, highlighting the significance of understanding student fee debt in higher education. The key finding indicates that students at the institution analyzed do not owe fees but instead have credit balances.

## Methodology  
- Description of data collection through interviews.  
- Explanation of how responses were analyzed to extract key themes.

## Findings  
### Total Fees Owed  
- **Institution with the Most Fees**  
  - Total amount of fees owed: -$2,600  
  - Interpretation: Negative value indicates a credit balance rather than outstanding fees.

### Average Fee Debt per Student  
- **Average Debt Analysis**  
  - Average fee debt per student: -$1,300  
  - Interpretation: Negative average suggests students have a credit balance, possibly due to overpayment or financial aid.

### Percentage of Students in Debt  
- **Debt Status of Students**  
  -...', , ...}
2025-08-09 05:34:12,340 - celery.worker.strategy - INFO - Task generate_streaming_report[1fb15329-d63c-4fde-b22b-182c707c6b2a] received
2025-08-09 05:34:12,341 - app.tasks.report_task - INFO - starting streaming report generation...
2025-08-09 05:34:12,341 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:34:12,341 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 43386e93-91a7-445c-9ddc-e20c87870b5e
2025-08-09 05:34:12,341 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:34:12,341 - REPORT_REQUEST - INFO - 📝 Original Question: 'From which institution do students owe the most fees?'
2025-08-09 05:34:12,341 - REPORT_REQUEST - INFO - 🆔 Task ID: 43386e93-91a7-445c-9ddc-e20c87870b5e
2025-08-09 05:34:12,341 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-09T05:34:12.341531
2025-08-09 05:34:12,509 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.168s]
2025-08-09 05:34:12,510 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 22
2025-08-09 05:34:12,712 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.203s]
2025-08-09 05:34:12,713 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 10
2025-08-09 05:34:12,938 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.225s]
2025-08-09 05:34:12,938 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (3):
2025-08-09 05:34:12,939 - ELASTICSEARCH_STATE - INFO -   - 'From which institution do students owe the most fees?' (8 docs)
2025-08-09 05:34:12,939 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (7 docs)
2025-08-09 05:34:12,939 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (7 docs)
2025-08-09 05:34:12,939 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:34:12,939 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-09 05:34:12,939 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:34:12,939 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: From which institution do students owe the most fees?
2025-08-09 05:34:12,939 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-09 05:34:12,939 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-09 05:34:20,825 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:20,841 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-09 05:34:25,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:25,501 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-09 05:34:28,188 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:28,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:28,371 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:28,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:28,735 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:28,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:28,901 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:28,941 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:29,289 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:29,683 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:29,750 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-09 05:34:32,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:32,359 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:32,590 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:32,603 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:32,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:32,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:32,714 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:32,810 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:33,159 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:34,798 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:34,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:35,412 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:35,552 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:35,735 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:35,764 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:35,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:36,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:36,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:36,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:36,953 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:36,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:36,984 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:37,191 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:37,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:37,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:37,869 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:38,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:41,188 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:42,051 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:42,163 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:42,249 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:42,887 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:42,888 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:43,541 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:44,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:45,617 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:47,631 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:48,231 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:48,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:48,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:48,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:48,885 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the fee debt at the institution where students owe the most fees compare to other institutions?', 'sql': 'WITH InstitutionDebt AS (  SELECT institution_id, SUM(balance) AS total_debt  FROM student_balances  GROUP BY institution_id), MaxDebtInstitution AS (  SELECT institution_id, total_debt  FROM InstitutionDebt  ORDER BY total_debt DESC  LIMIT 1) SELECT i.name AS institution_name, d.total_debt, (SELECT AVG(total_debt) FROM InstitutionDebt) AS average_debt FROM MaxDebtInstitution d JOIN institutions i ON d.institution_id = i.id;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the highest total debt by summing the balances for each institution and then selecting the one with the maximum debt. It also retrieves the average debt across all institutions, which allows for a comparison between the maximum debt and the average debt. This directly addresses the question of how the fee debt at the institution with the highest debt compares to other institutions.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be enhanced by also including the total debts of all institutions in the final output for a more comprehensive comparison, rather than just the average debt.'}
2025-08-09 05:34:48,885 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:34:48,885 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:34:48,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:51,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:51,474 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:51,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:51,632 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:51,633 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How many students are currently enrolled at the institution where students owe the most fees?', 'sql': "SELECT COUNT(DISTINCT s.id) AS enrolled_students\nFROM students s\nJOIN student_balances sb ON s.id = sb.student_id\nJOIN bills b ON sb.billing_period_id = b.billing_period_id\nJOIN institutions i ON s.institution_id = i.id\nWHERE s.status = 'active'\nAND sb.balance > 0\nAND i.id = (\n    SELECT sb2.institution_id\n    FROM student_balances sb2\n    JOIN bills b2 ON sb2.billing_period_id = b2.billing_period_id\n    GROUP BY sb2.institution_id\n    ORDER BY SUM(b2.total_due) DESC\n    LIMIT 1\n);", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution where students owe the most fees by summing the total due from the bills associated with each institution. It then counts the distinct active students who have a positive balance at that institution. The use of a subquery to find the institution with the highest total due is appropriate and aligns with the question's requirement.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be beneficial to clarify the definition of 'currently enrolled' in the question to ensure it aligns with the criteria used in the query (i.e., students with an 'active' status)."}
2025-08-09 05:34:51,633 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:34:51,633 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:34:51,867 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:51,983 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:52,091 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:52,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:52,786 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:52,795 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What percentage of students at the institution where students owe the most fees are in arrears on their payments?', 'sql': 'WITH InstitutionArrears AS (  SELECT sb.institution_id, SUM(sb.balance) AS total_balance  FROM student_balances sb  JOIN student_bills s ON sb.student_id = s.student_id  GROUP BY sb.institution_id), MaxInstitution AS (  SELECT institution_id  FROM InstitutionArrears  ORDER BY total_balance DESC  LIMIT 1), TotalStudents AS (  SELECT COUNT(*) AS total_students  FROM students s  WHERE s.institution_id = (SELECT institution_id FROM MaxInstitution)), ArrearsStudents AS (  SELECT COUNT(*) AS students_in_arrears  FROM student_balances sb  JOIN students s ON sb.student_id = s.id  WHERE sb.balance > 0 AND s.institution_id = (SELECT institution_id FROM MaxInstitution)) SELECT (CAST(ArrearsStudents.students_in_arrears AS FLOAT) / TotalStudents.total_students) * 100 AS percentage_in_arrears FROM TotalStudents, ArrearsStudents;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the highest total balance owed by students and calculates the percentage of students in arrears at that institution. It does this by first aggregating the total balances by institution, then selecting the institution with the maximum balance, counting the total number of students at that institution, and finally counting how many of those students are in arrears (i.e., have a positive balance). The final calculation of the percentage is also correctly formulated.', 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by adding comments to clarify each step of the CTEs for better readability and maintainability. Additionally, ensure that the definition of 'in arrears' is consistent with the business logic, as it is currently defined as having a positive balance."}
2025-08-09 05:34:52,795 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:34:52,795 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:34:53,010 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:53,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:54,276 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:54,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:54,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:54,406 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:54,496 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:55,192 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:55,462 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:55,608 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:55,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:55,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:55,820 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:55,821 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:55,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:56,533 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:56,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:56,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:57,031 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:57,430 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:57,497 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:58,759 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:59,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:59,578 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:59,592 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What specific factors contribute to the high fee debt at Central University compared to other institutions, and how do these factors influence students' financial decisions?", 'answerable': False, 'reasoning': "The question seeks to identify specific factors contributing to high fee debt at Central University compared to other institutions, as well as the influence of these factors on students' financial decisions. However, the provided schema does not contain direct information about the reasons for fee debt or the financial decision-making processes of students. While there are tables related to financial transactions, student bills, and institutions, there is no explicit data or analysis available in the schema that would allow for a comprehensive answer to the question regarding the specific factors and their influence on financial decisions.", 'feedback': ''}
2025-08-09 05:34:59,592 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:34:59,592 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:34:59,592 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What specific factors contribute to the high fee debt at Central University compared to other institutions, and how do these factors influence students' financial decisions?", 'answerable': False, 'reasoning': "The question seeks to identify specific factors contributing to high fee debt at Central University compared to other institutions, as well as the influence of these factors on students' financial decisions. However, the provided schema does not contain direct information about the reasons for fee debt or the financial decision-making processes of students. While there are tables related to financial transactions, student bills, and institutions, there is no explicit data or analysis available in the schema that would allow for a comprehensive answer to the question regarding the specific factors and their influence on financial decisions.", 'feedback': ''}
2025-08-09 05:34:59,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:34:59,789 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:00,212 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:01,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:01,526 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:01,539 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high fee debt among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt among students, which requires qualitative insights and potentially complex data analysis. The schema provides various tables related to students, fees, and transactions, but it does not contain direct information or metrics that would allow for a comprehensive analysis of the factors leading to high fee debt. Factors could include student demographics, financial aid, payment history, and other contextual data that are not explicitly represented in the schema.', 'feedback': ''}
2025-08-09 05:35:01,539 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:01,539 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:01,539 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high fee debt among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt among students, which requires qualitative insights and potentially complex data analysis. The schema provides various tables related to students, fees, and transactions, but it does not contain direct information or metrics that would allow for a comprehensive analysis of the factors leading to high fee debt. Factors could include student demographics, financial aid, payment history, and other contextual data that are not explicitly represented in the schema.', 'feedback': ''}
2025-08-09 05:35:02,248 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:02,264 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of arrears among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the absence of arrears among students, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to students, their financial transactions, and academic records, but it does not include specific qualitative data or insights that would allow for an analysis of factors leading to the absence of arrears. To answer this question, one would need qualitative data such as student feedback, institutional policies, or financial support programs, none of which are present in the schema.', 'feedback': ''}
2025-08-09 05:35:02,264 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:02,264 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:02,264 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of arrears among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the absence of arrears among students, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to students, their financial transactions, and academic records, but it does not include specific qualitative data or insights that would allow for an analysis of factors leading to the absence of arrears. To answer this question, one would need qualitative data such as student feedback, institutional policies, or financial support programs, none of which are present in the schema.', 'feedback': ''}
2025-08-09 05:35:02,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:03,674 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:04,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:04,698 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:05,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:05,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:05,426 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:05,426 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What specific factors contribute to the high fee debt at Central University compared to other institutions, and how do these factors influence students' financial decisions?", 'answerable': False, 'reasoning': "The question seeks to identify specific factors contributing to high fee debt at Central University compared to other institutions, as well as the influence of these factors on students' financial decisions. However, the provided schema does not contain direct information about the reasons for fee debt or the financial decision-making processes of students. While there are tables related to financial transactions, student bills, and institutions, there is no explicit data or analysis available in the schema that would allow for a comprehensive answer to the question regarding the specific factors and their influence on financial decisions.", 'feedback': ''}
2025-08-09 05:35:05,427 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:05,427 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:05,427 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What specific factors contribute to the high fee debt at Central University compared to other institutions, and how do these factors influence students' financial decisions?", 'answerable': False, 'reasoning': "The question seeks to identify specific factors contributing to high fee debt at Central University compared to other institutions, as well as the influence of these factors on students' financial decisions. However, the provided schema does not contain direct information about the reasons for fee debt or the financial decision-making processes of students. While there are tables related to financial transactions, student bills, and institutions, there is no explicit data or analysis available in the schema that would allow for a comprehensive answer to the question regarding the specific factors and their influence on financial decisions.", 'feedback': ''}
2025-08-09 05:35:05,432 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total amount of fees owed by students at the institution where students owe the most fees?', 'sql': 'SELECT institution_id, SUM(balance) AS total_fees_owed\nFROM student_balances\nGROUP BY institution_id\nORDER BY total_fees_owed DESC\nLIMIT 1;', 'correct': True, 'reasoning': "The SQL query correctly aggregates the total balance owed by students for each institution by summing the 'balance' field in the 'student_balances' table. It groups the results by 'institution_id' and orders them in descending order based on the total fees owed. The use of 'LIMIT 1' ensures that only the institution with the highest total fees owed is returned, which directly answers the question.", 'feedback': 'The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to include a check for cases where there are no balances recorded to avoid potential errors.'}
2025-08-09 05:35:05,433 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:05,433 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:35:06,132 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:06,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:07,698 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:07,719 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high fee debt among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt among students, which requires qualitative insights and potentially a combination of various data points such as student financial records, payment history, and possibly socio-economic factors. The schema primarily contains structured data about students, their programs, transactions, and institutional settings, but it does not provide direct insights or analytical capabilities to assess the reasons behind high fee debt. Therefore, while some data may be relevant, the question cannot be answered completely or correctly from the schema alone.', 'feedback': ''}
2025-08-09 05:35:07,720 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:07,720 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:07,720 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high fee debt among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt among students, which requires qualitative insights and potentially a combination of various data points such as student financial records, payment history, and possibly socio-economic factors. The schema primarily contains structured data about students, their programs, transactions, and institutional settings, but it does not provide direct insights or analytical capabilities to assess the reasons behind high fee debt. Therefore, while some data may be relevant, the question cannot be answered completely or correctly from the schema alone.', 'feedback': ''}
2025-08-09 05:35:07,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:08,145 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:08,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:08,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:08,371 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of arrears among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the absence of arrears among students, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to students, their financial transactions, and academic records, but it does not include specific qualitative data or insights that would allow for an analysis of factors influencing the absence of arrears. To answer this question, one would need qualitative data, such as surveys or interviews with students, which is not present in the schema.', 'feedback': ''}
2025-08-09 05:35:08,371 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:08,371 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:08,371 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of arrears among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the absence of arrears among students, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to students, their financial transactions, and academic records, but it does not include specific qualitative data or insights that would allow for an analysis of factors influencing the absence of arrears. To answer this question, one would need qualitative data, such as surveys or interviews with students, which is not present in the schema.', 'feedback': ''}
2025-08-09 05:35:08,566 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:09,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:09,881 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:10,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:10,232 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific programs or courses at the institution where students owe the most fees that have higher associated costs?', 'sql': 'SELECT p.long_name AS program_name, c.title AS course_title, SUM(bi.amount) AS total_fees owed\nFROM student_bills sb\nJOIN bill_items bi ON sb.bill_id = bi.bill_id\nJOIN core.programs p ON sb.student_program_id = p.id\nJOIN core.courses c ON p.unit_id = c.unit_id\nGROUP BY p.long_name, c.title\nORDER BY total_fees DESC;', 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses associated with student bills and calculates the total fees owed by summing the amounts from the bill_items table. It groups the results by program name and course title, which aligns with the question's request for specific programs or courses. The ordering by total fees in descending order ensures that the programs or courses with the highest fees owed are prioritized, directly addressing the question's focus on higher associated costs.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by adding a WHERE clause to filter for specific institutions if that context is necessary, as the question implies a focus on a particular institution. Additionally, clarifying what is meant by 'higher associated costs' could help refine the query further, such as specifying a threshold for what constitutes 'higher costs'."}
2025-08-09 05:35:10,232 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:10,232 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:35:10,740 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:10,828 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:11,021 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:11,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:11,278 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What specific factors contribute to the high fee debt at Central University compared to other institutions, and how do these factors influence students' financial decisions?", 'answerable': False, 'reasoning': "The question seeks to identify specific factors contributing to high fee debt at Central University compared to other institutions, as well as the influence of these factors on students' financial decisions. However, the provided schema does not contain direct information about the reasons for fee debt or the financial decision-making processes of students. While there are tables related to financial transactions, student bills, and institutions, there is no explicit data or analysis available in the schema that would allow for a comprehensive answer to the question regarding the specific factors and their influence on financial decisions.", 'feedback': ''}
2025-08-09 05:35:11,278 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:11,278 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:11,278 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What specific factors contribute to the high fee debt at Central University compared to other institutions, and how do these factors influence students' financial decisions?", 'answerable': False, 'reasoning': "The question seeks to identify specific factors contributing to high fee debt at Central University compared to other institutions, as well as the influence of these factors on students' financial decisions. However, the provided schema does not contain direct information about the reasons for fee debt or the financial decision-making processes of students. While there are tables related to financial transactions, student bills, and institutions, there is no explicit data or analysis available in the schema that would allow for a comprehensive answer to the question regarding the specific factors and their influence on financial decisions.", 'feedback': ''}
2025-08-09 05:35:11,798 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:11,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:12,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:12,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:13,229 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:13,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:13,723 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high fee debt among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt among students, which requires qualitative insights and possibly statistical analysis. The schema provides data on students, their financial transactions, and related entities, but it does not contain direct information or metrics that would allow for a comprehensive analysis of the factors leading to high fee debt. To answer this question, one would need to analyze trends, reasons for non-payment, and possibly student demographics or financial aid data, none of which are explicitly detailed in the schema.', 'feedback': ''}
2025-08-09 05:35:13,723 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:13,723 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:13,723 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high fee debt among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt among students, which requires qualitative insights and possibly statistical analysis. The schema provides data on students, their financial transactions, and related entities, but it does not contain direct information or metrics that would allow for a comprehensive analysis of the factors leading to high fee debt. To answer this question, one would need to analyze trends, reasons for non-payment, and possibly student demographics or financial aid data, none of which are explicitly detailed in the schema.', 'feedback': ''}
2025-08-09 05:35:13,994 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:14,089 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:14,109 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of arrears among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the absence of arrears among students, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to students, their financial transactions, and academic records, but it does not include specific qualitative data or insights that would allow for an analysis of factors influencing the absence of arrears. To answer this question, one would need qualitative data, possibly from surveys or interviews, which is not present in the schema.', 'feedback': ''}
2025-08-09 05:35:14,110 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:14,110 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:14,110 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of arrears among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the absence of arrears among students, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to students, their financial transactions, and academic records, but it does not include specific qualitative data or insights that would allow for an analysis of factors influencing the absence of arrears. To answer this question, one would need qualitative data, possibly from surveys or interviews, which is not present in the schema.', 'feedback': ''}
2025-08-09 05:35:14,268 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:14,806 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:15,143 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:15,155 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. This requires qualitative insights and potentially complex calculations or aggregations that are not directly supported by the schema. The schema provides data on student transactions, balances, and related entities, but it does not include the necessary analytical framework or qualitative data to assess the factors and impacts comprehensively. Therefore, while some data may be available, it is insufficient to fully answer the question.', 'feedback': ''}
2025-08-09 05:35:15,155 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:15,155 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:15,156 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. This requires qualitative insights and potentially complex calculations or aggregations that are not directly supported by the schema. The schema provides data on student transactions, balances, and related entities, but it does not include the necessary analytical framework or qualitative data to assess the factors and impacts comprehensively. Therefore, while some data may be available, it is insufficient to fully answer the question.', 'feedback': ''}
2025-08-09 05:35:15,552 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:15,867 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:16,047 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:16,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:17,193 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:17,211 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What specific factors contribute to the high fee debt at Central University compared to other institutions, and how do these factors influence students' financial decisions?", 'answerable': False, 'reasoning': "The question seeks to identify specific factors contributing to high fee debt at Central University compared to other institutions, as well as the influence of these factors on students' financial decisions. However, the provided schema does not contain direct information about the reasons for fee debt or the financial decision-making processes of students. While there are tables related to financial transactions, student bills, and institutions, there is no explicit data or analysis available in the schema that would allow for a comprehensive answer to the question regarding the specific factors and their influence on financial decisions.", 'feedback': ''}
2025-08-09 05:35:17,211 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:17,211 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:17,211 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What specific factors contribute to the high fee debt at Central University compared to other institutions, and how do these factors influence students' financial decisions?", 'answerable': False, 'reasoning': "The question seeks to identify specific factors contributing to high fee debt at Central University compared to other institutions, as well as the influence of these factors on students' financial decisions. However, the provided schema does not contain direct information about the reasons for fee debt or the financial decision-making processes of students. While there are tables related to financial transactions, student bills, and institutions, there is no explicit data or analysis available in the schema that would allow for a comprehensive answer to the question regarding the specific factors and their influence on financial decisions.", 'feedback': ''}
2025-08-09 05:35:17,225 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:17,600 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:17,810 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:18,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:18,390 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:18,411 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:19,058 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:19,546 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:19,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:19,562 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the high fee debt among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt among students, which requires qualitative insights and potentially complex data analysis. The schema provides various tables related to students, fees, and transactions, but it does not contain direct information or metrics that would allow for a comprehensive analysis of the factors leading to high fee debt. To answer this question, one would need to analyze trends, student demographics, financial aid data, and possibly external factors, none of which are explicitly represented in the schema.', 'feedback': ''}
2025-08-09 05:35:19,562 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of arrears among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the absence of arrears among students, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to students, their financial transactions, and academic records, but it does not include specific qualitative data or insights that would allow for an analysis of factors leading to the absence of arrears. To answer this question, one would need qualitative data, possibly from surveys or interviews, which is not present in the schema.', 'feedback': ''}
2025-08-09 05:35:19,562 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:19,562 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:19,563 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:19,563 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:19,563 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the high fee debt among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt among students, which requires qualitative insights and potentially complex data analysis. The schema provides various tables related to students, fees, and transactions, but it does not contain direct information or metrics that would allow for a comprehensive analysis of the factors leading to high fee debt. To answer this question, one would need to analyze trends, student demographics, financial aid data, and possibly external factors, none of which are explicitly represented in the schema.', 'feedback': ''}
2025-08-09 05:35:19,563 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the absence of arrears among students at this institution?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors contributing to the absence of arrears among students, which is not directly quantifiable or represented in the provided database schema. The schema contains various tables related to students, their financial transactions, and academic records, but it does not include specific qualitative data or insights that would allow for an analysis of factors leading to the absence of arrears. To answer this question, one would need qualitative data, possibly from surveys or interviews, which is not present in the schema.', 'feedback': ''}
2025-08-09 05:35:20,057 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:20,070 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. The schema provides various tables related to students, transactions, and financial records, but it does not contain specific analytical data or insights into the reasons behind credit balances or their implications on financial experiences. The schema lacks the necessary context and qualitative data to answer such a complex question.', 'feedback': ''}
2025-08-09 05:35:20,071 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:20,071 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:20,071 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. The schema provides various tables related to students, transactions, and financial records, but it does not contain specific analytical data or insights into the reasons behind credit balances or their implications on financial experiences. The schema lacks the necessary context and qualitative data to answer such a complex question.', 'feedback': ''}
2025-08-09 05:35:20,775 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:20,961 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:21,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:21,304 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to the overall fee structure of programs at the institution, and how might these factors impact students' financial decisions?", 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to the overall fee structure of programs at the institution and their impact on students' financial decisions. While the schema contains tables related to fees, programs, and financial transactions, it does not provide qualitative data or insights into the factors influencing fee structures or students' financial decisions. The schema lacks information on external factors, institutional policies, or student perspectives that would be necessary to answer the question comprehensively.", 'feedback': ''}
2025-08-09 05:35:21,304 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:21,304 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:21,305 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to the overall fee structure of programs at the institution, and how might these factors impact students' financial decisions?", 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to the overall fee structure of programs at the institution and their impact on students' financial decisions. While the schema contains tables related to fees, programs, and financial transactions, it does not provide qualitative data or insights into the factors influencing fee structures or students' financial decisions. The schema lacks information on external factors, institutional policies, or student perspectives that would be necessary to answer the question comprehensively.", 'feedback': ''}
2025-08-09 05:35:22,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:23,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:23,216 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:23,744 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:24,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:24,963 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. While the schema contains various tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or how these balances affect students' financial experiences, which would require qualitative data or analysis beyond the available tables.", 'feedback': ''}
2025-08-09 05:35:24,963 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:24,963 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:24,963 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. While the schema contains various tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or how these balances affect students' financial experiences, which would require qualitative data or analysis beyond the available tables.", 'feedback': ''}
2025-08-09 05:35:25,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:26,042 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:26,060 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to the overall fee structure of programs at the institution, and how might these factors impact students' financial decisions?", 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to the overall fee structure of programs at the institution and their impact on students' financial decisions. While the schema contains tables related to fees, programs, and financial transactions, it does not provide qualitative data or insights into the specific factors influencing fee structures or how these factors affect students' financial decisions. The schema lacks the necessary context and analytical data to answer such a subjective and complex question.", 'feedback': ''}
2025-08-09 05:35:26,060 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:26,060 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:26,060 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to the overall fee structure of programs at the institution, and how might these factors impact students' financial decisions?", 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to the overall fee structure of programs at the institution and their impact on students' financial decisions. While the schema contains tables related to fees, programs, and financial transactions, it does not provide qualitative data or insights into the specific factors influencing fee structures or how these factors affect students' financial decisions. The schema lacks the necessary context and analytical data to answer such a subjective and complex question.", 'feedback': ''}
2025-08-09 05:35:27,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:27,968 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:28,396 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:28,833 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:29,546 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:29,551 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average fee amount owed per student at the institution where students owe the most fees?', 'sql': 'SELECT SUM(total_due) / COUNT(DISTINCT student_id) AS average_fee_per_student FROM student_bills WHERE institution_id = (SELECT institution_id FROM student_bills GROUP BY institution_id ORDER BY SUM(total_due) DESC LIMIT 1)', 'correct': True, 'reasoning': 'The SQL query correctly calculates the average fee amount owed per student at the institution where students owe the most fees. It first identifies the institution with the highest total fees owed by using a subquery that groups the records by institution_id, sums the total_due for each, and orders them in descending order to get the institution with the highest sum. Then, it calculates the average fee by dividing the total fees owed (SUM(total_due)) by the number of distinct students (COUNT(DISTINCT student_id)) for that institution. This aligns perfectly with the question asked.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be beneficial to include a check for cases where there are no students or no fees owed to avoid potential division by zero errors.'}
2025-08-09 05:35:29,551 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:29,552 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:35:29,604 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:29,618 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. This requires qualitative insights and potentially complex calculations or aggregations that are not directly supported by the schema. The schema provides data on student transactions, balances, and related entities, but it does not include the necessary context or analytical framework to derive insights about the factors influencing credit balances or their implications on financial experiences.', 'feedback': ''}
2025-08-09 05:35:29,618 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:29,618 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:29,618 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. This requires qualitative insights and potentially complex calculations or aggregations that are not directly supported by the schema. The schema provides data on student transactions, balances, and related entities, but it does not include the necessary context or analytical framework to derive insights about the factors influencing credit balances or their implications on financial experiences.', 'feedback': ''}
2025-08-09 05:35:30,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:30,425 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to the overall fee structure of programs at the institution, and how might these factors impact students' financial decisions?", 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to the overall fee structure of programs at the institution and their impact on students' financial decisions. While the schema contains tables related to fees, programs, and students, it does not provide specific data or insights into the qualitative factors that influence fee structures or financial decisions. The schema lacks information on external factors, institutional policies, or student perspectives that would be necessary to answer the question comprehensively.", 'feedback': ''}
2025-08-09 05:35:30,425 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:30,425 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:30,425 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to the overall fee structure of programs at the institution, and how might these factors impact students' financial decisions?", 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to the overall fee structure of programs at the institution and their impact on students' financial decisions. While the schema contains tables related to fees, programs, and students, it does not provide specific data or insights into the qualitative factors that influence fee structures or financial decisions. The schema lacks information on external factors, institutional policies, or student perspectives that would be necessary to answer the question comprehensively.", 'feedback': ''}
2025-08-09 05:35:30,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:31,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:32,055 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:32,960 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:32,961 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:33,816 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:33,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:34,053 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:34,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:35,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:35,133 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to the overall fee structure of programs at the institution, and how might these factors impact students' financial decisions?", 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to the overall fee structure of programs at the institution and their impact on students' financial decisions. While the schema contains tables related to fees, programs, and financial transactions, it does not provide qualitative data or insights into the factors influencing fee structures or how these factors affect students' financial decisions. The schema lacks the necessary context, such as economic conditions, institutional policies, or student perspectives, to answer this question comprehensively.", 'feedback': ''}
2025-08-09 05:35:35,133 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:35,133 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:35,133 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to the overall fee structure of programs at the institution, and how might these factors impact students' financial decisions?", 'answerable': False, 'reasoning': "The question asks for an analysis of the factors contributing to the overall fee structure of programs at the institution and their impact on students' financial decisions. While the schema contains tables related to fees, programs, and financial transactions, it does not provide qualitative data or insights into the factors influencing fee structures or how these factors affect students' financial decisions. The schema lacks the necessary context, such as economic conditions, institutional policies, or student perspectives, to answer this question comprehensively.", 'feedback': ''}
2025-08-09 05:35:36,745 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:36,788 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:37,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:37,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:38,695 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:39,777 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:39,794 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any contextual factors that could explain this situation.', 'feedback': ''}
2025-08-09 05:35:39,794 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:39,795 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:39,795 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any contextual factors that could explain this situation.', 'feedback': ''}
2025-08-09 05:35:40,640 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:41,958 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:41,979 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:42,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:44,318 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:44,338 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. This is a qualitative inquiry that requires contextual understanding and analysis of the data management practices, policies, or external factors affecting data availability. The schema provides tables related to financial transactions, student billing, and institutional data, but it does not contain any information about the reasons or factors that might lead to a lack of data. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-09 05:35:44,338 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:44,338 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:44,338 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. This is a qualitative inquiry that requires contextual understanding and analysis of the data management practices, policies, or external factors affecting data availability. The schema provides tables related to financial transactions, student billing, and institutional data, but it does not contain any information about the reasons or factors that might lead to a lack of data. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-09 05:35:45,726 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:46,259 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:48,320 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:48,334 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any contextual factors that could explain this situation.', 'feedback': ''}
2025-08-09 05:35:48,334 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:48,334 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:48,334 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any contextual factors that could explain this situation.', 'feedback': ''}
2025-08-09 05:35:48,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:49,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:50,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:52,358 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:53,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:53,306 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question is asking for factors contributing to the lack of available data on outstanding fees at an institution. This is a qualitative inquiry that requires contextual understanding and analysis of the data management practices, policies, or external factors affecting data availability. The schema provides tables related to financial transactions, student billing, and institutional data, but it does not contain any information about the reasons or factors that might lead to a lack of data. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-09 05:35:53,307 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:35:53,307 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:35:53,307 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question is asking for factors contributing to the lack of available data on outstanding fees at an institution. This is a qualitative inquiry that requires contextual understanding and analysis of the data management practices, policies, or external factors affecting data availability. The schema provides tables related to financial transactions, student billing, and institutional data, but it does not contain any information about the reasons or factors that might lead to a lack of data. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-09 05:35:54,055 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:55,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:56,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:35:57,770 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:00,142 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:00,264 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:02,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:02,551 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:03,169 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:06,098 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:07,718 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:08,009 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:11,530 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:11,550 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What payment plans or financial aid options are available to students at the institution where students owe the most fees?', 'sql': 'SELECT DISTINCT f.type AS financial_aid_type, f.description AS financial_aid_description, p.type AS payment_plan_type\nFROM student_bills b\nJOIN financial_aid_requests f ON b.bill_id = f.billing_period_id\nJOIN student_payment_wallets p ON b.student_id = p.student_id\nWHERE b.institution_id = (SELECT institution_id FROM student_bills GROUP BY institution_id ORDER BY SUM(bill_id) DESC LIMIT 1)', 'correct': False, 'reasoning': 'The SQL query attempts to find financial aid options and payment plans for students at the institution with the highest total fees owed. However, it incorrectly joins the `financial_aid_requests` table using `bill_id` and `billing_period_id`, which may not be the correct relationship. Additionally, it does not ensure that the financial aid options and payment plans are specifically related to the institution with the highest fees owed, as it does not filter the results based on the total fees owed by students at that institution. The query also does not account for the fact that there may be multiple financial aid options or payment plans available, which could lead to incomplete results.', 'feedback': 'To improve the SQL query, ensure that the join conditions accurately reflect the relationships between the tables. Additionally, consider aggregating the total fees owed per institution more clearly and ensure that the financial aid options and payment plans are specifically filtered for that institution. You might also want to clarify the question to specify whether you are looking for all available options or just those that are currently active or applicable.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-09 05:36:11,551 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:36:11,551 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-09 05:36:14,335 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:14,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:14,855 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the trend in fee debt at the institution where students owe the most fees over the past few years?', 'sql': 'SELECT bp.description AS billing_period, SUM(sb.balance) AS total_debt\nFROM student_balances sb\nJOIN billing_periods bp ON sb.billing_period_id = bp.id\nWHERE bp.start_date >= DATEADD(YEAR, -3, GETDATE())\nGROUP BY bp.description\nORDER BY total_debt DESC;', 'correct': False, 'reasoning': 'The SQL query retrieves the total debt for each billing period over the last three years, which is a good start. However, it does not specify the institution where students owe the most fees, nor does it provide a trend analysis over time. The question asks for a trend, which implies a need for a time series analysis or a comparison of debts over multiple periods, rather than just a sum of debts. Additionally, the query does not filter for the institution with the highest total debt, which is a key part of the question.', 'feedback': 'To improve the SQL query, you should first calculate the total debt for each institution and then filter to find the institution with the highest debt. After identifying that institution, you can then analyze the trend in fee debt over the past few years by grouping the results by billing period. Consider using a subquery or a CTE to first determine the institution with the highest debt before performing the trend analysis.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-09 05:36:14,856 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:36:14,856 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-09 05:36:15,237 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:36:16,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:12,565 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:13,767 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:17,608 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:17,626 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the fee structure at the institution where students owe the most fees differ from other institutions?', 'sql': 'WITH InstitutionFees AS (  SELECT i.id AS institution_id, i.NAME AS institution_name, SUM(b.total_due) AS total_fees  FROM institutions i  JOIN bills b ON i.id = b.institution_id  GROUP BY i.id, i.NAME), MaxInstitution AS (  SELECT institution_id, institution_name, total_fees  FROM InstitutionFees  ORDER BY total_fees DESC  LIMIT 1) SELECT i.institution_name, b.bill_category_id, SUM(bi.amount) AS total_amount, COUNT(bi.id) AS total_items  FROM bills b  JOIN bill_items bi ON b.id = bi.bill_id  JOIN institutions i ON b.institution_id = i.id  WHERE i.id IN (SELECT institution_id FROM MaxInstitution) OR i.id NOT IN (SELECT institution_id FROM MaxInstitution)  GROUP BY i.institution_name, b.bill_category_id;', 'correct': False, 'reasoning': 'The SQL query attempts to compare the fee structure of the institution with the highest fees to other institutions. However, the WHERE clause in the final SELECT statement is not correctly structured to differentiate between the institution with the highest fees and the others. It uses an OR condition that includes all institutions, which does not effectively isolate the comparison. Additionally, the query does not provide a clear comparison of fee structures; it aggregates total amounts and counts of bill items without explicitly contrasting the highest fee institution with others.', 'feedback': 'The question could be clarified by specifying what aspects of the fee structure should be compared (e.g., total fees, types of fees, etc.). The SQL could be improved by restructuring the final SELECT statement to explicitly compare the fee details of the institution with the highest fees against those of other institutions, possibly using a UNION or a more defined JOIN condition.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-09 05:37:17,627 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:37:17,627 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-09 05:37:18,725 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:20,810 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:22,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:22,347 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:24,488 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:25,125 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:26,085 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:26,994 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:27,188 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:29,733 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:31,538 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:33,374 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:34,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:36,146 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:37,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:39,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:40,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:40,772 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:40,780 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What payment plans or financial aid options are available to students at the institution where students owe the most fees?', 'sql': 'WITH InstitutionFees AS (  SELECT sb.institution_id, SUM(sb.balance) AS total_balance  FROM student_balances sb  GROUP BY sb.institution_id), MaxInstitution AS (  SELECT institution_id  FROM InstitutionFees  ORDER BY total_balance DESC  LIMIT 1) SELECT DISTINCT fa.description, fa.type, fa.status  FROM financial_aid_requests fa  JOIN MaxInstitution mi ON fa.institution_id = mi.institution_id;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total balance owed by students by first aggregating the balances in the 'student_balances' table. It then selects the institution with the maximum balance. Finally, it retrieves distinct financial aid options available for that institution from the 'financial_aid_requests' table, which aligns with the question's request for payment plans or financial aid options.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly mentioning 'payment plans' in the query if such data exists in the schema, as the current query only retrieves financial aid options."}
2025-08-09 05:37:40,781 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:37:40,781 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 05:37:42,290 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:42,719 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:43,314 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:44,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:45,432 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:46,284 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:48,331 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:50,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:50,687 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:52,229 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:52,244 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons why this institution may lack financial support programs for students, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons why an institution may lack financial support programs for students and the impact of this on student enrollment and retention. The schema provided does not contain any direct information regarding the reasons for the absence of financial support programs or the qualitative impacts on student enrollment and retention. While there are tables related to financial aid requests and student transactions, they do not provide insights into the underlying reasons for the lack of programs or the broader implications on enrollment and retention.', 'feedback': ''}
2025-08-09 05:37:52,245 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:37:52,245 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:37:52,245 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons why this institution may lack financial support programs for students, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons why an institution may lack financial support programs for students and the impact of this on student enrollment and retention. The schema provided does not contain any direct information regarding the reasons for the absence of financial support programs or the qualitative impacts on student enrollment and retention. While there are tables related to financial aid requests and student transactions, they do not provide insights into the underlying reasons for the lack of programs or the broader implications on enrollment and retention.', 'feedback': ''}
2025-08-09 05:37:54,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:55,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:56,968 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:56,984 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons why this institution may lack financial support programs for students, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons why an institution may lack financial support programs for students and the impact of this on student enrollment and retention. This requires qualitative insights and contextual understanding that cannot be derived from the provided database schema, which primarily contains structured data about institutions, students, programs, and financial transactions. The schema does not include any information about the policies, decision-making processes, or external factors influencing financial support programs, nor does it provide data on student enrollment and retention metrics in relation to financial support.', 'feedback': ''}
2025-08-09 05:37:56,984 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 05:37:56,984 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 05:37:56,984 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons why this institution may lack financial support programs for students, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons why an institution may lack financial support programs for students and the impact of this on student enrollment and retention. This requires qualitative insights and contextual understanding that cannot be derived from the provided database schema, which primarily contains structured data about institutions, students, programs, and financial transactions. The schema does not include any information about the policies, decision-making processes, or external factors influencing financial support programs, nor does it provide data on student enrollment and retention metrics in relation to financial support.', 'feedback': ''}
2025-08-09 05:37:57,609 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 05:37:57,610 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-09 05:37:57,610 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 05:37:57,610 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: <class 'celery.concurrency.solo.TaskPool'> does not implement kill_job
2025-08-09 05:37:57,610 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250809_052720.log
2025-08-09 05:37:57,613 - celery.app.trace - INFO - Task generate_streaming_report[1fb15329-d63c-4fde-b22b-182c707c6b2a] succeeded in 190.7845391249939s: {'error': 'Error generating streaming report: <class \'celery.concurrency.solo.TaskPool\'> does not implement kill_job'}
2025-08-09 05:37:59,068 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 05:37:59,787 - celery.redirected - WARNING - Exception ignored in: 
2025-08-09 05:37:59,788 - celery.redirected - WARNING - <module 'threading' from '/opt/anaconda3/lib/python3.12/threading.py'>
2025-08-09 05:37:59,788 - celery.redirected - WARNING - Traceback (most recent call last):
2025-08-09 05:37:59,788 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/threading.py", line 1594, in _shutdown
2025-08-09 05:37:59,790 - celery.redirected - WARNING -     
2025-08-09 05:37:59,790 - celery.redirected - WARNING - atexit_call()
2025-08-09 05:37:59,790 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/concurrent/futures/thread.py", line 31, in _python_exit
2025-08-09 05:37:59,791 - celery.redirected - WARNING -     
2025-08-09 05:37:59,791 - celery.redirected - WARNING - t.join()
2025-08-09 05:37:59,791 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/threading.py", line 1149, in join
2025-08-09 05:37:59,792 - celery.redirected - WARNING -     
2025-08-09 05:37:59,792 - celery.redirected - WARNING - self._wait_for_tstate_lock()
2025-08-09 05:37:59,792 - celery.redirected - WARNING -   File "/opt/anaconda3/lib/python3.12/threading.py", line 1169, in _wait_for_tstate_lock
2025-08-09 05:37:59,792 - celery.redirected - WARNING -     
2025-08-09 05:37:59,792 - celery.redirected - WARNING - if lock.acquire(block, timeout):
2025-08-09 05:37:59,792 - celery.redirected - WARNING -  
2025-08-09 05:37:59,792 - celery.redirected - WARNING -  
2025-08-09 05:37:59,792 - celery.redirected - WARNING -  
2025-08-09 05:37:59,792 - celery.redirected - WARNING -  
2025-08-09 05:37:59,792 - celery.redirected - WARNING -  
2025-08-09 05:37:59,792 - celery.redirected - WARNING -  
2025-08-09 05:37:59,792 - celery.redirected - WARNING -  
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,793 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,795 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,795 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,795 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,795 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,795 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,795 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,795 - celery.redirected - WARNING - ^
2025-08-09 05:37:59,795 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/apps/worker.py", line 300, in _handle_request
2025-08-09 05:37:59,796 - celery.redirected - WARNING -     
2025-08-09 05:37:59,796 - celery.redirected - WARNING - callback(worker)
2025-08-09 05:37:59,796 - celery.redirected - WARNING -   File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/apps/worker.py", line 329, in on_hard_shutdown
2025-08-09 05:37:59,797 - celery.redirected - WARNING -     
2025-08-09 05:37:59,797 - celery.redirected - WARNING - raise WorkerTerminate(EX_FAILURE)
2025-08-09 05:37:59,797 - celery.redirected - WARNING - celery.exceptions
2025-08-09 05:37:59,797 - celery.redirected - WARNING - .
2025-08-09 05:37:59,797 - celery.redirected - WARNING - WorkerTerminate
2025-08-09 05:37:59,797 - celery.redirected - WARNING - : 
2025-08-09 05:37:59,797 - celery.redirected - WARNING - 1

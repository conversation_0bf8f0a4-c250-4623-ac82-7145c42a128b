2025-08-08 06:01:18,769 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_060118.log
2025-08-08 06:01:18,769 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:01:18,769 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 8f441904-9178-48cb-ad83-4e758092834e
2025-08-08 06:01:18,769 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:01:18,769 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:01:18,769 - REPORT_REQUEST - INFO - 🆔 Task ID: 8f441904-9178-48cb-ad83-4e758092834e
2025-08-08 06:01:18,769 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T06:01:18.769404
2025-08-08 06:01:19,199 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.430s]
2025-08-08 06:01:19,200 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 49
2025-08-08 06:01:19,538 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.338s]
2025-08-08 06:01:19,538 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 2
2025-08-08 06:01:22,626 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:3.088s]
2025-08-08 06:01:22,627 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (4):
2025-08-08 06:01:22,627 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (28 docs)
2025-08-08 06:01:22,627 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (13 docs)
2025-08-08 06:01:22,627 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 06:01:22,627 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing in ITC University?' (1 docs)
2025-08-08 06:01:22,627 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 06:01:34,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:34,145 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 06:01:38,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:38,676 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 06:01:41,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:41,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:41,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:41,647 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:41,847 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:42,002 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:42,308 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:42,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:42,881 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:43,970 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:44,040 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 06:01:44,041 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:01:44,041 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 06:01:44,041 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:01:44,041 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 3
2025-08-08 06:01:46,218 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:47,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:47,220 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:50,169 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:50,451 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:51,047 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:51,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:52,046 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:53,005 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:57,500 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:58,114 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:01:58,934 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:04,404 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:04,421 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic performance metrics of girls compare to those of boys at ITC University?', 'sql': "SELECT sex, AVG(assessment_total) AS average_assessment_total, AVG(exam_total) AS average_exam_total, AVG(finalscore) AS average_finalscore, COUNT(*) AS total_students\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN student_programs sp ON s.id = sp.student_id\nWHERE sp.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY sex;", 'correct': True, 'reasoning': 'The SQL query correctly retrieves the average academic performance metrics (assessment_total, exam_total, finalscore) for students grouped by sex (boys and girls) at ITC University. It joins the necessary tables (assessment_results, students, and student_programs) to ensure that only students from the specified institution are included. The use of AVG() functions provides the average scores, and COUNT(*) gives the total number of students in each sex category, which directly addresses the question of comparing academic performance metrics between girls and boys.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly including the institution_id in the SELECT clause to clarify that the metrics are specifically for ITC University. Additionally, consider adding a filter for the current semester if the analysis is intended to be time-specific.'}
2025-08-08 06:02:04,421 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:04,421 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:02:05,180 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:05,184 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any trends in the performance of girls at ITC University over the past few years?', 'sql': "SELECT s.sex, ar.semester_id, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS number_of_results\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.assessment_results ar ON s.id = ar.student_id\nJOIN core.semesters sem ON ar.semester_id = sem.id\nWHERE s.sex = 'F' AND sp.institution_id = 'ITC University' AND sem.status = 'Active'\nGROUP BY s.sex, ar.semester_id\nORDER BY ar.semester_id;", 'correct': True, 'reasoning': "The SQL query correctly identifies female students at ITC University and aggregates their performance data over different semesters. It calculates the average final score and counts the number of results for each semester, which allows for the analysis of trends in performance over time. The use of 'GROUP BY' and 'ORDER BY' ensures that the results are organized by semester, which is essential for identifying trends.", 'feedback': 'The question could be clarified by specifying what kind of trends are of interest (e.g., increasing or decreasing scores, comparison with boys, etc.). Additionally, the SQL could be improved by including a time frame in the WHERE clause to focus on the past few years, such as filtering semesters based on their start dates.'}
2025-08-08 06:02:05,185 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:05,185 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:02:05,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:05,637 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in the most?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(ar.finalscore) AS average_score\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.assessment_results ar ON sp.id = ar.student_program_id\nJOIN core.courses c ON ar.course_id = c.id\nWHERE s.sex = 'F' AND s.status = 'active'\nGROUP BY p.long_name, c.title\nORDER BY average_score DESC;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the programs and courses that female students (girls) at ITC University are excelling in by calculating the average score of assessment results for active female students. It joins the necessary tables to gather information about students, their programs, and their assessment results, and it groups the results by program and course to find the average scores, which is what the question asks for.', 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be clarified that 'excelling' is interpreted as having a higher average score. If there are specific thresholds for 'excelling' that should be considered, such as a minimum average score, that could be added to the WHERE clause for more precision."}
2025-08-08 06:02:05,638 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:05,638 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 06:02:10,200 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:10,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:11,119 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:11,938 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:12,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:12,898 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:13,529 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:14,104 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:15,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:15,624 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:22,075 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:22,129 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:23,704 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:27,771 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:27,785 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of girls in these specific programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of girls in certain programs at ITC University. However, the provided schema does not contain any direct information about performance factors, gender-specific performance metrics, or qualitative data that would allow for an analysis of the reasons behind performance differences. The schema includes various tables related to students, programs, and academic records, but it lacks the necessary data to answer the question regarding the contributing factors to performance.', 'feedback': ''}
2025-08-08 06:02:27,785 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:27,785 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:27,785 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of girls in these specific programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of girls in certain programs at ITC University. However, the provided schema does not contain any direct information about performance factors, gender-specific performance metrics, or qualitative data that would allow for an analysis of the reasons behind performance differences. The schema includes various tables related to students, programs, and academic records, but it lacks the necessary data to answer the question regarding the contributing factors to performance.', 'feedback': ''}
2025-08-08 06:02:27,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:27,814 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the performance of girls, and how might this impact future trends?', 'answerable': False, 'reasoning': 'The question asks for specific steps that ITC University could take to improve data collection and analysis regarding the performance of girls, as well as the potential impact on future trends. This requires qualitative insights and strategic recommendations that are not directly derivable from the provided database schema. The schema contains tables related to student data, academic performance, and institutional information, but it does not provide the necessary context or strategic frameworks to formulate actionable steps or analyze trends based on qualitative factors.', 'feedback': ''}
2025-08-08 06:02:27,814 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:27,814 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:27,814 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the performance of girls, and how might this impact future trends?', 'answerable': False, 'reasoning': 'The question asks for specific steps that ITC University could take to improve data collection and analysis regarding the performance of girls, as well as the potential impact on future trends. This requires qualitative insights and strategic recommendations that are not directly derivable from the provided database schema. The schema contains tables related to student data, academic performance, and institutional information, but it does not provide the necessary context or strategic frameworks to formulate actionable steps or analyze trends based on qualitative factors.', 'feedback': ''}
2025-08-08 06:02:28,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:28,600 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing academic performance would typically require qualitative data, statistical analysis, or specific studies, none of which are represented in the schema.', 'feedback': ''}
2025-08-08 06:02:28,601 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:28,601 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:28,601 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing academic performance would typically require qualitative data, statistical analysis, or specific studies, none of which are represented in the schema.', 'feedback': ''}
2025-08-08 06:02:30,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:31,292 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:31,959 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:33,543 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:33,559 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of girls in these specific programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of girls in certain programs at ITC University. However, the provided schema does not contain any direct information about performance factors, gender-specific performance metrics, or any qualitative data that could explain the reasons behind performance differences. The schema includes various tables related to students, programs, and academic records, but it lacks the necessary data to analyze or identify the factors influencing performance, especially in a gender-specific context.', 'feedback': ''}
2025-08-08 06:02:33,559 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:33,559 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:33,559 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of girls in these specific programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of girls in certain programs at ITC University. However, the provided schema does not contain any direct information about performance factors, gender-specific performance metrics, or any qualitative data that could explain the reasons behind performance differences. The schema includes various tables related to students, programs, and academic records, but it lacks the necessary data to analyze or identify the factors influencing performance, especially in a gender-specific context.', 'feedback': ''}
2025-08-08 06:02:34,197 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:34,219 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the performance of girls, and how might this impact future trends?', 'answerable': False, 'reasoning': 'The question asks for specific steps that ITC University could take to improve data collection and analysis regarding the performance of girls, as well as the potential impact on future trends. This requires qualitative insights and strategic recommendations that are not directly derivable from the provided database schema. The schema contains tables related to student data, academic performance, and institutional information, but it does not provide the necessary context or strategic frameworks to formulate actionable steps or analyze future trends based on qualitative assessments.', 'feedback': ''}
2025-08-08 06:02:34,219 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:34,219 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:34,219 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the performance of girls, and how might this impact future trends?', 'answerable': False, 'reasoning': 'The question asks for specific steps that ITC University could take to improve data collection and analysis regarding the performance of girls, as well as the potential impact on future trends. This requires qualitative insights and strategic recommendations that are not directly derivable from the provided database schema. The schema contains tables related to student data, academic performance, and institutional information, but it does not provide the necessary context or strategic frameworks to formulate actionable steps or analyze future trends based on qualitative assessments.', 'feedback': ''}
2025-08-08 06:02:35,592 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:35,601 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing academic performance would typically require qualitative data, statistical analysis, or specific studies, none of which are represented in the schema.', 'feedback': ''}
2025-08-08 06:02:35,602 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:35,602 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:35,602 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing academic performance would typically require qualitative data, statistical analysis, or specific studies, none of which are represented in the schema.', 'feedback': ''}
2025-08-08 06:02:36,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:37,319 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:38,501 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:39,259 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:39,274 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of girls in these specific programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of girls in certain programs at ITC University. However, the provided schema does not contain any direct information about performance factors, gender-specific performance metrics, or any qualitative data that could explain the reasons behind performance differences. The schema primarily consists of tables related to institutions, students, programs, and various administrative aspects, but lacks the necessary data to analyze or infer the factors affecting performance based on gender.', 'feedback': ''}
2025-08-08 06:02:39,275 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:39,275 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:39,275 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of girls in these specific programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of girls in certain programs at ITC University. However, the provided schema does not contain any direct information about performance factors, gender-specific performance metrics, or any qualitative data that could explain the reasons behind performance differences. The schema primarily consists of tables related to institutions, students, programs, and various administrative aspects, but lacks the necessary data to analyze or infer the factors affecting performance based on gender.', 'feedback': ''}
2025-08-08 06:02:40,149 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:40,169 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the performance of girls, and how might this impact future trends?', 'answerable': False, 'reasoning': 'The question asks for specific steps that ITC University could take to improve data collection and analysis regarding the performance of girls, as well as the potential impact on future trends. This requires qualitative insights and strategic recommendations that are not directly derivable from the provided database schema. The schema contains tables related to students, programs, assessments, and other institutional data, but it does not provide information on policies, strategies, or qualitative assessments that would inform such recommendations. Therefore, the question cannot be answered based on the schema alone.', 'feedback': ''}
2025-08-08 06:02:40,169 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:40,170 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:40,170 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the performance of girls, and how might this impact future trends?', 'answerable': False, 'reasoning': 'The question asks for specific steps that ITC University could take to improve data collection and analysis regarding the performance of girls, as well as the potential impact on future trends. This requires qualitative insights and strategic recommendations that are not directly derivable from the provided database schema. The schema contains tables related to students, programs, assessments, and other institutional data, but it does not provide information on policies, strategies, or qualitative assessments that would inform such recommendations. Therefore, the question cannot be answered based on the schema alone.', 'feedback': ''}
2025-08-08 06:02:41,429 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:41,445 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain any specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing academic performance would typically require qualitative data, statistical analysis, or specific studies, none of which are represented in the schema.', 'feedback': ''}
2025-08-08 06:02:41,445 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:41,445 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:41,445 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain any specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing academic performance would typically require qualitative data, statistical analysis, or specific studies, none of which are represented in the schema.', 'feedback': ''}
2025-08-08 06:02:42,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:43,043 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:44,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:44,911 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:44,925 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of girls in these specific programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of girls in certain programs at ITC University. However, the provided schema does not contain any direct information about performance factors, gender-specific performance metrics, or any qualitative data that could explain the reasons behind performance differences. The schema primarily consists of tables related to institutions, students, programs, and various administrative aspects, but lacks the necessary analytical or qualitative data to answer the question.', 'feedback': ''}
2025-08-08 06:02:44,926 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:44,926 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:44,926 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high performance of girls in these specific programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for specific factors contributing to the high performance of girls in certain programs at ITC University. However, the provided schema does not contain any direct information about performance factors, gender-specific performance metrics, or any qualitative data that could explain the reasons behind performance differences. The schema primarily consists of tables related to institutions, students, programs, and various administrative aspects, but lacks the necessary analytical or qualitative data to answer the question.', 'feedback': ''}
2025-08-08 06:02:45,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:45,809 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the performance of girls, and how might this impact future trends?', 'answerable': False, 'reasoning': 'The question asks for specific steps that ITC University could take to improve data collection and analysis regarding the performance of girls, as well as the potential impact on future trends. This requires qualitative insights and strategic recommendations that are not directly derivable from the provided database schema. The schema contains tables related to students, programs, assessments, and other institutional data, but it does not provide information on strategic initiatives, policies, or qualitative analysis that would inform such recommendations. Therefore, the question cannot be answered based on the schema alone.', 'feedback': ''}
2025-08-08 06:02:45,809 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:45,809 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:45,809 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to improve data collection and analysis regarding the performance of girls, and how might this impact future trends?', 'answerable': False, 'reasoning': 'The question asks for specific steps that ITC University could take to improve data collection and analysis regarding the performance of girls, as well as the potential impact on future trends. This requires qualitative insights and strategic recommendations that are not directly derivable from the provided database schema. The schema contains tables related to students, programs, assessments, and other institutional data, but it does not provide information on strategic initiatives, policies, or qualitative analysis that would inform such recommendations. Therefore, the question cannot be answered based on the schema alone.', 'feedback': ''}
2025-08-08 06:02:47,671 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:47,690 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing academic performance would typically require qualitative data, statistical analysis, or specific studies, none of which are represented in the schema.', 'feedback': ''}
2025-08-08 06:02:47,690 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 06:02:47,690 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 06:02:47,690 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls compared to boys at ITC University?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons. While there are tables related to students, courses, and assessments, there is no direct information or analysis available in the schema that would allow for a comprehensive answer to this question. Factors influencing academic performance would typically require qualitative data, statistical analysis, or specific studies, none of which are represented in the schema.', 'feedback': ''}
2025-08-08 06:02:47,691 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 06:02:47,691 - root - INFO - [{'sex': 'F', 'average_assessment_total': 36.93, 'average_exam_total': 36.18, 'average_finalscore': 69.0, 'total_students': 65}, {'sex': 'M', 'average_assessment_total': 10.92, 'average_exam_total': 13.44, 'average_finalscore': 54.86, 'total_students': 71}]
2025-08-08 06:02:47,691 - root - INFO - [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 86.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 84.26}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Media And Technologies For Teaching And Learning', 'average_score': 78.83}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'On-Campus Teaching Practice', 'average_score': 75.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Project Work', 'average_score': 74.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 72.0}, {'program_name': 'Master Of Arts In Educational Leadership', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Guidance and Counselling in Schools', 'average_score': 70.0}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'course_title': 'Academic Writing', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Methods in Specified Subjects', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Historical, Philosophical and Sociological Foundations of Education', 'average_score': 67.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'INTRODUCTION TO HOME ECONOMICS', 'average_score': 66.45}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'General Principles And Methods Of Instruction', 'average_score': 66.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'School Organisation and Management', 'average_score': 65.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Software', 'average_score': 61.2}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 59.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 54.2}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Off-Campus Teaching Practice', 'average_score': 0.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'INTRODUCTION TO HOME ECONOMICS', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Emerging Technologies And Internet Application', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'PERSONAL GROWTH AND COMMUNICATION SKILLS', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'INTRODUCTION TO PHYSICAL CHEMISTRY', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Applied Science In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'INTRODUCTION TO CLOTHING AND TEXTILES', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Applied Mathematics In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Applied Science In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Applied Mathematics In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}]
2025-08-08 06:02:47,691 - root - INFO - 'No results'
2025-08-08 06:02:47,691 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 2
2025-08-08 06:02:47,691 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 1
2025-08-08 06:02:47,691 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 06:02:57,609 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:02:57,614 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 06:03:12,149 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:03:12,155 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:03:12,156 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 06:03:12,156 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:03:12,156 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/3
2025-08-08 06:03:12,156 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:03:12,156 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:03:12,156 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:03:12,156 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 06:03:12,156 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:03:12,156 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:03:12,156 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:03:12,156 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:03:12,156 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the academic performance metrics of girls compare to those of boys at ITC University?...
2025-08-08 06:03:12,156 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several k...
2025-08-08 06:03:12,156 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: graph
2025-08-08 06:03:12,156 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': 'F', 'average_assessment_total': 36.93, 'average_exam_total': 36.18, 'average_finalscore': ...
2025-08-08 06:03:12,156 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: academic_performance_comparison_by_sex
2025-08-08 06:03:12,157 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 06:03:12,157 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 06:03:12,157 - celery.redirected - WARNING - [{'sex': 'F', 'average_assessment_total': 36.93, 'average_exam_total': 36.18, 'average_finalscore': 69.0, 'total_students': 65}, {'sex': 'M', 'average_assessment_total': 10.92, 'average_exam_total': 13.44, 'average_finalscore': 54.86, 'total_students': 71}]
2025-08-08 06:03:12,157 - celery.redirected - WARNING - ================================= 
2025-08-08 06:03:12,157 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: academic_performance_comparison_by_sex
2025-08-08 06:03:12,157 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 06:03:12,157 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:03:12,157 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/3
2025-08-08 06:03:12,157 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:03:12,157 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:03:12,157 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:03:12,157 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 06:03:12,158 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:03:12,158 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:03:12,158 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:03:12,158 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:03:12,158 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are girls at ITC University excelling in the most?...
2025-08-08 06:03:12,158 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Girls at ITC University are excelling in several programs and courses. The highest average score is ...
2025-08-08 06:03:12,158 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-08 06:03:12,158 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Re...
2025-08-08 06:03:12,158 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_academic_performance_by_program_and_course
2025-08-08 06:03:12,158 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-08 06:03:12,158 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-08 06:03:12,158 - celery.redirected - WARNING - [{'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 86.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 84.26}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Media And Technologies For Teaching And Learning', 'average_score': 78.83}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'On-Campus Teaching Practice', 'average_score': 75.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Project Work', 'average_score': 74.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 72.0}, {'program_name': 'Master Of Arts In Educational Leadership', 'course_title': 'Fundamentals of Psychology of Education and the Education of Individuals with Special Needs', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Guidance and Counselling in Schools', 'average_score': 70.0}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'course_title': 'Academic Writing', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Instructional Methods in Specified Subjects', 'average_score': 70.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Historical, Philosophical and Sociological Foundations of Education', 'average_score': 67.0}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'INTRODUCTION TO HOME ECONOMICS', 'average_score': 66.45}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'General Principles And Methods Of Instruction', 'average_score': 66.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'School Organisation and Management', 'average_score': 65.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Software', 'average_score': 61.2}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Curriculum Studies', 'average_score': 60.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Educational Research Methods, Assessment and Statistics', 'average_score': 59.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Information And Communication Technology', 'average_score': 54.2}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Off-Campus Teaching Practice', 'average_score': 0.0}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'INTRODUCTION TO HOME ECONOMICS', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'Emerging Technologies And Internet Application', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'PERSONAL GROWTH AND COMMUNICATION SKILLS', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'course_title': 'INTRODUCTION TO PHYSICAL CHEMISTRY', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Applied Science In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'INTRODUCTION TO CLOTHING AND TEXTILES', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Applied Mathematics In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Applied Science In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Applied Mathematics In Clothing And Textiles', 'average_score': None}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'course_title': 'Introduction To Educational Technology', 'average_score': None}]
2025-08-08 06:03:12,158 - celery.redirected - WARNING - ================================= 
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: girls_academic_performance_by_program_and_course
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-08 06:03:12,159 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:03:12,159 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/3
2025-08-08 06:03:12,159 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:03:12,159 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 06:03:12,159 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 06:03:12,159 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any trends in the performance of girls at ITC University over the past few years?...
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there is no available data on the performance of ...
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_performance_trends_itc_university
2025-08-08 06:03:12,159 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 06:03:12,160 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 06:03:12,160 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 06:03:12,160 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 3
2025-08-08 06:03:12,160 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:03:12,160 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 06:03:12,160 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 3 documents
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO -     Content: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:02:47.690763+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: Girls ...
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:02:44.926501+00:00', 'data_returned': True, 'data_tag': 'girls_academic_performance_by_program_and_course'}
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO -     Content: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T06:02:45.809838+00:00', 'data_returned': False}
2025-08-08 06:03:12,160 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 2/3
2025-08-08 06:03:12,326 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.165s]
2025-08-08 06:03:16,648 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 06:03:26,955 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:N/A duration:10.002s]
2025-08-08 06:03:26,956 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://*************:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-08 06:03:26,956 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: Connection timed out
2025-08-08 06:03:26,957 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:03:26,957 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION ERROR
2025-08-08 06:03:26,957 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:03:26,957 - REPORT_PIPELINE_ERROR - ERROR - ❌ Report generation failed: Error generating report: Connection timed out
2025-08-08 06:03:26,957 - REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_060118.log
2025-08-08 06:03:26,960 - celery.app.trace - INFO - Task generate_report[87ce5689-5478-4d91-9800-09803c1291ab] succeeded in 128.193953916s: {'error': 'Error generating report: Connection timed out'}
2025-08-08 06:30:43,995 - celery.worker.strategy - INFO - Task generate_report[f46826a8-cd9e-417f-aa56-deabecb66ba0] received
2025-08-08 06:30:43,998 - app.tasks.report_task - INFO - starting...
2025-08-08 06:30:43,999 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:30:43,999 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 1c1efd0a-4e79-47eb-8553-d259e33a735f
2025-08-08 06:30:43,999 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:30:43,999 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-08 06:30:43,999 - REPORT_REQUEST - INFO - 🆔 Task ID: 1c1efd0a-4e79-47eb-8553-d259e33a735f
2025-08-08 06:30:43,999 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T06:30:43.999287
2025-08-08 06:30:44,575 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:404 duration:0.575s]
2025-08-08 06:30:44,575 - ELASTICSEARCH_STATE - ERROR - ❌ Error checking Elasticsearch state: NotFoundError(404, 'index_not_found_exception', 'no such index [deep_research]', deep_research, index_or_alias)
2025-08-08 06:30:44,576 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 06:30:53,855 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:30:53,884 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 06:30:58,844 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:30:58,870 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 06:31:04,038 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:31:04,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:31:04,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:31:04,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:31:04,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:31:04,703 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:31:04,768 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:31:04,845 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:31:05,040 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:31:05,149 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 06:31:05,156 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 06:31:05,156 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION ERROR
2025-08-08 06:31:05,156 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 06:31:05,156 - REPORT_PIPELINE_ERROR - ERROR - ❌ Report generation failed: Error generating report: <class 'celery.concurrency.solo.TaskPool'> does not implement kill_job
2025-08-08 06:31:05,156 - REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_060118.log
2025-08-08 06:31:05,158 - celery.app.trace - INFO - Task generate_report[f46826a8-cd9e-417f-aa56-deabecb66ba0] succeeded in 21.160692875000223s: {'error': 'Error generating report: <class \'celery.concurrency.solo.TaskPool\'> does not implement kill_job'}

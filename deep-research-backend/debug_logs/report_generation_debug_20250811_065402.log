2025-08-11 06:54:02,287 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250811_065402.log
2025-08-11 06:54:02,287 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:54:02,287 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: f6b6ee6e-c4d5-46e8-9446-a7c5e8114d3c
2025-08-11 06:54:02,287 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:54:02,287 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are students performing at ITC University?'
2025-08-11 06:54:02,287 - REPORT_REQUEST - INFO - 🆔 Task ID: f6b6ee6e-c4d5-46e8-9446-a7c5e8114d3c
2025-08-11 06:54:02,287 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-11T06:54:02.287521
2025-08-11 06:54:02,417 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 06:54:02,417 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 114
2025-08-11 06:54:02,544 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-11 06:54:02,545 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 64
2025-08-11 06:54:02,710 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.165s]
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (24 docs)
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (13 docs)
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (11 docs)
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO -   - 'Which instituion has the most students?' (6 docs)
2025-08-11 06:54:02,710 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC university?' (3 docs)
2025-08-11 06:54:02,710 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:54:02,710 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-11 06:54:02,710 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:54:02,710 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How are students performing at ITC University?
2025-08-11 06:54:02,710 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-11 06:54:02,710 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-11 06:54:15,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:15,775 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-11 06:54:20,037 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:20,061 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-11 06:54:22,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:22,521 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:22,698 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:22,753 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:22,946 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:23,053 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:23,095 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:23,154 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:23,215 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:24,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:24,139 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-11 06:54:26,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:26,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:27,351 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:27,553 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:29,097 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:29,555 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:30,013 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:30,067 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:30,541 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:31,571 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:31,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:31,976 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:37,140 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:38,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:39,670 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:39,938 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:43,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:43,419 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the average grades or GPAs of students at ITC University across different programs?', 'sql': "SELECT p.long_name AS program_name, AVG(sg.gpa) AS average_gpa\nFROM student_semester_gpas sg\nJOIN students s ON sg.student_id = s.id\nJOIN programs p ON sg.student_program_id = p.id\nWHERE s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY p.long_name\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average GPAs of students enrolled in different programs at ITC University. It joins the 'student_semester_gpas' table with the 'students' and 'programs' tables to get the necessary information. The WHERE clause filters students by the institution ID corresponding to 'ITC University', and the GROUP BY clause groups the results by program name, allowing for the calculation of the average GPA for each program. The LIMIT clause is not necessary for answering the question but does not affect the correctness of the query.", 'feedback': "The question could specify whether there is a need for a limit on the number of programs returned, as the LIMIT clause in the SQL may imply a restriction that isn't necessary for the average GPA calculation. Additionally, if there are many programs, it might be useful to clarify if the user wants all programs or just the top N based on average GPA."}
2025-08-11 06:54:43,420 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:54:43,420 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 06:54:46,196 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:46,200 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What specific metrics are being used to evaluate student performance at ITC University?', 'sql': "SELECT DISTINCT grade, assessment_total, exam_total, finalscore, gp FROM core.assessment_results WHERE institution_id = (SELECT id FROM core.institutions WHERE name = 'ITC University') LIMIT 20;", 'correct': True, 'reasoning': "The SQL query retrieves distinct metrics related to student performance from the 'assessment_results' table, specifically focusing on 'grade', 'assessment_total', 'exam_total', 'finalscore', and 'gp'. These metrics are relevant to evaluating student performance. The query also filters results to only include those from 'ITC University' by matching the institution_id with the id of the institution named 'ITC University'. The use of 'LIMIT 20' is not necessary for answering the question but does not affect the correctness of the query.", 'feedback': "The question could be clarified by specifying whether the user is interested in all metrics or just a subset. Additionally, removing the 'LIMIT 20' clause could provide a complete view of all metrics available for evaluation."}
2025-08-11 06:54:46,200 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:54:46,200 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 06:54:47,340 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:47,357 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there particular courses or programs at ITC University where students are excelling or struggling?', 'sql': "SELECT c.title AS course_title, p.long_name AS program_name, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS total_students, COUNT(CASE WHEN ar.grade IN ('A', 'B', 'B+') THEN 1 END) AS excelling_students, COUNT(CASE WHEN ar.grade IN ('D', 'D+', 'E', 'F') THEN 1 END) AS struggling_students\nFROM core.assessment_results ar\nJOIN core.courses c ON ar.course_id = c.id\nJOIN core.programs p ON ar.student_program_id = p.id\nWHERE c.institution_id = 'ITC University'\nGROUP BY c.title, p.long_name\nORDER BY average_score DESC\nLIMIT 20;", 'correct': True, 'reasoning': 'The SQL query accurately retrieves data about courses and programs at ITC University by joining the assessment results with courses and programs. It calculates the average score, counts total students, and distinguishes between excelling and struggling students based on their grades. The use of GROUP BY allows for aggregation by course and program, which directly addresses the question about student performance in specific courses and programs.', 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by including a more explicit definition of what constitutes 'excelling' and 'struggling' in the context of the university's grading system, as this may vary by institution. Additionally, consider removing the LIMIT clause if the goal is to get a comprehensive view of all courses and programs rather than just the top 20."}
2025-08-11 06:54:47,358 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:54:47,358 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 06:54:47,512 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:47,517 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does student performance at ITC University vary by demographic factors such as age, gender, or nationality?', 'sql': "SELECT\n    FLOOR(DATEDIFF(CURDATE(), s.dob) / 365) AS age,\n    s.sex,\n    n.name AS nationality,\n    COUNT(sp.id) AS student_count,\n    AVG(sp.student_program_status) AS average_performance\nFROM\n    core.students s\nJOIN\n    core.student_info si ON s.email = si.email\nJOIN\n    core.student_programs sp ON s.id = sp.student_id\nJOIN\n    core.nationalities n ON s.nationality_id = n.id\nWHERE\n    s.institution_id = 'ITC University'\nGROUP BY\n    age, s.sex, n.name\nORDER BY\n    age, s.sex, n.name\nLIMIT 20;", 'correct': True, 'reasoning': 'The SQL query effectively addresses the question by calculating the average performance of students at ITC University based on demographic factors such as age (derived from the date of birth), gender (sex), and nationality. It uses appropriate joins to gather data from the relevant tables and groups the results by age, sex, and nationality, which aligns with the request to analyze performance variations by these demographics. The inclusion of COUNT(sp.id) provides additional context on the number of students in each demographic group, which is relevant to understanding performance variations.', 'feedback': "The query is well-structured and answers the question accurately. However, it could be improved by ensuring that the 'student_program_status' field used for average performance is indeed a numeric representation of performance. If 'student_program_status' is not a numeric field, consider using a different metric that accurately reflects student performance."}
2025-08-11 06:54:47,517 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:54:47,517 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 06:54:48,444 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:49,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:49,681 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:50,673 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:50,716 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:51,523 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:51,555 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:52,279 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:52,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:52,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:53,607 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:53,780 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:54,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:55,127 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:55,952 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:56,207 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:56,638 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:56,655 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the absence of data on average grades or GPAs at ITC University, and how could this impact the university's reputation or student recruitment?", 'answerable': False, 'reasoning': "The question is asking for an analysis of factors contributing to the absence of data on average grades or GPAs at ITC University, as well as the potential impact on the university's reputation or student recruitment. This requires qualitative insights and contextual understanding that cannot be derived from the provided database schema, which contains structured data about various entities but does not include any information about the reasons for data absence or its implications on reputation and recruitment.", 'feedback': ''}
2025-08-11 06:54:56,656 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:54:56,656 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:54:56,656 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the absence of data on average grades or GPAs at ITC University, and how could this impact the university's reputation or student recruitment?", 'answerable': False, 'reasoning': "The question is asking for an analysis of factors contributing to the absence of data on average grades or GPAs at ITC University, as well as the potential impact on the university's reputation or student recruitment. This requires qualitative insights and contextual understanding that cannot be derived from the provided database schema, which contains structured data about various entities but does not include any information about the reasons for data absence or its implications on reputation and recruitment.", 'feedback': ''}
2025-08-11 06:54:57,722 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:57,744 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of data on student performance in specific courses or programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of data on student performance in specific courses or programs at ITC University. However, the provided schema does not contain any direct information or attributes that would allow for an analysis of factors affecting data availability or quality. The schema primarily consists of tables related to student information, courses, assessments, and institutional data, but it lacks any explicit references to data quality issues or factors influencing data completeness. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 06:54:57,744 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:54:57,744 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:54:57,744 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of data on student performance in specific courses or programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of data on student performance in specific courses or programs at ITC University. However, the provided schema does not contain any direct information or attributes that would allow for an analysis of factors affecting data availability or quality. The schema primarily consists of tables related to student information, courses, assessments, and institutional data, but it lacks any explicit references to data quality issues or factors influencing data completeness. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 06:54:58,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:58,424 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "How do these metrics align with the university's overall educational goals and objectives for student learning and development?", 'answerable': False, 'reasoning': "The question asks about the alignment of metrics with the university's educational goals and objectives for student learning and development. However, the provided database schema does not contain any tables or fields that explicitly define or relate to the university's educational goals, objectives, or metrics for student learning and development. The schema primarily consists of data related to institutions, students, courses, assessments, and other operational aspects, but lacks qualitative or strategic information regarding educational goals.", 'feedback': ''}
2025-08-11 06:54:58,425 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:54:58,425 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:54:58,425 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "How do these metrics align with the university's overall educational goals and objectives for student learning and development?", 'answerable': False, 'reasoning': "The question asks about the alignment of metrics with the university's educational goals and objectives for student learning and development. However, the provided database schema does not contain any tables or fields that explicitly define or relate to the university's educational goals, objectives, or metrics for student learning and development. The schema primarily consists of data related to institutions, students, courses, assessments, and other operational aspects, but lacks qualitative or strategic information regarding educational goals.", 'feedback': ''}
2025-08-11 06:54:58,754 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:54:58,772 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to begin collecting and analyzing demographic data related to student performance?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to collect and analyze demographic data related to student performance. While the schema contains various tables related to students, their performance, and demographic information (such as `students`, `student_programs`, and `student_status_types`), it does not provide explicit guidance or steps for implementing a data collection and analysis strategy. The schema is primarily a representation of the data structure and does not include procedural or strategic information necessary to answer the question.', 'feedback': ''}
2025-08-11 06:54:58,773 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:54:58,773 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:54:58,773 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to begin collecting and analyzing demographic data related to student performance?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to collect and analyze demographic data related to student performance. While the schema contains various tables related to students, their performance, and demographic information (such as `students`, `student_programs`, and `student_status_types`), it does not provide explicit guidance or steps for implementing a data collection and analysis strategy. The schema is primarily a representation of the data structure and does not include procedural or strategic information necessary to answer the question.', 'feedback': ''}
2025-08-11 06:54:59,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:01,101 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:01,254 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:01,399 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:01,419 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the absence of data on average grades or GPAs at ITC University, and how could this impact the university's reputation or student recruitment?", 'answerable': False, 'reasoning': "The question is asking for an analysis of factors contributing to the absence of data on average grades or GPAs at ITC University, as well as the potential impact on the university's reputation or student recruitment. This requires qualitative insights and contextual understanding that cannot be derived from the provided database schema, which contains structured data about various entities but does not include any information about perceptions, reputations, or qualitative factors influencing data absence.", 'feedback': ''}
2025-08-11 06:55:01,419 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:01,419 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:01,419 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the absence of data on average grades or GPAs at ITC University, and how could this impact the university's reputation or student recruitment?", 'answerable': False, 'reasoning': "The question is asking for an analysis of factors contributing to the absence of data on average grades or GPAs at ITC University, as well as the potential impact on the university's reputation or student recruitment. This requires qualitative insights and contextual understanding that cannot be derived from the provided database schema, which contains structured data about various entities but does not include any information about perceptions, reputations, or qualitative factors influencing data absence.", 'feedback': ''}
2025-08-11 06:55:01,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:04,810 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:04,811 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:04,825 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "How do these metrics align with the university's overall educational goals and objectives for student learning and development?", 'answerable': False, 'reasoning': "The question asks about the alignment of metrics with the university's educational goals and objectives for student learning and development. However, the provided database schema does not contain any tables or fields that explicitly define educational goals, objectives, or metrics related to student learning and development. The schema primarily consists of tables related to institutional data, student information, courses, assessments, and financial transactions, but lacks qualitative data or strategic frameworks that would allow for an analysis of alignment with educational goals.", 'feedback': ''}
2025-08-11 06:55:04,829 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:04,829 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:04,829 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "How do these metrics align with the university's overall educational goals and objectives for student learning and development?", 'answerable': False, 'reasoning': "The question asks about the alignment of metrics with the university's educational goals and objectives for student learning and development. However, the provided database schema does not contain any tables or fields that explicitly define educational goals, objectives, or metrics related to student learning and development. The schema primarily consists of tables related to institutional data, student information, courses, assessments, and financial transactions, but lacks qualitative data or strategic frameworks that would allow for an analysis of alignment with educational goals.", 'feedback': ''}
2025-08-11 06:55:05,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:05,899 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of data on student performance in specific courses or programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of data on student performance in specific courses or programs at ITC University. However, the provided schema does not contain any direct information or attributes that would allow for an analysis of factors affecting data availability or quality. The schema primarily consists of tables related to student information, courses, assessments, and institutional data, but it lacks any explicit references to data quality issues, data collection processes, or specific factors that could lead to a lack of data. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 06:55:05,899 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:05,900 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:05,900 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of data on student performance in specific courses or programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of data on student performance in specific courses or programs at ITC University. However, the provided schema does not contain any direct information or attributes that would allow for an analysis of factors affecting data availability or quality. The schema primarily consists of tables related to student information, courses, assessments, and institutional data, but it lacks any explicit references to data quality issues, data collection processes, or specific factors that could lead to a lack of data. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 06:55:05,910 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:05,947 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to begin collecting and analyzing demographic data related to student performance?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to collect and analyze demographic data related to student performance. This is a strategic and procedural question that requires insights into data collection methods, analysis techniques, and possibly institutional policies or practices. The provided schema contains tables related to student information, performance metrics, and demographic details, but it does not provide explicit guidance or steps on how to implement data collection or analysis processes. Therefore, while the schema may contain relevant data, it does not directly answer the question regarding the steps to be taken.', 'feedback': ''}
2025-08-11 06:55:05,947 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:05,947 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:05,948 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to begin collecting and analyzing demographic data related to student performance?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to collect and analyze demographic data related to student performance. This is a strategic and procedural question that requires insights into data collection methods, analysis techniques, and possibly institutional policies or practices. The provided schema contains tables related to student information, performance metrics, and demographic details, but it does not provide explicit guidance or steps on how to implement data collection or analysis processes. Therefore, while the schema may contain relevant data, it does not directly answer the question regarding the steps to be taken.', 'feedback': ''}
2025-08-11 06:55:09,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:09,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:09,219 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the absence of data on average grades or GPAs at ITC University, and how could this impact the university's reputation or student recruitment?", 'answerable': False, 'reasoning': "The question is asking for an analysis of factors contributing to the absence of data on average grades or GPAs at ITC University, as well as the potential impact on the university's reputation or student recruitment. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about students, courses, and academic records. The schema does not provide information on the reasons for data absence or the implications of such absence on reputation or recruitment.", 'feedback': ''}
2025-08-11 06:55:09,220 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:09,220 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:09,220 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the absence of data on average grades or GPAs at ITC University, and how could this impact the university's reputation or student recruitment?", 'answerable': False, 'reasoning': "The question is asking for an analysis of factors contributing to the absence of data on average grades or GPAs at ITC University, as well as the potential impact on the university's reputation or student recruitment. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about students, courses, and academic records. The schema does not provide information on the reasons for data absence or the implications of such absence on reputation or recruitment.", 'feedback': ''}
2025-08-11 06:55:11,888 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:12,312 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:14,573 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:14,589 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "How do these metrics align with the university's overall educational goals and objectives for student learning and development?", 'answerable': False, 'reasoning': "The question asks about the alignment of metrics with the university's educational goals and objectives for student learning and development. However, the provided database schema does not contain any tables or fields that explicitly define educational goals, objectives, or metrics related to student learning and development. The schema primarily consists of tables related to institutional data, student information, courses, assessments, and financial transactions, but lacks qualitative data or strategic frameworks that would allow for an analysis of alignment with educational goals.", 'feedback': ''}
2025-08-11 06:55:14,589 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:14,589 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:14,589 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "How do these metrics align with the university's overall educational goals and objectives for student learning and development?", 'answerable': False, 'reasoning': "The question asks about the alignment of metrics with the university's educational goals and objectives for student learning and development. However, the provided database schema does not contain any tables or fields that explicitly define educational goals, objectives, or metrics related to student learning and development. The schema primarily consists of tables related to institutional data, student information, courses, assessments, and financial transactions, but lacks qualitative data or strategic frameworks that would allow for an analysis of alignment with educational goals.", 'feedback': ''}
2025-08-11 06:55:15,145 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:18,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:18,120 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of data on student performance in specific courses or programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of data on student performance in specific courses or programs at ITC University. However, the provided schema does not contain any direct information or attributes that would allow for an analysis of factors affecting data availability or performance. The schema includes various tables related to students, courses, and assessments, but it does not provide insights into the reasons for data absence or performance issues. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-11 06:55:18,121 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:18,121 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:18,121 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of data on student performance in specific courses or programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of data on student performance in specific courses or programs at ITC University. However, the provided schema does not contain any direct information or attributes that would allow for an analysis of factors affecting data availability or performance. The schema includes various tables related to students, courses, and assessments, but it does not provide insights into the reasons for data absence or performance issues. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-11 06:55:19,453 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:19,473 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to begin collecting and analyzing demographic data related to student performance?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to collect and analyze demographic data related to student performance. This is a strategic and procedural question that requires insights into data collection methods, analysis techniques, and possibly institutional policies or practices. The provided schema contains tables related to student information, performance metrics, and demographics, but it does not provide explicit guidance or steps on how to implement data collection or analysis processes. Therefore, while the schema may contain relevant data, it does not directly answer the question regarding the steps to be taken.', 'feedback': ''}
2025-08-11 06:55:19,473 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:19,473 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:19,473 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to begin collecting and analyzing demographic data related to student performance?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to collect and analyze demographic data related to student performance. This is a strategic and procedural question that requires insights into data collection methods, analysis techniques, and possibly institutional policies or practices. The provided schema contains tables related to student information, performance metrics, and demographics, but it does not provide explicit guidance or steps on how to implement data collection or analysis processes. Therefore, while the schema may contain relevant data, it does not directly answer the question regarding the steps to be taken.', 'feedback': ''}
2025-08-11 06:55:22,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:23,026 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:23,042 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the absence of data on average grades or GPAs at ITC University, and how could this impact the university's reputation or student recruitment?", 'answerable': False, 'reasoning': "The question is asking for an analysis of factors contributing to the absence of data on average grades or GPAs at ITC University, as well as the potential impact on the university's reputation or student recruitment. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about students, courses, and academic records. The schema does not provide information on the reasons for data absence or the implications of such absence on reputation or recruitment.", 'feedback': ''}
2025-08-11 06:55:23,042 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:23,042 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:23,042 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the absence of data on average grades or GPAs at ITC University, and how could this impact the university's reputation or student recruitment?", 'answerable': False, 'reasoning': "The question is asking for an analysis of factors contributing to the absence of data on average grades or GPAs at ITC University, as well as the potential impact on the university's reputation or student recruitment. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about students, courses, and academic records. The schema does not provide information on the reasons for data absence or the implications of such absence on reputation or recruitment.", 'feedback': ''}
2025-08-11 06:55:26,705 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:27,087 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:27,439 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:27,455 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "How do these metrics align with the university's overall educational goals and objectives for student learning and development?", 'answerable': False, 'reasoning': "The question asks about the alignment of metrics with the university's educational goals and objectives for student learning and development. However, the provided database schema does not contain any tables or fields that explicitly define educational goals, objectives, or metrics related to student learning and development. The schema primarily consists of data related to institutions, students, courses, and various administrative aspects, but lacks qualitative or strategic information that would be necessary to answer the question.", 'feedback': ''}
2025-08-11 06:55:27,455 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:27,455 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:27,455 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "How do these metrics align with the university's overall educational goals and objectives for student learning and development?", 'answerable': False, 'reasoning': "The question asks about the alignment of metrics with the university's educational goals and objectives for student learning and development. However, the provided database schema does not contain any tables or fields that explicitly define educational goals, objectives, or metrics related to student learning and development. The schema primarily consists of data related to institutions, students, courses, and various administrative aspects, but lacks qualitative or strategic information that would be necessary to answer the question.", 'feedback': ''}
2025-08-11 06:55:29,008 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:29,024 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of data on student performance in specific courses or programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of data on student performance in specific courses or programs at ITC University. However, the provided schema does not contain any direct information or attributes that would allow for an analysis of factors affecting data availability or quality. The schema primarily consists of tables related to student information, courses, assessments, and institutional data, but it lacks any explicit references to data quality issues or factors influencing data completeness. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 06:55:29,024 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:29,024 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:29,024 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of data on student performance in specific courses or programs at ITC University?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of data on student performance in specific courses or programs at ITC University. However, the provided schema does not contain any direct information or attributes that would allow for an analysis of factors affecting data availability or quality. The schema primarily consists of tables related to student information, courses, assessments, and institutional data, but it lacks any explicit references to data quality issues or factors influencing data completeness. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 06:55:29,852 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:29,873 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could ITC University take to begin collecting and analyzing demographic data related to student performance?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to collect and analyze demographic data related to student performance. This is a strategic and procedural question that requires insights into data collection methods, analysis techniques, and possibly institutional policies or practices. The provided schema contains tables related to student information, performance metrics, and demographic details, but it does not provide explicit guidance or steps on how to implement data collection or analysis processes. Therefore, while the schema may contain relevant data, it does not directly answer the question regarding the steps to be taken.', 'feedback': ''}
2025-08-11 06:55:29,873 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 06:55:29,874 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 06:55:29,874 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could ITC University take to begin collecting and analyzing demographic data related to student performance?', 'answerable': False, 'reasoning': 'The question asks for steps that ITC University could take to collect and analyze demographic data related to student performance. This is a strategic and procedural question that requires insights into data collection methods, analysis techniques, and possibly institutional policies or practices. The provided schema contains tables related to student information, performance metrics, and demographic details, but it does not provide explicit guidance or steps on how to implement data collection or analysis processes. Therefore, while the schema may contain relevant data, it does not directly answer the question regarding the steps to be taken.', 'feedback': ''}
2025-08-11 06:55:29,874 - root - INFO - [{'grade': 'C+', 'assessment_total': None, 'exam_total': None, 'finalscore': 66.0, 'gp': 2.5}, {'grade': 'B', 'assessment_total': 60.0, 'exam_total': 10.0, 'finalscore': 70.0, 'gp': 3.0}, {'grade': 'C', 'assessment_total': None, 'exam_total': None, 'finalscore': 60.0, 'gp': 2.0}, {'grade': 'D+', 'assessment_total': None, 'exam_total': None, 'finalscore': 59.0, 'gp': 1.5}, {'grade': 'C+', 'assessment_total': None, 'exam_total': None, 'finalscore': 67.0, 'gp': 2.5}, {'grade': 'B', 'assessment_total': None, 'exam_total': None, 'finalscore': 72.0, 'gp': 3.0}, {'grade': 'B', 'assessment_total': None, 'exam_total': None, 'finalscore': 70.0, 'gp': 3.0}, {'grade': 'C+', 'assessment_total': None, 'exam_total': None, 'finalscore': 65.0, 'gp': 2.5}, {'grade': 'IC', 'assessment_total': None, 'exam_total': None, 'finalscore': 0.0, 'gp': 0.0}, {'grade': 'B+', 'assessment_total': None, 'exam_total': None, 'finalscore': 75.0, 'gp': 3.5}, {'grade': 'B', 'assessment_total': None, 'exam_total': None, 'finalscore': 74.0, 'gp': 3.0}, {'grade': 'A', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 82.0, 'gp': 4.0}, {'grade': 'B', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 72.0, 'gp': 3.0}, {'grade': 'C', 'assessment_total': 24.4, 'exam_total': 36.6, 'finalscore': 61.0, 'gp': 2.5}, {'grade': 'C', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 60.0, 'gp': 2.0}, {'grade': 'C', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 63.0, 'gp': 2.0}, {'grade': 'C+', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 67.0, 'gp': 2.5}, {'grade': 'D', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 51.0, 'gp': 1.0}, {'grade': 'D', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 50.0, 'gp': 1.0}, {'grade': 'D', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 53.0, 'gp': 1.0}]
2025-08-11 06:55:29,874 - root - INFO - 'No results'
2025-08-11 06:55:29,875 - root - INFO - 'No results'
2025-08-11 06:55:29,875 - root - INFO - 'No results'
2025-08-11 06:55:29,875 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-11 06:55:38,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:38,712 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-11 06:55:47,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:47,670 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:55:47,671 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 06:55:47,671 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:55:47,671 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are students performing at ITC University?'
2025-08-11 06:55:47,671 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 06:55:47,671 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 06:55:47,671 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 06:55:47,671 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 06:55:47,671 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What specific metrics are being used to evaluate student performance at ITC University?...
2025-08-11 06:55:47,671 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, student performance is evaluated using several metrics, including the final score...
2025-08-11 06:55:47,671 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-11 06:55:47,671 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'grade': 'C+', 'assessment_total': None, 'exam_total': None, 'finalscore': 66.0, 'gp': 2.5}, {'gra...
2025-08-11 06:55:47,671 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_performance_metrics
2025-08-11 06:55:47,671 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-11 06:55:47,671 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-11 06:55:47,672 - celery.redirected - WARNING - [{'grade': 'C+', 'assessment_total': None, 'exam_total': None, 'finalscore': 66.0, 'gp': 2.5}, {'grade': 'B', 'assessment_total': 60.0, 'exam_total': 10.0, 'finalscore': 70.0, 'gp': 3.0}, {'grade': 'C', 'assessment_total': None, 'exam_total': None, 'finalscore': 60.0, 'gp': 2.0}, {'grade': 'D+', 'assessment_total': None, 'exam_total': None, 'finalscore': 59.0, 'gp': 1.5}, {'grade': 'C+', 'assessment_total': None, 'exam_total': None, 'finalscore': 67.0, 'gp': 2.5}, {'grade': 'B', 'assessment_total': None, 'exam_total': None, 'finalscore': 72.0, 'gp': 3.0}, {'grade': 'B', 'assessment_total': None, 'exam_total': None, 'finalscore': 70.0, 'gp': 3.0}, {'grade': 'C+', 'assessment_total': None, 'exam_total': None, 'finalscore': 65.0, 'gp': 2.5}, {'grade': 'IC', 'assessment_total': None, 'exam_total': None, 'finalscore': 0.0, 'gp': 0.0}, {'grade': 'B+', 'assessment_total': None, 'exam_total': None, 'finalscore': 75.0, 'gp': 3.5}, {'grade': 'B', 'assessment_total': None, 'exam_total': None, 'finalscore': 74.0, 'gp': 3.0}, {'grade': 'A', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 82.0, 'gp': 4.0}, {'grade': 'B', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 72.0, 'gp': 3.0}, {'grade': 'C', 'assessment_total': 24.4, 'exam_total': 36.6, 'finalscore': 61.0, 'gp': 2.5}, {'grade': 'C', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 60.0, 'gp': 2.0}, {'grade': 'C', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 63.0, 'gp': 2.0}, {'grade': 'C+', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 67.0, 'gp': 2.5}, {'grade': 'D', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 51.0, 'gp': 1.0}, {'grade': 'D', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 50.0, 'gp': 1.0}, {'grade': 'D', 'assessment_total': 0.0, 'exam_total': 0.0, 'finalscore': 53.0, 'gp': 1.0}]
2025-08-11 06:55:47,672 - celery.redirected - WARNING - ================================= 
2025-08-11 06:55:47,672 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-11 06:55:47,672 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:55:47,672 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 06:55:47,672 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:55:47,672 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are students performing at ITC University?'
2025-08-11 06:55:47,672 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 06:55:47,672 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 06:55:47,672 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 06:55:47,672 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 06:55:47,672 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What are the average grades or GPAs of students at ITC University across different programs?...
2025-08-11 06:55:47,672 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no available records of average grades or GPAs for students at ITC Univers...
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_gpa_by_program
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-11 06:55:47,673 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:55:47,673 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 06:55:47,673 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are students performing at ITC University?'
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 06:55:47,673 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there particular courses or programs at ITC University where students are excelling or strugglin...
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there is currently no available data on student p...
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_performance_data
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-11 06:55:47,673 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-11 06:55:47,674 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:55:47,674 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 06:55:47,674 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are students performing at ITC University?'
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 06:55:47,674 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does student performance at ITC University vary by demographic factors such as age, gender, or n...
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, indicating that there may be no available data on student ...
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_performance_demographics
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-11 06:55:47,674 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-11 06:55:47,675 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 4
2025-08-11 06:55:47,675 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:55:47,675 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-11 06:55:47,675 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:55:47,675 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 4 documents
2025-08-11 06:55:47,675 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-11 06:55:47,675 - UPSERT_DOCS - INFO -     Content: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:55:47,675 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:55:47,675 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-11 06:55:47,675 - UPSERT_DOCS - INFO -     Content: Question: What are the average grades or GPAs of students at ITC University across different program...
2025-08-11 06:55:47,675 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:23.042778+00:00', 'data_returned': False}
2025-08-11 06:55:47,675 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-11 06:55:47,676 - UPSERT_DOCS - INFO -     Content: Question: Are there particular courses or programs at ITC University where students are excelling or...
2025-08-11 06:55:47,676 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:29.025013+00:00', 'data_returned': False}
2025-08-11 06:55:47,676 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-11 06:55:47,676 - UPSERT_DOCS - INFO -     Content: Question: How does student performance at ITC University vary by demographic factors such as age, ge...
2025-08-11 06:55:47,676 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:29.874190+00:00', 'data_returned': False}
2025-08-11 06:55:47,676 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 1/4
2025-08-11 06:55:47,802 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.126s]
2025-08-11 06:55:51,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 06:55:52,142 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.534s]
2025-08-11 06:55:52,143 - UPSERT_DOCS - INFO - ✅ Successfully upserted 4 documents to Elasticsearch
2025-08-11 06:55:52,143 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 6
2025-08-11 06:55:52,144 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-11 06:55:52,144 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:55:52,144 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-11 06:55:52,144 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:55:52,144 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/6...
2025-08-11 06:55:52,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:52,772 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:55:52,772 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 06:55:52,772 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:55:52,772 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student performance evaluation ITC University'
2025-08-11 06:55:52,772 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are students performing at ITC University?'
2025-08-11 06:55:52,772 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are students performing at ITC University?'
2025-08-11 06:55:52,903 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 06:55:52,903 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 118
2025-08-11 06:55:53,032 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-11 06:55:53,032 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 65
2025-08-11 06:55:53,161 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 06:55:53,162 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-11 06:55:53,292 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 06:55:53,292 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-11 06:55:54,008 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 06:55:54,144 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.132s]
2025-08-11 06:55:54,145 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-11 06:55:54,145 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:55:54,145 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:55:54,146 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:55:54,146 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:55:54,146 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (804 chars):
2025-08-11 06:55:54,146 - app.chains.section_writer - INFO -    Question: What specific metrics are being used to evaluate student performance at ITC University?
Answer: At ITC University, student performance is evaluated using several metrics, including the final score, which is a numerical representation of a student's overall performance in a course. Additionally, grades are assigned based on these final scores, with corresponding grade points (GP) that reflect the quality of performance. Some courses also include assessments and exams, which contribute t...
2025-08-11 06:55:58,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:58,764 - app.chains.section_writer - INFO - 🤖 AI generated section (988 chars):
2025-08-11 06:55:58,764 - app.chains.section_writer - INFO -    The purpose of this report is to evaluate student performance at ITC University by analyzing various academic metrics and factors influencing success. The key finding indicates that while many students perform well, there are significant areas for improvement that can enhance overall academic outcom...
2025-08-11 06:55:58,764 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-11 06:55:58,764 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 988 characters
2025-08-11 06:55:58,765 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/6...
2025-08-11 06:55:59,539 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:55:59,543 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:55:59,543 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 06:55:59,543 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:55:59,543 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Student performance evaluation metrics'
2025-08-11 06:55:59,544 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are students performing at ITC University?'
2025-08-11 06:55:59,544 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are students performing at ITC University?'
2025-08-11 06:55:59,670 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-11 06:55:59,671 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 118
2025-08-11 06:55:59,800 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 06:55:59,800 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 65
2025-08-11 06:55:59,930 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 06:55:59,930 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-11 06:56:00,061 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 06:56:00,061 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-11 06:56:00,611 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 06:56:00,753 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.138s]
2025-08-11 06:56:00,754 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-11 06:56:00,754 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:56:00,754 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:56:00,755 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:56:00,755 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:56:00,755 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (804 chars):
2025-08-11 06:56:00,755 - app.chains.section_writer - INFO -    Question: What specific metrics are being used to evaluate student performance at ITC University?
Answer: At ITC University, student performance is evaluated using several metrics, including the final score, which is a numerical representation of a student's overall performance in a course. Additionally, grades are assigned based on these final scores, with corresponding grade points (GP) that reflect the quality of performance. Some courses also include assessments and exams, which contribute t...
2025-08-11 06:56:06,460 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:56:06,473 - app.chains.section_writer - INFO - 🤖 AI generated section (869 chars):
2025-08-11 06:56:06,473 - app.chains.section_writer - INFO -    ## Evaluation Metrics for Student Performance  

At ITC University, student performance is evaluated using several key metrics. The final score serves as a numerical representation of a student's overall performance in a course, highlighting its significance in the evaluation process.

The grading s...
2025-08-11 06:56:06,473 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-11 06:56:06,473 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 869 characters
2025-08-11 06:56:06,474 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/6...
2025-08-11 06:56:07,399 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:56:07,408 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:56:07,408 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 06:56:07,408 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:56:07,409 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'factors affecting student performance'
2025-08-11 06:56:07,409 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are students performing at ITC University?'
2025-08-11 06:56:07,409 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are students performing at ITC University?'
2025-08-11 06:56:07,538 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 06:56:07,539 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 118
2025-08-11 06:56:07,669 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 06:56:07,669 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 65
2025-08-11 06:56:07,798 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-11 06:56:07,798 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-11 06:56:07,928 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 06:56:07,929 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-11 06:56:08,359 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 06:56:08,501 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.139s]
2025-08-11 06:56:08,502 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-11 06:56:08,502 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:56:08,502 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:56:08,503 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:56:08,503 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:56:08,503 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (804 chars):
2025-08-11 06:56:08,503 - app.chains.section_writer - INFO -    Question: What specific metrics are being used to evaluate student performance at ITC University?
Answer: At ITC University, student performance is evaluated using several metrics, including the final score, which is a numerical representation of a student's overall performance in a course. Additionally, grades are assigned based on these final scores, with corresponding grade points (GP) that reflect the quality of performance. Some courses also include assessments and exams, which contribute t...
2025-08-11 06:56:15,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:56:15,212 - app.chains.section_writer - INFO - 🤖 AI generated section (1768 chars):
2025-08-11 06:56:15,212 - app.chains.section_writer - INFO -    ## Factors Influencing Student Performance  

### Socioeconomic Background  
Socioeconomic status plays a significant role in shaping student performance. Students from higher socioeconomic backgrounds often have access to better educational resources, including tutoring and extracurricular activiti...
2025-08-11 06:56:15,212 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-11 06:56:15,213 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 1768 characters
2025-08-11 06:56:15,213 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/6...
2025-08-11 06:56:15,880 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:56:15,885 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:56:15,885 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 06:56:15,885 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:56:15,885 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student performance metrics trends'
2025-08-11 06:56:15,885 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are students performing at ITC University?'
2025-08-11 06:56:15,885 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are students performing at ITC University?'
2025-08-11 06:56:16,015 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 06:56:16,015 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 118
2025-08-11 06:56:16,145 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 06:56:16,146 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 65
2025-08-11 06:56:16,276 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 06:56:16,276 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-11 06:56:16,406 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 06:56:16,407 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-11 06:56:16,854 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 06:56:16,990 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.132s]
2025-08-11 06:56:16,991 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-11 06:56:16,991 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:56:16,991 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:56:16,992 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:56:16,992 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:56:16,992 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (804 chars):
2025-08-11 06:56:16,992 - app.chains.section_writer - INFO -    Question: What specific metrics are being used to evaluate student performance at ITC University?
Answer: At ITC University, student performance is evaluated using several metrics, including the final score, which is a numerical representation of a student's overall performance in a course. Additionally, grades are assigned based on these final scores, with corresponding grade points (GP) that reflect the quality of performance. Some courses also include assessments and exams, which contribute t...
2025-08-11 06:56:22,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:56:22,681 - app.chains.section_writer - INFO - 🤖 AI generated section (994 chars):
2025-08-11 06:56:22,681 - app.chains.section_writer - INFO -    ## Summary of Findings  

At ITC University, student performance is evaluated through various metrics, primarily focusing on final scores that represent a student's overall performance in a course. These final scores are translated into letter grades ranging from 'A' to 'D', with 'IC' indicating an ...
2025-08-11 06:56:22,681 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-11 06:56:22,681 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 994 characters
2025-08-11 06:56:22,682 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/6...
2025-08-11 06:56:24,023 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:56:24,043 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:56:24,043 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 06:56:24,043 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:56:24,043 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'academic support faculty-student interaction at-risk students programs'
2025-08-11 06:56:24,043 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are students performing at ITC University?'
2025-08-11 06:56:24,043 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are students performing at ITC University?'
2025-08-11 06:56:24,176 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-11 06:56:24,177 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 118
2025-08-11 06:56:24,304 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-11 06:56:24,305 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 65
2025-08-11 06:56:24,434 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 06:56:24,435 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-11 06:56:24,563 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-11 06:56:24,564 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-11 06:56:24,947 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 06:56:25,089 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.138s]
2025-08-11 06:56:25,090 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-11 06:56:25,090 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:56:25,090 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:56:25,091 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:56:25,091 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:56:25,091 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (804 chars):
2025-08-11 06:56:25,092 - app.chains.section_writer - INFO -    Question: What specific metrics are being used to evaluate student performance at ITC University?
Answer: At ITC University, student performance is evaluated using several metrics, including the final score, which is a numerical representation of a student's overall performance in a course. Additionally, grades are assigned based on these final scores, with corresponding grade points (GP) that reflect the quality of performance. Some courses also include assessments and exams, which contribute t...
2025-08-11 06:56:30,690 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:56:30,702 - app.chains.section_writer - INFO - 🤖 AI generated section (1219 chars):
2025-08-11 06:56:30,702 - app.chains.section_writer - INFO -    ## Recommendations  

### Enhancing Academic Support Services  
To improve student performance, it is essential to enhance academic support services. This can be achieved by providing additional tutoring resources, study groups, and workshops that focus on key areas where students typically struggle...
2025-08-11 06:56:30,702 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-11 06:56:30,702 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 1219 characters
2025-08-11 06:56:30,703 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 6/6...
2025-08-11 06:56:31,616 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 06:56:31,624 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 06:56:31,624 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 06:56:31,624 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 06:56:31,624 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student performance evaluation ITC University conclusion'
2025-08-11 06:56:31,624 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are students performing at ITC University?'
2025-08-11 06:56:31,624 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are students performing at ITC University?'
2025-08-11 06:56:31,754 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 06:56:31,755 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 118
2025-08-11 06:56:31,883 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-11 06:56:31,883 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 65
2025-08-11 06:56:32,014 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 06:56:32,014 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 4
2025-08-11 06:56:32,144 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 06:56:32,144 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 1
2025-08-11 06:56:33,538 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 06:56:33,678 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.136s]
2025-08-11 06:56:33,678 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 1 documents
2025-08-11 06:56:33,678 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:56:33,678 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:56:33,679 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What specific metrics are being used to evaluate student performance at ITC University?
An...
2025-08-11 06:56:33,680 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'How are students performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T06:55:27.455954+00:00', 'data_returned': True}
2025-08-11 06:56:33,680 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (804 chars):
2025-08-11 06:56:33,680 - app.chains.section_writer - INFO -    Question: What specific metrics are being used to evaluate student performance at ITC University?
Answer: At ITC University, student performance is evaluated using several metrics, including the final score, which is a numerical representation of a student's overall performance in a course. Additionally, grades are assigned based on these final scores, with corresponding grade points (GP) that reflect the quality of performance. Some courses also include assessments and exams, which contribute t...
2025-08-11 07:02:21,477 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:21,488 - app.chains.section_writer - INFO - 🤖 AI generated section (893 chars):
2025-08-11 07:02:21,488 - app.chains.section_writer - INFO -    In conclusion, the evaluation of student performance at ITC University relies on a structured set of metrics, primarily focusing on final scores that translate into grades and grade points. This system allows for a clear representation of student achievement, with grades ranging from 'A' to 'D', and...
2025-08-11 07:02:21,488 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-11 07:02:21,489 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 6 completed and processed: 893 characters
2025-08-11 07:02:21,489 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:02:21,489 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-11 07:02:21,489 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:02:21,489 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-11 07:02:21,489 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 6
2025-08-11 07:02:21,489 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 4
2025-08-11 07:02:21,490 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 0
2025-08-11 07:02:21,490 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250811_065402.log
2025-08-11 07:02:21,493 - celery.app.trace - INFO - Task generate_streaming_report[91b4cfc3-bdfc-4ae3-9fd0-c5b0e513765c] succeeded in 243.32198550000004s: {'outline': '# Report on Student Performance at ITC University

## Introduction  
The purpose of this report is to evaluate student performance at ITC University by analyzing various academic metrics and factors influencing success. The key finding indicates that while many students perform well, there are significant areas for improvement that can enhance overall academic outcomes.

## Evaluation Metrics for Student Performance  
- **Final Score**  
  - Definition and significance  
  - Numerical representation of overall performance  
- **Grading System**  
  - Description of grades (A to D, IC for incomplete)  
  - Corresponding Grade Points (GP)  
    - Example:  
      - Final score of 82.0 = \'A\' grade, 4.0 GP  
      - Final score of 51.0 = \'D\' grade, 1.0 GP  
- **Assessments and Exams**  
  - Role in overall evaluation  
  - Limitations in data availability (noted as None)  

## Factors Influencing Student Performance  
- **Socioeconomic Background**  
- **Study Habits and Time Management**  
-...', ...}
2025-08-11 07:02:21,495 - celery.worker.strategy - INFO - Task generate_streaming_report[490550bf-8fd3-4c96-b150-fde35fb92248] received
2025-08-11 07:02:21,495 - app.tasks.report_task - INFO - starting streaming report generation...
2025-08-11 07:02:21,496 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:02:21,496 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 1d21b01b-24a4-4443-a7bf-186d3d9fe240
2025-08-11 07:02:21,496 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:02:21,496 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing at ITC University?'
2025-08-11 07:02:21,496 - REPORT_REQUEST - INFO - 🆔 Task ID: 1d21b01b-24a4-4443-a7bf-186d3d9fe240
2025-08-11 07:02:21,496 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-11T07:02:21.496240
2025-08-11 07:02:21,622 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-11 07:02:21,622 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 118
2025-08-11 07:02:21,748 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-11 07:02:21,749 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 65
2025-08-11 07:02:21,880 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.132s]
2025-08-11 07:02:21,881 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-11 07:02:21,881 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (24 docs)
2025-08-11 07:02:21,881 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-11 07:02:21,881 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (13 docs)
2025-08-11 07:02:21,881 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-11 07:02:21,881 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-11 07:02:21,881 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (11 docs)
2025-08-11 07:02:21,882 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-11 07:02:21,882 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-11 07:02:21,882 - ELASTICSEARCH_STATE - INFO -   - 'Which instituion has the most students?' (6 docs)
2025-08-11 07:02:21,882 - ELASTICSEARCH_STATE - INFO -   - 'How are students performing at ITC University?' (4 docs)
2025-08-11 07:02:21,882 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:02:21,882 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-11 07:02:21,882 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:02:21,882 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: How are girls performing at ITC University?
2025-08-11 07:02:21,882 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-11 07:02:21,882 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-11 07:02:32,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:32,518 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-11 07:02:35,294 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:35,309 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-11 07:02:39,143 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:44,515 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:45,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:45,157 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:45,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:45,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:45,940 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:46,045 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:46,066 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:46,737 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:46,805 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-11 07:02:54,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:54,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:02:55,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:02,319 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:03,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:03,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:03,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:05,774 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:10,235 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:12,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:13,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:16,721 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:16,732 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are girls at ITC University excelling in the most?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, AVG(ar.finalscore) AS average_score\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.programs p ON sp.program_id = p.id\nJOIN core.assessment_results ar ON sp.id = ar.student_program_id\nJOIN core.courses c ON ar.course_id = c.id\nWHERE s.sex = 'F' AND s.institution_id = 'ITC University'\nGROUP BY p.long_name, c.title\nORDER BY average_score DESC\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses that female students at ITC University are excelling in by calculating the average score of assessment results for each program and course combination. It filters the students by sex and institution, groups the results by program and course, and orders them by average score in descending order, which aligns with the question's request for identifying areas of excellence.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be clarified whether 'excelling' refers to a specific threshold of average scores or simply the highest averages. If a threshold is intended, the query should include a HAVING clause to filter results accordingly."}
2025-08-11 07:03:16,732 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:03:16,732 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 07:03:19,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:19,130 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic performance metrics of girls compare to those of boys at ITC University?', 'sql': "SELECT sex, AVG(assessment_total) AS average_assessment_total, AVG(exam_total) AS average_exam_total, AVG(finalscore) AS average_finalscore, COUNT(*) AS total_students\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nWHERE s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY sex\nLIMIT 20;", 'correct': True, 'reasoning': 'The SQL query correctly retrieves the average academic performance metrics (assessment_total, exam_total, finalscore) for students grouped by sex (boys and girls) at ITC University. It uses a JOIN between the assessment_results and students tables to link student performance with their demographic information. The WHERE clause filters results to only include students from ITC University, and the GROUP BY clause aggregates the data by sex, which is necessary to compare the performance metrics between girls and boys. The LIMIT clause is unnecessary in this context, but it does not affect the correctness of the query.', 'feedback': 'The question could be clarified by specifying which academic performance metrics are of interest, but the SQL already covers the main metrics. The LIMIT clause could be removed since it is not needed for this comparison.'}
2025-08-11 07:03:19,131 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:03:19,131 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 07:03:20,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:22,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:24,119 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:25,229 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:25,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:25,837 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:27,193 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:27,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:27,541 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any trends in the performance of girls at ITC University over the past few years?', 'sql': "SELECT s.sex, sem.start_year, AVG(ar.finalscore) AS average_score, COUNT(ar.id) AS assessment_count\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN semesters sem ON ar.semester_id = sem.id\nWHERE s.sex = 'F' AND sem.status = 'Ended'\nGROUP BY s.sex, sem.start_year\nORDER BY sem.start_year DESC\nLIMIT 20;", 'correct': True, 'reasoning': "The SQL query correctly identifies the performance trends of female students ('F') at ITC University by calculating the average final score and the count of assessments for each year. It joins the necessary tables (assessment_results, students, and semesters) to gather the required data, filters for ended semesters, and groups the results by year. The ordering by start year in descending order allows for an analysis of trends over the years, which aligns with the question's intent.", 'feedback': 'The query is well-structured for the question asked. However, it could be improved by removing the LIMIT clause if the intention is to analyze all available years rather than just the latest 20. Additionally, specifying the institution_id in the WHERE clause could ensure that the results are limited to ITC University specifically, if that is a requirement.'}
2025-08-11 07:03:27,542 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:03:27,542 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-11 07:03:29,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:30,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:30,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:32,197 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:32,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:35,566 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:38,715 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:39,366 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:39,382 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify contributing factors to the lack of data on girls' performance. Therefore, the question cannot be answered based on the schema.", 'feedback': ''}
2025-08-11 07:03:39,382 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:03:39,383 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:03:39,383 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify contributing factors to the lack of data on girls' performance. Therefore, the question cannot be answered based on the schema.", 'feedback': ''}
2025-08-11 07:03:42,207 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:42,219 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question seeks to understand the factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons, such as grades, assessments, or qualitative factors influencing performance. While there are tables related to students, courses, and assessments, there is no direct way to analyze or derive insights about gender differences in academic performance from the schema alone.', 'feedback': ''}
2025-08-11 07:03:42,219 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:03:42,219 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:03:42,219 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question seeks to understand the factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons, such as grades, assessments, or qualitative factors influencing performance. While there are tables related to students, courses, and assessments, there is no direct way to analyze or derive insights about gender differences in academic performance from the schema alone.', 'feedback': ''}
2025-08-11 07:03:45,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:45,184 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could be taken to improve data collection and analysis regarding the performance of girls at ITC University?', 'answerable': False, 'reasoning': "The question is about improving data collection and analysis regarding the performance of girls at ITC University. However, the provided schema does not contain specific information about gender-based performance metrics or any direct indicators related to the performance of girls. While there are tables related to students, courses, and assessments, there is no clear structure or data that directly addresses the question of how to improve data collection and analysis specifically for girls' performance. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-11 07:03:45,184 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:03:45,185 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:03:45,185 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could be taken to improve data collection and analysis regarding the performance of girls at ITC University?', 'answerable': False, 'reasoning': "The question is about improving data collection and analysis regarding the performance of girls at ITC University. However, the provided schema does not contain specific information about gender-based performance metrics or any direct indicators related to the performance of girls. While there are tables related to students, courses, and assessments, there is no clear structure or data that directly addresses the question of how to improve data collection and analysis specifically for girls' performance. Therefore, the question cannot be answered based on the current schema.", 'feedback': ''}
2025-08-11 07:03:45,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:47,769 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:51,382 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:51,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:51,634 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify contributing factors to the lack of data on girls' performance. Therefore, the question cannot be answered based on the schema.", 'feedback': ''}
2025-08-11 07:03:51,635 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:03:51,635 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:03:51,635 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify contributing factors to the lack of data on girls' performance. Therefore, the question cannot be answered based on the schema.", 'feedback': ''}
2025-08-11 07:03:53,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:53,531 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question seeks to understand the factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons, such as grades, assessments, or qualitative factors influencing performance. While there are tables related to students, courses, and assessments, there is no direct way to analyze or derive insights about gender differences in academic performance from the schema alone.', 'feedback': ''}
2025-08-11 07:03:53,531 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:03:53,531 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:03:53,531 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question seeks to understand the factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons, such as grades, assessments, or qualitative factors influencing performance. While there are tables related to students, courses, and assessments, there is no direct way to analyze or derive insights about gender differences in academic performance from the schema alone.', 'feedback': ''}
2025-08-11 07:03:56,271 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:56,310 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could be taken to improve data collection and analysis regarding the performance of girls at ITC University?', 'answerable': False, 'reasoning': 'The question is about improving data collection and analysis regarding the performance of girls at ITC University. However, the provided schema does not contain specific information about gender-based performance metrics or any direct indicators related to the performance of girls. While there are tables related to students, courses, and assessments, there is no clear structure in the schema that would allow for a comprehensive analysis of gender-specific performance. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 07:03:56,310 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:03:56,310 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:03:56,310 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could be taken to improve data collection and analysis regarding the performance of girls at ITC University?', 'answerable': False, 'reasoning': 'The question is about improving data collection and analysis regarding the performance of girls at ITC University. However, the provided schema does not contain specific information about gender-based performance metrics or any direct indicators related to the performance of girls. While there are tables related to students, courses, and assessments, there is no clear structure in the schema that would allow for a comprehensive analysis of gender-specific performance. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 07:03:56,680 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:03:57,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:00,904 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:00,920 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify contributing factors to the lack of data. Therefore, the question cannot be answered based on the schema.", 'feedback': ''}
2025-08-11 07:04:00,920 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:04:00,920 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:04:00,921 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify contributing factors to the lack of data. Therefore, the question cannot be answered based on the schema.", 'feedback': ''}
2025-08-11 07:04:01,766 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:01,769 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:01,780 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons, such as grades, assessments, or demographic studies that would allow for such an analysis. The schema includes various tables related to students, courses, and assessments, but it lacks the necessary data to directly answer the question about the comparative performance of genders.', 'feedback': ''}
2025-08-11 07:04:01,781 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:04:01,781 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:04:01,781 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons, such as grades, assessments, or demographic studies that would allow for such an analysis. The schema includes various tables related to students, courses, and assessments, but it lacks the necessary data to directly answer the question about the comparative performance of genders.', 'feedback': ''}
2025-08-11 07:04:05,309 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:05,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:06,045 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:06,065 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could be taken to improve data collection and analysis regarding the performance of girls at ITC University?', 'answerable': False, 'reasoning': 'The question is about improving data collection and analysis regarding the performance of girls at ITC University. However, the provided schema does not contain specific information about gender-based performance metrics or any direct indicators related to the performance of girls specifically. While there are tables related to students, courses, and assessments, there is no clear structure or data that directly addresses the question of how to improve data collection and analysis for a specific demographic (girls) at a specific institution (ITC University). Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 07:04:06,065 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:04:06,065 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:04:06,065 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could be taken to improve data collection and analysis regarding the performance of girls at ITC University?', 'answerable': False, 'reasoning': 'The question is about improving data collection and analysis regarding the performance of girls at ITC University. However, the provided schema does not contain specific information about gender-based performance metrics or any direct indicators related to the performance of girls specifically. While there are tables related to students, courses, and assessments, there is no clear structure or data that directly addresses the question of how to improve data collection and analysis for a specific demographic (girls) at a specific institution (ITC University). Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-11 07:04:09,753 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:09,768 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify contributing factors to the lack of data. Therefore, the question cannot be answered based on the schema.", 'feedback': ''}
2025-08-11 07:04:09,768 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:04:09,768 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:04:09,769 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might contribute to the lack of data on girls' academic performance in specific programs at ITC University?", 'answerable': False, 'reasoning': "The question asks for factors contributing to the lack of data on girls' academic performance in specific programs at ITC University. However, the provided schema does not contain any direct information or attributes related to the analysis of gender-specific academic performance or the reasons for data absence. The schema includes various tables related to students, programs, and academic records, but it lacks qualitative data or insights that would help identify contributing factors to the lack of data. Therefore, the question cannot be answered based on the schema.", 'feedback': ''}
2025-08-11 07:04:10,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:10,353 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons, such as grades, assessments, or demographic studies that would allow for such an analysis. The schema includes various tables related to students, courses, and assessments, but it lacks the necessary data to directly answer the question about gender differences in academic performance.', 'feedback': ''}
2025-08-11 07:04:10,353 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:04:10,353 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:04:10,353 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the higher academic performance of girls at ITC University compared to boys?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the academic performance of girls compared to boys at ITC University. However, the provided schema does not contain specific data or metrics related to gender-based academic performance comparisons, such as grades, assessments, or demographic studies that would allow for such an analysis. The schema includes various tables related to students, courses, and assessments, but it lacks the necessary data to directly answer the question about gender differences in academic performance.', 'feedback': ''}
2025-08-11 07:04:10,530 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:14,988 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:15,014 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What steps could be taken to improve data collection and analysis regarding the performance of girls at ITC University?', 'answerable': False, 'reasoning': "The question is about improving data collection and analysis regarding the performance of girls at ITC University. However, the provided schema does not contain specific information about gender-based performance metrics or any direct indicators related to the performance of girls. While there are tables related to students, courses, and assessments, there is no clear structure or data that directly addresses the question of how to improve data collection and analysis specifically for girls' performance. Therefore, without additional context or data points that focus on gender-specific performance, the question cannot be answered from the schema.", 'feedback': ''}
2025-08-11 07:04:15,015 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-11 07:04:15,015 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-11 07:04:15,015 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What steps could be taken to improve data collection and analysis regarding the performance of girls at ITC University?', 'answerable': False, 'reasoning': "The question is about improving data collection and analysis regarding the performance of girls at ITC University. However, the provided schema does not contain specific information about gender-based performance metrics or any direct indicators related to the performance of girls. While there are tables related to students, courses, and assessments, there is no clear structure or data that directly addresses the question of how to improve data collection and analysis specifically for girls' performance. Therefore, without additional context or data points that focus on gender-specific performance, the question cannot be answered from the schema.", 'feedback': ''}
2025-08-11 07:04:15,015 - root - INFO - [{'sex': 'F', 'average_assessment_total': 36.26, 'average_exam_total': 36.87, 'average_finalscore': 68.72, 'total_students': 58}, {'sex': 'M', 'average_assessment_total': 10.92, 'average_exam_total': 13.44, 'average_finalscore': 54.86, 'total_students': 71}]
2025-08-11 07:04:15,015 - root - INFO - 'No results'
2025-08-11 07:04:15,016 - root - INFO - 'No results'
2025-08-11 07:04:15,016 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-11 07:04:20,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:20,881 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-11 07:04:34,334 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:34,370 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:04:34,370 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 07:04:34,370 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:04:34,370 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-11 07:04:34,370 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 07:04:34,370 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 07:04:34,370 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 07:04:34,370 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 07:04:34,370 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do the academic performance metrics of girls compare to those of boys at ITC University?...
2025-08-11 07:04:34,370 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several k...
2025-08-11 07:04:34,370 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: bar_chart
2025-08-11 07:04:34,371 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'sex': 'F', 'average_assessment_total': 36.26, 'average_exam_total': 36.87, 'average_finalscore': ...
2025-08-11 07:04:34,371 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: academic_performance_comparison_by_gender
2025-08-11 07:04:34,371 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-11 07:04:34,371 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-11 07:04:34,371 - celery.redirected - WARNING - [{'sex': 'F', 'average_assessment_total': 36.26, 'average_exam_total': 36.87, 'average_finalscore': 68.72, 'total_students': 58}, {'sex': 'M', 'average_assessment_total': 10.92, 'average_exam_total': 13.44, 'average_finalscore': 54.86, 'total_students': 71}]
2025-08-11 07:04:34,371 - celery.redirected - WARNING - ================================= 
2025-08-11 07:04:34,371 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: academic_performance_comparison_by_gender
2025-08-11 07:04:34,371 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-11 07:04:34,372 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:04:34,372 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 07:04:34,372 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:04:34,372 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-11 07:04:34,373 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 07:04:34,373 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 07:04:34,373 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 07:04:34,373 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 07:04:34,373 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What programs or courses are girls at ITC University excelling in the most?...
2025-08-11 07:04:34,373 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are currently no available data or results indicating which programs or course...
2025-08-11 07:04:34,373 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-11 07:04:34,373 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-11 07:04:34,373 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_programs_performance_at_itc
2025-08-11 07:04:34,373 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-11 07:04:34,373 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-11 07:04:34,374 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:04:34,374 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-11 07:04:34,374 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing at ITC University?'
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-11 07:04:34,374 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any trends in the performance of girls at ITC University over the past few years?...
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there may not be sufficient data available to ana...
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: girls_performance_trends_itc_university
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-11 07:04:34,374 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-11 07:04:34,375 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 3
2025-08-11 07:04:34,375 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:04:34,375 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-11 07:04:34,375 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:04:34,375 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 3 documents
2025-08-11 07:04:34,375 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-11 07:04:34,375 - UPSERT_DOCS - INFO -     Content: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:34,375 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:04:34,375 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-11 07:04:34,375 - UPSERT_DOCS - INFO -     Content: Question: What programs or courses are girls at ITC University excelling in the most?
Answer: It app...
2025-08-11 07:04:34,375 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:09.769125+00:00', 'data_returned': False}
2025-08-11 07:04:34,376 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-11 07:04:34,376 - UPSERT_DOCS - INFO -     Content: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:04:34,376 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:15.015439+00:00', 'data_returned': False}
2025-08-11 07:04:34,376 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 1/3
2025-08-11 07:04:34,502 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.126s]
2025-08-11 07:04:35,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 07:04:35,924 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.540s]
2025-08-11 07:04:35,925 - UPSERT_DOCS - INFO - ✅ Successfully upserted 3 documents to Elasticsearch
2025-08-11 07:04:35,925 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 8
2025-08-11 07:04:35,925 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-11 07:04:35,926 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:04:35,926 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-11 07:04:35,926 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:04:35,926 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/8...
2025-08-11 07:04:37,377 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:37,391 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:04:37,391 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 07:04:37,391 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:04:37,391 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'female students academic performance ITC University'
2025-08-11 07:04:37,391 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-11 07:04:37,391 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-11 07:04:37,519 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-11 07:04:37,519 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 121
2025-08-11 07:04:37,649 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 07:04:37,649 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 66
2025-08-11 07:04:37,778 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-11 07:04:37,779 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 16
2025-08-11 07:04:37,909 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 07:04:37,910 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-11 07:04:38,631 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 07:04:38,773 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.139s]
2025-08-11 07:04:38,774 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 07:04:38,775 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:38,775 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:04:38,775 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:38,775 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:04:38,775 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:38,775 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:38,775 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:38,775 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:38,776 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:04:38,776 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:04:38,776 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:04:38,777 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:04:38,778 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:04:38,778 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_of_girls_over_years
2025-08-11 07:04:38,778 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3405 chars):
2025-08-11 07:04:38,778 - app.chains.section_writer - INFO -    Question: How do the academic performance metrics of girls compare to those of boys at ITC University?
Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several key areas. Girls have an average assessment total of 36.26 and an average exam total of 36.87, leading to an average final score of 68.72. In contrast, boys have significantly lower averages, with an average assessment total of 10.92, an average exam total of 13.44, and an average final sc...
2025-08-11 07:04:44,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:44,129 - app.chains.section_writer - INFO - 🤖 AI generated section (1289 chars):
2025-08-11 07:04:44,129 - app.chains.section_writer - INFO -    The purpose of this report is to analyze the academic performance of female students at ITC University, highlighting their achievements and challenges in comparison to their male peers. The key finding indicates that girls outperform boys across various academic metrics, suggesting a positive trend ...
2025-08-11 07:04:44,129 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['academic_performance_comparison_by_gender']
2025-08-11 07:04:44,130 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 1299 characters
2025-08-11 07:04:44,130 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/8...
2025-08-11 07:04:45,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:45,579 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:04:45,579 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 07:04:45,579 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:04:45,579 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University gender performance in academia'
2025-08-11 07:04:45,579 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-11 07:04:45,579 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-11 07:04:45,708 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 07:04:45,709 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 121
2025-08-11 07:04:45,837 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-11 07:04:45,837 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 66
2025-08-11 07:04:45,966 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-11 07:04:45,966 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 16
2025-08-11 07:04:46,096 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 07:04:46,097 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-11 07:04:46,607 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 07:04:46,746 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.134s]
2025-08-11 07:04:46,746 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 07:04:46,747 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:46,747 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:46,747 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:46,747 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:46,747 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:46,748 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:04:46,748 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:46,748 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:04:46,748 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:04:46,748 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:04:46,749 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-11 07:04:46,750 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:04:46,750 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:04:46,750 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_of_girls_over_years
2025-08-11 07:04:46,750 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3405 chars):
2025-08-11 07:04:46,750 - app.chains.section_writer - INFO -    Question: How do the academic performance metrics of girls compare to those of boys at ITC University?
Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several key areas. Girls have an average final score of 68.72, compared to boys' average final score of 54.86. Additionally, girls have a slightly higher average grade point (GP) of 1.293 compared to boys' average GP of 1.254. In terms of total assessments, boys have completed 71 assessments while...
2025-08-11 07:04:51,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:51,224 - app.chains.section_writer - INFO - 🤖 AI generated section (1068 chars):
2025-08-11 07:04:51,224 - app.chains.section_writer - INFO -    ## I. Background  

ITC University is an institution dedicated to fostering academic excellence and inclusivity among its diverse student body. A critical aspect of this commitment involves examining gender performance in academia, particularly the academic outcomes of female students compared to th...
2025-08-11 07:04:51,224 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['academic_performance_by_gender']
2025-08-11 07:04:51,224 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 1034 characters
2025-08-11 07:04:51,225 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/8...
2025-08-11 07:04:53,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:53,085 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:04:53,085 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 07:04:53,085 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:04:53,085 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'girls boys academic performance comparison'
2025-08-11 07:04:53,085 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-11 07:04:53,085 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-11 07:04:53,215 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-11 07:04:53,216 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 121
2025-08-11 07:04:53,345 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 07:04:53,346 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 66
2025-08-11 07:04:53,475 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-11 07:04:53,475 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 16
2025-08-11 07:04:53,602 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-11 07:04:53,602 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-11 07:04:54,046 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 07:04:54,191 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.141s]
2025-08-11 07:04:54,192 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 07:04:54,192 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:54,192 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:54,192 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:54,192 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:54,192 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:54,193 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:04:54,193 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:54,193 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:04:54,193 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:04:54,193 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:04:54,194 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:54,194 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:54,194 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:04:54,194 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:54,194 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_of_girls_over_years
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3405 chars):
2025-08-11 07:04:54,195 - app.chains.section_writer - INFO -    Question: How do the academic performance metrics of girls compare to those of boys at ITC University?
Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several key areas. Girls have an average final score of 68.72, compared to boys' average final score of 54.86. Additionally, girls have a slightly higher average grade point (GP) of 1.293 compared to boys' average GP of 1.254. In terms of total assessments, boys have completed 71 assessments while...
2025-08-11 07:04:58,030 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:58,034 - app.chains.section_writer - INFO - 🤖 AI generated section (901 chars):
2025-08-11 07:04:58,034 - app.chains.section_writer - INFO -    ## II. Academic Performance Metrics  

### Comparison of Girls' and Boys' Performance  

The academic performance metrics at ITC University reveal a significant disparity between girls and boys across various assessments. 

- **Average Assessment Scores**: Girls achieved an average score of 36.26, w...
2025-08-11 07:04:58,034 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['academic_performance_by_gender']
2025-08-11 07:04:58,034 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 867 characters
2025-08-11 07:04:58,035 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/8...
2025-08-11 07:04:59,904 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:04:59,908 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:04:59,908 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 07:04:59,908 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:04:59,908 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Extracurricular activities impact on academics and leadership roles'
2025-08-11 07:04:59,908 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-11 07:04:59,908 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-11 07:05:00,098 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.190s]
2025-08-11 07:05:00,099 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 121
2025-08-11 07:05:00,298 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.199s]
2025-08-11 07:05:00,298 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 66
2025-08-11 07:05:00,498 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.199s]
2025-08-11 07:05:00,498 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 16
2025-08-11 07:05:00,688 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.190s]
2025-08-11 07:05:00,689 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-11 07:05:01,362 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 07:05:01,565 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.199s]
2025-08-11 07:05:01,566 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 07:05:01,566 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:01,566 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:01,566 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:01,566 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:01,566 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:01,566 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:05:01,566 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:01,567 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:05:01,567 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:05:01,567 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:05:01,567 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:01,567 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:01,567 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_of_girls_over_years
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3405 chars):
2025-08-11 07:05:01,568 - app.chains.section_writer - INFO -    Question: How do the academic performance metrics of girls compare to those of boys at ITC University?
Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several key areas. Girls have an average assessment total of 36.26 and an average exam total of 36.87, leading to an average final score of 68.72. In contrast, boys have significantly lower averages, with an average assessment total of 10.92, an average exam total of 13.44, and an average final sc...
2025-08-11 07:05:07,227 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:05:07,240 - app.chains.section_writer - INFO - 🤖 AI generated section (1441 chars):
2025-08-11 07:05:07,240 - app.chains.section_writer - INFO -    ## III. Participation in Extracurricular Activities  

### Involvement in Clubs and Organizations  
At ITC University, participation in clubs and organizations is a significant aspect of student life. Female students are actively involved in various extracurricular activities, which not only enhance...
2025-08-11 07:05:07,240 - app.chains.section_writer - INFO - ✅ Found 3 data tag placeholders: ['academic_performance_by_gender', 'academic_performance_comparison_by_sex', 'average_scores_of_girls_over_years']
2025-08-11 07:05:07,240 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 1327 characters
2025-08-11 07:05:07,241 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/8...
2025-08-11 07:05:08,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:05:08,521 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:05:08,521 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 07:05:08,521 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:05:08,521 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Challenges Female Students Face'
2025-08-11 07:05:08,521 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-11 07:05:08,521 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-11 07:05:08,714 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.193s]
2025-08-11 07:05:08,715 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 121
2025-08-11 07:05:08,906 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.191s]
2025-08-11 07:05:08,907 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 66
2025-08-11 07:05:09,105 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.198s]
2025-08-11 07:05:09,105 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 16
2025-08-11 07:05:09,296 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.190s]
2025-08-11 07:05:09,296 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-11 07:05:09,758 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 07:05:09,963 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.202s]
2025-08-11 07:05:09,964 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 07:05:09,964 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:09,965 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:09,965 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:05:09,965 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:05:09,965 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:09,965 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:09,965 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:09,965 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:05:09,965 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:09,965 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   📄 Doc 2: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_of_girls_over_years
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   📄 Doc 5: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-11 07:05:09,966 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3405 chars):
2025-08-11 07:05:09,967 - app.chains.section_writer - INFO -    Question: How do the academic performance metrics of girls compare to those of boys at ITC University?
Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several key areas. Girls have an average assessment total of 36.26 and an average exam total of 36.87, leading to an average final score of 68.72. In contrast, boys have significantly lower averages, with an average assessment total of 10.92, an average exam total of 13.44, and an average final sc...
2025-08-11 07:05:15,589 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:05:15,598 - app.chains.section_writer - INFO - 🤖 AI generated section (1602 chars):
2025-08-11 07:05:15,599 - app.chains.section_writer - INFO -    ## IV. Challenges Faced by Female Students  

### Social and Cultural Barriers  
Female students at ITC University encounter various social and cultural barriers that can hinder their academic success. These barriers often stem from traditional gender roles and societal expectations, which may disco...
2025-08-11 07:05:15,599 - app.chains.section_writer - INFO - ✅ Found 4 data tag placeholders: ['academic_performance_by_gender', 'average_scores_of_girls_over_years', 'academic_performance_comparison_by_gender', 'academic_performance_comparison_by_sex']
2025-08-11 07:05:15,599 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 1457 characters
2025-08-11 07:05:15,600 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 6/8...
2025-08-11 07:05:16,344 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:05:16,354 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:05:16,355 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 07:05:16,355 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:05:16,355 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'female student success stories'
2025-08-11 07:05:16,355 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-11 07:05:16,355 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-11 07:05:16,556 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.200s]
2025-08-11 07:05:16,557 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 121
2025-08-11 07:05:16,754 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.197s]
2025-08-11 07:05:16,755 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 66
2025-08-11 07:05:16,955 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.199s]
2025-08-11 07:05:16,955 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 16
2025-08-11 07:05:17,146 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.190s]
2025-08-11 07:05:17,147 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-11 07:05:17,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 07:05:17,872 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.203s]
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:17,873 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:05:17,875 - app.chains.section_writer - INFO -   📄 Doc 1: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:05:17,875 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:05:17,875 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_of_girls_over_years
2025-08-11 07:05:17,875 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:17,875 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:17,875 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:05:17,875 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:17,875 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:17,875 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:05:17,876 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:17,876 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:05:17,876 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-11 07:05:17,876 - app.chains.section_writer - INFO -   📄 Doc 5: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:17,876 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:05:17,876 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-11 07:05:17,876 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3405 chars):
2025-08-11 07:05:17,876 - app.chains.section_writer - INFO -    Question: Are there any trends in the performance of girls at ITC University over the past few years?
Answer: The performance of girls at ITC University shows some interesting trends over the past few years. In 2019, the average score for female students was 65.98, based on 17 assessments. By 2023, there was a significant drop in the number of assessments to just 3, and the average score for that year is not available. However, in 2024, the average score improved to 72.95, with 38 assessments co...
2025-08-11 07:05:24,556 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:05:24,561 - app.chains.section_writer - INFO - 🤖 AI generated section (1421 chars):
2025-08-11 07:05:24,561 - app.chains.section_writer - INFO -    ## V. Success Stories  

### Notable Achievements by Female Students  
The performance of female students at ITC University has shown a positive trend over recent years. In 2019, the average score for female students was 65.98, based on 17 assessments. Despite a gap in data for 2023, the average sco...
2025-08-11 07:05:24,561 - app.chains.section_writer - INFO - ✅ Found 3 data tag placeholders: ['average_scores_of_girls_over_years', 'academic_performance_by_gender', 'academic_performance_comparison_by_sex']
2025-08-11 07:05:24,562 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 6 completed and processed: 1307 characters
2025-08-11 07:05:24,562 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 7/8...
2025-08-11 07:05:26,285 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:05:26,315 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:05:26,315 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 07:05:26,315 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:05:26,315 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Recommendations for STEM performance improvement'
2025-08-11 07:05:26,315 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-11 07:05:26,315 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-11 07:05:26,506 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.191s]
2025-08-11 07:05:26,507 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 121
2025-08-11 07:05:26,702 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.195s]
2025-08-11 07:05:26,702 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 66
2025-08-11 07:05:26,893 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.190s]
2025-08-11 07:05:26,893 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 16
2025-08-11 07:05:27,085 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.191s]
2025-08-11 07:05:27,085 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-11 07:05:27,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 07:05:27,709 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.207s]
2025-08-11 07:05:27,710 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 07:05:27,710 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:05:27,711 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:05:27,711 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:27,711 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:27,711 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:27,711 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:27,711 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:27,711 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:05:27,711 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:27,711 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:05:27,712 - app.chains.section_writer - INFO -   📄 Doc 1: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:05:27,712 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:05:27,712 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_of_girls_over_years
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   📄 Doc 5: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:27,713 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:05:27,714 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-11 07:05:27,714 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3405 chars):
2025-08-11 07:05:27,714 - app.chains.section_writer - INFO -    Question: Are there any trends in the performance of girls at ITC University over the past few years?
Answer: The performance of girls at ITC University shows some interesting trends over the past few years. In 2019, the average score for female students was 65.98, based on 17 assessments. By 2023, there was a significant drop in the number of assessments to just 3, and the average score for that year is not available. However, in 2024, the average score improved to 72.95, with 38 assessments co...
2025-08-11 07:05:34,483 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:05:34,496 - app.chains.section_writer - INFO - 🤖 AI generated section (1800 chars):
2025-08-11 07:05:34,496 - app.chains.section_writer - INFO -    ## VI. Recommendations  

### Strategies for Improving Performance  
To enhance the academic performance of female students at ITC University, it is essential to implement targeted strategies. These may include personalized tutoring programs, mentorship opportunities, and workshops that focus on stu...
2025-08-11 07:05:34,496 - app.chains.section_writer - INFO - ✅ Found 4 data tag placeholders: ['average_scores_of_girls_over_years', 'academic_performance_by_gender', 'academic_performance_comparison_by_gender', 'academic_performance_comparison_by_sex']
2025-08-11 07:05:34,496 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 7 completed and processed: 1655 characters
2025-08-11 07:05:34,497 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 8/8...
2025-08-11 07:05:35,164 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:05:35,175 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:05:35,175 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-11 07:05:35,175 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:05:35,175 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'girls' academic success ITC University conclusion'
2025-08-11 07:05:35,175 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing at ITC University?'
2025-08-11 07:05:35,175 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing at ITC University?'
2025-08-11 07:05:35,371 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.195s]
2025-08-11 07:05:35,372 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 121
2025-08-11 07:05:35,564 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.192s]
2025-08-11 07:05:35,565 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 66
2025-08-11 07:05:35,761 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.195s]
2025-08-11 07:05:35,761 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 16
2025-08-11 07:05:35,961 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.200s]
2025-08-11 07:05:35,962 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-11 07:05:36,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-11 07:05:36,637 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.209s]
2025-08-11 07:05:36,638 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-11 07:05:36,639 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:36,639 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:05:36,639 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:36,640 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:36,640 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:36,640 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:05:36,640 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:36,640 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:36,640 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:05:36,640 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:05:36,641 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:11:02.169807+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_sex'}
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_sex
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T16:05:29.605840+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-11T07:04:10.354004+00:00', 'data_returned': True, 'data_tag': 'academic_performance_comparison_by_gender'}
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_comparison_by_gender
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How do the academic performance metrics of girls compare to those of boys at ITC Universit...
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T17:28:06.022382+00:00', 'data_returned': True, 'data_tag': 'academic_performance_by_gender'}
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   ✅ Added Data Tag: academic_performance_by_gender
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any trends in the performance of girls at ITC University over the past few years...
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'How are girls performing at ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:28:48.704406+00:00', 'data_returned': True, 'data_tag': 'average_scores_of_girls_over_years'}
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -   ✅ Added Data Tag: average_scores_of_girls_over_years
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (3405 chars):
2025-08-11 07:05:36,642 - app.chains.section_writer - INFO -    Question: How do the academic performance metrics of girls compare to those of boys at ITC University?
Answer: At ITC University, the academic performance metrics indicate that girls outperform boys in several key areas. Girls have an average assessment total of 36.26 and an average exam total of 36.87, leading to an average final score of 68.72. In contrast, boys have significantly lower averages, with an average assessment total of 10.92, an average exam total of 13.44, and an average final sc...
2025-08-11 07:05:41,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-11 07:05:41,407 - app.chains.section_writer - INFO - 🤖 AI generated section (1358 chars):
2025-08-11 07:05:41,408 - app.chains.section_writer - INFO -    In conclusion, the academic performance metrics at ITC University reveal that girls consistently outperform boys across several key indicators. Girls have an average final score of 68.72, significantly higher than boys' average final score of 54.86. Additionally, girls exhibit a higher average grade...
2025-08-11 07:05:41,408 - app.chains.section_writer - INFO - ✅ Found 3 data tag placeholders: ['academic_performance_comparison_by_gender', 'academic_performance_by_gender', 'average_scores_of_girls_over_years']
2025-08-11 07:05:41,408 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 8 completed and processed: 1255 characters
2025-08-11 07:05:41,409 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-11 07:05:41,409 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-11 07:05:41,409 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-11 07:05:41,409 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-11 07:05:41,410 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 9
2025-08-11 07:05:41,410 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 3
2025-08-11 07:05:41,410 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 1
2025-08-11 07:05:41,410 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250811_065402.log
2025-08-11 07:05:41,414 - celery.app.trace - INFO - Task generate_streaming_report[490550bf-8fd3-4c96-b150-fde35fb92248] succeeded in 199.92156508300002s: {'outline': '# Report on Girls\' Academic Performance at ITC University

## Introduction  
The purpose of this report is to analyze the academic performance of female students at ITC University, highlighting their achievements and challenges in comparison to their male peers. The key finding indicates that girls outperform boys across various academic metrics, suggesting a positive trend in gender performance within the university.

## I. Background  
- Overview of ITC University  
- Importance of examining gender performance in academia  

## II. Academic Performance Metrics  
- Comparison of Girls\' and Boys\' Performance  
   - Average Assessment Scores  
      - Girls: 36.26  
      - Boys: 10.92  
   - Average Exam Scores  
      - Girls: 36.87  
      - Boys: 13.44  
   - Average Final Scores  
      - Girls: 68.72  
      - Boys: 54.86  
- Summary of Findings  
   - Girls outperform boys in all key academic metrics  
   - Implications of higher performance for gender dynamics at the university  

## III....', ...}
2025-08-11 07:08:18,506 - celery.worker.consumer.consumer - WARNING - consumer: Connection to broker lost. Trying to re-establish the connection...
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 341, in start
    blueprint.start(self)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/bootsteps.py", line 116, in start
    step.start(parent)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 772, in start
    c.loop(*c.loop_args())
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/loops.py", line 97, in asynloop
    next(loop)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/asynchronous/hub.py", line 373, in create_loop
    cb(*cbargs)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 1352, in on_readable
    self.cycle.on_readable(fileno)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 569, in on_readable
    chan.handlers[type]()
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 918, in _receive
    ret.append(self._receive_one(c))
               ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 928, in _receive_one
    response = c.parse_response()
               ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 926, in parse_response
    response = self._execute(conn, try_read)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 902, in _execute
    return conn.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/retry.py", line 65, in call_with_retry
    fail(error)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 904, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 891, in _disconnect_raise_connect
    raise error
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/retry.py", line 62, in call_with_retry
    return do()
           ^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 903, in <lambda>
    lambda: command(*args, **kwargs),
            ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 924, in try_read
    return conn.read_response(disconnect_on_error=False, push_request=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/connection.py", line 613, in read_response
    response = self._parser.read_response(disable_decoding=disable_decoding)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/resp2.py", line 15, in read_response
    result = self._read_response(disable_decoding=disable_decoding)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/resp2.py", line 25, in _read_response
    raw = self._buffer.readline()
          ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/socket.py", line 115, in readline
    self._read_from_socket()
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/socket.py", line 68, in _read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
2025-08-11 07:08:18,514 - py.warnings - WARNING - /Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py:392: CPendingDeprecationWarning: 
In Celery 5.1 we introduced an optional breaking change which
on connection loss cancels all currently executed tasks with late acknowledgement enabled.
These tasks cannot be acknowledged as the connection is gone, and the tasks are automatically redelivered
back to the queue. You can enable this behavior using the worker_cancel_long_running_tasks_on_connection_loss
setting. In Celery 5.1 it is set to False by default. The setting will be set to True by default in Celery 6.0.

  warnings.warn(CANCEL_TASKS_BY_DEFAULT, CPendingDeprecationWarning)

2025-08-11 07:08:18,518 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 2.00 seconds... (1/100)

2025-08-11 07:08:20,527 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 4.00 seconds... (2/100)

2025-08-11 07:08:24,549 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 6.00 seconds... (3/100)


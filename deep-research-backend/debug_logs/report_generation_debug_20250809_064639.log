2025-08-09 06:46:39,325 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250809_064639.log
2025-08-09 06:46:39,326 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:46:39,326 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 208e5dde-c9d4-4f9e-bab8-19261c430e0c
2025-08-09 06:46:39,326 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:46:39,327 - REPORT_REQUEST - INFO - 📝 Original Question: 'From which instituion do students owe the most fees?'
2025-08-09 06:46:39,327 - REPORT_REQUEST - INFO - 🆔 Task ID: 208e5dde-c9d4-4f9e-bab8-19261c430e0c
2025-08-09 06:46:39,327 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-09T06:46:39.327572
2025-08-09 06:46:39,484 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.156s]
2025-08-09 06:46:39,484 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 44
2025-08-09 06:46:39,636 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.152s]
2025-08-09 06:46:39,636 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 15
2025-08-09 06:46:39,768 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:0.132s]
2025-08-09 06:46:39,769 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (4):
2025-08-09 06:46:39,769 - ELASTICSEARCH_STATE - INFO -   - 'From which institution do students owe the most fees?' (20 docs)
2025-08-09 06:46:39,769 - ELASTICSEARCH_STATE - INFO -   - 'From which instituion do students owe the most fees?' (10 docs)
2025-08-09 06:46:39,769 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (7 docs)
2025-08-09 06:46:39,769 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (7 docs)
2025-08-09 06:46:39,769 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:46:39,769 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-09 06:46:39,769 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:46:39,769 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: From which instituion do students owe the most fees?
2025-08-09 06:46:39,769 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-09 06:46:39,769 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-09 06:46:47,760 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:47,780 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-09 06:46:51,712 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:51,728 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-09 06:46:54,018 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:54,065 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:54,148 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:54,429 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:54,538 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:54,655 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:54,925 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:55,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:55,431 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:56,421 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:56,488 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-09 06:46:59,101 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:59,351 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:59,359 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:59,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:59,505 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:59,589 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:59,738 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:46:59,995 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:02,019 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:02,086 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:02,172 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:02,392 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:02,428 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:02,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:02,882 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:03,272 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:03,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:03,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:03,510 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:03,550 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:03,588 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:04,182 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:04,397 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:04,598 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:07,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:07,595 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:09,289 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:09,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:10,031 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:10,042 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:10,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:10,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:11,287 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:11,293 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average fee debt per student at the institution where students owe the most fees?', 'sql': 'SELECT AVG(balance) AS average_fee_debt\nFROM student_balances\nWHERE institution_id = (\n    SELECT institution_id\n    FROM student_balances\n    GROUP BY institution_id\n    ORDER BY SUM(balance) DESC\n    LIMIT 1\n)', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution where students owe the most fees by summing the balances for each institution and ordering them in descending order. It then limits the result to the top institution. The outer query calculates the average balance (fee debt) for that specific institution, which directly answers the question about the average fee debt per student at the institution with the highest total debt.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly calculating the average fee debt per student rather than just the average balance. This would involve dividing the total balance by the number of students at that institution to get a more accurate representation of the average fee debt per student.'}
2025-08-09 06:47:11,293 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:11,294 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:47:13,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:14,729 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:14,935 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:14,990 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:15,409 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:15,412 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the fee debt at the institution where students owe the most fees compare to other institutions?', 'sql': 'WITH institution_debts AS (  SELECT institution_id, SUM(balance) AS total_debt  FROM student_balances  GROUP BY institution_id), max_debt AS (  SELECT institution_id, total_debt  FROM institution_debts  WHERE total_debt = (SELECT MAX(total_debt) FROM institution_debts)) SELECT i.name AS institution_name, id.total_debt, (SELECT AVG(total_debt) FROM institution_debts) AS average_debt FROM max_debt id JOIN institutions i ON id.institution_id = i.id;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the highest total debt by summing the balances for each institution and then selecting the one with the maximum total debt. It also calculates the average debt across all institutions, which allows for a comparison between the institution with the highest debt and the average debt of other institutions. The final output includes the name of the institution with the highest debt, its total debt, and the average debt, which directly addresses the question.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating how the highest debt compares to the average debt, perhaps by including a percentage difference or a comparison statement in the output. Additionally, clarifying the question to specify whether a direct comparison (like a ratio or percentage) is desired could enhance the understanding of the expected output.'}
2025-08-09 06:47:15,412 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:15,413 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:47:15,856 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:16,212 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:16,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:16,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:17,403 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:17,461 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:17,781 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:18,420 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:18,690 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:19,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:19,233 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:19,462 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:19,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:19,988 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:20,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:20,416 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:20,546 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:20,843 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:21,259 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:21,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:21,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:21,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:21,864 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:22,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:23,110 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:23,426 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:23,457 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications on financial situations. The schema lacks detailed financial behavior data, contextual factors, or a mechanism to analyze trends over time, which are necessary to answer the question fully.', 'feedback': ''}
2025-08-09 06:47:23,457 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:23,457 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:23,457 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications on financial situations. The schema lacks detailed financial behavior data, contextual factors, or a mechanism to analyze trends over time, which are necessary to answer the question fully.', 'feedback': ''}
2025-08-09 06:47:23,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:24,037 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:25,263 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:25,674 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:25,820 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:25,848 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:25,858 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high average fee debt at Central University compared to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high average fee debt at Central University compared to other institutions. While the schema contains various tables related to institutions, students, and financial transactions, it does not provide specific data or metrics that would allow for a comparative analysis of fee debts across institutions. Additionally, the schema lacks qualitative data that could explain the reasons behind the fee debts, such as economic factors, student demographics, or institutional policies. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:47:25,858 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:25,858 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:25,858 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high average fee debt at Central University compared to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high average fee debt at Central University compared to other institutions. While the schema contains various tables related to institutions, students, and financial transactions, it does not provide specific data or metrics that would allow for a comparative analysis of fee debts across institutions. Additionally, the schema lacks qualitative data that could explain the reasons behind the fee debts, such as economic factors, student demographics, or institutional policies. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:47:25,924 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:26,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:26,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:27,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:27,956 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:27,963 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total amount of fees owed by students at the institution where students owe the most fees?', 'sql': 'SELECT institution_id, SUM(balance) AS total_fees_owed\nFROM student_balances\nGROUP BY institution_id\nORDER BY total_fees_owed DESC\nLIMIT 1;', 'correct': True, 'reasoning': "The SQL query correctly aggregates the total balance (fees owed) for each institution by summing the 'balance' field in the 'student_balances' table. It groups the results by 'institution_id' and orders them in descending order based on the total fees owed. The use of 'LIMIT 1' ensures that only the institution with the highest total fees owed is returned, which directly answers the question.", 'feedback': 'The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to include a WHERE clause to filter for active students if the schema supports it, depending on the context of the question.'}
2025-08-09 06:47:27,963 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:27,963 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:47:28,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:28,131 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors that lead to credit balances or the broader implications on students' financial situations. The schema lacks specific data on the reasons behind credit balances, such as financial aid policies, student spending habits, or external financial support, which are necessary to answer the question comprehensively.", 'feedback': ''}
2025-08-09 06:47:28,131 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:28,131 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:28,131 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors that lead to credit balances or the broader implications on students' financial situations. The schema lacks specific data on the reasons behind credit balances, such as financial aid policies, student spending habits, or external financial support, which are necessary to answer the question comprehensively.", 'feedback': ''}
2025-08-09 06:47:28,934 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:29,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:29,958 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high average fee debt at Central University compared to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high average fee debt at Central University compared to other institutions. However, the provided schema does not contain specific data or metrics related to average fee debt or the factors influencing it. While there are tables related to billing, fees, and institutions, there is no direct way to derive or analyze the factors contributing to fee debt without additional context or data points that are not present in the schema.', 'feedback': ''}
2025-08-09 06:47:29,958 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:29,958 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:29,958 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high average fee debt at Central University compared to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high average fee debt at Central University compared to other institutions. However, the provided schema does not contain specific data or metrics related to average fee debt or the factors influencing it. While there are tables related to billing, fees, and institutions, there is no direct way to derive or analyze the factors contributing to fee debt without additional context or data points that are not present in the schema.', 'feedback': ''}
2025-08-09 06:47:30,477 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:30,669 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:30,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:31,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:31,614 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:32,259 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:32,783 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:32,799 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:32,811 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How has the fee debt at the institution where students owe the most fees changed over the past few years?', 'sql': 'WITH InstitutionDebt AS (  SELECT sb.institution_id, SUM(sb.balance) AS total_debt  FROM student_balances sb  GROUP BY sb.institution_id), MaxDebtInstitution AS (  SELECT institution_id  FROM InstitutionDebt  ORDER BY total_debt DESC  LIMIT 1) SELECT bp.description, SUM(sb.balance) AS total_debt  FROM student_balances sb  JOIN billing_periods bp ON sb.billing_period_id = bp.id  WHERE sb.institution_id = (SELECT institution_id FROM MaxDebtInstitution)  GROUP BY bp.description ORDER BY bp.start_date;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total debt by summing the balances from the 'student_balances' table and grouping by institution. It then selects the billing periods associated with that institution and sums the balances again, grouping by the billing period description. This effectively answers the question about how the fee debt has changed over the past few years, as it provides a breakdown of total debt by billing period.", 'feedback': "The question could be clarified by specifying what is meant by 'changed'—whether it refers to an increase or decrease in debt over time, or simply the total debt amounts for each period. Additionally, the SQL could be improved by including a time filter to focus on the relevant years, ensuring that only the last few years of data are considered."}
2025-08-09 06:47:32,811 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:32,811 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:47:32,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:33,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:33,899 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:33,917 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial context, such as income sources, expenses, or specific financial policies that could influence these balances.', 'feedback': ''}
2025-08-09 06:47:33,917 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:33,917 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:33,918 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial context, such as income sources, expenses, or specific financial policies that could influence these balances.', 'feedback': ''}
2025-08-09 06:47:34,331 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:34,338 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the breakdown of the types of fees owed by students at the institution where students owe the most fees?', 'sql': "WITH InstitutionFees AS (  SELECT s.institution_id, SUM(st.transaction_amount) AS total_fees  FROM student_transactions st  JOIN students s ON st.student_id = s.id  WHERE st.type = 'debit'  GROUP BY s.institution_id), MaxInstitution AS (  SELECT institution_id  FROM InstitutionFees  ORDER BY total_fees DESC  LIMIT 1) SELECT st.transaction_description, SUM(st.transaction_amount) AS total_amount  FROM student_transactions st  JOIN students s ON st.student_id = s.id  WHERE s.institution_id = (SELECT institution_id FROM MaxInstitution) AND st.type = 'debit'  GROUP BY st.transaction_description;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution where students owe the most fees by summing the transaction amounts for each institution and selecting the one with the highest total. It then retrieves the breakdown of fee types (transaction descriptions) owed by students at that institution, summing the amounts for each type. The use of CTEs (Common Table Expressions) is appropriate for organizing the logic of the query, and the filtering by 'debit' type aligns with the question's focus on fees owed.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by adding comments to clarify each step of the CTEs for better readability. Additionally, ensuring that the transaction types are clearly defined in the schema would help confirm that 'debit' is indeed the correct type to filter for fees owed."}
2025-08-09 06:47:34,338 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:34,338 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:47:34,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:34,498 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high average fee debt at Central University compared to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high average fee debt at Central University compared to other institutions. However, the provided schema does not contain specific data or metrics related to average fee debt or the factors influencing it. While there are tables related to billing, fees, and institutions, there is no direct correlation or analysis capability within the schema to determine the reasons behind the fee debt levels. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 06:47:34,498 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:34,498 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:34,498 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high average fee debt at Central University compared to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high average fee debt at Central University compared to other institutions. However, the provided schema does not contain specific data or metrics related to average fee debt or the factors influencing it. While there are tables related to billing, fees, and institutions, there is no direct correlation or analysis capability within the schema to determine the reasons behind the fee debt levels. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 06:47:34,907 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:35,051 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:35,502 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:36,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:36,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:36,633 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:37,230 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:37,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:37,524 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:37,539 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or how these balances affect students' financial experiences, which would require qualitative data or analysis not present in the schema.", 'feedback': ''}
2025-08-09 06:47:37,540 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:37,540 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:37,540 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or how these balances affect students' financial experiences, which would require qualitative data or analysis not present in the schema.", 'feedback': ''}
2025-08-09 06:47:38,575 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:38,710 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:38,784 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:38,788 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What percentage of students at the institution where students owe the most fees are currently in debt?', 'sql': 'WITH InstitutionDebt AS (  SELECT sb.institution_id, SUM(b.total_due) AS total_due  FROM student_balances sb  JOIN bills b ON sb.billing_period_id = b.billing_period_id  GROUP BY sb.institution_id  ORDER BY total_due DESC  LIMIT 1),  TotalStudents AS (  SELECT COUNT(*) AS total_students  FROM students s  JOIN student_balances sb ON s.id = sb.student_id  WHERE sb.balance < 0 AND sb.institution_id = (SELECT institution_id FROM InstitutionDebt)  )  SELECT (SELECT COUNT(*) FROM students s JOIN student_balances sb ON s.id = sb.student_id WHERE sb.balance < 0 AND sb.institution_id = (SELECT institution_id FROM InstitutionDebt)) * 100.0 / (SELECT COUNT(*) FROM students WHERE institution_id = (SELECT institution_id FROM InstitutionDebt)) AS percentage_in_debt;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the highest total fees owed by students by summing the total_due from the bills table, grouped by institution_id. It then calculates the number of students in debt (those with a negative balance) at that institution and divides it by the total number of students at the same institution to find the percentage of students in debt. The use of Common Table Expressions (CTEs) is appropriate for breaking down the problem into manageable parts, and the final calculation accurately reflects the question asked.', 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by ensuring that the CTEs are named more descriptively, such as 'HighestDebtInstitution' instead of 'InstitutionDebt', to enhance readability. Additionally, consider adding comments to explain each part of the query for future reference."}
2025-08-09 06:47:38,789 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:38,789 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:47:39,182 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:39,201 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. The schema provides various tables related to students, transactions, and financial records, but it does not contain specific analytical data or insights into the reasons behind credit balances or debts. Additionally, the schema lacks a direct way to correlate these financial situations with broader impacts on students' financial situations. Therefore, while some data may be available, it is insufficient to fully answer the question.", 'feedback': ''}
2025-08-09 06:47:39,202 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:39,202 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:39,202 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. The schema provides various tables related to students, transactions, and financial records, but it does not contain specific analytical data or insights into the reasons behind credit balances or debts. Additionally, the schema lacks a direct way to correlate these financial situations with broader impacts on students' financial situations. Therefore, while some data may be available, it is insufficient to fully answer the question.", 'feedback': ''}
2025-08-09 06:47:39,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:39,688 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:39,869 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:40,215 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:40,229 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high average fee debt at Central University compared to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high average fee debt at Central University compared to other institutions. While the schema contains various tables related to institutions, students, and financial transactions, it does not provide specific data or metrics that would allow for a comparative analysis of fee debts across institutions. Additionally, the schema lacks qualitative data that could explain the reasons behind the fee debts, such as institutional policies, economic factors, or student demographics. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:47:40,230 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:40,230 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:40,230 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high average fee debt at Central University compared to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high average fee debt at Central University compared to other institutions. While the schema contains various tables related to institutions, students, and financial transactions, it does not provide specific data or metrics that would allow for a comparative analysis of fee debts across institutions. Additionally, the schema lacks qualitative data that could explain the reasons behind the fee debts, such as institutional policies, economic factors, or student demographics. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:47:41,598 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:41,616 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded fee debt data for this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded fee debt data for an institution. However, the provided schema does not contain any specific tables or fields that directly relate to the reasons or factors behind the absence of fee debt data. The schema includes various tables related to institutions, students, financial transactions, and billing, but it does not provide insights into the reasons for missing data or the context behind fee debts. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:47:41,616 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:41,616 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:41,616 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded fee debt data for this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded fee debt data for an institution. However, the provided schema does not contain any specific tables or fields that directly relate to the reasons or factors behind the absence of fee debt data. The schema includes various tables related to institutions, students, financial transactions, and billing, but it does not provide insights into the reasons for missing data or the context behind fee debts. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:47:41,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:41,671 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or a comprehensive view of students' financial experiences, making it impossible to answer the question fully.", 'feedback': ''}
2025-08-09 06:47:41,671 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:41,671 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:41,671 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or a comprehensive view of students' financial experiences, making it impossible to answer the question fully.", 'feedback': ''}
2025-08-09 06:47:43,449 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:43,901 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:44,131 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:45,889 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:46,139 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:46,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:46,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:46,471 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or a comprehensive view of students' financial experiences, which would require qualitative data or analysis beyond the available tables.", 'feedback': ''}
2025-08-09 06:47:46,472 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:46,472 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:46,472 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or a comprehensive view of students' financial experiences, which would require qualitative data or analysis beyond the available tables.", 'feedback': ''}
2025-08-09 06:47:46,513 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:46,535 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded fee debt data for this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded fee debt data for an institution. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the absence of fee debt data. The schema includes various tables related to financial transactions, student information, and billing, but it does not provide insights into the reasons for missing data or the context behind fee debts. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:47:46,535 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:46,535 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:46,535 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded fee debt data for this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded fee debt data for an institution. However, the provided schema does not contain any specific tables or fields that directly address the reasons or factors behind the absence of fee debt data. The schema includes various tables related to financial transactions, student information, and billing, but it does not provide insights into the reasons for missing data or the context behind fee debts. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:47:47,485 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:47,962 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:48,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:48,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:48,647 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:48,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:49,715 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:50,762 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:50,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:50,769 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific programs or courses at the institution where students owe the most fees that have higher debts?', 'sql': 'SELECT p.long_name AS program_name, c.title AS course_title, SUM(b.amount) AS total_debt\nFROM student_bills b\nJOIN students s ON b.student_id = s.id\nJOIN student_programs sp ON s.id = sp.student_id\nJOIN programs p ON sp.program_id = p.id\nJOIN courses c ON sp.major_id = c.id\nGROUP BY p.long_name, c.title\nHAVING SUM(b.amount) > (SELECT AVG(total_debt) FROM (SELECT SUM(b.amount) AS total_debt FROM student_bills b GROUP BY b.student_id) AS avg_debts)', 'correct': True, 'reasoning': "The SQL query correctly identifies programs and courses associated with students who have the highest debts. It aggregates the total debt per program and course combination and filters those with a total debt greater than the average debt across all students. This aligns with the question's requirement to find specific programs or courses where students owe the most fees.", 'feedback': 'The query is well-structured and effectively answers the question. However, it could be improved by explicitly stating the institution in the WHERE clause if the schema supports filtering by institution_id, to ensure the results are specific to the institution in question.'}
2025-08-09 06:47:50,769 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:50,769 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:47:50,777 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or a comprehensive view of students' financial experiences, making it impossible to answer the question fully.", 'feedback': ''}
2025-08-09 06:47:50,777 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:50,777 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:50,777 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial experience?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial experience. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors or the overall financial experience of students. The schema lacks specific data on the reasons behind credit balances or a comprehensive view of students' financial experiences, making it impossible to answer the question fully.", 'feedback': ''}
2025-08-09 06:47:51,039 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:51,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:51,139 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded fee debt data for this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded fee debt data for an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of fee debt data. The schema includes tables related to financial transactions, student billing, and institutional data, but it does not provide insights into the reasons behind missing data or the context of fee debts. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:47:51,139 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:51,139 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:51,139 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded fee debt data for this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded fee debt data for an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of fee debt data. The schema includes tables related to financial transactions, student billing, and institutional data, but it does not provide insights into the reasons behind missing data or the context of fee debts. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:47:51,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:52,504 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:53,935 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:54,647 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:55,574 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:56,188 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:56,233 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:56,251 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to such a significant percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to student debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons behind student debt. Factors contributing to debt could include tuition fees, living expenses, financial aid availability, and student financial behavior, none of which are explicitly detailed in the schema. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:47:56,251 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:56,251 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:56,251 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to such a significant percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to student debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons behind student debt. Factors contributing to debt could include tuition fees, living expenses, financial aid availability, and student financial behavior, none of which are explicitly detailed in the schema. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:47:57,247 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:58,172 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:58,187 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high levels of fees owed by students at this institution, and how do these factors compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high levels of fees owed by students at an institution and a comparison with other institutions. The schema provides data on students, fees, and institutions, but it does not contain qualitative data or insights into the reasons behind the fees owed. Additionally, there is no direct way to compare these factors across institutions based on the provided schema. The schema lacks the necessary context or analytical data to answer such a subjective and comparative question.', 'feedback': ''}
2025-08-09 06:47:58,187 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:47:58,187 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:47:58,187 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high levels of fees owed by students at this institution, and how do these factors compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high levels of fees owed by students at an institution and a comparison with other institutions. The schema provides data on students, fees, and institutions, but it does not contain qualitative data or insights into the reasons behind the fees owed. Additionally, there is no direct way to compare these factors across institutions based on the provided schema. The schema lacks the necessary context or analytical data to answer such a subjective and comparative question.', 'feedback': ''}
2025-08-09 06:47:58,674 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:59,567 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:59,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:47:59,977 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:00,004 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded fee debt data for this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded fee debt data for an institution. However, the provided schema does not contain any specific tables or fields that directly relate to the reasons or factors behind the absence of fee debt data. The schema includes various tables related to institutions, students, financial transactions, and billing, but it does not provide insights into the reasons for missing data or the context behind fee debts. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:48:00,004 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:00,004 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:00,004 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded fee debt data for this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded fee debt data for an institution. However, the provided schema does not contain any specific tables or fields that directly relate to the reasons or factors behind the absence of fee debt data. The schema includes various tables related to institutions, students, financial transactions, and billing, but it does not provide insights into the reasons for missing data or the context behind fee debts. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:48:00,696 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:02,129 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:02,140 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the overall debt levels of students at the institution, even if specific programs do not show higher debts?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to overall debt levels of students at the institution, which is a qualitative assessment rather than a quantitative one. The schema provides data on students, their programs, financial transactions, and billing information, but it does not include specific factors or qualitative insights that would explain the reasons behind debt levels. Additionally, the schema lacks any direct indicators or metrics that could be used to analyze or correlate various factors affecting student debt.', 'feedback': ''}
2025-08-09 06:48:02,140 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:02,140 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:02,140 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the overall debt levels of students at the institution, even if specific programs do not show higher debts?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to overall debt levels of students at the institution, which is a qualitative assessment rather than a quantitative one. The schema provides data on students, their programs, financial transactions, and billing information, but it does not include specific factors or qualitative insights that would explain the reasons behind debt levels. Additionally, the schema lacks any direct indicators or metrics that could be used to analyze or correlate various factors affecting student debt.', 'feedback': ''}
2025-08-09 06:48:02,238 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:02,254 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to such a significant percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to student debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons or factors behind student debt. The schema lacks qualitative data or specific metrics that would allow for a comprehensive analysis of the causes of student debt, such as economic factors, student financial behavior, or institutional policies. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-09 06:48:02,254 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:02,254 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:02,254 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to such a significant percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to student debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons or factors behind student debt. The schema lacks qualitative data or specific metrics that would allow for a comprehensive analysis of the causes of student debt, such as economic factors, student financial behavior, or institutional policies. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-09 06:48:03,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:03,265 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high levels of fees owed by students at this institution, and how do these factors compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high levels of fees owed by students at an institution and a comparison with other institutions. The schema provides data on students, fees, and institutions, but it does not contain qualitative data or insights into the reasons behind the fee levels. Additionally, there is no direct way to compare these factors across institutions as the schema lacks specific attributes or tables that would provide insights into the reasons for fee accumulation or comparisons between institutions. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 06:48:03,266 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:03,266 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:03,266 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high levels of fees owed by students at this institution, and how do these factors compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high levels of fees owed by students at an institution and a comparison with other institutions. The schema provides data on students, fees, and institutions, but it does not contain qualitative data or insights into the reasons behind the fee levels. Additionally, there is no direct way to compare these factors across institutions as the schema lacks specific attributes or tables that would provide insights into the reasons for fee accumulation or comparisons between institutions. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 06:48:04,427 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:04,495 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:04,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:05,610 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:06,679 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:06,694 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to such a significant percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a significant percentage of students being in debt at the institution. However, the provided schema does not contain specific data or metrics related to student debt or the factors influencing it. While there are tables related to student transactions, bills, and financial aid, there is no direct correlation or analysis available in the schema that would allow for a comprehensive answer to the question. The schema lacks qualitative data or insights into the reasons behind student debt.', 'feedback': ''}
2025-08-09 06:48:06,695 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:06,695 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:06,695 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to such a significant percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a significant percentage of students being in debt at the institution. However, the provided schema does not contain specific data or metrics related to student debt or the factors influencing it. While there are tables related to student transactions, bills, and financial aid, there is no direct correlation or analysis available in the schema that would allow for a comprehensive answer to the question. The schema lacks qualitative data or insights into the reasons behind student debt.', 'feedback': ''}
2025-08-09 06:48:06,856 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:06,871 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the overall debt levels of students at the institution, even if specific programs do not show higher debts?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to overall debt levels of students at the institution, which is a qualitative assessment rather than a straightforward query that can be answered with the existing schema. The schema contains various tables related to students, their financial transactions, and programs, but it does not provide direct insights into the qualitative factors influencing debt levels. To answer this question, one would need to analyze data trends, student demographics, financial aid, and other contextual factors that are not explicitly captured in the schema.', 'feedback': ''}
2025-08-09 06:48:06,871 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:06,871 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:06,871 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the overall debt levels of students at the institution, even if specific programs do not show higher debts?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to overall debt levels of students at the institution, which is a qualitative assessment rather than a straightforward query that can be answered with the existing schema. The schema contains various tables related to students, their financial transactions, and programs, but it does not provide direct insights into the qualitative factors influencing debt levels. To answer this question, one would need to analyze data trends, student demographics, financial aid, and other contextual factors that are not explicitly captured in the schema.', 'feedback': ''}
2025-08-09 06:48:07,965 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:07,981 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high levels of fees owed by students at this institution, and how do these factors compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high levels of fees owed by students at an institution and a comparison with other institutions. The schema provides data on students, fees, and institutions, but it does not contain qualitative data or insights into the reasons behind the fees owed. Additionally, there is no direct way to compare these factors across institutions as the schema lacks specific attributes or tables that would provide such comparative analysis. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 06:48:07,981 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:07,981 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:07,981 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high levels of fees owed by students at this institution, and how do these factors compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high levels of fees owed by students at an institution and a comparison with other institutions. The schema provides data on students, fees, and institutions, but it does not contain qualitative data or insights into the reasons behind the fees owed. Additionally, there is no direct way to compare these factors across institutions as the schema lacks specific attributes or tables that would provide such comparative analysis. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 06:48:09,296 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:09,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:10,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:11,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:11,618 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:11,634 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the overall debt levels of students at the institution, even if specific programs do not show higher debts?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to overall debt levels of students at the institution, which is a qualitative assessment rather than a quantitative one. The schema provides data on students, their financial transactions, and various related entities, but it does not contain specific factors or qualitative insights that would allow for a comprehensive analysis of debt levels. Additionally, the schema lacks any direct indicators or metrics that could be used to evaluate the reasons behind debt levels, making it impossible to answer the question as posed.', 'feedback': ''}
2025-08-09 06:48:11,634 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:11,635 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:11,635 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the overall debt levels of students at the institution, even if specific programs do not show higher debts?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to overall debt levels of students at the institution, which is a qualitative assessment rather than a quantitative one. The schema provides data on students, their financial transactions, and various related entities, but it does not contain specific factors or qualitative insights that would allow for a comprehensive analysis of debt levels. Additionally, the schema lacks any direct indicators or metrics that could be used to evaluate the reasons behind debt levels, making it impossible to answer the question as posed.', 'feedback': ''}
2025-08-09 06:48:11,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:11,814 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to such a significant percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a significant percentage of students being in debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons or factors behind student debt. The schema lacks specific attributes or tables that would allow for a comprehensive analysis of the causes of student debt, such as socioeconomic factors, student financial aid details, or detailed financial behavior patterns. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-09 06:48:11,814 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:11,814 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:11,814 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to such a significant percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a significant percentage of students being in debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons or factors behind student debt. The schema lacks specific attributes or tables that would allow for a comprehensive analysis of the causes of student debt, such as socioeconomic factors, student financial aid details, or detailed financial behavior patterns. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-09 06:48:12,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:12,724 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the high levels of fees owed by students at this institution, and how do these factors compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high levels of fees owed by students at an institution and a comparison with other institutions. The schema provides data on students, fees, and institutions, but it does not contain qualitative data or insights into the reasons behind the fees owed. Additionally, there is no direct way to compare these factors across institutions as the schema lacks specific attributes or tables that would provide such comparative analysis. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 06:48:12,724 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:12,724 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:12,724 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the high levels of fees owed by students at this institution, and how do these factors compare to other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high levels of fees owed by students at an institution and a comparison with other institutions. The schema provides data on students, fees, and institutions, but it does not contain qualitative data or insights into the reasons behind the fees owed. Additionally, there is no direct way to compare these factors across institutions as the schema lacks specific attributes or tables that would provide such comparative analysis. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 06:48:14,158 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:14,210 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:16,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:16,494 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the overall debt levels of students at the institution, even if specific programs do not show higher debts?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to overall debt levels of students at the institution, which is a qualitative assessment rather than a quantitative one. The schema provides data on students, their financial transactions, and various related entities, but it does not contain specific factors or qualitative insights that would allow for a comprehensive answer to this question. The schema lacks information on external factors, student behavior, or institutional policies that might influence debt levels.', 'feedback': ''}
2025-08-09 06:48:16,495 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:16,495 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:16,495 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the overall debt levels of students at the institution, even if specific programs do not show higher debts?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to overall debt levels of students at the institution, which is a qualitative assessment rather than a quantitative one. The schema provides data on students, their financial transactions, and various related entities, but it does not contain specific factors or qualitative insights that would allow for a comprehensive answer to this question. The schema lacks information on external factors, student behavior, or institutional policies that might influence debt levels.', 'feedback': ''}
2025-08-09 06:48:16,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:18,201 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:22,875 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:30,186 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:30,191 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What payment plans or financial aid options are available to students at the institution where students owe the most fees?', 'sql': 'WITH InstitutionFees AS (  SELECT institution_id, SUM(balance) AS total_fees  FROM student_balances  GROUP BY institution_id), MaxInstitution AS (  SELECT institution_id  FROM InstitutionFees  ORDER BY total_fees DESC  LIMIT 1) SELECT DISTINCT far.description, far.type, far.status  FROM financial_aid_requests far  WHERE far.institution_id = (SELECT institution_id FROM MaxInstitution);', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total fees owed by students by summing the balances in the 'student_balances' table and grouping by 'institution_id'. It then selects the institution with the maximum total fees. Finally, it retrieves distinct descriptions, types, and statuses of financial aid requests from the 'financial_aid_requests' table for that institution. This aligns with the question's requirement to find available financial aid options at the institution with the highest fees.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by including additional details about the financial aid options, such as the amount or any other relevant fields that might provide more context to the available options.'}
2025-08-09 06:48:30,191 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:30,191 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:48:32,746 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:33,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:35,101 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:37,332 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:40,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:40,105 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons for the absence of financial support programs at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of financial support programs at an institution and the impact on student enrollment and retention. However, the provided schema does not contain any specific information regarding the reasons for the absence of financial support programs or any direct metrics related to student enrollment and retention. While there are tables related to financial aid requests and student transactions, they do not provide insights into the absence of programs or the reasons behind it. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:48:40,105 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:40,105 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:40,105 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons for the absence of financial support programs at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of financial support programs at an institution and the impact on student enrollment and retention. However, the provided schema does not contain any specific information regarding the reasons for the absence of financial support programs or any direct metrics related to student enrollment and retention. While there are tables related to financial aid requests and student transactions, they do not provide insights into the absence of programs or the reasons behind it. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:48:42,522 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:45,105 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:45,120 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons for the absence of financial support programs at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of financial support programs at an institution and the impact of this absence on student enrollment and retention. The provided schema does not contain any direct information regarding the reasons for the absence of financial support programs, nor does it provide data on student enrollment and retention metrics. While there are tables related to financial aid requests and student transactions, they do not explicitly address the reasons for the absence of such programs or their effects on enrollment and retention. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:48:45,120 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:45,120 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:45,121 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons for the absence of financial support programs at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of financial support programs at an institution and the impact of this absence on student enrollment and retention. The provided schema does not contain any direct information regarding the reasons for the absence of financial support programs, nor does it provide data on student enrollment and retention metrics. While there are tables related to financial aid requests and student transactions, they do not explicitly address the reasons for the absence of such programs or their effects on enrollment and retention. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:48:48,096 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:50,665 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:50,681 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons for the absence of financial support programs at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of financial support programs at an institution and the impact of this absence on student enrollment and retention. However, the provided schema does not contain any specific information regarding the reasons for the absence of financial support programs or any direct correlation between such programs and student enrollment or retention. The schema includes tables related to financial aid requests, student transactions, and student programs, but it lacks qualitative data or insights into the reasons behind the absence of financial support programs or their effects on student behavior.', 'feedback': ''}
2025-08-09 06:48:50,681 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:50,682 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:50,682 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons for the absence of financial support programs at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of financial support programs at an institution and the impact of this absence on student enrollment and retention. However, the provided schema does not contain any specific information regarding the reasons for the absence of financial support programs or any direct correlation between such programs and student enrollment or retention. The schema includes tables related to financial aid requests, student transactions, and student programs, but it lacks qualitative data or insights into the reasons behind the absence of financial support programs or their effects on student behavior.', 'feedback': ''}
2025-08-09 06:48:53,036 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:55,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:48:55,815 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons for the absence of financial support programs at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of financial support programs at an institution and the impact of this absence on student enrollment and retention. However, the provided schema does not contain any specific information regarding the reasons for the absence of financial support programs or any direct metrics related to student enrollment and retention. While there are tables related to financial aid requests and student transactions, they do not provide insights into the reasons for the absence of such programs or their effects on enrollment and retention. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:48:55,815 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:48:55,815 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:48:55,815 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons for the absence of financial support programs at this institution, and how might this impact student enrollment and retention?', 'answerable': False, 'reasoning': 'The question asks for potential reasons for the absence of financial support programs at an institution and the impact of this absence on student enrollment and retention. However, the provided schema does not contain any specific information regarding the reasons for the absence of financial support programs or any direct metrics related to student enrollment and retention. While there are tables related to financial aid requests and student transactions, they do not provide insights into the reasons for the absence of such programs or their effects on enrollment and retention. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:48:55,816 - root - INFO - [{'institution_id': 1, 'total_fees_owed': -2600.0}]
2025-08-09 06:48:55,816 - root - INFO - [{'institution_name': 'Central University', 'total_debt': -2600.0, 'average_debt': -41011563.506}]
2025-08-09 06:48:55,816 - root - INFO - [{'average_fee_debt': -1300.0}]
2025-08-09 06:48:55,816 - root - INFO - [{'percentage_in_debt': 24.33}]
2025-08-09 06:48:55,816 - root - INFO - [{'transaction_description': 'Bill For 2022/2023 Academic Year', 'total_amount': 60514748.0}, {'transaction_description': 'Transaction Reversal For Abrafi Cynthia ', 'total_amount': 1500.0}, {'transaction_description': 'Transaction Reversal For Opoku Jenniford ', 'total_amount': 200.0}, {'transaction_description': 'Transaction Reversal For Boamah Alberta Minta', 'total_amount': 200.0}, {'transaction_description': 'Transaction Reversal For Obour Yeboah Augustina ', 'total_amount': 200.0}, {'transaction_description': 'Transaction Reversal For Owusu Ansah Francisca ', 'total_amount': 200.0}, {'transaction_description': 'Bill For 2022/2023 Academic Year (Regular)', 'total_amount': 55157645.0}, {'transaction_description': 'Transaction Reversal For Kwarteng Evans Kofi', 'total_amount': 1500.0}, {'transaction_description': 'Closing Balance For 2021/2022 Academic Year', 'total_amount': 1458495.99}, {'transaction_description': 'Adhoc Bill For Practical/wel', 'total_amount': 450.0}, {'transaction_description': 'Reversal Of Adhoc Bill For Practical/wel', 'total_amount': -450.0}, {'transaction_description': 'Transaction Reversal For Umu-Salma Mohammed', 'total_amount': 2200.0}, {'transaction_description': 'Transaction Reversal For Asangalisah Reginald ', 'total_amount': 15015.0}, {'transaction_description': 'Transaction Reversal For Asumang Leticia ', 'total_amount': 10900.0}, {'transaction_description': 'Transaction Reversal For Adoma Christiana Osei', 'total_amount': 1550.0}, {'transaction_description': 'Reversal Of Bill For 2022/2023 Academic Year (Regular)', 'total_amount': -2036579.0}, {'transaction_description': 'Transaction Reversal For Sarfo Sesmond Addo', 'total_amount': 1500.0}, {'transaction_description': 'Transaction Reversal For Abdullah Sharehan ', 'total_amount': 4000.0}, {'transaction_description': 'Transaction Reversal For Yakubu Mufida', 'total_amount': 1274.0}, {'transaction_description': 'Transaction Reversal For Frimpong Patrick', 'total_amount': 100.0}, {'transaction_description': 'Transaction Reversal For Gana Albert Dortaa', 'total_amount': 1724.0}, {'transaction_description': 'Reversal Of Bill For 2022/2023 Academic Year', 'total_amount': -447877.0}, {'transaction_description': 'Transaction Reversal For Duah Evans', 'total_amount': 14000.0}, {'transaction_description': 'Transaction Reversal For Owusu Charles', 'total_amount': 1500.0}, {'transaction_description': 'Transaction Reversal For Sulley Razak', 'total_amount': 1274.0}, {'transaction_description': 'Transaction Reversal For Fieve Benjamin Destiny', 'total_amount': 1274.0}, {'transaction_description': 'Transaction Reversal For Amaniwaa Ellen  Amponsah', 'total_amount': 683.0}, {'transaction_description': 'Transaction Reversal For Kuffour Jazaa Abdullah', 'total_amount': 1300.0}, {'transaction_description': 'Transaction Reversal For Nlala John ', 'total_amount': 1000.0}, {'transaction_description': 'Transaction Reversal For Fosu Nicholas ', 'total_amount': 1500.0}, {'transaction_description': 'Transaction Reversal For Asare Eugene Agyei', 'total_amount': 7774.0}, {'transaction_description': 'Transaction Reversal For Avorkliya  Gladyssarpong ', 'total_amount': 3000.0}, {'transaction_description': 'Transaction Reversal For Obese Ophelia', 'total_amount': 100.0}, {'transaction_description': 'Transaction Reversal For Ngmalbini Tabila Emmanuel', 'total_amount': 3904.0}, {'transaction_description': 'Transaction Reversal For Mornaah Asana', 'total_amount': 11800.0}, {'transaction_description': 'Transaction Reversal For Gaisie Isaac', 'total_amount': 1869.0}, {'transaction_description': 'Transaction Reversal For Issah Mariama', 'total_amount': 900.0}, {'transaction_description': 'Transaction Reversal For Gyan Felix Owusu', 'total_amount': 1724.0}, {'transaction_description': 'Transaction Reversal For Benyarko Douglas', 'total_amount': 100.0}, {'transaction_description': 'Transaction Reversal For Ayamga Ezekiel Awine', 'total_amount': 300.0}, {'transaction_description': 'Transaction Reversal For Otchere David Akuetteh', 'total_amount': 1500.0}, {'transaction_description': 'Transaction Reversal For Agampim Christiana', 'total_amount': 800.0}, {'transaction_description': 'Transaction Reversal For Afrah Felicity', 'total_amount': 1300.0}, {'transaction_description': 'Refund Payment Of School Fee For 2022/2023 Academic Year (Regular)-[Aug]', 'total_amount': 1500.0}, {'transaction_description': 'Adhoc Bill For Examination Malpractice Fine', 'total_amount': 12000.0}, {'transaction_description': 'Refund Payment Of School Fee For 2024/2025 Academic Year-[Aug]', 'total_amount': 2028.0}, {'transaction_description': 'Payment Transfer To Mr. Akudago Enoch Atiewin (5221040089)', 'total_amount': 0.0}, {'transaction_description': 'Bill For 2023/2024 Academic Year Sem 1', 'total_amount': 2948.88}, {'transaction_description': 'Adhoc Bill For Damaged Projector', 'total_amount': 8000.0}, {'transaction_description': 'Payment Transfer To Mr. White Christopher (3155914941)', 'total_amount': 0.0}, {'transaction_description': 'Bill For Third Test Cycle', 'total_amount': 12100.0}, {'transaction_description': 'Graduation Bill For Jul2022 Cohort', 'total_amount': 123560.0}, {'transaction_description': 'Bill For 2024/2025 Academic Year', 'total_amount': 1175220.0}, {'transaction_description': 'Reversal Of Bill For 2024/2025 Academic Year', 'total_amount': -482210.0}, {'transaction_description': 'Reversal Of Reversal Of Bill For 2024/2025 Academic Year', 'total_amount': -1890.0}, {'transaction_description': 'Payment Transfer To Mr. Okai Aaron Nii Aryeetey (5211340009)', 'total_amount': 200.0}, {'transaction_description': 'Bill For Transflow Sip Merger Bill 2025/2026', 'total_amount': 200.0}, {'transaction_description': 'Adhoc Bill For Cost Of Chair Repairs', 'total_amount': 1000.0}, {'transaction_description': 'Payment Transfer To Mr. Acheampong Yaw  (5191940013)', 'total_amount': 500.0}, {'transaction_description': 'Adhoc Bill For Cost Of Replacing Broken Chair', 'total_amount': 100.0}, {'transaction_description': 'Payment Transfer To Mr. Osei Benjamin (5181940150)', 'total_amount': 800.0}, {'transaction_description': 'Bill For Billing Period Management Apr 2025/2026', 'total_amount': 210.0}, {'transaction_description': 'Graduation Bill For Nov2029 Cohort', 'total_amount': 440.0}, {'transaction_description': 'Bill For Aug Bill', 'total_amount': 1050.0}, {'transaction_description': 'Bill For Program Duration-Based Doc Computation', 'total_amount': 4000.0}]
2025-08-09 06:48:55,817 - root - INFO - 'No results'
2025-08-09 06:48:55,817 - root - INFO - 'No results'
2025-08-09 06:48:55,817 - root - INFO - 'No results'
2025-08-09 06:48:55,817 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-09 06:49:04,318 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:49:04,323 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-09 06:49:22,594 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:49:22,600 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:49:22,601 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:49:22,601 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:49:22,601 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total amount of fees owed by students at the institution where students owe the most fee...
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total amount of fees owed by students at the institution where students owe the most fees is -$2...
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_id': 1, 'total_fees_owed': -2600.0}]...
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_fees_owed_by_top_institution
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 06:49:22,601 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 06:49:22,601 - celery.redirected - WARNING - [{'institution_id': 1, 'total_fees_owed': -2600.0}]
2025-08-09 06:49:22,601 - celery.redirected - WARNING - ================================= 
2025-08-09 06:49:22,601 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 06:49:22,602 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:49:22,602 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:49:22,602 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:49:22,602 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the fee debt at the institution where students owe the most fees compare to other instituti...
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At Central University, the total fee debt amounts to -$2,600, with an average debt per student of ap...
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_name': 'Central University', 'total_debt': -2600.0, 'average_debt': -41011563.506}]...
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: central_university_fee_debt_comparison
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 06:49:22,602 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 06:49:22,602 - celery.redirected - WARNING - [{'institution_name': 'Central University', 'total_debt': -2600.0, 'average_debt': -41011563.506}]
2025-08-09 06:49:22,602 - celery.redirected - WARNING - ================================= 
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 06:49:22,602 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:49:22,602 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:49:22,602 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:49:22,602 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:49:22,602 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the average fee debt per student at the institution where students owe the most fees?...
2025-08-09 06:49:22,603 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The average fee debt per student at the institution where students owe the most fees is -$1300. This...
2025-08-09 06:49:22,603 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:49:22,603 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'average_fee_debt': -1300.0}]...
2025-08-09 06:49:22,603 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_fee_debt_at_top_institution
2025-08-09 06:49:22,603 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 06:49:22,603 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 06:49:22,603 - celery.redirected - WARNING - [{'average_fee_debt': -1300.0}]
2025-08-09 06:49:22,603 - celery.redirected - WARNING - ================================= 
2025-08-09 06:49:22,603 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 06:49:22,603 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:49:22,603 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:49:22,603 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:49:22,603 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:49:22,603 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:49:22,603 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:49:22,603 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific programs or courses at the institution where students owe the most fees that have...
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there are currently no specific programs or cours...
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: programs_with_highest_student_debts
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 06:49:22,604 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:49:22,604 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:49:22,604 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:49:22,604 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What percentage of students at the institution where students owe the most fees are currently in deb...
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At the institution where students owe the most fees, approximately 24.33% of students are currently ...
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'percentage_in_debt': 24.33}]...
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: percentage_of_students_in_debt_at_top_fee_institution
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 06:49:22,604 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 06:49:22,604 - celery.redirected - WARNING - [{'percentage_in_debt': 24.33}]
2025-08-09 06:49:22,604 - celery.redirected - WARNING - ================================= 
2025-08-09 06:49:22,604 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 06:49:22,605 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:49:22,605 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:49:22,605 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:49:22,605 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How has the fee debt at the institution where students owe the most fees changed over the past few y...
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, indicating that there may not be any recorded fee debt dat...
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: fee_debt_trends_by_institution
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 06:49:22,605 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:49:22,605 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:49:22,605 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:49:22,605 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:49:22,605 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What payment plans or financial aid options are available to students at the institution where stude...
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are currently no available payment plans or financial aid options for students...
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: financial_aid_options_for_highest_fee_institution
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 06:49:22,606 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:49:22,606 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:49:22,606 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:49:22,606 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the breakdown of the types of fees owed by students at the institution where students owe th...
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The breakdown of the types of fees owed by students at the institution where students owe the most f...
2025-08-09 06:49:22,606 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-09 06:49:22,607 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'transaction_description': 'Bill For 2022/2023 Academic Year', 'total_amount': 60514748.0}, {'tran...
2025-08-09 06:49:22,607 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_fee_breakdown_by_type
2025-08-09 06:49:22,607 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 06:49:22,607 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 06:49:22,607 - celery.redirected - WARNING - [{'transaction_description': 'Bill For 2022/2023 Academic Year', 'total_amount': 60514748.0}, {'transaction_description': 'Transaction Reversal For Abrafi Cynthia ', 'total_amount': 1500.0}, {'transaction_description': 'Transaction Reversal For Opoku Jenniford ', 'total_amount': 200.0}, {'transaction_description': 'Transaction Reversal For Boamah Alberta Minta', 'total_amount': 200.0}, {'transaction_description': 'Transaction Reversal For Obour Yeboah Augustina ', 'total_amount': 200.0}, {'transaction_description': 'Transaction Reversal For Owusu Ansah Francisca ', 'total_amount': 200.0}, {'transaction_description': 'Bill For 2022/2023 Academic Year (Regular)', 'total_amount': 55157645.0}, {'transaction_description': 'Transaction Reversal For Kwarteng Evans Kofi', 'total_amount': 1500.0}, {'transaction_description': 'Closing Balance For 2021/2022 Academic Year', 'total_amount': 1458495.99}, {'transaction_description': 'Adhoc Bill For Practical/wel', 'total_amount': 450.0}, {'transaction_description': 'Reversal Of Adhoc Bill For Practical/wel', 'total_amount': -450.0}, {'transaction_description': 'Transaction Reversal For Umu-Salma Mohammed', 'total_amount': 2200.0}, {'transaction_description': 'Transaction Reversal For Asangalisah Reginald ', 'total_amount': 15015.0}, {'transaction_description': 'Transaction Reversal For Asumang Leticia ', 'total_amount': 10900.0}, {'transaction_description': 'Transaction Reversal For Adoma Christiana Osei', 'total_amount': 1550.0}, {'transaction_description': 'Reversal Of Bill For 2022/2023 Academic Year (Regular)', 'total_amount': -2036579.0}, {'transaction_description': 'Transaction Reversal For Sarfo Sesmond Addo', 'total_amount': 1500.0}, {'transaction_description': 'Transaction Reversal For Abdullah Sharehan ', 'total_amount': 4000.0}, {'transaction_description': 'Transaction Reversal For Yakubu Mufida', 'total_amount': 1274.0}, {'transaction_description': 'Transaction Reversal For Frimpong Patrick', 'total_amount': 100.0}, {'transaction_description': 'Transaction Reversal For Gana Albert Dortaa', 'total_amount': 1724.0}, {'transaction_description': 'Reversal Of Bill For 2022/2023 Academic Year', 'total_amount': -447877.0}, {'transaction_description': 'Transaction Reversal For Duah Evans', 'total_amount': 14000.0}, {'transaction_description': 'Transaction Reversal For Owusu Charles', 'total_amount': 1500.0}, {'transaction_description': 'Transaction Reversal For Sulley Razak', 'total_amount': 1274.0}, {'transaction_description': 'Transaction Reversal For Fieve Benjamin Destiny', 'total_amount': 1274.0}, {'transaction_description': 'Transaction Reversal For Amaniwaa Ellen  Amponsah', 'total_amount': 683.0}, {'transaction_description': 'Transaction Reversal For Kuffour Jazaa Abdullah', 'total_amount': 1300.0}, {'transaction_description': 'Transaction Reversal For Nlala John ', 'total_amount': 1000.0}, {'transaction_description': 'Transaction Reversal For Fosu Nicholas ', 'total_amount': 1500.0}, {'transaction_description': 'Transaction Reversal For Asare Eugene Agyei', 'total_amount': 7774.0}, {'transaction_description': 'Transaction Reversal For Avorkliya  Gladyssarpong ', 'total_amount': 3000.0}, {'transaction_description': 'Transaction Reversal For Obese Ophelia', 'total_amount': 100.0}, {'transaction_description': 'Transaction Reversal For Ngmalbini Tabila Emmanuel', 'total_amount': 3904.0}, {'transaction_description': 'Transaction Reversal For Mornaah Asana', 'total_amount': 11800.0}, {'transaction_description': 'Transaction Reversal For Gaisie Isaac', 'total_amount': 1869.0}, {'transaction_description': 'Transaction Reversal For Issah Mariama', 'total_amount': 900.0}, {'transaction_description': 'Transaction Reversal For Gyan Felix Owusu', 'total_amount': 1724.0}, {'transaction_description': 'Transaction Reversal For Benyarko Douglas', 'total_amount': 100.0}, {'transaction_description': 'Transaction Reversal For Ayamga Ezekiel Awine', 'total_amount': 300.0}, {'transaction_description': 'Transaction Reversal For Otchere David Akuetteh', 'total_amount': 1500.0}, {'transaction_description': 'Transaction Reversal For Agampim Christiana', 'total_amount': 800.0}, {'transaction_description': 'Transaction Reversal For Afrah Felicity', 'total_amount': 1300.0}, {'transaction_description': 'Refund Payment Of School Fee For 2022/2023 Academic Year (Regular)-[Aug]', 'total_amount': 1500.0}, {'transaction_description': 'Adhoc Bill For Examination Malpractice Fine', 'total_amount': 12000.0}, {'transaction_description': 'Refund Payment Of School Fee For 2024/2025 Academic Year-[Aug]', 'total_amount': 2028.0}, {'transaction_description': 'Payment Transfer To Mr. Akudago Enoch Atiewin (5221040089)', 'total_amount': 0.0}, {'transaction_description': 'Bill For 2023/2024 Academic Year Sem 1', 'total_amount': 2948.88}, {'transaction_description': 'Adhoc Bill For Damaged Projector', 'total_amount': 8000.0}, {'transaction_description': 'Payment Transfer To Mr. White Christopher (3155914941)', 'total_amount': 0.0}, {'transaction_description': 'Bill For Third Test Cycle', 'total_amount': 12100.0}, {'transaction_description': 'Graduation Bill For Jul2022 Cohort', 'total_amount': 123560.0}, {'transaction_description': 'Bill For 2024/2025 Academic Year', 'total_amount': 1175220.0}, {'transaction_description': 'Reversal Of Bill For 2024/2025 Academic Year', 'total_amount': -482210.0}, {'transaction_description': 'Reversal Of Reversal Of Bill For 2024/2025 Academic Year', 'total_amount': -1890.0}, {'transaction_description': 'Payment Transfer To Mr. Okai Aaron Nii Aryeetey (5211340009)', 'total_amount': 200.0}, {'transaction_description': 'Bill For Transflow Sip Merger Bill 2025/2026', 'total_amount': 200.0}, {'transaction_description': 'Adhoc Bill For Cost Of Chair Repairs', 'total_amount': 1000.0}, {'transaction_description': 'Payment Transfer To Mr. Acheampong Yaw  (5191940013)', 'total_amount': 500.0}, {'transaction_description': 'Adhoc Bill For Cost Of Replacing Broken Chair', 'total_amount': 100.0}, {'transaction_description': 'Payment Transfer To Mr. Osei Benjamin (5181940150)', 'total_amount': 800.0}, {'transaction_description': 'Bill For Billing Period Management Apr 2025/2026', 'total_amount': 210.0}, {'transaction_description': 'Graduation Bill For Nov2029 Cohort', 'total_amount': 440.0}, {'transaction_description': 'Bill For Aug Bill', 'total_amount': 1050.0}, {'transaction_description': 'Bill For Program Duration-Based Doc Computation', 'total_amount': 4000.0}]
2025-08-09 06:49:22,607 - celery.redirected - WARNING - ================================= 
2025-08-09 06:49:22,607 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_fee_breakdown_by_type
2025-08-09 06:49:22,607 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 06:49:22,607 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 8
2025-08-09 06:49:22,608 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:49:22,608 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-09 06:49:22,608 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 8 documents
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Content: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:50.777977+00:00', 'data_returned': True}
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Content: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:40.230224+00:00', 'data_returned': True}
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Content: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Content: Question: Are there specific programs or courses at the institution where students owe the most fees...
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:48:16.495329+00:00', 'data_returned': False}
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Content: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:48:11.814901+00:00', 'data_returned': True}
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Content: Question: How has the fee debt at the institution where students owe the most fees changed over the ...
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:48:00.005040+00:00', 'data_returned': False}
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Content: Question: What payment plans or financial aid options are available to students at the institution w...
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:48:55.816043+00:00', 'data_returned': False}
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -   📄 Doc 8:
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Content: Question: What is the breakdown of the types of fees owed by students at the institution where stude...
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:48:12.724710+00:00', 'data_returned': True, 'data_tag': 'student_fee_breakdown_by_type'}
2025-08-09 06:49:22,608 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 5/8
2025-08-09 06:49:22,750 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:200 duration:0.141s]
2025-08-09 06:49:24,527 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 06:52:15,258 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: <class 'celery.concurrency.solo.TaskPool'> does not implement kill_job
2025-08-09 06:52:15,261 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:52:15,261 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION ERROR
2025-08-09 06:52:15,261 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:52:15,261 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - ❌ Streaming report generation failed: Error generating streaming report: <class 'celery.concurrency.solo.TaskPool'> does not implement kill_job
2025-08-09 06:52:15,261 - STREAMING_REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250809_064639.log
2025-08-09 06:52:15,267 - celery.app.trace - INFO - Task generate_streaming_report[23f56871-feff-4840-a4fb-7ee584fd861a] succeeded in 172.2262149590024s: {'error': 'Error generating streaming report: <class \'celery.concurrency.solo.TaskPool\'> does not implement kill_job'}

2025-08-10 17:55:48,827 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250810_175548.log
2025-08-10 17:55:48,827 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 17:55:48,827 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 75c6eab8-6397-4f4c-bec3-214716eb6c15
2025-08-10 17:55:48,827 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 17:55:48,827 - REPORT_REQUEST - INFO - 📝 Original Question: 'Give me a report on student population across all institutions.'
2025-08-10 17:55:48,827 - REPORT_REQUEST - INFO - 🆔 Task ID: 75c6eab8-6397-4f4c-bec3-214716eb6c15
2025-08-10 17:55:48,827 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-10T17:55:48.827934
2025-08-10 17:55:48,957 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-10 17:55:48,957 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 102
2025-08-10 17:55:49,082 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.124s]
2025-08-10 17:55:49,082 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 58
2025-08-10 17:55:49,215 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.132s]
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (24 docs)
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (13 docs)
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (11 docs)
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO -   - 'Which instituion has the most students?' (6 docs)
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC university?' (3 docs)
2025-08-10 17:55:49,215 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University' (1 docs)
2025-08-10 17:55:49,216 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 17:55:49,216 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-10 17:55:49,216 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 17:55:49,216 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: Give me a report on student population across all institutions.
2025-08-10 17:55:49,216 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-10 17:55:49,216 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-10 17:55:59,381 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:55:59,405 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-10 17:56:02,823 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:02,842 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-10 17:56:04,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:04,995 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:05,342 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:05,555 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:05,646 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:05,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:05,733 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:05,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:05,996 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:06,169 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:06,232 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-10 17:56:08,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:08,710 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:08,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:09,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:09,155 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:09,163 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:09,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:09,721 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:10,034 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:11,109 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:11,373 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:11,441 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:11,475 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:12,098 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:12,105 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:12,282 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:12,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:12,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:12,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:12,640 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:12,702 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:12,720 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:13,276 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:13,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:13,640 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:13,813 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:14,853 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:16,912 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:16,954 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:17,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:18,045 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:18,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:18,631 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:18,689 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:19,419 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:20,316 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:21,991 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:22,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:22,945 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:22,956 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the trend in student enrollment over the past few years for each institution?', 'sql': "SELECT i.name AS institution_name, ay.start_year, COUNT(s.id) AS enrollment_count\nFROM institutions i\nJOIN students s ON s.institution_id = i.id\nJOIN academic_years ay ON ay.institution_id = i.id\nWHERE ay.status = 'Active'\nGROUP BY i.name, ay.start_year\nORDER BY i.name, ay.start_year DESC\nLIMIT 20;", 'correct': True, 'reasoning': 'The SQL query correctly retrieves the trend in student enrollment by counting the number of students enrolled at each institution for each academic year. It joins the institutions, students, and academic_years tables appropriately, filters for active academic years, and groups the results by institution name and start year. The ordering and limiting of results are also appropriate for analyzing trends over the years.', 'feedback': 'The query is well-structured, but the LIMIT clause may restrict the results unnecessarily. If the goal is to see trends over multiple years, consider removing the LIMIT to capture all relevant data.'}
2025-08-10 17:56:22,956 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:22,957 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-10 17:56:22,983 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:22,996 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total number of students enrolled in each institution?', 'sql': 'SELECT institutions.name, COUNT(students.id) AS total_students\nFROM institutions\nLEFT JOIN students ON institutions.id = students.institution_id\nGROUP BY institutions.name\nLIMIT 20;', 'correct': True, 'reasoning': "The SQL query correctly selects the name of each institution and counts the number of students associated with each institution by joining the 'institutions' table with the 'students' table on the institution ID. The use of COUNT(students.id) ensures that it counts the number of students per institution. The GROUP BY clause groups the results by institution name, which is necessary for aggregating the student counts. The LEFT JOIN ensures that institutions with no students are still included in the results, which is appropriate for the question. The LIMIT 20 clause does not affect the correctness of the query but may limit the results to only 20 institutions, which could be a concern if there are more than 20 institutions.", 'feedback': 'Consider removing the LIMIT clause if you want to see the total number of students for all institutions, not just the first 20. Additionally, clarify if you want to include institutions with zero students in the final count, which the current query does handle correctly.'}
2025-08-10 17:56:22,996 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:22,996 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-10 17:56:23,460 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:24,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:24,275 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:24,750 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:25,047 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:25,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:26,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:26,018 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What percentage of the student population is international versus domestic at each institution?', 'sql': 'SELECT i.name AS institution_name, \n       SUM(CASE WHEN s.nationality_id IS NOT NULL THEN 1 ELSE 0 END) AS international_count, \n       SUM(CASE WHEN s.nationality_id IS NULL THEN 1 ELSE 0 END) AS domestic_count, \n       (SUM(CASE WHEN s.nationality_id IS NOT NULL THEN 1 ELSE 0 END) * 100.0 / COUNT(s.id)) AS international_percentage, \n       (SUM(CASE WHEN s.nationality_id IS NULL THEN 1 ELSE 0 END) * 100.0 / COUNT(s.id)) AS domestic_percentage \nFROM students s \nJOIN institutions i ON s.institution_id = i.id \nGROUP BY i.name \nLIMIT 20;', 'correct': True, 'reasoning': "The SQL query correctly calculates the percentage of international and domestic students at each institution. It uses a CASE statement to count international students (where nationality_id is not NULL) and domestic students (where nationality_id is NULL). It then computes the percentages based on these counts relative to the total number of students at each institution. The GROUP BY clause ensures that the results are aggregated by institution, which aligns with the question's requirement to report percentages at each institution.", 'feedback': 'The query is well-structured and answers the question accurately. However, the LIMIT clause may restrict the results to only 20 institutions, which could be problematic if there are more than 20 institutions in the database. Consider removing the LIMIT clause to ensure all institutions are included in the results.'}
2025-08-10 17:56:26,018 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:26,018 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-10 17:56:26,492 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:26,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:27,346 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:27,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:28,181 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:28,575 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:28,746 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:29,804 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:29,805 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:29,983 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:30,541 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:31,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:31,384 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:31,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:31,877 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:32,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:32,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:33,437 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:33,521 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:33,679 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:34,412 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:34,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:35,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:35,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:36,131 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:36,466 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:36,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:37,055 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:37,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:37,191 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:37,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:37,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:38,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:38,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:39,102 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:39,116 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment numbers among institutions. While the schema contains data about institutions and applicants, it does not provide specific metrics or factors that would allow for a comprehensive analysis of enrollment differences. Factors such as marketing strategies, local demographics, or institutional reputation are not represented in the schema. Therefore, the question cannot be answered directly or indirectly using the available data.', 'feedback': ''}
2025-08-10 17:56:39,116 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:39,116 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:39,117 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment numbers among institutions. While the schema contains data about institutions and applicants, it does not provide specific metrics or factors that would allow for a comprehensive analysis of enrollment differences. Factors such as marketing strategies, local demographics, or institutional reputation are not represented in the schema. Therefore, the question cannot be answered directly or indirectly using the available data.', 'feedback': ''}
2025-08-10 17:56:39,672 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:40,351 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:40,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:41,047 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:41,051 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the demographics of the student population, such as age, gender, and nationality, across all institutions?', 'sql': "SELECT s.institution_id, EXTRACT(YEAR FROM AGE(s.dob)) AS age, s.sex, n.name AS nationality\nFROM core.students s\nJOIN core.nationalities n ON s.nationality_id = n.id\nWHERE s.status = 'active'", 'correct': True, 'reasoning': "The SQL query retrieves the institution ID, calculates the age of students based on their date of birth, and selects their gender and nationality by joining the students table with the nationalities table. It also filters for students with an 'active' status, which is relevant for demographic analysis. This aligns well with the question's request for demographics such as age, gender, and nationality across all institutions.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by including a GROUP BY clause if the intention is to summarize the demographics by institution or by adding more demographic details if needed.'}
2025-08-10 17:56:41,051 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:41,051 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-10 17:56:41,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:41,524 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:42,280 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:42,284 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Can we break down the student population by program or course of study at each institution?', 'sql': 'SELECT p.long_name AS program_name, s.institution_id, COUNT(s.id) AS student_count \nFROM core.students s \nJOIN core.student_programs sp ON s.id = sp.student_id \nJOIN core.programs p ON sp.program_id = p.id \nGROUP BY p.long_name, s.institution_id \nORDER BY s.institution_id, student_count DESC;', 'correct': True, 'reasoning': "The SQL query accurately retrieves the breakdown of the student population by program (course of study) at each institution. It joins the 'students', 'student_programs', and 'programs' tables to gather the necessary data. The use of COUNT(s.id) provides the number of students per program at each institution, and the GROUP BY clause ensures that the results are aggregated correctly by program name and institution ID. The ORDER BY clause organizes the results by institution and student count, which aligns with the question's request for a breakdown.", 'feedback': 'The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to clarify if the question is interested in all programs or only active ones, which might require adding a condition to filter based on the program status.'}
2025-08-10 17:56:42,284 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:42,284 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-10 17:56:42,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:42,382 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in enrollment trends among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment trends among institutions. However, the provided schema does not contain specific data or metrics related to enrollment trends, such as historical enrollment numbers, demographic data of applicants, or any analytical data that could be used to assess trends. The schema primarily contains structural data about institutions, applicants, and various related entities, but lacks the necessary information to analyze enrollment trends.', 'feedback': ''}
2025-08-10 17:56:42,382 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:42,382 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:42,382 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in enrollment trends among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment trends among institutions. However, the provided schema does not contain specific data or metrics related to enrollment trends, such as historical enrollment numbers, demographic data of applicants, or any analytical data that could be used to assess trends. The schema primarily contains structural data about institutions, applicants, and various related entities, but lacks the necessary information to analyze enrollment trends.', 'feedback': ''}
2025-08-10 17:56:42,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:42,810 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:42,820 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the student population distribution differ between undergraduate and postgraduate programs across all institutions?', 'sql': "SELECT CASE \n            WHEN p.programme_type IN ('DEGREE', 'DIPLOMA') THEN 'Undergraduate' \n            WHEN p.programme_type IN ('MASTERS', 'Doctoral') THEN 'Postgraduate' \n            ELSE 'Other' \n        END AS program_level, \n        COUNT(s.id) AS student_count \nFROM students s \nJOIN programs p ON s.institution_id = p.institution_id \nGROUP BY program_level \nORDER BY student_count DESC;", 'correct': True, 'reasoning': "The SQL query correctly categorizes programs into 'Undergraduate' and 'Postgraduate' based on the 'programme_type' field from the 'programs' table. It counts the number of students associated with each program level by joining the 'students' and 'programs' tables on 'institution_id'. The grouping by 'program_level' and ordering by 'student_count' provides a clear distribution of the student population across the specified program types, which directly addresses the question.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly excluding the 'Other' category from the results if the focus is solely on undergraduate and postgraduate programs. Additionally, including a filter for active programs (if applicable) might provide more relevant insights."}
2025-08-10 17:56:42,820 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:42,820 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-10 17:56:43,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:43,695 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:43,716 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment numbers among institutions. While the schema contains data about institutions and applicants, it does not provide specific metrics or factors that would allow for a comprehensive analysis of enrollment differences. Factors such as marketing strategies, local demographics, or institutional reputation are not represented in the schema. Therefore, the question cannot be answered directly or indirectly using the available data.', 'feedback': ''}
2025-08-10 17:56:43,716 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:43,716 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:43,716 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment numbers among institutions. While the schema contains data about institutions and applicants, it does not provide specific metrics or factors that would allow for a comprehensive analysis of enrollment differences. Factors such as marketing strategies, local demographics, or institutional reputation are not represented in the schema. Therefore, the question cannot be answered directly or indirectly using the available data.', 'feedback': ''}
2025-08-10 17:56:44,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:44,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:44,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:44,835 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to these institutions attracting such a high percentage of international students, and how do they support their unique needs compared to domestic students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors that contribute to institutions attracting international students and how they support their unique needs compared to domestic students. The provided schema primarily contains structured data about institutions, students, programs, and various administrative aspects, but it lacks qualitative insights, such as policies, support services, or specific programs aimed at international students. Therefore, the schema does not provide the necessary information to answer the question.', 'feedback': ''}
2025-08-10 17:56:44,835 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:44,835 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:44,835 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to these institutions attracting such a high percentage of international students, and how do they support their unique needs compared to domestic students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors that contribute to institutions attracting international students and how they support their unique needs compared to domestic students. The provided schema primarily contains structured data about institutions, students, programs, and various administrative aspects, but it lacks qualitative insights, such as policies, support services, or specific programs aimed at international students. Therefore, the schema does not provide the necessary information to answer the question.', 'feedback': ''}
2025-08-10 17:56:45,109 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:45,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:45,630 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:45,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:46,275 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:46,514 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:46,534 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in enrollment trends among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment trends among institutions. While the schema contains various tables related to institutions, students, and programs, it does not provide specific data or metrics that would allow for a comprehensive analysis of enrollment trends. Factors such as demographic data, economic conditions, or institutional policies are not represented in the schema, making it impossible to answer the question directly or indirectly.', 'feedback': ''}
2025-08-10 17:56:46,534 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:46,534 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:46,534 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in enrollment trends among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment trends among institutions. While the schema contains various tables related to institutions, students, and programs, it does not provide specific data or metrics that would allow for a comprehensive analysis of enrollment trends. Factors such as demographic data, economic conditions, or institutional policies are not represented in the schema, making it impossible to answer the question directly or indirectly.', 'feedback': ''}
2025-08-10 17:56:46,817 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:47,037 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:47,929 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:47,936 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:47,948 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment numbers among institutions. However, the provided schema does not contain specific data or attributes that directly relate to enrollment numbers or the factors influencing them. While there are tables related to institutions and applicants, there is no clear way to derive or analyze the factors affecting enrollment from the schema alone. Additional context or data would be needed to answer this question.', 'feedback': ''}
2025-08-10 17:56:47,948 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:47,948 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:47,948 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment numbers among institutions. However, the provided schema does not contain specific data or attributes that directly relate to enrollment numbers or the factors influencing them. While there are tables related to institutions and applicants, there is no clear way to derive or analyze the factors affecting enrollment from the schema alone. Additional context or data would be needed to answer this question.', 'feedback': ''}
2025-08-10 17:56:48,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:49,054 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:49,070 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:49,088 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to these institutions attracting such a high percentage of international students, and how do they support their unique needs compared to domestic students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors that contribute to institutions attracting international students and how they support their unique needs compared to domestic students. The provided schema contains data about institutions, students, and various related entities, but it does not include qualitative insights, policies, or specific support mechanisms for international students. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:56:49,089 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:49,089 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:49,089 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to these institutions attracting such a high percentage of international students, and how do they support their unique needs compared to domestic students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors that contribute to institutions attracting international students and how they support their unique needs compared to domestic students. The provided schema contains data about institutions, students, and various related entities, but it does not include qualitative insights, policies, or specific support mechanisms for international students. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:56:49,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:49,150 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:49,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:50,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:51,068 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:51,084 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in enrollment trends among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment trends among institutions. While the schema contains various tables related to institutions, students, and programs, it does not provide specific data or metrics that would allow for a comprehensive analysis of enrollment trends. Factors such as demographic data, economic conditions, or institutional policies are not represented in the schema, making it impossible to answer the question directly or indirectly.', 'feedback': ''}
2025-08-10 17:56:51,084 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:51,084 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:51,084 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in enrollment trends among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment trends among institutions. While the schema contains various tables related to institutions, students, and programs, it does not provide specific data or metrics that would allow for a comprehensive analysis of enrollment trends. Factors such as demographic data, economic conditions, or institutional policies are not represented in the schema, making it impossible to answer the question directly or indirectly.', 'feedback': ''}
2025-08-10 17:56:51,160 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:52,061 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:52,089 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What challenges do you think institutions face in collecting and reporting demographic data on their student populations?', 'answerable': False, 'reasoning': 'The question asks about the challenges institutions face in collecting and reporting demographic data on their student populations. This is a qualitative question that requires insights, opinions, or experiences, which cannot be derived from the provided database schema. The schema contains structured data about institutions, students, and various related entities, but it does not include any information regarding the challenges or issues faced by institutions in data collection or reporting.', 'feedback': ''}
2025-08-10 17:56:52,089 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:52,089 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:52,089 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What challenges do you think institutions face in collecting and reporting demographic data on their student populations?', 'answerable': False, 'reasoning': 'The question asks about the challenges institutions face in collecting and reporting demographic data on their student populations. This is a qualitative question that requires insights, opinions, or experiences, which cannot be derived from the provided database schema. The schema contains structured data about institutions, students, and various related entities, but it does not include any information regarding the challenges or issues faced by institutions in data collection or reporting.', 'feedback': ''}
2025-08-10 17:56:52,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:52,345 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:52,361 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment numbers among institutions. While the schema contains data about institutions and applicants, it does not provide specific metrics or factors that would allow for a comprehensive analysis of enrollment differences. Factors such as marketing strategies, local demographics, or institutional reputation are not represented in the schema. Therefore, the question cannot be answered directly or indirectly using the available data.', 'feedback': ''}
2025-08-10 17:56:52,361 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:52,361 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:52,361 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the significant differences in enrollment numbers among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment numbers among institutions. While the schema contains data about institutions and applicants, it does not provide specific metrics or factors that would allow for a comprehensive analysis of enrollment differences. Factors such as marketing strategies, local demographics, or institutional reputation are not represented in the schema. Therefore, the question cannot be answered directly or indirectly using the available data.', 'feedback': ''}
2025-08-10 17:56:53,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:54,136 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:54,150 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to these institutions attracting such a high percentage of international students, and how do they support their unique needs compared to domestic students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors that contribute to institutions attracting international students and how they support their unique needs compared to domestic students. The provided schema contains data about institutions, students, and various related entities, but it does not include qualitative insights, policies, or specific support mechanisms for international students. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:56:54,150 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:54,150 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:54,150 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to these institutions attracting such a high percentage of international students, and how do they support their unique needs compared to domestic students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors that contribute to institutions attracting international students and how they support their unique needs compared to domestic students. The provided schema contains data about institutions, students, and various related entities, but it does not include qualitative insights, policies, or specific support mechanisms for international students. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:56:54,836 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:55,317 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:55,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:55,724 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in enrollment trends among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment trends among institutions. However, the provided schema does not contain specific data or metrics related to enrollment trends, such as historical enrollment numbers, demographic data of applicants, or any analytical data that could be used to assess trends. The schema primarily contains structural data about institutions, applicants, and various related entities, but lacks the necessary analytical context or historical data to answer the question.', 'feedback': ''}
2025-08-10 17:56:55,724 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:55,725 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:55,725 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the differences in enrollment trends among these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to differences in enrollment trends among institutions. However, the provided schema does not contain specific data or metrics related to enrollment trends, such as historical enrollment numbers, demographic data of applicants, or any analytical data that could be used to assess trends. The schema primarily contains structural data about institutions, applicants, and various related entities, but lacks the necessary analytical context or historical data to answer the question.', 'feedback': ''}
2025-08-10 17:56:56,408 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:56,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:56,574 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:57,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:58,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:58,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:58,681 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to these institutions attracting such a high percentage of international students, and how do they support their unique needs compared to domestic students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors that contribute to institutions attracting international students and how they support their unique needs compared to domestic students. The provided schema contains data about institutions, students, and various related entities, but it does not include qualitative insights, policies, or specific support mechanisms for international students. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:56:58,681 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:56:58,681 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:56:58,681 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to these institutions attracting such a high percentage of international students, and how do they support their unique needs compared to domestic students?', 'answerable': False, 'reasoning': 'The question asks for qualitative factors that contribute to institutions attracting international students and how they support their unique needs compared to domestic students. The provided schema contains data about institutions, students, and various related entities, but it does not include qualitative insights, policies, or specific support mechanisms for international students. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:56:59,848 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:56:59,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:00,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:02,411 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:02,989 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:03,003 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs over others at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of certain programs over others at institutions. This requires qualitative insights and potentially external data or analysis that is not directly available in the provided schema. The schema contains data about programs, students, and their enrollments, but it does not provide specific metrics or factors that would explain popularity, such as student satisfaction, job placement rates, or market demand. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:57:03,003 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:03,003 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:03,003 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs over others at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of certain programs over others at institutions. This requires qualitative insights and potentially external data or analysis that is not directly available in the provided schema. The schema contains data about programs, students, and their enrollments, but it does not provide specific metrics or factors that would explain popularity, such as student satisfaction, job placement rates, or market demand. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:57:03,070 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:04,303 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:04,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:04,332 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any institutions experiencing significant growth or decline in student population?', 'sql': 'WITH student_counts AS (  SELECT institution_id, COUNT(*) AS student_count, EXTRACT(YEAR FROM created_at) AS year  FROM core.students  GROUP BY institution_id, year),  yearly_growth AS (  SELECT a.institution_id,  a.year AS current_year,  a.student_count AS current_count,  b.student_count AS last_count,  ((a.student_count - b.student_count) / NULLIF(b.student_count, 0)) * 100 AS percentage_change  FROM student_counts a  LEFT JOIN student_counts b ON a.institution_id = b.institution_id AND a.year = b.year + 1)  SELECT i.name,  yg.current_year,  yg.current_count,  yg.last_count,  yg.percentage_change  FROM yearly_growth yg  JOIN auth.institutions i ON yg.institution_id = i.id  WHERE ABS(yg.percentage_change) > 20  LIMIT 20;', 'correct': True, 'reasoning': "The SQL query effectively identifies institutions that have experienced significant changes in student population by calculating the percentage change in student counts year over year. It uses a Common Table Expression (CTE) to first aggregate student counts by institution and year, then compares these counts to determine the percentage change. The final selection filters for changes greater than 20%, which aligns with the question's focus on 'significant growth or decline'.", 'feedback': "The question could be clarified by specifying what constitutes 'significant growth or decline' in terms of percentage, as the SQL uses a threshold of 20%. Additionally, the SQL could be improved by allowing for dynamic thresholds or providing more context on what 'significant' means in this context."}
2025-08-10 17:57:04,332 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:04,332 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-10 17:57:05,234 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:05,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:06,434 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:08,308 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:08,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:08,354 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs over others at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of certain programs over others at institutions. However, the provided schema does not contain specific data or metrics that would allow for such an analysis. While there are tables related to programs, students, and their enrollments, there are no attributes that directly indicate popularity or the factors influencing it, such as student preferences, satisfaction ratings, or external market trends. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-10 17:57:08,354 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:08,354 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:08,354 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs over others at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of certain programs over others at institutions. However, the provided schema does not contain specific data or metrics that would allow for such an analysis. While there are tables related to programs, students, and their enrollments, there are no attributes that directly indicate popularity or the factors influencing it, such as student preferences, satisfaction ratings, or external market trends. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-10 17:57:08,929 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:10,662 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:10,704 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:11,055 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:11,190 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:11,220 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What challenges do you think institutions face in collecting and reporting demographic data on their student populations?', 'answerable': False, 'reasoning': 'The question asks about the challenges institutions face in collecting and reporting demographic data on their student populations. This is a qualitative inquiry that requires insights into institutional practices, policies, and possibly external factors affecting data collection and reporting. The provided database schema contains tables related to student information, demographics, and institutional data, but it does not include any qualitative data, insights, or challenges faced by institutions. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:57:11,220 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:11,220 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:11,220 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What challenges do you think institutions face in collecting and reporting demographic data on their student populations?', 'answerable': False, 'reasoning': 'The question asks about the challenges institutions face in collecting and reporting demographic data on their student populations. This is a qualitative inquiry that requires insights into institutional practices, policies, and possibly external factors affecting data collection and reporting. The provided database schema contains tables related to student information, demographics, and institutional data, but it does not include any qualitative data, insights, or challenges faced by institutions. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:57:11,683 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:12,959 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:13,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:13,465 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:13,607 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:13,620 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs over others at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of certain programs over others at institutions. However, the provided schema does not contain specific data or metrics that would allow for such an analysis. While there are tables related to programs, students, and their attributes, there are no direct indicators of popularity or factors influencing it, such as student enrollment numbers, satisfaction ratings, or external market trends. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-10 17:57:13,621 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:13,621 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:13,621 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs over others at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of certain programs over others at institutions. However, the provided schema does not contain specific data or metrics that would allow for such an analysis. While there are tables related to programs, students, and their attributes, there are no direct indicators of popularity or factors influencing it, such as student enrollment numbers, satisfaction ratings, or external market trends. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-10 17:57:13,956 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:14,757 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:15,392 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:15,782 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:15,907 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:15,928 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What challenges do you think institutions face in collecting and reporting demographic data on their student populations?', 'answerable': False, 'reasoning': 'The question asks about the challenges institutions face in collecting and reporting demographic data on their student populations. This is a qualitative inquiry that requires insights, opinions, or experiences, which cannot be derived from the provided database schema. The schema contains structured data about institutions, students, and various related entities, but it does not include any information regarding the challenges or issues faced by institutions in data collection or reporting.', 'feedback': ''}
2025-08-10 17:57:15,928 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:15,928 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:15,928 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What challenges do you think institutions face in collecting and reporting demographic data on their student populations?', 'answerable': False, 'reasoning': 'The question asks about the challenges institutions face in collecting and reporting demographic data on their student populations. This is a qualitative inquiry that requires insights, opinions, or experiences, which cannot be derived from the provided database schema. The schema contains structured data about institutions, students, and various related entities, but it does not include any information regarding the challenges or issues faced by institutions in data collection or reporting.', 'feedback': ''}
2025-08-10 17:57:15,944 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:16,141 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:16,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:17,798 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:17,811 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe are driving these dramatic fluctuations in student populations at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors driving fluctuations in student populations at institutions, which requires qualitative insights and potentially external data not present in the schema. The schema contains data about institutions and students, but it does not provide specific factors or metrics that would explain fluctuations in student populations, such as economic conditions, policy changes, or demographic trends. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-10 17:57:17,812 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:17,812 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:17,812 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe are driving these dramatic fluctuations in student populations at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors driving fluctuations in student populations at institutions, which requires qualitative insights and potentially external data not present in the schema. The schema contains data about institutions and students, but it does not provide specific factors or metrics that would explain fluctuations in student populations, such as economic conditions, policy changes, or demographic trends. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-10 17:57:17,911 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:18,164 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:18,243 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:18,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:18,839 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:18,856 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs over others at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of certain programs over others at institutions. This requires qualitative insights and potentially external data or metrics that are not directly represented in the schema. The schema contains data about programs, students, and their enrollments, but it does not provide specific factors or metrics that would allow for a comprehensive analysis of program popularity. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:57:18,857 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:18,857 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:18,857 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs over others at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of certain programs over others at institutions. This requires qualitative insights and potentially external data or metrics that are not directly represented in the schema. The schema contains data about programs, students, and their enrollments, but it does not provide specific factors or metrics that would allow for a comprehensive analysis of program popularity. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-10 17:57:20,142 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:20,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:20,171 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available data on student population distribution between undergraduate and postgraduate programs?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available data on student population distribution between undergraduate and postgraduate programs. This requires qualitative insights and contextual understanding that cannot be derived from the provided database schema, which primarily contains structured data about students, programs, and related entities. The schema does not include any information about data availability, quality, or the reasons behind the lack of data.', 'feedback': ''}
2025-08-10 17:57:20,172 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:20,172 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:20,172 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available data on student population distribution between undergraduate and postgraduate programs?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available data on student population distribution between undergraduate and postgraduate programs. This requires qualitative insights and contextual understanding that cannot be derived from the provided database schema, which primarily contains structured data about students, programs, and related entities. The schema does not include any information about data availability, quality, or the reasons behind the lack of data.', 'feedback': ''}
2025-08-10 17:57:20,178 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:20,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:20,269 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What challenges do you think institutions face in collecting and reporting demographic data on their student populations?', 'answerable': False, 'reasoning': 'The question asks about the challenges institutions face in collecting and reporting demographic data on their student populations. This is a qualitative question that requires insights, opinions, or experiences, which cannot be derived from the provided database schema. The schema contains structured data about institutions, students, and various related entities, but it does not include any information regarding challenges, opinions, or qualitative assessments related to demographic data collection and reporting.', 'feedback': ''}
2025-08-10 17:57:20,269 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:20,269 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:20,269 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What challenges do you think institutions face in collecting and reporting demographic data on their student populations?', 'answerable': False, 'reasoning': 'The question asks about the challenges institutions face in collecting and reporting demographic data on their student populations. This is a qualitative question that requires insights, opinions, or experiences, which cannot be derived from the provided database schema. The schema contains structured data about institutions, students, and various related entities, but it does not include any information regarding challenges, opinions, or qualitative assessments related to demographic data collection and reporting.', 'feedback': ''}
2025-08-10 17:57:21,277 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:22,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:22,515 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:22,535 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe are driving these dramatic fluctuations in student populations at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors driving fluctuations in student populations at institutions. However, the provided schema does not contain any specific data or attributes that directly relate to the reasons or factors influencing student population changes. The schema includes tables related to student information, admissions, programs, and other administrative aspects, but it lacks qualitative data or analytical insights that would help in understanding the underlying causes of population fluctuations.', 'feedback': ''}
2025-08-10 17:57:22,535 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:22,535 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:22,535 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe are driving these dramatic fluctuations in student populations at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors driving fluctuations in student populations at institutions. However, the provided schema does not contain any specific data or attributes that directly relate to the reasons or factors influencing student population changes. The schema includes tables related to student information, admissions, programs, and other administrative aspects, but it lacks qualitative data or analytical insights that would help in understanding the underlying causes of population fluctuations.', 'feedback': ''}
2025-08-10 17:57:22,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:24,626 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:24,647 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available data on student population distribution between undergraduate and postgraduate programs?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available data on student population distribution between undergraduate and postgraduate programs. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about students, programs, and related entities. The schema does not provide any information about the reasons or factors affecting data availability or distribution.', 'feedback': ''}
2025-08-10 17:57:24,647 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:24,647 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:24,647 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available data on student population distribution between undergraduate and postgraduate programs?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available data on student population distribution between undergraduate and postgraduate programs. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about students, programs, and related entities. The schema does not provide any information about the reasons or factors affecting data availability or distribution.', 'feedback': ''}
2025-08-10 17:57:24,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:24,759 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:26,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:26,714 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:27,148 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:27,162 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe are driving these dramatic fluctuations in student populations at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors driving fluctuations in student populations at institutions. However, the provided schema does not contain any specific data or attributes that directly relate to the reasons or factors influencing student population changes. The schema includes tables related to student information, admissions, programs, and other administrative aspects, but it lacks qualitative data or analytical insights that would help in understanding the underlying causes of population fluctuations. Therefore, this question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-10 17:57:27,162 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:27,162 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:27,162 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe are driving these dramatic fluctuations in student populations at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors driving fluctuations in student populations at institutions. However, the provided schema does not contain any specific data or attributes that directly relate to the reasons or factors influencing student population changes. The schema includes tables related to student information, admissions, programs, and other administrative aspects, but it lacks qualitative data or analytical insights that would help in understanding the underlying causes of population fluctuations. Therefore, this question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-10 17:57:28,890 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:28,903 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available data on student population distribution between undergraduate and postgraduate programs?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available data on student population distribution between undergraduate and postgraduate programs. This requires qualitative insights and contextual understanding that cannot be derived from the provided database schema, which primarily contains structured data about students, programs, and related entities. The schema does not include any information about data availability, quality, or the reasons behind the lack of data.', 'feedback': ''}
2025-08-10 17:57:28,904 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:28,904 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:28,904 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available data on student population distribution between undergraduate and postgraduate programs?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available data on student population distribution between undergraduate and postgraduate programs. This requires qualitative insights and contextual understanding that cannot be derived from the provided database schema, which primarily contains structured data about students, programs, and related entities. The schema does not include any information about data availability, quality, or the reasons behind the lack of data.', 'feedback': ''}
2025-08-10 17:57:29,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:29,687 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:29,702 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average class size based on the student population at each institution?', 'sql': "SELECT s.institution_id, AVG(v.class_capacity) / COUNT(s.id) AS average_class_size\nFROM students s\nJOIN venues v ON s.institution_id = v.institution_id\nWHERE s.status = 'active'\nGROUP BY s.institution_id\nLIMIT 20;", 'correct': False, 'reasoning': 'The SQL query attempts to calculate the average class size by dividing the average class capacity by the count of active students. However, this is not the correct approach to find the average class size based on the student population. The average class size should be calculated as the total number of students divided by the total number of classes (or venues) available, not by averaging the class capacity and dividing by the count of students. Additionally, the query does not account for the number of classes (venues) per institution, which is necessary to accurately compute the average class size.', 'feedback': 'The question could be clarified by specifying that the average class size should be calculated as the total number of students divided by the total number of classes. The SQL query should be revised to count the number of venues per institution and then divide the total number of active students by this count.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-10 17:57:29,703 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:29,703 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-10 17:57:30,937 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:30,977 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:30,981 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the student population vary by academic year across all institutions?', 'sql': "SELECT ay.start_year, ay.end_year, COUNT(s.id) AS student_count \nFROM academic_years ay \nLEFT JOIN students s ON ay.status = 'Active' \nGROUP BY ay.start_year, ay.end_year \nORDER BY ay.start_year;", 'correct': False, 'reasoning': "The SQL query attempts to count the number of students per academic year, but it incorrectly joins the 'students' table based on the 'status' of the academic year instead of the 'academic_years' table. The join condition should relate the students to the academic years based on the institution_id or another relevant field that connects students to their respective academic years. Additionally, the query does not filter students based on their active status, which is necessary to accurately reflect the student population. The current join condition does not make logical sense in the context of the question.", 'feedback': "The question could be clarified by specifying how students are associated with academic years (e.g., through institution_id or another field). The SQL should include a proper join condition that connects students to their respective academic years, possibly by including a condition that filters students based on their active status. A correct join might look like 'ON s.institution_id = ay.institution_id' to ensure that the student counts are relevant to the academic years being queried.", 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-10 17:57:30,982 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:30,982 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-10 17:57:31,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:31,696 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe are driving these dramatic fluctuations in student populations at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors driving fluctuations in student populations at institutions, which requires qualitative insights and potentially external data not present in the schema. The schema contains data about institutions and students, but it does not provide specific factors or metrics that would explain fluctuations in student populations, such as economic conditions, policy changes, or demographic trends. Therefore, the question cannot be answered directly or indirectly using the available schema.', 'feedback': ''}
2025-08-10 17:57:31,696 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:31,696 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:31,696 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe are driving these dramatic fluctuations in student populations at these institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors driving fluctuations in student populations at institutions, which requires qualitative insights and potentially external data not present in the schema. The schema contains data about institutions and students, but it does not provide specific factors or metrics that would explain fluctuations in student populations, such as economic conditions, policy changes, or demographic trends. Therefore, the question cannot be answered directly or indirectly using the available schema.', 'feedback': ''}
2025-08-10 17:57:32,139 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:33,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:33,266 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available data on student population distribution between undergraduate and postgraduate programs?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available data on student population distribution between undergraduate and postgraduate programs. However, the provided schema does not contain any specific data or attributes related to the reasons or factors affecting data availability. The schema includes tables related to student information, programs, and institutions, but it lacks qualitative data or insights that would help in understanding the reasons behind the lack of data availability. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-10 17:57:33,266 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:57:33,266 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:57:33,266 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the lack of available data on student population distribution between undergraduate and postgraduate programs?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the lack of available data on student population distribution between undergraduate and postgraduate programs. However, the provided schema does not contain any specific data or attributes related to the reasons or factors affecting data availability. The schema includes tables related to student information, programs, and institutions, but it lacks qualitative data or insights that would help in understanding the reasons behind the lack of data availability. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-10 17:57:33,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:35,226 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:36,155 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:36,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:37,793 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:41,219 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:42,848 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:48,372 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:49,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:52,279 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:52,499 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:55,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:55,331 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:56,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:57:56,660 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:00,677 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:01,646 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:07,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:08,601 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:08,617 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the student population vary by academic year across all institutions?', 'sql': "SELECT ay.start_year, COUNT(s.id) AS student_count\nFROM academic_years ay\nJOIN students s ON s.institution_id = ay.institution_id\nWHERE ay.status = 'Active'\nGROUP BY ay.start_year\nORDER BY ay.start_year;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the start year of each academic year and counts the number of students associated with each year. It joins the 'academic_years' table with the 'students' table based on the institution_id, which is necessary to relate students to their respective academic years. The condition 'WHERE ay.status = 'Active'' ensures that only active academic years are considered. The results are grouped by 'ay.start_year', which aligns with the question's focus on variations in student population by academic year. The ordering by 'ay.start_year' provides a chronological view of the data, which is also relevant to the question.", 'feedback': 'The query is well-structured and addresses the question effectively. However, it could be enhanced by including the end year or additional details about the academic years to provide more context. Additionally, if the question intended to analyze variations over time, it might be useful to include a comparison or trend analysis in the results.'}
2025-08-10 17:58:08,617 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:58:08,617 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-10 17:58:10,356 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:13,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:14,858 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:15,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:16,218 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:17,114 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:19,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:19,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:21,838 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:24,063 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:24,086 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contributed to the significant increase in student enrollment in 2023 and 2024, and how might these factors differ from those influencing the decline in 2025?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to changes in student enrollment over multiple years (2023, 2024, and 2025). While the schema contains tables related to students, programs, and possibly enrollment data, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing enrollment trends. The schema lacks qualitative data or contextual information that would be necessary to understand the reasons behind enrollment increases or declines.', 'feedback': ''}
2025-08-10 17:58:24,087 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:58:24,087 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:58:24,087 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contributed to the significant increase in student enrollment in 2023 and 2024, and how might these factors differ from those influencing the decline in 2025?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to changes in student enrollment over multiple years (2023, 2024, and 2025). While the schema contains tables related to students, programs, and possibly enrollment data, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing enrollment trends. The schema lacks qualitative data or contextual information that would be necessary to understand the reasons behind enrollment increases or declines.', 'feedback': ''}
2025-08-10 17:58:24,731 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:27,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:27,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:29,468 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:29,498 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contributed to the significant increase in student enrollment in 2023 and 2024, and how might these factors differ from those influencing the decline in 2025?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to changes in student enrollment over multiple years (2023, 2024, and 2025). While the schema contains tables related to students, programs, and possibly enrollment data, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing enrollment trends. The schema lacks qualitative data or contextual information that would be necessary to understand the reasons behind enrollment increases or declines.', 'feedback': ''}
2025-08-10 17:58:29,499 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:58:29,499 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:58:29,499 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contributed to the significant increase in student enrollment in 2023 and 2024, and how might these factors differ from those influencing the decline in 2025?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to changes in student enrollment over multiple years (2023, 2024, and 2025). While the schema contains tables related to students, programs, and possibly enrollment data, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors influencing enrollment trends. The schema lacks qualitative data or contextual information that would be necessary to understand the reasons behind enrollment increases or declines.', 'feedback': ''}
2025-08-10 17:58:31,051 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:31,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:32,781 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:34,439 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:34,462 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contributed to the significant increase in student enrollment in 2023 and 2024, and how might these factors differ from those influencing the decline in 2025?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to changes in student enrollment over specific years (2023, 2024, and 2025). However, the provided schema does not contain any direct data or tables that track or analyze enrollment trends or the reasons behind them. While there are tables related to students, programs, and admissions, they do not provide insights into the factors influencing enrollment changes. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-10 17:58:34,462 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:58:34,462 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:58:34,462 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contributed to the significant increase in student enrollment in 2023 and 2024, and how might these factors differ from those influencing the decline in 2025?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to changes in student enrollment over specific years (2023, 2024, and 2025). However, the provided schema does not contain any direct data or tables that track or analyze enrollment trends or the reasons behind them. While there are tables related to students, programs, and admissions, they do not provide insights into the factors influencing enrollment changes. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-10 17:58:38,310 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:43,840 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:47,014 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:50,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:52,032 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:58:56,843 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:02,414 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:02,418 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average class size based on the student population at each institution?', 'sql': "SELECT s.institution_id, AVG(tp.capacity) AS average_class_size\nFROM students s\nJOIN timetable_placements tp ON s.id = tp.user_id\nJOIN timetables t ON tp.timetable_id = t.id\nWHERE s.status = 'active'\nGROUP BY s.institution_id\nLIMIT 20;", 'correct': False, 'reasoning': 'The SQL query attempts to calculate the average class size based on the capacity of timetable placements associated with active students. However, it does not directly relate the average class size to the student population at each institution. The average class size should ideally be calculated based on the number of students enrolled in each class (or timetable) rather than the capacity of the venues. The query also limits the results to 20 institutions, which may not be appropriate if the question seeks a complete overview of all institutions.', 'feedback': 'To improve the SQL query, consider calculating the average based on the number of students per class rather than the capacity of the venues. You might need to count the number of students associated with each timetable and then calculate the average. Additionally, remove the LIMIT clause to ensure all institutions are included in the results.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-10 17:59:02,419 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:59:02,419 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-10 17:59:05,241 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:07,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:09,297 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:14,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:20,649 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:23,138 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:25,312 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:25,331 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average class size based on the student population at each institution?', 'answerable': False, 'reasoning': 'The question asks for the average class size based on the student population at each institution. However, the current schema does not provide a direct way to determine the number of students enrolled in each class or venue. While there are tables related to students and venues, there is no explicit table that links students to specific classes or venues, which is necessary to calculate the average class size. Without this enrollment data, it is impossible to compute the average class size accurately.', 'feedback': 'To improve the SQL query, you should consider using the number of students enrolled in each class or venue to calculate the average class size. This may require additional information about class enrollments, which is not provided in the current schema. If such data is available, you could join with a table that contains enrollment information to accurately compute the average class size based on the actual student population.'}
2025-08-10 17:59:25,331 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 17:59:25,332 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 17:59:25,332 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What is the average class size based on the student population at each institution?', 'answerable': False, 'reasoning': 'The question asks for the average class size based on the student population at each institution. However, the current schema does not provide a direct way to determine the number of students enrolled in each class or venue. While there are tables related to students and venues, there is no explicit table that links students to specific classes or venues, which is necessary to calculate the average class size. Without this enrollment data, it is impossible to compute the average class size accurately.', 'feedback': 'To improve the SQL query, you should consider using the number of students enrolled in each class or venue to calculate the average class size. This may require additional information about class enrollments, which is not provided in the current schema. If such data is available, you could join with a table that contains enrollment information to accurately compute the average class size based on the actual student population.'}
2025-08-10 17:59:28,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:31,148 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:32,172 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:36,462 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:41,271 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:45,689 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:48,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:49,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:54,270 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 17:59:58,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:02,544 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:05,849 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:08,028 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:14,346 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:20,750 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:23,837 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:26,725 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:27,703 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:34,634 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:41,834 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:44,770 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:48,360 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:49,484 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:00:55,999 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:01:03,000 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:01:03,003 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average class size based on the student population at each institution?', 'sql': "SELECT s.institution_id, COUNT(s.id) AS total_students, COUNT(DISTINCT v.id) AS total_classes, (COUNT(s.id) / NULLIF(COUNT(DISTINCT v.id), 0)) AS average_class_size\nFROM students s\nJOIN venues v ON s.institution_id = v.institution_id\nWHERE v.status = 'active'\nGROUP BY s.institution_id\nLIMIT 20;", 'correct': False, 'reasoning': 'The SQL query attempts to calculate the average class size by dividing the total number of students by the total number of distinct classes (venues). However, it does not take into account the class capacity of each venue, which is necessary to determine the actual average class size based on the student population. The average class size should be calculated using the class capacity from the venues table, not just the count of distinct venues. Therefore, the query does not accurately answer the question.', 'feedback': 'To improve the SQL query, it should incorporate the class_capacity from the venues table to calculate the average class size more accurately. The calculation should consider the total class capacity available at each institution rather than just the count of distinct venues.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-10 18:01:03,004 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 18:01:03,004 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-10 18:01:06,011 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:01:09,021 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:01:10,372 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:01:15,697 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:01:20,885 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:01:24,334 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:01:27,509 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:01:27,528 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average class size based on the student population at each institution?', 'answerable': False, 'reasoning': 'The question asks for the average class size based on the student population at each institution. However, the current schema does not provide a direct way to determine the number of students enrolled in each class or venue. While there are tables related to students and venues, there is no explicit table that links students to specific classes or venues to calculate the average class size. Therefore, without additional information about class enrollments, which is not present in the schema, the question cannot be answered.', 'feedback': 'To improve the SQL query, you should consider using the number of students enrolled in each class or venue to calculate the average class size. This may require additional information about class enrollments, which is not provided in the current schema. If such data is available, you could join with a table that contains enrollment information to get the actual number of students per class.'}
2025-08-10 18:01:27,529 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-10 18:01:27,529 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-10 18:01:27,529 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What is the average class size based on the student population at each institution?', 'answerable': False, 'reasoning': 'The question asks for the average class size based on the student population at each institution. However, the current schema does not provide a direct way to determine the number of students enrolled in each class or venue. While there are tables related to students and venues, there is no explicit table that links students to specific classes or venues to calculate the average class size. Therefore, without additional information about class enrollments, which is not present in the schema, the question cannot be answered.', 'feedback': 'To improve the SQL query, you should consider using the number of students enrolled in each class or venue to calculate the average class size. This may require additional information about class enrollments, which is not provided in the current schema. If such data is available, you could join with a table that contains enrollment information to get the actual number of students per class.'}
2025-08-10 18:01:27,530 - root - INFO - [{'name': 'Accra College Of Medicine', 'total_students': 114}, {'name': 'Akenten Appiah-Menka University of Skills Training and Entrepreneurial Development', 'total_students': 1}, {'name': 'Akrofi-Christaller Institute', 'total_students': 262}, {'name': 'Central University', 'total_students': 49153}, {'name': 'Ghana Institute of Languages', 'total_students': 18552}, {'name': 'Ghana School of Law', 'total_students': 13012}, {'name': 'GNAT Institute for Research and Industrial Relations Studies', 'total_students': 0}, {'name': 'Institute of Local Government Studies', 'total_students': 0}, {'name': 'ITC College', 'total_students': 0}, {'name': 'ITC University', 'total_students': 192627}, {'name': 'Klintaps College of Health and Allied Sciences', 'total_students': 1135}, {'name': 'Knutsford University College', 'total_students': 0}, {'name': 'Koforidua Technical University', 'total_students': 0}, {'name': 'Methodist University College Ghana', 'total_students': 31258}, {'name': 'Palm Institute', 'total_students': 169}, {'name': 'Presbyterian University College Ghana', 'total_students': 17758}, {'name': 'Public Health Nurses School, Korle Bu', 'total_students': 1065}, {'name': 'Trinity Theological Seminary', 'total_students': 1202}, {'name': 'University Of Education, Winneba', 'total_students': 0}, {'name': 'University of Health and Allied Sciences', 'total_students': 0}]
2025-08-10 18:01:27,530 - root - INFO - [{'start_year': 2019, 'student_count': 192627}, {'start_year': 2021, 'student_count': 192627}, {'start_year': 2022, 'student_count': 192679}, {'start_year': 2023, 'student_count': 241780}, {'start_year': 2024, 'student_count': 242915}, {'start_year': 2025, 'student_count': 193762}, {'start_year': 2026, 'student_count': 192627}, {'start_year': 2027, 'student_count': 192627}]
2025-08-10 18:01:27,531 - root - INFO - [{'program_name': 'Bachelor Of Science In Business Administration (Human Resource Management Option)', 'institution_id': 1, 'student_count': 5260}, {'program_name': 'Bachelor Of Science In Business Administration (Accounting Option)', 'institution_id': 1, 'student_count': 4222}, {'program_name': 'Bachelor Of Science In Banking And Finance', 'institution_id': 1, 'student_count': 3910}, {'program_name': 'Bachelor Of Science In Business Administration (Marketing Option)', 'institution_id': 1, 'student_count': 3435}, {'program_name': 'Bachelor Of Science In Management Studies', 'institution_id': 1, 'student_count': 2599}, {'program_name': 'Bachelor Of Science In Physician Assistantship', 'institution_id': 1, 'student_count': 2449}, {'program_name': 'Bachelor Of Science In Nursing', 'institution_id': 1, 'student_count': 2275}, {'program_name': 'Bachelor Of Laws (Post-Degree Weekend)', 'institution_id': 1, 'student_count': 2072}, {'program_name': 'Bachelor Of Pharmacy', 'institution_id': 1, 'student_count': 1713}, {'program_name': 'Bachelor Of Science In Nursing (Professional)(Weekend)', 'institution_id': 1, 'student_count': 1485}, {'program_name': 'Bachelor Of Science In Nursing (Professional)', 'institution_id': 1, 'student_count': 1462}, {'program_name': 'Bachelor Of Laws', 'institution_id': 1, 'student_count': 1251}, {'program_name': 'Doctor Of Pharmacy', 'institution_id': 1, 'student_count': 1237}, {'program_name': 'Bachelor Of Architecture (5 Years)', 'institution_id': 1, 'student_count': 1222}, {'program_name': 'Bachelor Of Arts In Communication Studies', 'institution_id': 1, 'student_count': 1128}, {'program_name': 'MBA (Finance)', 'institution_id': 1, 'student_count': 1117}, {'program_name': 'Bachelor Of Science In Business Administration (Human Resource Management Option) (Weekend)', 'institution_id': 1, 'student_count': 1019}, {'program_name': 'Bachelor Of Science In Economics', 'institution_id': 1, 'student_count': 862}, {'program_name': 'Bachelor Of Science In Management Studies (Weekend)', 'institution_id': 1, 'student_count': 847}, {'program_name': 'Bachelor Of Science In Business Administration (Accounting Option) (Weekend)', 'institution_id': 1, 'student_count': 824}, {'program_name': 'Bachelor Of Arts In Theology', 'institution_id': 1, 'student_count': 661}, {'program_name': 'Bachelor Of Science In Business Administration (Marketing Option) (Weekend)', 'institution_id': 1, 'student_count': 617}, {'program_name': 'MBA (General Management)', 'institution_id': 1, 'student_count': 563}, {'program_name': 'Bachelor Of Science In Information Technology', 'institution_id': 1, 'student_count': 537}, {'program_name': 'Bachelor Of Science In Computer Science', 'institution_id': 1, 'student_count': 527}, {'program_name': 'DIPLOMA IN BUSINESS', 'institution_id': 1, 'student_count': 490}, {'program_name': 'MBA (Human Resource Management)', 'institution_id': 1, 'student_count': 482}, {'program_name': 'Bachelor Of Science In Environment And Development Studies', 'institution_id': 1, 'student_count': 472}, {'program_name': 'Bachelor Of Laws (Post-Degree Evening)', 'institution_id': 1, 'student_count': 465}, {'program_name': 'Bachelor Of Public Health', 'institution_id': 1, 'student_count': 374}, {'program_name': 'Bachelor Of Science In Agribusiness Management', 'institution_id': 1, 'student_count': 352}, {'program_name': 'Bachelor Of Science In Banking And Finance (Weekend)', 'institution_id': 1, 'student_count': 335}, {'program_name': 'Bachelor Of Science In Civil Engineering', 'institution_id': 1, 'student_count': 328}, {'program_name': 'MBA (Marketing)', 'institution_id': 1, 'student_count': 318}, {'program_name': 'DIPLOMA IN INFORMATION AND DIGITAL TECHNOLOGIES', 'institution_id': 1, 'student_count': 265}, {'program_name': 'Bachelor Of Arts In English', 'institution_id': 1, 'student_count': 230}, {'program_name': 'Master of Philosophy (Religious Studies)', 'institution_id': 1, 'student_count': 221}, {'program_name': 'DIPLOMA IN HEALTH AND SOCIAL CARE', 'institution_id': 1, 'student_count': 177}, {'program_name': 'Master Of Arts In Sacred Ministry', 'institution_id': 1, 'student_count': 175}, {'program_name': 'Master Of Arts (Religious Studies)', 'institution_id': 1, 'student_count': 166}, {'program_name': 'Bachelor Of Science In Agribusiness Management (Weekend)', 'institution_id': 1, 'student_count': 156}, {'program_name': 'Bachelor Of Science In Psychology', 'institution_id': 1, 'student_count': 147}, {'program_name': 'DIPLOMA IN LAW', 'institution_id': 1, 'student_count': 147}, {'program_name': 'Bachelor Of Arts In Family Counseling', 'institution_id': 1, 'student_count': 106}, {'program_name': 'Bachelor Of Arts In French', 'institution_id': 1, 'student_count': 103}, {'program_name': 'Bachelor Of Arts In Social Work', 'institution_id': 1, 'student_count': 76}, {'program_name': 'Executive Masters In Leadership And Governance', 'institution_id': 1, 'student_count': 66}, {'program_name': 'Bachelor Of Science In Real Estate', 'institution_id': 1, 'student_count': 64}, {'program_name': 'Bachelor Of Arts In Sociology', 'institution_id': 1, 'student_count': 60}, {'program_name': 'MASTER OF PHILOSOPHY IN THEOLOGY', 'institution_id': 1, 'student_count': 60}, {'program_name': 'Bachelor Of Arts In Church Administration', 'institution_id': 1, 'student_count': 58}, {'program_name': 'Doctor Of Pharmacy Top-Up', 'institution_id': 1, 'student_count': 50}, {'program_name': 'Bachelor Of Science In Graphic Design', 'institution_id': 1, 'student_count': 41}, {'program_name': 'M.Sc Marketing, Social and Organisational Research', 'institution_id': 1, 'student_count': 34}, {'program_name': 'DIPLOMA IN LAW (FOL)', 'institution_id': 1, 'student_count': 28}, {'program_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'institution_id': 1, 'student_count': 27}, {'program_name': 'MASTER OF PHILOSOPHY IN GUIDANCE AND COUNSELLING', 'institution_id': 1, 'student_count': 26}, {'program_name': 'MASTER OF SCIENCE IN ACCOUNTING', 'institution_id': 1, 'student_count': 24}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION - PROJECT MANAGEMENT', 'institution_id': 1, 'student_count': 23}, {'program_name': 'MASTER OF ARTS IN COMMUNICATION STUDIES', 'institution_id': 1, 'student_count': 19}, {'program_name': 'Master Of Philosophy In Economics', 'institution_id': 1, 'student_count': 18}, {'program_name': 'Bachelor Of Science In Planning', 'institution_id': 1, 'student_count': 17}, {'program_name': 'Bachelor of Arts in Christian Education', 'institution_id': 1, 'student_count': 16}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION - MARKETING', 'institution_id': 1, 'student_count': 16}, {'program_name': 'Bachelor Of Science In Interior Design', 'institution_id': 1, 'student_count': 15}, {'program_name': 'Executive Masters In Leadership And Governance (Law)', 'institution_id': 1, 'student_count': 15}, {'program_name': 'Master Of Public Health', 'institution_id': 1, 'student_count': 14}, {'program_name': 'MASTER OF ARTS IN DEVELOPMENT POLICY', 'institution_id': 1, 'student_count': 13}, {'program_name': 'Master of Philosophy Preparatory Studies', 'institution_id': 1, 'student_count': 13}, {'program_name': 'MASTER OF ARTS IN EDUCATION', 'institution_id': 1, 'student_count': 12}, {'program_name': 'MASTER OF PHILOSOPHY IN COMMUNICATION STUDIES', 'institution_id': 1, 'student_count': 12}, {'program_name': 'Bachelor of Science in Logistics Management', 'institution_id': 1, 'student_count': 10}, {'program_name': 'Bachelor Of Science In Fashion Design', 'institution_id': 1, 'student_count': 9}, {'program_name': 'DIPLOMA IN BUSINESS AND MANAGEMENT', 'institution_id': 1, 'student_count': 9}, {'program_name': 'Bachelor Of Science In Business Administration (Human Resource Management Option) (Distance)', 'institution_id': 1, 'student_count': 6}, {'program_name': 'Master Of Science In Marketing Research', 'institution_id': 1, 'student_count': 6}, {'program_name': 'MASTER OF ARTS IN EDUCATIONAL LEADERSHIP AND ADMINISTRATION', 'institution_id': 1, 'student_count': 3}, {'program_name': 'MASTER OF PHILOSOPHY IN ACCOUNTING', 'institution_id': 1, 'student_count': 3}, {'program_name': 'Bachelor of Arts in Business French', 'institution_id': 1, 'student_count': 2}, {'program_name': 'Bachelor Of Science In Landscape Design', 'institution_id': 1, 'student_count': 2}, {'program_name': 'MASTER OF PHILOSOPHY IN DEVELOPMENT POLICY', 'institution_id': 1, 'student_count': 2}, {'program_name': 'MASTER OF PHILOSOPHY IN EDUCATION', 'institution_id': 1, 'student_count': 2}, {'program_name': 'Bachelor Of Education (Social Sciences)', 'institution_id': 1, 'student_count': 1}, {'program_name': 'BACHELOR OF SCIENCE IN ARCHITECTURE (4 YEARS)', 'institution_id': 1, 'student_count': 1}, {'program_name': 'CERTIFICATE FOR SELECTED UNDERGRADUATE COURSES IN ECONOMICS', 'institution_id': 1, 'student_count': 1}, {'program_name': 'DIPLOMA IN AWARD IN TRAINING & EDUCATION', 'institution_id': 1, 'student_count': 1}, {'program_name': 'DIPLOMA IN MANAGMENT (OPERATIONS MANAGEMENT)', 'institution_id': 1, 'student_count': 1}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION - AGRIBUSINESS', 'institution_id': 1, 'student_count': 1}, {'program_name': 'Bachelor Of Medicine & Bachelor Of Surgery', 'institution_id': 2, 'student_count': 114}, {'program_name': 'PROFESSIONAL LAW COURSE', 'institution_id': 3, 'student_count': 12236}, {'program_name': 'POST CALL LAW COURSE', 'institution_id': 3, 'student_count': 510}, {'program_name': 'Preliminary Law Course', 'institution_id': 3, 'student_count': 163}, {'program_name': 'Ghana Legal Systems / Constitutional Law Course', 'institution_id': 3, 'student_count': 29}, {'program_name': 'COMMONWEALTH LEGISLATIVE DRAFTING COURSE', 'institution_id': 3, 'student_count': 2}, {'program_name': 'MASTER OF THEOLOGY (AFRICAN CHRISTIANITY) - REGULAR', 'institution_id': 4, 'student_count': 126}, {'program_name': 'MASTER OF ARTS THEOLOGY AND MISSION', 'institution_id': 4, 'student_count': 60}, {'program_name': 'MASTER OF ARTS BIBLICAL STUDIES', 'institution_id': 4, 'student_count': 47}, {'program_name': 'MASTER OF ARTS (AUGUST)', 'institution_id': 4, 'student_count': 36}, {'program_name': 'MASTER OF THEOLOGY (AFRICAN CHRISTIANITY)-INTENSIVE', 'institution_id': 4, 'student_count': 27}, {'program_name': 'MASTER OF ARTS HOLISTIC MISSION AND DEVELOPMENT', 'institution_id': 4, 'student_count': 24}, {'program_name': 'MASTER OF ARTS LEADERSHIP', 'institution_id': 4, 'student_count': 17}, {'program_name': 'DOCTOR OF PHILOSOPHY (PHD) THEOLOGY)', 'institution_id': 4, 'student_count': 15}, {'program_name': 'MASTER OF ARTS (BIBLE AND SCIENCE)', 'institution_id': 4, 'student_count': 15}, {'program_name': 'MASTER OF ARTS PENTECOSTAL STUDIES', 'institution_id': 4, 'student_count': 4}, {'program_name': 'MASTER OF ARTS WORLD CHRISTIANITY OPTION', 'institution_id': 4, 'student_count': 4}, {'program_name': 'DOCTOR OF THEOLOGY (AFRICAN CHRISTIANITY) -INTENSIVE', 'institution_id': 4, 'student_count': 2}, {'program_name': 'MASTER OF THEOLOGY (BIBLE TRANSLATION AND INTERPRETATION)', 'institution_id': 4, 'student_count': 1}, {'program_name': 'Diploma in Public Relations', 'institution_id': 5, 'student_count': 3444}, {'program_name': 'Bachelor Of Science (Physician Assistantship)', 'institution_id': 5, 'student_count': 2208}, {'program_name': 'Bachelor Of Science (Nursing)', 'institution_id': 5, 'student_count': 2167}, {'program_name': 'Master Of Education (Educational Studies)', 'institution_id': 5, 'student_count': 1128}, {'program_name': 'Bachelor Of Science (Midwifery)', 'institution_id': 5, 'student_count': 1124}, {'program_name': 'Master Of Public Health', 'institution_id': 5, 'student_count': 851}, {'program_name': 'Bachelor of Science (Business Administration)', 'institution_id': 5, 'student_count': 733}, {'program_name': 'Bachelor Of Laws (3-Years)', 'institution_id': 5, 'student_count': 529}, {'program_name': 'Bachelor Of Science (Information And Communication Technology)', 'institution_id': 5, 'student_count': 398}, {'program_name': 'Bachelor of Science in Business Administration (General Management)', 'institution_id': 5, 'student_count': 382}, {'program_name': 'Master Of Arts (International Development Studies)', 'institution_id': 5, 'student_count': 367}, {'program_name': 'Bachelor of Science in Business Administration (Human Resources Management)', 'institution_id': 5, 'student_count': 354}, {'program_name': 'Master Of Philosophy (Educational Studies)', 'institution_id': 5, 'student_count': 314}, {'program_name': 'Bachelor Of Science (Environmental And Natural Resources Management)', 'institution_id': 5, 'student_count': 294}, {'program_name': 'Certificate In Occupational Safety, Health And Environmental Management', 'institution_id': 5, 'student_count': 290}, {'program_name': 'Certificate In Health Services Management And Administration', 'institution_id': 5, 'student_count': 274}, {'program_name': 'Bachelor of Science in Business Administration (Accounting and Finance)', 'institution_id': 5, 'student_count': 231}, {'program_name': 'Certificate In Public Relations And Strategic Communication', 'institution_id': 5, 'student_count': 218}, {'program_name': 'Certificate In Project Management', 'institution_id': 5, 'student_count': 217}, {'program_name': 'Bachelor Of Science (Agribusiness)', 'institution_id': 5, 'student_count': 215}, {'program_name': 'Master Of Science (Financial Risk Management)', 'institution_id': 5, 'student_count': 203}, {'program_name': 'Master Of Education (Educational Studies) Trim', 'institution_id': 5, 'student_count': 194}, {'program_name': 'Bachelor of Arts (Rural and Community Development)', 'institution_id': 5, 'student_count': 172}, {'program_name': 'Certificate In Governance And Leadership', 'institution_id': 5, 'student_count': 154}, {'program_name': 'Certificate In Public Service Leadership And Governance', 'institution_id': 5, 'student_count': 150}, {'program_name': 'Master Of Science (Environmental Health And Sanitation)', 'institution_id': 5, 'student_count': 149}, {'program_name': 'Certificate In Monitoring And Evaluation', 'institution_id': 5, 'student_count': 148}, {'program_name': 'Certificate In Labour Law And Human Resource  Policy', 'institution_id': 5, 'student_count': 146}, {'program_name': 'Bachelor Of Science In Public Health', 'institution_id': 5, 'student_count': 133}, {'program_name': 'Certificate In Child And Adolescent Psychotherapy', 'institution_id': 5, 'student_count': 117}, {'program_name': 'BSc. Information & Communication Technology (Networking & Telecommunications)', 'institution_id': 5, 'student_count': 111}, {'program_name': 'Certificate In Cyber Security Management', 'institution_id': 5, 'student_count': 107}, {'program_name': 'MSc. Information And Communication Technology (ICT) Management', 'institution_id': 5, 'student_count': 93}, {'program_name': 'Certificate In Emotional Intelligence', 'institution_id': 5, 'student_count': 91}, {'program_name': 'Bachelor Of Laws (4-Years)', 'institution_id': 5, 'student_count': 90}, {'program_name': 'Bachelor Of Education (Social Studies)', 'institution_id': 5, 'student_count': 87}, {'program_name': 'Certificate In Advanced Research And Academic Publication', 'institution_id': 5, 'student_count': 79}, {'program_name': 'Bachelor Of Science (Mathematics)', 'institution_id': 5, 'student_count': 77}, {'program_name': 'Master Of Science (Natural Resources Management)', 'institution_id': 5, 'student_count': 77}, {'program_name': 'Bachelor of Science (International Development)', 'institution_id': 5, 'student_count': 75}, {'program_name': 'Bachelor of Science in Business Administration (Marketing)', 'institution_id': 5, 'student_count': 74}, {'program_name': 'Certificate In Contemporary Customer Service Management', 'institution_id': 5, 'student_count': 57}, {'program_name': 'Bachelor of Science in Business Administration (Banking and Finance)', 'institution_id': 5, 'student_count': 51}, {'program_name': 'Bachelor Of Science (Computer Engineering)', 'institution_id': 5, 'student_count': 41}, {'program_name': 'Pre-University', 'institution_id': 5, 'student_count': 37}, {'program_name': 'BSc. Information & Communication Technology (Cybersecurity)', 'institution_id': 5, 'student_count': 34}, {'program_name': 'Certificate In Credit And Portfolio Risk Management', 'institution_id': 5, 'student_count': 33}, {'program_name': 'BSc. Information & Communication Technology (Software Engineering)', 'institution_id': 5, 'student_count': 31}, {'program_name': 'Doctor Of Philosophy In Environment & Development', 'institution_id': 5, 'student_count': 28}, {'program_name': 'Bachelor Of Science (Business Economics)', 'institution_id': 5, 'student_count': 26}, {'program_name': 'Master Of Philosophy (Educational Studies) - 5 Semesters', 'institution_id': 5, 'student_count': 23}, {'program_name': 'BSc. Information & Communication Technology (Information Security)', 'institution_id': 5, 'student_count': 22}, {'program_name': 'Certificate In Intellectual Property Management', 'institution_id': 5, 'student_count': 18}, {'program_name': 'BSc. Information & Communication Technology (Business Information Systems)', 'institution_id': 5, 'student_count': 14}, {'program_name': 'Diploma In Public Administration', 'institution_id': 5, 'student_count': 12}, {'program_name': 'ATHE Level 3 Diploma In Business', 'institution_id': 5, 'student_count': 10}, {'program_name': 'Higher National Diploma In Information And Communication Technology', 'institution_id': 5, 'student_count': 10}, {'program_name': 'ATHE Level 3 Diploma In Information And Digital Technologies', 'institution_id': 5, 'student_count': 8}, {'program_name': 'Diploma In Business Administration', 'institution_id': 5, 'student_count': 5}, {'program_name': 'BSc. Information & Communication Technology (Data Communication & Networking )', 'institution_id': 5, 'student_count': 4}, {'program_name': 'Master Of Divinity', 'institution_id': 5, 'student_count': 1}, {'program_name': 'A1 CERTIFICATE IN GERMAN (ELEMENTARY)', 'institution_id': 7, 'student_count': 2214}, {'program_name': 'DIPLOMA IN ARABIC', 'institution_id': 7, 'student_count': 1553}, {'program_name': 'CERTIFICATE IN ENGLISH (BEGINNER)', 'institution_id': 7, 'student_count': 1308}, {'program_name': 'CERTIFICATE IN ENGLISH (INTERMEDIATE)', 'institution_id': 7, 'student_count': 1247}, {'program_name': 'A2 CERTIFICATE IN ENGLISH (BEGINNER)', 'institution_id': 7, 'student_count': 1199}, {'program_name': 'B1 CERTIFICATE IN ENGLISH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 1099}, {'program_name': 'A1 CERTIFICATE IN ENGLISH (ELEMENTARY)', 'institution_id': 7, 'student_count': 1085}, {'program_name': 'CERTIFICATE IN FRENCH (BEGINNER)', 'institution_id': 7, 'student_count': 893}, {'program_name': 'A1 CERTIFICATE IN FRENCH (ELEMENTARY)', 'institution_id': 7, 'student_count': 824}, {'program_name': 'B2 CERTIFICATE IN ENGLISH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 761}, {'program_name': 'BACHELOR OF ARTS (TRANSLATION)', 'institution_id': 7, 'student_count': 687}, {'program_name': 'C1 CERTIFICATE IN ENGLISH (ADVANCED)', 'institution_id': 7, 'student_count': 536}, {'program_name': 'HIGHER  NATIONAL DIPLOMA IN BILINGUAL SECRETARYSHIP', 'institution_id': 7, 'student_count': 507}, {'program_name': 'CERTIFICATE IN ENGLISH (PROFICIENCY)', 'institution_id': 7, 'student_count': 442}, {'program_name': 'A2 CERTIFICATE IN FRENCH (BEGINNER)', 'institution_id': 7, 'student_count': 385}, {'program_name': 'CERTIFICATE IN FRENCH (INTERMEDIATE)', 'institution_id': 7, 'student_count': 364}, {'program_name': 'CERTIFICATE IN SPANISH (BEGINNER)', 'institution_id': 7, 'student_count': 335}, {'program_name': 'CERTIFICATE IN ENGLISH (ADVANCED)', 'institution_id': 7, 'student_count': 306}, {'program_name': 'A1 CERTIFICATE IN SPANISH (ELEMENTARY)', 'institution_id': 7, 'student_count': 293}, {'program_name': 'CERTIFICATE IN ENGLISH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 230}, {'program_name': 'CERTIFICATE IN GERMAN (BEGINNER)', 'institution_id': 7, 'student_count': 218}, {'program_name': 'CERTIFICATE IN ARABIC (BEGINNER)', 'institution_id': 7, 'student_count': 187}, {'program_name': 'B1 CERTIFICATE IN FRENCH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 171}, {'program_name': 'CERTIFICATE IN ENGLISH(ELEMENTARY BEGINNER)', 'institution_id': 7, 'student_count': 170}, {'program_name': 'CERTIFICATE IN ENGLISH (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 167}, {'program_name': 'CERTIFICATE IN ARABIC (ADVANCED)', 'institution_id': 7, 'student_count': 163}, {'program_name': 'CERTIFICATE IN ENGLISH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 160}, {'program_name': 'CERTIFICATE IN ARABIC (INTERMEDIATE)', 'institution_id': 7, 'student_count': 159}, {'program_name': 'A1 CERTIFICATE IN ARABIC (ELEMENTARY)', 'institution_id': 7, 'student_count': 157}, {'program_name': 'A2 CERTIFICATE IN GERMAN (BEGINNER)', 'institution_id': 7, 'student_count': 154}, {'program_name': 'BACHELOR OF ARTS (ARABIC EDUCATION)', 'institution_id': 7, 'student_count': 148}, {'program_name': 'CERTIFICATE IN FRENCH (PROFICIENCY)', 'institution_id': 7, 'student_count': 143}, {'program_name': 'CERTIFICATE IN FRENCH (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 131}, {'program_name': 'B2 CERTIFICATE IN FRENCH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 100}, {'program_name': 'CERTIFICATE IN FRENCH (ADVANCED)', 'institution_id': 7, 'student_count': 99}, {'program_name': 'A2 CERTIFICATE IN SPANISH (BEGINNER)', 'institution_id': 7, 'student_count': 89}, {'program_name': 'A1 CERTIFICATE IN CHINESE(ELEMENTARY)', 'institution_id': 7, 'student_count': 85}, {'program_name': 'CERTIFICATE IN ENGLISH (INTENSIVE ADVANCED)', 'institution_id': 7, 'student_count': 80}, {'program_name': 'BA. BUSINESS AND BILINGUAL ADMINISTRATION', 'institution_id': 7, 'student_count': 79}, {'program_name': 'C1 CERTIFICATE IN FRENCH (ADVANCED)', 'institution_id': 7, 'student_count': 73}, {'program_name': 'CERTIFICATE IN SPANISH (INTERMEDIATE)', 'institution_id': 7, 'student_count': 65}, {'program_name': 'B1 CERTIFICATE IN ARABIC (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 60}, {'program_name': 'C1 CERTIFICATE IN ARABIC (ADVANCED)', 'institution_id': 7, 'student_count': 53}, {'program_name': 'CERTIFICATE IN SPANISH (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 51}, {'program_name': 'CERTIFICATE IN ARABIC (PROFICIENCY)', 'institution_id': 7, 'student_count': 50}, {'program_name': 'A2 CERTIFICATE IN ARABIC (BEGINNER)', 'institution_id': 7, 'student_count': 48}, {'program_name': 'B2 CERTIFICATE IN ARABIC (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 41}, {'program_name': 'CERTIFICATE IN GERMAN (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 38}, {'program_name': 'CERTIFICATE IN GERMAN (INTERMEDIATE)', 'institution_id': 7, 'student_count': 29}, {'program_name': 'B1 CERTIFICATE IN SPANISH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 28}, {'program_name': 'CERTIFICATE IN FRENCH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 28}, {'program_name': 'CERTIFICATE IN PORTUGUESE (BEGINNER)', 'institution_id': 7, 'student_count': 23}, {'program_name': 'CERTIFICATE IN FRENCH (INTENSIVE ADVANCED)', 'institution_id': 7, 'student_count': 18}, {'program_name': 'A2 CERTIFICATE IN CHINESE (BEGINNER)', 'institution_id': 7, 'student_count': 17}, {'program_name': 'CERTIFICATE IN SPANISH (PROFICIENCY)', 'institution_id': 7, 'student_count': 16}, {'program_name': 'A1 CERTIFICATE IN PORTUGUESE (ELEMENTARY)', 'institution_id': 7, 'student_count': 14}, {'program_name': 'CERTIFICATE IN CHINESE (BEGINNER)', 'institution_id': 7, 'student_count': 13}, {'program_name': 'CERTIFICATE IN FRENCH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 13}, {'program_name': 'CERTIFICATE IN GERMAN (PROFICIENCY)', 'institution_id': 7, 'student_count': 12}, {'program_name': 'DIPLOMA IN BILINGUAL BUSINESS ADMINISTRATION', 'institution_id': 7, 'student_count': 11}, {'program_name': 'CERTIFICATE IN ARABIC(INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 9}, {'program_name': 'CERTIFICATE IN GERMAN (ADVANCED)', 'institution_id': 7, 'student_count': 9}, {'program_name': 'B2 CERTIFICATE IN SPANISH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 8}, {'program_name': 'C1 CERTIFICATE IN SPANISH (ADVANCED)', 'institution_id': 7, 'student_count': 8}, {'program_name': 'CERTIFICATE IN PORTUGUESE (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 8}, {'program_name': 'CERTIFICATE IN ENGLISH (INTENSIVE ELEMENTARY BEGINNER)', 'institution_id': 7, 'student_count': 7}, {'program_name': 'CERTIFICATE IN RUSSIA (BEGINNER)', 'institution_id': 7, 'student_count': 7}, {'program_name': 'CERTIFICATE IN SPANISH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 7}, {'program_name': 'B1 CERTIFICATE IN GERMAN (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 6}, {'program_name': 'CERTIFICATE IN SPANISH (INTENSIVE ADVANCED)', 'institution_id': 7, 'student_count': 5}, {'program_name': 'CERTIFICATE IN SPANISH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 5}, {'program_name': 'DIPLOMA IN FRENCH', 'institution_id': 7, 'student_count': 5}, {'program_name': 'A1 CERTIFICATE IN RUSSIAN (ELEMENTARY)', 'institution_id': 7, 'student_count': 4}, {'program_name': 'CERTIFICATE IN FRENCH', 'institution_id': 7, 'student_count': 4}, {'program_name': 'CERTIFICATE IN SPANISH (ADVANCED)', 'institution_id': 7, 'student_count': 4}, {'program_name': 'A2 CERTIFICATE IN PORTUGUESE (BEGINNER)', 'institution_id': 7, 'student_count': 3}, {'program_name': 'B1 CERTIFICATE IN CHINESE (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 3}, {'program_name': 'CERTIFICATE IN GERMAN (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 3}, {'program_name': 'DIPLOMA IN BUSINESS STUDIES', 'institution_id': 7, 'student_count': 3}, {'program_name': 'EFFECTIVE COMMUNICATION SKILLS', 'institution_id': 7, 'student_count': 3}, {'program_name': 'ONE YEAR HND IN BILINGUAL SECRETARYSHIP', 'institution_id': 7, 'student_count': 3}, {'program_name': 'B2 CERTIFICATE IN GERMAN (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (ENGLISH)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'CERTIFICATE IN ARABIC (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'CERTIFICATE IN CHINESE (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'CERTIFICATE IN GERMAN (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'CERTIFICATE IN PORTUGUESE (INTENSIVE ADVANCED)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'SPECIAL COURSE IN TRANSLATION', 'institution_id': 7, 'student_count': 2}, {'program_name': 'B1 CERTIFICATE IN PORTUGUESE (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'B1 CERTIFICATE IN RUSSIAN (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'BACHELOR OF DUMMY', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN ARABIC (INTENSIVE ADVANCED)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN CHINESE (ADVANCED)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN CHINESE (INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN ENGLISH (SHORT INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN ENGLISH(SHORT COURSE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN FRENCH (SHORT BEGINNER)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN GERMAN', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN PORTUGUESE (INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN PORTUGUESE (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN PORTUGUESE (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN RUSSIAN (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN RUSSIAN (INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'HIGHER NATIONAL DIPLOMA IN MONOLINGUAL SECRETARYSHIP', 'institution_id': 7, 'student_count': 1}, {'program_name': 'BSc. Business Information Technology', 'institution_id': 8, 'student_count': 52}, {'program_name': 'BSc. Business Administration', 'institution_id': 8, 'student_count': 49}, {'program_name': 'BSc. Computer Science', 'institution_id': 8, 'student_count': 38}, {'program_name': 'Diploma In Business Information Technology', 'institution_id': 8, 'student_count': 18}, {'program_name': 'MSc. Health Services Management & Leadership', 'institution_id': 8, 'student_count': 15}, {'program_name': 'Diploma In Business Administration', 'institution_id': 8, 'student_count': 7}, {'program_name': 'MSc. Information Technology Management', 'institution_id': 8, 'student_count': 2}, {'program_name': 'MSc. Business Information Technology', 'institution_id': 8, 'student_count': 1}, {'program_name': 'Diploma In Registered Community Nursing', 'institution_id': 9, 'student_count': 910}, {'program_name': 'DIPLOMA IN REGISTERED PUBLIC HEALTH NURSING', 'institution_id': 9, 'student_count': 228}, {'program_name': 'Bachelor Of Business Administration (Human Resource Management)', 'institution_id': 10, 'student_count': 4091}, {'program_name': 'Bachelor Of Business Administration (Accounting)', 'institution_id': 10, 'student_count': 2996}, {'program_name': 'Bachelor Of Science (Information Technology)', 'institution_id': 10, 'student_count': 2739}, {'program_name': 'Bachelor Of Business Administration (Marketing)', 'institution_id': 10, 'student_count': 2556}, {'program_name': 'Bachelor Of Science(General Nursing)', 'institution_id': 10, 'student_count': 2007}, {'program_name': 'Bachelor Of Business Administration (Banking & Finance)', 'institution_id': 10, 'student_count': 1772}, {'program_name': 'Bachelor Of Business Administration (Management Studies)', 'institution_id': 10, 'student_count': 1578}, {'program_name': 'Bachelor Of Science (Procurement And Supply Chain Management)', 'institution_id': 10, 'student_count': 1031}, {'program_name': 'Bachelor Of Arts (Psychology)', 'institution_id': 10, 'student_count': 1012}, {'program_name': 'Master Of Business Administration (Finance)', 'institution_id': 10, 'student_count': 667}, {'program_name': 'Master of Business Administration', 'institution_id': 10, 'student_count': 658}, {'program_name': 'Bachelor Of Science(Nursing) Direct Entry', 'institution_id': 10, 'student_count': 650}, {'program_name': 'Bachelor of Business Administration (Human Resource Management) (Weekend)', 'institution_id': 10, 'student_count': 632}, {'program_name': 'Diploma In Information Technology', 'institution_id': 10, 'student_count': 561}, {'program_name': 'Bachelor of Business Administration(Accounting) (Weekend)', 'institution_id': 10, 'student_count': 544}, {'program_name': 'MA Guidance And Counselling', 'institution_id': 10, 'student_count': 529}, {'program_name': 'Master Of Business Administration (Human Resource Management)', 'institution_id': 10, 'student_count': 505}, {'program_name': 'Bachelor Of Science (Economics)', 'institution_id': 10, 'student_count': 423}, {'program_name': 'Bachelor of Business Administration (Marketing) (Weekend)', 'institution_id': 10, 'student_count': 392}, {'program_name': 'Bachelor of Business Administration (Management Studies)(Weekend)', 'institution_id': 10, 'student_count': 376}, {'program_name': 'Bachelor Of Science General Agric', 'institution_id': 10, 'student_count': 336}, {'program_name': 'Bachelor Of Science(Nursing) January', 'institution_id': 10, 'student_count': 319}, {'program_name': 'Master Of Business Administration (Marketing)', 'institution_id': 10, 'student_count': 308}, {'program_name': 'Bachelor Of Arts (Social Work)', 'institution_id': 10, 'student_count': 298}, {'program_name': 'Diploma Registered General Nursing', 'institution_id': 10, 'student_count': 252}, {'program_name': 'PROFESSIONAL CERTIFICATE IN PSYCHOLOGY', 'institution_id': 10, 'student_count': 241}, {'program_name': 'Certificate in Information Technology', 'institution_id': 10, 'student_count': 235}, {'program_name': 'Master Of Philosophy (Guidance & Counselling)', 'institution_id': 10, 'student_count': 230}, {'program_name': 'Bachelor Of Science(Nursing) Direct Entry (January)', 'institution_id': 10, 'student_count': 216}, {'program_name': 'Bachelor of Arts (English Studies)', 'institution_id': 10, 'student_count': 207}, {'program_name': 'Bachelor of Business Administration (Banking & Finance)(Weekend)', 'institution_id': 10, 'student_count': 201}, {'program_name': 'Bachelor of Science in Economics and Statistics', 'institution_id': 10, 'student_count': 196}, {'program_name': 'Bachelor Of Science In Public Health Nursing', 'institution_id': 10, 'student_count': 187}, {'program_name': 'Master Of Business Administration (Accounting)', 'institution_id': 10, 'student_count': 175}, {'program_name': 'Bachelor Of Arts Communication Studies', 'institution_id': 10, 'student_count': 172}, {'program_name': 'Master Of Education Degree(Education Leadership And Innovation)', 'institution_id': 10, 'student_count': 153}, {'program_name': 'Certificate in Church Music', 'institution_id': 10, 'student_count': 144}, {'program_name': 'Diploma In Business And Management', 'institution_id': 10, 'student_count': 139}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'institution_id': 10, 'student_count': 136}, {'program_name': 'BACHELOR OF SCIENCE BIOLOGICAL ENGINEERING', 'institution_id': 10, 'student_count': 136}, {'program_name': 'Diploma In General Agric', 'institution_id': 10, 'student_count': 128}, {'program_name': 'Bachelor of Science General Agric (Weekend)', 'institution_id': 10, 'student_count': 123}, {'program_name': 'Master Of Arts (Entrepreneurship And Corporate Strategy)', 'institution_id': 10, 'student_count': 116}, {'program_name': 'Master Of Education Degree(Education Management And Practice)', 'institution_id': 10, 'student_count': 114}, {'program_name': 'Certificate In Agribusiness', 'institution_id': 10, 'student_count': 113}, {'program_name': 'Bachelor of Science(Information Technology)(January)', 'institution_id': 10, 'student_count': 106}, {'program_name': 'Bachelor of Science (Economics & Mathematics-Statistics)', 'institution_id': 10, 'student_count': 104}, {'program_name': 'Business Administration', 'institution_id': 10, 'student_count': 104}, {'program_name': 'Bachelor Of Science In Public Health Nursing(Direct Entry)', 'institution_id': 10, 'student_count': 92}, {'program_name': 'Master Of Science (Accounting And Finance)', 'institution_id': 10, 'student_count': 91}, {'program_name': 'Bachelor of Business Administration(Human Resource Management)(January)', 'institution_id': 10, 'student_count': 88}, {'program_name': 'Certificate in Environmental Management and Entrepreneurship', 'institution_id': 10, 'student_count': 88}, {'program_name': 'Bachelor of Science (Mathematics & Statistics)', 'institution_id': 10, 'student_count': 81}, {'program_name': 'Certificate in Business Administration', 'institution_id': 10, 'student_count': 78}, {'program_name': 'Certificate In Horticulture', 'institution_id': 10, 'student_count': 75}, {'program_name': 'Bachelor of Arts in Music', 'institution_id': 10, 'student_count': 65}, {'program_name': 'Bachelor Of Science(Procurement And Supply Chain Management)(January)', 'institution_id': 10, 'student_count': 65}, {'program_name': 'Bachelor of Arts (Religion Ethics & Psychology)', 'institution_id': 10, 'student_count': 59}, {'program_name': 'Bachelor of Business Administration(Management Studies)(January)', 'institution_id': 10, 'student_count': 59}, {'program_name': 'Diploma in Registered General Nursing', 'institution_id': 10, 'student_count': 56}, {'program_name': 'MASTER OF PUBLIC POLICY AND GOVERNANCE', 'institution_id': 10, 'student_count': 55}, {'program_name': 'Bachelor Of Arts (French)', 'institution_id': 10, 'student_count': 54}, {'program_name': 'Bachelor Of Science In Public Health Nursing Direct(January))', 'institution_id': 10, 'student_count': 53}, {'program_name': 'Certificate In Agroprocessing', 'institution_id': 10, 'student_count': 53}, {'program_name': 'Bachelor Of Arts (Social Work)January', 'institution_id': 10, 'student_count': 51}, {'program_name': 'Master Of Arts Small Business Management', 'institution_id': 10, 'student_count': 51}, {'program_name': 'Bachelor of Science in Actuarial Science', 'institution_id': 10, 'student_count': 49}, {'program_name': 'Masters of Business Administration(Finance)(Combined)', 'institution_id': 10, 'student_count': 49}, {'program_name': 'ADVANCE CERTIFICATE IN BUSINESS ADMINISTRATION', 'institution_id': 10, 'student_count': 47}, {'program_name': 'Certificate in Information Technology (January)', 'institution_id': 10, 'student_count': 45}, {'program_name': 'Master Of Arts Entrepreneurship', 'institution_id': 10, 'student_count': 45}, {'program_name': 'Master of Philosophy (Statistics)', 'institution_id': 10, 'student_count': 44}, {'program_name': 'MASTER OF SCIENCE (ACCOUNTING AND FINANCE) JANUARY', 'institution_id': 10, 'student_count': 44}, {'program_name': 'Master Of Education Degree(Education Leadership And Innovation)(January)', 'institution_id': 10, 'student_count': 43}, {'program_name': 'Bachelor of Science  (Procurement and Supply Chain Management)(Weekend)', 'institution_id': 10, 'student_count': 42}, {'program_name': 'Bachelor of Business Administration(Accounting)(January)', 'institution_id': 10, 'student_count': 38}, {'program_name': 'BSC INFORMATION TECHNOLOGY(Direct)', 'institution_id': 10, 'student_count': 37}, {'program_name': 'Bachelor of Business Administration(Marketing)(January)', 'institution_id': 10, 'student_count': 35}, {'program_name': 'Bachelor Of Arts(Psychology)(January)', 'institution_id': 10, 'student_count': 32}, {'program_name': 'Diploma in Music', 'institution_id': 10, 'student_count': 31}, {'program_name': 'Bachelor of Fine Arts (Theatre Studies)', 'institution_id': 10, 'student_count': 29}, {'program_name': 'Master of Business Administration (Finance)(January)(Weekend)', 'institution_id': 10, 'student_count': 29}, {'program_name': 'Master Of Education Degree (Education Management And Practice)(January)', 'institution_id': 10, 'student_count': 29}, {'program_name': 'Master of Business Administration (Human Resource Management)(January)(Weekend)', 'institution_id': 10, 'student_count': 28}, {'program_name': 'Masters of Business Administration(Human Resource Management)(Combined)', 'institution_id': 10, 'student_count': 27}, {'program_name': 'Master Of Arts (Entrepreneurship And Corporate Strategy)(January)', 'institution_id': 10, 'student_count': 26}, {'program_name': 'Master of Business Administration (Finance)(Weekend)', 'institution_id': 10, 'student_count': 24}, {'program_name': 'Master of Business Administration (Human Resource Management)(Weekend)', 'institution_id': 10, 'student_count': 22}, {'program_name': 'Masters of Business Administration(Accounting)(Combined)', 'institution_id': 10, 'student_count': 19}, {'program_name': 'Bachelor of Science (Procurement and Supply Chain Management)(January)(Weekend)', 'institution_id': 10, 'student_count': 17}, {'program_name': 'Diploma In Business', 'institution_id': 10, 'student_count': 17}, {'program_name': 'Diploma in Information Technology (w)', 'institution_id': 10, 'student_count': 17}, {'program_name': 'Doctor Of Philosophy In Education', 'institution_id': 10, 'student_count': 17}, {'program_name': 'Master Of Philosophy Education Leadership And School Improvement (TOPUP)', 'institution_id': 10, 'student_count': 17}, {'program_name': 'MASTER OF SCIENCE  IN PROCUREMENT AND SUPPLY  CHAIN MANAGEMENT', 'institution_id': 10, 'student_count': 17}, {'program_name': 'Bachelor of Science(Economics)(January)', 'institution_id': 10, 'student_count': 16}, {'program_name': 'Master of Arts Guidance And Counselling Weekend', 'institution_id': 10, 'student_count': 15}, {'program_name': 'MASTER OF PUBLIC POLICY AND GOVERNANCE (January)', 'institution_id': 10, 'student_count': 15}, {'program_name': 'Bachelor of Business Administration(Banking & Finance)(January)', 'institution_id': 10, 'student_count': 14}, {'program_name': 'Bachelor Of Science In Public Health Nursing(January)', 'institution_id': 10, 'student_count': 14}, {'program_name': 'CERTIFICATE IN CHRISTIAN MINISTRY AND LEADERSHIP PROGRAMME', 'institution_id': 10, 'student_count': 14}, {'program_name': 'Certificate in Entrepreneurship (January)', 'institution_id': 10, 'student_count': 14}, {'program_name': 'ADVANCE CERTIFICATE IN BUSINESS ADMINISTRATION(JANUARY)', 'institution_id': 10, 'student_count': 13}, {'program_name': 'Diploma In Information Technology(January)', 'institution_id': 10, 'student_count': 13}, {'program_name': 'Master of Business Administration (Finance)(January)', 'institution_id': 10, 'student_count': 13}, {'program_name': 'Master Of Philosophy Educational Adminstration And Supervision (TOPUP)', 'institution_id': 10, 'student_count': 13}, {'program_name': 'Masters of Business Administration(Human Resource Management)(January)', 'institution_id': 10, 'student_count': 12}, {'program_name': 'Bachelor of Business Administration(Management Studies)(January)(Weekend)', 'institution_id': 10, 'student_count': 11}, {'program_name': 'Bachelor of Science(Procurement and Supply Chain Management)(w)', 'institution_id': 10, 'student_count': 11}, {'program_name': 'Master of Business Administration(Accounting)(Weekend)', 'institution_id': 10, 'student_count': 11}, {'program_name': 'Bachelor of Business Administration(Human Resource Management)(January)(Weekend)', 'institution_id': 10, 'student_count': 10}, {'program_name': 'Bachelor of Business Administration(Human Resource Management)(w)', 'institution_id': 10, 'student_count': 10}, {'program_name': 'Bachelor Of Business Administration(Management Studies)(w)', 'institution_id': 10, 'student_count': 10}, {'program_name': 'Master of Arts GUIDANCE & COUNSELLING(Combined)', 'institution_id': 10, 'student_count': 10}, {'program_name': 'Master Of Philosophy (Entrepreneurship And Corporate Strategy) TOPUP', 'institution_id': 10, 'student_count': 10}, {'program_name': 'Bachelor of Arts (Communication Studies)(January)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Bachelor of Science(Information Technology)(Weekend)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Certificate in Business Administration(January)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Master of Business Administration (Marketing)(January)(Weekend)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Master of Business Administration (Marketing)(Weekend)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Master Of Philosophy (Entrepreneurship And Corporate Strategy)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Bachelor of Arts (Religious Studies and Ethics)', 'institution_id': 10, 'student_count': 8}, {'program_name': 'BSC INFORMATION TECHNOLOGY(Direct) January', 'institution_id': 10, 'student_count': 8}, {'program_name': 'Master Of Arts Educational Administration And Supervision', 'institution_id': 10, 'student_count': 8}, {'program_name': 'Masters of Business Administration(Marketing)(Combined)', 'institution_id': 10, 'student_count': 8}, {'program_name': 'Bachelor of Business Administration(Accounting)(January)(Weekend)', 'institution_id': 10, 'student_count': 7}, {'program_name': 'Certificate in Music (January)', 'institution_id': 10, 'student_count': 7}, {'program_name': 'Master Of Arts Education Leadership And School Improvement', 'institution_id': 10, 'student_count': 7}, {'program_name': 'Masters of Business Administration(Marketing)(January)', 'institution_id': 10, 'student_count': 7}, {'program_name': 'Bachelor of Arts (English Studies)(January)', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Certificate In Counselling Psychology', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Certificate in Counselling Psychology Weekend', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Certificate In English Proficiency', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Certificate in Substainable Entrepreneurship Career In Oil And Gas Industry', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Master Of Philosophy (Guidance & Counselling)Topup', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Masters of Business Administration(Accounting)(January)', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Access Course', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Bachelor of Business Administration(Accounting)(w)', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Bachelor of Business Administration(Marketing)(January)(Weekend) ', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Certificate in BandMastership', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Certificate in Sound Engineering', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Certificate in Theatre Practice', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Certificate in Theatre Studies', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Diploma In Health And Social Care', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Diploma In Information And Digital Technologies', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Master Of Philosophy (Education Leadership And School Improvement)', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Bachelor of Business Administration(Banking & Finance)(January)(Weekend)', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Bachelor Of Science (Wood Technology With Education) - S', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Certificate in Events Management', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Certificate in Multimedia Studies', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Certificate in Theatre Practice January', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Master of Philosophy (Mathematics)', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Bachelor Of Arts in Music(January)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Bachelor of Business Administration(Marketing)(w)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Bachelor of Fine Arts(Theatre Studies)(January)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Bachelor of Science(Mathematics & Statistics)(January)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Certificate in Fashion, Costume and Make-Up Design', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Certificate in Sound Engineering(January)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Master of Business Administration (Human Resource Management-THESIS OPTION)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Master of Business Administration (Marketing-THESIS OPTION)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Master of Business Administration(Accounting)(January)(Weekend)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Master Of Philosophy Education Assessment And Evaluation(TOPUP)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Bachelor of Arts (English Studies)(W)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Arts Communication Studies (w)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Science General Agric(Combined)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Science in Actuarial Science January', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Science(Economics)(Weekend)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Science(Information Technology)(w)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'CERTIFICATE IN CHRISTIAN MINISTRY AND LEADERSHIP PROGRAMME(January)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Master Of Arts Entrepreneurship(January)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Master Of Philosophy (Education Assessment And Evaluation)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Master Of Philosophy (Educational Adminstration And Supervision)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Arts(French)(January)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Bachelor of Arts(Psychology)(Weekend)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Bachelor of Science(Economics & Statistics)(January)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Bachelor of Science(Economics & Statistics)(Weekend)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Certificate in Counselling Psychology(Weekend)(January)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Certificate in Events Management(January)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Certificate in Statistics', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Diploma In General Agric (w)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Diploma In Horticulture', 'institution_id': 10, 'student_count': 1}, {'program_name': 'IT CONSORTIUM COURSE', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Master of Arts Guidance & Counselling(January)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Master of Business Administration (Management Studies)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Master of Philosophy (Statistics)(PRE-MPHIL)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'BACHELOR OF ARTS BUSINESS STUDIES', 'institution_id': 11, 'student_count': 8201}, {'program_name': 'BACHELOR OF SCIENCE IN NURSING', 'institution_id': 11, 'student_count': 7614}, {'program_name': 'BACHELOR OF LAWS', 'institution_id': 11, 'student_count': 3470}, {'program_name': 'BACHELOR OF SCIENCE MIDWIFERY', 'institution_id': 11, 'student_count': 2774}, {'program_name': 'BACHELOR OF SCIENCE MANAGEMENT AND COMPUTER STUDIES', 'institution_id': 11, 'student_count': 1988}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION', 'institution_id': 11, 'student_count': 1537}, {'program_name': 'BACHELOR OF SCIENCE IN PUBLIC HEALTH NURSING', 'institution_id': 11, 'student_count': 1370}, {'program_name': 'B.SC INFORMATION TECHNOLOGY', 'institution_id': 11, 'student_count': 1163}, {'program_name': 'BACHELOR OF SCIENCE COMMUNITY HEALTH NURSING', 'institution_id': 11, 'student_count': 1133}, {'program_name': 'BACHELOR OF ARTS COMPUTER SCIENCE AND MANAGEMENT', 'institution_id': 11, 'student_count': 1090}, {'program_name': 'BACHELOR OF ARTS IN COMMUNICATION STUDIES', 'institution_id': 11, 'student_count': 656}, {'program_name': 'CERTIFICATE IN BUSINESS STUDIES', 'institution_id': 11, 'student_count': 503}, {'program_name': 'DIPLOMA IN INFORMATION TECHNOLOGY', 'institution_id': 11, 'student_count': 476}, {'program_name': 'BACHELOR OF SCIENCE IN ACCOUNTING', 'institution_id': 11, 'student_count': 338}, {'program_name': 'M.SC. ENVIRONMENTAL SUSTAINABILITY AND MANAGEMENT', 'institution_id': 11, 'student_count': 229}, {'program_name': 'MSC LOGISTICS AND SUPPLY CHAIN MANAGEMENT', 'institution_id': 11, 'student_count': 132}, {'program_name': 'MA ADULT EDUCATION', 'institution_id': 11, 'student_count': 110}, {'program_name': 'MASTER OF ARTS INTERNATIONAL RELATIONS', 'institution_id': 11, 'student_count': 108}, {'program_name': 'BACHELOR OF SCIENCE IN ECONOMICS', 'institution_id': 11, 'student_count': 102}, {'program_name': 'BACHELOR OF ARTS IN DEVELOPMENT AND ENVIRONMENTAL STUDIES', 'institution_id': 11, 'student_count': 83}, {'program_name': 'MASTER OF SCIENCE IN CYBER SECURITY AND DIGITAL FORENSICS', 'institution_id': 11, 'student_count': 82}, {'program_name': 'CERTIFICATE IN MANAGEMENT AND COMPUTER STUDIES', 'institution_id': 11, 'student_count': 60}, {'program_name': 'DIPLOMA IN COMMUNICATION STUDIES', 'institution_id': 11, 'student_count': 40}, {'program_name': 'BACHELOR OF SCIENCE ECONOMICS WITH MANAGEMENT', 'institution_id': 11, 'student_count': 33}, {'program_name': 'BACHELOR OF EDUCATION (PRIMARY EDUCATION)', 'institution_id': 11, 'student_count': 32}, {'program_name': 'MASTER OF PHILOSOPHY IN NURSING', 'institution_id': 11, 'student_count': 32}, {'program_name': 'BACHELOR OF ARTS IN RURAL DEVELOPMENT & ECOTOURISM', 'institution_id': 11, 'student_count': 25}, {'program_name': 'BACHELOR OF ARTS IN MUSIC', 'institution_id': 11, 'student_count': 22}, {'program_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'institution_id': 11, 'student_count': 22}, {'program_name': 'BACHELOR OF SCIENCE COMPUTING WITH ACTUARIAL SCIENCE', 'institution_id': 11, 'student_count': 19}, {'program_name': 'MASTER OF PHILOSOPHY IN MIDWIFERY', 'institution_id': 11, 'student_count': 16}, {'program_name': 'MASTER OF SCIENCE IN INFORMATION TECHNOLOGY', 'institution_id': 11, 'student_count': 16}, {'program_name': 'MASTER OF SCIENCE IN BUSINESS COMPUTING', 'institution_id': 11, 'student_count': 15}, {'program_name': 'MASTER OF SCIENCE IN NURSING', 'institution_id': 11, 'student_count': 14}, {'program_name': 'DUMMY PROG', 'institution_id': 11, 'student_count': 7}, {'program_name': 'NURSING ACCESS COURSE', 'institution_id': 11, 'student_count': 3}, {'program_name': 'CERTIFICATE IN TRAVEL AND TOURISM MANAGEMENT', 'institution_id': 11, 'student_count': 2}, {'program_name': 'EXTENDED DIPLOMA STRATEGIC MANAGEMENT AND LEADERSHIP', 'institution_id': 11, 'student_count': 2}, {'program_name': 'Bachelor Of Science (Mathematics Education)', 'institution_id': 12, 'student_count': 1}, {'program_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'institution_id': 12, 'student_count': 1}, {'program_name': 'Bachelor Of Theology', 'institution_id': 13, 'student_count': 392}, {'program_name': 'Master Of Divinity', 'institution_id': 13, 'student_count': 365}, {'program_name': 'Master Of Arts In Ministry', 'institution_id': 13, 'student_count': 213}, {'program_name': 'Certificate in Ministry', 'institution_id': 13, 'student_count': 174}, {'program_name': 'Master of Theology', 'institution_id': 13, 'student_count': 44}, {'program_name': 'Diploma Of Theology', 'institution_id': 13, 'student_count': 4}, {'program_name': 'Bachelor Of Science In Computer Science', 'institution_id': 16, 'student_count': 102}, {'program_name': 'Bachelor Of Science In Information Technology', 'institution_id': 16, 'student_count': 9}, {'program_name': 'Bachelor Of Science In Community Health', 'institution_id': 16, 'student_count': 8}, {'program_name': 'Post - First Degree LL.B', 'institution_id': 16, 'student_count': 8}, {'program_name': 'Bachelor Of Arts In Finance', 'institution_id': 16, 'student_count': 6}, {'program_name': 'Bachelor Of Science In Agriculture', 'institution_id': 16, 'student_count': 6}, {'program_name': 'Bachelor Of Science In Medicine And Therapeutics', 'institution_id': 16, 'student_count': 6}, {'program_name': 'Bachelor Of Science In Pharmaceutical Chemistry', 'institution_id': 16, 'student_count': 6}, {'program_name': 'Bachelor Of Science In Statistics', 'institution_id': 16, 'student_count': 6}, {'program_name': 'Bachelor Of Arts In History', 'institution_id': 16, 'student_count': 5}, {'program_name': 'Bachelor Of Science In Actuarial Science', 'institution_id': 16, 'student_count': 5}, {'program_name': 'Bachelor Of Science In Mathematics', 'institution_id': 16, 'student_count': 5}, {'program_name': 'BSc. Administration (Accounting Option)', 'institution_id': 16, 'student_count': 5}, {'program_name': 'Bachelor Of Science In Plant & Environmental Biology', 'institution_id': 16, 'student_count': 4}, {'program_name': 'LL.B', 'institution_id': 16, 'student_count': 4}, {'program_name': 'Bachelor Of Arts In Marketing', 'institution_id': 16, 'student_count': 3}, {'program_name': 'Bachelor Of Arts In Philosophy And Classics', 'institution_id': 16, 'student_count': 3}, {'program_name': 'Bachelor Of Arts In The Study Of Religion', 'institution_id': 16, 'student_count': 3}, {'program_name': 'Bachelor Of Science In Computer Engineering', 'institution_id': 16, 'student_count': 3}, {'program_name': 'MA - Human Rights And Administration', 'institution_id': 16, 'student_count': 3}, {'program_name': 'LL.M - Alternative Dispute Resolution', 'institution_id': 16, 'student_count': 2}, {'program_name': 'BACHELOR OF NURSING', 'institution_id': 17, 'student_count': 2282}, {'program_name': 'BACHELOR OF NURSING (SANDWICH)', 'institution_id': 17, 'student_count': 1824}, {'program_name': 'BACHELOR OF MIDWIFERY', 'institution_id': 17, 'student_count': 1314}, {'program_name': 'BACHELOR OF PUBLIC HEALTH NURSING ((SANDWICH))', 'institution_id': 17, 'student_count': 1040}, {'program_name': 'BACHELOR OF PUBLIC HEALTH NURSING', 'institution_id': 17, 'student_count': 1020}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (DISEASE CONTROL)', 'institution_id': 17, 'student_count': 1005}, {'program_name': 'BACHELOR OF MEDICINE, BACHELOR OF SURGERY', 'institution_id': 17, 'student_count': 905}, {'program_name': 'BACHELOR OF MEDICAL LABORATORY SCIENCES', 'institution_id': 17, 'student_count': 874}, {'program_name': 'BACHELOR OF MIDWIFERY (SANDWICH)', 'institution_id': 17, 'student_count': 818}, {'program_name': 'BSc. BIOCHEMISTRY AND MOLECULAR BIOLOGY', 'institution_id': 17, 'student_count': 810}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (NUTRITION)', 'institution_id': 17, 'student_count': 794}, {'program_name': 'BACHELOR OF PHYSICIAN ASSISTANTSHIP (MEDICAL)', 'institution_id': 17, 'student_count': 787}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (DISEASE CONTROL SANDWICH)', 'institution_id': 17, 'student_count': 781}, {'program_name': 'DOCTOR OF PHARMACY', 'institution_id': 17, 'student_count': 645}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH PROMOTION)', 'institution_id': 17, 'student_count': 620}, {'program_name': 'BACHELOR OF DIETETICS', 'institution_id': 17, 'student_count': 543}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH INFORMATION)', 'institution_id': 17, 'student_count': 454}, {'program_name': 'BACHELOR OF PUBLIC HEALTH ( NUTRITION SANDWICH)', 'institution_id': 17, 'student_count': 416}, {'program_name': 'BACHELOR OF MIDWIFERY (TOP-UP SANDWICH)', 'institution_id': 17, 'student_count': 368}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH INFORMATION SANDWICH)', 'institution_id': 17, 'student_count': 359}, {'program_name': 'BACHELOR OF PHYSIOTHERAPY', 'institution_id': 17, 'student_count': 353}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH PROMOTION SANDWICH)', 'institution_id': 17, 'student_count': 296}, {'program_name': 'BACHELOR OF DIAGNOSTIC IMAGING', 'institution_id': 17, 'student_count': 292}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (MENTAL HEALTH SANDWICH)', 'institution_id': 17, 'student_count': 279}, {'program_name': 'BACHELOR OF PUBLIC HEALTH NURSING (TOP-UP SANDWICH)', 'institution_id': 17, 'student_count': 263}, {'program_name': 'BACHELOR OF MEDICAL LABORATORY SCIENCES (SANDWICH)', 'institution_id': 17, 'student_count': 253}, {'program_name': 'BACHELOR OF NURSING (TOP-UP SANDWICH)', 'institution_id': 17, 'student_count': 237}, {'program_name': 'DOCTOR OF MEDICAL LABORATORY SCIENCES', 'institution_id': 17, 'student_count': 211}, {'program_name': 'BACHELOR OF SPEECH, LANGUAGE AND HEARING SCIENCES', 'institution_id': 17, 'student_count': 204}, {'program_name': 'ACCESS - SCHOOL OF NURSING AND MIDWIFERY', 'institution_id': 17, 'student_count': 166}, {'program_name': 'BACHELOR OF MIDWIFERY TOP-UP', 'institution_id': 17, 'student_count': 158}, {'program_name': 'BACHELOR OF PHYSIOTHERAPY (SANDWICH)', 'institution_id': 17, 'student_count': 153}, {'program_name': 'BACHELOR OF SPORTS AND EXERCISE MEDICAL SCIENCES', 'institution_id': 17, 'student_count': 119}, {'program_name': 'BACHELOR OF NURSING TOP-UP', 'institution_id': 17, 'student_count': 115}, {'program_name': 'BACHELOR OF DIAGNOSTIC IMAGING (RADIOGRAPHY)', 'institution_id': 17, 'student_count': 111}, {'program_name': 'BACHELOR OF HEALTH SERVICES ADMINISTRATION', 'institution_id': 17, 'student_count': 110}, {'program_name': 'BACHELOR OF ORTHOTICS AND PROSTHETICS', 'institution_id': 17, 'student_count': 100}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (ENVIRONMENTAL HEALTH SANDWICH)', 'institution_id': 17, 'student_count': 95}, {'program_name': 'BACHELOR OF DIETETICS (SANDWICH)', 'institution_id': 17, 'student_count': 88}, {'program_name': 'MASTER OF PHILOSOPHY (APPLIED EPIDEMIOLOGY)', 'institution_id': 17, 'student_count': 87}, {'program_name': 'MASTER OF PUBLIC HEALTH (EPIDEMIOLOGY AND DISEASE CONTROL )', 'institution_id': 17, 'student_count': 73}, {'program_name': 'BACHELOR OF DENTAL SURGERY', 'institution_id': 17, 'student_count': 66}, {'program_name': 'MASTER OF PUBLIC HEALTH (EPIDEMIOLOGY AND DISEASE CONTROL) WEEKEND OPTION', 'institution_id': 17, 'student_count': 62}, {'program_name': 'MASTER OF PUBLIC HEALTH (GENERAL)', 'institution_id': 17, 'student_count': 61}, {'program_name': 'BACHELOR OF MEDICAL LABORATORY SCIENCES (TOP UP)', 'institution_id': 17, 'student_count': 58}, {'program_name': 'MASTER OF PUBLIC HEALTH (GENERAL) WEEKEND OPTION', 'institution_id': 17, 'student_count': 57}, {'program_name': 'MASTER OF PHILOSOPHY (NURSING STUDIES)', 'institution_id': 17, 'student_count': 50}, {'program_name': 'DOCTOR OF PHILOSOPHY (PUBLIC HEALTH)', 'institution_id': 17, 'student_count': 46}, {'program_name': 'MASTER OF PHILOSOPHY (BIOMEDICAL SCIENCES)', 'institution_id': 17, 'student_count': 36}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (DISEASE CONTROL TOP-UP)', 'institution_id': 17, 'student_count': 30}, {'program_name': 'MASTER OF PUBLIC HEALTH (FAMILY AND REPRODUCTIVE HEALTH) WEEKEND OPTION', 'institution_id': 17, 'student_count': 28}, {'program_name': 'MASTER OF PUBLIC HEALTH (NUTRITION)', 'institution_id': 17, 'student_count': 25}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (MENTAL HEALTH)', 'institution_id': 17, 'student_count': 23}, {'program_name': 'BACHELOR OF DIAGNOSTIC IMAGING (SANDWICH)', 'institution_id': 17, 'student_count': 22}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (PUBLIC HEALTH NURSING)', 'institution_id': 17, 'student_count': 22}, {'program_name': 'BACHELOR OF SPEECH, LANGUAGE AND HEARING SCIENCES (AUDIOLOGY)', 'institution_id': 17, 'student_count': 21}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (ENVIRONMENTAL HEALTH)', 'institution_id': 17, 'student_count': 20}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH INFORMATION TOP-UP)', 'institution_id': 17, 'student_count': 19}, {'program_name': 'MASTER OF PUBLIC HEALTH (HEALTH PROMOTION)', 'institution_id': 17, 'student_count': 19}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH PROMOTION TOP-UP)', 'institution_id': 17, 'student_count': 17}, {'program_name': 'MASTER OF PHILOSOPHY (MEDICAL IMAGING)', 'institution_id': 17, 'student_count': 17}, {'program_name': 'MASTER PHILOSOPHY (MIDWIFERY)', 'institution_id': 17, 'student_count': 17}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (CLINICAL TOP-UP)', 'institution_id': 17, 'student_count': 16}, {'program_name': 'MASTER OF PHILOSOPHY (MEDICAL LABORATORY SCIENCES)', 'institution_id': 17, 'student_count': 16}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (CLINICAL)', 'institution_id': 17, 'student_count': 15}, {'program_name': 'BACHELOR OF ORTHOTICS AND PROSTHETICS (SANDWICH)', 'institution_id': 17, 'student_count': 14}, {'program_name': 'BACHELOR OF PUBLIC HEALTH ( NUTRITION TOP-UP)', 'institution_id': 17, 'student_count': 14}, {'program_name': 'DOCTOR OF PHILOSOPHY (BIOMEDICAL SCIENCES)', 'institution_id': 17, 'student_count': 12}, {'program_name': 'DOCTOR OF PHILOSOPHY (MEDICAL IMAGING)', 'institution_id': 17, 'student_count': 12}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (PUBLIC HEALTH NURSING TOP-UP)', 'institution_id': 17, 'student_count': 10}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (COUNSELLING)', 'institution_id': 17, 'student_count': 9}, {'program_name': 'DOCTOR OF MEDICAL LABORATORY SCIENCES (TOP UP)', 'institution_id': 17, 'student_count': 9}, {'program_name': 'MASTER OF PHILOSOPHY (MEDICAL IMAGING) - PART TIME', 'institution_id': 17, 'student_count': 9}, {'program_name': 'MASTER OF PHILOSOPHY (PHARMACEUTICAL CHEMISTRY)', 'institution_id': 17, 'student_count': 9}, {'program_name': 'MASTER OF PHILOSOPHY (PHARMACOLOGY)', 'institution_id': 17, 'student_count': 9}, {'program_name': 'MASTER OF PUBLIC HEALTH (HEALTH PROMOTION) WEEKEND OPTION', 'institution_id': 17, 'student_count': 7}, {'program_name': 'MASTER OF PUBLIC HEALTH (MONITORING AND EVALUATION) WEEKEND OPTION', 'institution_id': 17, 'student_count': 7}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (ENVIRONMENTAL HEALTH TOP-UP)', 'institution_id': 17, 'student_count': 6}, {'program_name': 'BACHELOR OF SPEECH, LANGUAGE AND HEARING SCIENCES (SPEECH AND LANGUAGE THERAPY)', 'institution_id': 17, 'student_count': 6}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (NEUROPSYCHOLOGY)', 'institution_id': 17, 'student_count': 6}, {'program_name': 'MASTER OF PUBLIC HEALTH (MONITORING AND EVALUATION)', 'institution_id': 17, 'student_count': 6}, {'program_name': 'MASTER OF PUBLIC HEALTH (NUTRITION) WEEKEND OPTION', 'institution_id': 17, 'student_count': 6}, {'program_name': 'TEST PROGRAM', 'institution_id': 17, 'student_count': 6}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (COUNSELLING TOP-UP)', 'institution_id': 17, 'student_count': 4}, {'program_name': 'DOCTOR OF PHILOSOPHY (PHARMACOLOGY)', 'institution_id': 17, 'student_count': 4}, {'program_name': 'ACCESS - F. N. BINKA SCHOOL OF PUBLIC HEALTH', 'institution_id': 17, 'student_count': 3}, {'program_name': 'DOCTOR PHILOSOPHY (PHARMACEUTICAL CHEMISTRY)', 'institution_id': 17, 'student_count': 3}, {'program_name': 'MASTER PHILOSOPHY (PHARMACOGNOSY)', 'institution_id': 17, 'student_count': 3}, {'program_name': 'DOCTOR OF PHILOSOPHY (MEDICAL LABORATORY SCIENCES)', 'institution_id': 17, 'student_count': 2}, {'program_name': 'MASTER OF PUBLIC HEALTH (FAMILY AND REPRODUCTIVE HEALTH)', 'institution_id': 17, 'student_count': 2}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (MENTAL HEALTH TOP-UP)', 'institution_id': 17, 'student_count': 1}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (NEUROPSYCHOLOGY TOP-UP)', 'institution_id': 17, 'student_count': 1}, {'program_name': 'DOCTOR OF PHILOSOPHY (MEDICAL IMAGING) - PART TIME', 'institution_id': 17, 'student_count': 1}, {'program_name': 'MASTER PHILOSOPHY (TOXICOLOGY)', 'institution_id': 17, 'student_count': 1}, {'program_name': 'EXECUTIVE MASTER OF ARTS IN CONFLICT PEACE AND SECURITY', 'institution_id': 18, 'student_count': 42}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'institution_id': 24, 'student_count': 23927}, {'program_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'institution_id': 24, 'student_count': 10732}, {'program_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY)', 'institution_id': 24, 'student_count': 9618}, {'program_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'institution_id': 24, 'student_count': 8905}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'institution_id': 24, 'student_count': 7662}, {'program_name': 'BACHELOR OF EDUCATION (EARLY GRADE)', 'institution_id': 24, 'student_count': 7606}, {'program_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'institution_id': 24, 'student_count': 5826}, {'program_name': 'Bachelor Of Science (Mathematics Education)', 'institution_id': 24, 'student_count': 5491}, {'program_name': 'BACHELOR OF ARTS (POLITICAL SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 5305}, {'program_name': 'Bachelor Of Science (Information Technology Education)', 'institution_id': 24, 'student_count': 4481}, {'program_name': 'BACHELOR OF ART (SOCIAL STUDIES EDUCATION)', 'institution_id': 24, 'student_count': 4255}, {'program_name': 'BACHELOR OF ARTS (ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 3826}, {'program_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'institution_id': 24, 'student_count': 3519}, {'program_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'institution_id': 24, 'student_count': 3348}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'institution_id': 24, 'student_count': 3330}, {'program_name': 'BACHELOR OF ARTS (SOCIAL STUDIES EDUCATION)', 'institution_id': 24, 'student_count': 2988}, {'program_name': 'BACHELOR OF ARTS (GEOGRAPHY EDUCATION)', 'institution_id': 24, 'student_count': 2947}, {'program_name': 'Diploma In Education', 'institution_id': 24, 'student_count': 2821}, {'program_name': 'BACHELOR OF SCIENCE (MANAGEMENT EDUCATION)', 'institution_id': 24, 'student_count': 2750}, {'program_name': 'Bachelor Of Science (Accounting Education)', 'institution_id': 24, 'student_count': 2576}, {'program_name': 'BACHELOR OF ARTS (ART EDUCATION)', 'institution_id': 24, 'student_count': 2503}, {'program_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'institution_id': 24, 'student_count': 2381}, {'program_name': 'DIPLOMA IN EARLY GRADE', 'institution_id': 24, 'student_count': 1994}, {'program_name': 'Bachelor Of Science (Agricultural Science Education)', 'institution_id': 24, 'student_count': 1988}, {'program_name': 'BACHELOR OF SCIENCE (INTEGRATED SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 1974}, {'program_name': 'BACHELOR OF ARTS (ECONOMICS EDUCATION)', 'institution_id': 24, 'student_count': 1921}, {'program_name': 'BACHELOR OF SCIENCE (PHYSICAL EDUCATION)', 'institution_id': 24, 'student_count': 1678}, {'program_name': 'DIPLOMA IN BASIC EDUCATION', 'institution_id': 24, 'student_count': 1580}, {'program_name': 'BACHELOR OF ARTS (FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 1574}, {'program_name': 'BACHELOR OF ARTS (GRAPHIC DESIGN)', 'institution_id': 24, 'student_count': 1558}, {'program_name': 'Bachelor Of Science (Catering And Hospitality Education) - R', 'institution_id': 24, 'student_count': 1508}, {'program_name': 'BACHELOR OF ARTS (EWE EDUCATION)', 'institution_id': 24, 'student_count': 1397}, {'program_name': 'BACHELOR OF SCIENCE (FOOD AND NUTRITION EDUCATION)', 'institution_id': 24, 'student_count': 1359}, {'program_name': 'BACHELOR OF ARTS (ENGLISH EDUCATION)', 'institution_id': 24, 'student_count': 1358}, {'program_name': 'Bachelor Of Science (Biological Sciences Education)', 'institution_id': 24, 'student_count': 1193}, {'program_name': 'Bachelor Of Science (Environmental Health And Sanitation Education)', 'institution_id': 24, 'student_count': 1175}, {'program_name': 'BACHELOR OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'institution_id': 24, 'student_count': 1173}, {'program_name': 'Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'institution_id': 24, 'student_count': 1171}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - R', 'institution_id': 24, 'student_count': 1160}, {'program_name': 'Bachelor Of Science (Catering And Hospitality Education) - S', 'institution_id': 24, 'student_count': 1137}, {'program_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'institution_id': 24, 'student_count': 1089}, {'program_name': 'BACHELOR OF ARTS (TWI EDUCATION)', 'institution_id': 24, 'student_count': 1051}, {'program_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION AND MANAGEMENT) BY DISTANCE', 'institution_id': 24, 'student_count': 1029}, {'program_name': 'BACHELOR OF ARTS (HISTORY EDUCATION)', 'institution_id': 24, 'student_count': 989}, {'program_name': 'BACHELOR OF SCIENCE (HEALTH ADMINISTRATION EDUCATION)', 'institution_id': 24, 'student_count': 942}, {'program_name': 'BACHELOR OF EDUCATION (SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 929}, {'program_name': 'Bachelor Of Arts (Social Studies With Economics Education)', 'institution_id': 24, 'student_count': 880}, {'program_name': 'BACHELOR OF SCIENCE (INTEGRATED HOME ECONOMICS EDUCATION)', 'institution_id': 24, 'student_count': 870}, {'program_name': 'BACHELOR OF EDUCATION (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'institution_id': 24, 'student_count': 858}, {'program_name': 'Bachelor Of Education (Early Grade) - M', 'institution_id': 24, 'student_count': 816}, {'program_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - R', 'institution_id': 24, 'student_count': 811}, {'program_name': 'BACHELOR OF ARTS (MUSIC EDUCATION)', 'institution_id': 24, 'student_count': 751}, {'program_name': 'Bachelor Of Education (Junior High)', 'institution_id': 24, 'student_count': 707}, {'program_name': 'BACHELOR OF SCIENCE (BIOLOGY EDUCATION)', 'institution_id': 24, 'student_count': 704}, {'program_name': 'DIPLOMA (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'institution_id': 24, 'student_count': 690}, {'program_name': 'BACHELOR OF ARTS (FASHION DESIGN AND TEXTILE EDUCATION)', 'institution_id': 24, 'student_count': 684}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'institution_id': 24, 'student_count': 663}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'institution_id': 24, 'student_count': 649}, {'program_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - S', 'institution_id': 24, 'student_count': 627}, {'program_name': 'Master Of Arts In Educational Leadership', 'institution_id': 24, 'student_count': 542}, {'program_name': 'MASTER OF PHILOSOPHY (GHANAIAN LANGUAGE STUDIES)', 'institution_id': 24, 'student_count': 517}, {'program_name': 'Diploma In Education - KS', 'institution_id': 24, 'student_count': 509}, {'program_name': 'BACHELOR OF SCIENCE (HOME ECONOMICS EDUCATION)', 'institution_id': 24, 'student_count': 501}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'institution_id': 24, 'student_count': 481}, {'program_name': 'BACHELOR OF ARTS (TWI WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 466}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'institution_id': 24, 'student_count': 444}, {'program_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'institution_id': 24, 'student_count': 444}, {'program_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'institution_id': 24, 'student_count': 439}, {'program_name': 'MASTER OF PHILOSOPHY (ENGLISH LANGUAGE)', 'institution_id': 24, 'student_count': 400}, {'program_name': 'BACHELOR OF SCIENCE (PHYSICS EDUCATION)', 'institution_id': 24, 'student_count': 393}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'institution_id': 24, 'student_count': 392}, {'program_name': 'MASTER OF PHILOSOPHY (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'institution_id': 24, 'student_count': 379}, {'program_name': 'BACHELOR  OF ARTS (EWE WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 366}, {'program_name': 'MASTER OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'institution_id': 24, 'student_count': 364}, {'program_name': 'Bachelor Of Arts (Economics With Social Studies Education)', 'institution_id': 24, 'student_count': 351}, {'program_name': 'BACHELOR OF MUSIC', 'institution_id': 24, 'student_count': 351}, {'program_name': 'Bachelor Of Business Administration (Management) - E', 'institution_id': 24, 'student_count': 348}, {'program_name': 'MASTER OF PHILOSOPHY (SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 342}, {'program_name': 'MASTER OF PHILOSOPHY (EARLY CHILDHOOD EDUCATION)', 'institution_id': 24, 'student_count': 340}, {'program_name': 'BACHELOR OF ART (LINGUISTICS AND ENGLISH EDUCATION)', 'institution_id': 24, 'student_count': 339}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 325}, {'program_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY) BY DISTANCE', 'institution_id': 24, 'student_count': 323}, {'program_name': 'BACHELOR OF ARTS (FANTE EDUCATION)', 'institution_id': 24, 'student_count': 316}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - S', 'institution_id': 24, 'student_count': 315}, {'program_name': 'Bachelor Of Science (Information Technology Education) - W', 'institution_id': 24, 'student_count': 310}, {'program_name': 'Diploma In Business Administration (Management)', 'institution_id': 24, 'student_count': 309}, {'program_name': 'MASTER OF PHILOSOPHY (ART EDUCATION)', 'institution_id': 24, 'student_count': 308}, {'program_name': 'BACHELOR OF ARTS (COMMUNICATION STUDIES)', 'institution_id': 24, 'student_count': 306}, {'program_name': 'DIPLOMA (GRAPHIC DESIGN)', 'institution_id': 24, 'student_count': 298}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (MARKETING AND ENTREPRENEURSHIP)', 'institution_id': 24, 'student_count': 296}, {'program_name': 'DIPLOMA (ART)', 'institution_id': 24, 'student_count': 295}, {'program_name': 'Bachelor Of Science (Agribusiness Management And Entrepreneurship Education)', 'institution_id': 24, 'student_count': 291}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'institution_id': 24, 'student_count': 283}, {'program_name': 'MASTER OF PHILOSOPHY IN SOCIAL STUDIES', 'institution_id': 24, 'student_count': 283}, {'program_name': 'BACHELOR OF ARTS (THEATRE ARTS)', 'institution_id': 24, 'student_count': 269}, {'program_name': 'MASTER OF PHILOSOPHY (SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 265}, {'program_name': 'MASTER OF PHILOSOPHY (BASIC EDUCATION)', 'institution_id': 24, 'student_count': 254}, {'program_name': 'DIPLOMA IN MANAGEMENT STUDIES', 'institution_id': 24, 'student_count': 251}, {'program_name': 'MASTER OF PHILOSOPHY (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES)', 'institution_id': 24, 'student_count': 243}, {'program_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'institution_id': 24, 'student_count': 232}, {'program_name': 'BACHELOR OF SCIENCE (MATHEMATICS WITH ECONOMICS EDUCATION)', 'institution_id': 24, 'student_count': 226}, {'program_name': 'Bachelor Of Business Administration (Human Resource Management) - W', 'institution_id': 24, 'student_count': 224}, {'program_name': 'DIPLOMA IN MUSIC', 'institution_id': 24, 'student_count': 208}, {'program_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education) - S', 'institution_id': 24, 'student_count': 207}, {'program_name': 'MASTER OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'institution_id': 24, 'student_count': 202}, {'program_name': 'Bachelor Of Science (Information Technology)', 'institution_id': 24, 'student_count': 193}, {'program_name': 'Bachelor Of Science (Natural Resources Management And Education)', 'institution_id': 24, 'student_count': 192}, {'program_name': 'Bachelor Of Science (Wood Technology Education) - S', 'institution_id': 24, 'student_count': 192}, {'program_name': 'MASTER OF PHILOSOPHY (FRENCH)', 'institution_id': 24, 'student_count': 189}, {'program_name': 'MASTER OF EDUCATION (EARLY CHILDHOOD EDUCATION)', 'institution_id': 24, 'student_count': 188}, {'program_name': 'DOCTOR OF PHILOSOPHY (SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 187}, {'program_name': 'BACHELOR OF ARTS (CREATIVE ARTS AND DESIGN EDUCATION)', 'institution_id': 24, 'student_count': 182}, {'program_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'institution_id': 24, 'student_count': 181}, {'program_name': 'MASTER OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'institution_id': 24, 'student_count': 178}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'institution_id': 24, 'student_count': 175}, {'program_name': 'Master Of Technology Education In Catering And Hospitality', 'institution_id': 24, 'student_count': 175}, {'program_name': 'Bachelor Of Science In Administration (Accounting)', 'institution_id': 24, 'student_count': 173}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND TWI EDUCATION)', 'institution_id': 24, 'student_count': 171}, {'program_name': 'MASTER OF PHILOSOPHY (INFORMATION AND COMMUNICATION TECHNOLOGY)', 'institution_id': 24, 'student_count': 169}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'institution_id': 24, 'student_count': 167}, {'program_name': 'MASTER OF PHILOSOPHY (POLITICAL SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 167}, {'program_name': 'Bachelor Of Education (Early Grade) - K', 'institution_id': 24, 'student_count': 166}, {'program_name': 'Bachelor Of Business Administration (Management) - W', 'institution_id': 24, 'student_count': 163}, {'program_name': 'Bachelor Of Arts (Arabic Education)', 'institution_id': 24, 'student_count': 158}, {'program_name': 'BACHELOR OF ARTS (STRATEGIC COMMUNICATION)', 'institution_id': 24, 'student_count': 156}, {'program_name': 'MASTER OF PHILOSOPHY (FOOD AND NUTRITION)', 'institution_id': 24, 'student_count': 156}, {'program_name': 'MASTER OF PHILOSOPHY (MUSIC EDUCATION)', 'institution_id': 24, 'student_count': 154}, {'program_name': 'BACHELOR OF ARTS (RELIGIONS AND MORAL STUDIES EDUCATION)', 'institution_id': 24, 'student_count': 153}, {'program_name': 'MASTER OF PHILOSOPHY (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'institution_id': 24, 'student_count': 150}, {'program_name': 'Master Of Business Administration (Human Resource Management And Organisational Behaviour ) - Weekend', 'institution_id': 24, 'student_count': 149}, {'program_name': 'DIPLOMA (THEATRE ARTS)', 'institution_id': 24, 'student_count': 144}, {'program_name': 'DIPLOMA (BASIC EDUCATION)', 'institution_id': 24, 'student_count': 143}, {'program_name': 'Bachelor Of Business Administration (Secretarial Education)', 'institution_id': 24, 'student_count': 129}, {'program_name': 'Diploma In Education - TM', 'institution_id': 24, 'student_count': 128}, {'program_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - R', 'institution_id': 24, 'student_count': 127}, {'program_name': 'MASTER OF EDUCATION (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'institution_id': 24, 'student_count': 124}, {'program_name': 'BACHELOR ARTS (GA WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 123}, {'program_name': 'DIPLOMA (EARLY GRADE)', 'institution_id': 24, 'student_count': 121}, {'program_name': 'MASTER OF ARTS (ENGLISH LANGUAGE)', 'institution_id': 24, 'student_count': 119}, {'program_name': 'Master Of Business Administration (Accounting)', 'institution_id': 24, 'student_count': 119}, {'program_name': 'MASTER OF EDUCATION (SCIENCE)', 'institution_id': 24, 'student_count': 119}, {'program_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'institution_id': 24, 'student_count': 118}, {'program_name': 'MASTER OF EDUCATION (MATHEMATICS)', 'institution_id': 24, 'student_count': 118}, {'program_name': 'MASTER OF EDUCATION (HOME ECONOMICS)', 'institution_id': 24, 'student_count': 115}, {'program_name': 'Bachelor Of Science (Mechanical Technology Education)', 'institution_id': 24, 'student_count': 114}, {'program_name': 'DIPLOMA (TEXTILES AND FASHION)', 'institution_id': 24, 'student_count': 114}, {'program_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - S', 'institution_id': 24, 'student_count': 114}, {'program_name': 'Bachelor Of Science (Mathematics Education) - M', 'institution_id': 24, 'student_count': 112}, {'program_name': 'Master Of Philosophy In Catering And Hospitality', 'institution_id': 24, 'student_count': 112}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (FINANCE)', 'institution_id': 24, 'student_count': 111}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'institution_id': 24, 'student_count': 110}, {'program_name': 'MASTER OF ARTS (STRATEGIC COMMUNICATION)', 'institution_id': 24, 'student_count': 108}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 107}, {'program_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'institution_id': 24, 'student_count': 106}, {'program_name': 'BACHELOR OF SCIENCE (FAMILY LIFE MANAGEMENT EDUCATION)', 'institution_id': 24, 'student_count': 106}, {'program_name': 'DOCTOR OF PHILOSOPHY (ARTS AND CULTURE)', 'institution_id': 24, 'student_count': 105}, {'program_name': 'BACHELOR OF ARTS (DANGME EDUCATION)', 'institution_id': 24, 'student_count': 104}, {'program_name': 'BACHELOR OF ARTS (TWI WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 104}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BUSINESS INFORMATION SYSTEMS)', 'institution_id': 24, 'student_count': 104}, {'program_name': 'Post Diploma Bachelor Of Science (Environmental Health And Sanitation Education)', 'institution_id': 24, 'student_count': 102}, {'program_name': 'CERTIFICATE (PRE-SCHOOL EDUCATION)', 'institution_id': 24, 'student_count': 100}, {'program_name': 'MASTER OF PHILOSOPHY (CURRICULUM & PEDAGOGIC STUDIES)', 'institution_id': 24, 'student_count': 100}, {'program_name': 'Bachelor Of Science (Mechanical Technology Education) - S', 'institution_id': 24, 'student_count': 99}, {'program_name': 'DIPLOMA (FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 98}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology Education) - S', 'institution_id': 24, 'student_count': 98}, {'program_name': 'Bachelor Of Science (Wood Technology Education)', 'institution_id': 24, 'student_count': 96}, {'program_name': 'MASTER OF PHILOSOPHY (CHEMISTRY EDUCATION)', 'institution_id': 24, 'student_count': 96}, {'program_name': 'DIPLOMA IN ACCOUNTING', 'institution_id': 24, 'student_count': 95}, {'program_name': 'DOCTOR OF PHILOSOPHY (APPLIED LINGUISTICS)', 'institution_id': 24, 'student_count': 95}, {'program_name': 'MASTER OF ARTS (GHANAIAN LANGUAGES EDUCATION)', 'institution_id': 24, 'student_count': 95}, {'program_name': 'Master Of Philosophy In Construction Management', 'institution_id': 24, 'student_count': 95}, {'program_name': 'BACHELOR OF SCIENCE (SPORTS COACHING)', 'institution_id': 24, 'student_count': 94}, {'program_name': 'BACHELOR OF ART (SISAALI WITH ENGLISH EDUCATION)', 'institution_id': 24, 'student_count': 93}, {'program_name': 'BACHELOR OF ARTS (DAGBANI EDUCATION)', 'institution_id': 24, 'student_count': 93}, {'program_name': 'BACHELOR OF ARTS (DAGAARE EDUCATION)', 'institution_id': 24, 'student_count': 90}, {'program_name': 'Master Of Philosophy In Construction Technology', 'institution_id': 24, 'student_count': 90}, {'program_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (FINANCE)', 'institution_id': 24, 'student_count': 89}, {'program_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'institution_id': 24, 'student_count': 86}, {'program_name': 'MASTER OF PHILOSOPHY (ECONOMICS)', 'institution_id': 24, 'student_count': 86}, {'program_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT COMMUNICATION)', 'institution_id': 24, 'student_count': 84}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH ENGLISH EDUCATION)', 'institution_id': 24, 'student_count': 82}, {'program_name': 'POST GRADUATE DIPLOMA IN TEACHING AND LEARNING IN HIGHER EDUCATION', 'institution_id': 24, 'student_count': 82}, {'program_name': 'EXECUTIVE MASTERS (CONFLICT HUMAN RIGHTS & PEACE STUDIES)', 'institution_id': 24, 'student_count': 81}, {'program_name': 'Bachelor Of Science (Automotive Technology Education) - S', 'institution_id': 24, 'student_count': 80}, {'program_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'institution_id': 24, 'student_count': 80}, {'program_name': 'Diploma In Electrical And Electronics Engineering Technology', 'institution_id': 24, 'student_count': 79}, {'program_name': 'Diploma In Environmental Health And Sanitation Education', 'institution_id': 24, 'student_count': 79}, {'program_name': 'MASTER OF EDUCATION (INSTITUTIONAL MENTORSHIP AND SUPERVISION)', 'institution_id': 24, 'student_count': 79}, {'program_name': 'Master Of Philosophy In Mathematics Education', 'institution_id': 24, 'student_count': 79}, {'program_name': 'MASTER OF PHILOSOPHY (VISUAL COMMUNICATION)', 'institution_id': 24, 'student_count': 77}, {'program_name': 'DOCTOR OF PHILOSOPHY (SOCIAL STUDIES)', 'institution_id': 24, 'student_count': 75}, {'program_name': 'MASTER OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'institution_id': 24, 'student_count': 75}, {'program_name': 'MASTER OF PHILOSOPHY (STRATEGIC COMMUNICATION)', 'institution_id': 24, 'student_count': 75}, {'program_name': 'BACHELOR OF ART (DANGME WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 74}, {'program_name': 'Diploma In Business Administration (Accounting)', 'institution_id': 24, 'student_count': 74}, {'program_name': 'Master Of Science In Information Technology Education', 'institution_id': 24, 'student_count': 74}, {'program_name': 'Master Of Technology In Construction Technology', 'institution_id': 24, 'student_count': 74}, {'program_name': 'Post Diploma Bachelor Of Science In Environmental Health And Sanitation Education - S', 'institution_id': 24, 'student_count': 74}, {'program_name': 'BACHELOR OF ARTS (EWE WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 72}, {'program_name': 'BACHELOR OF ARTS (EWE WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 71}, {'program_name': 'BACHELOR OF FINE ART (ANIMATION)', 'institution_id': 24, 'student_count': 70}, {'program_name': 'Diploma In Catering And Hospitality', 'institution_id': 24, 'student_count': 68}, {'program_name': 'Master Of Philosophy In Educational Leadership', 'institution_id': 24, 'student_count': 67}, {'program_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'institution_id': 24, 'student_count': 67}, {'program_name': 'DOCTOR OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'institution_id': 24, 'student_count': 66}, {'program_name': 'Master Of Philosophy In Environmental And Occupational Health Education', 'institution_id': 24, 'student_count': 66}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - R', 'institution_id': 24, 'student_count': 65}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'institution_id': 24, 'student_count': 65}, {'program_name': 'Master Of Philosophy In Fashion Design And Textiles', 'institution_id': 24, 'student_count': 65}, {'program_name': 'Bachelor Of Arts (French With English Education)', 'institution_id': 24, 'student_count': 64}, {'program_name': 'Bachelor Of Science (Mathematics Education) - W', 'institution_id': 24, 'student_count': 64}, {'program_name': 'BACHELOR OF ARTS (DAGBANI WITH LINGUISTIC EDUCATION)', 'institution_id': 24, 'student_count': 63}, {'program_name': 'DOCTOR OF PHILOSOPHY (EDUCATIONAL LEADERSHIP)', 'institution_id': 24, 'student_count': 63}, {'program_name': 'Master Of Philosophy In Accounting', 'institution_id': 24, 'student_count': 62}, {'program_name': 'Diploma In Economics', 'institution_id': 24, 'student_count': 60}, {'program_name': 'BACHELOR OF ARTS (DAGAARE WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 59}, {'program_name': 'DIPLOMA IN SPORTS COACHING', 'institution_id': 24, 'student_count': 59}, {'program_name': 'MASTER OF EDUCATION (GEOGRAPHY)', 'institution_id': 24, 'student_count': 59}, {'program_name': 'Master Of Philosophy In Mathematics Education - W', 'institution_id': 24, 'student_count': 59}, {'program_name': 'DOCTOR OF PHILOSOPHY (COMMUNICATION AND MEDIA)', 'institution_id': 24, 'student_count': 58}, {'program_name': 'MASTER OF PHILOSOPHY (ASSESSMENT, MEASUREMENT AND EVALUATION)', 'institution_id': 24, 'student_count': 58}, {'program_name': 'Master Of Technology In Construction Management', 'institution_id': 24, 'student_count': 58}, {'program_name': 'BACHELOR OF ART (DAGAARE WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 55}, {'program_name': 'BACHELOR OF EDUCATION (PSYCHOLOGY)', 'institution_id': 24, 'student_count': 55}, {'program_name': 'Bachelor Of Science (Wood Technology With Education)', 'institution_id': 24, 'student_count': 55}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 54}, {'program_name': 'Master Of Philosophy In Business Management', 'institution_id': 24, 'student_count': 54}, {'program_name': 'Master Of Philosophy In Catering And Hospitality (Top-Up)', 'institution_id': 24, 'student_count': 54}, {'program_name': 'Diploma In Education - TK', 'institution_id': 24, 'student_count': 53}, {'program_name': 'DOCTOR OF PHILOSOPHY IN GHANAIAN LANGUAGES', 'institution_id': 24, 'student_count': 53}, {'program_name': 'MASTER OF PHILOSOPHY (ACCOUNTING)', 'institution_id': 24, 'student_count': 52}, {'program_name': 'Bachelor Of Science (Wood Technology With Education) - S', 'institution_id': 24, 'student_count': 51}, {'program_name': 'Diploma In Education - CP', 'institution_id': 24, 'student_count': 51}, {'program_name': 'Diploma In Education - M', 'institution_id': 24, 'student_count': 51}, {'program_name': 'MASTER OF ARTS (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES', 'institution_id': 24, 'student_count': 51}, {'program_name': 'MASTER OF EDUCATION (FRENCH)', 'institution_id': 24, 'student_count': 51}, {'program_name': 'MASTER OF EDUCATION (SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 49}, {'program_name': 'BACHELOR OF ART (DAGAARE WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 48}, {'program_name': 'BACHELOR OF ARTS (DEVELOPMENT COMMUNICATION)', 'institution_id': 24, 'student_count': 48}, {'program_name': 'BACHELOR OF ARTS (EWE WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 48}, {'program_name': 'Bachelor Of Business Administration (Human Resource Management) - E', 'institution_id': 24, 'student_count': 48}, {'program_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (LOGISTICS AND TRANSPORT MANAGEMENT)', 'institution_id': 24, 'student_count': 48}, {'program_name': 'Diploma In Construction Technology', 'institution_id': 24, 'student_count': 48}, {'program_name': 'Diploma In Fashion Design And Textiles', 'institution_id': 24, 'student_count': 48}, {'program_name': 'Master Of Education In Agriculture', 'institution_id': 24, 'student_count': 48}, {'program_name': 'BACHELOR OF ARTS (TWI WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 47}, {'program_name': 'Bachelor Of Science In Occupational Health And Safety', 'institution_id': 24, 'student_count': 47}, {'program_name': 'MASTER OF EDUCATION (GUIDANCE AND COUNSELLING)', 'institution_id': 24, 'student_count': 47}, {'program_name': 'Master Of Philosophy In Construction Management (Top Up)', 'institution_id': 24, 'student_count': 47}, {'program_name': 'Master Of Philosophy In Mechanical Engineering Technology', 'institution_id': 24, 'student_count': 47}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 46}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'institution_id': 24, 'student_count': 46}, {'program_name': 'BACHELOR OF ARTS (GA EDUCATION)', 'institution_id': 24, 'student_count': 45}, {'program_name': 'Bachelor Of Science In Administration (Accounting) - W', 'institution_id': 24, 'student_count': 45}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - W', 'institution_id': 24, 'student_count': 45}, {'program_name': 'Master Of Philosophy In Science Education', 'institution_id': 24, 'student_count': 42}, {'program_name': 'Master Of Technology Education In Fashion Design And Textile', 'institution_id': 24, 'student_count': 42}, {'program_name': 'MASTER OF PHILOSOPHY (CLOTHING AND TEXTILES)', 'institution_id': 24, 'student_count': 41}, {'program_name': 'Master Of Philosophy In Wood Science And Technology', 'institution_id': 24, 'student_count': 41}, {'program_name': 'BACHELOR OF ART (KUSAAL WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 40}, {'program_name': 'MASTER OF PHILOSOPHY (INTEGRATED SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 40}, {'program_name': 'MASTER OF PHILOSOPHY (TEXTILES AND FASHION)', 'institution_id': 24, 'student_count': 40}, {'program_name': 'BACHELOR OF ART (DAGBANI WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 39}, {'program_name': 'DOCTOR OF PHILOSOPHY (ENGLISH LANGUAGE)', 'institution_id': 24, 'student_count': 39}, {'program_name': 'BACHELOR OF ARTS (GA WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 38}, {'program_name': 'Bachelor Of Science (Marketing) - W', 'institution_id': 24, 'student_count': 37}, {'program_name': 'Master Of Philosophy In Biology', 'institution_id': 24, 'student_count': 37}, {'program_name': 'Master Of Technology In Electrical And Electronics Engineering', 'institution_id': 24, 'student_count': 37}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 36}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance)', 'institution_id': 24, 'student_count': 36}, {'program_name': 'DOCTOR OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'institution_id': 24, 'student_count': 36}, {'program_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education)', 'institution_id': 24, 'student_count': 36}, {'program_name': 'Doctor Of Philosophy In Educational Leadership', 'institution_id': 24, 'student_count': 35}, {'program_name': 'Bachelor Of Business Administration (Executive Office Administration)', 'institution_id': 24, 'student_count': 34}, {'program_name': 'Diploma In Business Administration (Management) - W', 'institution_id': 24, 'student_count': 34}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (MARKETING)', 'institution_id': 24, 'student_count': 34}, {'program_name': 'MASTER OF EDUCATION (MATHEMATICS) BY DISTANCE', 'institution_id': 24, 'student_count': 34}, {'program_name': 'MASTER OF PHILOSOPHY (HISTORY EDUCATION)', 'institution_id': 24, 'student_count': 34}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 33}, {'program_name': 'DOCTOR OF PHILOSOPHY (SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 33}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGMENT & ORGANIZATION BEHAVIOUR)', 'institution_id': 24, 'student_count': 33}, {'program_name': 'MASTER OF EDUCATION IN SCHOOL SUPERVISION AND QUALITY ASSURANCE)', 'institution_id': 24, 'student_count': 33}, {'program_name': 'MASTER OF ARTS (COMMUNICATION INSTRUCTION)', 'institution_id': 24, 'student_count': 32}, {'program_name': 'Master Of Philosophy In Business Management - W', 'institution_id': 24, 'student_count': 32}, {'program_name': 'Master Of Philosophy In Crop Science', 'institution_id': 24, 'student_count': 32}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education) - S', 'institution_id': 24, 'student_count': 32}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 31}, {'program_name': 'BACHELOR OF ARTS (NZEMA EDUCATION)', 'institution_id': 24, 'student_count': 31}, {'program_name': 'MASTER OF PHILOSOPHY (HUMAN RESOURCE MANAGEMENT)', 'institution_id': 24, 'student_count': 31}, {'program_name': 'MASTER OF PHILOSOPHY (JOURNALISM AND MEDIA ST)', 'institution_id': 24, 'student_count': 31}, {'program_name': 'Diploma In Education (Competency Based Training - CBT)', 'institution_id': 24, 'student_count': 30}, {'program_name': 'MASTER OF EDUCATION (FOOD AND NUTRITION)', 'institution_id': 24, 'student_count': 30}, {'program_name': 'Master Of Philosophy In Agronomy', 'institution_id': 24, 'student_count': 30}, {'program_name': 'BACHELOR OF ARTS (GONJA EDUCATION)', 'institution_id': 24, 'student_count': 29}, {'program_name': 'Bachelor Of Science (Marketing And Entrepreneurship) - W', 'institution_id': 24, 'student_count': 29}, {'program_name': 'MASTER OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'institution_id': 24, 'student_count': 29}, {'program_name': 'MASTER OF EDUCATION (ENGLISH LANGUAGE) BY DISTANCE', 'institution_id': 24, 'student_count': 29}, {'program_name': 'Master Of Education In Mathematics Education', 'institution_id': 24, 'student_count': 29}, {'program_name': 'MASTER OF PHILOSOPHY (ARTS AND CULTURE)', 'institution_id': 24, 'student_count': 29}, {'program_name': 'Master Of Philosophy In Fashion Design And Textiles (Top-Up)', 'institution_id': 24, 'student_count': 29}, {'program_name': 'BACHELOR OF ARTS (DAGBANI WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 28}, {'program_name': 'Bachelor Of Arts (English Language Education) - M', 'institution_id': 24, 'student_count': 28}, {'program_name': 'Bachelor Of Science (Mathematics Education) - S', 'institution_id': 24, 'student_count': 28}, {'program_name': 'MASTER OF PHILOSOPHY (COMMUNICATION INSTRUCTION)', 'institution_id': 24, 'student_count': 28}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'institution_id': 24, 'student_count': 28}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND FANTE EDUCATION)', 'institution_id': 24, 'student_count': 27}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 26}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 26}, {'program_name': 'Diploma In Education (Junior High)', 'institution_id': 24, 'student_count': 26}, {'program_name': 'DOCTOR OF PHILOSOPHY (FRENCH)', 'institution_id': 24, 'student_count': 26}, {'program_name': 'MASTER OF PHILOSOPHY (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'institution_id': 24, 'student_count': 26}, {'program_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 25}, {'program_name': 'DOCTOR OF PHILOSOPHY (MUSIC EDUCATION)', 'institution_id': 24, 'student_count': 25}, {'program_name': 'MASTER OF ARTS (DEVELOPMENT COMMUNICATION)', 'institution_id': 24, 'student_count': 25}, {'program_name': 'MASTER OF SCIENCE (ECONOMICS EDUCATION)', 'institution_id': 24, 'student_count': 25}, {'program_name': 'Master Of Science In Information Technology Education - W', 'institution_id': 24, 'student_count': 25}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology Education)', 'institution_id': 24, 'student_count': 25}, {'program_name': 'BACHELOR OF SCIENCE (INSURANCE AND RISK MANAGEMENT)', 'institution_id': 24, 'student_count': 24}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Management) - E', 'institution_id': 24, 'student_count': 24}, {'program_name': 'Bachelor Of Business Administration (Management) - R', 'institution_id': 24, 'student_count': 23}, {'program_name': 'MASTER OF EDUCATION (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'institution_id': 24, 'student_count': 23}, {'program_name': 'Master Of Education In Science Education', 'institution_id': 24, 'student_count': 23}, {'program_name': 'Master Of Philosophy In Construction Technology (Top-Up)', 'institution_id': 24, 'student_count': 23}, {'program_name': 'Master Of Technology In Mechanical Technology', 'institution_id': 24, 'student_count': 23}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 22}, {'program_name': 'Bachelor Of Science (Civil Engineering)', 'institution_id': 24, 'student_count': 22}, {'program_name': 'Diploma In Automotive Engineering Technology', 'institution_id': 24, 'student_count': 22}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (MANAGEMENT INFORMATION SYSTEMS)', 'institution_id': 24, 'student_count': 22}, {'program_name': 'MASTER OF PHILOSOPHY (ENVIRONMENTAL SCIENCE)', 'institution_id': 24, 'student_count': 22}, {'program_name': 'MASTER OF PHILOSOPHY (FINANCE)', 'institution_id': 24, 'student_count': 22}, {'program_name': 'Master Of Philosophy In Automotive Engineering Technology', 'institution_id': 24, 'student_count': 22}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'BACHELOR OF ARTS (SISAALI EDUCATION)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'MASTER OF EDUCATION (COMPUTER EDUCATION AND TECHNOLOGY)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'MASTER OF PHILOSOPHY (FAMILY LIFE MANAGEMENT)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'MASTER OF PHILOSOPHY (THEATRE ARTS)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'Master Of Philosophy In Public Health', 'institution_id': 24, 'student_count': 21}, {'program_name': 'Post Diploma Bachelor Of Science (Management Education)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'BACHELOR OF ARTS (KASEM EDUCATION)', 'institution_id': 24, 'student_count': 20}, {'program_name': 'Bachelor Of Science (Marketing And Entrepreneurship)', 'institution_id': 24, 'student_count': 20}, {'program_name': 'MASTER OF ARTS (ART EDUCATION)', 'institution_id': 24, 'student_count': 20}, {'program_name': 'MASTER OF EDUCATION (SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 20}, {'program_name': 'Master Of Philosophy In Accounting (Top-Up)', 'institution_id': 24, 'student_count': 20}, {'program_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education) - S', 'institution_id': 24, 'student_count': 20}, {'program_name': 'BACHELOR OF ARTS (KUSAAL EDUCATION)', 'institution_id': 24, 'student_count': 19}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 19}, {'program_name': 'Master Of Philosophy In Educational Leadership (Top-Up)', 'institution_id': 24, 'student_count': 19}, {'program_name': 'MASTER OF SCIENCE (DEVELOPMENT FINANCE)', 'institution_id': 24, 'student_count': 19}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Management) - W', 'institution_id': 24, 'student_count': 19}, {'program_name': 'Bachelor Of Arts (Arabic With English Education)', 'institution_id': 24, 'student_count': 18}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 18}, {'program_name': 'Bachelor Of Science (Mechanical Engineering Technology Education)', 'institution_id': 24, 'student_count': 18}, {'program_name': 'MASTER OF EDUCATION (POLITICAL SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 18}, {'program_name': 'MASTER OF PHILOSOPHY (PHYSICS EDUCATION)', 'institution_id': 24, 'student_count': 18}, {'program_name': 'Master Of Philosophy In Chemistry Education', 'institution_id': 24, 'student_count': 18}, {'program_name': 'Master Of Technology In Automotive Engineering Technology', 'institution_id': 24, 'student_count': 18}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'institution_id': 24, 'student_count': 18}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 17}, {'program_name': 'BACHELOR OF ARTS (GURUNE EDUCATION)', 'institution_id': 24, 'student_count': 17}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 17}, {'program_name': 'Doctor Of Philosophy In Construction Management', 'institution_id': 24, 'student_count': 17}, {'program_name': 'Master Of Philosophy In Mechanical Engineering Technology (Top-Up)', 'institution_id': 24, 'student_count': 17}, {'program_name': 'Master Of Philosophy In Accounting - W', 'institution_id': 24, 'student_count': 16}, {'program_name': 'Master Of Philosophy In Electrical Power Systems Engineering (Top-Up)', 'institution_id': 24, 'student_count': 16}, {'program_name': 'Master Of Philosophy In Information Technology', 'institution_id': 24, 'student_count': 16}, {'program_name': 'Master Of Philosophy In Mathematics Education (Top-Up) - W', 'institution_id': 24, 'student_count': 16}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - W', 'institution_id': 24, 'student_count': 16}, {'program_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'institution_id': 24, 'student_count': 16}, {'program_name': 'BACHELOR OF ART (DAGAARE WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 15}, {'program_name': 'BACHELOR OF ARTS (TWI WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance) - W', 'institution_id': 24, 'student_count': 15}, {'program_name': 'DIPLOMA (EDUCATION)', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Diploma In Architecture And Digital Construction', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Diploma In Mechanical Engineering Technology', 'institution_id': 24, 'student_count': 15}, {'program_name': 'MASTER OF PHILOSOPHY (ENTREPRENEURSHIP AND INNOVATION)', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Master Of Philosophy In Animal Science', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education) - S', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Bachelor Of Science (Plumbing, And Gas Technology)', 'institution_id': 24, 'student_count': 14}, {'program_name': 'Bachelor Of Science In Administration (Accounting) - E', 'institution_id': 24, 'student_count': 14}, {'program_name': 'Doctor Of Philosophy In Construction Technology', 'institution_id': 24, 'student_count': 14}, {'program_name': 'MASTER OF EDUCATION (BIOLOGY)', 'institution_id': 24, 'student_count': 14}, {'program_name': 'MASTER OF PHILOSOPHY (INSTRUCTIONAL DESIGN AND TECHNOLOGY)', 'institution_id': 24, 'student_count': 14}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology Education)', 'institution_id': 24, 'student_count': 14}, {'program_name': 'Bachelor Of Science (Agriculture Engineering, Technology And Innovation Education)', 'institution_id': 24, 'student_count': 13}, {'program_name': 'MASTER OF ARTS (HISTORY EDUCATION)', 'institution_id': 24, 'student_count': 13}, {'program_name': 'Master Of Philosophy In Business Management (Top-Up)', 'institution_id': 24, 'student_count': 13}, {'program_name': 'Master Of Philosophy In Electrical Power Systems Engineering', 'institution_id': 24, 'student_count': 13}, {'program_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'institution_id': 24, 'student_count': 13}, {'program_name': 'Bachelor Of Science (Automotive Engineering Technology Education)', 'institution_id': 24, 'student_count': 12}, {'program_name': 'Bachelor Of Science (Computerised Accounting)', 'institution_id': 24, 'student_count': 12}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education)', 'institution_id': 24, 'student_count': 12}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology)', 'institution_id': 24, 'student_count': 12}, {'program_name': 'Doctor Of Philosophy In Wood Science And Technology', 'institution_id': 24, 'student_count': 11}, {'program_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT FINANCE)', 'institution_id': 24, 'student_count': 11}, {'program_name': 'Master Of Technology In Wood Technology', 'institution_id': 24, 'student_count': 11}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting)', 'institution_id': 24, 'student_count': 11}, {'program_name': 'BACHELOR OF ARTS (GA WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 10}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL EDUCATION)', 'institution_id': 24, 'student_count': 10}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 10}, {'program_name': 'Bachelor Of Science (Marketing) - E', 'institution_id': 24, 'student_count': 10}, {'program_name': 'Master Of Philosophy In Information Technology - W', 'institution_id': 24, 'student_count': 10}, {'program_name': 'Master Of Philosophy In Soil Science', 'institution_id': 24, 'student_count': 10}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education)', 'institution_id': 24, 'student_count': 10}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 9}, {'program_name': 'BACHELOR OF ARTS (GA WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 9}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Doctor Of Philosophy In Mathematics Education', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Master Of Philosophy In Business Management (Top-Up) - W', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Master Of Philosophy In Information Technology - W (Top-Up)', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Post Diploma Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology Education) - S', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Bachelor Of Science (Marketing) - R', 'institution_id': 24, 'student_count': 8}, {'program_name': 'Diploma In Business Administration (Accounting) - W', 'institution_id': 24, 'student_count': 8}, {'program_name': 'Master Of Philosophy In Biology Education', 'institution_id': 24, 'student_count': 8}, {'program_name': 'Master Of Philosophy In Teaching And Learning', 'institution_id': 24, 'student_count': 8}, {'program_name': 'Master Of Philosophy In Wood Science And Technology (Top-Up)', 'institution_id': 24, 'student_count': 8}, {'program_name': 'MASTER OF SCIENCE (BIOLOGY)', 'institution_id': 24, 'student_count': 8}, {'program_name': 'BACHELOR OF ART (LINGUISTICS AND DAGAARE EDUCATION)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'Bachelor Of Science In Administration (Business Information Systems)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'Master Of Philosophy In Crop Science (Top-Up)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'Post Diploama Bachelor Of Science (Computerised Accounting)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - E', 'institution_id': 24, 'student_count': 7}, {'program_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'BACHELOR OF ART (DAGBANI WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 6}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 6}, {'program_name': 'Master Of Philosophy In Automotive Engineering Technology (Top-Up)', 'institution_id': 24, 'student_count': 6}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - E', 'institution_id': 24, 'student_count': 6}, {'program_name': 'BACHELOR OF ART (SISAALI EDUCATION WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 5}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND DANGME EDUCATION)', 'institution_id': 24, 'student_count': 5}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Doctor Of Philosophy In Crop Science', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Master Of Philosophy In Accounting (Top-Up) - W', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Agricultural Science Education)', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology Education) - W', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing And Entrepreneurship) - W', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education) -S', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology Education)', 'institution_id': 24, 'student_count': 5}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND GA EDUCATION)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'BACHELOR OF EDUCATION (SPECIAL NEEDS EDUCATION)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'Diploma In Management Education', 'institution_id': 24, 'student_count': 4}, {'program_name': 'MASTER OF EDUCATION (ENGLISH EDUCATION)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'MASTER OF PHILOSOPHY (HOME ECONOMICS)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing) - W', 'institution_id': 24, 'student_count': 4}, {'program_name': 'BACHELOR OF ART (LINGUISTIC AND EWE EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Bachelor Of Arts (English With French Education)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'DOCTOR OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'MASTER OF ARTS (FRENCH TRANSLATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'MASTER OF FINE ARTS (THEATRE ARTS)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Master Of Philosophy In Agronomy (Top-up)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Master Of Philosophy In Plant Pathology', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing) - R', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S (Top-Up)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W (Top-Up)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) -- R', 'institution_id': 24, 'student_count': 3}, {'program_name': 'B.SC. DOC', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND GONJA EDUCATION)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'Bachelor Of Science (Welding And Fabrication Technology Education', 'institution_id': 24, 'student_count': 2}, {'program_name': 'Doctor Of Philosophy In Animal Science', 'institution_id': 24, 'student_count': 2}, {'program_name': 'Doctor Of Philosophy In Soil Fertility And Plant Nutrition', 'institution_id': 24, 'student_count': 2}, {'program_name': 'MASTER OF ARTS (ARTS AND CULTURE)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'MASTER OF ARTS (THEATRE ARTS)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'MASTER OF PHILOSOPHY COMMUNICATION AND MEDIA STUDIES   (COMMUNICATION SKILLS)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'MASTER OF PHILOSOPHY IN AGRICULTURAL EXTENSION AND RURAL ENTREPRENEURSHIP', 'institution_id': 24, 'student_count': 2}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Executive Office Administration)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND KASEM EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND NZEMA EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance) - E', 'institution_id': 24, 'student_count': 1}, {'program_name': 'DIPLOMA (SIGN LANGUAGE)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Diploma In Welding And Fabrication Engineering Technology', 'institution_id': 24, 'student_count': 1}, {'program_name': 'DOCTOR OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'MASTER OF EDUCATION (ASSESSMENT IN SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'MASTER OF PHILOSOPHY (ASSESSMENT IN SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Master Of Philosphy In Water And Environmental Management', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Education (Early Grade) - K', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education) - S', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Banking And Finance) - W', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Business Information Systems)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'BSc. Diagnostic Medical Sonography', 'institution_id': 30, 'student_count': 518}, {'program_name': 'BSc. Medical Laboratory Science', 'institution_id': 30, 'student_count': 208}, {'program_name': 'BSc. Medical Imaging Science Radiography', 'institution_id': 30, 'student_count': 163}, {'program_name': 'BSc. Ophthalmic Dispensing', 'institution_id': 30, 'student_count': 118}, {'program_name': 'BSc. Clinical Dieteics', 'institution_id': 30, 'student_count': 93}, {'program_name': 'BSc. Community Nutrition', 'institution_id': 30, 'student_count': 22}, {'program_name': 'BSc. Public Health', 'institution_id': 30, 'student_count': 12}, {'program_name': 'Bachelor Of Medicine And Surgery', 'institution_id': 30, 'student_count': 1}]
2025-08-10 18:01:27,545 - root - INFO - [{'institution_name': 'Central University', 'start_year': 2024, 'enrollment_count': 49153}, {'institution_name': 'Central University', 'start_year': 2023, 'enrollment_count': 49153}, {'institution_name': 'ITC University', 'start_year': 2027, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2026, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2025, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2024, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2023, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2022, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2021, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2019, 'enrollment_count': 192627}, {'institution_name': 'Klintaps College of Health and Allied Sciences', 'start_year': 2025, 'enrollment_count': 1135}, {'institution_name': 'Klintaps College of Health and Allied Sciences', 'start_year': 2024, 'enrollment_count': 1135}]
2025-08-10 18:01:27,545 - root - INFO - [{'institution_name': 'Accra College Of Medicine', 'international_count': 114.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Akenten Appiah-Menka University of Skills Training and Entrepreneurial Development', 'international_count': 1.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Akrofi-Christaller Institute', 'international_count': 262.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Central University', 'international_count': 49153.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Ghana Institute of Languages', 'international_count': 18552.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Ghana School of Law', 'international_count': 13012.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'ITC University', 'international_count': 192627.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Klintaps College of Health and Allied Sciences', 'international_count': 1135.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Methodist University College Ghana', 'international_count': 31258.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Palm Institute', 'international_count': 169.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Presbyterian University College Ghana', 'international_count': 17758.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Public Health Nurses School, Korle Bu', 'international_count': 1065.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Trinity Theological Seminary', 'international_count': 1202.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Wisconsin International University College, Ghana', 'international_count': 32093.0, 'domestic_count': 1.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}]
2025-08-10 18:01:27,545 - root - INFO - [{'name': 'ITC University', 'current_year': 2020, 'current_count': 601, 'last_count': 38083, 'percentage_change': -98.42}, {'name': 'ITC University', 'current_year': 2021, 'current_count': 1750, 'last_count': 601, 'percentage_change': 191.18}, {'name': 'ITC University', 'current_year': 2023, 'current_count': 2561, 'last_count': 1703, 'percentage_change': 50.38}, {'name': 'ITC University', 'current_year': 2025, 'current_count': 1651, 'last_count': 2804, 'percentage_change': -41.12}, {'name': 'Accra Medical', 'current_year': 2021, 'current_count': 2541, 'last_count': 7050, 'percentage_change': -63.96}, {'name': 'Accra Medical', 'current_year': 2022, 'current_count': 781, 'last_count': 2541, 'percentage_change': -69.26}, {'name': 'Accra Medical', 'current_year': 2023, 'current_count': 1043, 'last_count': 781, 'percentage_change': 33.55}, {'name': 'Accra Medical', 'current_year': 2024, 'current_count': 1594, 'last_count': 1043, 'percentage_change': 52.83}, {'name': 'Accra Medical', 'current_year': 2025, 'current_count': 3, 'last_count': 1594, 'percentage_change': -99.81}, {'name': 'Ghana School of Law', 'current_year': 2023, 'current_count': 35, 'last_count': 176, 'percentage_change': -80.11}, {'name': 'Ghana School of Law', 'current_year': 2024, 'current_count': 51, 'last_count': 35, 'percentage_change': 45.71}, {'name': 'Koforidua Technical University', 'current_year': 2021, 'current_count': 1397, 'last_count': 5282, 'percentage_change': -73.55}, {'name': 'Koforidua Technical University', 'current_year': 2022, 'current_count': 4220, 'last_count': 1397, 'percentage_change': 202.08}, {'name': 'Koforidua Technical University', 'current_year': 2024, 'current_count': 1948, 'last_count': 4186, 'percentage_change': -53.46}, {'name': 'Koforidua Technical University', 'current_year': 2025, 'current_count': 725, 'last_count': 1948, 'percentage_change': -62.78}, {'name': 'Presbyterian University College Ghana', 'current_year': 2022, 'current_count': 3967, 'last_count': 19922, 'percentage_change': -80.09}, {'name': 'Presbyterian University College Ghana', 'current_year': 2025, 'current_count': 743, 'last_count': 3974, 'percentage_change': -81.3}, {'name': 'Wisconsin International University College', 'current_year': 2021, 'current_count': 12, 'last_count': 414, 'percentage_change': -97.1}, {'name': 'Wisconsin International University College', 'current_year': 2022, 'current_count': 636, 'last_count': 12, 'percentage_change': 5200.0}, {'name': 'Wisconsin International University College', 'current_year': 2023, 'current_count': 140, 'last_count': 636, 'percentage_change': -77.99}]
2025-08-10 18:01:27,545 - root - INFO - 'No results'
2025-08-10 18:01:27,545 - root - INFO - 'No results'
2025-08-10 18:01:27,545 - root - INFO - 'No results'
2025-08-10 18:01:27,545 - root - INFO - 'No results'
2025-08-10 18:01:27,546 - root - INFO - 'No results'
2025-08-10 18:01:27,546 - root - INFO - 'No results'
2025-08-10 18:01:27,546 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-10 18:01:47,432 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:01:47,448 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-10 18:02:13,330 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:13,348 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:13,348 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-10 18:02:13,348 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:13,348 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:13,348 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-10 18:02:13,349 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-10 18:02:13,349 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-10 18:02:13,349 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-10 18:02:13,349 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total number of students enrolled in each institution?...
2025-08-10 18:02:13,349 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total number of students enrolled in each institution is as follows: Accra College Of Medicine h...
2025-08-10 18:02:13,349 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-10 18:02:13,349 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'name': 'Accra College Of Medicine', 'total_students': 114}, {'name': 'Akenten Appiah-Menka Univer...
2025-08-10 18:02:13,349 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_students_by_institution
2025-08-10 18:02:13,349 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-10 18:02:13,349 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-10 18:02:13,349 - celery.redirected - WARNING - [{'name': 'Accra College Of Medicine', 'total_students': 114}, {'name': 'Akenten Appiah-Menka University of Skills Training and Entrepreneurial Development', 'total_students': 1}, {'name': 'Akrofi-Christaller Institute', 'total_students': 262}, {'name': 'Central University', 'total_students': 49153}, {'name': 'Ghana Institute of Languages', 'total_students': 18552}, {'name': 'Ghana School of Law', 'total_students': 13012}, {'name': 'GNAT Institute for Research and Industrial Relations Studies', 'total_students': 0}, {'name': 'Institute of Local Government Studies', 'total_students': 0}, {'name': 'ITC College', 'total_students': 0}, {'name': 'ITC University', 'total_students': 192627}, {'name': 'Klintaps College of Health and Allied Sciences', 'total_students': 1135}, {'name': 'Knutsford University College', 'total_students': 0}, {'name': 'Koforidua Technical University', 'total_students': 0}, {'name': 'Methodist University College Ghana', 'total_students': 31258}, {'name': 'Palm Institute', 'total_students': 169}, {'name': 'Presbyterian University College Ghana', 'total_students': 17758}, {'name': 'Public Health Nurses School, Korle Bu', 'total_students': 1065}, {'name': 'Trinity Theological Seminary', 'total_students': 1202}, {'name': 'University Of Education, Winneba', 'total_students': 0}, {'name': 'University of Health and Allied Sciences', 'total_students': 0}]
2025-08-10 18:02:13,350 - celery.redirected - WARNING - ================================= 
2025-08-10 18:02:13,350 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: total_students_by_institution
2025-08-10 18:02:13,350 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-10 18:02:13,350 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:13,350 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-10 18:02:13,350 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:13,350 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:13,350 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-10 18:02:13,350 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 2
2025-08-10 18:02:13,350 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-10 18:02:13,350 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the student population vary by academic year across all institutions?...
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 2:
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the student population vary by academic year across all institutions?...
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The student population across all institutions has shown some variation by academic year. In 2019, t...
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: line_chart
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'start_year': 2019, 'student_count': 192627}, {'start_year': 2021, 'student_count': 192627}, {'sta...
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_population_by_academic_year
2025-08-10 18:02:13,351 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-10 18:02:13,351 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-10 18:02:13,351 - celery.redirected - WARNING - [{'start_year': 2019, 'student_count': 192627}, {'start_year': 2021, 'student_count': 192627}, {'start_year': 2022, 'student_count': 192679}, {'start_year': 2023, 'student_count': 241780}, {'start_year': 2024, 'student_count': 242915}, {'start_year': 2025, 'student_count': 193762}, {'start_year': 2026, 'student_count': 192627}, {'start_year': 2027, 'student_count': 192627}]
2025-08-10 18:02:13,351 - celery.redirected - WARNING - ================================= 
2025-08-10 18:02:13,352 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_population_by_academic_year
2025-08-10 18:02:13,352 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 2 documents, 1 with data_returned=True
2025-08-10 18:02:13,352 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:13,352 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-10 18:02:13,352 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:13,352 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:13,352 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-10 18:02:13,352 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-10 18:02:13,352 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-10 18:02:13,352 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-10 18:02:13,352 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What are the demographics of the student population, such as age, gender, and nationality, across al...
2025-08-10 18:02:13,352 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Unfortunately, there are no available results regarding the demographics of the student population, ...
2025-08-10 18:02:13,353 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-10 18:02:13,353 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-10 18:02:13,353 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_demographics_overview
2025-08-10 18:02:13,353 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-10 18:02:13,353 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-10 18:02:13,353 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:13,353 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-10 18:02:13,353 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:13,353 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:13,353 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-10 18:02:13,354 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-10 18:02:13,354 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-10 18:02:13,354 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-10 18:02:13,354 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Can we break down the student population by program or course of study at each institution?...
2025-08-10 18:02:13,354 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The student population can be broken down by program or course of study at each institution. For exa...
2025-08-10 18:02:13,354 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-10 18:02:13,355 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'program_name': 'Bachelor Of Science In Business Administration (Human Resource Management Option)...
2025-08-10 18:02:13,355 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_population_by_program_and_institution
2025-08-10 18:02:13,355 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-10 18:02:13,355 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-10 18:02:13,357 - celery.redirected - WARNING - [{'program_name': 'Bachelor Of Science In Business Administration (Human Resource Management Option)', 'institution_id': 1, 'student_count': 5260}, {'program_name': 'Bachelor Of Science In Business Administration (Accounting Option)', 'institution_id': 1, 'student_count': 4222}, {'program_name': 'Bachelor Of Science In Banking And Finance', 'institution_id': 1, 'student_count': 3910}, {'program_name': 'Bachelor Of Science In Business Administration (Marketing Option)', 'institution_id': 1, 'student_count': 3435}, {'program_name': 'Bachelor Of Science In Management Studies', 'institution_id': 1, 'student_count': 2599}, {'program_name': 'Bachelor Of Science In Physician Assistantship', 'institution_id': 1, 'student_count': 2449}, {'program_name': 'Bachelor Of Science In Nursing', 'institution_id': 1, 'student_count': 2275}, {'program_name': 'Bachelor Of Laws (Post-Degree Weekend)', 'institution_id': 1, 'student_count': 2072}, {'program_name': 'Bachelor Of Pharmacy', 'institution_id': 1, 'student_count': 1713}, {'program_name': 'Bachelor Of Science In Nursing (Professional)(Weekend)', 'institution_id': 1, 'student_count': 1485}, {'program_name': 'Bachelor Of Science In Nursing (Professional)', 'institution_id': 1, 'student_count': 1462}, {'program_name': 'Bachelor Of Laws', 'institution_id': 1, 'student_count': 1251}, {'program_name': 'Doctor Of Pharmacy', 'institution_id': 1, 'student_count': 1237}, {'program_name': 'Bachelor Of Architecture (5 Years)', 'institution_id': 1, 'student_count': 1222}, {'program_name': 'Bachelor Of Arts In Communication Studies', 'institution_id': 1, 'student_count': 1128}, {'program_name': 'MBA (Finance)', 'institution_id': 1, 'student_count': 1117}, {'program_name': 'Bachelor Of Science In Business Administration (Human Resource Management Option) (Weekend)', 'institution_id': 1, 'student_count': 1019}, {'program_name': 'Bachelor Of Science In Economics', 'institution_id': 1, 'student_count': 862}, {'program_name': 'Bachelor Of Science In Management Studies (Weekend)', 'institution_id': 1, 'student_count': 847}, {'program_name': 'Bachelor Of Science In Business Administration (Accounting Option) (Weekend)', 'institution_id': 1, 'student_count': 824}, {'program_name': 'Bachelor Of Arts In Theology', 'institution_id': 1, 'student_count': 661}, {'program_name': 'Bachelor Of Science In Business Administration (Marketing Option) (Weekend)', 'institution_id': 1, 'student_count': 617}, {'program_name': 'MBA (General Management)', 'institution_id': 1, 'student_count': 563}, {'program_name': 'Bachelor Of Science In Information Technology', 'institution_id': 1, 'student_count': 537}, {'program_name': 'Bachelor Of Science In Computer Science', 'institution_id': 1, 'student_count': 527}, {'program_name': 'DIPLOMA IN BUSINESS', 'institution_id': 1, 'student_count': 490}, {'program_name': 'MBA (Human Resource Management)', 'institution_id': 1, 'student_count': 482}, {'program_name': 'Bachelor Of Science In Environment And Development Studies', 'institution_id': 1, 'student_count': 472}, {'program_name': 'Bachelor Of Laws (Post-Degree Evening)', 'institution_id': 1, 'student_count': 465}, {'program_name': 'Bachelor Of Public Health', 'institution_id': 1, 'student_count': 374}, {'program_name': 'Bachelor Of Science In Agribusiness Management', 'institution_id': 1, 'student_count': 352}, {'program_name': 'Bachelor Of Science In Banking And Finance (Weekend)', 'institution_id': 1, 'student_count': 335}, {'program_name': 'Bachelor Of Science In Civil Engineering', 'institution_id': 1, 'student_count': 328}, {'program_name': 'MBA (Marketing)', 'institution_id': 1, 'student_count': 318}, {'program_name': 'DIPLOMA IN INFORMATION AND DIGITAL TECHNOLOGIES', 'institution_id': 1, 'student_count': 265}, {'program_name': 'Bachelor Of Arts In English', 'institution_id': 1, 'student_count': 230}, {'program_name': 'Master of Philosophy (Religious Studies)', 'institution_id': 1, 'student_count': 221}, {'program_name': 'DIPLOMA IN HEALTH AND SOCIAL CARE', 'institution_id': 1, 'student_count': 177}, {'program_name': 'Master Of Arts In Sacred Ministry', 'institution_id': 1, 'student_count': 175}, {'program_name': 'Master Of Arts (Religious Studies)', 'institution_id': 1, 'student_count': 166}, {'program_name': 'Bachelor Of Science In Agribusiness Management (Weekend)', 'institution_id': 1, 'student_count': 156}, {'program_name': 'Bachelor Of Science In Psychology', 'institution_id': 1, 'student_count': 147}, {'program_name': 'DIPLOMA IN LAW', 'institution_id': 1, 'student_count': 147}, {'program_name': 'Bachelor Of Arts In Family Counseling', 'institution_id': 1, 'student_count': 106}, {'program_name': 'Bachelor Of Arts In French', 'institution_id': 1, 'student_count': 103}, {'program_name': 'Bachelor Of Arts In Social Work', 'institution_id': 1, 'student_count': 76}, {'program_name': 'Executive Masters In Leadership And Governance', 'institution_id': 1, 'student_count': 66}, {'program_name': 'Bachelor Of Science In Real Estate', 'institution_id': 1, 'student_count': 64}, {'program_name': 'Bachelor Of Arts In Sociology', 'institution_id': 1, 'student_count': 60}, {'program_name': 'MASTER OF PHILOSOPHY IN THEOLOGY', 'institution_id': 1, 'student_count': 60}, {'program_name': 'Bachelor Of Arts In Church Administration', 'institution_id': 1, 'student_count': 58}, {'program_name': 'Doctor Of Pharmacy Top-Up', 'institution_id': 1, 'student_count': 50}, {'program_name': 'Bachelor Of Science In Graphic Design', 'institution_id': 1, 'student_count': 41}, {'program_name': 'M.Sc Marketing, Social and Organisational Research', 'institution_id': 1, 'student_count': 34}, {'program_name': 'DIPLOMA IN LAW (FOL)', 'institution_id': 1, 'student_count': 28}, {'program_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'institution_id': 1, 'student_count': 27}, {'program_name': 'MASTER OF PHILOSOPHY IN GUIDANCE AND COUNSELLING', 'institution_id': 1, 'student_count': 26}, {'program_name': 'MASTER OF SCIENCE IN ACCOUNTING', 'institution_id': 1, 'student_count': 24}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION - PROJECT MANAGEMENT', 'institution_id': 1, 'student_count': 23}, {'program_name': 'MASTER OF ARTS IN COMMUNICATION STUDIES', 'institution_id': 1, 'student_count': 19}, {'program_name': 'Master Of Philosophy In Economics', 'institution_id': 1, 'student_count': 18}, {'program_name': 'Bachelor Of Science In Planning', 'institution_id': 1, 'student_count': 17}, {'program_name': 'Bachelor of Arts in Christian Education', 'institution_id': 1, 'student_count': 16}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION - MARKETING', 'institution_id': 1, 'student_count': 16}, {'program_name': 'Bachelor Of Science In Interior Design', 'institution_id': 1, 'student_count': 15}, {'program_name': 'Executive Masters In Leadership And Governance (Law)', 'institution_id': 1, 'student_count': 15}, {'program_name': 'Master Of Public Health', 'institution_id': 1, 'student_count': 14}, {'program_name': 'MASTER OF ARTS IN DEVELOPMENT POLICY', 'institution_id': 1, 'student_count': 13}, {'program_name': 'Master of Philosophy Preparatory Studies', 'institution_id': 1, 'student_count': 13}, {'program_name': 'MASTER OF ARTS IN EDUCATION', 'institution_id': 1, 'student_count': 12}, {'program_name': 'MASTER OF PHILOSOPHY IN COMMUNICATION STUDIES', 'institution_id': 1, 'student_count': 12}, {'program_name': 'Bachelor of Science in Logistics Management', 'institution_id': 1, 'student_count': 10}, {'program_name': 'Bachelor Of Science In Fashion Design', 'institution_id': 1, 'student_count': 9}, {'program_name': 'DIPLOMA IN BUSINESS AND MANAGEMENT', 'institution_id': 1, 'student_count': 9}, {'program_name': 'Bachelor Of Science In Business Administration (Human Resource Management Option) (Distance)', 'institution_id': 1, 'student_count': 6}, {'program_name': 'Master Of Science In Marketing Research', 'institution_id': 1, 'student_count': 6}, {'program_name': 'MASTER OF ARTS IN EDUCATIONAL LEADERSHIP AND ADMINISTRATION', 'institution_id': 1, 'student_count': 3}, {'program_name': 'MASTER OF PHILOSOPHY IN ACCOUNTING', 'institution_id': 1, 'student_count': 3}, {'program_name': 'Bachelor of Arts in Business French', 'institution_id': 1, 'student_count': 2}, {'program_name': 'Bachelor Of Science In Landscape Design', 'institution_id': 1, 'student_count': 2}, {'program_name': 'MASTER OF PHILOSOPHY IN DEVELOPMENT POLICY', 'institution_id': 1, 'student_count': 2}, {'program_name': 'MASTER OF PHILOSOPHY IN EDUCATION', 'institution_id': 1, 'student_count': 2}, {'program_name': 'Bachelor Of Education (Social Sciences)', 'institution_id': 1, 'student_count': 1}, {'program_name': 'BACHELOR OF SCIENCE IN ARCHITECTURE (4 YEARS)', 'institution_id': 1, 'student_count': 1}, {'program_name': 'CERTIFICATE FOR SELECTED UNDERGRADUATE COURSES IN ECONOMICS', 'institution_id': 1, 'student_count': 1}, {'program_name': 'DIPLOMA IN AWARD IN TRAINING & EDUCATION', 'institution_id': 1, 'student_count': 1}, {'program_name': 'DIPLOMA IN MANAGMENT (OPERATIONS MANAGEMENT)', 'institution_id': 1, 'student_count': 1}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION - AGRIBUSINESS', 'institution_id': 1, 'student_count': 1}, {'program_name': 'Bachelor Of Medicine & Bachelor Of Surgery', 'institution_id': 2, 'student_count': 114}, {'program_name': 'PROFESSIONAL LAW COURSE', 'institution_id': 3, 'student_count': 12236}, {'program_name': 'POST CALL LAW COURSE', 'institution_id': 3, 'student_count': 510}, {'program_name': 'Preliminary Law Course', 'institution_id': 3, 'student_count': 163}, {'program_name': 'Ghana Legal Systems / Constitutional Law Course', 'institution_id': 3, 'student_count': 29}, {'program_name': 'COMMONWEALTH LEGISLATIVE DRAFTING COURSE', 'institution_id': 3, 'student_count': 2}, {'program_name': 'MASTER OF THEOLOGY (AFRICAN CHRISTIANITY) - REGULAR', 'institution_id': 4, 'student_count': 126}, {'program_name': 'MASTER OF ARTS THEOLOGY AND MISSION', 'institution_id': 4, 'student_count': 60}, {'program_name': 'MASTER OF ARTS BIBLICAL STUDIES', 'institution_id': 4, 'student_count': 47}, {'program_name': 'MASTER OF ARTS (AUGUST)', 'institution_id': 4, 'student_count': 36}, {'program_name': 'MASTER OF THEOLOGY (AFRICAN CHRISTIANITY)-INTENSIVE', 'institution_id': 4, 'student_count': 27}, {'program_name': 'MASTER OF ARTS HOLISTIC MISSION AND DEVELOPMENT', 'institution_id': 4, 'student_count': 24}, {'program_name': 'MASTER OF ARTS LEADERSHIP', 'institution_id': 4, 'student_count': 17}, {'program_name': 'DOCTOR OF PHILOSOPHY (PHD) THEOLOGY)', 'institution_id': 4, 'student_count': 15}, {'program_name': 'MASTER OF ARTS (BIBLE AND SCIENCE)', 'institution_id': 4, 'student_count': 15}, {'program_name': 'MASTER OF ARTS PENTECOSTAL STUDIES', 'institution_id': 4, 'student_count': 4}, {'program_name': 'MASTER OF ARTS WORLD CHRISTIANITY OPTION', 'institution_id': 4, 'student_count': 4}, {'program_name': 'DOCTOR OF THEOLOGY (AFRICAN CHRISTIANITY) -INTENSIVE', 'institution_id': 4, 'student_count': 2}, {'program_name': 'MASTER OF THEOLOGY (BIBLE TRANSLATION AND INTERPRETATION)', 'institution_id': 4, 'student_count': 1}, {'program_name': 'Diploma in Public Relations', 'institution_id': 5, 'student_count': 3444}, {'program_name': 'Bachelor Of Science (Physician Assistantship)', 'institution_id': 5, 'student_count': 2208}, {'program_name': 'Bachelor Of Science (Nursing)', 'institution_id': 5, 'student_count': 2167}, {'program_name': 'Master Of Education (Educational Studies)', 'institution_id': 5, 'student_count': 1128}, {'program_name': 'Bachelor Of Science (Midwifery)', 'institution_id': 5, 'student_count': 1124}, {'program_name': 'Master Of Public Health', 'institution_id': 5, 'student_count': 851}, {'program_name': 'Bachelor of Science (Business Administration)', 'institution_id': 5, 'student_count': 733}, {'program_name': 'Bachelor Of Laws (3-Years)', 'institution_id': 5, 'student_count': 529}, {'program_name': 'Bachelor Of Science (Information And Communication Technology)', 'institution_id': 5, 'student_count': 398}, {'program_name': 'Bachelor of Science in Business Administration (General Management)', 'institution_id': 5, 'student_count': 382}, {'program_name': 'Master Of Arts (International Development Studies)', 'institution_id': 5, 'student_count': 367}, {'program_name': 'Bachelor of Science in Business Administration (Human Resources Management)', 'institution_id': 5, 'student_count': 354}, {'program_name': 'Master Of Philosophy (Educational Studies)', 'institution_id': 5, 'student_count': 314}, {'program_name': 'Bachelor Of Science (Environmental And Natural Resources Management)', 'institution_id': 5, 'student_count': 294}, {'program_name': 'Certificate In Occupational Safety, Health And Environmental Management', 'institution_id': 5, 'student_count': 290}, {'program_name': 'Certificate In Health Services Management And Administration', 'institution_id': 5, 'student_count': 274}, {'program_name': 'Bachelor of Science in Business Administration (Accounting and Finance)', 'institution_id': 5, 'student_count': 231}, {'program_name': 'Certificate In Public Relations And Strategic Communication', 'institution_id': 5, 'student_count': 218}, {'program_name': 'Certificate In Project Management', 'institution_id': 5, 'student_count': 217}, {'program_name': 'Bachelor Of Science (Agribusiness)', 'institution_id': 5, 'student_count': 215}, {'program_name': 'Master Of Science (Financial Risk Management)', 'institution_id': 5, 'student_count': 203}, {'program_name': 'Master Of Education (Educational Studies) Trim', 'institution_id': 5, 'student_count': 194}, {'program_name': 'Bachelor of Arts (Rural and Community Development)', 'institution_id': 5, 'student_count': 172}, {'program_name': 'Certificate In Governance And Leadership', 'institution_id': 5, 'student_count': 154}, {'program_name': 'Certificate In Public Service Leadership And Governance', 'institution_id': 5, 'student_count': 150}, {'program_name': 'Master Of Science (Environmental Health And Sanitation)', 'institution_id': 5, 'student_count': 149}, {'program_name': 'Certificate In Monitoring And Evaluation', 'institution_id': 5, 'student_count': 148}, {'program_name': 'Certificate In Labour Law And Human Resource  Policy', 'institution_id': 5, 'student_count': 146}, {'program_name': 'Bachelor Of Science In Public Health', 'institution_id': 5, 'student_count': 133}, {'program_name': 'Certificate In Child And Adolescent Psychotherapy', 'institution_id': 5, 'student_count': 117}, {'program_name': 'BSc. Information & Communication Technology (Networking & Telecommunications)', 'institution_id': 5, 'student_count': 111}, {'program_name': 'Certificate In Cyber Security Management', 'institution_id': 5, 'student_count': 107}, {'program_name': 'MSc. Information And Communication Technology (ICT) Management', 'institution_id': 5, 'student_count': 93}, {'program_name': 'Certificate In Emotional Intelligence', 'institution_id': 5, 'student_count': 91}, {'program_name': 'Bachelor Of Laws (4-Years)', 'institution_id': 5, 'student_count': 90}, {'program_name': 'Bachelor Of Education (Social Studies)', 'institution_id': 5, 'student_count': 87}, {'program_name': 'Certificate In Advanced Research And Academic Publication', 'institution_id': 5, 'student_count': 79}, {'program_name': 'Bachelor Of Science (Mathematics)', 'institution_id': 5, 'student_count': 77}, {'program_name': 'Master Of Science (Natural Resources Management)', 'institution_id': 5, 'student_count': 77}, {'program_name': 'Bachelor of Science (International Development)', 'institution_id': 5, 'student_count': 75}, {'program_name': 'Bachelor of Science in Business Administration (Marketing)', 'institution_id': 5, 'student_count': 74}, {'program_name': 'Certificate In Contemporary Customer Service Management', 'institution_id': 5, 'student_count': 57}, {'program_name': 'Bachelor of Science in Business Administration (Banking and Finance)', 'institution_id': 5, 'student_count': 51}, {'program_name': 'Bachelor Of Science (Computer Engineering)', 'institution_id': 5, 'student_count': 41}, {'program_name': 'Pre-University', 'institution_id': 5, 'student_count': 37}, {'program_name': 'BSc. Information & Communication Technology (Cybersecurity)', 'institution_id': 5, 'student_count': 34}, {'program_name': 'Certificate In Credit And Portfolio Risk Management', 'institution_id': 5, 'student_count': 33}, {'program_name': 'BSc. Information & Communication Technology (Software Engineering)', 'institution_id': 5, 'student_count': 31}, {'program_name': 'Doctor Of Philosophy In Environment & Development', 'institution_id': 5, 'student_count': 28}, {'program_name': 'Bachelor Of Science (Business Economics)', 'institution_id': 5, 'student_count': 26}, {'program_name': 'Master Of Philosophy (Educational Studies) - 5 Semesters', 'institution_id': 5, 'student_count': 23}, {'program_name': 'BSc. Information & Communication Technology (Information Security)', 'institution_id': 5, 'student_count': 22}, {'program_name': 'Certificate In Intellectual Property Management', 'institution_id': 5, 'student_count': 18}, {'program_name': 'BSc. Information & Communication Technology (Business Information Systems)', 'institution_id': 5, 'student_count': 14}, {'program_name': 'Diploma In Public Administration', 'institution_id': 5, 'student_count': 12}, {'program_name': 'ATHE Level 3 Diploma In Business', 'institution_id': 5, 'student_count': 10}, {'program_name': 'Higher National Diploma In Information And Communication Technology', 'institution_id': 5, 'student_count': 10}, {'program_name': 'ATHE Level 3 Diploma In Information And Digital Technologies', 'institution_id': 5, 'student_count': 8}, {'program_name': 'Diploma In Business Administration', 'institution_id': 5, 'student_count': 5}, {'program_name': 'BSc. Information & Communication Technology (Data Communication & Networking )', 'institution_id': 5, 'student_count': 4}, {'program_name': 'Master Of Divinity', 'institution_id': 5, 'student_count': 1}, {'program_name': 'A1 CERTIFICATE IN GERMAN (ELEMENTARY)', 'institution_id': 7, 'student_count': 2214}, {'program_name': 'DIPLOMA IN ARABIC', 'institution_id': 7, 'student_count': 1553}, {'program_name': 'CERTIFICATE IN ENGLISH (BEGINNER)', 'institution_id': 7, 'student_count': 1308}, {'program_name': 'CERTIFICATE IN ENGLISH (INTERMEDIATE)', 'institution_id': 7, 'student_count': 1247}, {'program_name': 'A2 CERTIFICATE IN ENGLISH (BEGINNER)', 'institution_id': 7, 'student_count': 1199}, {'program_name': 'B1 CERTIFICATE IN ENGLISH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 1099}, {'program_name': 'A1 CERTIFICATE IN ENGLISH (ELEMENTARY)', 'institution_id': 7, 'student_count': 1085}, {'program_name': 'CERTIFICATE IN FRENCH (BEGINNER)', 'institution_id': 7, 'student_count': 893}, {'program_name': 'A1 CERTIFICATE IN FRENCH (ELEMENTARY)', 'institution_id': 7, 'student_count': 824}, {'program_name': 'B2 CERTIFICATE IN ENGLISH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 761}, {'program_name': 'BACHELOR OF ARTS (TRANSLATION)', 'institution_id': 7, 'student_count': 687}, {'program_name': 'C1 CERTIFICATE IN ENGLISH (ADVANCED)', 'institution_id': 7, 'student_count': 536}, {'program_name': 'HIGHER  NATIONAL DIPLOMA IN BILINGUAL SECRETARYSHIP', 'institution_id': 7, 'student_count': 507}, {'program_name': 'CERTIFICATE IN ENGLISH (PROFICIENCY)', 'institution_id': 7, 'student_count': 442}, {'program_name': 'A2 CERTIFICATE IN FRENCH (BEGINNER)', 'institution_id': 7, 'student_count': 385}, {'program_name': 'CERTIFICATE IN FRENCH (INTERMEDIATE)', 'institution_id': 7, 'student_count': 364}, {'program_name': 'CERTIFICATE IN SPANISH (BEGINNER)', 'institution_id': 7, 'student_count': 335}, {'program_name': 'CERTIFICATE IN ENGLISH (ADVANCED)', 'institution_id': 7, 'student_count': 306}, {'program_name': 'A1 CERTIFICATE IN SPANISH (ELEMENTARY)', 'institution_id': 7, 'student_count': 293}, {'program_name': 'CERTIFICATE IN ENGLISH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 230}, {'program_name': 'CERTIFICATE IN GERMAN (BEGINNER)', 'institution_id': 7, 'student_count': 218}, {'program_name': 'CERTIFICATE IN ARABIC (BEGINNER)', 'institution_id': 7, 'student_count': 187}, {'program_name': 'B1 CERTIFICATE IN FRENCH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 171}, {'program_name': 'CERTIFICATE IN ENGLISH(ELEMENTARY BEGINNER)', 'institution_id': 7, 'student_count': 170}, {'program_name': 'CERTIFICATE IN ENGLISH (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 167}, {'program_name': 'CERTIFICATE IN ARABIC (ADVANCED)', 'institution_id': 7, 'student_count': 163}, {'program_name': 'CERTIFICATE IN ENGLISH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 160}, {'program_name': 'CERTIFICATE IN ARABIC (INTERMEDIATE)', 'institution_id': 7, 'student_count': 159}, {'program_name': 'A1 CERTIFICATE IN ARABIC (ELEMENTARY)', 'institution_id': 7, 'student_count': 157}, {'program_name': 'A2 CERTIFICATE IN GERMAN (BEGINNER)', 'institution_id': 7, 'student_count': 154}, {'program_name': 'BACHELOR OF ARTS (ARABIC EDUCATION)', 'institution_id': 7, 'student_count': 148}, {'program_name': 'CERTIFICATE IN FRENCH (PROFICIENCY)', 'institution_id': 7, 'student_count': 143}, {'program_name': 'CERTIFICATE IN FRENCH (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 131}, {'program_name': 'B2 CERTIFICATE IN FRENCH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 100}, {'program_name': 'CERTIFICATE IN FRENCH (ADVANCED)', 'institution_id': 7, 'student_count': 99}, {'program_name': 'A2 CERTIFICATE IN SPANISH (BEGINNER)', 'institution_id': 7, 'student_count': 89}, {'program_name': 'A1 CERTIFICATE IN CHINESE(ELEMENTARY)', 'institution_id': 7, 'student_count': 85}, {'program_name': 'CERTIFICATE IN ENGLISH (INTENSIVE ADVANCED)', 'institution_id': 7, 'student_count': 80}, {'program_name': 'BA. BUSINESS AND BILINGUAL ADMINISTRATION', 'institution_id': 7, 'student_count': 79}, {'program_name': 'C1 CERTIFICATE IN FRENCH (ADVANCED)', 'institution_id': 7, 'student_count': 73}, {'program_name': 'CERTIFICATE IN SPANISH (INTERMEDIATE)', 'institution_id': 7, 'student_count': 65}, {'program_name': 'B1 CERTIFICATE IN ARABIC (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 60}, {'program_name': 'C1 CERTIFICATE IN ARABIC (ADVANCED)', 'institution_id': 7, 'student_count': 53}, {'program_name': 'CERTIFICATE IN SPANISH (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 51}, {'program_name': 'CERTIFICATE IN ARABIC (PROFICIENCY)', 'institution_id': 7, 'student_count': 50}, {'program_name': 'A2 CERTIFICATE IN ARABIC (BEGINNER)', 'institution_id': 7, 'student_count': 48}, {'program_name': 'B2 CERTIFICATE IN ARABIC (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 41}, {'program_name': 'CERTIFICATE IN GERMAN (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 38}, {'program_name': 'CERTIFICATE IN GERMAN (INTERMEDIATE)', 'institution_id': 7, 'student_count': 29}, {'program_name': 'B1 CERTIFICATE IN SPANISH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 28}, {'program_name': 'CERTIFICATE IN FRENCH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 28}, {'program_name': 'CERTIFICATE IN PORTUGUESE (BEGINNER)', 'institution_id': 7, 'student_count': 23}, {'program_name': 'CERTIFICATE IN FRENCH (INTENSIVE ADVANCED)', 'institution_id': 7, 'student_count': 18}, {'program_name': 'A2 CERTIFICATE IN CHINESE (BEGINNER)', 'institution_id': 7, 'student_count': 17}, {'program_name': 'CERTIFICATE IN SPANISH (PROFICIENCY)', 'institution_id': 7, 'student_count': 16}, {'program_name': 'A1 CERTIFICATE IN PORTUGUESE (ELEMENTARY)', 'institution_id': 7, 'student_count': 14}, {'program_name': 'CERTIFICATE IN CHINESE (BEGINNER)', 'institution_id': 7, 'student_count': 13}, {'program_name': 'CERTIFICATE IN FRENCH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 13}, {'program_name': 'CERTIFICATE IN GERMAN (PROFICIENCY)', 'institution_id': 7, 'student_count': 12}, {'program_name': 'DIPLOMA IN BILINGUAL BUSINESS ADMINISTRATION', 'institution_id': 7, 'student_count': 11}, {'program_name': 'CERTIFICATE IN ARABIC(INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 9}, {'program_name': 'CERTIFICATE IN GERMAN (ADVANCED)', 'institution_id': 7, 'student_count': 9}, {'program_name': 'B2 CERTIFICATE IN SPANISH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 8}, {'program_name': 'C1 CERTIFICATE IN SPANISH (ADVANCED)', 'institution_id': 7, 'student_count': 8}, {'program_name': 'CERTIFICATE IN PORTUGUESE (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 8}, {'program_name': 'CERTIFICATE IN ENGLISH (INTENSIVE ELEMENTARY BEGINNER)', 'institution_id': 7, 'student_count': 7}, {'program_name': 'CERTIFICATE IN RUSSIA (BEGINNER)', 'institution_id': 7, 'student_count': 7}, {'program_name': 'CERTIFICATE IN SPANISH (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 7}, {'program_name': 'B1 CERTIFICATE IN GERMAN (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 6}, {'program_name': 'CERTIFICATE IN SPANISH (INTENSIVE ADVANCED)', 'institution_id': 7, 'student_count': 5}, {'program_name': 'CERTIFICATE IN SPANISH (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 5}, {'program_name': 'DIPLOMA IN FRENCH', 'institution_id': 7, 'student_count': 5}, {'program_name': 'A1 CERTIFICATE IN RUSSIAN (ELEMENTARY)', 'institution_id': 7, 'student_count': 4}, {'program_name': 'CERTIFICATE IN FRENCH', 'institution_id': 7, 'student_count': 4}, {'program_name': 'CERTIFICATE IN SPANISH (ADVANCED)', 'institution_id': 7, 'student_count': 4}, {'program_name': 'A2 CERTIFICATE IN PORTUGUESE (BEGINNER)', 'institution_id': 7, 'student_count': 3}, {'program_name': 'B1 CERTIFICATE IN CHINESE (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 3}, {'program_name': 'CERTIFICATE IN GERMAN (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 3}, {'program_name': 'DIPLOMA IN BUSINESS STUDIES', 'institution_id': 7, 'student_count': 3}, {'program_name': 'EFFECTIVE COMMUNICATION SKILLS', 'institution_id': 7, 'student_count': 3}, {'program_name': 'ONE YEAR HND IN BILINGUAL SECRETARYSHIP', 'institution_id': 7, 'student_count': 3}, {'program_name': 'B2 CERTIFICATE IN GERMAN (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (ENGLISH)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'CERTIFICATE IN ARABIC (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'CERTIFICATE IN CHINESE (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'CERTIFICATE IN GERMAN (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'CERTIFICATE IN PORTUGUESE (INTENSIVE ADVANCED)', 'institution_id': 7, 'student_count': 2}, {'program_name': 'SPECIAL COURSE IN TRANSLATION', 'institution_id': 7, 'student_count': 2}, {'program_name': 'B1 CERTIFICATE IN PORTUGUESE (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'B1 CERTIFICATE IN RUSSIAN (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'BACHELOR OF DUMMY', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN ARABIC (INTENSIVE ADVANCED)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN CHINESE (ADVANCED)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN CHINESE (INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN ENGLISH (SHORT INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN ENGLISH(SHORT COURSE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN FRENCH (SHORT BEGINNER)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN GERMAN', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN PORTUGUESE (INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN PORTUGUESE (LOWER INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN PORTUGUESE (UPPER INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN RUSSIAN (INTENSIVE BEGINNER)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'CERTIFICATE IN RUSSIAN (INTERMEDIATE)', 'institution_id': 7, 'student_count': 1}, {'program_name': 'HIGHER NATIONAL DIPLOMA IN MONOLINGUAL SECRETARYSHIP', 'institution_id': 7, 'student_count': 1}, {'program_name': 'BSc. Business Information Technology', 'institution_id': 8, 'student_count': 52}, {'program_name': 'BSc. Business Administration', 'institution_id': 8, 'student_count': 49}, {'program_name': 'BSc. Computer Science', 'institution_id': 8, 'student_count': 38}, {'program_name': 'Diploma In Business Information Technology', 'institution_id': 8, 'student_count': 18}, {'program_name': 'MSc. Health Services Management & Leadership', 'institution_id': 8, 'student_count': 15}, {'program_name': 'Diploma In Business Administration', 'institution_id': 8, 'student_count': 7}, {'program_name': 'MSc. Information Technology Management', 'institution_id': 8, 'student_count': 2}, {'program_name': 'MSc. Business Information Technology', 'institution_id': 8, 'student_count': 1}, {'program_name': 'Diploma In Registered Community Nursing', 'institution_id': 9, 'student_count': 910}, {'program_name': 'DIPLOMA IN REGISTERED PUBLIC HEALTH NURSING', 'institution_id': 9, 'student_count': 228}, {'program_name': 'Bachelor Of Business Administration (Human Resource Management)', 'institution_id': 10, 'student_count': 4091}, {'program_name': 'Bachelor Of Business Administration (Accounting)', 'institution_id': 10, 'student_count': 2996}, {'program_name': 'Bachelor Of Science (Information Technology)', 'institution_id': 10, 'student_count': 2739}, {'program_name': 'Bachelor Of Business Administration (Marketing)', 'institution_id': 10, 'student_count': 2556}, {'program_name': 'Bachelor Of Science(General Nursing)', 'institution_id': 10, 'student_count': 2007}, {'program_name': 'Bachelor Of Business Administration (Banking & Finance)', 'institution_id': 10, 'student_count': 1772}, {'program_name': 'Bachelor Of Business Administration (Management Studies)', 'institution_id': 10, 'student_count': 1578}, {'program_name': 'Bachelor Of Science (Procurement And Supply Chain Management)', 'institution_id': 10, 'student_count': 1031}, {'program_name': 'Bachelor Of Arts (Psychology)', 'institution_id': 10, 'student_count': 1012}, {'program_name': 'Master Of Business Administration (Finance)', 'institution_id': 10, 'student_count': 667}, {'program_name': 'Master of Business Administration', 'institution_id': 10, 'student_count': 658}, {'program_name': 'Bachelor Of Science(Nursing) Direct Entry', 'institution_id': 10, 'student_count': 650}, {'program_name': 'Bachelor of Business Administration (Human Resource Management) (Weekend)', 'institution_id': 10, 'student_count': 632}, {'program_name': 'Diploma In Information Technology', 'institution_id': 10, 'student_count': 561}, {'program_name': 'Bachelor of Business Administration(Accounting) (Weekend)', 'institution_id': 10, 'student_count': 544}, {'program_name': 'MA Guidance And Counselling', 'institution_id': 10, 'student_count': 529}, {'program_name': 'Master Of Business Administration (Human Resource Management)', 'institution_id': 10, 'student_count': 505}, {'program_name': 'Bachelor Of Science (Economics)', 'institution_id': 10, 'student_count': 423}, {'program_name': 'Bachelor of Business Administration (Marketing) (Weekend)', 'institution_id': 10, 'student_count': 392}, {'program_name': 'Bachelor of Business Administration (Management Studies)(Weekend)', 'institution_id': 10, 'student_count': 376}, {'program_name': 'Bachelor Of Science General Agric', 'institution_id': 10, 'student_count': 336}, {'program_name': 'Bachelor Of Science(Nursing) January', 'institution_id': 10, 'student_count': 319}, {'program_name': 'Master Of Business Administration (Marketing)', 'institution_id': 10, 'student_count': 308}, {'program_name': 'Bachelor Of Arts (Social Work)', 'institution_id': 10, 'student_count': 298}, {'program_name': 'Diploma Registered General Nursing', 'institution_id': 10, 'student_count': 252}, {'program_name': 'PROFESSIONAL CERTIFICATE IN PSYCHOLOGY', 'institution_id': 10, 'student_count': 241}, {'program_name': 'Certificate in Information Technology', 'institution_id': 10, 'student_count': 235}, {'program_name': 'Master Of Philosophy (Guidance & Counselling)', 'institution_id': 10, 'student_count': 230}, {'program_name': 'Bachelor Of Science(Nursing) Direct Entry (January)', 'institution_id': 10, 'student_count': 216}, {'program_name': 'Bachelor of Arts (English Studies)', 'institution_id': 10, 'student_count': 207}, {'program_name': 'Bachelor of Business Administration (Banking & Finance)(Weekend)', 'institution_id': 10, 'student_count': 201}, {'program_name': 'Bachelor of Science in Economics and Statistics', 'institution_id': 10, 'student_count': 196}, {'program_name': 'Bachelor Of Science In Public Health Nursing', 'institution_id': 10, 'student_count': 187}, {'program_name': 'Master Of Business Administration (Accounting)', 'institution_id': 10, 'student_count': 175}, {'program_name': 'Bachelor Of Arts Communication Studies', 'institution_id': 10, 'student_count': 172}, {'program_name': 'Master Of Education Degree(Education Leadership And Innovation)', 'institution_id': 10, 'student_count': 153}, {'program_name': 'Certificate in Church Music', 'institution_id': 10, 'student_count': 144}, {'program_name': 'Diploma In Business And Management', 'institution_id': 10, 'student_count': 139}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'institution_id': 10, 'student_count': 136}, {'program_name': 'BACHELOR OF SCIENCE BIOLOGICAL ENGINEERING', 'institution_id': 10, 'student_count': 136}, {'program_name': 'Diploma In General Agric', 'institution_id': 10, 'student_count': 128}, {'program_name': 'Bachelor of Science General Agric (Weekend)', 'institution_id': 10, 'student_count': 123}, {'program_name': 'Master Of Arts (Entrepreneurship And Corporate Strategy)', 'institution_id': 10, 'student_count': 116}, {'program_name': 'Master Of Education Degree(Education Management And Practice)', 'institution_id': 10, 'student_count': 114}, {'program_name': 'Certificate In Agribusiness', 'institution_id': 10, 'student_count': 113}, {'program_name': 'Bachelor of Science(Information Technology)(January)', 'institution_id': 10, 'student_count': 106}, {'program_name': 'Bachelor of Science (Economics & Mathematics-Statistics)', 'institution_id': 10, 'student_count': 104}, {'program_name': 'Business Administration', 'institution_id': 10, 'student_count': 104}, {'program_name': 'Bachelor Of Science In Public Health Nursing(Direct Entry)', 'institution_id': 10, 'student_count': 92}, {'program_name': 'Master Of Science (Accounting And Finance)', 'institution_id': 10, 'student_count': 91}, {'program_name': 'Bachelor of Business Administration(Human Resource Management)(January)', 'institution_id': 10, 'student_count': 88}, {'program_name': 'Certificate in Environmental Management and Entrepreneurship', 'institution_id': 10, 'student_count': 88}, {'program_name': 'Bachelor of Science (Mathematics & Statistics)', 'institution_id': 10, 'student_count': 81}, {'program_name': 'Certificate in Business Administration', 'institution_id': 10, 'student_count': 78}, {'program_name': 'Certificate In Horticulture', 'institution_id': 10, 'student_count': 75}, {'program_name': 'Bachelor of Arts in Music', 'institution_id': 10, 'student_count': 65}, {'program_name': 'Bachelor Of Science(Procurement And Supply Chain Management)(January)', 'institution_id': 10, 'student_count': 65}, {'program_name': 'Bachelor of Arts (Religion Ethics & Psychology)', 'institution_id': 10, 'student_count': 59}, {'program_name': 'Bachelor of Business Administration(Management Studies)(January)', 'institution_id': 10, 'student_count': 59}, {'program_name': 'Diploma in Registered General Nursing', 'institution_id': 10, 'student_count': 56}, {'program_name': 'MASTER OF PUBLIC POLICY AND GOVERNANCE', 'institution_id': 10, 'student_count': 55}, {'program_name': 'Bachelor Of Arts (French)', 'institution_id': 10, 'student_count': 54}, {'program_name': 'Bachelor Of Science In Public Health Nursing Direct(January))', 'institution_id': 10, 'student_count': 53}, {'program_name': 'Certificate In Agroprocessing', 'institution_id': 10, 'student_count': 53}, {'program_name': 'Bachelor Of Arts (Social Work)January', 'institution_id': 10, 'student_count': 51}, {'program_name': 'Master Of Arts Small Business Management', 'institution_id': 10, 'student_count': 51}, {'program_name': 'Bachelor of Science in Actuarial Science', 'institution_id': 10, 'student_count': 49}, {'program_name': 'Masters of Business Administration(Finance)(Combined)', 'institution_id': 10, 'student_count': 49}, {'program_name': 'ADVANCE CERTIFICATE IN BUSINESS ADMINISTRATION', 'institution_id': 10, 'student_count': 47}, {'program_name': 'Certificate in Information Technology (January)', 'institution_id': 10, 'student_count': 45}, {'program_name': 'Master Of Arts Entrepreneurship', 'institution_id': 10, 'student_count': 45}, {'program_name': 'Master of Philosophy (Statistics)', 'institution_id': 10, 'student_count': 44}, {'program_name': 'MASTER OF SCIENCE (ACCOUNTING AND FINANCE) JANUARY', 'institution_id': 10, 'student_count': 44}, {'program_name': 'Master Of Education Degree(Education Leadership And Innovation)(January)', 'institution_id': 10, 'student_count': 43}, {'program_name': 'Bachelor of Science  (Procurement and Supply Chain Management)(Weekend)', 'institution_id': 10, 'student_count': 42}, {'program_name': 'Bachelor of Business Administration(Accounting)(January)', 'institution_id': 10, 'student_count': 38}, {'program_name': 'BSC INFORMATION TECHNOLOGY(Direct)', 'institution_id': 10, 'student_count': 37}, {'program_name': 'Bachelor of Business Administration(Marketing)(January)', 'institution_id': 10, 'student_count': 35}, {'program_name': 'Bachelor Of Arts(Psychology)(January)', 'institution_id': 10, 'student_count': 32}, {'program_name': 'Diploma in Music', 'institution_id': 10, 'student_count': 31}, {'program_name': 'Bachelor of Fine Arts (Theatre Studies)', 'institution_id': 10, 'student_count': 29}, {'program_name': 'Master of Business Administration (Finance)(January)(Weekend)', 'institution_id': 10, 'student_count': 29}, {'program_name': 'Master Of Education Degree (Education Management And Practice)(January)', 'institution_id': 10, 'student_count': 29}, {'program_name': 'Master of Business Administration (Human Resource Management)(January)(Weekend)', 'institution_id': 10, 'student_count': 28}, {'program_name': 'Masters of Business Administration(Human Resource Management)(Combined)', 'institution_id': 10, 'student_count': 27}, {'program_name': 'Master Of Arts (Entrepreneurship And Corporate Strategy)(January)', 'institution_id': 10, 'student_count': 26}, {'program_name': 'Master of Business Administration (Finance)(Weekend)', 'institution_id': 10, 'student_count': 24}, {'program_name': 'Master of Business Administration (Human Resource Management)(Weekend)', 'institution_id': 10, 'student_count': 22}, {'program_name': 'Masters of Business Administration(Accounting)(Combined)', 'institution_id': 10, 'student_count': 19}, {'program_name': 'Bachelor of Science (Procurement and Supply Chain Management)(January)(Weekend)', 'institution_id': 10, 'student_count': 17}, {'program_name': 'Diploma In Business', 'institution_id': 10, 'student_count': 17}, {'program_name': 'Diploma in Information Technology (w)', 'institution_id': 10, 'student_count': 17}, {'program_name': 'Doctor Of Philosophy In Education', 'institution_id': 10, 'student_count': 17}, {'program_name': 'Master Of Philosophy Education Leadership And School Improvement (TOPUP)', 'institution_id': 10, 'student_count': 17}, {'program_name': 'MASTER OF SCIENCE  IN PROCUREMENT AND SUPPLY  CHAIN MANAGEMENT', 'institution_id': 10, 'student_count': 17}, {'program_name': 'Bachelor of Science(Economics)(January)', 'institution_id': 10, 'student_count': 16}, {'program_name': 'Master of Arts Guidance And Counselling Weekend', 'institution_id': 10, 'student_count': 15}, {'program_name': 'MASTER OF PUBLIC POLICY AND GOVERNANCE (January)', 'institution_id': 10, 'student_count': 15}, {'program_name': 'Bachelor of Business Administration(Banking & Finance)(January)', 'institution_id': 10, 'student_count': 14}, {'program_name': 'Bachelor Of Science In Public Health Nursing(January)', 'institution_id': 10, 'student_count': 14}, {'program_name': 'CERTIFICATE IN CHRISTIAN MINISTRY AND LEADERSHIP PROGRAMME', 'institution_id': 10, 'student_count': 14}, {'program_name': 'Certificate in Entrepreneurship (January)', 'institution_id': 10, 'student_count': 14}, {'program_name': 'ADVANCE CERTIFICATE IN BUSINESS ADMINISTRATION(JANUARY)', 'institution_id': 10, 'student_count': 13}, {'program_name': 'Diploma In Information Technology(January)', 'institution_id': 10, 'student_count': 13}, {'program_name': 'Master of Business Administration (Finance)(January)', 'institution_id': 10, 'student_count': 13}, {'program_name': 'Master Of Philosophy Educational Adminstration And Supervision (TOPUP)', 'institution_id': 10, 'student_count': 13}, {'program_name': 'Masters of Business Administration(Human Resource Management)(January)', 'institution_id': 10, 'student_count': 12}, {'program_name': 'Bachelor of Business Administration(Management Studies)(January)(Weekend)', 'institution_id': 10, 'student_count': 11}, {'program_name': 'Bachelor of Science(Procurement and Supply Chain Management)(w)', 'institution_id': 10, 'student_count': 11}, {'program_name': 'Master of Business Administration(Accounting)(Weekend)', 'institution_id': 10, 'student_count': 11}, {'program_name': 'Bachelor of Business Administration(Human Resource Management)(January)(Weekend)', 'institution_id': 10, 'student_count': 10}, {'program_name': 'Bachelor of Business Administration(Human Resource Management)(w)', 'institution_id': 10, 'student_count': 10}, {'program_name': 'Bachelor Of Business Administration(Management Studies)(w)', 'institution_id': 10, 'student_count': 10}, {'program_name': 'Master of Arts GUIDANCE & COUNSELLING(Combined)', 'institution_id': 10, 'student_count': 10}, {'program_name': 'Master Of Philosophy (Entrepreneurship And Corporate Strategy) TOPUP', 'institution_id': 10, 'student_count': 10}, {'program_name': 'Bachelor of Arts (Communication Studies)(January)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Bachelor of Science(Information Technology)(Weekend)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Certificate in Business Administration(January)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Master of Business Administration (Marketing)(January)(Weekend)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Master of Business Administration (Marketing)(Weekend)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Master Of Philosophy (Entrepreneurship And Corporate Strategy)', 'institution_id': 10, 'student_count': 9}, {'program_name': 'Bachelor of Arts (Religious Studies and Ethics)', 'institution_id': 10, 'student_count': 8}, {'program_name': 'BSC INFORMATION TECHNOLOGY(Direct) January', 'institution_id': 10, 'student_count': 8}, {'program_name': 'Master Of Arts Educational Administration And Supervision', 'institution_id': 10, 'student_count': 8}, {'program_name': 'Masters of Business Administration(Marketing)(Combined)', 'institution_id': 10, 'student_count': 8}, {'program_name': 'Bachelor of Business Administration(Accounting)(January)(Weekend)', 'institution_id': 10, 'student_count': 7}, {'program_name': 'Certificate in Music (January)', 'institution_id': 10, 'student_count': 7}, {'program_name': 'Master Of Arts Education Leadership And School Improvement', 'institution_id': 10, 'student_count': 7}, {'program_name': 'Masters of Business Administration(Marketing)(January)', 'institution_id': 10, 'student_count': 7}, {'program_name': 'Bachelor of Arts (English Studies)(January)', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Certificate In Counselling Psychology', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Certificate in Counselling Psychology Weekend', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Certificate In English Proficiency', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Certificate in Substainable Entrepreneurship Career In Oil And Gas Industry', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Master Of Philosophy (Guidance & Counselling)Topup', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Masters of Business Administration(Accounting)(January)', 'institution_id': 10, 'student_count': 6}, {'program_name': 'Access Course', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Bachelor of Business Administration(Accounting)(w)', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Bachelor of Business Administration(Marketing)(January)(Weekend) ', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Certificate in BandMastership', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Certificate in Sound Engineering', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Certificate in Theatre Practice', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Certificate in Theatre Studies', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Diploma In Health And Social Care', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Diploma In Information And Digital Technologies', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Master Of Philosophy (Education Leadership And School Improvement)', 'institution_id': 10, 'student_count': 5}, {'program_name': 'Bachelor of Business Administration(Banking & Finance)(January)(Weekend)', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Bachelor Of Science (Wood Technology With Education) - S', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Certificate in Events Management', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Certificate in Multimedia Studies', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Certificate in Theatre Practice January', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Master of Philosophy (Mathematics)', 'institution_id': 10, 'student_count': 4}, {'program_name': 'Bachelor Of Arts in Music(January)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Bachelor of Business Administration(Marketing)(w)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Bachelor of Fine Arts(Theatre Studies)(January)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Bachelor of Science(Mathematics & Statistics)(January)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Certificate in Fashion, Costume and Make-Up Design', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Certificate in Sound Engineering(January)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Master of Business Administration (Human Resource Management-THESIS OPTION)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Master of Business Administration (Marketing-THESIS OPTION)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Master of Business Administration(Accounting)(January)(Weekend)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Master Of Philosophy Education Assessment And Evaluation(TOPUP)', 'institution_id': 10, 'student_count': 3}, {'program_name': 'Bachelor of Arts (English Studies)(W)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Arts Communication Studies (w)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Science General Agric(Combined)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Science in Actuarial Science January', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Science(Economics)(Weekend)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Science(Information Technology)(w)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'CERTIFICATE IN CHRISTIAN MINISTRY AND LEADERSHIP PROGRAMME(January)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Master Of Arts Entrepreneurship(January)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Master Of Philosophy (Education Assessment And Evaluation)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Master Of Philosophy (Educational Adminstration And Supervision)', 'institution_id': 10, 'student_count': 2}, {'program_name': 'Bachelor of Arts(French)(January)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Bachelor of Arts(Psychology)(Weekend)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Bachelor of Science(Economics & Statistics)(January)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Bachelor of Science(Economics & Statistics)(Weekend)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Certificate in Counselling Psychology(Weekend)(January)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Certificate in Events Management(January)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Certificate in Statistics', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Diploma In General Agric (w)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Diploma In Horticulture', 'institution_id': 10, 'student_count': 1}, {'program_name': 'IT CONSORTIUM COURSE', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Master of Arts Guidance & Counselling(January)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Master of Business Administration (Management Studies)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'Master of Philosophy (Statistics)(PRE-MPHIL)', 'institution_id': 10, 'student_count': 1}, {'program_name': 'BACHELOR OF ARTS BUSINESS STUDIES', 'institution_id': 11, 'student_count': 8201}, {'program_name': 'BACHELOR OF SCIENCE IN NURSING', 'institution_id': 11, 'student_count': 7614}, {'program_name': 'BACHELOR OF LAWS', 'institution_id': 11, 'student_count': 3470}, {'program_name': 'BACHELOR OF SCIENCE MIDWIFERY', 'institution_id': 11, 'student_count': 2774}, {'program_name': 'BACHELOR OF SCIENCE MANAGEMENT AND COMPUTER STUDIES', 'institution_id': 11, 'student_count': 1988}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION', 'institution_id': 11, 'student_count': 1537}, {'program_name': 'BACHELOR OF SCIENCE IN PUBLIC HEALTH NURSING', 'institution_id': 11, 'student_count': 1370}, {'program_name': 'B.SC INFORMATION TECHNOLOGY', 'institution_id': 11, 'student_count': 1163}, {'program_name': 'BACHELOR OF SCIENCE COMMUNITY HEALTH NURSING', 'institution_id': 11, 'student_count': 1133}, {'program_name': 'BACHELOR OF ARTS COMPUTER SCIENCE AND MANAGEMENT', 'institution_id': 11, 'student_count': 1090}, {'program_name': 'BACHELOR OF ARTS IN COMMUNICATION STUDIES', 'institution_id': 11, 'student_count': 656}, {'program_name': 'CERTIFICATE IN BUSINESS STUDIES', 'institution_id': 11, 'student_count': 503}, {'program_name': 'DIPLOMA IN INFORMATION TECHNOLOGY', 'institution_id': 11, 'student_count': 476}, {'program_name': 'BACHELOR OF SCIENCE IN ACCOUNTING', 'institution_id': 11, 'student_count': 338}, {'program_name': 'M.SC. ENVIRONMENTAL SUSTAINABILITY AND MANAGEMENT', 'institution_id': 11, 'student_count': 229}, {'program_name': 'MSC LOGISTICS AND SUPPLY CHAIN MANAGEMENT', 'institution_id': 11, 'student_count': 132}, {'program_name': 'MA ADULT EDUCATION', 'institution_id': 11, 'student_count': 110}, {'program_name': 'MASTER OF ARTS INTERNATIONAL RELATIONS', 'institution_id': 11, 'student_count': 108}, {'program_name': 'BACHELOR OF SCIENCE IN ECONOMICS', 'institution_id': 11, 'student_count': 102}, {'program_name': 'BACHELOR OF ARTS IN DEVELOPMENT AND ENVIRONMENTAL STUDIES', 'institution_id': 11, 'student_count': 83}, {'program_name': 'MASTER OF SCIENCE IN CYBER SECURITY AND DIGITAL FORENSICS', 'institution_id': 11, 'student_count': 82}, {'program_name': 'CERTIFICATE IN MANAGEMENT AND COMPUTER STUDIES', 'institution_id': 11, 'student_count': 60}, {'program_name': 'DIPLOMA IN COMMUNICATION STUDIES', 'institution_id': 11, 'student_count': 40}, {'program_name': 'BACHELOR OF SCIENCE ECONOMICS WITH MANAGEMENT', 'institution_id': 11, 'student_count': 33}, {'program_name': 'BACHELOR OF EDUCATION (PRIMARY EDUCATION)', 'institution_id': 11, 'student_count': 32}, {'program_name': 'MASTER OF PHILOSOPHY IN NURSING', 'institution_id': 11, 'student_count': 32}, {'program_name': 'BACHELOR OF ARTS IN RURAL DEVELOPMENT & ECOTOURISM', 'institution_id': 11, 'student_count': 25}, {'program_name': 'BACHELOR OF ARTS IN MUSIC', 'institution_id': 11, 'student_count': 22}, {'program_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'institution_id': 11, 'student_count': 22}, {'program_name': 'BACHELOR OF SCIENCE COMPUTING WITH ACTUARIAL SCIENCE', 'institution_id': 11, 'student_count': 19}, {'program_name': 'MASTER OF PHILOSOPHY IN MIDWIFERY', 'institution_id': 11, 'student_count': 16}, {'program_name': 'MASTER OF SCIENCE IN INFORMATION TECHNOLOGY', 'institution_id': 11, 'student_count': 16}, {'program_name': 'MASTER OF SCIENCE IN BUSINESS COMPUTING', 'institution_id': 11, 'student_count': 15}, {'program_name': 'MASTER OF SCIENCE IN NURSING', 'institution_id': 11, 'student_count': 14}, {'program_name': 'DUMMY PROG', 'institution_id': 11, 'student_count': 7}, {'program_name': 'NURSING ACCESS COURSE', 'institution_id': 11, 'student_count': 3}, {'program_name': 'CERTIFICATE IN TRAVEL AND TOURISM MANAGEMENT', 'institution_id': 11, 'student_count': 2}, {'program_name': 'EXTENDED DIPLOMA STRATEGIC MANAGEMENT AND LEADERSHIP', 'institution_id': 11, 'student_count': 2}, {'program_name': 'Bachelor Of Science (Mathematics Education)', 'institution_id': 12, 'student_count': 1}, {'program_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'institution_id': 12, 'student_count': 1}, {'program_name': 'Bachelor Of Theology', 'institution_id': 13, 'student_count': 392}, {'program_name': 'Master Of Divinity', 'institution_id': 13, 'student_count': 365}, {'program_name': 'Master Of Arts In Ministry', 'institution_id': 13, 'student_count': 213}, {'program_name': 'Certificate in Ministry', 'institution_id': 13, 'student_count': 174}, {'program_name': 'Master of Theology', 'institution_id': 13, 'student_count': 44}, {'program_name': 'Diploma Of Theology', 'institution_id': 13, 'student_count': 4}, {'program_name': 'Bachelor Of Science In Computer Science', 'institution_id': 16, 'student_count': 102}, {'program_name': 'Bachelor Of Science In Information Technology', 'institution_id': 16, 'student_count': 9}, {'program_name': 'Bachelor Of Science In Community Health', 'institution_id': 16, 'student_count': 8}, {'program_name': 'Post - First Degree LL.B', 'institution_id': 16, 'student_count': 8}, {'program_name': 'Bachelor Of Arts In Finance', 'institution_id': 16, 'student_count': 6}, {'program_name': 'Bachelor Of Science In Agriculture', 'institution_id': 16, 'student_count': 6}, {'program_name': 'Bachelor Of Science In Medicine And Therapeutics', 'institution_id': 16, 'student_count': 6}, {'program_name': 'Bachelor Of Science In Pharmaceutical Chemistry', 'institution_id': 16, 'student_count': 6}, {'program_name': 'Bachelor Of Science In Statistics', 'institution_id': 16, 'student_count': 6}, {'program_name': 'Bachelor Of Arts In History', 'institution_id': 16, 'student_count': 5}, {'program_name': 'Bachelor Of Science In Actuarial Science', 'institution_id': 16, 'student_count': 5}, {'program_name': 'Bachelor Of Science In Mathematics', 'institution_id': 16, 'student_count': 5}, {'program_name': 'BSc. Administration (Accounting Option)', 'institution_id': 16, 'student_count': 5}, {'program_name': 'Bachelor Of Science In Plant & Environmental Biology', 'institution_id': 16, 'student_count': 4}, {'program_name': 'LL.B', 'institution_id': 16, 'student_count': 4}, {'program_name': 'Bachelor Of Arts In Marketing', 'institution_id': 16, 'student_count': 3}, {'program_name': 'Bachelor Of Arts In Philosophy And Classics', 'institution_id': 16, 'student_count': 3}, {'program_name': 'Bachelor Of Arts In The Study Of Religion', 'institution_id': 16, 'student_count': 3}, {'program_name': 'Bachelor Of Science In Computer Engineering', 'institution_id': 16, 'student_count': 3}, {'program_name': 'MA - Human Rights And Administration', 'institution_id': 16, 'student_count': 3}, {'program_name': 'LL.M - Alternative Dispute Resolution', 'institution_id': 16, 'student_count': 2}, {'program_name': 'BACHELOR OF NURSING', 'institution_id': 17, 'student_count': 2282}, {'program_name': 'BACHELOR OF NURSING (SANDWICH)', 'institution_id': 17, 'student_count': 1824}, {'program_name': 'BACHELOR OF MIDWIFERY', 'institution_id': 17, 'student_count': 1314}, {'program_name': 'BACHELOR OF PUBLIC HEALTH NURSING ((SANDWICH))', 'institution_id': 17, 'student_count': 1040}, {'program_name': 'BACHELOR OF PUBLIC HEALTH NURSING', 'institution_id': 17, 'student_count': 1020}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (DISEASE CONTROL)', 'institution_id': 17, 'student_count': 1005}, {'program_name': 'BACHELOR OF MEDICINE, BACHELOR OF SURGERY', 'institution_id': 17, 'student_count': 905}, {'program_name': 'BACHELOR OF MEDICAL LABORATORY SCIENCES', 'institution_id': 17, 'student_count': 874}, {'program_name': 'BACHELOR OF MIDWIFERY (SANDWICH)', 'institution_id': 17, 'student_count': 818}, {'program_name': 'BSc. BIOCHEMISTRY AND MOLECULAR BIOLOGY', 'institution_id': 17, 'student_count': 810}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (NUTRITION)', 'institution_id': 17, 'student_count': 794}, {'program_name': 'BACHELOR OF PHYSICIAN ASSISTANTSHIP (MEDICAL)', 'institution_id': 17, 'student_count': 787}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (DISEASE CONTROL SANDWICH)', 'institution_id': 17, 'student_count': 781}, {'program_name': 'DOCTOR OF PHARMACY', 'institution_id': 17, 'student_count': 645}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH PROMOTION)', 'institution_id': 17, 'student_count': 620}, {'program_name': 'BACHELOR OF DIETETICS', 'institution_id': 17, 'student_count': 543}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH INFORMATION)', 'institution_id': 17, 'student_count': 454}, {'program_name': 'BACHELOR OF PUBLIC HEALTH ( NUTRITION SANDWICH)', 'institution_id': 17, 'student_count': 416}, {'program_name': 'BACHELOR OF MIDWIFERY (TOP-UP SANDWICH)', 'institution_id': 17, 'student_count': 368}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH INFORMATION SANDWICH)', 'institution_id': 17, 'student_count': 359}, {'program_name': 'BACHELOR OF PHYSIOTHERAPY', 'institution_id': 17, 'student_count': 353}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH PROMOTION SANDWICH)', 'institution_id': 17, 'student_count': 296}, {'program_name': 'BACHELOR OF DIAGNOSTIC IMAGING', 'institution_id': 17, 'student_count': 292}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (MENTAL HEALTH SANDWICH)', 'institution_id': 17, 'student_count': 279}, {'program_name': 'BACHELOR OF PUBLIC HEALTH NURSING (TOP-UP SANDWICH)', 'institution_id': 17, 'student_count': 263}, {'program_name': 'BACHELOR OF MEDICAL LABORATORY SCIENCES (SANDWICH)', 'institution_id': 17, 'student_count': 253}, {'program_name': 'BACHELOR OF NURSING (TOP-UP SANDWICH)', 'institution_id': 17, 'student_count': 237}, {'program_name': 'DOCTOR OF MEDICAL LABORATORY SCIENCES', 'institution_id': 17, 'student_count': 211}, {'program_name': 'BACHELOR OF SPEECH, LANGUAGE AND HEARING SCIENCES', 'institution_id': 17, 'student_count': 204}, {'program_name': 'ACCESS - SCHOOL OF NURSING AND MIDWIFERY', 'institution_id': 17, 'student_count': 166}, {'program_name': 'BACHELOR OF MIDWIFERY TOP-UP', 'institution_id': 17, 'student_count': 158}, {'program_name': 'BACHELOR OF PHYSIOTHERAPY (SANDWICH)', 'institution_id': 17, 'student_count': 153}, {'program_name': 'BACHELOR OF SPORTS AND EXERCISE MEDICAL SCIENCES', 'institution_id': 17, 'student_count': 119}, {'program_name': 'BACHELOR OF NURSING TOP-UP', 'institution_id': 17, 'student_count': 115}, {'program_name': 'BACHELOR OF DIAGNOSTIC IMAGING (RADIOGRAPHY)', 'institution_id': 17, 'student_count': 111}, {'program_name': 'BACHELOR OF HEALTH SERVICES ADMINISTRATION', 'institution_id': 17, 'student_count': 110}, {'program_name': 'BACHELOR OF ORTHOTICS AND PROSTHETICS', 'institution_id': 17, 'student_count': 100}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (ENVIRONMENTAL HEALTH SANDWICH)', 'institution_id': 17, 'student_count': 95}, {'program_name': 'BACHELOR OF DIETETICS (SANDWICH)', 'institution_id': 17, 'student_count': 88}, {'program_name': 'MASTER OF PHILOSOPHY (APPLIED EPIDEMIOLOGY)', 'institution_id': 17, 'student_count': 87}, {'program_name': 'MASTER OF PUBLIC HEALTH (EPIDEMIOLOGY AND DISEASE CONTROL )', 'institution_id': 17, 'student_count': 73}, {'program_name': 'BACHELOR OF DENTAL SURGERY', 'institution_id': 17, 'student_count': 66}, {'program_name': 'MASTER OF PUBLIC HEALTH (EPIDEMIOLOGY AND DISEASE CONTROL) WEEKEND OPTION', 'institution_id': 17, 'student_count': 62}, {'program_name': 'MASTER OF PUBLIC HEALTH (GENERAL)', 'institution_id': 17, 'student_count': 61}, {'program_name': 'BACHELOR OF MEDICAL LABORATORY SCIENCES (TOP UP)', 'institution_id': 17, 'student_count': 58}, {'program_name': 'MASTER OF PUBLIC HEALTH (GENERAL) WEEKEND OPTION', 'institution_id': 17, 'student_count': 57}, {'program_name': 'MASTER OF PHILOSOPHY (NURSING STUDIES)', 'institution_id': 17, 'student_count': 50}, {'program_name': 'DOCTOR OF PHILOSOPHY (PUBLIC HEALTH)', 'institution_id': 17, 'student_count': 46}, {'program_name': 'MASTER OF PHILOSOPHY (BIOMEDICAL SCIENCES)', 'institution_id': 17, 'student_count': 36}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (DISEASE CONTROL TOP-UP)', 'institution_id': 17, 'student_count': 30}, {'program_name': 'MASTER OF PUBLIC HEALTH (FAMILY AND REPRODUCTIVE HEALTH) WEEKEND OPTION', 'institution_id': 17, 'student_count': 28}, {'program_name': 'MASTER OF PUBLIC HEALTH (NUTRITION)', 'institution_id': 17, 'student_count': 25}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (MENTAL HEALTH)', 'institution_id': 17, 'student_count': 23}, {'program_name': 'BACHELOR OF DIAGNOSTIC IMAGING (SANDWICH)', 'institution_id': 17, 'student_count': 22}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (PUBLIC HEALTH NURSING)', 'institution_id': 17, 'student_count': 22}, {'program_name': 'BACHELOR OF SPEECH, LANGUAGE AND HEARING SCIENCES (AUDIOLOGY)', 'institution_id': 17, 'student_count': 21}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (ENVIRONMENTAL HEALTH)', 'institution_id': 17, 'student_count': 20}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH INFORMATION TOP-UP)', 'institution_id': 17, 'student_count': 19}, {'program_name': 'MASTER OF PUBLIC HEALTH (HEALTH PROMOTION)', 'institution_id': 17, 'student_count': 19}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (HEALTH PROMOTION TOP-UP)', 'institution_id': 17, 'student_count': 17}, {'program_name': 'MASTER OF PHILOSOPHY (MEDICAL IMAGING)', 'institution_id': 17, 'student_count': 17}, {'program_name': 'MASTER PHILOSOPHY (MIDWIFERY)', 'institution_id': 17, 'student_count': 17}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (CLINICAL TOP-UP)', 'institution_id': 17, 'student_count': 16}, {'program_name': 'MASTER OF PHILOSOPHY (MEDICAL LABORATORY SCIENCES)', 'institution_id': 17, 'student_count': 16}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (CLINICAL)', 'institution_id': 17, 'student_count': 15}, {'program_name': 'BACHELOR OF ORTHOTICS AND PROSTHETICS (SANDWICH)', 'institution_id': 17, 'student_count': 14}, {'program_name': 'BACHELOR OF PUBLIC HEALTH ( NUTRITION TOP-UP)', 'institution_id': 17, 'student_count': 14}, {'program_name': 'DOCTOR OF PHILOSOPHY (BIOMEDICAL SCIENCES)', 'institution_id': 17, 'student_count': 12}, {'program_name': 'DOCTOR OF PHILOSOPHY (MEDICAL IMAGING)', 'institution_id': 17, 'student_count': 12}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (PUBLIC HEALTH NURSING TOP-UP)', 'institution_id': 17, 'student_count': 10}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (COUNSELLING)', 'institution_id': 17, 'student_count': 9}, {'program_name': 'DOCTOR OF MEDICAL LABORATORY SCIENCES (TOP UP)', 'institution_id': 17, 'student_count': 9}, {'program_name': 'MASTER OF PHILOSOPHY (MEDICAL IMAGING) - PART TIME', 'institution_id': 17, 'student_count': 9}, {'program_name': 'MASTER OF PHILOSOPHY (PHARMACEUTICAL CHEMISTRY)', 'institution_id': 17, 'student_count': 9}, {'program_name': 'MASTER OF PHILOSOPHY (PHARMACOLOGY)', 'institution_id': 17, 'student_count': 9}, {'program_name': 'MASTER OF PUBLIC HEALTH (HEALTH PROMOTION) WEEKEND OPTION', 'institution_id': 17, 'student_count': 7}, {'program_name': 'MASTER OF PUBLIC HEALTH (MONITORING AND EVALUATION) WEEKEND OPTION', 'institution_id': 17, 'student_count': 7}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (ENVIRONMENTAL HEALTH TOP-UP)', 'institution_id': 17, 'student_count': 6}, {'program_name': 'BACHELOR OF SPEECH, LANGUAGE AND HEARING SCIENCES (SPEECH AND LANGUAGE THERAPY)', 'institution_id': 17, 'student_count': 6}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (NEUROPSYCHOLOGY)', 'institution_id': 17, 'student_count': 6}, {'program_name': 'MASTER OF PUBLIC HEALTH (MONITORING AND EVALUATION)', 'institution_id': 17, 'student_count': 6}, {'program_name': 'MASTER OF PUBLIC HEALTH (NUTRITION) WEEKEND OPTION', 'institution_id': 17, 'student_count': 6}, {'program_name': 'TEST PROGRAM', 'institution_id': 17, 'student_count': 6}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (COUNSELLING TOP-UP)', 'institution_id': 17, 'student_count': 4}, {'program_name': 'DOCTOR OF PHILOSOPHY (PHARMACOLOGY)', 'institution_id': 17, 'student_count': 4}, {'program_name': 'ACCESS - F. N. BINKA SCHOOL OF PUBLIC HEALTH', 'institution_id': 17, 'student_count': 3}, {'program_name': 'DOCTOR PHILOSOPHY (PHARMACEUTICAL CHEMISTRY)', 'institution_id': 17, 'student_count': 3}, {'program_name': 'MASTER PHILOSOPHY (PHARMACOGNOSY)', 'institution_id': 17, 'student_count': 3}, {'program_name': 'DOCTOR OF PHILOSOPHY (MEDICAL LABORATORY SCIENCES)', 'institution_id': 17, 'student_count': 2}, {'program_name': 'MASTER OF PUBLIC HEALTH (FAMILY AND REPRODUCTIVE HEALTH)', 'institution_id': 17, 'student_count': 2}, {'program_name': 'BACHELOR OF PUBLIC HEALTH (MENTAL HEALTH TOP-UP)', 'institution_id': 17, 'student_count': 1}, {'program_name': 'COMBINED BACHELOR AND MASTER OF SCIENCE IN PSYCHOLOGY (NEUROPSYCHOLOGY TOP-UP)', 'institution_id': 17, 'student_count': 1}, {'program_name': 'DOCTOR OF PHILOSOPHY (MEDICAL IMAGING) - PART TIME', 'institution_id': 17, 'student_count': 1}, {'program_name': 'MASTER PHILOSOPHY (TOXICOLOGY)', 'institution_id': 17, 'student_count': 1}, {'program_name': 'EXECUTIVE MASTER OF ARTS IN CONFLICT PEACE AND SECURITY', 'institution_id': 18, 'student_count': 42}, {'program_name': 'BACHELOR OF EDUCATION (JUNIOR HIGH SCHOOL)', 'institution_id': 24, 'student_count': 23927}, {'program_name': 'BACHELOR OF EDUCATION (BASIC EDUCATION)', 'institution_id': 24, 'student_count': 10732}, {'program_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY)', 'institution_id': 24, 'student_count': 9618}, {'program_name': 'BACHELOR OF EDUCATION (UPPER PRIMARY EDUCATION)', 'institution_id': 24, 'student_count': 8905}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'institution_id': 24, 'student_count': 7662}, {'program_name': 'BACHELOR OF EDUCATION (EARLY GRADE)', 'institution_id': 24, 'student_count': 7606}, {'program_name': 'BACHELOR OF EDUCATION (EARLY GRADE EDUCATION)', 'institution_id': 24, 'student_count': 5826}, {'program_name': 'Bachelor Of Science (Mathematics Education)', 'institution_id': 24, 'student_count': 5491}, {'program_name': 'BACHELOR OF ARTS (POLITICAL SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 5305}, {'program_name': 'Bachelor Of Science (Information Technology Education)', 'institution_id': 24, 'student_count': 4481}, {'program_name': 'BACHELOR OF ART (SOCIAL STUDIES EDUCATION)', 'institution_id': 24, 'student_count': 4255}, {'program_name': 'BACHELOR OF ARTS (ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 3826}, {'program_name': 'POST GRADUATE DIPLOMA IN EDUCATION', 'institution_id': 24, 'student_count': 3519}, {'program_name': 'BACHELOR OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'institution_id': 24, 'student_count': 3348}, {'program_name': 'BACHELOR OF SCIENCE (INFORMATION AND COMMUNICATION TECHNOLOGY EDUCATION)', 'institution_id': 24, 'student_count': 3330}, {'program_name': 'BACHELOR OF ARTS (SOCIAL STUDIES EDUCATION)', 'institution_id': 24, 'student_count': 2988}, {'program_name': 'BACHELOR OF ARTS (GEOGRAPHY EDUCATION)', 'institution_id': 24, 'student_count': 2947}, {'program_name': 'Diploma In Education', 'institution_id': 24, 'student_count': 2821}, {'program_name': 'BACHELOR OF SCIENCE (MANAGEMENT EDUCATION)', 'institution_id': 24, 'student_count': 2750}, {'program_name': 'Bachelor Of Science (Accounting Education)', 'institution_id': 24, 'student_count': 2576}, {'program_name': 'BACHELOR OF ARTS (ART EDUCATION)', 'institution_id': 24, 'student_count': 2503}, {'program_name': 'BACHELOR OF EDUCATION (EARLY CHILDHOOD)', 'institution_id': 24, 'student_count': 2381}, {'program_name': 'DIPLOMA IN EARLY GRADE', 'institution_id': 24, 'student_count': 1994}, {'program_name': 'Bachelor Of Science (Agricultural Science Education)', 'institution_id': 24, 'student_count': 1988}, {'program_name': 'BACHELOR OF SCIENCE (INTEGRATED SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 1974}, {'program_name': 'BACHELOR OF ARTS (ECONOMICS EDUCATION)', 'institution_id': 24, 'student_count': 1921}, {'program_name': 'BACHELOR OF SCIENCE (PHYSICAL EDUCATION)', 'institution_id': 24, 'student_count': 1678}, {'program_name': 'DIPLOMA IN BASIC EDUCATION', 'institution_id': 24, 'student_count': 1580}, {'program_name': 'BACHELOR OF ARTS (FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 1574}, {'program_name': 'BACHELOR OF ARTS (GRAPHIC DESIGN)', 'institution_id': 24, 'student_count': 1558}, {'program_name': 'Bachelor Of Science (Catering And Hospitality Education) - R', 'institution_id': 24, 'student_count': 1508}, {'program_name': 'BACHELOR OF ARTS (EWE EDUCATION)', 'institution_id': 24, 'student_count': 1397}, {'program_name': 'BACHELOR OF SCIENCE (FOOD AND NUTRITION EDUCATION)', 'institution_id': 24, 'student_count': 1359}, {'program_name': 'BACHELOR OF ARTS (ENGLISH EDUCATION)', 'institution_id': 24, 'student_count': 1358}, {'program_name': 'Bachelor Of Science (Biological Sciences Education)', 'institution_id': 24, 'student_count': 1193}, {'program_name': 'Bachelor Of Science (Environmental Health And Sanitation Education)', 'institution_id': 24, 'student_count': 1175}, {'program_name': 'BACHELOR OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'institution_id': 24, 'student_count': 1173}, {'program_name': 'Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'institution_id': 24, 'student_count': 1171}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - R', 'institution_id': 24, 'student_count': 1160}, {'program_name': 'Bachelor Of Science (Catering And Hospitality Education) - S', 'institution_id': 24, 'student_count': 1137}, {'program_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'institution_id': 24, 'student_count': 1089}, {'program_name': 'BACHELOR OF ARTS (TWI EDUCATION)', 'institution_id': 24, 'student_count': 1051}, {'program_name': 'MASTER OF EDUCATION (EDUCATIONAL ADMINISTRATION AND MANAGEMENT) BY DISTANCE', 'institution_id': 24, 'student_count': 1029}, {'program_name': 'BACHELOR OF ARTS (HISTORY EDUCATION)', 'institution_id': 24, 'student_count': 989}, {'program_name': 'BACHELOR OF SCIENCE (HEALTH ADMINISTRATION EDUCATION)', 'institution_id': 24, 'student_count': 942}, {'program_name': 'BACHELOR OF EDUCATION (SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 929}, {'program_name': 'Bachelor Of Arts (Social Studies With Economics Education)', 'institution_id': 24, 'student_count': 880}, {'program_name': 'BACHELOR OF SCIENCE (INTEGRATED HOME ECONOMICS EDUCATION)', 'institution_id': 24, 'student_count': 870}, {'program_name': 'BACHELOR OF EDUCATION (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'institution_id': 24, 'student_count': 858}, {'program_name': 'Bachelor Of Education (Early Grade) - M', 'institution_id': 24, 'student_count': 816}, {'program_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - R', 'institution_id': 24, 'student_count': 811}, {'program_name': 'BACHELOR OF ARTS (MUSIC EDUCATION)', 'institution_id': 24, 'student_count': 751}, {'program_name': 'Bachelor Of Education (Junior High)', 'institution_id': 24, 'student_count': 707}, {'program_name': 'BACHELOR OF SCIENCE (BIOLOGY EDUCATION)', 'institution_id': 24, 'student_count': 704}, {'program_name': 'DIPLOMA (COMMUNITY BASED REHABILITATION & DISABILITY STUDIES)', 'institution_id': 24, 'student_count': 690}, {'program_name': 'BACHELOR OF ARTS (FASHION DESIGN AND TEXTILE EDUCATION)', 'institution_id': 24, 'student_count': 684}, {'program_name': 'BACHELOR OF SCIENCE (CHEMISTRY EDUCATION)', 'institution_id': 24, 'student_count': 663}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'institution_id': 24, 'student_count': 649}, {'program_name': 'Bachelor Of Science (Fashion Design And Textiles Education) - S', 'institution_id': 24, 'student_count': 627}, {'program_name': 'Master Of Arts In Educational Leadership', 'institution_id': 24, 'student_count': 542}, {'program_name': 'MASTER OF PHILOSOPHY (GHANAIAN LANGUAGE STUDIES)', 'institution_id': 24, 'student_count': 517}, {'program_name': 'Diploma In Education - KS', 'institution_id': 24, 'student_count': 509}, {'program_name': 'BACHELOR OF SCIENCE (HOME ECONOMICS EDUCATION)', 'institution_id': 24, 'student_count': 501}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education)', 'institution_id': 24, 'student_count': 481}, {'program_name': 'BACHELOR OF ARTS (TWI WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 466}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BANKING AND FINANCE)', 'institution_id': 24, 'student_count': 444}, {'program_name': 'MASTER OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'institution_id': 24, 'student_count': 444}, {'program_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'institution_id': 24, 'student_count': 439}, {'program_name': 'MASTER OF PHILOSOPHY (ENGLISH LANGUAGE)', 'institution_id': 24, 'student_count': 400}, {'program_name': 'BACHELOR OF SCIENCE (PHYSICS EDUCATION)', 'institution_id': 24, 'student_count': 393}, {'program_name': 'BACHELOR OF SCIENCE (CLOTHING AND TEXTILES EDUCATION)', 'institution_id': 24, 'student_count': 392}, {'program_name': 'MASTER OF PHILOSOPHY (EDUCATIONAL ADMINISTRATION & MANAGEMENT)', 'institution_id': 24, 'student_count': 379}, {'program_name': 'BACHELOR  OF ARTS (EWE WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 366}, {'program_name': 'MASTER OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'institution_id': 24, 'student_count': 364}, {'program_name': 'Bachelor Of Arts (Economics With Social Studies Education)', 'institution_id': 24, 'student_count': 351}, {'program_name': 'BACHELOR OF MUSIC', 'institution_id': 24, 'student_count': 351}, {'program_name': 'Bachelor Of Business Administration (Management) - E', 'institution_id': 24, 'student_count': 348}, {'program_name': 'MASTER OF PHILOSOPHY (SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 342}, {'program_name': 'MASTER OF PHILOSOPHY (EARLY CHILDHOOD EDUCATION)', 'institution_id': 24, 'student_count': 340}, {'program_name': 'BACHELOR OF ART (LINGUISTICS AND ENGLISH EDUCATION)', 'institution_id': 24, 'student_count': 339}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 325}, {'program_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY) BY DISTANCE', 'institution_id': 24, 'student_count': 323}, {'program_name': 'BACHELOR OF ARTS (FANTE EDUCATION)', 'institution_id': 24, 'student_count': 316}, {'program_name': 'Bachelor Of Science (Construction Technology Education) - S', 'institution_id': 24, 'student_count': 315}, {'program_name': 'Bachelor Of Science (Information Technology Education) - W', 'institution_id': 24, 'student_count': 310}, {'program_name': 'Diploma In Business Administration (Management)', 'institution_id': 24, 'student_count': 309}, {'program_name': 'MASTER OF PHILOSOPHY (ART EDUCATION)', 'institution_id': 24, 'student_count': 308}, {'program_name': 'BACHELOR OF ARTS (COMMUNICATION STUDIES)', 'institution_id': 24, 'student_count': 306}, {'program_name': 'DIPLOMA (GRAPHIC DESIGN)', 'institution_id': 24, 'student_count': 298}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (MARKETING AND ENTREPRENEURSHIP)', 'institution_id': 24, 'student_count': 296}, {'program_name': 'DIPLOMA (ART)', 'institution_id': 24, 'student_count': 295}, {'program_name': 'Bachelor Of Science (Agribusiness Management And Entrepreneurship Education)', 'institution_id': 24, 'student_count': 291}, {'program_name': 'BACHELOR OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'institution_id': 24, 'student_count': 283}, {'program_name': 'MASTER OF PHILOSOPHY IN SOCIAL STUDIES', 'institution_id': 24, 'student_count': 283}, {'program_name': 'BACHELOR OF ARTS (THEATRE ARTS)', 'institution_id': 24, 'student_count': 269}, {'program_name': 'MASTER OF PHILOSOPHY (SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 265}, {'program_name': 'MASTER OF PHILOSOPHY (BASIC EDUCATION)', 'institution_id': 24, 'student_count': 254}, {'program_name': 'DIPLOMA IN MANAGEMENT STUDIES', 'institution_id': 24, 'student_count': 251}, {'program_name': 'MASTER OF PHILOSOPHY (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES)', 'institution_id': 24, 'student_count': 243}, {'program_name': 'MASTER OF EDUCATION (COUNSELLING PSYCHOLOGY)', 'institution_id': 24, 'student_count': 232}, {'program_name': 'BACHELOR OF SCIENCE (MATHEMATICS WITH ECONOMICS EDUCATION)', 'institution_id': 24, 'student_count': 226}, {'program_name': 'Bachelor Of Business Administration (Human Resource Management) - W', 'institution_id': 24, 'student_count': 224}, {'program_name': 'DIPLOMA IN MUSIC', 'institution_id': 24, 'student_count': 208}, {'program_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education) - S', 'institution_id': 24, 'student_count': 207}, {'program_name': 'MASTER OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'institution_id': 24, 'student_count': 202}, {'program_name': 'Bachelor Of Science (Information Technology)', 'institution_id': 24, 'student_count': 193}, {'program_name': 'Bachelor Of Science (Natural Resources Management And Education)', 'institution_id': 24, 'student_count': 192}, {'program_name': 'Bachelor Of Science (Wood Technology Education) - S', 'institution_id': 24, 'student_count': 192}, {'program_name': 'MASTER OF PHILOSOPHY (FRENCH)', 'institution_id': 24, 'student_count': 189}, {'program_name': 'MASTER OF EDUCATION (EARLY CHILDHOOD EDUCATION)', 'institution_id': 24, 'student_count': 188}, {'program_name': 'DOCTOR OF PHILOSOPHY (SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 187}, {'program_name': 'BACHELOR OF ARTS (CREATIVE ARTS AND DESIGN EDUCATION)', 'institution_id': 24, 'student_count': 182}, {'program_name': 'MASTER OF PHILOSOPHY (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'institution_id': 24, 'student_count': 181}, {'program_name': 'MASTER OF BUSINESS ADMNISTRATION (ACCOUNTING)', 'institution_id': 24, 'student_count': 178}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'institution_id': 24, 'student_count': 175}, {'program_name': 'Master Of Technology Education In Catering And Hospitality', 'institution_id': 24, 'student_count': 175}, {'program_name': 'Bachelor Of Science In Administration (Accounting)', 'institution_id': 24, 'student_count': 173}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND TWI EDUCATION)', 'institution_id': 24, 'student_count': 171}, {'program_name': 'MASTER OF PHILOSOPHY (INFORMATION AND COMMUNICATION TECHNOLOGY)', 'institution_id': 24, 'student_count': 169}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGEMENT)', 'institution_id': 24, 'student_count': 167}, {'program_name': 'MASTER OF PHILOSOPHY (POLITICAL SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 167}, {'program_name': 'Bachelor Of Education (Early Grade) - K', 'institution_id': 24, 'student_count': 166}, {'program_name': 'Bachelor Of Business Administration (Management) - W', 'institution_id': 24, 'student_count': 163}, {'program_name': 'Bachelor Of Arts (Arabic Education)', 'institution_id': 24, 'student_count': 158}, {'program_name': 'BACHELOR OF ARTS (STRATEGIC COMMUNICATION)', 'institution_id': 24, 'student_count': 156}, {'program_name': 'MASTER OF PHILOSOPHY (FOOD AND NUTRITION)', 'institution_id': 24, 'student_count': 156}, {'program_name': 'MASTER OF PHILOSOPHY (MUSIC EDUCATION)', 'institution_id': 24, 'student_count': 154}, {'program_name': 'BACHELOR OF ARTS (RELIGIONS AND MORAL STUDIES EDUCATION)', 'institution_id': 24, 'student_count': 153}, {'program_name': 'MASTER OF PHILOSOPHY (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'institution_id': 24, 'student_count': 150}, {'program_name': 'Master Of Business Administration (Human Resource Management And Organisational Behaviour ) - Weekend', 'institution_id': 24, 'student_count': 149}, {'program_name': 'DIPLOMA (THEATRE ARTS)', 'institution_id': 24, 'student_count': 144}, {'program_name': 'DIPLOMA (BASIC EDUCATION)', 'institution_id': 24, 'student_count': 143}, {'program_name': 'Bachelor Of Business Administration (Secretarial Education)', 'institution_id': 24, 'student_count': 129}, {'program_name': 'Diploma In Education - TM', 'institution_id': 24, 'student_count': 128}, {'program_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - R', 'institution_id': 24, 'student_count': 127}, {'program_name': 'MASTER OF EDUCATION (TEACHING ENGLISH AS A SECOND LANGUAGE)', 'institution_id': 24, 'student_count': 124}, {'program_name': 'BACHELOR ARTS (GA WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 123}, {'program_name': 'DIPLOMA (EARLY GRADE)', 'institution_id': 24, 'student_count': 121}, {'program_name': 'MASTER OF ARTS (ENGLISH LANGUAGE)', 'institution_id': 24, 'student_count': 119}, {'program_name': 'Master Of Business Administration (Accounting)', 'institution_id': 24, 'student_count': 119}, {'program_name': 'MASTER OF EDUCATION (SCIENCE)', 'institution_id': 24, 'student_count': 119}, {'program_name': 'Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'institution_id': 24, 'student_count': 118}, {'program_name': 'MASTER OF EDUCATION (MATHEMATICS)', 'institution_id': 24, 'student_count': 118}, {'program_name': 'MASTER OF EDUCATION (HOME ECONOMICS)', 'institution_id': 24, 'student_count': 115}, {'program_name': 'Bachelor Of Science (Mechanical Technology Education)', 'institution_id': 24, 'student_count': 114}, {'program_name': 'DIPLOMA (TEXTILES AND FASHION)', 'institution_id': 24, 'student_count': 114}, {'program_name': 'Post Diploma Bachelor Of Science (Fashion Design And Textiles Education) - S', 'institution_id': 24, 'student_count': 114}, {'program_name': 'Bachelor Of Science (Mathematics Education) - M', 'institution_id': 24, 'student_count': 112}, {'program_name': 'Master Of Philosophy In Catering And Hospitality', 'institution_id': 24, 'student_count': 112}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (FINANCE)', 'institution_id': 24, 'student_count': 111}, {'program_name': 'Bachelor Of Science (Construction Technology And Management With Education) - S', 'institution_id': 24, 'student_count': 110}, {'program_name': 'MASTER OF ARTS (STRATEGIC COMMUNICATION)', 'institution_id': 24, 'student_count': 108}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 107}, {'program_name': 'Bachelor Of Science (Automotive Technology Education) - R', 'institution_id': 24, 'student_count': 106}, {'program_name': 'BACHELOR OF SCIENCE (FAMILY LIFE MANAGEMENT EDUCATION)', 'institution_id': 24, 'student_count': 106}, {'program_name': 'DOCTOR OF PHILOSOPHY (ARTS AND CULTURE)', 'institution_id': 24, 'student_count': 105}, {'program_name': 'BACHELOR OF ARTS (DANGME EDUCATION)', 'institution_id': 24, 'student_count': 104}, {'program_name': 'BACHELOR OF ARTS (TWI WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 104}, {'program_name': 'BACHELOR OF BUSINESS ADMINISTRATION (BUSINESS INFORMATION SYSTEMS)', 'institution_id': 24, 'student_count': 104}, {'program_name': 'Post Diploma Bachelor Of Science (Environmental Health And Sanitation Education)', 'institution_id': 24, 'student_count': 102}, {'program_name': 'CERTIFICATE (PRE-SCHOOL EDUCATION)', 'institution_id': 24, 'student_count': 100}, {'program_name': 'MASTER OF PHILOSOPHY (CURRICULUM & PEDAGOGIC STUDIES)', 'institution_id': 24, 'student_count': 100}, {'program_name': 'Bachelor Of Science (Mechanical Technology Education) - S', 'institution_id': 24, 'student_count': 99}, {'program_name': 'DIPLOMA (FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 98}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology Education) - S', 'institution_id': 24, 'student_count': 98}, {'program_name': 'Bachelor Of Science (Wood Technology Education)', 'institution_id': 24, 'student_count': 96}, {'program_name': 'MASTER OF PHILOSOPHY (CHEMISTRY EDUCATION)', 'institution_id': 24, 'student_count': 96}, {'program_name': 'DIPLOMA IN ACCOUNTING', 'institution_id': 24, 'student_count': 95}, {'program_name': 'DOCTOR OF PHILOSOPHY (APPLIED LINGUISTICS)', 'institution_id': 24, 'student_count': 95}, {'program_name': 'MASTER OF ARTS (GHANAIAN LANGUAGES EDUCATION)', 'institution_id': 24, 'student_count': 95}, {'program_name': 'Master Of Philosophy In Construction Management', 'institution_id': 24, 'student_count': 95}, {'program_name': 'BACHELOR OF SCIENCE (SPORTS COACHING)', 'institution_id': 24, 'student_count': 94}, {'program_name': 'BACHELOR OF ART (SISAALI WITH ENGLISH EDUCATION)', 'institution_id': 24, 'student_count': 93}, {'program_name': 'BACHELOR OF ARTS (DAGBANI EDUCATION)', 'institution_id': 24, 'student_count': 93}, {'program_name': 'BACHELOR OF ARTS (DAGAARE EDUCATION)', 'institution_id': 24, 'student_count': 90}, {'program_name': 'Master Of Philosophy In Construction Technology', 'institution_id': 24, 'student_count': 90}, {'program_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (FINANCE)', 'institution_id': 24, 'student_count': 89}, {'program_name': 'MASTER OF PHILOSOPHY (APPLIED LINGUISTICS)', 'institution_id': 24, 'student_count': 86}, {'program_name': 'MASTER OF PHILOSOPHY (ECONOMICS)', 'institution_id': 24, 'student_count': 86}, {'program_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT COMMUNICATION)', 'institution_id': 24, 'student_count': 84}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH ENGLISH EDUCATION)', 'institution_id': 24, 'student_count': 82}, {'program_name': 'POST GRADUATE DIPLOMA IN TEACHING AND LEARNING IN HIGHER EDUCATION', 'institution_id': 24, 'student_count': 82}, {'program_name': 'EXECUTIVE MASTERS (CONFLICT HUMAN RIGHTS & PEACE STUDIES)', 'institution_id': 24, 'student_count': 81}, {'program_name': 'Bachelor Of Science (Automotive Technology Education) - S', 'institution_id': 24, 'student_count': 80}, {'program_name': 'MASTER OF PHILOSOPHY (BIOLOGY EDUCATION)', 'institution_id': 24, 'student_count': 80}, {'program_name': 'Diploma In Electrical And Electronics Engineering Technology', 'institution_id': 24, 'student_count': 79}, {'program_name': 'Diploma In Environmental Health And Sanitation Education', 'institution_id': 24, 'student_count': 79}, {'program_name': 'MASTER OF EDUCATION (INSTITUTIONAL MENTORSHIP AND SUPERVISION)', 'institution_id': 24, 'student_count': 79}, {'program_name': 'Master Of Philosophy In Mathematics Education', 'institution_id': 24, 'student_count': 79}, {'program_name': 'MASTER OF PHILOSOPHY (VISUAL COMMUNICATION)', 'institution_id': 24, 'student_count': 77}, {'program_name': 'DOCTOR OF PHILOSOPHY (SOCIAL STUDIES)', 'institution_id': 24, 'student_count': 75}, {'program_name': 'MASTER OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'institution_id': 24, 'student_count': 75}, {'program_name': 'MASTER OF PHILOSOPHY (STRATEGIC COMMUNICATION)', 'institution_id': 24, 'student_count': 75}, {'program_name': 'BACHELOR OF ART (DANGME WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 74}, {'program_name': 'Diploma In Business Administration (Accounting)', 'institution_id': 24, 'student_count': 74}, {'program_name': 'Master Of Science In Information Technology Education', 'institution_id': 24, 'student_count': 74}, {'program_name': 'Master Of Technology In Construction Technology', 'institution_id': 24, 'student_count': 74}, {'program_name': 'Post Diploma Bachelor Of Science In Environmental Health And Sanitation Education - S', 'institution_id': 24, 'student_count': 74}, {'program_name': 'BACHELOR OF ARTS (EWE WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 72}, {'program_name': 'BACHELOR OF ARTS (EWE WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 71}, {'program_name': 'BACHELOR OF FINE ART (ANIMATION)', 'institution_id': 24, 'student_count': 70}, {'program_name': 'Diploma In Catering And Hospitality', 'institution_id': 24, 'student_count': 68}, {'program_name': 'Master Of Philosophy In Educational Leadership', 'institution_id': 24, 'student_count': 67}, {'program_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - S', 'institution_id': 24, 'student_count': 67}, {'program_name': 'DOCTOR OF PHILOSOPHY (MATHEMATICS EDUCATION)', 'institution_id': 24, 'student_count': 66}, {'program_name': 'Master Of Philosophy In Environmental And Occupational Health Education', 'institution_id': 24, 'student_count': 66}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - R', 'institution_id': 24, 'student_count': 65}, {'program_name': 'Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'institution_id': 24, 'student_count': 65}, {'program_name': 'Master Of Philosophy In Fashion Design And Textiles', 'institution_id': 24, 'student_count': 65}, {'program_name': 'Bachelor Of Arts (French With English Education)', 'institution_id': 24, 'student_count': 64}, {'program_name': 'Bachelor Of Science (Mathematics Education) - W', 'institution_id': 24, 'student_count': 64}, {'program_name': 'BACHELOR OF ARTS (DAGBANI WITH LINGUISTIC EDUCATION)', 'institution_id': 24, 'student_count': 63}, {'program_name': 'DOCTOR OF PHILOSOPHY (EDUCATIONAL LEADERSHIP)', 'institution_id': 24, 'student_count': 63}, {'program_name': 'Master Of Philosophy In Accounting', 'institution_id': 24, 'student_count': 62}, {'program_name': 'Diploma In Economics', 'institution_id': 24, 'student_count': 60}, {'program_name': 'BACHELOR OF ARTS (DAGAARE WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 59}, {'program_name': 'DIPLOMA IN SPORTS COACHING', 'institution_id': 24, 'student_count': 59}, {'program_name': 'MASTER OF EDUCATION (GEOGRAPHY)', 'institution_id': 24, 'student_count': 59}, {'program_name': 'Master Of Philosophy In Mathematics Education - W', 'institution_id': 24, 'student_count': 59}, {'program_name': 'DOCTOR OF PHILOSOPHY (COMMUNICATION AND MEDIA)', 'institution_id': 24, 'student_count': 58}, {'program_name': 'MASTER OF PHILOSOPHY (ASSESSMENT, MEASUREMENT AND EVALUATION)', 'institution_id': 24, 'student_count': 58}, {'program_name': 'Master Of Technology In Construction Management', 'institution_id': 24, 'student_count': 58}, {'program_name': 'BACHELOR OF ART (DAGAARE WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 55}, {'program_name': 'BACHELOR OF EDUCATION (PSYCHOLOGY)', 'institution_id': 24, 'student_count': 55}, {'program_name': 'Bachelor Of Science (Wood Technology With Education)', 'institution_id': 24, 'student_count': 55}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 54}, {'program_name': 'Master Of Philosophy In Business Management', 'institution_id': 24, 'student_count': 54}, {'program_name': 'Master Of Philosophy In Catering And Hospitality (Top-Up)', 'institution_id': 24, 'student_count': 54}, {'program_name': 'Diploma In Education - TK', 'institution_id': 24, 'student_count': 53}, {'program_name': 'DOCTOR OF PHILOSOPHY IN GHANAIAN LANGUAGES', 'institution_id': 24, 'student_count': 53}, {'program_name': 'MASTER OF PHILOSOPHY (ACCOUNTING)', 'institution_id': 24, 'student_count': 52}, {'program_name': 'Bachelor Of Science (Wood Technology With Education) - S', 'institution_id': 24, 'student_count': 51}, {'program_name': 'Diploma In Education - CP', 'institution_id': 24, 'student_count': 51}, {'program_name': 'Diploma In Education - M', 'institution_id': 24, 'student_count': 51}, {'program_name': 'MASTER OF ARTS (HUMAN RIGHTS, CONFLICT AND PEACE STUDIES', 'institution_id': 24, 'student_count': 51}, {'program_name': 'MASTER OF EDUCATION (FRENCH)', 'institution_id': 24, 'student_count': 51}, {'program_name': 'MASTER OF EDUCATION (SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 49}, {'program_name': 'BACHELOR OF ART (DAGAARE WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 48}, {'program_name': 'BACHELOR OF ARTS (DEVELOPMENT COMMUNICATION)', 'institution_id': 24, 'student_count': 48}, {'program_name': 'BACHELOR OF ARTS (EWE WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 48}, {'program_name': 'Bachelor Of Business Administration (Human Resource Management) - E', 'institution_id': 24, 'student_count': 48}, {'program_name': 'DIPLOMA IN BUSINESS ADMINISTRATION (LOGISTICS AND TRANSPORT MANAGEMENT)', 'institution_id': 24, 'student_count': 48}, {'program_name': 'Diploma In Construction Technology', 'institution_id': 24, 'student_count': 48}, {'program_name': 'Diploma In Fashion Design And Textiles', 'institution_id': 24, 'student_count': 48}, {'program_name': 'Master Of Education In Agriculture', 'institution_id': 24, 'student_count': 48}, {'program_name': 'BACHELOR OF ARTS (TWI WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 47}, {'program_name': 'Bachelor Of Science In Occupational Health And Safety', 'institution_id': 24, 'student_count': 47}, {'program_name': 'MASTER OF EDUCATION (GUIDANCE AND COUNSELLING)', 'institution_id': 24, 'student_count': 47}, {'program_name': 'Master Of Philosophy In Construction Management (Top Up)', 'institution_id': 24, 'student_count': 47}, {'program_name': 'Master Of Philosophy In Mechanical Engineering Technology', 'institution_id': 24, 'student_count': 47}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 46}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'institution_id': 24, 'student_count': 46}, {'program_name': 'BACHELOR OF ARTS (GA EDUCATION)', 'institution_id': 24, 'student_count': 45}, {'program_name': 'Bachelor Of Science In Administration (Accounting) - W', 'institution_id': 24, 'student_count': 45}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - W', 'institution_id': 24, 'student_count': 45}, {'program_name': 'Master Of Philosophy In Science Education', 'institution_id': 24, 'student_count': 42}, {'program_name': 'Master Of Technology Education In Fashion Design And Textile', 'institution_id': 24, 'student_count': 42}, {'program_name': 'MASTER OF PHILOSOPHY (CLOTHING AND TEXTILES)', 'institution_id': 24, 'student_count': 41}, {'program_name': 'Master Of Philosophy In Wood Science And Technology', 'institution_id': 24, 'student_count': 41}, {'program_name': 'BACHELOR OF ART (KUSAAL WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 40}, {'program_name': 'MASTER OF PHILOSOPHY (INTEGRATED SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 40}, {'program_name': 'MASTER OF PHILOSOPHY (TEXTILES AND FASHION)', 'institution_id': 24, 'student_count': 40}, {'program_name': 'BACHELOR OF ART (DAGBANI WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 39}, {'program_name': 'DOCTOR OF PHILOSOPHY (ENGLISH LANGUAGE)', 'institution_id': 24, 'student_count': 39}, {'program_name': 'BACHELOR OF ARTS (GA WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 38}, {'program_name': 'Bachelor Of Science (Marketing) - W', 'institution_id': 24, 'student_count': 37}, {'program_name': 'Master Of Philosophy In Biology', 'institution_id': 24, 'student_count': 37}, {'program_name': 'Master Of Technology In Electrical And Electronics Engineering', 'institution_id': 24, 'student_count': 37}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 36}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance)', 'institution_id': 24, 'student_count': 36}, {'program_name': 'DOCTOR OF PHILOSOPHY (COUNSELLING PSYCHOLOGY)', 'institution_id': 24, 'student_count': 36}, {'program_name': 'Post Diploma Bachelor Of Science (Catering And Hospitality Education)', 'institution_id': 24, 'student_count': 36}, {'program_name': 'Doctor Of Philosophy In Educational Leadership', 'institution_id': 24, 'student_count': 35}, {'program_name': 'Bachelor Of Business Administration (Executive Office Administration)', 'institution_id': 24, 'student_count': 34}, {'program_name': 'Diploma In Business Administration (Management) - W', 'institution_id': 24, 'student_count': 34}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (MARKETING)', 'institution_id': 24, 'student_count': 34}, {'program_name': 'MASTER OF EDUCATION (MATHEMATICS) BY DISTANCE', 'institution_id': 24, 'student_count': 34}, {'program_name': 'MASTER OF PHILOSOPHY (HISTORY EDUCATION)', 'institution_id': 24, 'student_count': 34}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 33}, {'program_name': 'DOCTOR OF PHILOSOPHY (SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 33}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (HUMAN RESOURCE MANAGMENT & ORGANIZATION BEHAVIOUR)', 'institution_id': 24, 'student_count': 33}, {'program_name': 'MASTER OF EDUCATION IN SCHOOL SUPERVISION AND QUALITY ASSURANCE)', 'institution_id': 24, 'student_count': 33}, {'program_name': 'MASTER OF ARTS (COMMUNICATION INSTRUCTION)', 'institution_id': 24, 'student_count': 32}, {'program_name': 'Master Of Philosophy In Business Management - W', 'institution_id': 24, 'student_count': 32}, {'program_name': 'Master Of Philosophy In Crop Science', 'institution_id': 24, 'student_count': 32}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education) - S', 'institution_id': 24, 'student_count': 32}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 31}, {'program_name': 'BACHELOR OF ARTS (NZEMA EDUCATION)', 'institution_id': 24, 'student_count': 31}, {'program_name': 'MASTER OF PHILOSOPHY (HUMAN RESOURCE MANAGEMENT)', 'institution_id': 24, 'student_count': 31}, {'program_name': 'MASTER OF PHILOSOPHY (JOURNALISM AND MEDIA ST)', 'institution_id': 24, 'student_count': 31}, {'program_name': 'Diploma In Education (Competency Based Training - CBT)', 'institution_id': 24, 'student_count': 30}, {'program_name': 'MASTER OF EDUCATION (FOOD AND NUTRITION)', 'institution_id': 24, 'student_count': 30}, {'program_name': 'Master Of Philosophy In Agronomy', 'institution_id': 24, 'student_count': 30}, {'program_name': 'BACHELOR OF ARTS (GONJA EDUCATION)', 'institution_id': 24, 'student_count': 29}, {'program_name': 'Bachelor Of Science (Marketing And Entrepreneurship) - W', 'institution_id': 24, 'student_count': 29}, {'program_name': 'MASTER OF ARTS (JOURNALISM AND MEDIA STUDIES)', 'institution_id': 24, 'student_count': 29}, {'program_name': 'MASTER OF EDUCATION (ENGLISH LANGUAGE) BY DISTANCE', 'institution_id': 24, 'student_count': 29}, {'program_name': 'Master Of Education In Mathematics Education', 'institution_id': 24, 'student_count': 29}, {'program_name': 'MASTER OF PHILOSOPHY (ARTS AND CULTURE)', 'institution_id': 24, 'student_count': 29}, {'program_name': 'Master Of Philosophy In Fashion Design And Textiles (Top-Up)', 'institution_id': 24, 'student_count': 29}, {'program_name': 'BACHELOR OF ARTS (DAGBANI WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 28}, {'program_name': 'Bachelor Of Arts (English Language Education) - M', 'institution_id': 24, 'student_count': 28}, {'program_name': 'Bachelor Of Science (Mathematics Education) - S', 'institution_id': 24, 'student_count': 28}, {'program_name': 'MASTER OF PHILOSOPHY (COMMUNICATION INSTRUCTION)', 'institution_id': 24, 'student_count': 28}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - W', 'institution_id': 24, 'student_count': 28}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND FANTE EDUCATION)', 'institution_id': 24, 'student_count': 27}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 26}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 26}, {'program_name': 'Diploma In Education (Junior High)', 'institution_id': 24, 'student_count': 26}, {'program_name': 'DOCTOR OF PHILOSOPHY (FRENCH)', 'institution_id': 24, 'student_count': 26}, {'program_name': 'MASTER OF PHILOSOPHY (PROCUREMENT AND SUPPLY CHAIN MANAGEMENT)', 'institution_id': 24, 'student_count': 26}, {'program_name': 'BACHELOR OF SCIENCE (ENVIRONMENTAL SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 25}, {'program_name': 'DOCTOR OF PHILOSOPHY (MUSIC EDUCATION)', 'institution_id': 24, 'student_count': 25}, {'program_name': 'MASTER OF ARTS (DEVELOPMENT COMMUNICATION)', 'institution_id': 24, 'student_count': 25}, {'program_name': 'MASTER OF SCIENCE (ECONOMICS EDUCATION)', 'institution_id': 24, 'student_count': 25}, {'program_name': 'Master Of Science In Information Technology Education - W', 'institution_id': 24, 'student_count': 25}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology Education)', 'institution_id': 24, 'student_count': 25}, {'program_name': 'BACHELOR OF SCIENCE (INSURANCE AND RISK MANAGEMENT)', 'institution_id': 24, 'student_count': 24}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Management) - E', 'institution_id': 24, 'student_count': 24}, {'program_name': 'Bachelor Of Business Administration (Management) - R', 'institution_id': 24, 'student_count': 23}, {'program_name': 'MASTER OF EDUCATION (PHYSICAL EDUCATION AND SPORTS STUDIES)', 'institution_id': 24, 'student_count': 23}, {'program_name': 'Master Of Education In Science Education', 'institution_id': 24, 'student_count': 23}, {'program_name': 'Master Of Philosophy In Construction Technology (Top-Up)', 'institution_id': 24, 'student_count': 23}, {'program_name': 'Master Of Technology In Mechanical Technology', 'institution_id': 24, 'student_count': 23}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 22}, {'program_name': 'Bachelor Of Science (Civil Engineering)', 'institution_id': 24, 'student_count': 22}, {'program_name': 'Diploma In Automotive Engineering Technology', 'institution_id': 24, 'student_count': 22}, {'program_name': 'MASTER OF BUSINESS ADMINISTRATION (MANAGEMENT INFORMATION SYSTEMS)', 'institution_id': 24, 'student_count': 22}, {'program_name': 'MASTER OF PHILOSOPHY (ENVIRONMENTAL SCIENCE)', 'institution_id': 24, 'student_count': 22}, {'program_name': 'MASTER OF PHILOSOPHY (FINANCE)', 'institution_id': 24, 'student_count': 22}, {'program_name': 'Master Of Philosophy In Automotive Engineering Technology', 'institution_id': 24, 'student_count': 22}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH ENGLISH LANGUAGE EDUCATION)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'BACHELOR OF ARTS (SISAALI EDUCATION)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'MASTER OF EDUCATION (COMPUTER EDUCATION AND TECHNOLOGY)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'MASTER OF PHILOSOPHY (FAMILY LIFE MANAGEMENT)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'MASTER OF PHILOSOPHY (THEATRE ARTS)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'Master Of Philosophy In Public Health', 'institution_id': 24, 'student_count': 21}, {'program_name': 'Post Diploma Bachelor Of Science (Management Education)', 'institution_id': 24, 'student_count': 21}, {'program_name': 'BACHELOR OF ARTS (KASEM EDUCATION)', 'institution_id': 24, 'student_count': 20}, {'program_name': 'Bachelor Of Science (Marketing And Entrepreneurship)', 'institution_id': 24, 'student_count': 20}, {'program_name': 'MASTER OF ARTS (ART EDUCATION)', 'institution_id': 24, 'student_count': 20}, {'program_name': 'MASTER OF EDUCATION (SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 20}, {'program_name': 'Master Of Philosophy In Accounting (Top-Up)', 'institution_id': 24, 'student_count': 20}, {'program_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education) - S', 'institution_id': 24, 'student_count': 20}, {'program_name': 'BACHELOR OF ARTS (KUSAAL EDUCATION)', 'institution_id': 24, 'student_count': 19}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 19}, {'program_name': 'Master Of Philosophy In Educational Leadership (Top-Up)', 'institution_id': 24, 'student_count': 19}, {'program_name': 'MASTER OF SCIENCE (DEVELOPMENT FINANCE)', 'institution_id': 24, 'student_count': 19}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Management) - W', 'institution_id': 24, 'student_count': 19}, {'program_name': 'Bachelor Of Arts (Arabic With English Education)', 'institution_id': 24, 'student_count': 18}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 18}, {'program_name': 'Bachelor Of Science (Mechanical Engineering Technology Education)', 'institution_id': 24, 'student_count': 18}, {'program_name': 'MASTER OF EDUCATION (POLITICAL SCIENCE EDUCATION)', 'institution_id': 24, 'student_count': 18}, {'program_name': 'MASTER OF PHILOSOPHY (PHYSICS EDUCATION)', 'institution_id': 24, 'student_count': 18}, {'program_name': 'Master Of Philosophy In Chemistry Education', 'institution_id': 24, 'student_count': 18}, {'program_name': 'Master Of Technology In Automotive Engineering Technology', 'institution_id': 24, 'student_count': 18}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) - E', 'institution_id': 24, 'student_count': 18}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 17}, {'program_name': 'BACHELOR OF ARTS (GURUNE EDUCATION)', 'institution_id': 24, 'student_count': 17}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 17}, {'program_name': 'Doctor Of Philosophy In Construction Management', 'institution_id': 24, 'student_count': 17}, {'program_name': 'Master Of Philosophy In Mechanical Engineering Technology (Top-Up)', 'institution_id': 24, 'student_count': 17}, {'program_name': 'Master Of Philosophy In Accounting - W', 'institution_id': 24, 'student_count': 16}, {'program_name': 'Master Of Philosophy In Electrical Power Systems Engineering (Top-Up)', 'institution_id': 24, 'student_count': 16}, {'program_name': 'Master Of Philosophy In Information Technology', 'institution_id': 24, 'student_count': 16}, {'program_name': 'Master Of Philosophy In Mathematics Education (Top-Up) - W', 'institution_id': 24, 'student_count': 16}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - W', 'institution_id': 24, 'student_count': 16}, {'program_name': 'Post Diploma Bachelor Of Science (Electrical And Electronics Engineering Technology Education) - R', 'institution_id': 24, 'student_count': 16}, {'program_name': 'BACHELOR OF ART (DAGAARE WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 15}, {'program_name': 'BACHELOR OF ARTS (TWI WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance) - W', 'institution_id': 24, 'student_count': 15}, {'program_name': 'DIPLOMA (EDUCATION)', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Diploma In Architecture And Digital Construction', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Diploma In Mechanical Engineering Technology', 'institution_id': 24, 'student_count': 15}, {'program_name': 'MASTER OF PHILOSOPHY (ENTREPRENEURSHIP AND INNOVATION)', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Master Of Philosophy In Animal Science', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education) - S', 'institution_id': 24, 'student_count': 15}, {'program_name': 'Bachelor Of Science (Plumbing, And Gas Technology)', 'institution_id': 24, 'student_count': 14}, {'program_name': 'Bachelor Of Science In Administration (Accounting) - E', 'institution_id': 24, 'student_count': 14}, {'program_name': 'Doctor Of Philosophy In Construction Technology', 'institution_id': 24, 'student_count': 14}, {'program_name': 'MASTER OF EDUCATION (BIOLOGY)', 'institution_id': 24, 'student_count': 14}, {'program_name': 'MASTER OF PHILOSOPHY (INSTRUCTIONAL DESIGN AND TECHNOLOGY)', 'institution_id': 24, 'student_count': 14}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology Education)', 'institution_id': 24, 'student_count': 14}, {'program_name': 'Bachelor Of Science (Agriculture Engineering, Technology And Innovation Education)', 'institution_id': 24, 'student_count': 13}, {'program_name': 'MASTER OF ARTS (HISTORY EDUCATION)', 'institution_id': 24, 'student_count': 13}, {'program_name': 'Master Of Philosophy In Business Management (Top-Up)', 'institution_id': 24, 'student_count': 13}, {'program_name': 'Master Of Philosophy In Electrical Power Systems Engineering', 'institution_id': 24, 'student_count': 13}, {'program_name': 'Post Diploma Bachelor Of Science (Accounting Education)', 'institution_id': 24, 'student_count': 13}, {'program_name': 'Bachelor Of Science (Automotive Engineering Technology Education)', 'institution_id': 24, 'student_count': 12}, {'program_name': 'Bachelor Of Science (Computerised Accounting)', 'institution_id': 24, 'student_count': 12}, {'program_name': 'Post Diploma Bachelor Of Science (Construction Technology And Management With Education)', 'institution_id': 24, 'student_count': 12}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology)', 'institution_id': 24, 'student_count': 12}, {'program_name': 'Doctor Of Philosophy In Wood Science And Technology', 'institution_id': 24, 'student_count': 11}, {'program_name': 'MASTER OF PHILOSOPHY (DEVELOPMENT FINANCE)', 'institution_id': 24, 'student_count': 11}, {'program_name': 'Master Of Technology In Wood Technology', 'institution_id': 24, 'student_count': 11}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting)', 'institution_id': 24, 'student_count': 11}, {'program_name': 'BACHELOR OF ARTS (GA WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 10}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL EDUCATION)', 'institution_id': 24, 'student_count': 10}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 10}, {'program_name': 'Bachelor Of Science (Marketing) - E', 'institution_id': 24, 'student_count': 10}, {'program_name': 'Master Of Philosophy In Information Technology - W', 'institution_id': 24, 'student_count': 10}, {'program_name': 'Master Of Philosophy In Soil Science', 'institution_id': 24, 'student_count': 10}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Technology Education)', 'institution_id': 24, 'student_count': 10}, {'program_name': 'BACHELOR OF ARTS (FANTE WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 9}, {'program_name': 'BACHELOR OF ARTS (GA WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 9}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH LINGUISTICS EDUCATION)', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Doctor Of Philosophy In Mathematics Education', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Master Of Philosophy In Business Management (Top-Up) - W', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Master Of Philosophy In Information Technology - W (Top-Up)', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Post Diploma Bachelor Of Science (Public Health - Nutrition, Health Promotion And Disease Control)', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology Education) - S', 'institution_id': 24, 'student_count': 9}, {'program_name': 'Bachelor Of Science (Marketing) - R', 'institution_id': 24, 'student_count': 8}, {'program_name': 'Diploma In Business Administration (Accounting) - W', 'institution_id': 24, 'student_count': 8}, {'program_name': 'Master Of Philosophy In Biology Education', 'institution_id': 24, 'student_count': 8}, {'program_name': 'Master Of Philosophy In Teaching And Learning', 'institution_id': 24, 'student_count': 8}, {'program_name': 'Master Of Philosophy In Wood Science And Technology (Top-Up)', 'institution_id': 24, 'student_count': 8}, {'program_name': 'MASTER OF SCIENCE (BIOLOGY)', 'institution_id': 24, 'student_count': 8}, {'program_name': 'BACHELOR OF ART (LINGUISTICS AND DAGAARE EDUCATION)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'Bachelor Of Science In Administration (Business Information Systems)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'Master Of Philosophy In Crop Science (Top-Up)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'Post Diploama Bachelor Of Science (Computerised Accounting)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Human Resource Management) - E', 'institution_id': 24, 'student_count': 7}, {'program_name': 'Post Diploma Bachelor Of Science (Automotive Technology Education)', 'institution_id': 24, 'student_count': 7}, {'program_name': 'BACHELOR OF ART (DAGBANI WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 6}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 6}, {'program_name': 'Master Of Philosophy In Automotive Engineering Technology (Top-Up)', 'institution_id': 24, 'student_count': 6}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Accounting) - E', 'institution_id': 24, 'student_count': 6}, {'program_name': 'BACHELOR OF ART (SISAALI EDUCATION WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 5}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND DANGME EDUCATION)', 'institution_id': 24, 'student_count': 5}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Doctor Of Philosophy In Crop Science', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Master Of Philosophy In Accounting (Top-Up) - W', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Agricultural Science Education)', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Information Technology Education) - W', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing And Entrepreneurship) - W', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education) -S', 'institution_id': 24, 'student_count': 5}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology Education)', 'institution_id': 24, 'student_count': 5}, {'program_name': 'BACHELOR OF ARTS (DANGME WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND GA EDUCATION)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'BACHELOR OF EDUCATION (SPECIAL NEEDS EDUCATION)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'Diploma In Management Education', 'institution_id': 24, 'student_count': 4}, {'program_name': 'MASTER OF EDUCATION (ENGLISH EDUCATION)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'MASTER OF PHILOSOPHY (HOME ECONOMICS)', 'institution_id': 24, 'student_count': 4}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing) - W', 'institution_id': 24, 'student_count': 4}, {'program_name': 'BACHELOR OF ART (LINGUISTIC AND EWE EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Bachelor Of Arts (English With French Education)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'BACHELOR OF ARTS (KASEM WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'BACHELOR OF ARTS (LIKPAKPAANL WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'BACHELOR OF ARTS (NZEMA WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'BACHELOR OF ARTS (SISAALI WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'DOCTOR OF PHILOSOPHY (GEOGRAPHY EDUCATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'MASTER OF ARTS (FRENCH TRANSLATION)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'MASTER OF FINE ARTS (THEATRE ARTS)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Master Of Philosophy In Agronomy (Top-up)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Master Of Philosophy In Plant Pathology', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Post Diploma Bachelor Of Science (Marketing) - R', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S (Top-Up)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - W (Top-Up)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Post Diploma Bachelor Of Science (Mechanical Engineering Technology Education)', 'institution_id': 24, 'student_count': 3}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Procurement And Supply Chain Management) -- R', 'institution_id': 24, 'student_count': 3}, {'program_name': 'B.SC. DOC', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (GONJA WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (GURUNE WITH GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (KUSAAL WITH FRENCH EDUCATION)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND GONJA EDUCATION)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'Bachelor Of Science (Welding And Fabrication Technology Education', 'institution_id': 24, 'student_count': 2}, {'program_name': 'Doctor Of Philosophy In Animal Science', 'institution_id': 24, 'student_count': 2}, {'program_name': 'Doctor Of Philosophy In Soil Fertility And Plant Nutrition', 'institution_id': 24, 'student_count': 2}, {'program_name': 'MASTER OF ARTS (ARTS AND CULTURE)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'MASTER OF ARTS (THEATRE ARTS)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'MASTER OF PHILOSOPHY COMMUNICATION AND MEDIA STUDIES   (COMMUNICATION SKILLS)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'MASTER OF PHILOSOPHY IN AGRICULTURAL EXTENSION AND RURAL ENTREPRENEURSHIP', 'institution_id': 24, 'student_count': 2}, {'program_name': 'Post Diploma Bachelor Of Business Administration (Executive Office Administration)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education)', 'institution_id': 24, 'student_count': 2}, {'program_name': 'BACHELOR OF ARTS (GERMAN EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND KASEM EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'BACHELOR OF ARTS (LINGUISTICS AND NZEMA EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Bachelor Of Science In Administration (Banking And Finance) - E', 'institution_id': 24, 'student_count': 1}, {'program_name': 'DIPLOMA (SIGN LANGUAGE)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Diploma In Welding And Fabrication Engineering Technology', 'institution_id': 24, 'student_count': 1}, {'program_name': 'DOCTOR OF EDUCATION (SOCIAL STUDIES EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'MASTER OF EDUCATION (ASSESSMENT IN SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'MASTER OF PHILOSOPHY (ASSESSMENT IN SPECIAL EDUCATION)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Master Of Philosphy In Water And Environmental Management', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Education (Early Grade) - K', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Science (Mathematics Education) - S', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Science (Wood Technology With Education) - S', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Banking And Finance) - W', 'institution_id': 24, 'student_count': 1}, {'program_name': 'Post Diploma Bachelor Of Science In Administration (Business Information Systems)', 'institution_id': 24, 'student_count': 1}, {'program_name': 'BSc. Diagnostic Medical Sonography', 'institution_id': 30, 'student_count': 518}, {'program_name': 'BSc. Medical Laboratory Science', 'institution_id': 30, 'student_count': 208}, {'program_name': 'BSc. Medical Imaging Science Radiography', 'institution_id': 30, 'student_count': 163}, {'program_name': 'BSc. Ophthalmic Dispensing', 'institution_id': 30, 'student_count': 118}, {'program_name': 'BSc. Clinical Dieteics', 'institution_id': 30, 'student_count': 93}, {'program_name': 'BSc. Community Nutrition', 'institution_id': 30, 'student_count': 22}, {'program_name': 'BSc. Public Health', 'institution_id': 30, 'student_count': 12}, {'program_name': 'Bachelor Of Medicine And Surgery', 'institution_id': 30, 'student_count': 1}]
2025-08-10 18:02:13,380 - celery.redirected - WARNING - ================================= 
2025-08-10 18:02:13,380 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_population_by_program_and_institution
2025-08-10 18:02:13,380 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-10 18:02:13,381 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:13,381 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-10 18:02:13,381 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-10 18:02:13,381 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the trend in student enrollment over the past few years for each institution?...
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The trend in student enrollment over the past few years shows that Central University has maintained...
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: line_chart
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_name': 'Central University', 'start_year': 2024, 'enrollment_count': 49153}, {'instit...
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_enrollment_trends_by_institution
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-10 18:02:13,381 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-10 18:02:13,381 - celery.redirected - WARNING - [{'institution_name': 'Central University', 'start_year': 2024, 'enrollment_count': 49153}, {'institution_name': 'Central University', 'start_year': 2023, 'enrollment_count': 49153}, {'institution_name': 'ITC University', 'start_year': 2027, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2026, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2025, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2024, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2023, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2022, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2021, 'enrollment_count': 192627}, {'institution_name': 'ITC University', 'start_year': 2019, 'enrollment_count': 192627}, {'institution_name': 'Klintaps College of Health and Allied Sciences', 'start_year': 2025, 'enrollment_count': 1135}, {'institution_name': 'Klintaps College of Health and Allied Sciences', 'start_year': 2024, 'enrollment_count': 1135}]
2025-08-10 18:02:13,381 - celery.redirected - WARNING - ================================= 
2025-08-10 18:02:13,381 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: student_enrollment_trends_by_institution
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-10 18:02:13,382 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:13,382 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-10 18:02:13,382 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-10 18:02:13,382 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What percentage of the student population is international versus domestic at each institution?...
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At each institution listed, the student population is predominantly international, with some institu...
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: table
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_name': 'Accra College Of Medicine', 'international_count': 114.0, 'domestic_count': 0...
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: international_vs_domestic_student_percentage
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-10 18:02:13,382 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-10 18:02:13,382 - celery.redirected - WARNING - [{'institution_name': 'Accra College Of Medicine', 'international_count': 114.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Akenten Appiah-Menka University of Skills Training and Entrepreneurial Development', 'international_count': 1.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Akrofi-Christaller Institute', 'international_count': 262.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Central University', 'international_count': 49153.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Ghana Institute of Languages', 'international_count': 18552.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Ghana School of Law', 'international_count': 13012.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'ITC University', 'international_count': 192627.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Klintaps College of Health and Allied Sciences', 'international_count': 1135.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Methodist University College Ghana', 'international_count': 31258.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Palm Institute', 'international_count': 169.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Presbyterian University College Ghana', 'international_count': 17758.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Public Health Nurses School, Korle Bu', 'international_count': 1065.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Trinity Theological Seminary', 'international_count': 1202.0, 'domestic_count': 0.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}, {'institution_name': 'Wisconsin International University College, Ghana', 'international_count': 32093.0, 'domestic_count': 1.0, 'international_percentage': 100.0, 'domestic_percentage': 0.0}]
2025-08-10 18:02:13,382 - celery.redirected - WARNING - ================================= 
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: international_vs_domestic_student_percentage
2025-08-10 18:02:13,382 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-10 18:02:13,383 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:13,383 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-10 18:02:13,383 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-10 18:02:13,383 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there any institutions experiencing significant growth or decline in student population?...
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Several institutions are experiencing significant fluctuations in their student populations. For ins...
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'name': 'ITC University', 'current_year': 2020, 'current_count': 601, 'last_count': 38083, 'percen...
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_population_growth_decline
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-10 18:02:13,383 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-10 18:02:13,383 - celery.redirected - WARNING - [{'name': 'ITC University', 'current_year': 2020, 'current_count': 601, 'last_count': 38083, 'percentage_change': -98.42}, {'name': 'ITC University', 'current_year': 2021, 'current_count': 1750, 'last_count': 601, 'percentage_change': 191.18}, {'name': 'ITC University', 'current_year': 2023, 'current_count': 2561, 'last_count': 1703, 'percentage_change': 50.38}, {'name': 'ITC University', 'current_year': 2025, 'current_count': 1651, 'last_count': 2804, 'percentage_change': -41.12}, {'name': 'Accra Medical', 'current_year': 2021, 'current_count': 2541, 'last_count': 7050, 'percentage_change': -63.96}, {'name': 'Accra Medical', 'current_year': 2022, 'current_count': 781, 'last_count': 2541, 'percentage_change': -69.26}, {'name': 'Accra Medical', 'current_year': 2023, 'current_count': 1043, 'last_count': 781, 'percentage_change': 33.55}, {'name': 'Accra Medical', 'current_year': 2024, 'current_count': 1594, 'last_count': 1043, 'percentage_change': 52.83}, {'name': 'Accra Medical', 'current_year': 2025, 'current_count': 3, 'last_count': 1594, 'percentage_change': -99.81}, {'name': 'Ghana School of Law', 'current_year': 2023, 'current_count': 35, 'last_count': 176, 'percentage_change': -80.11}, {'name': 'Ghana School of Law', 'current_year': 2024, 'current_count': 51, 'last_count': 35, 'percentage_change': 45.71}, {'name': 'Koforidua Technical University', 'current_year': 2021, 'current_count': 1397, 'last_count': 5282, 'percentage_change': -73.55}, {'name': 'Koforidua Technical University', 'current_year': 2022, 'current_count': 4220, 'last_count': 1397, 'percentage_change': 202.08}, {'name': 'Koforidua Technical University', 'current_year': 2024, 'current_count': 1948, 'last_count': 4186, 'percentage_change': -53.46}, {'name': 'Koforidua Technical University', 'current_year': 2025, 'current_count': 725, 'last_count': 1948, 'percentage_change': -62.78}, {'name': 'Presbyterian University College Ghana', 'current_year': 2022, 'current_count': 3967, 'last_count': 19922, 'percentage_change': -80.09}, {'name': 'Presbyterian University College Ghana', 'current_year': 2025, 'current_count': 743, 'last_count': 3974, 'percentage_change': -81.3}, {'name': 'Wisconsin International University College', 'current_year': 2021, 'current_count': 12, 'last_count': 414, 'percentage_change': -97.1}, {'name': 'Wisconsin International University College', 'current_year': 2022, 'current_count': 636, 'last_count': 12, 'percentage_change': 5200.0}, {'name': 'Wisconsin International University College', 'current_year': 2023, 'current_count': 140, 'last_count': 636, 'percentage_change': -77.99}]
2025-08-10 18:02:13,383 - celery.redirected - WARNING - ================================= 
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-10 18:02:13,383 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:13,383 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-10 18:02:13,383 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:13,383 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 3
2025-08-10 18:02:13,384 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the average class size based on the student population at each institution?...
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 2:
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the average class size based on the student population at each institution?...
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 3:
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the average class size based on the student population at each institution?...
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-10 18:02:13,384 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 3 documents, 0 with data_returned=True
2025-08-10 18:02:13,385 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:13,385 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-10 18:02:13,385 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-10 18:02:13,385 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the student population distribution differ between undergraduate and postgraduate programs ...
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, indicating that there may be no available data on the stud...
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: student_population_distribution_by_program
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-10 18:02:13,385 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-10 18:02:13,385 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 12
2025-08-10 18:02:13,385 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:13,385 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-10 18:02:13,385 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:13,385 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 12 documents
2025-08-10 18:02:13,385 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: What is the total number of students enrolled in each institution?
Answer: The total numbe...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:52.362054+00:00', 'data_returned': True, 'data_tag': 'total_students_by_institution'}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: How does the student population vary by academic year across all institutions?
Answer: I'm...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': False}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: What are the demographics of the student population, such as age, gender, and nationality,...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:20.269466+00:00', 'data_returned': False}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: What percentage of the student population is international versus domestic at each institu...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:58.681887+00:00', 'data_returned': True, 'data_tag': 'international_vs_domestic_student_percentage'}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 8:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 9:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: What is the average class size based on the student population at each institution?
Answer...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T18:01:27.529460+00:00', 'data_returned': False}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 10:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: What is the average class size based on the student population at each institution?
Answer...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T18:01:27.529460+00:00', 'data_returned': False}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 11:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: What is the average class size based on the student population at each institution?
Answer...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-10T18:01:27.529460+00:00', 'data_returned': False}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -   📄 Doc 12:
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Content: Question: How does the student population distribution differ between undergraduate and postgraduate...
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:33.266874+00:00', 'data_returned': False}
2025-08-10 18:02:13,386 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 6/12
2025-08-10 18:02:13,517 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.131s]
2025-08-10 18:02:14,224 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-10 18:02:15,862 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.804s]
2025-08-10 18:02:15,863 - UPSERT_DOCS - INFO - ✅ Successfully upserted 12 documents to Elasticsearch
2025-08-10 18:02:15,864 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 7
2025-08-10 18:02:15,864 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-10 18:02:15,865 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:15,865 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-10 18:02:15,865 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:15,865 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/7...
2025-08-10 18:02:16,614 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:16,618 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:16,618 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-10 18:02:16,619 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:16,619 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student population enrollment trends demographics'
2025-08-10 18:02:16,619 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:16,619 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:16,755 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-10 18:02:16,756 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 114
2025-08-10 18:02:16,888 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-10 18:02:16,889 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 64
2025-08-10 18:02:17,019 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-10 18:02:17,019 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 12
2025-08-10 18:02:17,146 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-10 18:02:17,147 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-10 18:02:17,754 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-10 18:02:17,897 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.139s]
2025-08-10 18:02:17,898 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-10 18:02:17,898 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:17,898 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:17,898 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:17,898 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:17,898 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:17,898 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:17,898 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:17,898 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:17,899 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total number of students enrolled in each institution?
Answer: The total numbe...
2025-08-10 18:02:17,899 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:52.362054+00:00', 'data_returned': True, 'data_tag': 'total_students_by_institution'}
2025-08-10 18:02:17,900 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:17,900 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:17,900 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_academic_year
2025-08-10 18:02:17,900 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:17,900 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:17,900 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_enrollment_trends_by_institution
2025-08-10 18:02:17,900 - app.chains.section_writer - INFO -   📄 Doc 3: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:17,900 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:17,901 - app.chains.section_writer - INFO -   📄 Doc 4: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:17,901 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:17,901 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_program_and_institution
2025-08-10 18:02:17,901 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What is the total number of students enrolled in each institution?
Answer: The total numbe...
2025-08-10 18:02:17,901 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:52.362054+00:00', 'data_returned': True, 'data_tag': 'total_students_by_institution'}
2025-08-10 18:02:17,901 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_students_by_institution
2025-08-10 18:02:17,901 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4965 chars):
2025-08-10 18:02:17,901 - app.chains.section_writer - INFO -    Question: How does the student population vary by academic year across all institutions?
Answer: The student population across all institutions has shown some variation by academic year. In 2019, there were 192,627 students. This number remained the same in 2021. In 2022, there was a slight increase to 192,679 students. The most significant growth occurred in 2023, with the student count rising to 241,780. This upward trend continued into 2024, reaching 242,915 students. However, in 2025, the st...
2025-08-10 18:02:21,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:21,938 - app.chains.section_writer - INFO - 🤖 AI generated section (960 chars):
2025-08-10 18:02:21,938 - app.chains.section_writer - INFO -    The student population across various institutions has experienced notable fluctuations over the years. Understanding these enrollment trends and demographics is crucial for institutional stakeholders to make informed decisions regarding resource allocation, program development, and strategic planni...
2025-08-10 18:02:21,938 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['student_population_by_academic_year']
2025-08-10 18:02:21,938 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 970 characters
2025-08-10 18:02:21,939 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/7...
2025-08-10 18:02:22,713 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:22,725 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:22,726 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-10 18:02:22,726 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:22,726 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Total enrollment by institution Ghana'
2025-08-10 18:02:22,726 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:22,726 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:22,856 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-10 18:02:22,856 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 114
2025-08-10 18:02:22,984 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-10 18:02:22,984 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 64
2025-08-10 18:02:23,113 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-10 18:02:23,113 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 12
2025-08-10 18:02:23,238 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-10 18:02:23,239 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-10 18:02:23,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-10 18:02:23,917 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.135s]
2025-08-10 18:02:23,918 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-10 18:02:23,918 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the total number of students enrolled in each institution?
Answer: The total numbe...
2025-08-10 18:02:23,918 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:52.362054+00:00', 'data_returned': True, 'data_tag': 'total_students_by_institution'}
2025-08-10 18:02:23,919 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What percentage of the student population is international versus domestic at each institu...
2025-08-10 18:02:23,919 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:58.681887+00:00', 'data_returned': True, 'data_tag': 'international_vs_domestic_student_percentage'}
2025-08-10 18:02:23,919 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:23,919 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:23,919 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:23,919 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:23,920 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:23,920 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:23,920 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What is the total number of students enrolled in each institution?
Answer: The total numbe...
2025-08-10 18:02:23,920 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:52.362054+00:00', 'data_returned': True, 'data_tag': 'total_students_by_institution'}
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_students_by_institution
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What percentage of the student population is international versus domestic at each institu...
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:58.681887+00:00', 'data_returned': True, 'data_tag': 'international_vs_domestic_student_percentage'}
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   ✅ Added Data Tag: international_vs_domestic_student_percentage
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   📄 Doc 3: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_academic_year
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_program_and_institution
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4944 chars):
2025-08-10 18:02:23,921 - app.chains.section_writer - INFO -    Question: What is the total number of students enrolled in each institution?
Answer: The total number of students enrolled in each institution is as follows: Accra College Of Medicine has 114 students, Akenten Appiah-Menka University of Skills Training and Entrepreneurial Development has 1 student, Akrofi-Christaller Institute has 262 students, Central University has 49,153 students, Ghana Institute of Languages has 18,552 students, Ghana School of Law has 13,012 students, GNAT Institute for Res...
2025-08-10 18:02:31,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:31,517 - app.chains.section_writer - INFO - 🤖 AI generated section (1298 chars):
2025-08-10 18:02:31,518 - app.chains.section_writer - INFO -    ## 2. Total Enrollment by Institution  

The total student enrollment figures for each institution are as follows: 

- Accra College Of Medicine: 114 students  
- Akenten Appiah-Menka University: 1 student  
- Akrofi-Christaller Institute: 262 students  
- Central University: 49,153 students  
- Gha...
2025-08-10 18:02:31,518 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_students_by_institution']
2025-08-10 18:02:31,518 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 1308 characters
2025-08-10 18:02:31,519 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/7...
2025-08-10 18:02:33,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:33,016 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:33,016 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-10 18:02:33,016 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:33,016 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'enrollment trends by year'
2025-08-10 18:02:33,016 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:33,016 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:33,144 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-10 18:02:33,144 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 114
2025-08-10 18:02:33,273 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-10 18:02:33,273 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 64
2025-08-10 18:02:33,399 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-10 18:02:33,399 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 12
2025-08-10 18:02:33,533 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-10 18:02:33,533 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-10 18:02:34,116 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-10 18:02:34,260 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.140s]
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled in each institution?
Answer: The total numbe...
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:52.362054+00:00', 'data_returned': True, 'data_tag': 'total_students_by_institution'}
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:34,261 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:34,262 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:34,262 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:34,262 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_academic_year
2025-08-10 18:02:34,262 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:34,262 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:34,262 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_enrollment_trends_by_institution
2025-08-10 18:02:34,262 - app.chains.section_writer - INFO -   📄 Doc 3: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:34,262 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:34,262 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What is the total number of students enrolled in each institution?
Answer: The total numbe...
2025-08-10 18:02:34,262 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:52.362054+00:00', 'data_returned': True, 'data_tag': 'total_students_by_institution'}
2025-08-10 18:02:34,263 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_students_by_institution
2025-08-10 18:02:34,263 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:34,263 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:34,263 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_program_and_institution
2025-08-10 18:02:34,263 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4965 chars):
2025-08-10 18:02:34,263 - app.chains.section_writer - INFO -    Question: How does the student population vary by academic year across all institutions?
Answer: The student population across all institutions has shown some variation by academic year. In 2019, there were 192,627 students. This number remained the same in 2021. In 2022, there was a slight increase to 192,679 students. The most significant growth occurred in 2023, with the student count rising to 241,780. This upward trend continued into 2024, reaching 242,915 students. However, in 2025, the st...
2025-08-10 18:02:38,745 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:38,751 - app.chains.section_writer - INFO - 🤖 AI generated section (869 chars):
2025-08-10 18:02:38,752 - app.chains.section_writer - INFO -    ## 3. Enrollment Trends Over Time  

The analysis of student population variation by academic year reveals significant fluctuations over the years. In 2019, the total enrollment stood at 192,627 students, a figure that remained unchanged in 2021. A slight increase was observed in 2022, with enrollme...
2025-08-10 18:02:38,752 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['student_population_by_academic_year']
2025-08-10 18:02:38,752 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 844 characters
2025-08-10 18:02:38,753 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/7...
2025-08-10 18:02:39,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:39,450 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:39,450 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-10 18:02:39,450 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:39,450 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'enrollment by program or course of study'
2025-08-10 18:02:39,450 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:39,451 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:39,583 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-10 18:02:39,583 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 114
2025-08-10 18:02:39,711 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-10 18:02:39,712 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 64
2025-08-10 18:02:39,842 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-10 18:02:39,842 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 12
2025-08-10 18:02:39,971 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-10 18:02:39,972 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-10 18:02:40,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-10 18:02:40,587 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.135s]
2025-08-10 18:02:40,587 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-10 18:02:40,588 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:40,588 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:40,588 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:40,588 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:40,588 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:40,588 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:40,588 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the total number of students enrolled in each institution?
Answer: The total numbe...
2025-08-10 18:02:40,588 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:52.362054+00:00', 'data_returned': True, 'data_tag': 'total_students_by_institution'}
2025-08-10 18:02:40,589 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:40,589 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:40,590 - app.chains.section_writer - INFO -   📄 Doc 1: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:40,590 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:40,590 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_program_and_institution
2025-08-10 18:02:40,590 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:40,590 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:40,590 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_academic_year
2025-08-10 18:02:40,590 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:40,590 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:40,591 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_enrollment_trends_by_institution
2025-08-10 18:02:40,591 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What is the total number of students enrolled in each institution?
Answer: The total numbe...
2025-08-10 18:02:40,591 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:52.362054+00:00', 'data_returned': True, 'data_tag': 'total_students_by_institution'}
2025-08-10 18:02:40,591 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_students_by_institution
2025-08-10 18:02:40,591 - app.chains.section_writer - INFO -   📄 Doc 5: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:40,591 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:40,591 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4965 chars):
2025-08-10 18:02:40,591 - app.chains.section_writer - INFO -    Question: Can we break down the student population by program or course of study at each institution?
Answer: The student population can be broken down by program or course of study at each institution. For example, at Institution 1, the largest program is the Bachelor of Science in Business Administration (Human Resource Management Option) with 5,260 students, followed by the Bachelor of Science in Business Administration (Accounting Option) with 4,222 students. Other notable programs include t...
2025-08-10 18:02:43,785 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:43,796 - app.chains.section_writer - INFO - 🤖 AI generated section (863 chars):
2025-08-10 18:02:43,796 - app.chains.section_writer - INFO -    ## 4. Enrollment by Program or Course of Study  

The student population can be broken down by program or course of study at selected institutions. Notable programs and their enrollment figures include:

- Bachelor of Science in Business Administration (Human Resource Management Option): 5,260 stude...
2025-08-10 18:02:43,796 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['student_population_by_program_and_institution']
2025-08-10 18:02:43,796 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 873 characters
2025-08-10 18:02:43,797 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/7...
2025-08-10 18:02:44,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:44,574 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:44,575 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-10 18:02:44,575 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:44,575 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Domestic vs International Student Population Analysis'
2025-08-10 18:02:44,575 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:44,575 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:44,701 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-10 18:02:44,702 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 114
2025-08-10 18:02:44,831 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-10 18:02:44,831 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 64
2025-08-10 18:02:44,956 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-10 18:02:44,957 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 12
2025-08-10 18:02:45,091 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-10 18:02:45,092 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-10 18:02:45,464 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-10 18:02:45,600 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.132s]
2025-08-10 18:02:45,600 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-10 18:02:45,601 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What percentage of the student population is international versus domestic at each institu...
2025-08-10 18:02:45,601 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:58.681887+00:00', 'data_returned': True, 'data_tag': 'international_vs_domestic_student_percentage'}
2025-08-10 18:02:45,601 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:45,601 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:45,601 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:45,602 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:45,602 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:45,602 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:45,602 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:45,602 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What percentage of the student population is international versus domestic at each institu...
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:58.681887+00:00', 'data_returned': True, 'data_tag': 'international_vs_domestic_student_percentage'}
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   ✅ Added Data Tag: international_vs_domestic_student_percentage
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_academic_year
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   📄 Doc 3: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_program_and_institution
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   📄 Doc 4: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:45,603 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:45,604 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:45,604 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:45,604 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_enrollment_trends_by_institution
2025-08-10 18:02:45,604 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4562 chars):
2025-08-10 18:02:45,604 - app.chains.section_writer - INFO -    Question: What percentage of the student population is international versus domestic at each institution?
Answer: At each institution listed, the student population is predominantly international, with some institutions having 100% of their students being international. For example, Accra College Of Medicine, Akenten Appiah-Menka University, and ITC University have no domestic students, resulting in an international percentage of 100%. The only institution with a domestic student is Wisconsin In...
2025-08-10 18:02:49,045 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:49,080 - app.chains.section_writer - INFO - 🤖 AI generated section (818 chars):
2025-08-10 18:02:49,080 - app.chains.section_writer - INFO -    ## 5. Domestic vs. International Student Population  

The analysis of the student population reveals a significant predominance of international students across various institutions. Notably, Accra College Of Medicine, Akenten Appiah-Menka University, and ITC University have achieved a 100% interna...
2025-08-10 18:02:49,080 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['international_vs_domestic_student_percentage']
2025-08-10 18:02:49,080 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 828 characters
2025-08-10 18:02:49,081 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 6/7...
2025-08-10 18:02:50,024 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:50,047 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:50,047 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-10 18:02:50,048 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:50,048 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Institutions student population growth decline trends'
2025-08-10 18:02:50,048 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:50,048 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:50,180 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-10 18:02:50,181 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 114
2025-08-10 18:02:50,309 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-10 18:02:50,309 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 64
2025-08-10 18:02:50,439 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-10 18:02:50,439 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 12
2025-08-10 18:02:50,569 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-10 18:02:50,569 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-10 18:02:51,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-10 18:02:51,156 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.137s]
2025-08-10 18:02:51,157 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-10 18:02:51,157 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:51,158 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:51,158 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:51,158 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:51,158 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:51,158 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:51,158 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:51,158 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:51,158 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What percentage of the student population is international versus domestic at each institu...
2025-08-10 18:02:51,158 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:58.681887+00:00', 'data_returned': True, 'data_tag': 'international_vs_domestic_student_percentage'}
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   📄 Doc 1: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_academic_year
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_enrollment_trends_by_institution
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   📄 Doc 4: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_program_and_institution
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What percentage of the student population is international versus domestic at each institu...
2025-08-10 18:02:51,159 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:58.681887+00:00', 'data_returned': True, 'data_tag': 'international_vs_domestic_student_percentage'}
2025-08-10 18:02:51,160 - app.chains.section_writer - INFO -   ✅ Added Data Tag: international_vs_domestic_student_percentage
2025-08-10 18:02:51,160 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4562 chars):
2025-08-10 18:02:51,160 - app.chains.section_writer - INFO -    Question: Are there any institutions experiencing significant growth or decline in student population?
Answer: Several institutions are experiencing significant fluctuations in their student populations. For instance, ITC University saw a drastic decline of 98.42% in 2020, followed by a remarkable recovery with a 191.18% increase in 2021, and continued growth of 50.38% in 2023. However, it faced another decline of 41.12% in 2025. Accra Medical experienced a significant drop of 63.96% in 2021, fo...
2025-08-10 18:02:56,972 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:56,978 - app.chains.section_writer - INFO - 🤖 AI generated section (1255 chars):
2025-08-10 18:02:56,979 - app.chains.section_writer - INFO -    ## 6. Institutions Experiencing Growth or Decline  

Several institutions are experiencing significant fluctuations in their student populations. ITC University reported a drastic decline of 98.42% in 2020, followed by a remarkable recovery with a 191.18% increase in 2021, and continued growth of 50...
2025-08-10 18:02:56,979 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['student_population_by_academic_year']
2025-08-10 18:02:56,979 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 6 completed and processed: 1230 characters
2025-08-10 18:02:56,979 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 7/7...
2025-08-10 18:02:58,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:02:58,207 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:02:58,207 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-10 18:02:58,207 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:02:58,207 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student population trends conclusion'
2025-08-10 18:02:58,207 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:58,207 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Give me a report on student population across all institutions.'
2025-08-10 18:02:58,339 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-10 18:02:58,339 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 114
2025-08-10 18:02:58,467 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-10 18:02:58,467 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 64
2025-08-10 18:02:58,597 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-10 18:02:58,597 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 12
2025-08-10 18:02:58,722 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.124s]
2025-08-10 18:02:58,722 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 6
2025-08-10 18:02:59,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-10 18:02:59,473 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.141s]
2025-08-10 18:02:59,473 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-10 18:02:59,473 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:59,473 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:59,473 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:59,473 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:59,473 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:59,473 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:59,473 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:59,474 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:59,474 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What percentage of the student population is international versus domestic at each institu...
2025-08-10 18:02:59,474 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:58.681887+00:00', 'data_returned': True, 'data_tag': 'international_vs_domestic_student_percentage'}
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the student population vary by academic year across all institutions?
Answer: The...
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-10T17:58:34.462874+00:00', 'data_returned': True, 'data_tag': 'student_population_by_academic_year'}
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_academic_year
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the trend in student enrollment over the past few years for each institution?
Answ...
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:55.725221+00:00', 'data_returned': True, 'data_tag': 'student_enrollment_trends_by_institution'}
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_enrollment_trends_by_institution
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   📄 Doc 3: Question: Are there any institutions experiencing significant growth or decline in student populatio...
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:31.696784+00:00', 'data_returned': True}
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   📄 Doc 4: Question: Can we break down the student population by program or course of study at each institution...
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:57:18.857382+00:00', 'data_returned': True, 'data_tag': 'student_population_by_program_and_institution'}
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   ✅ Added Data Tag: student_population_by_program_and_institution
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What percentage of the student population is international versus domestic at each institu...
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Give me a report on student population across all institutions.', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-10T17:56:58.681887+00:00', 'data_returned': True, 'data_tag': 'international_vs_domestic_student_percentage'}
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -   ✅ Added Data Tag: international_vs_domestic_student_percentage
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (4562 chars):
2025-08-10 18:02:59,474 - app.chains.section_writer - INFO -    Question: How does the student population vary by academic year across all institutions?
Answer: The student population across all institutions has shown some variation by academic year. In 2019, there were 192,627 students. This number remained the same in 2021. In 2022, there was a slight increase to 192,679 students. The most significant growth occurred in 2023, with the student count rising to 241,780. This upward trend continued into 2024, reaching 242,915 students. However, in 2025, the st...
2025-08-10 18:03:06,843 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-10 18:03:06,874 - app.chains.section_writer - INFO - 🤖 AI generated section (1715 chars):
2025-08-10 18:03:06,875 - app.chains.section_writer - INFO -    ## 7. Conclusion  

The analysis of student population trends reveals significant fluctuations over the years. The total student population across all institutions peaked in 2023 with 241,780 students, followed by a slight increase to 242,915 in 2024. However, a notable decline occurred in 2025, bri...
2025-08-10 18:03:06,875 - app.chains.section_writer - INFO - ✅ Found 4 data tag placeholders: ['student_population_by_academic_year', 'student_enrollment_trends_by_institution', 'student_population_by_program_and_institution', 'international_vs_domestic_student_percentage']
2025-08-10 18:03:06,875 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 7 completed and processed: 1631 characters
2025-08-10 18:03:06,876 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-10 18:03:06,876 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-10 18:03:06,876 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-10 18:03:06,876 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-10 18:03:06,876 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 8
2025-08-10 18:03:06,876 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 8
2025-08-10 18:03:06,876 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 5
2025-08-10 18:03:06,876 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250810_175548.log
2025-08-10 18:03:06,882 - celery.app.trace - INFO - Task generate_streaming_report[57776bec-f0fd-47b6-997b-ec4726114997] succeeded in 438.0654492919998s: {'outline': '# Report on Student Population Across All Institutions

## 1. Introduction  
   - Overview of the student population across various institutions  
   - Importance of understanding enrollment trends and demographics  

## 2. Total Enrollment by Institution  
   - Summary of total student enrollment figures for each institution  
     - Accra College Of Medicine: 114 students  
     - Akenten Appiah-Menka University: 1 student  
     - Akrofi-Christaller Institute: 262 students  
     - Central University: 49,153 students  
     - Ghana Institute of Languages: 18,552 students  
     - Ghana School of Law: 13,012 students  
     - GNAT Institute for Research and Industrial Relations Studies: 0 students  
     - Institute of Local Government Studies: 0 students  
     - ITC College: 0 students  
     - ITC University: 192,627 students  
     - Klintaps College of Health and Allied Sciences: 1,135 students  
     - Knutsford University College: 0 students  
     - Koforidua Technical University: 0...', ...}
2025-08-10 20:48:49,943 - celery.worker.consumer.consumer - WARNING - consumer: Connection to broker lost. Trying to re-establish the connection...
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 341, in start
    blueprint.start(self)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/bootsteps.py", line 116, in start
    step.start(parent)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py", line 772, in start
    c.loop(*c.loop_args())
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/loops.py", line 97, in asynloop
    next(loop)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/asynchronous/hub.py", line 373, in create_loop
    cb(*cbargs)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 1352, in on_readable
    self.cycle.on_readable(fileno)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 569, in on_readable
    chan.handlers[type]()
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 918, in _receive
    ret.append(self._receive_one(c))
               ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/kombu/transport/redis.py", line 928, in _receive_one
    response = c.parse_response()
               ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 926, in parse_response
    response = self._execute(conn, try_read)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 902, in _execute
    return conn.retry.call_with_retry(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/retry.py", line 65, in call_with_retry
    fail(error)
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 904, in <lambda>
    lambda error: self._disconnect_raise_connect(conn, error),
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 891, in _disconnect_raise_connect
    raise error
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/retry.py", line 62, in call_with_retry
    return do()
           ^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 903, in <lambda>
    lambda: command(*args, **kwargs),
            ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/client.py", line 924, in try_read
    return conn.read_response(disconnect_on_error=False, push_request=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/connection.py", line 613, in read_response
    response = self._parser.read_response(disable_decoding=disable_decoding)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/resp2.py", line 15, in read_response
    result = self._read_response(disable_decoding=disable_decoding)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/resp2.py", line 25, in _read_response
    raw = self._buffer.readline()
          ^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/socket.py", line 115, in readline
    self._read_from_socket()
  File "/Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/redis/_parsers/socket.py", line 68, in _read_from_socket
    raise ConnectionError(SERVER_CLOSED_CONNECTION_ERROR)
redis.exceptions.ConnectionError: Connection closed by server.
2025-08-10 20:48:49,955 - py.warnings - WARNING - /Users/<USER>/Desktop/Professional/Work/deep-research-api/.venv/lib/python3.12/site-packages/celery/worker/consumer/consumer.py:392: CPendingDeprecationWarning: 
In Celery 5.1 we introduced an optional breaking change which
on connection loss cancels all currently executed tasks with late acknowledgement enabled.
These tasks cannot be acknowledged as the connection is gone, and the tasks are automatically redelivered
back to the queue. You can enable this behavior using the worker_cancel_long_running_tasks_on_connection_loss
setting. In Celery 5.1 it is set to False by default. The setting will be set to True by default in Celery 6.0.

  warnings.warn(CANCEL_TASKS_BY_DEFAULT, CPendingDeprecationWarning)

2025-08-10 20:48:49,961 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 2.00 seconds... (1/100)

2025-08-10 20:48:51,975 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 4.00 seconds... (2/100)

2025-08-10 20:48:55,986 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 6.00 seconds... (3/100)

2025-08-10 20:49:02,014 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 8.00 seconds... (4/100)

2025-08-10 20:49:10,050 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 10.00 seconds... (5/100)

2025-08-10 20:49:20,091 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 12.00 seconds... (6/100)

2025-08-10 20:49:32,135 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 14.00 seconds... (7/100)

2025-08-10 20:49:46,195 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 16.00 seconds... (8/100)

2025-08-10 20:50:02,251 - celery.worker.consumer.consumer - ERROR - consumer: Cannot connect to redis://localhost:6379/0: Error 61 connecting to localhost:6379. Connection refused..
Trying again in 18.00 seconds... (9/100)


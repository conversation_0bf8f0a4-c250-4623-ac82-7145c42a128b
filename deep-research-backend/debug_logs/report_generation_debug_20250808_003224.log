2025-08-08 00:32:24,071 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250808_003224.log
2025-08-08 00:32:24,071 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:32:24,071 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: fd49dcb2-e502-410b-8871-f4fe4e65f935
2025-08-08 00:32:24,071 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:32:24,071 - REPORT_REQUEST - INFO - 📝 Original Question: 'What is the institution with the most students?'
2025-08-08 00:32:24,071 - REPORT_REQUEST - INFO - 🆔 Task ID: fd49dcb2-e502-410b-8871-f4fe4e65f935
2025-08-08 00:32:24,071 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T00:32:24.071952
2025-08-08 00:32:24,221 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.149s]
2025-08-08 00:32:24,221 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 18
2025-08-08 00:32:24,357 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 00:32:24,357 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:32:24,499 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.141s]
2025-08-08 00:32:24,499 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (1):
2025-08-08 00:32:24,499 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (18 docs)
2025-08-08 00:32:24,499 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 00:32:33,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:33,571 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 00:32:38,052 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:38,068 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 00:32:43,376 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:43,788 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:44,195 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:44,196 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:44,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:44,283 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:44,707 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:44,902 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:45,322 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:46,401 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:46,463 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 00:32:46,463 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:32:46,463 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 00:32:46,463 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:32:46,463 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 7
2025-08-08 00:32:50,666 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:50,879 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:51,056 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:51,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:51,123 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:51,186 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:51,455 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:53,462 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:54,018 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:54,094 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:54,315 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:54,382 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:54,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:54,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:55,254 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:55,304 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:55,766 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:55,819 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:55,819 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:55,886 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:56,077 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:59,351 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:59,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:59,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:32:59,438 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:00,504 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:02,423 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:02,832 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:03,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:04,163 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:04,164 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:04,170 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the demographic breakdown of students at the institution with the most students?', 'sql': 'SELECT sex, COUNT(*) AS student_count FROM students WHERE institution_id = (SELECT institution_id FROM students GROUP BY institution_id ORDER BY COUNT(*) DESC LIMIT 1) GROUP BY sex;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by using a subquery that groups students by institution_id, counts them, and orders the results to find the top institution. It then counts the number of students by sex for that specific institution, which aligns with the request for a demographic breakdown. The use of GROUP BY sex allows for the demographic breakdown to be presented as required.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be enhanced by including additional demographic factors, such as age or nationality, if those attributes are relevant to the breakdown being requested.'}
2025-08-08 00:33:04,173 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:04,174 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 00:33:05,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:06,495 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:07,207 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:07,672 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:08,379 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:09,088 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:09,123 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:09,612 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:10,204 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:10,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:10,530 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the retention rate of students at the institution with the most students?', 'sql': "WITH student_counts AS (SELECT institution_id, COUNT(*) AS total_students FROM core.students GROUP BY institution_id), max_institution AS (SELECT institution_id FROM student_counts ORDER BY total_students DESC LIMIT 1), retained_students AS (SELECT COUNT(*) AS retained FROM core.student_statuses s JOIN max_institution m ON s.student_id IN (SELECT id FROM core.students WHERE institution_id = m.institution_id) WHERE s.student_status_type_id = (SELECT id FROM core.student_status_types WHERE status = 'active')) SELECT (SELECT retained FROM retained_students) * 1.0 / (SELECT total_students FROM student_counts WHERE institution_id = (SELECT institution_id FROM max_institution)) AS retention_rate;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by counting the number of students per institution and selecting the one with the maximum count. It then calculates the number of retained students by joining the student statuses with the maximum institution and filtering for active statuses. Finally, it computes the retention rate by dividing the number of retained students by the total number of students at that institution. The logic follows the requirements of the question accurately.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding comments to clarify each step of the process for better readability and maintainability.'}
2025-08-08 00:33:10,530 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:10,530 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 00:33:10,921 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:11,331 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:12,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:12,072 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:13,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:13,378 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:13,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:13,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:13,988 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:14,553 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:15,064 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:15,071 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:15,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:15,767 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:15,834 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:16,320 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:16,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:16,991 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:17,579 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:18,745 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:19,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:19,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:19,938 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are the racial and ethnic demographics of the student population at this institution?', 'answerable': False, 'reasoning': "The schema does not contain any tables or fields that explicitly store information about the racial and ethnic demographics of the student population. While there are tables related to students, such as 'students' and 'nationalities', there is no direct indication that racial or ethnic demographics are recorded or can be derived from the existing data. Therefore, the question cannot be answered based on the provided schema.", 'feedback': ''}
2025-08-08 00:33:19,938 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:19,938 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:33:19,938 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are the racial and ethnic demographics of the student population at this institution?', 'answerable': False, 'reasoning': "The schema does not contain any tables or fields that explicitly store information about the racial and ethnic demographics of the student population. While there are tables related to students, such as 'students' and 'nationalities', there is no direct indication that racial or ethnic demographics are recorded or can be derived from the existing data. Therefore, the question cannot be answered based on the provided schema.", 'feedback': ''}
2025-08-08 00:33:21,878 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:21,883 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the student population of the institution with the most students compare to other institutions?', 'sql': "WITH student_counts AS (SELECT institution_id, COUNT(*) AS student_count FROM core.students GROUP BY institution_id), max_institution AS (SELECT institution_id, student_count FROM student_counts ORDER BY student_count DESC LIMIT 1) SELECT i.name, sc.student_count, CASE WHEN sc.student_count = mi.student_count THEN 'Most Students' ELSE 'Other' END AS comparison FROM student_counts sc JOIN institutions i ON sc.institution_id = i.id JOIN max_institution mi ON 1=1;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and then selecting the one with the highest count. It also compares the student counts of all institutions to this maximum, labeling them as 'Most Students' or 'Other'. The use of CTEs (Common Table Expressions) is appropriate for breaking down the problem into manageable parts, and the final selection provides the necessary comparison as requested in the question.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly naming the CTEs for clarity and ensuring that the comparison logic is clear to readers unfamiliar with SQL. Additionally, consider adding an ORDER BY clause in the final selection to present the results in a more organized manner.'}
2025-08-08 00:33:21,884 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:21,884 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 00:33:22,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:22,509 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total number of students enrolled at the institution with the most students?', 'sql': 'SELECT COUNT(*) AS total_students\nFROM students\nWHERE institution_id = (\n    SELECT institution_id\n    FROM students\n    GROUP BY institution_id\n    ORDER BY COUNT(*) DESC\n    LIMIT 1\n);', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by grouping the records in the 'students' table by 'institution_id', counting the number of students for each institution, and ordering the results in descending order. The subquery retrieves the 'institution_id' of the institution with the highest count of students. The outer query then counts the total number of students for that specific institution. Therefore, the SQL query accurately answers the question posed.", 'feedback': 'The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to add a comment in the SQL to clarify the purpose of the subquery for future reference.'}
2025-08-08 00:33:22,509 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:22,509 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 00:33:22,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:22,719 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about various entities related to institutions, students, programs, and assessments, but it does not include qualitative factors or insights that would explain retention rates. The schema lacks data on student experiences, institutional policies, or other qualitative metrics that could influence retention, making it impossible to answer the question based solely on the schema.', 'feedback': ''}
2025-08-08 00:33:22,719 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:22,719 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:33:22,719 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the retention rates at large institutions, and how might these differ from smaller colleges or universities?', 'answerable': False, 'reasoning': 'The question asks for qualitative insights into retention rates at large institutions compared to smaller colleges or universities. The provided database schema contains structured data about various entities related to institutions, students, programs, and assessments, but it does not include qualitative factors or insights that would explain retention rates. The schema lacks data on student experiences, institutional policies, or other qualitative metrics that could influence retention, making it impossible to answer the question based solely on the schema.', 'feedback': ''}
2025-08-08 00:33:22,796 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:23,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:24,718 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:25,482 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:25,554 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:25,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:25,996 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:26,280 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:26,932 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:27,312 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:28,235 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:28,553 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:28,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:28,976 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:29,416 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:30,992 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:30,993 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:32,118 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:32,227 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:33,245 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:33,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:33,560 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to its peers?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to its peers. The provided database schema contains quantitative data related to students, programs, admissions, and other operational aspects of the university, but it does not include qualitative insights or factors such as reputation, marketing strategies, student satisfaction, or community engagement that would be necessary to answer the question comprehensively. Therefore, the question cannot be answered from the schema.", 'feedback': ''}
2025-08-08 00:33:33,560 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:33,560 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:33:33,560 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think contribute to ITC University's ability to attract and maintain such a large student population compared to its peers?", 'answerable': False, 'reasoning': "The question asks for qualitative factors that contribute to ITC University's ability to attract and maintain a large student population compared to its peers. The provided database schema contains quantitative data related to students, programs, admissions, and other operational aspects of the university, but it does not include qualitative insights or factors such as reputation, marketing strategies, student satisfaction, or community engagement that would be necessary to answer the question comprehensively. Therefore, the question cannot be answered from the schema.", 'feedback': ''}
2025-08-08 00:33:33,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:33,637 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a quantitative one. The schema provides data about institutions, applicants, programs, and related entities, but it does not contain specific metrics or qualitative factors that would directly explain the reasons behind high enrollment numbers. To answer this question, one would need insights into marketing strategies, student satisfaction, program reputation, and other external factors that are not captured in the provided schema.', 'feedback': ''}
2025-08-08 00:33:33,637 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:33,637 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:33:33,637 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to such a high enrollment number at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to high enrollment numbers at an institution, which is a qualitative analysis rather than a quantitative one. The schema provides data about institutions, applicants, programs, and related entities, but it does not contain specific metrics or qualitative factors that would directly explain the reasons behind high enrollment numbers. To answer this question, one would need insights into marketing strategies, student satisfaction, program reputation, and other external factors that are not captured in the provided schema.', 'feedback': ''}
2025-08-08 00:33:34,371 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:34,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:36,111 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:38,530 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:39,308 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:41,846 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:41,942 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:43,995 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:44,001 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How has the student population at the institution with the most students changed over the past few years?', 'sql': 'SELECT ay.start_year, COUNT(s.id) AS student_count\nFROM students s\nJOIN institutions i ON s.institution_id = i.id\nJOIN academic_years ay ON ay.institution_id = i.id\nWHERE ay.start_year >= YEAR(CURDATE()) - 5\nAND i.id = (SELECT institution_id FROM students GROUP BY institution_id ORDER BY COUNT(id) DESC LIMIT 1)\nGROUP BY ay.start_year\nORDER BY ay.start_year;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by using a subquery that groups students by institution and orders them by the count of students in descending order. It then joins the students table with the institutions and academic_years tables to count the number of students for each academic year for the last five years. The use of 'WHERE ay.start_year >= YEAR(CURDATE()) - 5' ensures that only the relevant years are considered, and the final output groups the results by academic year, which aligns with the question's request for changes in student population over time.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating the institution's name or ID in the output for clarity, as the question asks about the institution with the most students."}
2025-08-08 00:33:44,001 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:44,002 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 00:33:44,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:46,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:47,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:47,833 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:47,934 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:49,108 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:50,404 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:50,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:51,614 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:53,725 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:54,519 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:54,549 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think have contributed to the stability of the student population at this institution, despite trends in higher education enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the stability of the student population at an institution, which is a qualitative assessment. The provided schema contains data about students, programs, admissions, and other related entities, but it does not include qualitative insights or analysis regarding trends in higher education enrollment or the specific factors affecting student population stability. Therefore, while the schema can provide quantitative data about student numbers and demographics, it cannot directly answer the qualitative question posed.', 'feedback': ''}
2025-08-08 00:33:54,549 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:54,549 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:33:54,549 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think have contributed to the stability of the student population at this institution, despite trends in higher education enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the stability of the student population at an institution, which is a qualitative assessment. The provided schema contains data about students, programs, admissions, and other related entities, but it does not include qualitative insights or analysis regarding trends in higher education enrollment or the specific factors affecting student population stability. Therefore, while the schema can provide quantitative data about student numbers and demographics, it cannot directly answer the qualitative question posed.', 'feedback': ''}
2025-08-08 00:33:54,940 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:55,484 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:55,499 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there any partnerships or affiliations that the institution with the most students has that influence its enrollment?', 'sql': 'SELECT a.long_name, a.short_name \nFROM affiliations a \nJOIN institutions i ON a.institution_id = i.id \nWHERE i.id = (\n    SELECT institution_id \n    FROM students \n    GROUP BY institution_id \n    ORDER BY COUNT(*) DESC \n    LIMIT 1\n)', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the most students by grouping the students by institution_id, counting them, and ordering them in descending order to find the top institution. It then retrieves the affiliations associated with that institution by joining the affiliations table with the institutions table based on the institution_id. The query returns the long_name and short_name of the affiliations, which directly relates to the question about partnerships or affiliations influencing enrollment.', 'feedback': "The question could be clarified by specifying what is meant by 'influence its enrollment.' For example, does it refer to the nature of the partnerships, their impact on student numbers, or something else? Additionally, the SQL could be improved by including a check for the type of affiliations if such a classification exists in the schema."}
2025-08-08 00:33:55,499 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:33:55,500 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 00:33:59,254 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:33:59,401 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:00,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:01,302 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:03,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:03,906 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:06,318 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:07,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:07,048 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do these partnerships specifically enhance the reputation and resources of the University of Owek, and can you provide examples of programs or initiatives that have resulted from these collaborations?', 'answerable': False, 'reasoning': 'The question asks about the impact of partnerships on the reputation and resources of the University of Owek, as well as specific examples of programs or initiatives resulting from these collaborations. However, the provided database schema does not contain any tables or fields that directly relate to partnerships, collaborations, or their outcomes. The schema primarily focuses on institutional data, student information, academic programs, and administrative records, but lacks any reference to partnerships or their effects on reputation and resources.', 'feedback': ''}
2025-08-08 00:34:07,048 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:34:07,048 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:34:07,048 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'How do these partnerships specifically enhance the reputation and resources of the University of Owek, and can you provide examples of programs or initiatives that have resulted from these collaborations?', 'answerable': False, 'reasoning': 'The question asks about the impact of partnerships on the reputation and resources of the University of Owek, as well as specific examples of programs or initiatives resulting from these collaborations. However, the provided database schema does not contain any tables or fields that directly relate to partnerships, collaborations, or their outcomes. The schema primarily focuses on institutional data, student information, academic programs, and administrative records, but lacks any reference to partnerships or their effects on reputation and resources.', 'feedback': ''}
2025-08-08 00:34:08,442 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:09,810 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:16,361 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:22,726 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:22,735 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What programs or courses are most popular at the institution with the most students?', 'sql': "WITH StudentCounts AS (SELECT institution_id, COUNT(*) AS student_count FROM students GROUP BY institution_id ORDER BY student_count DESC LIMIT 1), PopularPrograms AS (SELECT p.long_name AS program_name, COUNT(s.id) AS student_count FROM programs p JOIN students s ON p.institution_id = s.institution_id WHERE p.institution_id IN (SELECT institution_id FROM StudentCounts) GROUP BY p.long_name ORDER BY student_count DESC), PopularCourses AS (SELECT c.title AS course_title, COUNT(s.id) AS student_count FROM courses c JOIN students s ON c.institution_id = s.institution_id WHERE c.institution_id IN (SELECT institution_id FROM StudentCounts) GROUP BY c.title ORDER BY student_count DESC) SELECT 'Program' AS type, program_name AS name, student_count FROM PopularPrograms UNION ALL SELECT 'Course' AS type, course_title AS name, student_count FROM PopularCourses;", 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the most students by counting the number of students per institution and selecting the one with the highest count. It then retrieves the most popular programs and courses associated with that institution by counting the number of students enrolled in each program and course. The use of CTEs (Common Table Expressions) allows for clear separation of logic, and the final UNION combines the results for both programs and courses, which aligns with the question's request for both types of offerings.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating the maximum number of results to return for programs and courses, as the question does not specify whether to return all or just the top results. Additionally, consider adding a limit to the final SELECT statements to return only the top N programs and courses if that is a requirement.'}
2025-08-08 00:34:22,735 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:34:22,735 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 00:34:54,566 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:55,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:56,693 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:34:58,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:01,922 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:01,936 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights or opinions rather than specific data points. The schema provided contains a wealth of data about institutions, programs, courses, and students, but it does not include qualitative factors or insights that would explain popularity, such as student satisfaction, market demand, or institutional reputation. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 00:35:01,936 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:35:01,936 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:35:01,936 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think contribute to the popularity of certain programs or courses at large institutions, even if specific data is not available?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to the popularity of programs or courses at large institutions. This is a qualitative question that requires insights or opinions rather than specific data points. The schema provided contains a wealth of data about institutions, programs, courses, and students, but it does not include qualitative factors or insights that would explain popularity, such as student satisfaction, market demand, or institutional reputation. Therefore, the question cannot be answered directly or indirectly from the schema.', 'feedback': ''}
2025-08-08 00:35:01,937 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 00:35:01,937 - root - INFO - 'No results'
2025-08-08 00:35:01,937 - root - INFO - 'No results'
2025-08-08 00:35:01,938 - root - INFO - 'No results'
2025-08-08 00:35:01,938 - root - INFO - 'No results'
2025-08-08 00:35:01,938 - root - INFO - 'No results'
2025-08-08 00:35:01,938 - root - INFO - 'No results'
2025-08-08 00:35:01,938 - root - INFO - 'No results'
2025-08-08 00:35:01,938 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-08 00:35:01,938 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 7
2025-08-08 00:35:01,938 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 00:35:07,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:07,865 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 00:35:18,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:18,429 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:18,429 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 00:35:18,430 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:18,430 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/7
2025-08-08 00:35:18,430 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:18,430 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:35:18,430 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:18,430 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What is the institution with the most students?'
2025-08-08 00:35:18,430 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:35:18,430 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:35:18,430 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:35:18,430 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:35:18,430 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors contribute to such a high enrollment number at this institution?...
2025-08-08 00:35:18,430 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:35:18,430 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:35:18,430 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:35:18,430 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:35:18,430 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:35:18,431 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:35:18,431 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:35:18,431 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/7
2025-08-08 00:35:18,431 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:18,431 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:35:18,431 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:18,431 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What is the institution with the most students?'
2025-08-08 00:35:18,431 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:35:18,431 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:35:18,431 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:35:18,431 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:35:18,431 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contribute to ITC University's ability to attract and maintain such a larg...
2025-08-08 00:35:18,431 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:35:18,431 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:35:18,431 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:35:18,431 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:35:18,432 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:35:18,432 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:35:18,432 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:35:18,432 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/7
2025-08-08 00:35:18,432 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:18,432 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:35:18,432 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:18,432 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What is the institution with the most students?'
2025-08-08 00:35:18,432 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:35:18,432 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:35:18,432 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:35:18,432 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:35:18,432 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contribute to the popularity of certain programs or courses at large insti...
2025-08-08 00:35:18,432 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:35:18,432 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:35:18,432 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:35:18,433 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:35:18,433 - REPORT_PIPELINE - INFO - 🔄 Processing interview 4/7
2025-08-08 00:35:18,433 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:18,433 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:35:18,433 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What is the institution with the most students?'
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:35:18,433 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What are the racial and ethnic demographics of the student population at this institution?...
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:35:18,433 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:35:18,434 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:35:18,434 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:35:18,434 - REPORT_PIPELINE - INFO - 🔄 Processing interview 5/7
2025-08-08 00:35:18,434 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:18,434 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:35:18,434 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:18,434 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What is the institution with the most students?'
2025-08-08 00:35:18,434 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:35:18,434 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:35:18,434 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:35:18,434 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:35:18,434 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contribute to the retention rates at large institutions, and how might the...
2025-08-08 00:35:18,434 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:35:18,434 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:35:18,434 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:35:18,434 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:35:18,435 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:35:18,435 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:35:18,435 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:35:18,435 - REPORT_PIPELINE - INFO - 🔄 Processing interview 6/7
2025-08-08 00:35:18,435 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:18,435 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:35:18,435 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:18,435 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What is the institution with the most students?'
2025-08-08 00:35:18,435 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:35:18,435 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:35:18,435 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:35:18,435 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:35:18,435 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think have contributed to the stability of the student population at this instit...
2025-08-08 00:35:18,436 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:35:18,436 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:35:18,436 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:35:18,436 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:35:18,436 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:35:18,436 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:35:18,436 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:35:18,436 - REPORT_PIPELINE - INFO - 🔄 Processing interview 7/7
2025-08-08 00:35:18,436 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:18,436 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:35:18,436 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:18,436 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'What is the institution with the most students?'
2025-08-08 00:35:18,436 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:35:18,436 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:35:18,436 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:35:18,436 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:35:18,437 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How do these partnerships specifically enhance the reputation and resources of the University of Owe...
2025-08-08 00:35:18,437 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:35:18,437 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:35:18,437 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:35:18,437 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:35:18,437 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:35:18,437 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:35:18,437 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:35:18,437 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 7
2025-08-08 00:35:18,437 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:18,437 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 00:35:18,437 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 7 documents
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -     Content: Question: What factors contribute to such a high enrollment number at this institution?
Answer: I en...
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:33:33.637775+00:00', 'data_returned': False}
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contribute to ITC University's ability to attract and maintain s...
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:33:33.560713+00:00', 'data_returned': False}
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contribute to the popularity of certain programs or courses at l...
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:35:01.936846+00:00', 'data_returned': False}
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -     Content: Question: What are the racial and ethnic demographics of the student population at this institution?...
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:33:19.938433+00:00', 'data_returned': False}
2025-08-08 00:35:18,437 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 00:35:18,438 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contribute to the retention rates at large institutions, and how...
2025-08-08 00:35:18,438 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:33:22.720074+00:00', 'data_returned': False}
2025-08-08 00:35:18,438 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-08 00:35:18,438 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think have contributed to the stability of the student population at t...
2025-08-08 00:35:18,438 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:33:54.549695+00:00', 'data_returned': False}
2025-08-08 00:35:18,438 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-08 00:35:18,438 - UPSERT_DOCS - INFO -     Content: Question: How do these partnerships specifically enhance the reputation and resources of the Univers...
2025-08-08 00:35:18,438 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'What is the institution with the most students?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:34:07.048811+00:00', 'data_returned': False}
2025-08-08 00:35:18,438 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 0/7
2025-08-08 00:35:18,670 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.232s]
2025-08-08 00:35:19,332 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:35:22,182 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:2.271s]
2025-08-08 00:35:22,183 - UPSERT_DOCS - INFO - ✅ Successfully upserted 7 documents to Elasticsearch
2025-08-08 00:35:22,324 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.141s]
2025-08-08 00:35:22,325 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 25
2025-08-08 00:35:22,466 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.140s]
2025-08-08 00:35:22,466 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:35:23,425 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.959s]
2025-08-08 00:35:23,426 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (2):
2025-08-08 00:35:23,426 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (18 docs)
2025-08-08 00:35:23,426 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 00:35:23,426 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 8
2025-08-08 00:35:23,426 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 00:35:23,427 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:23,427 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-08 00:35:23,427 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:23,427 - REPORT_PIPELINE - INFO - ✍️ Writing 8 sections using batch processing...
2025-08-08 00:35:24,124 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:24,130 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:24,130 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:35:24,130 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:24,130 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion Key Insights Final Thoughts'
2025-08-08 00:35:24,130 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-08 00:35:24,130 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-08 00:35:24,179 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:24,183 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:24,185 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:24,185 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:35:24,185 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:24,185 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'appendices interview transcripts data charts'
2025-08-08 00:35:24,185 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-08 00:35:24,185 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-08 00:35:24,188 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:24,188 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:35:24,189 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:24,189 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Methodology Data Collection Interview Process Limitations'
2025-08-08 00:35:24,189 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-08 00:35:24,189 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-08 00:35:24,268 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:24,283 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.152s]
2025-08-08 00:35:24,283 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 25
2025-08-08 00:35:24,285 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:24,286 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:35:24,286 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:24,286 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'key terms institution student enrollment types of institutions'
2025-08-08 00:35:24,286 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-08 00:35:24,286 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-08 00:35:24,336 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:24,343 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:24,343 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:35:24,343 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:24,343 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'highest student enrollment institution'
2025-08-08 00:35:24,343 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-08 00:35:24,343 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-08 00:35:24,418 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 00:35:24,419 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:35:24,428 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:24,436 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:24,437 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:35:24,437 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:24,437 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Discussion of findings implications future research higher education'
2025-08-08 00:35:24,437 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-08 00:35:24,437 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-08 00:35:24,460 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.274s]
2025-08-08 00:35:24,460 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 25
2025-08-08 00:35:24,474 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.285s]
2025-08-08 00:35:24,474 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 25
2025-08-08 00:35:24,626 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.207s]
2025-08-08 00:35:24,626 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 00:35:24,635 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.161s]
2025-08-08 00:35:24,636 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.350s]
2025-08-08 00:35:24,636 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:35:24,636 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 25
2025-08-08 00:35:24,668 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.325s]
2025-08-08 00:35:24,669 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.209s]
2025-08-08 00:35:24,669 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 25
2025-08-08 00:35:24,670 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:35:24,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:24,696 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:24,697 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:35:24,697 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:24,697 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Interview findings enrollment trends'
2025-08-08 00:35:24,697 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-08 00:35:24,697 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-08 00:35:24,808 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.172s]
2025-08-08 00:35:24,809 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:35:24,810 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.140s]
2025-08-08 00:35:24,810 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.374s]
2025-08-08 00:35:24,811 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:35:24,811 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 25
2025-08-08 00:35:24,812 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.142s]
2025-08-08 00:35:24,812 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 00:35:24,813 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.176s]
2025-08-08 00:35:24,813 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 00:35:24,824 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.198s]
2025-08-08 00:35:24,824 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:35:24,996 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.183s]
2025-08-08 00:35:24,997 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.188s]
2025-08-08 00:35:24,998 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.185s]
2025-08-08 00:35:24,998 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:35:24,998 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 00:35:24,998 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:35:25,016 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.205s]
2025-08-08 00:35:25,017 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.205s]
2025-08-08 00:35:25,017 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 00:35:25,017 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:35:25,157 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.459s]
2025-08-08 00:35:25,157 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 25
2025-08-08 00:35:25,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:35:25,240 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.241s]
2025-08-08 00:35:25,240 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:35:25,375 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.357s]
2025-08-08 00:35:25,375 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:35:25,413 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.394s]
2025-08-08 00:35:25,413 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 00:35:25,425 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.268s]
2025-08-08 00:35:25,426 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:35:25,537 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:35:25,579 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.339s]
2025-08-08 00:35:25,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:35:25,580 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:35:25,581 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:35:25,664 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:25,669 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:25,669 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:35:25,669 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:25,669 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'References Sources Acknowledgments'
2025-08-08 00:35:25,669 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'What is the institution with the most students?'
2025-08-08 00:35:25,669 - VECTOR_SEARCH - INFO - ❓ Original Question: 'What is the institution with the most students?'
2025-08-08 00:35:25,735 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.309s]
2025-08-08 00:35:25,735 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.322s]
2025-08-08 00:35:25,736 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 00:35:25,736 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:35:25,758 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.173s]
2025-08-08 00:35:25,759 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:35:25,759 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:35:25,892 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:35:26,196 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.527s]
2025-08-08 00:35:26,197 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.461s]
2025-08-08 00:35:26,197 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 25
2025-08-08 00:35:26,197 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:35:26,199 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.303s]
2025-08-08 00:35:26,200 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.660s]
2025-08-08 00:35:26,200 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:35:26,200 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:35:26,201 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:35:26,202 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:35:26,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:35:26,514 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:35:26,559 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:35:26,638 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.440s]
2025-08-08 00:35:26,638 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:35:26,670 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.152s]
2025-08-08 00:35:26,670 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.340s]
2025-08-08 00:35:26,671 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:35:26,671 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:35:26,671 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:35:26,672 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:35:27,258 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.619s]
2025-08-08 00:35:27,258 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 7
2025-08-08 00:35:27,319 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.756s]
2025-08-08 00:35:27,320 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:35:27,320 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:35:27,712 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.453s]
2025-08-08 00:35:27,713 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:35:28,137 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:35:28,649 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.508s]
2025-08-08 00:35:28,649 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:35:28,650 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:35:30,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:30,726 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:31,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:32,061 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:33,602 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:34,618 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:34,642 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:34,998 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:35,010 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 328 characters
2025-08-08 00:35:35,011 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 1412 characters
2025-08-08 00:35:35,011 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1416 characters
2025-08-08 00:35:35,011 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 2073 characters
2025-08-08 00:35:35,012 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1481 characters
2025-08-08 00:35:35,012 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 875 characters
2025-08-08 00:35:35,012 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 519 characters
2025-08-08 00:35:35,012 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 265 characters
2025-08-08 00:35:37,934 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:35:37,939 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:35:37,939 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:35:37,939 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:35:37,939 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:35:37,939 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:35:37,939 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:35:37,939 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:35:37,939 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:35:37,939 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:35:37,939 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:35:37,939 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-08 00:35:37,939 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:35:37,939 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-08 00:35:37,940 - REPORT_PIPELINE - INFO - 📊 Final report sections: 9
2025-08-08 00:35:37,940 - REPORT_PIPELINE - INFO - 📈 Extended data items: 0
2025-08-08 00:35:37,940 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_003224.log
2025-08-08 00:35:37,942 - celery.app.trace - INFO - Task generate_report[34d7c06f-0967-4839-ba72-38e7e1875ffb] succeeded in 193.8746399590018s: {'outline': '# Report Outline: Investigating the Institution with the Most Students

## I. Introduction  
The purpose of this report is to investigate and identify the institution with the highest student enrollment. The key finding indicates that the institution with the most students is a large public university, which has implemented various strategies to attract and retain a significant number of students.

## II. Definition of Key Terms  
- Institution  
- Student Enrollment  
- Types of Institutions (e.g., universities, colleges, online institutions)  

## III. Methodology  
- Overview of Data Collection  
- Description of Interview Process  
- Limitations of the Study  

## IV. Findings  
- Summary of Interview Insights  
- Key Themes Identified  
   - Enrollment Numbers  
   - Institutional Types  
   - Geographic Distribution  
- Comparative Analysis  
   - Largest Institutions by Enrollment  
   - Trends in Student Enrollment  

## V. Discussion  
- Interpretation of Findings  
- Implications for...', ...}
2025-08-08 00:36:27,881 - celery.worker.strategy - INFO - Task generate_report[ef16a01f-eb0a-4369-b902-d6c27b027b8e] received
2025-08-08 00:36:27,882 - app.tasks.report_task - INFO - starting...
2025-08-08 00:36:27,882 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:36:27,882 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: acd04f6b-7955-468c-bf81-4729a6607a24
2025-08-08 00:36:27,882 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:36:27,883 - REPORT_REQUEST - INFO - 📝 Original Question: 'How are girls performing in ITC University?'
2025-08-08 00:36:27,883 - REPORT_REQUEST - INFO - 🆔 Task ID: acd04f6b-7955-468c-bf81-4729a6607a24
2025-08-08 00:36:27,883 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T00:36:27.883144
2025-08-08 00:36:28,041 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.158s]
2025-08-08 00:36:28,041 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 25
2025-08-08 00:36:28,169 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-08 00:36:28,170 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:36:28,347 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.177s]
2025-08-08 00:36:28,348 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (2):
2025-08-08 00:36:28,348 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (18 docs)
2025-08-08 00:36:28,348 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 00:36:28,348 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 00:36:39,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:39,111 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 00:36:46,268 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:46,282 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 00:36:48,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:48,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:48,222 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:48,329 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:48,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:48,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:48,478 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:48,487 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:48,726 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:48,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:48,800 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 00:36:48,801 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:36:48,801 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 00:36:48,801 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:36:48,801 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 1
2025-08-08 00:36:51,281 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:53,872 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:55,575 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:36:59,558 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:04,085 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:04,091 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How do the academic results of girls compare to those of boys at ITC University in IT-related courses?', 'sql': "SELECT s.sex, AVG(ar.finalscore) AS average_final_score, COUNT(ar.id) AS total_students\nFROM assessment_results ar\nJOIN students s ON ar.student_id = s.id\nJOIN programs p ON ar.course_id = p.id\nWHERE p.programme_type = 'IT'\nAND s.institution_id = (SELECT id FROM institutions WHERE name = 'ITC University')\nGROUP BY s.sex;", 'correct': True, 'reasoning': "The SQL query correctly retrieves the average final scores and total number of students for each sex (girls and boys) enrolled in IT-related courses at ITC University. It joins the necessary tables (assessment_results, students, and programs) and filters the results based on the specified institution and program type. The use of GROUP BY on 's.sex' allows for a comparison between the two groups, which directly addresses the question.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly including the institution name in the SELECT statement for clarity in the results, or by ensuring that the institution_id is correctly referenced if the institutions table is not part of the provided schema.'}
2025-08-08 00:37:04,091 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:37:04,091 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-08 00:37:08,180 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:08,896 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:10,167 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:12,677 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:14,731 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:14,754 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think could influence the academic performance of girls and boys in IT-related courses, even if we don't have specific data from ITC University?", 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance in IT-related courses, which is a qualitative analysis rather than a quantitative one. The provided schema contains tables related to institutions, students, courses, and various academic records, but it does not include any specific data or attributes that would allow for an analysis of factors influencing performance based on gender or course type. Additionally, the schema does not provide any qualitative insights or external factors that could affect academic performance.', 'feedback': ''}
2025-08-08 00:37:14,755 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-08 00:37:14,755 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-08 00:37:14,755 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think could influence the academic performance of girls and boys in IT-related courses, even if we don't have specific data from ITC University?", 'answerable': False, 'reasoning': 'The question asks for factors influencing academic performance in IT-related courses, which is a qualitative analysis rather than a quantitative one. The provided schema contains tables related to institutions, students, courses, and various academic records, but it does not include any specific data or attributes that would allow for an analysis of factors influencing performance based on gender or course type. Additionally, the schema does not provide any qualitative insights or external factors that could affect academic performance.', 'feedback': ''}
2025-08-08 00:37:14,755 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 00:37:14,755 - root - INFO - 'No results'
2025-08-08 00:37:14,755 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-08 00:37:14,756 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 1
2025-08-08 00:37:14,756 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 00:37:22,936 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:22,963 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 00:37:30,459 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:30,467 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:30,467 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 00:37:30,467 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:30,467 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/1
2025-08-08 00:37:30,467 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:30,467 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 00:37:30,468 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How are girls performing in ITC University?'
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 00:37:30,468 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think could influence the academic performance of girls and boys in IT-related c...
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 00:37:30,468 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 00:37:30,468 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 00:37:30,468 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 1
2025-08-08 00:37:30,468 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:30,468 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 00:37:30,468 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:30,468 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 1 documents
2025-08-08 00:37:30,468 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 00:37:30,469 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think could influence the academic performance of girls and boys in IT...
2025-08-08 00:37:30,469 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How are girls performing in ITC University?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:37:14.755662+00:00', 'data_returned': False}
2025-08-08 00:37:30,469 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 0/1
2025-08-08 00:37:30,606 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.138s]
2025-08-08 00:37:31,221 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:37:33,318 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:2.091s]
2025-08-08 00:37:33,319 - UPSERT_DOCS - INFO - ✅ Successfully upserted 1 documents to Elasticsearch
2025-08-08 00:37:33,614 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.294s]
2025-08-08 00:37:33,614 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 26
2025-08-08 00:37:33,926 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.311s]
2025-08-08 00:37:33,926 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:37:34,421 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.495s]
2025-08-08 00:37:34,422 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (3):
2025-08-08 00:37:34,422 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (18 docs)
2025-08-08 00:37:34,422 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 00:37:34,422 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing in ITC University?' (1 docs)
2025-08-08 00:37:34,422 - REPORT_PIPELINE - INFO - 📋 Outline sections to write: 9
2025-08-08 00:37:34,423 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-08 00:37:34,424 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:34,424 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS
2025-08-08 00:37:34,424 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:34,424 - REPORT_PIPELINE - INFO - ✍️ Writing 9 sections using batch processing...
2025-08-08 00:37:35,135 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:35,149 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:35,150 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:35,150 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:37:35,150 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:35,150 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'interview methodology data collection criteria'
2025-08-08 00:37:35,150 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,150 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,183 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:35,183 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:37:35,183 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:35,183 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'sources and literature review'
2025-08-08 00:37:35,184 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,184 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,240 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:35,257 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:35,258 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:37:35,258 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:35,258 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'IT education trends for girls'
2025-08-08 00:37:35,258 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,258 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,261 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:35,265 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:35,266 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:37:35,266 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:35,266 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'supporting girls in ITC University'
2025-08-08 00:37:35,266 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,266 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,304 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.154s]
2025-08-08 00:37:35,305 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 26
2025-08-08 00:37:35,319 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 00:37:35,319 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 26
2025-08-08 00:37:35,375 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:35,381 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:35,381 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:37:35,381 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:35,381 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'improving support for female students in IT'
2025-08-08 00:37:35,381 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,381 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,390 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:35,391 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-08 00:37:35,391 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:35,392 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 26
2025-08-08 00:37:35,396 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:35,396 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:37:35,396 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:35,396 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'girls academic performance ITC University'
2025-08-08 00:37:35,396 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,396 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,400 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:35,400 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:37:35,400 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:35,400 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Appendices interview transcripts data resources'
2025-08-08 00:37:35,400 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,400 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,405 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.147s]
2025-08-08 00:37:35,406 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 26
2025-08-08 00:37:35,442 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-08 00:37:35,442 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.123s]
2025-08-08 00:37:35,442 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:37:35,442 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:37:35,582 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.176s]
2025-08-08 00:37:35,582 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.186s]
2025-08-08 00:37:35,583 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:37:35,583 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 26
2025-08-08 00:37:35,593 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.212s]
2025-08-08 00:37:35,594 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 26
2025-08-08 00:37:35,594 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.194s]
2025-08-08 00:37:35,594 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.202s]
2025-08-08 00:37:35,595 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 26
2025-08-08 00:37:35,595 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:37:35,639 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.196s]
2025-08-08 00:37:35,639 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 1
2025-08-08 00:37:35,649 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.207s]
2025-08-08 00:37:35,650 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 1
2025-08-08 00:37:35,755 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.160s]
2025-08-08 00:37:35,756 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:37:35,762 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.179s]
2025-08-08 00:37:35,762 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 1
2025-08-08 00:37:35,763 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.170s]
2025-08-08 00:37:35,764 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.180s]
2025-08-08 00:37:35,764 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:37:35,764 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:37:35,765 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.169s]
2025-08-08 00:37:35,766 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 1
2025-08-08 00:37:35,769 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:35,772 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-08 00:37:35,772 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:37:35,779 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:35,779 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:37:35,779 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:35,779 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'girls performance ITC University'
2025-08-08 00:37:35,779 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,779 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing in ITC University?'
2025-08-08 00:37:35,844 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.194s]
2025-08-08 00:37:35,845 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:37:35,965 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.209s]
2025-08-08 00:37:35,966 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 1
2025-08-08 00:37:35,967 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.202s]
2025-08-08 00:37:35,967 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.188s]
2025-08-08 00:37:35,968 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 1
2025-08-08 00:37:35,968 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 26
2025-08-08 00:37:35,968 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.203s]
2025-08-08 00:37:35,969 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.206s]
2025-08-08 00:37:35,969 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.203s]
2025-08-08 00:37:35,969 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 1
2025-08-08 00:37:35,969 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:37:35,970 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:37:36,070 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:37:36,234 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:36,245 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:36,245 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 00:37:36,245 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:36,245 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Executive Summary Overview'
2025-08-08 00:37:36,245 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How are girls performing in ITC University?'
2025-08-08 00:37:36,245 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How are girls performing in ITC University?'
2025-08-08 00:37:36,267 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:37:36,389 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:37:36,492 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.524s]
2025-08-08 00:37:36,493 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:37:36,534 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.568s]
2025-08-08 00:37:36,535 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:37:36,572 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.604s]
2025-08-08 00:37:36,572 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:37:36,666 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.696s]
2025-08-08 00:37:36,666 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.421s]
2025-08-08 00:37:36,667 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:37:36,667 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 26
2025-08-08 00:37:36,778 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.206s]
2025-08-08 00:37:36,779 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 1
2025-08-08 00:37:36,779 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.508s]
2025-08-08 00:37:36,780 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:37:36,780 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:37:36,863 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:37:36,900 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.232s]
2025-08-08 00:37:36,900 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.508s]
2025-08-08 00:37:36,901 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 00:37:36,901 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:37:36,902 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:37:37,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:37:37,141 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.067s]
2025-08-08 00:37:37,142 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:37:37,142 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:37:37,205 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:37:37,411 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.631s]
2025-08-08 00:37:37,412 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:37:37,551 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.649s]
2025-08-08 00:37:37,551 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 1
2025-08-08 00:37:37,565 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.697s]
2025-08-08 00:37:37,565 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:37:37,565 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:37:37,595 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.563s]
2025-08-08 00:37:37,595 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:37:37,595 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:37:37,613 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.403s]
2025-08-08 00:37:37,613 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:37:37,613 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:37:37,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:37:37,926 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.375s]
2025-08-08 00:37:37,926 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 00:37:37,981 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:37:38,242 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 00:37:38,293 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.428s]
2025-08-08 00:37:38,293 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:37:38,293 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:37:38,899 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.914s]
2025-08-08 00:37:38,900 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.653s]
2025-08-08 00:37:38,901 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:37:38,901 - VECTOR_SEARCH - INFO - 🎯 Vector search returned 0 documents
2025-08-08 00:37:38,901 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:37:38,901 - VECTOR_SEARCH - WARNING - ⚠️ No documents returned from vector search!
2025-08-08 00:37:40,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:41,050 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:41,665 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:41,799 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:41,866 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:42,178 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:42,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:44,021 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:44,548 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:44,561 - REPORT_PIPELINE - INFO -   ✅ Section 1 completed: 760 characters
2025-08-08 00:37:44,562 - REPORT_PIPELINE - INFO -   ✅ Section 2 completed: 863 characters
2025-08-08 00:37:44,562 - REPORT_PIPELINE - INFO -   ✅ Section 3 completed: 1226 characters
2025-08-08 00:37:44,563 - REPORT_PIPELINE - INFO -   ✅ Section 4 completed: 1938 characters
2025-08-08 00:37:44,563 - REPORT_PIPELINE - INFO -   ✅ Section 5 completed: 1785 characters
2025-08-08 00:37:44,563 - REPORT_PIPELINE - INFO -   ✅ Section 6 completed: 1057 characters
2025-08-08 00:37:44,563 - REPORT_PIPELINE - INFO -   ✅ Section 7 completed: 879 characters
2025-08-08 00:37:44,563 - REPORT_PIPELINE - INFO -   ✅ Section 8 completed: 305 characters
2025-08-08 00:37:44,564 - REPORT_PIPELINE - INFO -   ✅ Section 9 completed: 805 characters
2025-08-08 00:37:45,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 00:37:45,972 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:37:45,972 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:37:45,972 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:37:45,972 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:37:45,972 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:37:45,972 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:37:45,972 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:37:45,972 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:37:45,972 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:37:45,972 - app.chains.composite.report_pipeline - INFO - No tag pattern found in item
2025-08-08 00:37:45,972 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 00:37:45,972 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION COMPLETED
2025-08-08 00:37:45,972 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 00:37:45,972 - REPORT_PIPELINE - INFO - ✅ Report generation successful!
2025-08-08 00:37:45,972 - REPORT_PIPELINE - INFO - 📊 Final report sections: 10
2025-08-08 00:37:45,972 - REPORT_PIPELINE - INFO - 📈 Extended data items: 0
2025-08-08 00:37:45,973 - REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250808_003224.log
2025-08-08 00:37:45,974 - celery.app.trace - INFO - Task generate_report[ef16a01f-eb0a-4369-b902-d6c27b027b8e] succeeded in 78.09297741600312s: {'outline': '# Report on Girls\' Performance in ITC University

## Executive Summary  
- Brief overview of the report\'s purpose and findings  

## Introduction  
- Background on ITC University  
- Importance of gender representation in IT fields  
- Statement of the guiding question: How are girls performing in ITC University?

## Methodology  
- Description of the interview process  
- Criteria for selecting interview participants  
- Overview of data collection methods, including surveys and academic records analysis  

## Key Themes and Insights  
### Academic Performance  
- Overview of girls\' academic achievements in ITC University  
- Comparison of average grades and GPA with male counterparts  
- Performance in specific IT courses and graduation rates  

### Participation in IT Programs  
- Enrollment statistics for girls in IT programs  
- Historical trends and current statistics compared to male enrollment  

### Challenges Faced by Female Students  
- Common obstacles reported by interviewees,...', ...}

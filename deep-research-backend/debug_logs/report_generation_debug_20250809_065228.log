2025-08-09 06:52:28,128 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250809_065228.log
2025-08-09 06:52:28,129 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:52:28,129 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: c06525bb-8f97-4d17-a5c2-d974a45f0056
2025-08-09 06:52:28,129 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:52:28,129 - REPORT_REQUEST - INFO - 📝 Original Question: 'From which instituion do students owe the most fees?'
2025-08-09 06:52:28,129 - REPORT_REQUEST - INFO - 🆔 Task ID: c06525bb-8f97-4d17-a5c2-d974a45f0056
2025-08-09 06:52:28,129 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-09T06:52:28.129886
2025-08-09 06:52:28,279 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.149s]
2025-08-09 06:52:28,279 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 52
2025-08-09 06:52:28,462 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.183s]
2025-08-09 06:52:28,462 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 20
2025-08-09 06:52:28,803 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.341s]
2025-08-09 06:52:28,804 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (4):
2025-08-09 06:52:28,804 - ELASTICSEARCH_STATE - INFO -   - 'From which institution do students owe the most fees?' (20 docs)
2025-08-09 06:52:28,804 - ELASTICSEARCH_STATE - INFO -   - 'From which instituion do students owe the most fees?' (18 docs)
2025-08-09 06:52:28,804 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (7 docs)
2025-08-09 06:52:28,804 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (7 docs)
2025-08-09 06:52:28,804 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:52:28,804 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-09 06:52:28,805 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:52:28,805 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: From which instituion do students owe the most fees?
2025-08-09 06:52:28,805 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-09 06:52:28,805 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-09 06:52:36,653 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:36,676 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-09 06:52:40,783 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:40,803 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-09 06:52:43,421 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:43,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:43,800 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:43,885 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:44,005 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:44,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:44,315 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:44,360 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:44,458 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:44,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:44,696 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-09 06:52:46,994 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:47,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:47,318 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:47,961 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:48,069 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:48,075 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:48,462 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:49,533 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:49,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:49,743 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:50,787 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:50,793 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:50,965 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:51,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:51,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:51,343 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:51,830 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:54,301 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:54,301 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:54,401 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:54,815 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:54,835 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:55,500 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:57,480 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:58,118 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:59,392 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:52:59,403 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total amount of fees owed by students at the institution where students owe the most fees?', 'sql': 'SELECT institution_id, SUM(balance) AS total_fees_owed\nFROM student_balances\nGROUP BY institution_id\nORDER BY total_fees_owed DESC\nLIMIT 1;', 'correct': True, 'reasoning': "The SQL query correctly aggregates the total balance owed by students for each institution by summing the 'balance' field in the 'student_balances' table. It groups the results by 'institution_id' and orders them in descending order based on the total fees owed. The use of 'LIMIT 1' ensures that only the institution with the highest total fees owed is returned, which directly answers the question.", 'feedback': "The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to include a 'SELECT' clause that explicitly states the total fees owed for clarity, such as 'SELECT SUM(balance) AS total_fees_owed'."}
2025-08-09 06:52:59,404 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:52:59,404 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:53:00,406 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:00,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:01,572 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:01,577 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average fee debt per student at the institution where students owe the most fees?', 'sql': 'SELECT AVG(balance) AS average_fee_debt\nFROM student_balances\nWHERE institution_id = (\n    SELECT institution_id\n    FROM student_balances\n    GROUP BY institution_id\n    ORDER BY SUM(balance) DESC\n    LIMIT 1\n)', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution where students owe the most fees by summing the balances for each institution and ordering them in descending order. It then limits the result to the top institution. The outer query calculates the average balance (fee debt) for students at that institution. This aligns with the question's requirement to find the average fee debt per student at the institution with the highest total fees owed.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly stating that the average is calculated per student, which may require dividing the total balance by the count of students at that institution to ensure clarity in the average calculation.'}
2025-08-09 06:53:01,578 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:01,578 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:53:01,734 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:02,109 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:02,936 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:03,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:04,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:04,413 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:05,309 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:05,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:05,369 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the fee debt at the institution where students owe the most fees compare to other institutions?', 'sql': 'WITH InstitutionDebt AS (  SELECT institution_id, SUM(balance) AS total_debt  FROM student_balances  GROUP BY institution_id), MaxDebtInstitution AS (  SELECT institution_id, total_debt  FROM InstitutionDebt  ORDER BY total_debt DESC  LIMIT 1) SELECT i.name, d.total_debt FROM institutions i JOIN InstitutionDebt d ON i.id = d.institution_id UNION ALL SELECT i.name, d.total_debt FROM institutions i JOIN InstitutionDebt d ON i.id = d.institution_id WHERE d.institution_id != (SELECT institution_id FROM MaxDebtInstitution);', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total debt by summing the balances from the 'student_balances' table and grouping by 'institution_id'. It then selects the institution with the maximum debt and compares it to all other institutions by using a UNION ALL to include the debt of all institutions except the one with the maximum debt. This effectively answers the question of how the fee debt at the institution with the highest debt compares to others.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly labeling the results to clarify which institution has the highest debt and which ones are being compared. Adding a column to indicate whether the institution is the one with the highest debt could enhance clarity.'}
2025-08-09 06:53:05,370 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:05,370 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:53:06,430 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:06,453 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:06,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:07,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:07,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:07,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:08,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:08,514 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or relationships that would allow for a comprehensive analysis of the factors leading to credit balances or their implications on financial situations. The schema lacks qualitative data or insights into the reasons behind credit balances, such as student behavior, financial aid policies, or other contextual factors.', 'feedback': ''}
2025-08-09 06:53:08,514 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:08,515 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:08,515 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or relationships that would allow for a comprehensive analysis of the factors leading to credit balances or their implications on financial situations. The schema lacks qualitative data or insights into the reasons behind credit balances, such as student behavior, financial aid policies, or other contextual factors.', 'feedback': ''}
2025-08-09 06:53:08,749 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:08,926 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:09,400 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:09,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:10,009 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:10,375 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:10,526 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:10,655 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:11,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:11,582 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:11,784 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:11,799 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications on financial situations. The schema lacks detailed financial behavior data, contextual information about student spending, income sources, or financial aid specifics that would be necessary to answer the question fully.', 'feedback': ''}
2025-08-09 06:53:11,799 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:11,799 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:11,799 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications on financial situations. The schema lacks detailed financial behavior data, contextual information about student spending, income sources, or financial aid specifics that would be necessary to answer the question fully.', 'feedback': ''}
2025-08-09 06:53:12,227 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:12,973 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:13,934 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:13,935 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:13,936 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:13,961 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial situation. While the schema contains various tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors that contribute to credit balances or their broader financial implications. The schema lacks specific data on the reasons behind credit balances or how they affect students' financial situations, making it impossible to answer the question completely.", 'feedback': ''}
2025-08-09 06:53:13,961 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:13,961 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:13,961 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial situation. While the schema contains various tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors that contribute to credit balances or their broader financial implications. The schema lacks specific data on the reasons behind credit balances or how they affect students' financial situations, making it impossible to answer the question completely.", 'feedback': ''}
2025-08-09 06:53:14,131 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:14,198 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:15,664 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:15,680 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:15,967 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:16,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:16,451 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:16,471 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. However, the provided schema does not contain specific data or metrics related to fee debt or the factors influencing it. While there are tables related to billing, students, and institutions, there is no direct correlation or analysis capability within the schema to determine the reasons for fee debt differences across institutions. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:53:16,471 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:16,471 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:16,471 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. However, the provided schema does not contain specific data or metrics related to fee debt or the factors influencing it. While there are tables related to billing, students, and institutions, there is no direct correlation or analysis capability within the schema to determine the reasons for fee debt differences across institutions. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:53:16,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:16,664 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or historical data that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 06:53:16,665 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:16,665 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:16,665 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or historical data that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 06:53:17,645 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:18,624 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:18,638 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial situation. While the schema contains various tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors that contribute to credit balances or their implications on students' financial situations. The schema lacks specific data on the reasons behind credit balances or a comprehensive view of students' financial health, making it impossible to answer the question fully.", 'feedback': ''}
2025-08-09 06:53:18,639 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:18,639 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:18,639 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial situation. While the schema contains various tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors that contribute to credit balances or their implications on students' financial situations. The schema lacks specific data on the reasons behind credit balances or a comprehensive view of students' financial health, making it impossible to answer the question fully.", 'feedback': ''}
2025-08-09 06:53:18,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:19,081 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:20,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:20,801 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:20,895 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:21,103 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:21,122 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. While the schema contains various tables related to institutions, students, and financial transactions, it does not provide specific data or metrics that would allow for a comparative analysis of fee debt across institutions. Additionally, the schema lacks qualitative data or insights into the reasons behind fee debt, such as institutional policies, student demographics, or economic factors. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:53:21,122 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:21,122 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:21,123 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. While the schema contains various tables related to institutions, students, and financial transactions, it does not provide specific data or metrics that would allow for a comparative analysis of fee debt across institutions. Additionally, the schema lacks qualitative data or insights into the reasons behind fee debt, such as institutional policies, student demographics, or economic factors. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:53:21,293 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:21,310 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or historical data that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 06:53:21,310 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:21,310 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:21,310 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to credit balances or their implications. The schema lacks detailed financial rules, policies, or historical data that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 06:53:22,432 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:23,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:23,227 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial situation. While the schema contains various tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors that contribute to credit balances or their implications on students' financial situations. The schema lacks specific data on the reasons behind credit balances or a comprehensive view of students' financial situations, making it impossible to answer the question fully.", 'feedback': ''}
2025-08-09 06:53:23,228 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:23,228 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:23,228 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students having a credit balance at this institution, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance and the impact on their overall financial situation. While the schema contains various tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors that contribute to credit balances or their implications on students' financial situations. The schema lacks specific data on the reasons behind credit balances or a comprehensive view of students' financial situations, making it impossible to answer the question fully.", 'feedback': ''}
2025-08-09 06:53:23,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:23,969 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:25,607 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:25,622 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. While the schema contains various tables related to institutions, students, and financial transactions, it does not provide specific data or metrics that would allow for a comparative analysis of fee debt across institutions. Additionally, the schema lacks qualitative data or insights into the reasons behind fee debt, such as institutional policies, student demographics, or economic factors. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:53:25,622 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:25,622 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:25,622 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. While the schema contains various tables related to institutions, students, and financial transactions, it does not provide specific data or metrics that would allow for a comparative analysis of fee debt across institutions. Additionally, the schema lacks qualitative data or insights into the reasons behind fee debt, such as institutional policies, student demographics, or economic factors. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:53:25,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:25,762 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the distribution of fee debt among different student demographics at the institution where students owe the most fees?', 'sql': 'SELECT s.sex, s.status, COUNT(sb.student_id) AS number_of_students, SUM(b.amount) AS total_debt\nFROM student_bills sb\nJOIN students s ON sb.student_id = s.id\nJOIN bills b ON sb.bill_id = b.id\nWHERE sb.institution_id = (\n    SELECT institution_id\n    FROM student_bills\n    GROUP BY institution_id\n    ORDER BY SUM(amount) DESC\n    LIMIT 1\n)\nGROUP BY s.sex, s.status;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution where students owe the most fees by using a subquery to find the institution_id with the highest total debt. It then joins the student_bills table with the students and bills tables to gather demographic information (sex and status) and calculates the number of students and total debt for that institution. The grouping by sex and status aligns with the request for a distribution of fee debt among different demographics.', 'feedback': "The question could be clarified by specifying which demographics are of interest (e.g., age, nationality) beyond just sex and status. Additionally, ensuring that the 'amount' field in the bills table is correctly referenced in the SQL query is crucial for accurate calculations."}
2025-08-09 06:53:25,762 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:25,762 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:53:26,206 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:26,225 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors that lead to credit balances or the broader implications on students' financial situations. The schema lacks specific data on the reasons behind credit balances, such as income sources, expenses, or financial aid details that would be necessary for a comprehensive analysis.", 'feedback': ''}
2025-08-09 06:53:26,225 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:26,225 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:26,226 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students at this institution having a credit balance instead of a debt, and how does this impact their overall financial situation?', 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to students having a credit balance instead of a debt, as well as the impact on their overall financial situation. While the schema contains tables related to student transactions, balances, and financial aid, it does not provide direct insights into the qualitative factors that lead to credit balances or the broader implications on students' financial situations. The schema lacks specific data on the reasons behind credit balances, such as income sources, expenses, or financial aid details that would be necessary for a comprehensive analysis.", 'feedback': ''}
2025-08-09 06:53:26,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:26,419 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:27,926 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:28,117 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:28,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:28,481 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What percentage of students at the institution where students owe the most fees are currently in debt?', 'sql': 'WITH InstitutionDebt AS (  SELECT institution_id, SUM(total_due) AS total_fees  FROM bills  GROUP BY institution_id  ORDER BY total_fees DESC  LIMIT 1),  TotalStudents AS (  SELECT COUNT(*) AS total_students  FROM students  WHERE institution_id = (SELECT institution_id FROM InstitutionDebt)  ),  StudentsInDebt AS (  SELECT COUNT(*) AS students_in_debt  FROM student_balances  WHERE institution_id = (SELECT institution_id FROM InstitutionDebt) AND balance < 0  )  SELECT (students_in_debt * 100.0 / total_students) AS percentage_in_debt  FROM TotalStudents, StudentsInDebt;', 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the highest total fees owed by summing the total_due from the bills table and grouping by institution_id. It then counts the total number of students in that institution and the number of students who have a negative balance (indicating debt) in the student_balances table. Finally, it calculates the percentage of students in debt by dividing the count of students in debt by the total number of students and multiplying by 100. This aligns perfectly with the question asked.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding comments to clarify each step of the query for better readability and maintainability.'}
2025-08-09 06:53:28,481 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:28,482 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:53:28,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:28,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:29,804 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:29,824 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:30,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:30,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:30,490 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. However, the provided schema does not contain specific data or metrics related to fee debt or the reasons behind it. While there are tables related to billing, students, and institutions, there is no direct correlation or analysis available in the schema that would allow for a comprehensive answer to this question. Factors contributing to fee debt would likely require qualitative data or external analysis that is not present in the schema.', 'feedback': ''}
2025-08-09 06:53:30,490 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:30,490 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:30,490 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. However, the provided schema does not contain specific data or metrics related to fee debt or the reasons behind it. While there are tables related to billing, students, and institutions, there is no direct correlation or analysis available in the schema that would allow for a comprehensive answer to this question. Factors contributing to fee debt would likely require qualitative data or external analysis that is not present in the schema.', 'feedback': ''}
2025-08-09 06:53:31,270 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:31,587 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:32,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:32,258 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:32,996 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:33,588 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:33,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:33,984 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What alternative methods could be employed to gather insights on fee debt distribution among student demographics if the current data is unavailable?', 'answerable': False, 'reasoning': 'The question asks for alternative methods to gather insights on fee debt distribution among student demographics. This is a conceptual question that does not directly relate to the data available in the schema. The schema provides various tables related to students, fees, and demographics, but it does not provide insights or methods for analysis. Therefore, the question cannot be answered using the schema.', 'feedback': ''}
2025-08-09 06:53:33,985 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:33,985 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:33,985 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What alternative methods could be employed to gather insights on fee debt distribution among student demographics if the current data is unavailable?', 'answerable': False, 'reasoning': 'The question asks for alternative methods to gather insights on fee debt distribution among student demographics. This is a conceptual question that does not directly relate to the data available in the schema. The schema provides various tables related to students, fees, and demographics, but it does not provide insights or methods for analysis. Therefore, the question cannot be answered using the schema.', 'feedback': ''}
2025-08-09 06:53:34,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:34,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:35,917 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:36,871 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:36,886 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to such a high percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a high percentage of students being in debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons behind student debt. Factors contributing to debt could include tuition fees, living expenses, financial aid availability, and student spending habits, none of which are explicitly detailed in the schema. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:53:36,886 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:36,886 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:36,887 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to such a high percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a high percentage of students being in debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons behind student debt. Factors contributing to debt could include tuition fees, living expenses, financial aid availability, and student spending habits, none of which are explicitly detailed in the schema. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:53:38,113 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:38,133 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What alternative methods could be employed to gather insights on fee debt distribution among student demographics if the current data is unavailable?', 'answerable': False, 'reasoning': 'The question asks for alternative methods to gather insights on fee debt distribution among student demographics, which is a conceptual inquiry rather than a request for specific data that can be retrieved from the schema. The schema provides various tables related to students, fees, and demographics, but it does not contain information on methodologies or alternative approaches for data collection or analysis. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:53:38,134 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:38,134 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:38,134 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What alternative methods could be employed to gather insights on fee debt distribution among student demographics if the current data is unavailable?', 'answerable': False, 'reasoning': 'The question asks for alternative methods to gather insights on fee debt distribution among student demographics, which is a conceptual inquiry rather than a request for specific data that can be retrieved from the schema. The schema provides various tables related to students, fees, and demographics, but it does not contain information on methodologies or alternative approaches for data collection or analysis. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:53:38,373 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:40,447 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:40,448 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:40,564 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:42,202 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:42,216 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What alternative methods could be employed to gather insights on fee debt distribution among student demographics if the current data is unavailable?', 'answerable': False, 'reasoning': 'The question asks for alternative methods to gather insights on fee debt distribution among student demographics, which is a conceptual inquiry rather than a request for specific data that can be directly retrieved from the database schema. The schema provides various tables related to students, fees, and demographics, but it does not contain information on alternative methods for data collection or analysis. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:53:42,216 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:42,216 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:42,216 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What alternative methods could be employed to gather insights on fee debt distribution among student demographics if the current data is unavailable?', 'answerable': False, 'reasoning': 'The question asks for alternative methods to gather insights on fee debt distribution among student demographics, which is a conceptual inquiry rather than a request for specific data that can be directly retrieved from the database schema. The schema provides various tables related to students, fees, and demographics, but it does not contain information on alternative methods for data collection or analysis. Therefore, the question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 06:53:42,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:42,588 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to such a high percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a high percentage of students being in debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons behind student debt. Factors contributing to debt could include tuition fees, living expenses, financial aid availability, and student spending habits, none of which are explicitly detailed in the schema. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:53:42,588 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:42,588 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:42,588 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to such a high percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a high percentage of students being in debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons behind student debt. Factors contributing to debt could include tuition fees, living expenses, financial aid availability, and student spending habits, none of which are explicitly detailed in the schema. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 06:53:44,653 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:44,654 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:45,327 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:45,671 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:46,641 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:46,655 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What alternative methods could be employed to gather insights on fee debt distribution among student demographics if the current data is unavailable?', 'answerable': False, 'reasoning': 'The question asks for alternative methods to gather insights on fee debt distribution among student demographics, which is a conceptual inquiry rather than a request for specific data that can be retrieved from the schema. The schema provides various tables related to students, fees, and demographics, but it does not contain information on alternative methods for data collection or analysis. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-09 06:53:46,655 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:46,655 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:46,656 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What alternative methods could be employed to gather insights on fee debt distribution among student demographics if the current data is unavailable?', 'answerable': False, 'reasoning': 'The question asks for alternative methods to gather insights on fee debt distribution among student demographics, which is a conceptual inquiry rather than a request for specific data that can be retrieved from the schema. The schema provides various tables related to students, fees, and demographics, but it does not contain information on alternative methods for data collection or analysis. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-09 06:53:47,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:47,560 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:47,574 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to such a high percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a high percentage of students being in debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons or factors behind student debt. The schema lacks qualitative data or metrics that would allow for a comprehensive analysis of the causes of student debt, such as economic conditions, student financial behavior, or institutional policies. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-09 06:53:47,574 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:47,574 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:47,574 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to such a high percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a high percentage of students being in debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons or factors behind student debt. The schema lacks qualitative data or metrics that would allow for a comprehensive analysis of the causes of student debt, such as economic conditions, student financial behavior, or institutional policies. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-09 06:53:48,085 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:50,097 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:50,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:50,702 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:52,276 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:52,338 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:52,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:52,552 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to such a high percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a high percentage of students being in debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons or factors behind student debt. The schema lacks qualitative data or specific metrics that would allow for a comprehensive analysis of the causes of student debt, such as economic conditions, student financial behavior, or institutional policies. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-09 06:53:52,552 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:53:52,552 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:53:52,552 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to such a high percentage of students being in debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to a high percentage of students being in debt at the institution. While the schema contains various tables related to students, their financial transactions, and billing information, it does not provide direct insights or analytical data regarding the reasons or factors behind student debt. The schema lacks qualitative data or specific metrics that would allow for a comprehensive analysis of the causes of student debt, such as economic conditions, student financial behavior, or institutional policies. Therefore, the question cannot be answered based on the provided schema.', 'feedback': ''}
2025-08-09 06:53:56,936 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:53:57,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:01,351 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:03,686 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:04,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:06,024 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:07,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:07,775 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:10,526 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:11,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:14,028 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:15,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:19,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:19,060 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What payment plans or financial aid options are available to students at the institution where students owe the most fees?', 'sql': 'WITH InstitutionFees AS (  SELECT institution_id, SUM(bill_id) AS total_fees  FROM student_bills  GROUP BY institution_id), MaxInstitution AS (  SELECT institution_id  FROM InstitutionFees  ORDER BY total_fees DESC  LIMIT 1) SELECT DISTINCT s.source, s.type, s.value, f.description, f.status, f.type AS financial_aid_type  FROM student_scholarships s  JOIN financial_aid_requests f ON s.student_id = f.student_id  WHERE s.institution_id = (SELECT institution_id FROM MaxInstitution) OR f.institution_id = (SELECT institution_id FROM MaxInstitution);', 'correct': False, 'reasoning': "The SQL query attempts to find the institution with the highest total fees and then retrieves financial aid options and payment plans for students at that institution. However, the query incorrectly uses 'SUM(bill_id)' to calculate total fees, which should instead sum a monetary value (like a fee amount) rather than the bill ID. Additionally, the query combines results from both 'student_scholarships' and 'financial_aid_requests' but does not specifically address 'payment plans' as mentioned in the question. The join condition may also not accurately reflect the relationship between scholarships and financial aid requests.", 'feedback': "Clarify the question to specify what constitutes 'payment plans' and ensure the SQL query sums the correct monetary values from the 'student_bills' table. Consider separating the retrieval of scholarships and financial aid requests to ensure clarity in the results. Additionally, ensure that the join conditions accurately reflect the relationships between the tables involved.", 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-09 06:54:19,061 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:54:19,061 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-09 06:54:19,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:19,482 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific programs or courses at the institution where students owe the most fees that contribute to higher debts?', 'sql': 'SELECT p.long_name AS program_name, SUM(b.total_due) AS total_fees_due\nFROM programs p\nJOIN student_programs sp ON p.id = sp.program_id\nJOIN student_bills sb ON sp.id = sb.student_program_id\nJOIN bills b ON sb.bill_id = b.id\nGROUP BY p.long_name\nORDER BY total_fees_due DESC;', 'correct': False, 'reasoning': 'The SQL query correctly aggregates the total fees due for each program by summing the total_due from the bills associated with each program. However, the question also inquires about specific courses, but the SQL query only retrieves data related to programs. It does not include any information about courses or their associated fees, which is a critical part of the question.', 'feedback': 'To improve the SQL query, it should also include a join with the courses table to retrieve information about specific courses and their associated fees. Additionally, the query could be modified to group by both program and course to fully address the question.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-09 06:54:19,482 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:54:19,482 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-09 06:54:21,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:21,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:23,721 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:23,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:24,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:25,184 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:29,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:30,817 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:35,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:38,050 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:38,360 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:41,243 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:41,553 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:42,861 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:45,457 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:47,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:49,199 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:52,861 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:54,187 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:54:58,687 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:00,131 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:01,288 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:02,754 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:03,882 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:06,981 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:08,471 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:08,837 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:13,608 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:14,746 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:17,501 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:20,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:20,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:22,004 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:23,274 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:25,938 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:27,483 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:27,968 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:32,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:34,419 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:35,638 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:39,613 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:41,398 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:44,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:47,677 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:48,358 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:51,975 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:53,643 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:53,855 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:53,864 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What payment plans or financial aid options are available to students at the institution where students owe the most fees?', 'sql': 'SELECT DISTINCT f.description AS financial_aid_option, f.type AS aid_type, s.source AS scholarship_source, s.type AS scholarship_type\nFROM student_bills b\nJOIN financial_aid_requests f ON b.bill_id = f.billing_period_id\nJOIN student_scholarships s ON b.student_id = s.student_id\nWHERE b.institution_id = (SELECT institution_id FROM student_bills GROUP BY institution_id ORDER BY SUM(bill_id) DESC LIMIT 1)', 'correct': False, 'reasoning': 'The SQL query attempts to find financial aid options and scholarships for students at the institution with the highest total fees owed. However, it incorrectly joins the `financial_aid_requests` table using `bill_id` and `billing_period_id`, which may not be the correct relationship. Additionally, the query does not consider the actual payment plans available, as it focuses solely on financial aid and scholarships. The question asks for payment plans or financial aid options, but the SQL only retrieves financial aid and scholarship information.', 'feedback': 'To improve the SQL query, ensure that the joins accurately reflect the relationships between the tables. Additionally, include a separate query or join to retrieve payment plans if they exist in the schema. Clarifying the question to specify whether payment plans are included in the financial aid options would also help.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-09 06:55:53,864 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:55:53,864 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-09 06:55:57,122 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:59,623 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:55:59,732 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:01,250 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:04,993 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:05,012 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific programs or courses at the institution where students owe the most fees that contribute to higher debts?', 'sql': 'SELECT p.long_name AS program_name, c.title AS course_title, SUM(b.total_due) AS total_debt\nFROM student_bills sb\nJOIN bills b ON sb.bill_id = b.id\nJOIN student_programs sp ON sb.student_id = sp.student_id\nJOIN programs p ON sp.program_id = p.id\nJOIN courses c ON sp.semester_id = c.id\nGROUP BY p.long_name, c.title\nORDER BY total_debt DESC;', 'correct': False, 'reasoning': "The SQL query attempts to aggregate the total debts by program and course, but it incorrectly joins the 'courses' table using 'semester_id' instead of a proper relationship that links courses to programs or students. This could lead to inaccurate results as it does not reflect the actual courses associated with the programs. Additionally, the question implies a need to identify specific programs or courses contributing to higher debts, but the query does not filter or specify any criteria to focus on those with the highest debts specifically.", 'feedback': "To improve the SQL query, ensure that the join between 'student_programs' and 'courses' is based on a valid relationship, such as a course being part of a program. Additionally, consider adding a WHERE clause to filter for programs or courses that exceed a certain debt threshold, which would directly address the question's focus on higher debts.", 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-09 06:56:05,012 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:56:05,012 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-09 06:56:06,784 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:07,551 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:10,518 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:11,594 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:11,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:14,259 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:17,178 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:17,562 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:19,073 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:24,997 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:28,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:30,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:30,098 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What payment plans or financial aid options are available to students at the institution where students owe the most fees?', 'sql': 'WITH MaxBalanceInstitution AS (  SELECT institution_id  FROM student_balances  GROUP BY institution_id  ORDER BY SUM(balance) DESC  LIMIT 1) SELECT DISTINCT f.type AS financial_aid_type, f.description AS financial_aid_description, s.source AS scholarship_source, s.type AS scholarship_type FROM financial_aid_requests f JOIN MaxBalanceInstitution m ON f.institution_id = m.institution_id JOIN student_scholarships s ON s.institution_id = m.institution_id;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total student balances by using a Common Table Expression (CTE) to find the institution_id with the maximum sum of balances. It then retrieves distinct financial aid options and scholarship details available at that institution by joining the financial_aid_requests and student_scholarships tables with the identified institution. This aligns with the question's requirement to find payment plans or financial aid options at the institution where students owe the most fees.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly mentioning 'payment plans' in the query if such data exists in the schema, as the current query focuses on financial aid and scholarships only. If there are specific tables or fields related to payment plans, they should be included in the query to provide a more comprehensive answer."}
2025-08-09 06:56:30,098 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:56:30,098 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:56:31,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:32,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:33,409 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:34,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:34,503 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:35,801 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:36,764 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:39,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:39,135 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons why this institution may lack financial support programs for students?', 'answerable': False, 'reasoning': 'The question asks for potential reasons why an institution may lack financial support programs for students. However, the provided schema does not contain any direct information or attributes that would indicate the reasons for the absence of financial support programs. The schema includes tables related to financial aid requests, student transactions, and other financial aspects, but it does not provide insights into institutional policies, decision-making processes, or external factors that could contribute to the lack of such programs. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:56:39,135 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:56:39,135 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:56:39,135 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons why this institution may lack financial support programs for students?', 'answerable': False, 'reasoning': 'The question asks for potential reasons why an institution may lack financial support programs for students. However, the provided schema does not contain any direct information or attributes that would indicate the reasons for the absence of financial support programs. The schema includes tables related to financial aid requests, student transactions, and other financial aspects, but it does not provide insights into institutional policies, decision-making processes, or external factors that could contribute to the lack of such programs. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:56:41,585 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:41,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:44,072 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:44,087 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What are some potential reasons why this institution may lack financial support programs for students?', 'answerable': False, 'reasoning': 'The question asks for potential reasons why an institution may lack financial support programs for students. However, the provided schema does not contain any direct information regarding the reasons or factors influencing the existence or absence of financial support programs. The schema includes tables related to financial aid requests, student transactions, and other financial aspects, but it does not provide insights into the institutional policies, decision-making processes, or external factors that could explain the lack of such programs. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:56:44,087 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:56:44,087 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:56:44,087 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What are some potential reasons why this institution may lack financial support programs for students?', 'answerable': False, 'reasoning': 'The question asks for potential reasons why an institution may lack financial support programs for students. However, the provided schema does not contain any direct information regarding the reasons or factors influencing the existence or absence of financial support programs. The schema includes tables related to financial aid requests, student transactions, and other financial aspects, but it does not provide insights into the institutional policies, decision-making processes, or external factors that could explain the lack of such programs. Therefore, the question cannot be answered based on the available schema.', 'feedback': ''}
2025-08-09 06:56:48,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:52,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:55,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:56:56,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:01,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:06,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:09,534 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:12,991 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:14,572 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:18,772 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:23,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:23,708 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific programs or courses at the institution where students owe the most fees that contribute to higher debts?', 'sql': "SELECT p.long_name AS program_name, c.title AS course_title, SUM(st.transaction_amount) AS total_debt\nFROM student_transactions st\nJOIN student_bills sb ON st.student_id = sb.student_id\nJOIN programs p ON sb.student_program_id = p.id\nJOIN courses c ON c.unit_id = st.level\nWHERE st.type = 'debit'\nGROUP BY p.long_name, c.title\nORDER BY total_debt DESC;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the programs and courses associated with student debts by summing the transaction amounts for debit transactions. It joins the necessary tables: student_transactions to get the transaction amounts, student_bills to link students to their programs, programs to get the program names, and courses to get the course titles. The grouping by program name and course title allows for the aggregation of total debts, which is then ordered to highlight those with the highest debts, directly addressing the question.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding a filter for a specific institution if the schema supports it, to ensure the results are relevant to the institution in question. Additionally, clarifying whether the question seeks to identify only the top programs/courses or all programs/courses with debts could refine the query further.'}
2025-08-09 06:57:23,708 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:57:23,708 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 06:57:27,211 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:28,262 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:29,542 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:31,663 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:33,988 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:34,007 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might influence the overall debt levels of students, even in the absence of specific programs or courses contributing to higher fees?', 'answerable': False, 'reasoning': 'The question asks for factors influencing overall debt levels of students, which is a qualitative analysis rather than a quantitative one. The schema primarily contains structured data about students, their programs, financial transactions, and institutional details, but it does not provide insights into qualitative factors or external influences that might affect student debt levels. To answer this question, one would need data on economic conditions, student demographics, financial literacy, and other external factors, none of which are present in the schema.', 'feedback': ''}
2025-08-09 06:57:34,008 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:57:34,008 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:57:34,008 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might influence the overall debt levels of students, even in the absence of specific programs or courses contributing to higher fees?', 'answerable': False, 'reasoning': 'The question asks for factors influencing overall debt levels of students, which is a qualitative analysis rather than a quantitative one. The schema primarily contains structured data about students, their programs, financial transactions, and institutional details, but it does not provide insights into qualitative factors or external influences that might affect student debt levels. To answer this question, one would need data on economic conditions, student demographics, financial literacy, and other external factors, none of which are present in the schema.', 'feedback': ''}
2025-08-09 06:57:36,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:38,498 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:38,515 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think might influence the overall debt levels of students, even in the absence of specific programs or courses contributing to higher fees?', 'answerable': False, 'reasoning': 'The question asks for factors influencing overall debt levels of students, which is a qualitative analysis rather than a quantitative one. The schema primarily contains structured data about students, their programs, financial transactions, and institutional details, but it does not provide insights into qualitative factors or external influences that might affect student debt levels. To answer this question, one would need data on economic conditions, student demographics, financial literacy, and other sociocultural factors, none of which are present in the schema.', 'feedback': ''}
2025-08-09 06:57:38,516 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 06:57:38,516 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 06:57:38,516 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think might influence the overall debt levels of students, even in the absence of specific programs or courses contributing to higher fees?', 'answerable': False, 'reasoning': 'The question asks for factors influencing overall debt levels of students, which is a qualitative analysis rather than a quantitative one. The schema primarily contains structured data about students, their programs, financial transactions, and institutional details, but it does not provide insights into qualitative factors or external influences that might affect student debt levels. To answer this question, one would need data on economic conditions, student demographics, financial literacy, and other sociocultural factors, none of which are present in the schema.', 'feedback': ''}
2025-08-09 06:57:38,517 - root - INFO - [{'institution_id': 1, 'total_fees_owed': -2600.0}]
2025-08-09 06:57:38,517 - root - INFO - [{'name': 'Central University', 'total_debt': -2600.0}, {'name': 'Trinity Theological Seminary', 'total_debt': -27040.0}, {'name': 'ITC University', 'total_debt': -75614369.64}, {'name': 'University Of Education, Winneba', 'total_debt': -34045567.83}, {'name': 'Trinity Theological Seminary', 'total_debt': -27040.0}, {'name': 'ITC University', 'total_debt': -75614369.64}, {'name': 'University Of Education, Winneba', 'total_debt': -34045567.83}]
2025-08-09 06:57:38,517 - root - INFO - [{'average_fee_debt': -1300.0}]
2025-08-09 06:57:38,517 - root - INFO - [{'percentage_in_debt': 24.38}]
2025-08-09 06:57:38,517 - root - INFO - 'No results'
2025-08-09 06:57:38,517 - root - INFO - 'No results'
2025-08-09 06:57:38,518 - root - INFO - 'No results'
2025-08-09 06:57:38,518 - root - INFO - 'No results'
2025-08-09 06:57:38,518 - root - INFO - 'No results'
2025-08-09 06:57:38,518 - root - INFO - 'No results'
2025-08-09 06:57:38,518 - root - INFO - 'No results'
2025-08-09 06:57:38,518 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-09 06:57:48,623 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:57:48,628 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-09 06:58:04,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:04,038 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:04,039 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:58:04,039 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:04,039 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:04,039 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:58:04,039 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:58:04,039 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:58:04,039 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:58:04,039 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total amount of fees owed by students at the institution where students owe the most fee...
2025-08-09 06:58:04,039 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total amount of fees owed by students at the institution where students owe the most fees is -$2...
2025-08-09 06:58:04,039 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:58:04,039 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_id': 1, 'total_fees_owed': -2600.0}]...
2025-08-09 06:58:04,039 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_fees_owed_by_top_institution
2025-08-09 06:58:04,039 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 06:58:04,040 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 06:58:04,040 - celery.redirected - WARNING - [{'institution_id': 1, 'total_fees_owed': -2600.0}]
2025-08-09 06:58:04,040 - celery.redirected - WARNING - ================================= 
2025-08-09 06:58:04,040 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 06:58:04,040 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:04,040 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:58:04,040 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:04,040 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:04,040 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:58:04,040 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:58:04,040 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:58:04,040 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:58:04,040 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the fee debt at the institution where students owe the most fees compare to other instituti...
2025-08-09 06:58:04,040 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the highest fee debt is ITC University, with a total debt of approximately -75,...
2025-08-09 06:58:04,040 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: bar_chart
2025-08-09 06:58:04,040 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'name': 'Central University', 'total_debt': -2600.0}, {'name': 'Trinity Theological Seminary', 'to...
2025-08-09 06:58:04,040 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_fee_debt_by_institution
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 06:58:04,041 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 06:58:04,041 - celery.redirected - WARNING - [{'name': 'Central University', 'total_debt': -2600.0}, {'name': 'Trinity Theological Seminary', 'total_debt': -27040.0}, {'name': 'ITC University', 'total_debt': -75614369.64}, {'name': 'University Of Education, Winneba', 'total_debt': -34045567.83}, {'name': 'Trinity Theological Seminary', 'total_debt': -27040.0}, {'name': 'ITC University', 'total_debt': -75614369.64}, {'name': 'University Of Education, Winneba', 'total_debt': -34045567.83}]
2025-08-09 06:58:04,041 - celery.redirected - WARNING - ================================= 
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: total_fee_debt_by_institution
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 06:58:04,041 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:04,041 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:58:04,041 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:58:04,041 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the average fee debt per student at the institution where students owe the most fees?...
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The average fee debt per student at the institution where students owe the most fees is -1300.0. Thi...
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'average_fee_debt': -1300.0}]...
2025-08-09 06:58:04,041 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_fee_debt_at_top_institution
2025-08-09 06:58:04,042 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 06:58:04,042 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 06:58:04,042 - celery.redirected - WARNING - [{'average_fee_debt': -1300.0}]
2025-08-09 06:58:04,042 - celery.redirected - WARNING - ================================= 
2025-08-09 06:58:04,042 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 06:58:04,042 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:04,042 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:58:04,042 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:04,042 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:04,042 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:58:04,042 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 3
2025-08-09 06:58:04,042 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:58:04,042 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:58:04,042 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific programs or courses at the institution where students owe the most fees that cont...
2025-08-09 06:58:04,042 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-09 06:58:04,042 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 2:
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific programs or courses at the institution where students owe the most fees that cont...
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 3:
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific programs or courses at the institution where students owe the most fees that cont...
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there are currently no specific programs or cours...
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: programs_with_high_student_debts
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 06:58:04,043 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 3 documents, 0 with data_returned=True
2025-08-09 06:58:04,043 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:04,043 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:58:04,044 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:04,044 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:04,044 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:58:04,044 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:58:04,044 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:58:04,044 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:58:04,044 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What percentage of students at the institution where students owe the most fees are currently in deb...
2025-08-09 06:58:04,044 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At the institution where students owe the most fees, approximately 24.38% of students are currently ...
2025-08-09 06:58:04,044 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:58:04,044 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'percentage_in_debt': 24.38}]...
2025-08-09 06:58:04,044 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: percentage_of_students_in_debt_at_high_fee_institution
2025-08-09 06:58:04,044 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 06:58:04,044 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 06:58:04,044 - celery.redirected - WARNING - [{'percentage_in_debt': 24.38}]
2025-08-09 06:58:04,044 - celery.redirected - WARNING - ================================= 
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 06:58:04,045 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:04,045 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:58:04,045 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 3
2025-08-09 06:58:04,045 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What payment plans or financial aid options are available to students at the institution where stude...
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-09 06:58:04,045 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 2:
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What payment plans or financial aid options are available to students at the institution where stude...
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 3:
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What payment plans or financial aid options are available to students at the institution where stude...
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are currently no available payment plans or financial aid options listed for s...
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: financial_aid_options_for_highest_fee_institution
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 06:58:04,046 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 3 documents, 0 with data_returned=True
2025-08-09 06:58:04,046 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:04,046 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 06:58:04,047 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 06:58:04,047 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the distribution of fee debt among different student demographics at the institution where s...
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query did not return any results, indicating that there may be no data available regarding t...
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: fee_debt_distribution_by_demographics
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 06:58:04,047 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 06:58:04,047 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 11
2025-08-09 06:58:04,047 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:04,047 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-09 06:58:04,047 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:04,047 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 11 documents
2025-08-09 06:58:04,047 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-09 06:58:04,047 - UPSERT_DOCS - INFO -     Content: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 06:58:04,047 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:23.228464+00:00', 'data_returned': True}
2025-08-09 06:58:04,047 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Content: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:30.490951+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Content: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Content: Question: Are there specific programs or courses at the institution where students owe the most fees...
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:57:38.516716+00:00', 'data_returned': False}
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Content: Question: Are there specific programs or courses at the institution where students owe the most fees...
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-09T06:57:38.516716+00:00', 'data_returned': False}
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Content: Question: Are there specific programs or courses at the institution where students owe the most fees...
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-09T06:57:38.516716+00:00', 'data_returned': False}
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Content: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:52.552505+00:00', 'data_returned': True}
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -   📄 Doc 8:
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Content: Question: What payment plans or financial aid options are available to students at the institution w...
2025-08-09 06:58:04,048 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:56:44.087988+00:00', 'data_returned': False}
2025-08-09 06:58:04,049 - UPSERT_DOCS - INFO -   📄 Doc 9:
2025-08-09 06:58:04,049 - UPSERT_DOCS - INFO -     Content: Question: What payment plans or financial aid options are available to students at the institution w...
2025-08-09 06:58:04,049 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-09T06:56:44.087988+00:00', 'data_returned': False}
2025-08-09 06:58:04,049 - UPSERT_DOCS - INFO -   📄 Doc 10:
2025-08-09 06:58:04,049 - UPSERT_DOCS - INFO -     Content: Question: What payment plans or financial aid options are available to students at the institution w...
2025-08-09 06:58:04,049 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 2, 'partition': 'interview', 'timestamp': '2025-08-09T06:56:44.087988+00:00', 'data_returned': False}
2025-08-09 06:58:04,049 - UPSERT_DOCS - INFO -   📄 Doc 11:
2025-08-09 06:58:04,049 - UPSERT_DOCS - INFO -     Content: Question: What is the distribution of fee debt among different student demographics at the instituti...
2025-08-09 06:58:04,049 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:46.656094+00:00', 'data_returned': False}
2025-08-09 06:58:04,049 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 4/11
2025-08-09 06:58:04,196 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.147s]
2025-08-09 06:58:05,413 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 06:58:12,581 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:6.547s]
2025-08-09 06:58:12,583 - UPSERT_DOCS - INFO - ✅ Successfully upserted 11 documents to Elasticsearch
2025-08-09 06:58:12,583 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 6
2025-08-09 06:58:12,583 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-09 06:58:12,584 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:12,584 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-09 06:58:12,584 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:12,584 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/6...
2025-08-09 06:58:13,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:13,560 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:13,560 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 06:58:13,560 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:13,561 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student fee debt analysis'
2025-08-09 06:58:13,561 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:13,561 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:13,747 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.186s]
2025-08-09 06:58:13,747 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 63
2025-08-09 06:58:13,948 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.200s]
2025-08-09 06:58:13,948 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 24
2025-08-09 06:58:14,107 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.158s]
2025-08-09 06:58:14,107 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 29
2025-08-09 06:58:14,369 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.261s]
2025-08-09 06:58:14,369 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 13
2025-08-09 06:58:15,142 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 06:58:15,468 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.325s]
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:33.371211+00:00', 'data_returned': True}
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:54.089192+00:00', 'data_returned': True}
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 06:58:15,469 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:23.228464+00:00', 'data_returned': True}
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:33.371211+00:00', 'data_returned': True}
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:54.089192+00:00', 'data_returned': True}
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:23.228464+00:00', 'data_returned': True}
2025-08-09 06:58:15,470 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1855 chars):
2025-08-09 06:58:15,471 - app.chains.section_writer - INFO -    Question: What is the average fee debt per student at the institution where students owe the most fees?
Answer: The average fee debt per student at the institution where students owe the most fees is -$1300. This negative value indicates that, on average, students at this institution have a credit balance rather than a debt, suggesting they may have overpaid their fees or received financial aid that exceeds their charges.

Question: What is the average fee debt per student at the institution whe...
2025-08-09 06:58:18,021 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:18,031 - app.chains.section_writer - INFO - 🤖 AI generated section (835 chars):
2025-08-09 06:58:18,031 - app.chains.section_writer - INFO -    This report analyzes student fee debt across various institutions, focusing on identifying which institution has the highest fees owed by students. Understanding the dynamics of fee debt is crucial for assessing the financial health of students and the effectiveness of institutional support systems....
2025-08-09 06:58:18,031 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-09 06:58:18,032 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 835 characters
2025-08-09 06:58:18,032 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/6...
2025-08-09 06:58:18,696 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:18,703 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:18,703 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 06:58:18,703 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:18,703 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student fee debt analysis'
2025-08-09 06:58:18,703 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:18,703 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:18,846 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.143s]
2025-08-09 06:58:18,847 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 63
2025-08-09 06:58:18,972 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.125s]
2025-08-09 06:58:18,973 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 24
2025-08-09 06:58:19,100 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-09 06:58:19,100 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 29
2025-08-09 06:58:19,229 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-09 06:58:19,229 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 13
2025-08-09 06:58:20,138 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 06:58:20,393 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.253s]
2025-08-09 06:58:20,394 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-09 06:58:20,394 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:20,394 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:58:20,394 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:20,394 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:33.371211+00:00', 'data_returned': True}
2025-08-09 06:58:20,394 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:20,394 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:20,394 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 06:58:20,394 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:54.089192+00:00', 'data_returned': True}
2025-08-09 06:58:20,394 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 06:58:20,395 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:23.228464+00:00', 'data_returned': True}
2025-08-09 06:58:20,395 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:20,395 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:58:20,395 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:20,396 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:33.371211+00:00', 'data_returned': True}
2025-08-09 06:58:20,396 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:20,396 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:20,396 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 06:58:20,396 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:54.089192+00:00', 'data_returned': True}
2025-08-09 06:58:20,396 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 06:58:20,396 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:23.228464+00:00', 'data_returned': True}
2025-08-09 06:58:20,396 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1855 chars):
2025-08-09 06:58:20,396 - app.chains.section_writer - INFO -    Question: What is the average fee debt per student at the institution where students owe the most fees?
Answer: The average fee debt per student at the institution where students owe the most fees is -$1300. This negative value indicates that, on average, students at this institution have a credit balance rather than a debt, suggesting they may have overpaid their fees or received financial aid that exceeds their charges.

Question: What is the average fee debt per student at the institution whe...
2025-08-09 06:58:22,629 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:22,636 - app.chains.section_writer - INFO - 🤖 AI generated section (758 chars):
2025-08-09 06:58:22,636 - app.chains.section_writer - INFO -    ## Methodology  
Data collection was conducted through interviews with students and institutional representatives. The responses were analyzed to extract key themes and insights regarding fee debt levels and contributing factors. Notably, the average fee debt per student at the institution where stu...
2025-08-09 06:58:22,636 - app.chains.section_writer - WARNING - ⚠️ No data tag placeholders found in AI output
2025-08-09 06:58:22,636 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 758 characters
2025-08-09 06:58:22,637 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/6...
2025-08-09 06:58:23,485 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:23,491 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:23,491 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 06:58:23,491 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:23,491 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Institutions overview ITC University University of Education Winneba Central University Trinity Theological Seminary'
2025-08-09 06:58:23,491 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:23,491 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:23,636 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.145s]
2025-08-09 06:58:23,637 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 63
2025-08-09 06:58:23,769 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-09 06:58:23,770 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 24
2025-08-09 06:58:23,899 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 06:58:23,899 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 29
2025-08-09 06:58:24,031 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-09 06:58:24,031 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 13
2025-08-09 06:58:25,177 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 06:58:25,382 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.203s]
2025-08-09 06:58:25,382 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-09 06:58:25,383 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:25,383 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:30.490951+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 06:58:25,383 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:25,383 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:40.230224+00:00', 'data_returned': True}
2025-08-09 06:58:25,383 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: Are there specific programs or courses at the institution where students owe the most fees...
2025-08-09 06:58:25,383 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:45.661352+00:00', 'data_returned': True, 'data_tag': 'total_debt_by_program'}
2025-08-09 06:58:25,383 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 06:58:25,383 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:48:11.814901+00:00', 'data_returned': True}
2025-08-09 06:58:25,383 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 06:58:25,383 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:52.552505+00:00', 'data_returned': True}
2025-08-09 06:58:25,384 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:30.490951+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_fee_debt_by_institution
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:40.230224+00:00', 'data_returned': True}
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   📄 Doc 3: Question: Are there specific programs or courses at the institution where students owe the most fees...
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:45.661352+00:00', 'data_returned': True, 'data_tag': 'total_debt_by_program'}
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_debt_by_program
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:48:11.814901+00:00', 'data_returned': True}
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What percentage of students at the institution where students owe the most fees are curren...
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:52.552505+00:00', 'data_returned': True}
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (2388 chars):
2025-08-09 06:58:25,385 - app.chains.section_writer - INFO -    Question: How does the fee debt at the institution where students owe the most fees compare to other institutions?
Answer: The institution with the highest fee debt is ITC University, with a total debt of approximately -75,614,369.64. This is significantly higher than the next highest, which is the University Of Education, Winneba, with a total debt of about -34,045,567.83. In comparison, Central University has a total debt of -2,600.0, and Trinity Theological Seminary has a total debt of -27,04...
2025-08-09 06:58:32,951 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:32,957 - app.chains.section_writer - INFO - 🤖 AI generated section (1230 chars):
2025-08-09 06:58:32,957 - app.chains.section_writer - INFO -    ## Overview of Institutions  

The institutions analyzed in this report include ITC University, University of Education, Winneba, Central University, and Trinity Theological Seminary. 

- **ITC University**: This is a private institution with a total fee debt of approximately -75,614,369.64, indicat...
2025-08-09 06:58:32,957 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_fee_debt_by_institution']
2025-08-09 06:58:32,958 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 1752 characters
2025-08-09 06:58:32,958 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/6...
2025-08-09 06:58:33,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:33,662 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:33,662 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 06:58:33,662 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:33,662 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University fee debt statistics'
2025-08-09 06:58:33,662 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:33,662 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:33,814 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.152s]
2025-08-09 06:58:33,815 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 63
2025-08-09 06:58:33,948 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.133s]
2025-08-09 06:58:33,949 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 24
2025-08-09 06:58:34,078 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 06:58:34,079 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 29
2025-08-09 06:58:34,205 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.126s]
2025-08-09 06:58:34,205 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 13
2025-08-09 06:58:34,640 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 06:58:34,878 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.233s]
2025-08-09 06:58:34,878 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-09 06:58:34,879 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:34,879 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:30.490951+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 06:58:34,879 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:34,879 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:40.230224+00:00', 'data_returned': True}
2025-08-09 06:58:34,879 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:34,879 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:33.371211+00:00', 'data_returned': True}
2025-08-09 06:58:34,879 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:34,879 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:34,879 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:34,879 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:58:34,880 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:34,880 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:30.490951+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 06:58:34,880 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_fee_debt_by_institution
2025-08-09 06:58:34,880 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:34,880 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:40.230224+00:00', 'data_returned': True}
2025-08-09 06:58:34,881 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:34,881 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:33.371211+00:00', 'data_returned': True}
2025-08-09 06:58:34,881 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:34,881 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:34,881 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:34,881 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:58:34,881 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (2549 chars):
2025-08-09 06:58:34,881 - app.chains.section_writer - INFO -    Question: How does the fee debt at the institution where students owe the most fees compare to other institutions?
Answer: The institution with the highest fee debt is ITC University, with a total debt of approximately -75,614,369.64. This is significantly higher than the next highest, which is the University Of Education, Winneba, with a total debt of about -34,045,567.83. In comparison, Central University has a total debt of -2,600.0, and Trinity Theological Seminary has a total debt of -27,04...
2025-08-09 06:58:43,200 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:43,205 - app.chains.section_writer - INFO - 🤖 AI generated section (1126 chars):
2025-08-09 06:58:43,205 - app.chains.section_writer - INFO -    ## Findings  

### 1. Institution with the Highest Fee Debt  
ITC University has the highest total fee debt, amounting to approximately -75,614,369.64. This figure is significantly greater than that of other institutions, with the University of Education, Winneba reporting a total debt of -34,045,56...
2025-08-09 06:58:43,205 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_fee_debt_by_institution']
2025-08-09 06:58:43,206 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 1107 characters
2025-08-09 06:58:43,206 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/6...
2025-08-09 06:58:44,316 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:44,327 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:44,328 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 06:58:44,328 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:44,328 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University fee debt comparison'
2025-08-09 06:58:44,328 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:44,328 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:44,465 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.137s]
2025-08-09 06:58:44,466 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 63
2025-08-09 06:58:44,593 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-09 06:58:44,594 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 24
2025-08-09 06:58:44,723 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 06:58:44,724 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 29
2025-08-09 06:58:44,856 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-09 06:58:44,857 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 13
2025-08-09 06:58:45,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 06:58:45,846 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.157s]
2025-08-09 06:58:45,847 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-09 06:58:45,847 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:45,847 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:30.490951+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 06:58:45,847 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:45,847 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:40.230224+00:00', 'data_returned': True}
2025-08-09 06:58:45,847 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:45,847 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:45,847 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:45,848 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:33.371211+00:00', 'data_returned': True}
2025-08-09 06:58:45,848 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:45,848 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:30.490951+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_fee_debt_by_institution
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:40.230224+00:00', 'data_returned': True}
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:33.371211+00:00', 'data_returned': True}
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (2549 chars):
2025-08-09 06:58:45,849 - app.chains.section_writer - INFO -    Question: How does the fee debt at the institution where students owe the most fees compare to other institutions?
Answer: The institution with the highest fee debt is ITC University, with a total debt of approximately -75,614,369.64. This is significantly higher than the next highest, which is the University Of Education, Winneba, with a total debt of about -34,045,567.83. In comparison, Central University has a total debt of -2,600.0, and Trinity Theological Seminary has a total debt of -27,04...
2025-08-09 06:58:52,057 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:52,064 - app.chains.section_writer - INFO - 🤖 AI generated section (1145 chars):
2025-08-09 06:58:52,065 - app.chains.section_writer - INFO -    ## Comparison with Other Institutions  

The fee debt landscape across various institutions reveals significant contrasts in both total debt levels and the financial situations of students. ITC University stands out prominently, with a total fee debt of approximately -75,614,369.64, which is substan...
2025-08-09 06:58:52,065 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_fee_debt_by_institution']
2025-08-09 06:58:52,065 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 1126 characters
2025-08-09 06:58:52,066 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 6/6...
2025-08-09 06:58:52,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:52,875 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:52,875 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 06:58:52,875 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:52,875 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student fee debt ITC University comparison recommendations'
2025-08-09 06:58:52,875 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:52,875 - VECTOR_SEARCH - INFO - ❓ Original Question: 'From which instituion do students owe the most fees?'
2025-08-09 06:58:53,011 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-09 06:58:53,014 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 63
2025-08-09 06:58:53,142 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-09 06:58:53,142 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 24
2025-08-09 06:58:53,270 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.128s]
2025-08-09 06:58:53,271 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 29
2025-08-09 06:58:53,400 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 06:58:53,401 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 13
2025-08-09 06:58:54,076 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 06:58:54,272 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.192s]
2025-08-09 06:58:54,273 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 5 documents
2025-08-09 06:58:54,273 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:54,273 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:30.490951+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 06:58:54,273 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:54,273 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:40.230224+00:00', 'data_returned': True}
2025-08-09 06:58:54,273 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:54,273 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:54,274 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:54,274 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:33.371211+00:00', 'data_returned': True}
2025-08-09 06:58:54,274 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:54,274 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:58:54,275 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:54,275 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:30.490951+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 06:58:54,275 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_fee_debt_by_institution
2025-08-09 06:58:54,275 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 06:58:54,275 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:40.230224+00:00', 'data_returned': True}
2025-08-09 06:58:54,275 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:54,275 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:53:26.226065+00:00', 'data_returned': True}
2025-08-09 06:58:54,275 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:54,275 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T05:50:33.371211+00:00', 'data_returned': True}
2025-08-09 06:58:54,276 - app.chains.section_writer - INFO -   📄 Doc 5: Question: What is the average fee debt per student at the institution where students owe the most fe...
2025-08-09 06:58:54,276 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'From which instituion do students owe the most fees?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T06:47:39.202382+00:00', 'data_returned': True}
2025-08-09 06:58:54,276 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (2549 chars):
2025-08-09 06:58:54,276 - app.chains.section_writer - INFO -    Question: How does the fee debt at the institution where students owe the most fees compare to other institutions?
Answer: The institution with the highest fee debt is ITC University, with a total debt of approximately -75,614,369.64. This is significantly higher than the next highest, which is the University Of Education, Winneba, with a total debt of about -34,045,567.83. In comparison, Central University has a total debt of -2,600.0, and Trinity Theological Seminary has a total debt of -27,04...
2025-08-09 06:58:57,414 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 06:58:57,422 - app.chains.section_writer - INFO - 🤖 AI generated section (993 chars):
2025-08-09 06:58:57,423 - app.chains.section_writer - INFO -    In conclusion, the analysis of student fee debt at ITC University reveals that it holds the highest total fee debt among comparable institutions, amounting to approximately -75,614,369.64. This figure is significantly greater than that of the University of Education, Winneba, which has a total debt ...
2025-08-09 06:58:57,423 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_fee_debt_by_institution']
2025-08-09 06:58:57,423 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 6 completed and processed: 974 characters
2025-08-09 06:58:57,424 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 06:58:57,424 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-09 06:58:57,424 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 06:58:57,424 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-09 06:58:57,424 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 7
2025-08-09 06:58:57,424 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 7
2025-08-09 06:58:57,424 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 1
2025-08-09 06:58:57,424 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250809_065228.log
2025-08-09 06:58:57,432 - celery.app.trace - INFO - Task generate_streaming_report[139530bd-f300-4820-acf1-e8afc043b9a5] succeeded in 389.26001749999705s: {'outline': '# Report on Student Fee Debt Across Institutions

## Introduction  
- This report analyzes student fee debt across various institutions, focusing on identifying which institution has the highest fees owed by students. Understanding the dynamics of fee debt is crucial for assessing the financial health of students and the effectiveness of institutional support systems.

## Methodology  
- Data collection was conducted through interviews with students and institutional representatives.  
- Responses were analyzed to extract key themes and insights regarding fee debt levels and contributing factors.

## Overview of Institutions  
- Institutions analyzed include:  
  - ITC University  
  - University of Education, Winneba  
  - Central University  
  - Trinity Theological Seminary  
- Brief descriptions of each institution:  
  - Type (public, private, community)  
  - Enrollment numbers  
  - Average fees charged

## Findings  

### 1. Institution with the Highest Fee Debt  
- **ITC University**  
 ...', ...}

2025-08-08 01:39:56,188 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:39:56,188 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:39:56,188 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:39:56,188 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'total student count'
2025-08-08 01:39:56,188 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 01:39:56,189 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 01:39:56,324 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.135s]
2025-08-08 01:39:56,324 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:39:56,574 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.250s]
2025-08-08 01:39:56,574 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:39:56,878 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.303s]
2025-08-08 01:39:56,878 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 01:39:57,075 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.196s]
2025-08-08 01:39:57,075 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:39:59,243 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:39:59,817 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.513s]
2025-08-08 01:39:59,818 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 01:39:59,818 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 01:40:00,497 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:40:01,007 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.509s]
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 5 documents
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How many students are part of each entry mode, such as direct entry or transfer?
Answer: I...
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:26:47.923365+00:00', 'data_returned': False}
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What factors do you think contributed to the significant increase in student enrollment fr...
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:25:17.089287+00:00', 'data_returned': False}
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How does the average class size of 15,532 compare to previous years, and what factors migh...
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:46.440488+00:00', 'data_returned': False}
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What factors do you believe are driving this dramatic increase in student enrollment since...
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:20:52.305446+00:00', 'data_returned': False}
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What factors do you believe contribute to the high number of inactive students, and how mi...
2025-08-08 01:40:01,008 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:11:03.238356+00:00', 'data_returned': False}
2025-08-08 01:40:01,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:40:01,959 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:40:01,959 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:40:01,959 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:40:01,959 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'total student enrollment'
2025-08-08 01:40:01,959 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 01:40:01,960 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 01:40:02,128 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.169s]
2025-08-08 01:40:02,129 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:40:02,274 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.145s]
2025-08-08 01:40:02,274 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:40:02,535 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.261s]
2025-08-08 01:40:02,536 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 01:40:02,802 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.266s]
2025-08-08 01:40:02,803 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:40:04,389 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:40:04,608 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.216s]
2025-08-08 01:40:04,609 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 01:40:04,609 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 01:40:05,005 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:40:05,411 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.403s]
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 5 documents
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What factors do you think contributed to the significant increase in student enrollment fr...
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:25:17.089287+00:00', 'data_returned': False}
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What factors do you believe are driving this dramatic increase in student enrollment since...
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:20:52.305446+00:00', 'data_returned': False}
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What factors do you think contributed to the significant increase in enrollment from 2021 ...
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:00.152982+00:00', 'data_returned': False}
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How many students are part of each entry mode, such as direct entry or transfer?
Answer: I...
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:26:47.923365+00:00', 'data_returned': False}
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What factors do you think have contributed to the current lack of student enrollment at th...
2025-08-08 01:40:05,412 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:53.301299+00:00', 'data_returned': False}
2025-08-08 01:40:06,769 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:40:07,461 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:40:07,464 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:40:07,464 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:40:07,464 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:40:07,465 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'total student enrollment'
2025-08-08 01:40:07,465 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 01:40:07,465 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 01:40:07,687 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.223s]
2025-08-08 01:40:07,688 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:40:07,751 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:40:07,757 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:40:07,757 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:40:07,757 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:40:07,757 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student enrollment overview'
2025-08-08 01:40:07,757 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 01:40:07,757 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 01:40:07,962 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.273s]
2025-08-08 01:40:07,962 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:40:08,142 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:40:08,154 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:40:08,154 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-08 01:40:08,154 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:40:08,154 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'student enrollment key findings'
2025-08-08 01:40:08,154 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'How many students are there?'
2025-08-08 01:40:08,154 - VECTOR_SEARCH - INFO - ❓ Original Question: 'How many students are there?'
2025-08-08 01:40:08,193 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.230s]
2025-08-08 01:40:08,193 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 01:40:08,271 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.514s]
2025-08-08 01:40:08,271 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:40:08,325 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.132s]
2025-08-08 01:40:08,325 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:40:08,398 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.127s]
2025-08-08 01:40:08,399 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:40:08,472 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.317s]
2025-08-08 01:40:08,472 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 33
2025-08-08 01:40:08,627 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.228s]
2025-08-08 01:40:08,627 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 01:40:08,813 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.340s]
2025-08-08 01:40:08,813 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:40:08,946 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.319s]
2025-08-08 01:40:08,947 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:40:09,141 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:40:09,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:40:09,582 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.769s]
2025-08-08 01:40:09,583 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 18
2025-08-08 01:40:10,326 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.744s]
2025-08-08 01:40:10,327 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.183s]
2025-08-08 01:40:10,327 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 0
2025-08-08 01:40:10,328 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 01:40:10,328 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 01:40:10,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:40:10,954 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:40:11,042 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.685s]
2025-08-08 01:40:11,042 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 01:40:11,042 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 01:40:11,520 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:40:12,170 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:1.485s]
2025-08-08 01:40:12,171 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 0 documents
2025-08-08 01:40:12,171 - VECTOR_SEARCH - WARNING - ⚠️ No documents with data_returned=True found, falling back to all conversation documents
2025-08-08 01:40:12,689 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:40:13,296 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:2.338s]
2025-08-08 01:40:13,296 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 5 documents
2025-08-08 01:40:13,296 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What factors do you think contributed to the significant increase in student enrollment fr...
2025-08-08 01:40:13,296 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:25:17.089287+00:00', 'data_returned': False}
2025-08-08 01:40:13,296 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What factors do you believe are driving this dramatic increase in student enrollment since...
2025-08-08 01:40:13,296 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:20:52.305446+00:00', 'data_returned': False}
2025-08-08 01:40:13,296 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What factors do you think contributed to the significant increase in enrollment from 2021 ...
2025-08-08 01:40:13,296 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:00.152982+00:00', 'data_returned': False}
2025-08-08 01:40:13,297 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How many students are part of each entry mode, such as direct entry or transfer?
Answer: I...
2025-08-08 01:40:13,297 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:26:47.923365+00:00', 'data_returned': False}
2025-08-08 01:40:13,297 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What factors do you think have contributed to the current lack of student enrollment at th...
2025-08-08 01:40:13,297 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:53.301299+00:00', 'data_returned': False}
2025-08-08 01:40:13,825 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:2.302s]
2025-08-08 01:40:13,825 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 5 documents
2025-08-08 01:40:13,826 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What factors do you believe are driving this dramatic increase in student enrollment since...
2025-08-08 01:40:13,826 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:20:52.305446+00:00', 'data_returned': False}
2025-08-08 01:40:13,826 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What factors do you think contributed to the significant increase in student enrollment fr...
2025-08-08 01:40:13,826 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:25:17.089287+00:00', 'data_returned': False}
2025-08-08 01:40:13,826 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What factors do you think contributed to the significant increase in enrollment from 2021 ...
2025-08-08 01:40:13,826 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:00.152982+00:00', 'data_returned': False}
2025-08-08 01:40:13,826 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What factors contribute to the significant differences in enrollment numbers across the va...
2025-08-08 01:40:13,826 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:21:36.522989+00:00', 'data_returned': False}
2025-08-08 01:40:13,826 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What factors do you think have contributed to the current lack of student enrollment at th...
2025-08-08 01:40:13,826 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:53.301299+00:00', 'data_returned': False}
2025-08-08 01:40:15,153 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:2.460s]
2025-08-08 01:40:15,154 - VECTOR_SEARCH - INFO - 🔄 Fallback search returned 5 documents
2025-08-08 01:40:15,154 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What factors do you think contributed to the significant increase in student enrollment fr...
2025-08-08 01:40:15,154 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:25:17.089287+00:00', 'data_returned': False}
2025-08-08 01:40:15,154 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What factors do you believe are driving this dramatic increase in student enrollment since...
2025-08-08 01:40:15,154 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:20:52.305446+00:00', 'data_returned': False}
2025-08-08 01:40:15,154 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What factors do you think contributed to the significant increase in enrollment from 2021 ...
2025-08-08 01:40:15,154 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:13:00.152982+00:00', 'data_returned': False}
2025-08-08 01:40:15,155 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What factors contribute to the significant differences in enrollment numbers across the va...
2025-08-08 01:40:15,155 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:21:36.522989+00:00', 'data_returned': False}
2025-08-08 01:40:15,155 - VECTOR_SEARCH - INFO -   📄 Doc 5: Question: What factors do you think contribute to the higher enrollment in full-time degree programs...
2025-08-08 01:40:15,155 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T00:20:49.078222+00:00', 'data_returned': False}
2025-08-08 01:40:16,874 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:40:17,293 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:40:17,701 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"

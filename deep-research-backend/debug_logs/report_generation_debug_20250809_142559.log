2025-08-09 14:25:59,307 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250809_142559.log
2025-08-09 14:25:59,307 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:25:59,307 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: cf2053d9-44b6-4d5a-8c42-abdf793243b0
2025-08-09 14:25:59,307 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:25:59,308 - REPORT_REQUEST - INFO - 📝 Original Question: 'Which institutions owe most fees'
2025-08-09 14:25:59,308 - REPORT_REQUEST - INFO - 🆔 Task ID: cf2053d9-44b6-4d5a-8c42-abdf793243b0
2025-08-09 14:25:59,308 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-09T14:25:59.308174
2025-08-09 14:25:59,548 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.240s]
2025-08-09 14:25:59,549 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 1
2025-08-09 14:25:59,691 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.142s]
2025-08-09 14:25:59,692 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-09 14:25:59,931 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search [status:200 duration:0.239s]
2025-08-09 14:25:59,931 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (1):
2025-08-09 14:25:59,931 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University' (1 docs)
2025-08-09 14:25:59,932 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:25:59,932 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-09 14:25:59,932 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:25:59,932 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: Which institutions owe most fees
2025-08-09 14:25:59,932 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-09 14:25:59,932 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
2025-08-09 14:26:11,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:11,141 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-09 14:26:16,194 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:16,214 - app.chains.composite.report_pipeline - INFO - filtering_questions
2025-08-09 14:26:18,509 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:18,567 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:18,678 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:19,091 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:19,092 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:19,272 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:19,286 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:19,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:19,402 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:19,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:19,737 - app.chains.composite.report_pipeline - INFO - conducting_interviews
2025-08-09 14:26:22,163 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:22,163 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:22,311 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:22,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:22,505 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:22,883 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:23,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:23,079 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:24,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:24,775 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:25,131 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:25,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:25,501 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:25,597 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:26,053 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:26,054 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:26,462 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:26,463 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:26,818 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:27,106 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:27,160 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:27,382 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:27,512 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:27,995 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:30,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:30,968 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:31,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:31,714 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:31,784 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:32,009 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:32,386 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:33,323 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:35,027 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:35,035 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the total amount of fees owed by students at the institution where students owe the most fees?', 'sql': 'SELECT institution_id, SUM(balance) AS total_fees_owed\nFROM student_balances\nGROUP BY institution_id\nORDER BY total_fees_owed DESC\nLIMIT 1;', 'correct': True, 'reasoning': "The SQL query correctly aggregates the total balance (fees owed) for each institution by summing the 'balance' field in the 'student_balances' table. It groups the results by 'institution_id' and orders them in descending order based on the total fees owed. The use of 'LIMIT 1' ensures that only the institution with the highest total fees owed is returned, which directly answers the question.", 'feedback': 'The SQL query is well-structured and effectively answers the question. No improvements are necessary, but it could be beneficial to include a check for cases where there are no balances recorded to avoid returning null results.'}
2025-08-09 14:26:35,035 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:35,036 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:26:36,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:37,419 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:37,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:37,432 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What types of fees are most commonly owed by students at the institution where students owe the most fees?', 'sql': 'SELECT bit.name AS fee_type, COUNT(sb.bill_id) AS fee_count\nFROM student_bills sb\nJOIN bills b ON sb.bill_id = b.id\nJOIN bill_item_types bit ON b.bill_category_id = bit.id\nWHERE sb.institution_id = (SELECT institution_id FROM student_balances GROUP BY institution_id ORDER BY SUM(balance) DESC LIMIT 1)\nGROUP BY bit.name\nORDER BY fee_count DESC;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution where students owe the most fees by using a subquery that sums the balances grouped by institution and orders them in descending order. It then counts the occurrences of each fee type associated with bills from that institution, grouping the results by fee type and ordering them by the count of fees owed. This aligns with the question's requirement to find the most common types of fees owed at the institution with the highest total fees owed.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding a LIMIT clause to the outer query to return only the top N fee types, if the question implies a need for a ranked list rather than all fee types.'}
2025-08-09 14:26:37,432 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How many students are currently enrolled at the institution where students owe the most fees?', 'sql': 'SELECT COUNT(DISTINCT s.id) AS enrolled_students\nFROM core.students s\nJOIN core.student_programs sp ON s.id = sp.student_id\nJOIN core.student_balances sb ON s.id = sb.student_id\nJOIN core.billing_periods bp ON sb.billing_period_id = bp.id\nWHERE sp.current = TRUE\nAND sb.balance > 0\nAND s.institution_id = (SELECT institution_id\n                        FROM core.student_balances\n                        GROUP BY institution_id\n                        ORDER BY SUM(balance) DESC\n                        LIMIT 1);', 'correct': True, 'reasoning': "The SQL query correctly identifies the number of distinct students who are currently enrolled (where 'current' is TRUE) and have a positive balance (indicating they owe fees). It also correctly identifies the institution with the highest total balance owed by students by using a subquery that groups by institution_id and orders by the sum of balances in descending order, limiting the result to the top institution. Therefore, the query accurately answers the question posed.", 'feedback': "The SQL query is well-structured and effectively answers the question. However, it could be improved by adding comments to clarify the purpose of each join and the subquery for future reference. Additionally, ensure that the balance condition (sb.balance > 0) aligns with the interpretation of 'owe the most fees' as it assumes that only students with a positive balance are considered."}
2025-08-09 14:26:37,432 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:37,432 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:37,432 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:26:37,432 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:26:37,529 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:37,581 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:37,748 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:39,243 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:39,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:39,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:39,835 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:40,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:40,469 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:40,639 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:40,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:41,332 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:41,516 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:42,392 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:42,406 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:42,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:43,164 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:43,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:43,871 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:43,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:44,326 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:44,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:45,000 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:45,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:45,774 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:46,178 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:46,179 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:46,199 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative fee balance at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative fee balance. While the schema contains various tables related to student transactions, billing, and financial aid, it does not explicitly define the factors or reasons that lead to overpayments or negative balances. The schema includes tables for student transactions, bills, and balances, but it lacks detailed descriptions or attributes that would provide insights into the causes of overpayments or credits. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:26:46,202 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:46,202 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:26:46,202 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative fee balance at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative fee balance. While the schema contains various tables related to student transactions, billing, and financial aid, it does not explicitly define the factors or reasons that lead to overpayments or negative balances. The schema includes tables for student transactions, bills, and balances, but it lacks detailed descriptions or attributes that would provide insights into the causes of overpayments or credits. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:26:46,894 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:46,913 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students accumulating these hostel fees, and how does the institution address this issue?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to students accumulating hostel fees and how the institution addresses this issue. However, the provided schema does not contain specific information about hostel fees, their accumulation factors, or institutional policies regarding these fees. While there are tables related to students, billing, and financial transactions, there is no direct reference to hostel fees or the factors influencing them. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:26:46,913 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:46,913 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:26:46,913 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students accumulating these hostel fees, and how does the institution address this issue?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to students accumulating hostel fees and how the institution addresses this issue. However, the provided schema does not contain specific information about hostel fees, their accumulation factors, or institutional policies regarding these fees. While there are tables related to students, billing, and financial transactions, there is no direct reference to hostel fees or the factors influencing them. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:26:47,602 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:47,786 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:48,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:48,593 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:48,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:49,013 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:49,133 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:49,652 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:49,860 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:49,909 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:50,865 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:50,886 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative fee balance at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative fee balance. While the schema contains tables related to student transactions, billing, and financial aid, it does not explicitly define the factors or reasons that lead to overpayments or negative balances. The schema lacks detailed descriptions or attributes that would allow for a comprehensive analysis of the causes of these financial situations. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:26:50,887 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:50,887 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:26:50,887 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative fee balance at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative fee balance. While the schema contains tables related to student transactions, billing, and financial aid, it does not explicitly define the factors or reasons that lead to overpayments or negative balances. The schema lacks detailed descriptions or attributes that would allow for a comprehensive analysis of the causes of these financial situations. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:26:50,963 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:50,984 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students accumulating these hostel fees, and how does the institution address this issue?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to students accumulating hostel fees and how the institution addresses this issue. However, the provided schema does not contain specific information regarding hostel fees, their accumulation factors, or institutional policies related to hostel fees. While there are tables related to students, billing, and financial transactions, there is no direct reference to hostel fees or the factors influencing them. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:26:50,984 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:50,984 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:26:50,984 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students accumulating these hostel fees, and how does the institution address this issue?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to students accumulating hostel fees and how the institution addresses this issue. However, the provided schema does not contain specific information regarding hostel fees, their accumulation factors, or institutional policies related to hostel fees. While there are tables related to students, billing, and financial transactions, there is no direct reference to hostel fees or the factors influencing them. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:26:52,369 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:52,566 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:53,394 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:53,395 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:53,401 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'Are there specific programs or courses at the institution where students owe the most fees that contribute to higher fee debts?', 'sql': 'SELECT p.long_name AS program_name, c.title AS course_title, SUM(b.total_due) AS total_debt\nFROM programs p\nJOIN student_bills sb ON sb.student_program_id = p.id\nJOIN bills b ON b.id = sb.bill_id\nJOIN courses c ON c.unit_id = p.unit_id\nGROUP BY p.long_name, c.title\nORDER BY total_debt DESC;', 'correct': True, 'reasoning': "The SQL query correctly identifies the programs and courses associated with students' fee debts by joining the relevant tables: programs, student_bills, bills, and courses. It aggregates the total fees owed (total_due) for each program and course combination, which directly addresses the question about specific programs or courses contributing to higher fee debts. The use of GROUP BY and ORDER BY ensures that the results are organized by the total debt in descending order, highlighting those with the highest debts.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by adding a WHERE clause to filter for specific institutions if the schema allows for multiple institutions, ensuring the results are relevant to the intended context.'}
2025-08-09 14:26:53,401 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:53,401 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:26:53,483 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:54,339 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:54,359 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide direct insights into the specific factors causing high fee debt or the correlation with student enrollment. The schema lacks qualitative data or analysis capabilities to assess the reasons behind fee debt and its effects on enrollment trends.', 'feedback': ''}
2025-08-09 14:26:54,360 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:54,360 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:26:54,360 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and financial aid, it does not provide direct insights into the specific factors causing high fee debt or the correlation with student enrollment. The schema lacks qualitative data or analysis capabilities to assess the reasons behind fee debt and its effects on enrollment trends.', 'feedback': ''}
2025-08-09 14:26:55,542 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:55,557 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students accumulating these hostel fees, and how does the institution address this issue?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to students accumulating hostel fees and how the institution addresses this issue. However, the provided schema does not contain specific information about hostel fees, their accumulation factors, or institutional policies regarding these fees. While there are tables related to students, billing, and financial transactions, there is no direct reference to hostel fees or the factors influencing them. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:26:55,557 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:55,557 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:26:55,557 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students accumulating these hostel fees, and how does the institution address this issue?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to students accumulating hostel fees and how the institution addresses this issue. However, the provided schema does not contain specific information about hostel fees, their accumulation factors, or institutional policies regarding these fees. While there are tables related to students, billing, and financial transactions, there is no direct reference to hostel fees or the factors influencing them. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:26:55,599 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:55,955 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:55,966 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'How does the fee debt at the institution where students owe the most fees compare to other institutions?', 'sql': "WITH InstitutionDebts AS (  SELECT institution_id, SUM(balance) AS total_debt  FROM student_balances  GROUP BY institution_id), MaxDebtInstitution AS (  SELECT institution_id, total_debt  FROM InstitutionDebts  WHERE total_debt = (SELECT MAX(total_debt) FROM InstitutionDebts)) SELECT i.name AS institution_name, d.total_debt, (SELECT AVG(total_debt) FROM InstitutionDebts) AS average_debt, (SELECT COUNT(*) FROM InstitutionDebts) AS total_institutions FROM InstitutionDebts d JOIN institutions i ON d.institution_id = i.id UNION ALL SELECT 'Max Debt Institution', m.total_debt, (SELECT AVG(total_debt) FROM InstitutionDebts) AS average_debt, (SELECT COUNT(*) FROM InstitutionDebts) AS total_institutions FROM MaxDebtInstitution m;", 'correct': True, 'reasoning': 'The SQL query correctly identifies the institution with the highest total debt by summing the balances from the student_balances table and grouping by institution_id. It then compares this maximum debt to the average debt across all institutions and counts the total number of institutions. The use of CTEs (Common Table Expressions) allows for clear organization of the data being processed, and the final output includes the necessary comparisons as requested in the question.', 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly labeling the columns in the final output for clarity, such as naming the average debt and total institutions more descriptively. Additionally, the question could specify whether it wants the comparison in terms of percentage or absolute values, which could lead to a more detailed analysis.'}
2025-08-09 14:26:55,967 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:55,967 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:26:56,064 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:56,087 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative fee balance at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative fee balance. While the schema contains various tables related to student transactions, billing, and financial aid, it does not explicitly define the factors or reasons that lead to overpayments or negative balances. The schema includes tables for student transactions, bills, and balances, but it lacks detailed descriptions or attributes that would provide insights into the causes of overpayments or credits. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:26:56,087 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:56,087 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:26:56,087 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative fee balance at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative fee balance. While the schema contains various tables related to student transactions, billing, and financial aid, it does not explicitly define the factors or reasons that lead to overpayments or negative balances. The schema includes tables for student transactions, bills, and balances, but it lacks detailed descriptions or attributes that would provide insights into the causes of overpayments or credits. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:26:56,292 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:56,772 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:56,849 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:57,044 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:57,708 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:58,323 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:58,398 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:58,759 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:58,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:59,228 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:59,242 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and enrollment, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to fee debt or the direct impact on enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:26:59,243 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:59,243 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:26:59,243 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and enrollment, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to fee debt or the direct impact on enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:26:59,639 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:59,661 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:59,920 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:26:59,939 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contribute to students accumulating these hostel fees, and how does the institution address this issue?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to students accumulating hostel fees and how the institution addresses this issue. However, the provided schema does not contain specific information about hostel fees, their accumulation, or institutional policies regarding them. While there are tables related to students, billing, and financial transactions, there is no direct reference to hostel fees or the factors influencing them. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:26:59,939 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:26:59,939 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:26:59,939 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contribute to students accumulating these hostel fees, and how does the institution address this issue?', 'answerable': False, 'reasoning': 'The question asks about the factors contributing to students accumulating hostel fees and how the institution addresses this issue. However, the provided schema does not contain specific information about hostel fees, their accumulation, or institutional policies regarding them. While there are tables related to students, billing, and financial transactions, there is no direct reference to hostel fees or the factors influencing them. Therefore, the question cannot be answered based on the current schema.', 'feedback': ''}
2025-08-09 14:27:00,459 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:00,506 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:00,526 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative fee balance at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative fee balance. While the schema contains tables related to student transactions, billing, and financial aid, it does not explicitly define the factors or reasons behind overpayments or credits. The schema lacks detailed descriptions or attributes that would allow for a comprehensive analysis of the causes of negative fee balances. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:27:00,526 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:00,526 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:00,526 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to students overpaying or receiving credits that lead to a negative fee balance at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to students overpaying or receiving credits that lead to a negative fee balance. While the schema contains tables related to student transactions, billing, and financial aid, it does not explicitly define the factors or reasons behind overpayments or credits. The schema lacks detailed descriptions or attributes that would allow for a comprehensive analysis of the causes of negative fee balances. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:27:00,530 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:01,170 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:01,301 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:01,635 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:01,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:02,421 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:02,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:02,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:02,752 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:02,769 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might influence students' decisions to enroll in programs with higher fees, even if they are not currently associated with significant debt?", 'answerable': False, 'reasoning': "The question asks about the factors influencing students' decisions to enroll in programs with higher fees, which is a qualitative analysis involving student behavior and motivations. The provided schema contains data about students, programs, fees, and related entities, but it does not include qualitative data or insights into student motivations or decision-making processes. Therefore, the question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-09 14:27:02,769 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:02,769 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:02,770 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might influence students' decisions to enroll in programs with higher fees, even if they are not currently associated with significant debt?", 'answerable': False, 'reasoning': "The question asks about the factors influencing students' decisions to enroll in programs with higher fees, which is a qualitative analysis involving student behavior and motivations. The provided schema contains data about students, programs, fees, and related entities, but it does not include qualitative data or insights into student motivations or decision-making processes. Therefore, the question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-09 14:27:03,566 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:03,580 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and possibly financial aid, it does not provide direct insights into the specific factors causing high fee debt or how these factors affect student enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:27:03,581 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:03,581 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:03,581 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and possibly financial aid, it does not provide direct insights into the specific factors causing high fee debt or how these factors affect student enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:27:03,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:04,979 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:05,016 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:05,021 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:05,619 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:06,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:06,574 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:07,112 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:07,135 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might influence students' decisions to enroll in programs with higher fees, even if they are not currently associated with significant debt?", 'answerable': False, 'reasoning': "The question asks about the factors influencing students' decisions to enroll in programs with higher fees, which is a qualitative analysis involving student behavior and motivations. The provided schema contains data about students, programs, fees, and transactions, but it does not include qualitative data or insights into student motivations, preferences, or external factors that might influence their decisions. Therefore, the question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-09 14:27:07,135 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:07,135 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:07,135 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might influence students' decisions to enroll in programs with higher fees, even if they are not currently associated with significant debt?", 'answerable': False, 'reasoning': "The question asks about the factors influencing students' decisions to enroll in programs with higher fees, which is a qualitative analysis involving student behavior and motivations. The provided schema contains data about students, programs, fees, and transactions, but it does not include qualitative data or insights into student motivations, preferences, or external factors that might influence their decisions. Therefore, the question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-09 14:27:07,373 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:07,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:07,401 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. While the schema contains various tables related to institutions, students, and financial transactions, it does not provide specific data or metrics that would allow for a comparative analysis of fee debt across institutions. Additionally, the schema lacks qualitative data or insights into the reasons behind fee debt, such as institutional policies, student demographics, or economic factors. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:27:07,402 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:07,402 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:07,402 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. While the schema contains various tables related to institutions, students, and financial transactions, it does not provide specific data or metrics that would allow for a comparative analysis of fee debt across institutions. Additionally, the schema lacks qualitative data or insights into the reasons behind fee debt, such as institutional policies, student demographics, or economic factors. Therefore, the question cannot be answered directly or indirectly from the provided schema.', 'feedback': ''}
2025-08-09 14:27:07,478 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:07,492 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and enrollment, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to fee debt or the direct impact on enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:27:07,492 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:07,492 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:07,492 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors contributed to the high fee debt at this institution, and how have they impacted student enrollment?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to high fee debt and their impact on student enrollment. While the schema contains tables related to student transactions, bills, and enrollment, it does not provide specific data or metrics that would allow for a comprehensive analysis of the factors leading to fee debt or the direct impact on enrollment. The schema lacks qualitative data or contextual information that would be necessary to answer such a complex question.', 'feedback': ''}
2025-08-09 14:27:09,515 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:09,586 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:10,496 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:12,234 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:12,234 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:12,241 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might influence students' decisions to enroll in programs with higher fees, even if they are not currently associated with significant debt?", 'answerable': False, 'reasoning': "The question asks about the factors influencing students' decisions to enroll in programs with higher fees, which is a qualitative analysis involving student behavior and motivations. The provided schema contains data about students, programs, fees, and related entities, but it does not include any qualitative data or insights into student motivations, preferences, or external factors that might influence their decisions. Therefore, the question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-09 14:27:12,242 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:12,242 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:12,242 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might influence students' decisions to enroll in programs with higher fees, even if they are not currently associated with significant debt?", 'answerable': False, 'reasoning': "The question asks about the factors influencing students' decisions to enroll in programs with higher fees, which is a qualitative analysis involving student behavior and motivations. The provided schema contains data about students, programs, fees, and related entities, but it does not include any qualitative data or insights into student motivations, preferences, or external factors that might influence their decisions. Therefore, the question cannot be answered directly or indirectly from the schema.", 'feedback': ''}
2025-08-09 14:27:12,565 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:12,580 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. However, the provided schema does not contain specific data or metrics that would allow for a comparative analysis of fee debts across institutions. While there are tables related to billing, fees, and institutions, there is no direct way to assess or compare the reasons behind fee debts without additional context or data that might explain the underlying factors (e.g., financial aid, student demographics, payment history). Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 14:27:12,580 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:12,580 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:12,580 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. However, the provided schema does not contain specific data or metrics that would allow for a comparative analysis of fee debts across institutions. While there are tables related to billing, fees, and institutions, there is no direct way to assess or compare the reasons behind fee debts without additional context or data that might explain the underlying factors (e.g., financial aid, student demographics, payment history). Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 14:27:13,273 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:14,722 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:14,723 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:14,821 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:15,204 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:16,126 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:16,952 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:16,967 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors do you think might influence students' decisions to enroll in programs with higher fees, even if they are not currently associated with significant debt?", 'answerable': False, 'reasoning': "The question asks about the factors influencing students' decisions to enroll in programs with higher fees, which is a qualitative inquiry. The provided schema contains data about students, programs, fees, and transactions, but it does not include qualitative data or insights into students' motivations, preferences, or decision-making processes. Therefore, the schema does not support answering this question.", 'feedback': ''}
2025-08-09 14:27:16,967 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:16,967 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:16,967 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors do you think might influence students' decisions to enroll in programs with higher fees, even if they are not currently associated with significant debt?", 'answerable': False, 'reasoning': "The question asks about the factors influencing students' decisions to enroll in programs with higher fees, which is a qualitative inquiry. The provided schema contains data about students, programs, fees, and transactions, but it does not include qualitative data or insights into students' motivations, preferences, or decision-making processes. Therefore, the schema does not support answering this question.", 'feedback': ''}
2025-08-09 14:27:17,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:18,276 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:18,277 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:18,298 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. However, the provided schema does not contain specific data or attributes that directly relate to the analysis of fee debt or the factors influencing it. While there are tables related to billing, fees, and institutions, the schema lacks detailed information on the reasons or factors that could lead to higher debt levels, such as financial aid data, student demographics, or specific financial behaviors. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 14:27:18,300 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:18,301 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:18,301 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. However, the provided schema does not contain specific data or attributes that directly relate to the analysis of fee debt or the factors influencing it. While there are tables related to billing, fees, and institutions, the schema lacks detailed information on the reasons or factors that could lead to higher debt levels, such as financial aid data, student demographics, or specific financial behaviors. Therefore, the question cannot be answered with the available schema.', 'feedback': ''}
2025-08-09 14:27:19,606 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:20,883 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:21,034 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:21,405 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:23,422 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:23,443 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. However, the provided schema does not contain specific data or metrics that would allow for a comparative analysis of fee debts across institutions. While there are tables related to fees, bills, and institutions, there is no direct way to assess or compare the reasons for fee debt without additional context or data that links these debts to specific factors or causes.', 'feedback': ''}
2025-08-09 14:27:23,443 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:23,443 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:23,443 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you believe contribute to the significantly higher fee debt at ITC University compared to the other institutions?', 'answerable': False, 'reasoning': 'The question asks for an analysis of factors contributing to higher fee debt at ITC University compared to other institutions. However, the provided schema does not contain specific data or metrics that would allow for a comparative analysis of fee debts across institutions. While there are tables related to fees, bills, and institutions, there is no direct way to assess or compare the reasons for fee debt without additional context or data that links these debts to specific factors or causes.', 'feedback': ''}
2025-08-09 14:27:24,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:24,803 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:26,468 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:27,847 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:29,335 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:29,406 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:30,155 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:32,621 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:33,933 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:34,036 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:34,557 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:37,041 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:38,556 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:38,873 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:41,214 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:41,219 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What percentage of students at the institution where students owe the most fees are on financial aid or scholarships?', 'sql': 'WITH max_balance AS (SELECT institution_id, MAX(balance) AS max_fee FROM student_balances GROUP BY institution_id), students_with_aid AS (SELECT DISTINCT s.id FROM students s JOIN student_scholarships ss ON s.id = ss.student_id JOIN financial_aid_requests far ON s.id = far.requested_by WHERE s.institution_id = (SELECT institution_id FROM max_balance)) SELECT COUNT(DISTINCT s.id) * 100.0 / (SELECT COUNT(*) FROM students WHERE institution_id = (SELECT institution_id FROM max_balance)) AS percentage_with_aid FROM students_with_aid s;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest student balance by using a Common Table Expression (CTE) to find the maximum balance per institution. It then retrieves the distinct student IDs who are either on scholarships or have financial aid requests at that institution. Finally, it calculates the percentage of these students relative to the total number of students at the same institution. The logic follows the question's requirements accurately.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by ensuring that the CTEs are clearly defined and perhaps adding comments for clarity. Additionally, it might be beneficial to handle cases where no students are found to avoid division by zero errors.'}
2025-08-09 14:27:41,219 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:41,219 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:27:42,660 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:44,183 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:45,320 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:46,380 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:47,134 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:47,139 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the average fee amount owed per student at the institution where students owe the most fees?', 'sql': 'SELECT AVG(total_due_per_student) AS average_fee_per_student\nFROM (\n    SELECT sb.student_id, SUM(b.total_due) AS total_due_per_student\n    FROM student_bills sb\n    JOIN bills b ON sb.bill_id = b.id\n    JOIN students s ON sb.student_id = s.id\n    GROUP BY sb.student_id, s.institution_id\n) AS student_totals\nWHERE institution_id = (\n    SELECT institution_id\n    FROM student_bills sb\n    JOIN bills b ON sb.bill_id = b.id\n    GROUP BY sb.student_id, sb.institution_id\n    ORDER BY SUM(b.total_due) DESC\n    LIMIT 1\n);', 'correct': True, 'reasoning': "The SQL query correctly calculates the average fee amount owed per student at the institution where students owe the most fees. It first computes the total amount owed by each student by summing the total_due from the bills table, grouped by student_id and institution_id. Then, it identifies the institution with the highest total fees owed by students using a subquery that orders the sums in descending order and limits the result to the top institution. Finally, it calculates the average of the total_due_per_student for that specific institution. The logic aligns with the question's requirements.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by ensuring that the subquery for the institution_id is clearly defined to avoid any potential ambiguity regarding which institution is being referenced. Additionally, adding comments to the SQL could enhance readability for others reviewing the code.'}
2025-08-09 14:27:47,139 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:47,139 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:27:47,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:47,875 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the historical trend of fee debt at the institution where students owe the most fees over the past few years?', 'sql': 'SELECT bp.start_date, SUM(sb.balance) AS total_debt\nFROM student_balances sb\nJOIN billing_periods bp ON sb.billing_period_id = bp.id\nWHERE bp.start_date >= DATEADD(YEAR, -3, GETDATE())\nGROUP BY bp.start_date\nORDER BY bp.start_date ASC;', 'correct': False, 'reasoning': 'The SQL query calculates the total debt over the past three years but does not address the requirement to find the institution where students owe the most fees. The query lacks a filter or aggregation to identify the institution with the highest total debt, which is crucial to answering the question accurately.', 'feedback': 'To improve the SQL query, you should include a way to aggregate the total debt by institution and then filter to find the institution with the maximum debt. This could involve using a subquery or a CTE to first calculate total debt per institution and then selecting the one with the highest total debt before calculating the historical trend.', 'note': 'Reached max retries (5) without finding a valid SQL.'}
2025-08-09 14:27:47,875 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:47,875 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback', 'note']
2025-08-09 14:27:48,586 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:49,745 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:50,550 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:50,572 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors might contribute to the lack of financial aid or scholarship data for this institution, and how could this impact students' ability to manage their fees?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of financial aid or scholarship data and its impact on students' fee management. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about financial aid and scholarships. The schema does not provide information on the reasons behind the absence of data or the broader implications for students.", 'feedback': ''}
2025-08-09 14:27:50,572 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:50,572 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:50,572 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors might contribute to the lack of financial aid or scholarship data for this institution, and how could this impact students' ability to manage their fees?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of financial aid or scholarship data and its impact on students' fee management. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about financial aid and scholarships. The schema does not provide information on the reasons behind the absence of data or the broader implications for students.", 'feedback': ''}
2025-08-09 14:27:50,589 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:50,770 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:51,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:52,682 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:53,218 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:53,665 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:54,700 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:54,721 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors might contribute to the lack of financial aid or scholarship data for this institution, and how could this impact students' ability to manage their fees?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of financial aid or scholarship data and its impact on students' fee management. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about financial aid and scholarships. The schema does not provide information on the reasons behind the absence of data or the broader implications for students.", 'feedback': ''}
2025-08-09 14:27:54,721 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:54,721 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:54,721 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors might contribute to the lack of financial aid or scholarship data for this institution, and how could this impact students' ability to manage their fees?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of financial aid or scholarship data and its impact on students' fee management. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about financial aid and scholarships. The schema does not provide information on the reasons behind the absence of data or the broader implications for students.", 'feedback': ''}
2025-08-09 14:27:54,766 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:55,651 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:55,674 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the schema provided does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any qualitative factors that could explain this situation.', 'feedback': ''}
2025-08-09 14:27:55,674 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:55,674 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:55,674 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the schema provided does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any qualitative factors that could explain this situation.', 'feedback': ''}
2025-08-09 14:27:56,677 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:57,981 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:58,706 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:59,132 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:27:59,147 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors might contribute to the lack of financial aid or scholarship data for this institution, and how could this impact students' ability to manage their fees?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of financial aid or scholarship data and its impact on students' fee management. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about financial aid and scholarships. The schema does not provide information on the reasons behind the absence of data or the broader implications for students.", 'feedback': ''}
2025-08-09 14:27:59,148 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:27:59,148 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:27:59,148 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors might contribute to the lack of financial aid or scholarship data for this institution, and how could this impact students' ability to manage their fees?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of financial aid or scholarship data and its impact on students' fee management. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about financial aid and scholarships. The schema does not provide information on the reasons behind the absence of data or the broader implications for students.", 'feedback': ''}
2025-08-09 14:28:00,157 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:00,172 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any contextual factors that could explain this situation.', 'feedback': ''}
2025-08-09 14:28:00,173 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:28:00,173 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:28:00,173 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any contextual factors that could explain this situation.', 'feedback': ''}
2025-08-09 14:28:01,126 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:02,163 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:03,332 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:03,359 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:03,376 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': "What factors might contribute to the lack of financial aid or scholarship data for this institution, and how could this impact students' ability to manage their fees?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of financial aid or scholarship data and its impact on students' fee management. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about financial aid and scholarships. The schema does not provide information on the reasons behind the absence of data or the broader implications for students.", 'feedback': ''}
2025-08-09 14:28:03,377 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:28:03,377 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:28:03,377 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': "What factors might contribute to the lack of financial aid or scholarship data for this institution, and how could this impact students' ability to manage their fees?", 'answerable': False, 'reasoning': "The question asks for an analysis of factors contributing to the lack of financial aid or scholarship data and its impact on students' fee management. This requires qualitative insights and contextual understanding that cannot be derived directly from the database schema, which primarily contains structured data about financial aid and scholarships. The schema does not provide information on the reasons behind the absence of data or the broader implications for students.", 'feedback': ''}
2025-08-09 14:28:04,560 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:04,575 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any contextual factors that could explain this situation.', 'feedback': ''}
2025-08-09 14:28:04,575 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:28:04,575 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:28:04,575 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any contextual factors that could explain this situation.', 'feedback': ''}
2025-08-09 14:28:06,006 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:06,920 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:09,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:09,136 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any contextual factors that could explain this situation.', 'feedback': ''}
2025-08-09 14:28:09,136 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:28:09,136 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:28:09,136 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors might contribute to the lack of available data on outstanding fees at that institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of available data on outstanding fees at an institution. However, the provided schema does not contain any direct information or attributes that would indicate reasons or factors for the absence of data. The schema includes tables related to fees, students, and transactions, but it does not provide insights into the reasons for data unavailability or any contextual factors that could explain this situation.', 'feedback': ''}
2025-08-09 14:28:10,295 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:11,656 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:19,913 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:26,677 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:26,686 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What is the historical trend of fee debt at the institution where students owe the most fees over the past few years?', 'sql': 'WITH InstitutionDebt AS (  SELECT b.institution_id, SUM(b.total_due) AS total_debt  FROM student_bills sb  JOIN bills b ON sb.bill_id = b.id  JOIN billing_periods bp ON b.billing_period_id = bp.id  WHERE bp.start_date >= DATEADD(YEAR, -5, GETDATE())  GROUP BY b.institution_id),  MaxDebtInstitution AS (  SELECT institution_id  FROM InstitutionDebt  ORDER BY total_debt DESC  LIMIT 1)  SELECT bp.description, SUM(b.total_due) AS total_debt  FROM student_bills sb  JOIN bills b ON sb.bill_id = b.id  JOIN billing_periods bp ON b.billing_period_id = bp.id  WHERE b.institution_id = (SELECT institution_id FROM MaxDebtInstitution)  AND bp.start_date >= DATEADD(YEAR, -5, GETDATE())  GROUP BY bp.description  ORDER BY bp.start_date;', 'correct': True, 'reasoning': "The SQL query correctly identifies the institution with the highest total fee debt over the past five years by first calculating the total debt for each institution and then selecting the one with the maximum debt. It then retrieves the historical trend of fee debt for that institution by grouping the total debt by billing period description. The use of date filtering ensures that only relevant data from the past five years is considered, aligning with the question's requirements.", 'feedback': 'The SQL query is well-structured and effectively answers the question. However, it could be improved by explicitly including the time frame (e.g., year) in the final output to clarify the trend over the years. Additionally, using a more descriptive alias for the final output could enhance readability.'}
2025-08-09 14:28:26,686 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:28:26,686 - celery.redirected - WARNING - 🔍 Keys: ['question', 'sql', 'correct', 'reasoning', 'feedback']
2025-08-09 14:28:29,341 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:30,293 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:31,425 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:33,894 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:35,911 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:35,927 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded data on fee debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded data on fee debt at the institution. However, the provided schema does not contain any tables or fields that directly address the reasons or factors behind the absence of recorded data. The schema includes various tables related to students, fees, transactions, and institutions, but it does not provide insights into the reasons for missing data or the context behind fee debt records. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 14:28:35,928 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:28:35,928 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:28:35,928 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded data on fee debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded data on fee debt at the institution. However, the provided schema does not contain any tables or fields that directly address the reasons or factors behind the absence of recorded data. The schema includes various tables related to students, fees, transactions, and institutions, but it does not provide insights into the reasons for missing data or the context behind fee debt records. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 14:28:37,943 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:40,276 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:40,295 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded data on fee debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded data on fee debt at the institution. This is a qualitative inquiry that requires analysis and interpretation of data, rather than a straightforward retrieval of information from the database schema. The schema provides tables related to financial transactions, student billing, and other related entities, but it does not contain explicit information or metrics that would directly answer the question about the reasons for the lack of recorded data. Therefore, it cannot be answered solely based on the schema.', 'feedback': ''}
2025-08-09 14:28:40,295 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:28:40,296 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:28:40,296 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded data on fee debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded data on fee debt at the institution. This is a qualitative inquiry that requires analysis and interpretation of data, rather than a straightforward retrieval of information from the database schema. The schema provides tables related to financial transactions, student billing, and other related entities, but it does not contain explicit information or metrics that would directly answer the question about the reasons for the lack of recorded data. Therefore, it cannot be answered solely based on the schema.', 'feedback': ''}
2025-08-09 14:28:42,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:45,109 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:45,124 - celery.redirected - WARNING - 🔍 SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded data on fee debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded data on fee debt at the institution. However, the provided schema does not contain any tables or fields that directly address the reasons or factors behind the absence of recorded data. The schema includes various tables related to students, fees, transactions, and institutions, but it does not provide insights into the reasons for missing data or the context behind fee debt records. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 14:28:45,124 - celery.redirected - WARNING - 🔍 Type: <class 'dict'>
2025-08-09 14:28:45,124 - celery.redirected - WARNING - 🔍 Keys: ['question', 'answerable', 'reasoning', 'feedback']
2025-08-09 14:28:45,124 - celery.redirected - WARNING - ❌ Invalid SQL query evaluation result: {'question': 'What factors do you think could contribute to the lack of recorded data on fee debt at this institution?', 'answerable': False, 'reasoning': 'The question asks for factors contributing to the lack of recorded data on fee debt at the institution. However, the provided schema does not contain any tables or fields that directly address the reasons or factors behind the absence of recorded data. The schema includes various tables related to students, fees, transactions, and institutions, but it does not provide insights into the reasons for missing data or the context behind fee debt records. Therefore, this question cannot be answered based on the schema.', 'feedback': ''}
2025-08-09 14:28:45,125 - root - INFO - [{'institution_id': 1, 'total_fees_owed': -2600.0}]
2025-08-09 14:28:45,125 - root - INFO - [{'enrolled_students': 0}]
2025-08-09 14:28:45,125 - root - INFO - [{'fee_type': 'Hostel Fees', 'fee_count': 1}]
2025-08-09 14:28:45,125 - root - INFO - [{'institution_name': 'Central University', 'total_debt': -2600.0, 'average_debt': -41011563.506, 'total_institutions': 5}, {'institution_name': 'Trinity Theological Seminary', 'total_debt': -27040.0, 'average_debt': -41011563.506, 'total_institutions': 5}, {'institution_name': 'ITC University', 'total_debt': -75614369.64, 'average_debt': -41011563.506, 'total_institutions': 5}, {'institution_name': 'University Of Education, Winneba', 'total_debt': -34045567.83, 'average_debt': -41011563.506, 'total_institutions': 5}, {'institution_name': 'Max Debt Institution', 'total_debt': -2600.0, 'average_debt': -41011563.506, 'total_institutions': 5}]
2025-08-09 14:28:45,125 - root - INFO - 'No results'
2025-08-09 14:28:45,126 - root - INFO - 'No results'
2025-08-09 14:28:45,126 - root - INFO - 'No results'
2025-08-09 14:28:45,126 - root - INFO - 'No results'
2025-08-09 14:28:45,126 - root - INFO - 'No results'
2025-08-09 14:28:45,126 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-09 14:28:53,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:28:53,513 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-09 14:29:02,483 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:02,490 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:02,490 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:29:02,490 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:29:02,491 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the total amount of fees owed by students at the institution where students owe the most fee...
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The total amount of fees owed by students at the institution where students owe the most fees is -$2...
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_id': 1, 'total_fees_owed': -2600.0}]...
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_fees_owed_by_top_institution
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 14:29:02,491 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 14:29:02,491 - celery.redirected - WARNING - [{'institution_id': 1, 'total_fees_owed': -2600.0}]
2025-08-09 14:29:02,491 - celery.redirected - WARNING - ================================= 
2025-08-09 14:29:02,491 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 14:29:02,492 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:02,492 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:29:02,492 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:29:02,492 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How many students are currently enrolled at the institution where students owe the most fees?...
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Currently, there are no students enrolled at the institution where students owe the most fees....
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'enrolled_students': 0}]...
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: enrolled_students_with_highest_fees
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 14:29:02,492 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 14:29:02,492 - celery.redirected - WARNING - [{'enrolled_students': 0}]
2025-08-09 14:29:02,492 - celery.redirected - WARNING - ================================= 
2025-08-09 14:29:02,492 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 14:29:02,493 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:02,493 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:29:02,493 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:02,493 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:29:02,493 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:29:02,493 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:29:02,493 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:29:02,493 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:29:02,493 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What types of fees are most commonly owed by students at the institution where students owe the most...
2025-08-09 14:29:02,493 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: At the institution where students owe the most fees, the most commonly owed fee type is 'Hostel Fees...
2025-08-09 14:29:02,493 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:29:02,493 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'fee_type': 'Hostel Fees', 'fee_count': 1}]...
2025-08-09 14:29:02,493 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: most_common_fee_type_at_top_fee_institution
2025-08-09 14:29:02,493 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 14:29:02,493 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 14:29:02,493 - celery.redirected - WARNING - [{'fee_type': 'Hostel Fees', 'fee_count': 1}]
2025-08-09 14:29:02,494 - celery.redirected - WARNING - ================================= 
2025-08-09 14:29:02,494 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 14:29:02,494 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:02,494 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:29:02,494 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:02,494 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:29:02,494 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:29:02,494 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:29:02,494 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:29:02,494 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:29:02,494 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the average fee amount owed per student at the institution where students owe the most fees?...
2025-08-09 14:29:02,494 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no records available for the average fee amount owed per student at the in...
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: average_fee_per_student_no_data
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 14:29:02,495 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:02,495 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:29:02,495 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:29:02,495 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the fee debt at the institution where students owe the most fees compare to other instituti...
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The institution with the highest fee debt is ITC University, which has a total debt of approximately...
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: bar_chart
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -       📈 Data: [{'institution_name': 'Central University', 'total_debt': -2600.0, 'average_debt': -41011563.506, 't...
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: total_fee_debt_by_institution
2025-08-09 14:29:02,495 - INTERVIEW_TO_DOCS - INFO -       ✅ Data found - setting data_returned=True
2025-08-09 14:29:02,495 - celery.redirected - WARNING - 
 Checking supposed non empty data
2025-08-09 14:29:02,495 - celery.redirected - WARNING - [{'institution_name': 'Central University', 'total_debt': -2600.0, 'average_debt': -41011563.506, 'total_institutions': 5}, {'institution_name': 'Trinity Theological Seminary', 'total_debt': -27040.0, 'average_debt': -41011563.506, 'total_institutions': 5}, {'institution_name': 'ITC University', 'total_debt': -75614369.64, 'average_debt': -41011563.506, 'total_institutions': 5}, {'institution_name': 'University Of Education, Winneba', 'total_debt': -34045567.83, 'average_debt': -41011563.506, 'total_institutions': 5}, {'institution_name': 'Max Debt Institution', 'total_debt': -2600.0, 'average_debt': -41011563.506, 'total_institutions': 5}]
2025-08-09 14:29:02,496 - celery.redirected - WARNING - ================================= 
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       🏷️ Added data_tag: total_fee_debt_by_institution
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 1 with data_returned=True
2025-08-09 14:29:02,496 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:02,496 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:29:02,496 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 2
2025-08-09 14:29:02,496 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the historical trend of fee debt at the institution where students owe the most fees over th...
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I'm sorry I don't have the answer to this question...
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: no_answer_result
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 2:
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the historical trend of fee debt at the institution where students owe the most fees over th...
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: It appears that there are no recorded results regarding the historical trend of fee debt at the inst...
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: historical_fee_debt_trend
2025-08-09 14:29:02,496 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 2 documents, 0 with data_returned=True
2025-08-09 14:29:02,497 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:02,497 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:29:02,497 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:29:02,497 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Are there specific programs or courses at the institution where students owe the most fees that cont...
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: The SQL query returned no results, indicating that there are currently no specific programs or cours...
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: programs_with_highest_fee_debts
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 14:29:02,497 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:02,497 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-09 14:29:02,497 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'Which institutions owe most fees'
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-09 14:29:02,497 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-09 14:29:02,498 - celery.redirected - WARNING - CONVERTING TO VECTOR STORE DOCS
2025-08-09 14:29:02,498 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-09 14:29:02,498 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What percentage of students at the institution where students owe the most fees are on financial aid...
2025-08-09 14:29:02,498 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: Unfortunately, there are no results available for the percentage of students on financial aid or sch...
2025-08-09 14:29:02,498 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-09 14:29:02,498 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-09 14:29:02,498 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: percentage_students_on_financial_aid
2025-08-09 14:29:02,498 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-09 14:29:02,498 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-09 14:29:02,498 - STREAMING_REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 9
2025-08-09 14:29:02,498 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:02,498 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-09 14:29:02,498 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 9 documents
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO -     Content: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO -     Content: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO -     Content: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:02,498 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Content: Question: What is the average fee amount owed per student at the institution where students owe the ...
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:28:09.137047+00:00', 'data_returned': False}
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Content: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Content: Question: What is the historical trend of fee debt at the institution where students owe the most fe...
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:28:45.125031+00:00', 'data_returned': False}
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Content: Question: What is the historical trend of fee debt at the institution where students owe the most fe...
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 1, 'partition': 'interview', 'timestamp': '2025-08-09T14:28:45.125031+00:00', 'data_returned': False}
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -   📄 Doc 8:
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Content: Question: Are there specific programs or courses at the institution where students owe the most fees...
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:16.968101+00:00', 'data_returned': False}
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -   📄 Doc 9:
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Content: Question: What percentage of students at the institution where students owe the most fees are on fin...
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:28:03.377202+00:00', 'data_returned': False}
2025-08-09 14:29:02,499 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 4/9
2025-08-09 14:29:02,636 - elastic_transport.transport - INFO - HEAD http://*************:9200/deep_research [status:200 duration:0.137s]
2025-08-09 14:29:04,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 14:29:06,061 - elastic_transport.transport - INFO - PUT http://*************:9200/_bulk?refresh=true [status:200 duration:0.959s]
2025-08-09 14:29:06,061 - UPSERT_DOCS - INFO - ✅ Successfully upserted 9 documents to Elasticsearch
2025-08-09 14:29:06,062 - STREAMING_REPORT_PIPELINE - INFO - 📋 Outline sections to write: 6
2025-08-09 14:29:06,062 - app.chains.composite.report_pipeline - INFO - writing_sections
2025-08-09 14:29:06,062 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:06,062 - DEBUG_SEPARATOR - INFO - 🎯 WRITING REPORT SECTIONS WITH STREAMING
2025-08-09 14:29:06,063 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:06,063 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 1/6...
2025-08-09 14:29:07,028 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:07,032 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:07,032 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 14:29:07,032 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:07,032 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University fee debt analysis'
2025-08-09 14:29:07,032 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institutions owe most fees'
2025-08-09 14:29:07,032 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institutions owe most fees'
2025-08-09 14:29:07,166 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.134s]
2025-08-09 14:29:07,167 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 10
2025-08-09 14:29:07,297 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-09 14:29:07,298 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 4
2025-08-09 14:29:07,434 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-09 14:29:07,434 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-09 14:29:07,580 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.145s]
2025-08-09 14:29:07,580 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 4
2025-08-09 14:29:08,054 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 14:29:08,332 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.275s]
2025-08-09 14:29:08,333 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 4 documents
2025-08-09 14:29:08,333 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:08,333 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:08,333 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:08,334 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:08,334 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:08,334 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:08,334 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:08,334 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:08,335 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:08,335 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:08,335 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_fee_debt_by_institution
2025-08-09 14:29:08,335 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:08,335 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:08,335 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:08,335 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:08,335 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:08,335 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:08,336 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1591 chars):
2025-08-09 14:29:08,336 - app.chains.section_writer - INFO -    Question: How does the fee debt at the institution where students owe the most fees compare to other institutions?
Answer: The institution with the highest fee debt is ITC University, which has a total debt of approximately -75,614,369.64. In comparison, the other institutions have significantly lower total debts, with the next highest being University Of Education, Winneba at about -34,045,567.83, followed by Trinity Theological Seminary at -27,040.0, and Central University and Max Debt Institu...
2025-08-09 14:29:10,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:10,800 - app.chains.section_writer - INFO - 🤖 AI generated section (781 chars):
2025-08-09 14:29:10,800 - app.chains.section_writer - INFO -    The purpose of this report is to investigate which institutions owe the most fees and to analyze the implications of these debts. The key finding indicates that ITC University has the highest fee debt, amounting to over $75 million, significantly impacting its operations and student enrollment. In c...
2025-08-09 14:29:10,800 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_fee_debt_by_institution']
2025-08-09 14:29:10,800 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 1 completed and processed: 1516 characters
2025-08-09 14:29:10,801 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 2/6...
2025-08-09 14:29:11,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:11,547 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:11,547 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 14:29:11,547 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:11,547 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'fee debts overview institutions'
2025-08-09 14:29:11,547 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institutions owe most fees'
2025-08-09 14:29:11,547 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institutions owe most fees'
2025-08-09 14:29:11,705 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.158s]
2025-08-09 14:29:11,706 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 10
2025-08-09 14:29:11,842 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-09 14:29:11,842 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 4
2025-08-09 14:29:11,971 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 14:29:11,972 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-09 14:29:12,103 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-09 14:29:12,103 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 4
2025-08-09 14:29:12,926 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 14:29:13,079 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.149s]
2025-08-09 14:29:13,080 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 4 documents
2025-08-09 14:29:13,080 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:13,080 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:13,081 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:13,081 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:13,081 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:13,081 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:13,081 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:13,081 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:13,082 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:13,082 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:13,082 - app.chains.section_writer - INFO -   📄 Doc 2: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:13,082 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:13,082 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_fee_debt_by_institution
2025-08-09 14:29:13,082 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:13,082 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:13,082 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:13,083 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:13,083 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1591 chars):
2025-08-09 14:29:13,083 - app.chains.section_writer - INFO -    Question: What is the total amount of fees owed by students at the institution where students owe the most fees?
Answer: The total amount of fees owed by students at the institution where students owe the most fees is -$2,600. This negative value may indicate that students at this institution have overpaid or that there are adjustments or credits applied to their accounts.

Question: How does the fee debt at the institution where students owe the most fees compare to other institutions?
Answer: ...
2025-08-09 14:29:16,038 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:16,042 - app.chains.section_writer - INFO - 🤖 AI generated section (631 chars):
2025-08-09 14:29:16,042 - app.chains.section_writer - INFO -    ## 2. Overview of Fee Debts Across Institutions  

### 2.1 Summary of Total Fees Owed  
The total fees owed by students across various institutions are as follows:  
- ITC University: -$75,614,369.64  
- University Of Education, Winneba: -$34,045,567.83  
- Trinity Theological Seminary: -$27,040.00 ...
2025-08-09 14:29:16,042 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_fee_debt_by_institution']
2025-08-09 14:29:16,042 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 2 completed and processed: 612 characters
2025-08-09 14:29:16,043 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 3/6...
2025-08-09 14:29:16,705 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:16,713 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:16,713 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 14:29:16,713 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:16,713 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'ITC University highest fees owed analysis'
2025-08-09 14:29:16,713 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institutions owe most fees'
2025-08-09 14:29:16,713 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institutions owe most fees'
2025-08-09 14:29:16,856 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.142s]
2025-08-09 14:29:16,856 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 10
2025-08-09 14:29:16,988 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-09 14:29:16,988 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 4
2025-08-09 14:29:17,119 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-09 14:29:17,119 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-09 14:29:17,251 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-09 14:29:17,251 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 4
2025-08-09 14:29:17,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 14:29:17,793 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.145s]
2025-08-09 14:29:17,794 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 4 documents
2025-08-09 14:29:17,794 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:17,794 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:17,794 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:17,794 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:17,794 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:17,794 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:17,794 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:17,795 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:17,795 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:17,796 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:17,796 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_fee_debt_by_institution
2025-08-09 14:29:17,796 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:17,796 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:17,796 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:17,796 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:17,796 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:17,796 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:17,796 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1591 chars):
2025-08-09 14:29:17,796 - app.chains.section_writer - INFO -    Question: How does the fee debt at the institution where students owe the most fees compare to other institutions?
Answer: The institution with the highest fee debt is ITC University, which has a total debt of approximately -75,614,369.64. In comparison, the other institutions have significantly lower total debts, with the next highest being University Of Education, Winneba at about -34,045,567.83, followed by Trinity Theological Seminary at -27,040.0, and Central University and Max Debt Institu...
2025-08-09 14:29:20,848 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:20,857 - app.chains.section_writer - INFO - 🤖 AI generated section (742 chars):
2025-08-09 14:29:20,857 - app.chains.section_writer - INFO -    ## 3. Detailed Analysis of the Institution with the Highest Fees Owed  

### 3.1 ITC University  

#### 3.1.1 Total Fees Owed  
The total fees owed by ITC University amount to -$75,614,369.64. This figure represents the highest fee debt among institutions, significantly surpassing that of other univ...
2025-08-09 14:29:20,857 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_fee_debt_by_institution']
2025-08-09 14:29:20,858 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 3 completed and processed: 723 characters
2025-08-09 14:29:20,858 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 4/6...
2025-08-09 14:29:21,564 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:21,569 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:21,569 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 14:29:21,569 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:21,569 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Comparative analysis fee debts ITC University institutions'
2025-08-09 14:29:21,569 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institutions owe most fees'
2025-08-09 14:29:21,569 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institutions owe most fees'
2025-08-09 14:29:21,699 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-09 14:29:21,700 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 10
2025-08-09 14:29:21,830 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-09 14:29:21,831 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 4
2025-08-09 14:29:21,967 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.136s]
2025-08-09 14:29:21,968 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-09 14:29:22,098 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-09 14:29:22,098 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 4
2025-08-09 14:29:22,589 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 14:29:22,732 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.140s]
2025-08-09 14:29:22,733 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 4 documents
2025-08-09 14:29:22,733 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:22,733 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:22,733 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:22,733 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:22,733 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:22,733 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:22,733 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:22,733 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:22,734 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:22,734 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:22,734 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_fee_debt_by_institution
2025-08-09 14:29:22,734 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:22,734 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:22,734 - app.chains.section_writer - INFO -   📄 Doc 3: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:22,734 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:22,734 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:22,734 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:22,734 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1591 chars):
2025-08-09 14:29:22,735 - app.chains.section_writer - INFO -    Question: How does the fee debt at the institution where students owe the most fees compare to other institutions?
Answer: The institution with the highest fee debt is ITC University, which has a total debt of approximately -75,614,369.64. In comparison, the other institutions have significantly lower total debts, with the next highest being University Of Education, Winneba at about -34,045,567.83, followed by Trinity Theological Seminary at -27,040.0, and Central University and Max Debt Institu...
2025-08-09 14:29:27,093 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:27,100 - app.chains.section_writer - INFO - 🤖 AI generated section (1223 chars):
2025-08-09 14:29:27,100 - app.chains.section_writer - INFO -    ## 4. Comparative Analysis with Other Institutions  

### 4.1 Comparison of Fee Debts  
ITC University has the highest fee debt among the institutions analyzed, with a total debt of approximately -75,614,369.64. In contrast, the next highest debt is recorded at the University Of Education, Winneba, ...
2025-08-09 14:29:27,100 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_fee_debt_by_institution']
2025-08-09 14:29:27,100 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 4 completed and processed: 1204 characters
2025-08-09 14:29:27,101 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 5/6...
2025-08-09 14:29:27,731 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:27,740 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:27,740 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 14:29:27,740 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:27,740 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'Conclusion summary recommendations future research'
2025-08-09 14:29:27,740 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institutions owe most fees'
2025-08-09 14:29:27,740 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institutions owe most fees'
2025-08-09 14:29:27,871 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-09 14:29:27,872 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 10
2025-08-09 14:29:28,001 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 14:29:28,001 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 4
2025-08-09 14:29:28,131 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 14:29:28,131 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-09 14:29:28,260 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-09 14:29:28,261 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 4
2025-08-09 14:29:28,697 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 14:29:28,846 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.145s]
2025-08-09 14:29:28,847 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 4 documents
2025-08-09 14:29:28,847 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:28,847 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:28,847 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:28,847 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:28,847 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:28,847 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:28,848 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:28,848 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:28,848 - app.chains.section_writer - INFO -   📄 Doc 1: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:28,848 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:28,849 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_fee_debt_by_institution
2025-08-09 14:29:28,849 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:28,849 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:28,849 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:28,849 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:28,849 - app.chains.section_writer - INFO -   📄 Doc 4: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:28,849 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:28,849 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1591 chars):
2025-08-09 14:29:28,849 - app.chains.section_writer - INFO -    Question: How does the fee debt at the institution where students owe the most fees compare to other institutions?
Answer: The institution with the highest fee debt is ITC University, which has a total debt of approximately -75,614,369.64. In comparison, the other institutions have significantly lower total debts, with the next highest being University Of Education, Winneba at about -34,045,567.83, followed by Trinity Theological Seminary at -27,040.0, and Central University and Max Debt Institu...
2025-08-09 14:29:34,160 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:34,165 - app.chains.section_writer - INFO - 🤖 AI generated section (1834 chars):
2025-08-09 14:29:34,166 - app.chains.section_writer - INFO -    ## 5. Conclusion  

### 5.1 Summary of Findings  
The analysis reveals that ITC University has the highest fee debt among the institutions studied, totaling approximately -75,614,369.64. This figure is significantly greater than the next highest debt, which belongs to the University Of Education, Wi...
2025-08-09 14:29:34,166 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_fee_debt_by_institution']
2025-08-09 14:29:34,166 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 5 completed and processed: 1815 characters
2025-08-09 14:29:34,166 - STREAMING_REPORT_PIPELINE - INFO - ✍️ Writing section 6/6...
2025-08-09 14:29:34,964 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:34,969 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:34,969 - DEBUG_SEPARATOR - INFO - 🎯 VECTOR SEARCH EXECUTION
2025-08-09 14:29:34,969 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:34,969 - VECTOR_SEARCH - INFO - 📝 Generated Query: 'appendices interview transcripts fee structures glossary'
2025-08-09 14:29:34,969 - VECTOR_SEARCH - INFO - 💬 Conversation ID: 'Which institutions owe most fees'
2025-08-09 14:29:34,969 - VECTOR_SEARCH - INFO - ❓ Original Question: 'Which institutions owe most fees'
2025-08-09 14:29:35,107 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.137s]
2025-08-09 14:29:35,107 - VECTOR_SEARCH - INFO - 📊 Total documents in index: 10
2025-08-09 14:29:35,238 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.130s]
2025-08-09 14:29:35,238 - VECTOR_SEARCH - INFO - ✅ Documents with data_returned=True: 4
2025-08-09 14:29:35,369 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.131s]
2025-08-09 14:29:35,370 - VECTOR_SEARCH - INFO - 🗣️ Documents for this conversation: 9
2025-08-09 14:29:35,507 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_count [status:200 duration:0.137s]
2025-08-09 14:29:35,508 - VECTOR_SEARCH - INFO - 🎯 Documents matching both filters: 4
2025-08-09 14:29:36,198 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-09 14:29:36,345 - elastic_transport.transport - INFO - POST http://*************:9200/deep_research/_search?_source_includes=metadata,text [status:200 duration:0.142s]
2025-08-09 14:29:36,345 - VECTOR_SEARCH - INFO - 🎯 Vector search with data_returned=True returned 4 documents
2025-08-09 14:29:36,346 - VECTOR_SEARCH - INFO -   📄 Doc 1: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:36,346 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:36,346 - VECTOR_SEARCH - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:36,346 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:36,346 - VECTOR_SEARCH - INFO -   📄 Doc 3: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:36,346 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:36,346 - VECTOR_SEARCH - INFO -   📄 Doc 4: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:36,346 - VECTOR_SEARCH - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:36,347 - app.chains.section_writer - INFO -   📄 Doc 1: Question: What types of fees are most commonly owed by students at the institution where students ow...
2025-08-09 14:29:36,347 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:26:59.939901+00:00', 'data_returned': True}
2025-08-09 14:29:36,347 - app.chains.section_writer - INFO -   📄 Doc 2: Question: What is the total amount of fees owed by students at the institution where students owe th...
2025-08-09 14:29:36,347 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:00.527051+00:00', 'data_returned': True}
2025-08-09 14:29:36,347 - app.chains.section_writer - INFO -   📄 Doc 3: Question: How many students are currently enrolled at the institution where students owe the most fe...
2025-08-09 14:29:36,347 - app.chains.section_writer - INFO -   ⚠️ No data_tag in metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:07.492799+00:00', 'data_returned': True}
2025-08-09 14:29:36,348 - app.chains.section_writer - INFO -   📄 Doc 4: Question: How does the fee debt at the institution where students owe the most fees compare to other...
2025-08-09 14:29:36,348 - app.chains.section_writer - INFO -   🏷️ Metadata: {'conversation_id': 'Which institutions owe most fees', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-09T14:27:23.443633+00:00', 'data_returned': True, 'data_tag': 'total_fee_debt_by_institution'}
2025-08-09 14:29:36,348 - app.chains.section_writer - INFO -   ✅ Added Data Tag: total_fee_debt_by_institution
2025-08-09 14:29:36,348 - app.chains.section_writer - INFO - 📝 Final context being sent to AI (1591 chars):
2025-08-09 14:29:36,348 - app.chains.section_writer - INFO -    Question: What types of fees are most commonly owed by students at the institution where students owe the most fees?
Answer: At the institution where students owe the most fees, the most commonly owed fee type is 'Hostel Fees', with a total of 1 instance recorded.

Question: What is the total amount of fees owed by students at the institution where students owe the most fees?
Answer: The total amount of fees owed by students at the institution where students owe the most fees is -$2,600. This ne...
2025-08-09 14:29:40,870 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-09 14:29:40,876 - app.chains.section_writer - INFO - 🤖 AI generated section (1461 chars):
2025-08-09 14:29:40,876 - app.chains.section_writer - INFO -    ## 6. Appendices  

### 6.1 Interview Transcripts/Summaries  
The interview transcripts and summaries provide insights into the perspectives of stakeholders regarding the fee structures and student experiences at the institution. These documents will be made available for review to ensure transparen...
2025-08-09 14:29:40,877 - app.chains.section_writer - INFO - ✅ Found 1 data tag placeholders: ['total_fee_debt_by_institution']
2025-08-09 14:29:40,877 - STREAMING_REPORT_PIPELINE - INFO -   ✅ Section 6 completed and processed: 1442 characters
2025-08-09 14:29:40,878 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-09 14:29:40,878 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION COMPLETED
2025-08-09 14:29:40,878 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-09 14:29:40,878 - STREAMING_REPORT_PIPELINE - INFO - ✅ Streaming report generation successful!
2025-08-09 14:29:40,878 - STREAMING_REPORT_PIPELINE - INFO - 📊 Final report sections: 7
2025-08-09 14:29:40,878 - STREAMING_REPORT_PIPELINE - INFO - 📈 Extended data items: 8
2025-08-09 14:29:40,878 - STREAMING_REPORT_PIPELINE - INFO - 🎨 Total figures processed: 1
2025-08-09 14:29:40,878 - STREAMING_REPORT_PIPELINE - INFO - 📁 Debug log file: debug_logs/report_generation_debug_20250809_142559.log
2025-08-09 14:29:40,886 - celery.app.trace - INFO - Task generate_streaming_report[e15ceec7-bb09-4155-b086-0e3837891331] succeeded in 221.58761195800253s: {'outline': '# Report on Institutional Fee Debts

## 1. Introduction  
   The purpose of this report is to investigate which institutions owe the most fees and to analyze the implications of these debts. The key finding indicates that ITC University has the highest fee debt, amounting to over $75 million, significantly impacting its operations and student enrollment.

## 2. Overview of Fee Debts Across Institutions  
   - 2.1 Summary of Total Fees Owed  
       - ITC University: -$75,614,369.64  
       - University Of Education, Winneba: -$34,045,567.83  
       - Trinity Theological Seminary: -$27,040.00  
       - Central University: -$2,600.00  
       - Max Debt Institution: -$2,600.00  
   - 2.2 Average Debt Across Institutions  
       - Average Debt: -$41,011,563.51  

## 3. Detailed Analysis of the Institution with the Highest Fees Owed  
   - 3.1 ITC University  
       - 3.1.1 Total Fees Owed  
           - Amount: -$75,614,369.64  
       - 3.1.2 Enrollment Status  
           - Current...', 'secti...', ...}

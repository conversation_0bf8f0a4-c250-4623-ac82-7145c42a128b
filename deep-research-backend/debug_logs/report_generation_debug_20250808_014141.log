2025-08-08 01:41:41,836 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:41:41,836 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: test-end-to-end
2025-08-08 01:41:41,836 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:41:41,836 - REPORT_REQUEST - INFO - 📝 Original Question: 'How many students are there?'
2025-08-08 01:41:41,836 - REPORT_REQUEST - INFO - 🆔 Task ID: test-end-to-end
2025-08-08 01:41:41,836 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-08T01:41:41.836397
2025-08-08 01:41:42,049 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.213s]
2025-08-08 01:41:42,050 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 33
2025-08-08 01:41:42,377 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.327s]
2025-08-08 01:41:42,378 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 0
2025-08-08 01:41:43,099 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:0.721s]
2025-08-08 01:41:43,099 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (4):
2025-08-08 01:41:43,099 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (18 docs)
2025-08-08 01:41:43,099 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (7 docs)
2025-08-08 01:41:43,099 - ELASTICSEARCH_STATE - INFO -   - 'What is the institution with the most students?' (7 docs)
2025-08-08 01:41:43,099 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing in ITC University?' (1 docs)
2025-08-08 01:41:43,100 - app.chains.composite.report_pipeline - INFO - analysing question...
2025-08-08 01:41:51,090 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:51,110 - app.chains.composite.report_pipeline - INFO - expanding_questions
2025-08-08 01:41:55,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:55,168 - app.chains.composite.report_pipeline - INFO - verifying_questions
2025-08-08 01:41:57,130 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:57,155 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:57,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:57,424 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:57,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:57,559 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:57,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:57,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:57,759 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:58,328 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:41:58,395 - app.chains.composite.report_pipeline - INFO - interviewing
2025-08-08 01:41:58,396 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:41:58,396 - DEBUG_SEPARATOR - INFO - 🎯 STARTING INTERVIEW PROCESS
2025-08-08 01:41:58,396 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:41:58,396 - REPORT_PIPELINE - INFO - 📋 Questions to interview: 10
2025-08-08 01:42:00,407 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:00,421 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:00,566 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:01,022 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:01,024 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:01,039 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:01,070 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:01,337 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:01,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:02,147 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:02,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:02,884 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:02,942 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:03,381 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:03,476 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:03,660 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:03,851 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:03,900 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:04,030 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:04,031 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:04,400 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:04,591 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:04,774 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:05,012 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:05,149 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:06,038 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:06,039 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:06,960 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:07,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:07,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:08,086 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:08,377 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:08,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:09,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:09,169 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:09,253 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:10,236 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:10,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:11,158 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:11,710 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:11,905 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:12,232 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:12,806 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:13,402 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:13,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:13,625 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:14,213 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:14,334 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:14,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:15,665 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:15,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:15,934 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:16,894 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:17,126 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:17,712 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:18,325 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:19,248 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:19,276 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:19,449 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:19,657 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:19,908 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:20,150 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:20,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:21,296 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:21,796 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:21,833 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:22,912 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:22,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:23,063 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:23,098 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:23,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:23,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:23,655 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:24,060 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:24,470 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:24,727 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:25,086 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:25,353 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:25,500 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:25,792 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:26,313 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:26,546 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:26,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:27,467 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:28,054 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:28,087 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:28,217 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:28,559 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:28,754 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:28,927 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:29,109 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:29,488 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:29,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:30,717 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:30,831 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:30,883 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:31,048 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:31,166 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:31,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:32,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:32,252 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:32,650 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:32,658 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:32,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:33,482 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:33,573 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:34,551 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:34,593 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:34,674 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:35,814 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:36,029 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:36,826 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:36,907 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:36,947 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:37,026 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:37,064 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:37,588 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:38,704 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:39,530 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:40,239 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:40,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:40,703 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:41,161 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:41,571 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:41,876 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:42,388 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:42,576 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:43,668 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:43,820 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:44,824 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:45,566 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:46,076 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:46,691 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:48,926 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:49,064 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:49,877 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:51,401 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:54,266 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:54,444 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:55,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:57,141 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:42:58,604 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:00,517 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:01,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:01,949 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:03,731 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:04,610 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:05,699 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:06,146 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:09,323 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:11,676 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:12,493 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:14,848 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:17,736 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:19,354 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:23,042 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:26,801 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:29,492 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:32,155 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:34,612 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:38,196 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:43,494 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:46,774 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:49,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:50,381 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:55,120 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:43:58,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:44:00,950 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:44:04,521 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:44:05,603 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:44:10,178 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:11,220 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:16,368 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:19,778 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:21,001 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:24,694 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:29,528 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:31,885 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:34,565 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:35,430 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:38,213 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:43,561 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:46,507 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:48,966 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:49,798 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:53,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:57,312 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:46:59,812 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:47:02,901 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:47:03,704 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:47:07,309 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:47:12,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:47:15,836 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:47:18,579 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:47:18,606 - REPORT_PIPELINE - INFO - 🗣️ Completed interviews: 1
2025-08-08 01:47:18,606 - root - INFO - 'No results'
2025-08-08 01:47:18,606 - root - INFO - 'No results'
2025-08-08 01:47:18,606 - root - INFO - 'No results'
2025-08-08 01:47:18,606 - root - INFO - 'No results'
2025-08-08 01:47:18,606 - root - INFO - 'No results'
2025-08-08 01:47:18,606 - root - INFO - 'No results'
2025-08-08 01:47:18,607 - root - INFO - 'No results'
2025-08-08 01:47:18,607 - root - INFO - 'No results'
2025-08-08 01:47:18,607 - root - INFO - 'No results'
2025-08-08 01:47:18,607 - root - INFO - 'No results'
2025-08-08 01:47:18,607 - REPORT_PIPELINE - INFO - ✅ Conversations with data: 0
2025-08-08 01:47:18,607 - REPORT_PIPELINE - INFO - ❌ Conversations without data: 10
2025-08-08 01:47:18,607 - app.chains.composite.report_pipeline - INFO - generating_outline
2025-08-08 01:47:25,762 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:47:25,777 - app.chains.composite.report_pipeline - INFO - refining_outline
2025-08-08 01:47:33,408 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-08 01:47:33,414 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,414 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEWS TO DOCUMENTS
2025-08-08 01:47:33,414 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,414 - REPORT_PIPELINE - INFO - 🔄 Processing interview 1/10
2025-08-08 01:47:33,414 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,414 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:47:33,414 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,414 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 01:47:33,414 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:47:33,414 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:47:33,414 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you believe have contributed to the current lack of student enrollment at the instit...
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:47:33,415 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:47:33,415 - REPORT_PIPELINE - INFO - 🔄 Processing interview 2/10
2025-08-08 01:47:33,415 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,415 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:47:33,415 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors typically influence student activity levels, and how might we gather data to assess the...
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:47:33,415 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:47:33,416 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:47:33,416 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:47:33,416 - REPORT_PIPELINE - INFO - 🔄 Processing interview 3/10
2025-08-08 01:47:33,416 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,416 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:47:33,416 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,416 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 01:47:33,416 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:47:33,416 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:47:33,416 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:47:33,416 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you think contribute to the high enrollment numbers in education-related programs co...
2025-08-08 01:47:33,416 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:47:33,416 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:47:33,416 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:47:33,417 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:47:33,417 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:47:33,417 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:47:33,417 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:47:33,417 - REPORT_PIPELINE - INFO - 🔄 Processing interview 4/10
2025-08-08 01:47:33,417 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,417 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:47:33,417 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,417 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 01:47:33,417 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:47:33,417 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:47:33,417 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:47:33,417 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you believe contributed to the significant increase in enrollment from 2021 to 2022?...
2025-08-08 01:47:33,417 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:47:33,417 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:47:33,418 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:47:33,418 - REPORT_PIPELINE - INFO - 🔄 Processing interview 5/10
2025-08-08 01:47:33,418 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,418 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:47:33,418 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does the demographic distribution of students impact the cultural dynamics and learning environm...
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:47:33,418 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:47:33,418 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:47:33,419 - REPORT_PIPELINE - INFO - 🔄 Processing interview 6/10
2025-08-08 01:47:33,419 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,419 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:47:33,419 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you believe have contributed to the significant increase in the student population f...
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:47:33,419 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:47:33,419 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:47:33,419 - REPORT_PIPELINE - INFO - 🔄 Processing interview 7/10
2025-08-08 01:47:33,419 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,419 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:47:33,419 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: How does this ratio compare to similar institutions, and what factors might contribute to any differ...
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:47:33,420 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:47:33,420 - REPORT_PIPELINE - INFO - 🔄 Processing interview 8/10
2025-08-08 01:47:33,420 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,420 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:47:33,420 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: Given the lack of explicit categorization between part-time and full-time programs, what methods or ...
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:47:33,420 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:47:33,421 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:47:33,421 - REPORT_PIPELINE - INFO - 🔄 Processing interview 9/10
2025-08-08 01:47:33,421 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,421 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:47:33,421 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What is the average class size based on the total number of students?...
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:47:33,421 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:47:33,421 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:47:33,421 - REPORT_PIPELINE - INFO - 🔄 Processing interview 10/10
2025-08-08 01:47:33,421 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,421 - DEBUG_SEPARATOR - INFO - 🎯 CONVERTING INTERVIEW TO DOCUMENTS
2025-08-08 01:47:33,421 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO - 📝 Original question: 'How many students are there?'
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO - 🗣️ Interview result keys: ['convo_id', 'convo', 'convo_with_data', 'data', 'timestamp']
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO - 💬 Number of conversation turns: 1
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO -     🔄 Turn 1:
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO -       ❓ Question: What factors do you believe contributed to the sharp decline in enrollments from 2020 to 2021, and h...
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO -       💬 Answer: I encountered an error while processing your question. Please try again....
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO -       📊 Presentation type: text
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO -       📈 Data: No results...
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO -       🏷️ Data tag: invalid_query_result
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO -       ❌ No data found - setting data_returned=False
2025-08-08 01:47:33,422 - INTERVIEW_TO_DOCS - INFO -   📊 SUMMARY: Created 1 documents, 0 with data_returned=True
2025-08-08 01:47:33,422 - REPORT_PIPELINE - INFO -   📄 Generated 1 documents from this interview
2025-08-08 01:47:33,422 - REPORT_PIPELINE - INFO - 📊 Total documents to upsert: 10
2025-08-08 01:47:33,422 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:33,422 - DEBUG_SEPARATOR - INFO - 🎯 UPSERTING DOCUMENTS TO ELASTICSEARCH
2025-08-08 01:47:33,422 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:33,422 - UPSERT_DOCS - INFO - 📝 Attempting to upsert 10 documents
2025-08-08 01:47:33,422 - UPSERT_DOCS - INFO -   📄 Doc 1:
2025-08-08 01:47:33,422 - UPSERT_DOCS - INFO -     Content: Question: What factors do you believe have contributed to the current lack of student enrollment at ...
2025-08-08 01:47:33,422 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:43:12.506546+00:00', 'data_returned': False}
2025-08-08 01:47:33,422 - UPSERT_DOCS - INFO -   📄 Doc 2:
2025-08-08 01:47:33,422 - UPSERT_DOCS - INFO -     Content: Question: What factors typically influence student activity levels, and how might we gather data to ...
2025-08-08 01:47:33,422 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:42:39.551175+00:00', 'data_returned': False}
2025-08-08 01:47:33,422 - UPSERT_DOCS - INFO -   📄 Doc 3:
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Content: Question: What factors do you think contribute to the high enrollment numbers in education-related p...
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:42:36.848498+00:00', 'data_returned': False}
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -   📄 Doc 4:
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Content: Question: What factors do you believe contributed to the significant increase in enrollment from 202...
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:42:43.681543+00:00', 'data_returned': False}
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -   📄 Doc 5:
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Content: Question: How does the demographic distribution of students impact the cultural dynamics and learnin...
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:42:30.896657+00:00', 'data_returned': False}
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -   📄 Doc 6:
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Content: Question: What factors do you believe have contributed to the significant increase in the student po...
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:42:32.664179+00:00', 'data_returned': False}
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -   📄 Doc 7:
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Content: Question: How does this ratio compare to similar institutions, and what factors might contribute to ...
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:42:26.564516+00:00', 'data_returned': False}
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -   📄 Doc 8:
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Content: Question: Given the lack of explicit categorization between part-time and full-time programs, what m...
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:42:36.052273+00:00', 'data_returned': False}
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -   📄 Doc 9:
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Content: Question: What is the average class size based on the total number of students?
Answer: I encountere...
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:47:18.605772+00:00', 'data_returned': False}
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -   📄 Doc 10:
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Content: Question: What factors do you believe contributed to the sharp decline in enrollments from 2020 to 2...
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO -     Metadata: {'conversation_id': 'How many students are there?', 'turn_index': 0, 'partition': 'interview', 'timestamp': '2025-08-08T01:42:49.086854+00:00', 'data_returned': False}
2025-08-08 01:47:33,423 - UPSERT_DOCS - INFO - ✅ Documents with data_returned=True: 0/10
2025-08-08 01:47:33,558 - elastic_transport.transport - INFO - HEAD http://54.246.247.31:9200/deep_research [status:200 duration:0.134s]
2025-08-08 01:47:34,526 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/embeddings "HTTP/1.1 200 OK"
2025-08-08 01:47:44,810 - elastic_transport.transport - INFO - PUT http://54.246.247.31:9200/_bulk?refresh=true [status:N/A duration:10.002s]
2025-08-08 01:47:44,810 - elastic_transport.node_pool - WARNING - Node <Urllib3HttpNode(http://54.246.247.31:9200)> has failed for 1 times in a row, putting on 1 second timeout
2025-08-08 01:47:44,811 - UPSERT_DOCS - ERROR - ❌ Error upserting documents: Connection timed out
2025-08-08 01:47:44,811 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-08 01:47:44,811 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION ERROR
2025-08-08 01:47:44,811 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-08 01:47:44,811 - REPORT_PIPELINE_ERROR - ERROR - ❌ Report generation failed: Error generating report: Connection timed out
2025-08-08 01:47:44,811 - REPORT_PIPELINE_ERROR - ERROR - 📁 Debug log file: debug_logs/report_generation_debug_20250808_014141.log

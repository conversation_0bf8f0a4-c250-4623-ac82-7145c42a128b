#!/usr/bin/env python3

import os
import sys
import json
import traceback

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_full_report_pipeline():
    print("🔍 Testing full report generation pipeline...")
    
    try:
        # Test the full report pipeline
        from app.chains.composite.report_pipeline import generate_full_report
        from app.models.report import ReportGenerationRequest

        test_question = "How many students are there?"
        print(f"🔎 Test question: '{test_question}'")

        # Create a request object
        request = ReportGenerationRequest(original_question=test_question)
        task_id = "test-task-123"

        # Test the full pipeline
        result = generate_full_report(request, task_id)
        print(f"📊 Full report result:")
        print(json.dumps(result, indent=2, default=str))
        
        # Check if the result has the expected structure
        if isinstance(result, dict):
            print(f"🔍 Result keys: {list(result.keys())}")
            
            expected_keys = ["outline", "sections", "data"]
            missing_keys = [key for key in expected_keys if key not in result]
            if missing_keys:
                print(f"❌ Missing expected keys: {missing_keys}")
            else:
                print("✅ All expected keys present")
                
            # Check if sections contain data
            if "sections" in result and result["sections"]:
                print(f"✅ Sections generated: {len(result['sections'])} sections")
                for i, section in enumerate(result["sections"]):
                    if isinstance(section, dict) and "presentation_type" in section:
                        print(f"  📊 Section {i+1}: Data object with presentation_type '{section['presentation_type']}'")
                    else:
                        print(f"  📝 Section {i+1}: Text content ({len(str(section))} chars)")
            else:
                print("❌ No sections generated")
                
            # Check if data dictionary is populated
            if "data" in result and result["data"]:
                print(f"✅ Data dictionary populated with {len(result['data'])} entries")
                for key, value in result["data"].items():
                    print(f"  🔑 {key}: {type(value)} with {len(value) if isinstance(value, (list, dict)) else 'N/A'} items")
            else:
                print("❌ Data dictionary is empty")
        else:
            print(f"❌ Result is not a dict: {type(result)}")
            
        return result
        
    except Exception as e:
        print(f"❌ Error in full report pipeline: {e}")
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_full_report_pipeline()

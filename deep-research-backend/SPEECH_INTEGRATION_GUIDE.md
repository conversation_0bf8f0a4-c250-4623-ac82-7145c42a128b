# Speech Integration Guide

This guide explains the Speech-to-Text (STT) and Text-to-Speech (TTS) integration implemented in the Deep Research project using ElevenLabs as the provider.

## Architecture Overview

The speech integration follows a provider factory pattern that allows easy switching between different speech service providers. The current implementation uses ElevenLabs but can be extended to support other providers like OpenAI, Google, or Azure.

### Backend Architecture

```
app/speech/
├── __init__.py
├── base.py                 # Abstract base classes for providers
├── factory.py              # Provider factory for creating instances
├── service.py              # High-level service interface
└── providers/
    ├── __init__.py
    └── elevenlabs.py       # ElevenLabs implementation
```

### Key Components

1. **Base Classes** (`base.py`):
   - `BaseTTSProvider`: Abstract interface for TTS providers
   - `BaseSTTProvider`: Abstract interface for STT providers
   - Request/Response models for type safety

2. **Provider Factory** (`factory.py`):
   - `SpeechProviderFactory`: Creates provider instances
   - `SpeechProviderType`: Enum of available providers
   - Registry system for adding new providers

3. **Service Layer** (`service.py`):
   - `SpeechService`: High-level interface for speech operations
   - Global service instance management
   - Configuration handling

4. **ElevenLabs Provider** (`providers/elevenlabs.py`):
   - `ElevenLabsTTSProvider`: TTS implementation
   - `ElevenLabsSTTProvider`: STT implementation
   - `ElevenLabsProvider`: Combined provider

## API Endpoints

### Text-to-Speech
```
POST /speech/tts
Content-Type: application/json

{
  "text": "Hello, world!",
  "voice_id": "pNInz6obpgDQGcFmaJgB",
  "output_format": "mp3_22050_32",
  "model_id": "eleven_multilingual_v2",
  "voice_settings": {
    "stability": 0.0,
    "similarity_boost": 1.0,
    "style": 0.0,
    "use_speaker_boost": true,
    "speed": 1.0
  }
}
```

Returns: Audio stream (MP3)

### Speech-to-Text
```
POST /speech/stt
Content-Type: multipart/form-data

audio_file: <audio file>
language_code: "eng"
model_id: "scribe_v1"
tag_audio_events: true
diarize: false
```

Returns:
```json
{
  "text": "Transcribed text",
  "confidence": 0.95,
  "language": "eng",
  "metadata": {
    "model_id": "scribe_v1",
    "tag_audio_events": true,
    "diarize": false
  }
}
```

### Utility Endpoints

- `GET /speech/voices` - Get available voices
- `GET /speech/formats` - Get supported audio formats
- `GET /speech/provider-info` - Get provider information
- `GET /speech/health` - Health check

## Configuration

### Environment Variables

Add to your `.env` file:
```
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
```

### Settings

The `app/core/config.py` includes:
```python
class Settings(BaseSettings):
    # ... other settings
    elevenlabs_api_key: str
```

## Usage Examples

### Backend Usage

```python
from app.speech.service import get_speech_service

# Get service instance
speech_service = get_speech_service()

# Text-to-Speech
audio_response = await speech_service.text_to_speech(
    text="Hello, world!",
    voice_id="pNInz6obpgDQGcFmaJgB"
)

# Speech-to-Text
with open("audio.wav", "rb") as audio_file:
    result = await speech_service.speech_to_text(
        audio_file=audio_file,
        language_code="eng"
    )
    print(result.text)
```

### Adding New Providers

1. Create a new provider file in `app/speech/providers/`
2. Implement `BaseTTSProvider` and `BaseSTTProvider`
3. Register in the factory:

```python
from app.speech.factory import SpeechProviderFactory, SpeechProviderType

# Add new provider type
class SpeechProviderType(str, Enum):
    ELEVENLABS = "elevenlabs"
    OPENAI = "openai"  # New provider

# Register provider
SpeechProviderFactory.register_tts_provider(
    SpeechProviderType.OPENAI,
    OpenAITTSProvider
)
```

## Frontend Integration

### Components

1. **AudioRecorder** (`AudioRecorder.tsx`):
   - Records audio from microphone
   - Provides playback controls
   - Handles transcription

2. **TextToSpeechButton** (`TextToSpeechButton.tsx`):
   - Converts text to speech
   - Provides play/pause controls
   - Shows loading and error states

3. **SpeechTest** (`SpeechTest.tsx`):
   - Test component for speech functionality
   - Connection status checking
   - Interactive testing interface

### Hooks

1. **useAudioRecorder** (`useAudioRecorder.ts`):
   - Audio recording state management
   - MediaRecorder API integration
   - Timer and controls

2. **useTextToSpeech** (`useTextToSpeech.ts`):
   - TTS state management
   - Audio playback controls
   - Error handling

### Services

**speechApi** (`speechApi.ts`):
- API client for speech endpoints
- Type-safe request/response handling
- Error handling and retries

## Testing

### Backend Testing

Run the test script:
```bash
cd deep-research-backend
python test_speech_api.py
```

This tests:
- Service initialization
- Provider information
- TTS functionality
- API endpoints (if server is running)

### Frontend Testing

Add the SpeechTest component to any page:
```tsx
import { SpeechTest } from '@/components/SpeechTest';

export default function TestPage() {
  return <SpeechTest />;
}
```

## Troubleshooting

### Common Issues

1. **API Key Issues**:
   - Ensure `ELEVENLABS_API_KEY` is set in `.env`
   - Verify the API key is valid and has sufficient credits

2. **Audio Recording Issues**:
   - Check browser permissions for microphone access
   - Ensure HTTPS in production (required for microphone access)

3. **CORS Issues**:
   - Verify CORS settings in `app/main.py`
   - Check that frontend origin is allowed

4. **Audio Format Issues**:
   - Supported formats: wav, mp3, mp4, mpeg, mpga, m4a, ogg, webm
   - Browser compatibility varies for recording formats

### Debug Mode

Enable debug logging:
```python
import logging
logging.getLogger("app.speech").setLevel(logging.DEBUG)
```

## Performance Considerations

1. **Audio Quality vs Size**:
   - Default format: `mp3_22050_32` (good balance)
   - Higher quality: `mp3_44100_128`
   - Lower bandwidth: `mp3_22050_32`

2. **Caching**:
   - Consider caching TTS results for repeated text
   - Voice information is cached by the service

3. **Rate Limiting**:
   - ElevenLabs has API rate limits
   - Implement client-side debouncing for rapid requests

## Security Considerations

1. **API Key Protection**:
   - Never expose API keys in frontend code
   - Use environment variables
   - Rotate keys regularly

2. **Audio Data**:
   - Audio data is processed server-side
   - Consider data retention policies
   - Implement user consent for voice recording

3. **HTTPS**:
   - Required for microphone access in browsers
   - Ensures encrypted communication

## Future Enhancements

1. **Multiple Providers**:
   - Add OpenAI Whisper for STT
   - Add Google Cloud Speech
   - Add Azure Cognitive Services

2. **Advanced Features**:
   - Voice cloning
   - Real-time streaming
   - Language detection
   - Speaker identification

3. **Performance**:
   - Audio compression
   - Streaming TTS
   - Background processing

4. **UI/UX**:
   - Voice activity detection
   - Audio visualization
   - Keyboard shortcuts
   - Voice commands

## Chat Interface Integration

The speech functionality has been integrated into the existing chat interface:

### Features Added

1. **Voice Input**:
   - Click the microphone button in the text input
   - Record audio and automatically transcribe to text
   - Audio recorder with play/pause/stop controls

2. **Voice Output**:
   - AI responses include a speaker button
   - Click to hear the response read aloud
   - Play/pause controls for audio playback

3. **Report Audio**:
   - Complete reports include "Listen to Report" button
   - Converts entire report content to speech
   - Useful for long research reports

### Usage in Chat

1. **Voice Questions**:
   - Click microphone icon in input field
   - Record your question
   - Audio is automatically transcribed and sent

2. **Listen to Responses**:
   - AI responses show a speaker icon
   - Click to hear the response
   - Pause/resume playback as needed

3. **Report Playback**:
   - Generated reports include audio controls
   - Listen while reviewing charts and data
   - Multi-section reports are read continuously

#!/usr/bin/env python3

import os
import sys
import json

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.chains.composite.sql_generation_and_verification import sql_gen_veri_chain

def test_sql_generation():
    print("🔍 Testing SQL generation chain...")
    
    test_question = "How many students are there?"
    print(f"🔎 Test question: '{test_question}'")
    
    try:
        result = sql_gen_veri_chain.invoke(test_question)
        print(f"📊 SQL generation result:")
        print(json.dumps(result, indent=2, default=str))
        
        # Debug: Check what keys are actually in the result
        print(f"🔍 Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        print(f"🔍 Result type: {type(result)}")
        
        # Check if it has the expected structure
        if isinstance(result, dict):
            has_correct = "correct" in result
            has_sql = "sql" in result
            has_question = "question" in result
            has_reasoning = "reasoning" in result
            
            print(f"🔍 Has 'correct' key: {has_correct}")
            print(f"🔍 Has 'sql' key: {has_sql}")
            print(f"🔍 Has 'question' key: {has_question}")
            print(f"🔍 Has 'reasoning' key: {has_reasoning}")
            
            if has_correct:
                print(f"✅ 'correct' value: {result['correct']}")
            else:
                print("❌ Missing 'correct' key!")
                
        return result
        
    except Exception as e:
        print(f"❌ Error in SQL generation: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_sql_generation()
